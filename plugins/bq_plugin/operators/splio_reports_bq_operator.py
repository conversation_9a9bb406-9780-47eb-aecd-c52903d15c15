# -*- coding: utf-8 -*-
import json
import os
import re
import tempfile
from datetime import datetime

import requests as req
from airflow.models import BaseOperator
from airflow.providers.google.cloud.hooks.bigquery import BigQueryHook
from airflow.providers.google.cloud.hooks.gcs import GCSHook


class SplioReportsBigQueryOperator(BaseOperator):
    """
    Direct API-to-BigQuery operator for Splio reports.
    Eliminates PostgreSQL dependency by writing directly to BigQuery.

    :param universes: Splio universes to fetch data from
    :type universes: dict
    :param splio_connection: Splio API parameters
    :type splio_connection: dict
    :param period: Date period for data fetching
    :type period: dict
    :param bigquery_conn_id: BigQuery connection ID
    :type bigquery_conn_id: string
    :param gcs_conn_id: GCS connection ID for temporary files
    :type gcs_conn_id: string
    :param bucket: GCS bucket name
    :type bucket: string
    :param bq_project: BigQuery project name
    :type bq_project: string
    :param chunk_id: Chunk identifier for parallel processing
    :type chunk_id: string
    """

    template_fields = ["period", "bq_project"]

    def __init__(
        self,
        universes,
        splio_connection,
        period,
        bigquery_conn_id="bq_matrix",
        gcs_conn_id="gcs_matrix",
        bucket=None,
        bq_project=None,
        chunk_id="",
        *args,
        **kwargs,
    ):
        super(SplioReportsBigQueryOperator, self).__init__(*args, **kwargs)

        self.universes = universes
        self.splio_connection = splio_connection
        self.period = period
        self.bigquery_conn_id = bigquery_conn_id
        self.gcs_conn_id = gcs_conn_id
        self.bucket = bucket
        self.bq_project = bq_project
        self.chunk_id = chunk_id

    def execute(self, context):
        self.log.info(
            f"Processing {len(self.universes)} universes for period {self.period}"
        )

        # Initialize hooks
        bq_hook = BigQueryHook(gcp_conn_id=self.bigquery_conn_id, use_legacy_sql=False)
        gcs_hook = GCSHook(gcp_conn_id=self.gcs_conn_id)

        # Prepare API parameters
        start_date = datetime.strptime(self.period["start"], "%Y/%m/%d")
        end_date = datetime.strptime(self.period["end"], "%Y/%m/%d")

        # Collect all API data
        api_records = []
        url = "https://s3s.fr/api/reports/nph-1.pl"

        for u_name, u_id in self.universes.items():
            self.log.info(f"Fetching data for universe: {u_name}")

            # Set API parameters for this universe
            api_params = self.splio_connection.copy()
            api_params.update(
                {
                    "universe": u_name,
                    "after": start_date.strftime("%Y-%m-%d"),
                    "before": end_date.strftime("%Y-%m-%d"),
                }
            )

            try:
                # Call Splio API
                response = req.get(url, params=api_params)
                response.raise_for_status()
                splio_json = json.loads(response.content)

                if splio_json.get("result") is None:
                    self.log.info(f"No results for universe {u_name}")
                    continue

                sendouts = splio_json["result"]["sendouts"]

                for sendout_id, data in sendouts.items():
                    if data["status"] in ["aborted", "paused"]:
                        self.log.info(f'Skipping {data["status"]} sendout {sendout_id}')
                        continue

                    # Extract Rogue One email ID from template
                    rogue_one_email_id = self._extract_rogue_one_id(
                        data["message"]["url"]
                    )

                    # Create record for BigQuery
                    record = {
                        "universe_id": u_id,
                        "sendout_id": sendout_id,
                        "campaign_id": data["campaign"],
                        "message_id": data["message"]["id"],
                        "api_data": json.dumps(data, ensure_ascii=False),
                        "rogue_one_email_id": rogue_one_email_id,
                        "create_timestamp": datetime.utcnow().isoformat() + "Z",
                    }

                    api_records.append(record)

                    # Save HTML template to GCS
                    self._save_html_template(
                        gcs_hook, data["message"]["url"], data["message"]["id"]
                    )

            except Exception as e:
                self.log.error(f"Error processing universe {u_name}: {str(e)}")
                continue

        # Always create the chunk table, even if empty
        self._create_chunk_table(bq_hook, context)

        if not api_records:
            self.log.info("No records to process - empty table created")
            return

        # Upload data to BigQuery via GCS
        self._upload_to_bigquery(api_records, bq_hook, gcs_hook, context)

        self.log.info(f"Successfully processed {len(api_records)} records")

    def _extract_rogue_one_id(self, template_url):
        """Extract Rogue One email ID from HTML template"""
        try:
            html_response = req.get(template_url, timeout=30)
            html_response.raise_for_status()

            # Search for Rogue One ID pattern
            match = re.search(r"<!--id:(?P<num>[0-9]+)-->", html_response.text)
            return int(match["num"]) if match else None

        except Exception as e:
            self.log.warning(
                f"Could not extract Rogue One ID from {template_url}: {str(e)}"
            )
            return None

    def _save_html_template(self, gcs_hook, template_url, message_id):
        """Save HTML template to GCS"""
        try:
            html_response = req.get(template_url, timeout=30)
            html_response.raise_for_status()

            # Save to temporary file and upload to GCS
            with tempfile.NamedTemporaryFile(
                mode="w", delete=False, suffix=".html"
            ) as tmp_file:
                tmp_file.write(html_response.text)
                tmp_file_path = tmp_file.name

            gcs_hook.upload(
                bucket_name=self.bucket,
                object_name=f"splio/campaign_html/{message_id}.html",
                filename=tmp_file_path,
                mime_type="text/html",
            )

            # Clean up temporary file
            os.unlink(tmp_file_path)

        except Exception as e:
            self.log.warning(
                f"Could not save HTML template for message {message_id}: {str(e)}"
            )

    def _upload_to_bigquery(self, records, bq_hook, gcs_hook, context):
        """Upload NDJSON data to BigQuery via GCS"""

        # Convert to NDJSON format
        ndjson_data = "\n".join([json.dumps(record) for record in records])

        # Upload NDJSON to GCS temporarily
        ds_nodash = context["ds_nodash"]
        temp_object = f"temp/splio_report_raw_{self.chunk_id}_{ds_nodash}.ndjson"

        with tempfile.NamedTemporaryFile(mode="w", delete=False) as tmp_file:
            tmp_file.write(ndjson_data)
            tmp_file_path = tmp_file.name

        try:
            gcs_hook.upload(
                bucket_name=self.bucket,
                object_name=temp_object,
                filename=tmp_file_path,
                mime_type="application/json",
            )

            # Define BigQuery schema
            schema_fields = [
                {"name": "universe_id", "type": "INTEGER", "mode": "REQUIRED"},
                {"name": "sendout_id", "type": "STRING", "mode": "REQUIRED"},
                {"name": "campaign_id", "type": "STRING", "mode": "REQUIRED"},
                {"name": "message_id", "type": "STRING", "mode": "REQUIRED"},
                {"name": "api_data", "type": "STRING", "mode": "REQUIRED"},
                {"name": "rogue_one_email_id", "type": "INTEGER", "mode": "NULLABLE"},
                {"name": "create_timestamp", "type": "TIMESTAMP", "mode": "REQUIRED"},
            ]

            # Load to BigQuery
            destination_table = (
                f"{self.bq_project}.import.splio_report_raw_{self.chunk_id}_{ds_nodash}"
            )

            bq_hook.run_load(
                destination_project_dataset_table=destination_table,
                source_uris=[f"gs://{self.bucket}/{temp_object}"],
                schema_fields=schema_fields,
                source_format="NEWLINE_DELIMITED_JSON",
                write_disposition="WRITE_TRUNCATE",
                create_disposition="CREATE_IF_NEEDED",
            )

            self.log.info(f"Data loaded to {destination_table}")

        finally:
            # Clean up temporary files
            os.unlink(tmp_file_path)
            try:
                gcs_hook.delete(bucket_name=self.bucket, object_name=temp_object)
            except:
                pass  # Ignore cleanup errors

    def _create_chunk_table(self, bq_hook, context):
        """Create empty chunk table to ensure it exists for SQL UNION operations"""
        ds_nodash = context["ds_nodash"]
        destination_table = (
            f"{self.bq_project}.import.splio_report_raw_{self.chunk_id}_{ds_nodash}"
        )

        # Create empty table with correct schema
        create_sql = f"""
        CREATE TABLE IF NOT EXISTS `{destination_table}` (
            universe_id INT64 NOT NULL,
            sendout_id STRING NOT NULL,
            campaign_id STRING NOT NULL,
            message_id STRING NOT NULL,
            api_data STRING NOT NULL,
            rogue_one_email_id INT64,
            create_timestamp TIMESTAMP NOT NULL
        )
        """

        bq_hook.run_query(create_sql)
        self.log.info(f"Created/ensured chunk table exists: {destination_table}")
