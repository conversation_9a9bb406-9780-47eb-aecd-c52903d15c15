from airflow.exceptions import AirflowSkipException
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator


##################################################################

class SkippableBigQueryOperator(BigQueryExecuteQueryOperator):
    """
    A wrapper to the 'BigQueryOperator' class
    that allows to conditionally skip its execution,
    based on the boolean value of a specified xCom entry.

    REMARK :
        if the specified xCom entry is missing
        (i.e. if it has not been provided by any
        of the specified upstream tasks),
        then the task will NOT be skipped.

    Parameters :
        -- xcom_dag_ids_list (list[str]) :
            (list of upstream) task that are susceptible
            to have set the xCom entry
        -- to_not_be_skipped_xcom_key (str) :
            The key to the xCom entry to be checked against.
        -- if_skipped_message (str - Optional) :
            The message to be logged (INFO) in case
            the execution of the task is indeed skipped
        -- skip_quietly (bool) :
            whether the task is not only NOT-TO-BE-EXECUTED
            but also to be flagged as 'skipped' (in which case,
            any downstrem task is laso to be skipped).
    """
    ui_color = '#e600ff'
    ui_fgcolor = '#00fbff'

    # -------------------------------------------------------------

    def __init__(
            self,
            xcom_dag_ids_list: list,
            to_not_be_skipped_xcom_key: str,
            if_skipped_message: str = '',
            skip_quietly: bool = False,
            *args, **kwargs
    ) -> object:

        super(SkippableBigQueryOperator, self).__init__(*args, **kwargs)
        self.xcom_dag_ids_list = xcom_dag_ids_list
        self.to_not_be_skipped_xcom_key = to_not_be_skipped_xcom_key
        self.if_skipped_message = if_skipped_message
        self.skip_quietly = skip_quietly

    # -------------------------------------------------------------
    def execute(self, context) -> bool:
        to_not_be_skipped_list = context['ti'].xcom_pull(
            key=self.to_not_be_skipped_xcom_key,
            task_ids=self.xcom_dag_ids_list
        )

        if len(to_not_be_skipped_list) == 0 or bool(to_not_be_skipped_list[0]):
            # proceed with the execution of the BigQueryExecuteQueryOperator
            super().execute(context)
        else:
            # Skip "successfully" the execution of the BigQueryExecuteQueryOperator
            self.log.info(
                'SKIPPING TASK [' + str(to_not_be_skipped_list) + ']')
            if self.if_skipped_message: self.log.info(self.if_skipped_message)

            # note that if instructed not to, we do not actually raise an
            # 'airflow.exceptions.AirflowSkipException",
            # as it would put the DAG in a "not ready" state
            # for the next scheduled run.
            if not self.skip_quietly:
                # The task will be flagged as 'skipped'
                # # AND any downstream task would be skipped as well
                raise AirflowSkipException
    # -------------------------------------------------------------
