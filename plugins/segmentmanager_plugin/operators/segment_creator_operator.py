import datetime
from pprint import pprint

import pytz
from airflow.models import <PERSON><PERSON>perator
from airflow.providers.google.cloud.hooks.bigquery import BigQueryHook
from airflow.providers.postgres.hooks.postgres import PostgresHook
from google.api_core.exceptions import NotFound
from google.cloud import bigquery
from jinja2 import Template


class SegmentCreatorOperator(BaseOperator):
    template_fields = ('export_definitions_sql', 'export_definitions_to_clean_sql')
    template_ext = ('.sql',)
    ui_color = '#00ff55'

    def __init__(self,
                 export_definitions_sql,
                 export_definitions_to_clean_sql,
                 create_segments_sql,
                 store_segment_dataset,
                 gcp_conn_id='bq_matrix',
                 database='matrix',
                 bq_project='',
                 postgres_conn_id='psql_picasso_app',
                 location='EU',
                 identifier='sha256',
                 operator='AND',
                 *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.segment_definition_id = None
        self.export_definitions_sql = export_definitions_sql
        self.export_definitions_to_clean_sql = export_definitions_to_clean_sql
        self.create_segments_sql = create_segments_sql
        self.store_segment_dataset = store_segment_dataset
        self.database = database
        self.postgres_conn_id = postgres_conn_id
        self.gcp_conn_id = gcp_conn_id
        self.bq_project = bq_project
        self.location = location
        self.identifier = identifier
        self.operator = operator

        # Initialize database hooks
        self.pg_hook = PostgresHook(postgres_conn_id=self.postgres_conn_id, database=self.database)
        self.bq_hook = BigQueryHook(gcp_conn_id=self.gcp_conn_id, location=self.location)
        # init BQ client
        self.client = bigquery.Client(project=self.bq_project, location=self.location)

    def execute(self, context):
        dag_run_conf = context.get('dag_run').conf
        if 'segment_definition_id' in dag_run_conf:
            self.segment_definition_id = dag_run_conf['segment_definition_id']
        if self.segment_definition_id is None:
            self.clear_not_used_definitions()
        self.create_segment_from_definitions()

    def create_segment_from_definitions(self):
        definitions_sql = self.export_definitions_sql

        if self.segment_definition_id is not None:
            definitions_sql += f" {self.operator} sd.id = {self.segment_definition_id}"

        segment_definitions = self.get_segment_definitions(sql=definitions_sql)
        if not isinstance(segment_definitions, list):
            self.log.warning("No segment definitions found to create segment.")
            return
        pprint(segment_definitions)
        for segment_definition in segment_definitions:
            self.create_segment_from_definition(segment_definition)

    def get_segment_definitions(self, sql):
        try:
            records = self.pg_hook.get_records(sql)
            keys = ['id', 'sql_definition', 'type', 'universe', 'name', 'description', 'frequency']
            return [dict(zip(keys, record)) for record in records]
        except Exception as e:
            self.log.error(f"Failed to get segment definitions: {str(e)}")

    def create_segment_from_definition(self, segment_definition):
        try:
            task_id = f'execute_create_bigquery_segment_{segment_definition["id"]}_{segment_definition["name"]}'.lower()
            self.log.info(f"Creating BigQuery table for task {task_id}")

            # Load the sql
            with open(self.create_segments_sql, 'r') as file:
                create_segments_sql_template = file.read()

            # Render the sql using jinja2
            template = Template(create_segments_sql_template)

            # Generate the sql
            rendered_sql = template.render(
                dag=self,
                task={'task_id': task_id},
                params={
                    'bq_project': self.bq_project,
                    'segment_definition': segment_definition,
                    'store_segment_dataset': self.store_segment_dataset,
                    'dag': self
                },
            )

            # Run the query
            self.bq_hook.run_query(sql=rendered_sql, use_legacy_sql=False)

            self.handle_prunes(segment_definition)

            # Count inserted rows
            count_sql = f"""
                    -- mozart-id : {self.dag.dag_id}.{task_id}
                    SELECT COUNT(*)
                    FROM `{self.bq_project}.{self.store_segment_dataset}.segment-{self.identifier}_{segment_definition['name'].lower()}`
                """

            query_result = self.client.query(count_sql, location="EU").result()
            count = next(iter(query_result))[0]

            # Update segment_definition in Postgres
            self.update_segment_definition(segment_definition, count)
        except Exception as e:
            self.log.error(f"Failed to create BigQuery table: {str(e)}")
            raise e

    def update_segment_definition(self, segment_definition, rows_inserted):
        try:
            # Your previous update SQL remains unchanged:
            update_sql = """
                UPDATE picasso.segment_definition
                SET calculated_at = now(), total = %s
                WHERE id = %s
            """
            self.pg_hook.run(update_sql, parameters=(rows_inserted, str(segment_definition['id'])))

            # The upsert statement for the picasso.segment_meta_data table:
            upsert_sql = """
                INSERT INTO picasso.segment_meta_data (segment_definition_id, name, big_query_table, total, created_at, updated_at)
                VALUES (%s, %s, %s, %s, now(), now())
                ON CONFLICT (segment_definition_id, name) DO UPDATE 
                SET total = EXCLUDED.total, updated_at = now()
            """

            segment_name = f"{segment_definition['name'].lower()}"
            full_big_query_table_name = f"{self.bq_project}.{self.store_segment_dataset}.segment-{self.identifier}_{segment_definition['name'].lower()}"

            # Execute the upsert statement
            self.pg_hook.run(upsert_sql, parameters=(
                str(segment_definition['id']), segment_name, full_big_query_table_name, rows_inserted))

        except Exception as e:
            self.log.error(f"Failed to update segment definition: {str(e)}")

    def clear_not_used_definitions(self):
        segment_definitions = self.get_segment_definitions(sql=self.export_definitions_to_clean_sql)
        if not isinstance(segment_definitions, list):
            self.log.warning("No segment definitions found to clear.")
            return
        for segment_definition in segment_definitions:
            self.move_table_to_archive(segment_definition)

    def move_table_to_archive(self, segment_definition):
        try:
            # Create the dataset reference
            dataset_ref = self.client.dataset(self.store_segment_dataset)

            # Create the table reference
            table_ref = dataset_ref.table(f"segment-{self.identifier}_{segment_definition['name'].lower()}")

            # Check if table exists
            try:
                self.client.get_table(table_ref)
                table_exists = True
            except NotFound:
                table_exists = False

            if table_exists:
                # Copy the table to the archived dataset
                dest_dataset_ref = self.client.dataset(f'{self.store_segment_dataset}_archived')

                # Append current timestamp to table id for archiving
                now = datetime.datetime.now(pytz.timezone('Europe/Paris'))
                new_table_id = f"{table_ref.table_id}_{now.strftime('%Y%m%d%H%M%S')}"
                dest_table_ref = dest_dataset_ref.table(new_table_id)

                job_config = bigquery.CopyJobConfig()
                job_config.write_disposition = 'WRITE_TRUNCATE'

                copy_job = self.client.copy_table(
                    sources=table_ref,
                    destination=dest_table_ref,
                    job_config=job_config
                )

                copy_job.result()  # Wait for job to complete

                # Set expiration to 10 days
                table_to_expire = self.client.get_table(dest_table_ref)
                table_to_expire.expires = datetime.datetime.now() + datetime.timedelta(days=10)
                self.client.update_table(table_to_expire, ["expires"])

                # Delete the table in the temporary dataset
                self.client.delete_table(table_ref)

                self.log.info(f'Table {table_ref.table_id} moved to archive and set to expire in 10 days.')
            else:
                self.log.info(f'Table {table_ref.table_id} does not exist, skipping.')

        except Exception as e:
            self.log.error(f'Error occurred while trying to clear table: {str(e)}')
            raise e

    def handle_prunes(self, segment_definition):
        sql = "SELECT prune_id FROM picasso.segment_prune WHERE segment_definition_id = %s"
        prunes = self.pg_hook.get_records(
            sql=sql,
            parameters=(segment_definition['id'],)
        )
        pprint(prunes)
        # prune_ids = dict(zip(['prune_id'], prune) for prune in prunes)

        # if len(prune_ids) > 0:
        # prune_str = ','.join()
        # bq_tables = self.pg_hook.get_records(f'SELECT big_query_table FROM picasso.segment_meta_data WHERE segment_definition_id = ({prune_str})')

        # if len(bq_tables) > 0:
