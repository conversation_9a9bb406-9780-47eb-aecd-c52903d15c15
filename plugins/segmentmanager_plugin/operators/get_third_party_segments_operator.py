# -*- coding: utf-8 -*-
#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

from tempfile import NamedTemporaryFile

import pandas as pd
from airflow.models import BaseOperator
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from googleads import ad_manager
from googleads import oauth2


class GetThirdPartySegmentsOperator(BaseOperator):
    template_fields = ('gcs_object_path',)

    # @apply_defaults
    def __init__(self,
                 gcs_conn_id='google_cloud_default',
                 gcs_bucket='',
                 gcs_object_path='',
                 delegate_to=None,
                 dfp_key_file='',
                 *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.gcs_conn_id = gcs_conn_id
        self.gcs_bucket = gcs_bucket
        self.gcs_object_path = gcs_object_path
        self.delegate_to = delegate_to
        self.dfp_key_file = dfp_key_file

    def get_dfp_segment_df(self, data_provider):
        oauth2_client = oauth2.GoogleServiceAccountClient(self.dfp_key_file, oauth2.GetAPIScope('ad_manager'))
        client = ad_manager.AdManagerClient(oauth2_client, 'Prisma Media - Premium', '*********')
        audience_segment_service = client.GetService('AudienceSegmentService', version='v202408')
        statement = (ad_manager.StatementBuilder(version='v202408')
                     .Where('type = :type AND status = :status AND size >= :size AND dataProviderName = :data_provider')
                     .WithBindVariable('type', 'THIRD_PARTY')
                     .WithBindVariable('status', 'ACTIVE')
                     .WithBindVariable('data_provider', data_provider)
                     .WithBindVariable('size', 200000))

        gam_data = []

        while True:
            id = 0
            name = ''
            desc = ''
            size = 0
            mobile = 0
            idfa = 0
            adid = 0
            ppid = 0
            cost = 0
            provider = ''
            response = audience_segment_service.getAudienceSegmentsByStatement(statement.ToStatement())

            if 'results' in response and len(response['results']):
                for segment in response['results']:
                    id = segment['id']
                    name = segment['name']
                    desc = segment['description']
                    size = segment['size']
                    mobile = segment['mobileWebSize']
                    idfa = segment['idfaSize']
                    adid = segment['adIdSize']
                    ppid = segment['ppidSize']
                    cost = str(segment['cost']['microAmount'] / 1000000) + ' ' + segment['cost']['currencyCode']
                    provider = segment['dataProvider']['name']
                    gam_data.append([id, name, desc, size, mobile, idfa, adid, ppid, cost, provider])

                statement.offset += statement.limit
            else:
                break

        df_cols = ['id', 'name', 'desc', 'size', 'mobile', 'idfa', 'adid', 'ppid', 'cost', 'provider']
        gam_df = pd.DataFrame(gam_data, columns=df_cols)

        return gam_df

    def split_exelate_df(self, exelate_df):
        split = exelate_df['name'].str.split('-', expand=True)
        split.columns = ['drop', 'src', 'typ', 'vert', 'nam', 'cont', 'fin']
        split = split.apply(lambda x: x.str.strip())
        split[['id']] = exelate_df[['id']]

        interest = split.loc[split['typ'].str.strip() == 'Interest']
        interest['source'] = interest[['src']]
        interest['type'] = interest[['typ']]
        interest['vertical'] = interest[['vert']]
        interest['name'] = interest[interest.columns[4:6]].apply(lambda x: ' - '.join(x.dropna().astype(str)), axis=1)
        interest.loc[interest['name'] == '', 'name'] = interest['vertical']
        interest.drop(['drop', 'src', 'typ', 'vert', 'nam', 'cont', 'fin'], axis=1, inplace=True)

        declarative = split.loc[split['typ'].str.strip() != 'Interest']
        declarative['source'] = declarative[['src']]
        declarative['type'] = 'Déclarative'
        declarative['vertical'] = declarative[['typ']]
        declarative['name'] = declarative[declarative.columns[3:6]].apply(lambda x: ' - '.join(x.dropna().astype(str)),
                                                                          axis=1)
        declarative.drop(['drop', 'src', 'typ', 'vert', 'nam', 'cont', 'fin'], axis=1, inplace=True)

        joined = pd.concat([interest, declarative])

        cols = ['id', 'ref', 'name', 'source', 'vertical', 'type', 'desc', 'size', 'mobile', 'idfa', 'adid', 'ppid',
                'cost']
        df = pd.DataFrame()
        df[['id', 'ref', 'desc', 'size', 'mobile', 'idfa', 'adid', 'ppid', 'cost']] = exelate_df[
            ['id', 'name', 'desc', 'size', 'mobile', 'idfa', 'adid', 'ppid', 'cost']]
        df = df.merge(joined, left_on='id', right_on='id')
        df = df[cols]

        return df

    def split_sirdata_df(self, sirdata_df):
        split_name = sirdata_df['name'].str.split('[', expand=True)
        split_name.columns = ['drop', 'tmp']
        split_name.drop(['drop'], axis=1, inplace=True)
        split_name = split_name[split_name.columns[0]].str.split(']', expand=True)
        split_name.columns = ['type', 'tmp']
        split_name[['vertical', 'name']] = split_name['tmp'].str.split('>', n=1, expand=True)
        split_name = split_name.mask(split_name == '')
        split_name['vertical'].fillna(split_name['name'], inplace=True)
        split_name['name'].fillna(split_name['vertical'], inplace=True)
        split_name['name'] = split_name['name'].str.replace('>', '-')
        split_name['id'] = sirdata_df['id']
        split_name.drop(['tmp'], axis=1, inplace=True)

        cols = ['id', 'ref', 'name', 'source', 'vertical', 'type', 'desc', 'size', 'mobile', 'idfa', 'adid', 'ppid',
                'cost']
        df = pd.DataFrame()
        df[['id', 'ref', 'desc', 'size', 'mobile', 'idfa', 'adid', 'ppid', 'source', 'cost']] = sirdata_df[
            ['id', 'name', 'desc', 'size', 'mobile', 'idfa', 'adid', 'ppid', 'provider', 'cost']]
        df = df.merge(split_name, left_on='id', right_on='id')
        df = df[cols]

        return df

    def save_to_gcs(self, csv_content, filename):
        with NamedTemporaryFile(mode='w+') as tmpfile:
            tmpfile.write(csv_content)
            self.log.info('The temp filename is %s', tmpfile.name)
            bucket = self.gcs_bucket
            object = self.gcs_object_path + filename
            # flush to force close file
            tmpfile.flush()
            gcs_hook = GCSHook(
                gcp_conn_id=self.gcs_conn_id,
                delegate_to=self.delegate_to
            )
            gcs_hook.upload(bucket_name=bucket, object_name=object, filename=tmpfile.name, mime_type='text/csv')

    def execute(self, context):
        sirdata_df = self.get_dfp_segment_df(data_provider='Sirdata')
        self.save_to_gcs(csv_content=sirdata_df.to_csv(index=False, sep=';'), filename='sirdata_raw.csv')

        if not sirdata_df.empty:
            sirdata_df = sirdata_df[sirdata_df['name'].str.contains('\[')]
            sirdata_df = self.split_sirdata_df(sirdata_df=sirdata_df)
            self.save_to_gcs(csv_content=sirdata_df.to_csv(index=False, sep=';'), filename='sirdata_split.csv')
        else:
            sirdata_df = pd.DataFrame(columns=['id', 'ref', 'name', 'source', 'vertical', 'type', 'desc', 'size', 'mobile', 'idfa', 'adid', 'ppid', 'cost'])

        exelate_df = self.get_dfp_segment_df(data_provider='NMC 3PD')
        self.save_to_gcs(csv_content=exelate_df.to_csv(index=False, sep=';'), filename='exelate_raw.csv')

        if not exelate_df.empty:
            exelate_df = exelate_df[exelate_df['name'].str.contains('Shopmium FR')]
            exelate_df = self.split_exelate_df(exelate_df=exelate_df)
            self.save_to_gcs(csv_content=exelate_df.to_csv(index=False, sep=';'), filename='exelate_split.csv')
        else:
            exelate_df = pd.DataFrame(columns=['id', 'ref', 'name', 'source', 'vertical', 'type', 'desc', 'size', 'mobile', 'idfa', 'adid', 'ppid', 'cost'])

        gam_df = pd.concat([sirdata_df, exelate_df], ignore_index=True)
        gam_df['id'] = gam_df['id'].fillna(0).astype('int64')
        gam_df['size'] = gam_df['size'].fillna(0).astype('int64')
        gam_df['mobile'] = gam_df['mobile'].fillna(0).astype('int64')
        gam_df['idfa'] = gam_df['idfa'].fillna(0).astype('int64')
        gam_df['adid'] = gam_df['adid'].fillna(0).astype('int64')
        gam_df['ppid'] = gam_df['ppid'].fillna(0).astype('int64')
        gam_df['cost'] = gam_df['cost'].fillna('0.0 EUR')
        df_obj = gam_df.select_dtypes(['object'])
        gam_df[df_obj.columns] = df_obj.apply(lambda x: x.str.strip())
        gam_df = gam_df.replace(r'\n', ' ', regex=True)
        gam_df.sort_values(by=['id', 'size'], axis=0, ascending=[True, False], inplace=True)
        gam_df.drop_duplicates(subset=['id'], keep='first', inplace=True)

        self.save_to_gcs(csv_content=gam_df.to_csv(index=False, sep=';'), filename='thirdparty_segments.csv')

        return True
