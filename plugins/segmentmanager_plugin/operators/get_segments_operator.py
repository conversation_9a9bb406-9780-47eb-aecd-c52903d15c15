# -*- coding: utf-8 -*-
#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

from tempfile import NamedTemporaryFile

import pandas as pd
import requests
import numpy as np
from airflow.models import BaseOperator
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from googleads import ad_manager
from googleads import oauth2

from pprint import pprint


# noinspection PyInterpreter
class GetSegmentsOperator(BaseOperator):
    template_fields = ('gcs_object_path',)
    query_firstids = 'SELECT @count{} FROM UserDevicePoint WHERE technical_identifiers {registry_id="21"} JOIN UserSegment WHERE'
    query_utiqids = 'SELECT @count{} FROM UserAccount WHERE compartment_id="20" JOIN UserSegment WHERE'
    query_emails = 'SELECT @count{} FROM UserEmail JOIN UserSegment WHERE'
    query_userpoints = 'SELECT @count{} FROM UserPoint JOIN UserSegment WHERE'

    def __init__(self,
                 mics_api_key='',
                 gcs_conn_id='google_cloud_default',
                 gcs_bucket='',
                 gcs_object_path='',
                 delegate_to=None,
                 dfp_key_file='',
                 *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.mics_api_key = mics_api_key
        self.gcs_conn_id = gcs_conn_id
        self.gcs_bucket = gcs_bucket
        self.gcs_object_path = gcs_object_path
        self.delegate_to = delegate_to
        self.dfp_key_file = dfp_key_file

    def get_segment_info(self, first_result=0):
        endpoint = 'https://api.mediarithmics.com/v1/audience_segments?organisation_id=1695'
        header = {'Authorization': self.mics_api_key}

        if first_result > 0:
            endpoint = endpoint + '&first_result={}'.format(first_result)

        response = requests.get(endpoint, headers=header)

        return response.json()

    def get_combined_metric(self, query, ids):
        endpoint = 'https://api.mediarithmics.com/v1/datamarts/1759/query_executions/otql?use_cache=true'
        header = {'Authorization': self.mics_api_key}

        condition = []
        for segment_id in ids:
            condition.append(' id == "{}" '.format(segment_id))

        condition = 'OR'.join(condition)
        response = requests.post(endpoint, headers=header, data=query + condition)
        response = response.json()

        return response['data']['rows'][0]['count']

    def get_segment_external_feeds(self, segment_id):
        endpoint = 'https://api.mediarithmics.com/v1/audience_segments/{}/external_feeds?organisation_id=1695'.format(
            segment_id)
        header = {'Authorization': self.mics_api_key}

        response = requests.get(endpoint, headers=header)

        return response.json()

    def get_segment_external_feeds_properties(self, segment_id, feed_id):
        endpoint = 'https://api.mediarithmics.com/v1/audience_segments/{}/external_feeds/{}/properties?organisation_id=1695'.format(
            segment_id, feed_id)
        header = {'Authorization': self.mics_api_key}

        response = requests.get(endpoint, headers=header)

        return response.json()

    def get_segments_dataframe(self, iterations, max_result):
        intermediate_dfs = []
        id_mapping = {
            '185': 'userpoints',
            '186': 'cookies',
            '187': 'emails',
            '188': 'first_ids',
            '191': 'utiq_ids'
        }

        for i in range(0, iterations):
            segments = self.get_segment_info(first_result=i * max_result)

            for j in range(0, max_result):
                data = segments['data'][j]
                metrics = data['audience_metrics']

                for metric in metrics:
                    metric_id = metric['id']

                    if metric_id in id_mapping:
                        data[id_mapping[metric_id]] = metric['count']

                del data['audience_metrics']

                segment_data = pd.DataFrame.from_dict([data], orient='columns')

                # trimming strings in the whole DataFrame
                df_obj = segment_data.select_dtypes(['object'])
                segment_data[df_obj.columns] = df_obj.apply(lambda x: x.str.strip())

                segment_data = segment_data[['id',
                                             'name',
                                             'short_description',
                                             'emails',
                                             'userpoints',
                                             'cookies',
                                             'first_ids',
                                             'utiq_ids',
                                             'creation_ts',
                                             'last_modified_ts']]
                intermediate_dfs.append(segment_data)

        raw_data = pd.DataFrame()
        raw_data = pd.concat(intermediate_dfs)
        raw_data.drop_duplicates(inplace=True)
        raw_data.reset_index(inplace=True, drop=True)

        return raw_data

    def get_grouped_segments_data(self, segments_df):
        segments_df['short_description'].replace('', np.nan, inplace=True)
        segments_df.dropna(subset=['short_description'], inplace=True)
        segments_df.reset_index(drop=True, inplace=True)
        segments_df['combined_first_ids'] = np.nan
        segments_df['combined_emails'] = np.nan
        segments_df['combined_userpoints'] = np.nan
        segments_df['combined_gam_ids'] = ''
        segments_df['combined_utiq_ids'] = np.nan
        segments_df['dmp_gam_id'] = segments_df['dmp_gam_id'].fillna(0).astype('int64')
        grouped_segments = segments_df.groupby('short_description')['id'].apply(list)

        for row in grouped_segments:

            if len(row) > 1:
                utiq_ids = self.get_combined_metric(self.query_utiqids, row)
                first_ids = self.get_combined_metric(self.query_firstids, row)
                emails = self.get_combined_metric(self.query_emails, row)
                userpoints = self.get_combined_metric(self.query_userpoints, row)
                gam_ids = segments_df.loc[segments_df['id'].isin(row)]['dmp_gam_id'].tolist()
                combined_gam_ids = ', '.join(str(id) for id in gam_ids if id != 0)

                for segment_id in row:
                    segments_df.loc[segments_df['id'] == segment_id, 'combined_first_ids'] = first_ids
                    segments_df.loc[segments_df['id'] == segment_id, 'combined_utiq_ids'] = utiq_ids
                    segments_df.loc[segments_df['id'] == segment_id, 'combined_emails'] = emails
                    segments_df.loc[segments_df['id'] == segment_id, 'combined_userpoints'] = userpoints
                    segments_df.loc[segments_df['id'] == segment_id, 'combined_gam_ids'] = combined_gam_ids
            else:
                segments_df.loc[segments_df['id'] == row[0], 'combined_first_ids'] = segments_df.loc[segments_df['id'] == row[0]]['first_ids']
                segments_df.loc[segments_df['id'] == row[0], 'combined_utiq_ids'] = segments_df.loc[segments_df['id'] == row[0]]['utiq_ids']
                segments_df.loc[segments_df['id'] == row[0], 'combined_emails'] = segments_df.loc[segments_df['id'] == row[0]]['emails']
                segments_df.loc[segments_df['id'] == row[0], 'combined_userpoints'] = segments_df.loc[segments_df['id'] == row[0]]['userpoints']
                segments_df.loc[segments_df['id'] == row[0], 'combined_gam_ids'] = segments_df.loc[segments_df['id'] == row[0]]['dmp_gam_id']

        segments_df['combined_gam_ids'].replace(0, None, inplace=True)
        segments_df['combined_gam_ids'].replace('0', None, inplace=True)
        segments_df.rename(columns={'id': 'segment_id'}, inplace=True)
        segments_df.drop(
            columns=['name', 'data', 'source', 'segment_type', 'vertical', 'segment_name', 'short_description',
                     'emails', 'userpoints', 'cookies', 'first_ids', 'utiq_ids', 'CPC', 'creation_ts',
                     'last_modified_ts', 'entity_id', 'dmp_gam_id', 'source_new', 'type_new', 'CPM', 'name_new'],
            axis=1,
            inplace=True
        )

        return segments_df

    def split_and_clean_segments(self, segments, ds=False):
        final_columns = ['id', 'name', 'data', 'source', 'segment_type', 'vertical', 'segment_name',
                         'short_description', 'emails', 'userpoints', 'cookies', 'first_ids', 'utiq_ids',
                         'CPC', 'creation_ts', 'last_modified_ts', 'source_new', 'type_new', 'CPM', 'name_new']

        split_data_name = segments['name'].str.split('_', expand=True)[range(5)].rename(
            columns={0: 'data', 1: 'source', 2: 'segment_type', 3: 'vertical', 4: 'segment_name'}
        )

        if not ds:
            split_description = segments['short_description'].str.split(' - ', n=3, expand=True)[range(4)].rename(
                columns={0: 'source_new', 1: 'type_new', 2: 'CPM', 3: 'name_new'}
            )
        else:
            split_description = pd.DataFrame(columns=['source_new', 'type_new', 'CPM', 'name_new'])

        segments = pd.concat([segments, split_data_name], axis=1)
        segments = pd.concat([segments, split_description], axis=1)

        prisma_sources = ['PRISMA', 'GENTSIDE', 'TELELOISIR', 'TELELOISIRS']
        condition_1 = segments['segment_type'] == 'INTERET'
        condition_2 = segments['source'].isin( prisma_sources )
        segments.loc[condition_1 & condition_2, 'CPC'] = 1
        segments.loc[~(condition_1 & condition_2), 'CPC'] = 2
        segments['CPC'] = segments['CPC'].astype('int64')

        if ds:
            segments['segment_name'] = segments['segment_name'] + ' DS'

        segments['segment_name'] = segments['segment_name'].str.replace('-', ' ')
        segments['short_description'] = segments['short_description'].str.replace(';', '-')
        segments['creation_ts'] = pd.to_datetime(segments['creation_ts'], unit='ms')
        segments['last_modified_ts'] = pd.to_datetime(segments['last_modified_ts'], unit='ms')

        segments = segments.drop(segments[segments['CPC'] == 0].index)
        segments = segments.dropna(subset=['vertical', 'segment_name'])
        segments = segments.sort_values('userpoints', ascending=False).drop_duplicates(['name'], keep='first')
        segments = segments[final_columns]
        segments.reset_index(inplace=True, drop=True)

        return segments

    def get_dfp_segment_df(self):
        oauth2_client = oauth2.GoogleServiceAccountClient(self.dfp_key_file, oauth2.GetAPIScope('ad_manager'))
        client = ad_manager.AdManagerClient(oauth2_client, 'Prisma Media - Premium', '*********')
        audience_segment_service = client.GetService('AudienceSegmentService', version='v202408')
        statement = (ad_manager.StatementBuilder(version='v202408')
                     .Where('type = :type')
                     .WithBindVariable('type', 'FIRST_PARTY'))
        gam_data = []

        while True:
            gam_id = 0
            gam_name = ''
            gam_desc = ''
            gam_size = 0
            gam_mobile = 0
            gam_idfa = 0
            gam_adid = 0
            gam_ppid = 0
            response = audience_segment_service.getAudienceSegmentsByStatement(statement.ToStatement())

            if 'results' in response and len(response['results']):
                for segment in response['results']:
                    gam_id = segment['id']
                    gam_name = segment['name']
                    gam_desc = segment['description']
                    gam_size = segment['size']
                    gam_mobile = segment['mobileWebSize']
                    gam_idfa = segment['idfaSize']
                    gam_adid = segment['adIdSize']
                    gam_ppid = segment['ppidSize']
                    gam_data.append([gam_id, gam_name, gam_desc, gam_size, gam_mobile, gam_idfa, gam_adid, gam_ppid])

                statement.offset += statement.limit
            else:
                break

        df_cols = ['gam_id', 'gam_name', 'gam_desc', 'gam_size', 'gam_mobile', 'gam_idfa', 'gam_adid', 'gam_ppid']
        gam_df = pd.DataFrame(gam_data, columns=df_cols)
        gam_df.drop_duplicates(inplace=True)

        return gam_df

    def save_to_gcs(self, csv_content, filename):
        with NamedTemporaryFile(mode='w+') as tmpfile:
            tmpfile.write(csv_content)
            self.log.info('The temp filename is %s', tmpfile.name)
            bucket = self.gcs_bucket
            object = self.gcs_object_path + filename
            # flush to force close file
            tmpfile.flush()
            gcs_hook = GCSHook(
                gcp_conn_id=self.gcs_conn_id,
                delegate_to=self.delegate_to
            )
            gcs_hook.upload(bucket_name=bucket, object_name=object, filename=tmpfile.name, mime_type='text/csv')

    def execute(self, context):
        inter_dfs = []
        segment_info = self.get_segment_info()
        total_segment = segment_info['total']
        max_result = segment_info['max_results']
        iterations = int((total_segment / max_result))
        raw_data = self.get_segments_dataframe(iterations=iterations, max_result=max_result)
        self.save_to_gcs(csv_content=raw_data.to_csv(index=False, sep=';'), filename='raw_data.csv')

        for segment_id in raw_data['id']:
            feeds = self.get_segment_external_feeds(segment_id=segment_id)

            for feed in feeds['data']:
                entity_id = ''
                dmp_gam_id = None
                properties = self.get_segment_external_feeds_properties(segment_id=segment_id, feed_id=feed['id'])

                for prop in properties['data']:
                    if prop['technical_name'] == 'entity_id':
                        entity_id = prop['value']['value']
                    if prop['technical_name'] == 'google_user_list_id':
                        dmp_gam_id = prop['value']['value']

                inter_dfs.append([segment_id, entity_id, dmp_gam_id])

        prop_data = pd.DataFrame(inter_dfs, columns=['segment_id', 'entity_id', 'dmp_gam_id'])
        self.save_to_gcs(csv_content=prop_data.to_csv(index=False, sep=';'), filename='prop_segments.csv')
        prop_data = prop_data[prop_data['entity_id'] == '150605569']
        prop_data['dmp_gam_id'] = prop_data['dmp_gam_id'].fillna(0).astype('int64')

        extended_segments = raw_data[raw_data['name'].str.lower().str.contains('_ds')]
        segments = raw_data[~raw_data['name'].str.lower().str.contains('_ds')]

        extended_segments = self.split_and_clean_segments(extended_segments, ds=True)
        segments = self.split_and_clean_segments(segments)

        segments_df = pd.concat([segments, extended_segments], ignore_index=True)
        segments_df = pd.merge(segments_df, prop_data, left_on='id', right_on='segment_id', how='left')
        segments_df.drop(['segment_id'], axis=1, inplace=True)
        self.save_to_gcs(csv_content=segments_df.to_csv(index=False, sep=';'), filename='split_segments.csv')

        grouped_segments_data = self.get_grouped_segments_data(segments_df.copy())
        self.save_to_gcs(csv_content=grouped_segments_data.to_csv(index=False, sep=';'), filename='grouped_segments.csv')
        segments_df = segments_df.merge(grouped_segments_data, left_on='id', right_on='segment_id', how='left')
        segments_df.drop(['segment_id'], axis=1, inplace=True)

        self.save_to_gcs(csv_content=segments_df.to_csv(index=False, sep=';'), filename='full_segments.csv')
        gam_df = self.get_dfp_segment_df()

        final_df = segments_df.merge(gam_df, left_on='dmp_gam_id', right_on='gam_id', how='left')
        final_df.drop(['entity_id', 'dmp_gam_id'], axis=1, inplace=True)
        final_df['emails'] = final_df['emails'].fillna(0).astype('int64')
        final_df['userpoints'] = final_df['userpoints'].fillna(0).astype('int64')
        final_df['cookies'] = final_df['cookies'].fillna(0).astype('int64')
        final_df['first_ids'] = final_df['first_ids'].fillna(0).astype('int64')
        final_df['utiq_ids'] = final_df['utiq_ids'].fillna(0).astype('int64')
        final_df['gam_id'] = final_df['gam_id'].fillna(0).astype('int64')
        final_df['gam_size'] = final_df['gam_size'].fillna(0).astype('int64')
        final_df['gam_mobile'] = final_df['gam_mobile'].fillna(0).astype('int64')
        final_df['gam_idfa'] = final_df['gam_idfa'].fillna(0).astype('int64')
        final_df['gam_adid'] = final_df['gam_adid'].fillna(0).astype('int64')
        final_df['gam_ppid'] = final_df['gam_ppid'].fillna(0).astype('int64')
        final_df['combined_first_ids'] = final_df['combined_first_ids'].fillna(0).astype('int64')
        final_df['combined_utiq_ids'] = final_df['combined_utiq_ids'].fillna(0).astype('int64')
        final_df['combined_emails'] = final_df['combined_emails'].fillna(0).astype('int64')
        final_df['combined_userpoints'] = final_df['combined_userpoints'].fillna(0).astype('int64')
        final_df.sort_values(by=['id', 'gam_size'], axis=0, ascending=[True, False], inplace=True)
        final_df.drop_duplicates(subset=['id'], keep='first', inplace=True)
        self.save_to_gcs(csv_content=final_df.to_csv(index=False, sep=';'), filename='segments.csv')

        return True
