from airflow.models import BaseOperator
from airflow.providers.google.cloud.hooks.bigquery import BigQueryHook
from airflow.providers.postgres.hooks.postgres import PostgresHook
from google.cloud import bigquery
from jinja2 import Template


class SegmentExtractExportOperator(BaseOperator):
    template_fields = ('export_sql',)
    template_ext = ('.sql',)
    ui_color = '#dd00ff'

    def __init__(self,
                 export_sql,
                 bq_export_sql,
                 store_extract_dataset,
                 gcp_conn_id='bq_matrix',
                 database='matrix',
                 bq_project='',
                 postgres_conn_id='psql_picasso_app',
                 location='EU',
                 gcs_bucket='pm-prod-picasso',
                 gcs_path='extract_segment',
                 *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.export_sql = export_sql
        self.bq_export_sql = bq_export_sql
        self.store_extract_dataset = store_extract_dataset
        self.database = database
        self.postgres_conn_id = postgres_conn_id
        self.gcp_conn_id = gcp_conn_id
        self.bq_project = bq_project
        self.location = location
        self.gcs_bucket = gcs_bucket
        self.gcs_path = gcs_path

        # Initialize database hooks
        self.pg_hook = PostgresHook(postgres_conn_id=self.postgres_conn_id, database=self.database)
        self.bq_hook = BigQueryHook(gcp_conn_id=self.gcp_conn_id, location=self.location)
        # init BQ client
        self.client = bigquery.Client(project=self.bq_project, location=self.location)

    def execute(self, context):
        extracts = self.get_extract_ids()
        for extract in extracts:
            try:
                task_id = f"execute_create_bigquery_export_{extract['segment_definition_id']}_{extract['id']}"
                self.log.info(f"Creating BigQuery temp table for task {task_id}")

                with open(self.bq_export_sql, 'r') as sql_file:
                    bq_export_sql_template = sql_file.read()

                template = Template(bq_export_sql_template)
                rendered_template = template.render(
                    dag=self,
                    task={'task_id': task_id},
                    params={
                        'bq_project': self.bq_project,
                        'extract_id': extract['id'],
                        'segment_definition_id': extract['segment_definition_id'],
                        'store_extract_dataset': self.store_extract_dataset,
                        'dag': self,
                    },
                )

                self.bq_hook.run_query(sql=rendered_template, use_legacy_sql=False)
                self.bq_hook.run_extract(
                    source_project_dataset_table=f"{self.bq_project}.export_postal.extract_{extract['segment_definition_id']}_{extract['id']}",
                    destination_cloud_storage_uris=[
                        f"gs://{self.gcs_bucket}/{self.gcs_path}/extract_{extract['segment_definition_id']}_{extract['id']}.csv"],
                    compression='NONE',
                    export_format='CSV',
                    field_delimiter=';',
                    print_header=True
                )

                self.update_extract_status(extract['id'])
            except Exception as e:
                self.log.error(f"Failed to create BigQuery table: {str(e)}")
                raise e

    def get_extract_ids(self):
        try:
            records = self.pg_hook.get_records(self.export_sql)
            keys = ['id', 'segment_definition_id']
            return [dict(zip(keys, record)) for record in records]
        except Exception as e:
            self.log.error(f"Failed to get extract ids to export: {str(e)}")

    def update_extract_status(self, extract_id):
        try:
            update_sql = """
                UPDATE picasso.segment_extract
                SET status = 'COMPLETED'
                WHERE id = %s;
            """
            self.pg_hook.run(update_sql, parameters=[extract_id])
        except Exception as e:
            self.log.error(f"Failed to update extract status: {str(e)}")
