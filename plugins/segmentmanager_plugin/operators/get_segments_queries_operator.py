# -*- coding: utf-8 -*-
#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

from tempfile import NamedTemporaryFile

import pandas as pd
import requests
from airflow.models import BaseOperator
from airflow.providers.google.cloud.hooks.gcs import GCSHook


class GetSegmentsQueriesOperator(BaseOperator):
    template_fields = ('gcs_object_path',)

    def __init__(self,
                 mics_api_key='',
                 gcs_conn_id='google_cloud_default',
                 gcs_bucket='',
                 gcs_object_path='',
                 delegate_to=None,
                 *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.mics_api_key = mics_api_key
        self.gcs_conn_id = gcs_conn_id
        self.gcs_bucket = gcs_bucket
        self.gcs_object_path = gcs_object_path
        self.delegate_to = delegate_to

    def get_segment_info(self, first_result=0):
        endpoint = 'https://api.mediarithmics.com/v1/audience_segments?organisation_id=1695'
        header = {'Authorization': self.mics_api_key}

        if first_result > 0:
            endpoint = endpoint + '&first_result={}'.format(first_result)

        response = requests.get(endpoint, headers=header)

        return response.json()

    def get_segment_query(self, query_id):
        endpoint = 'https://api.mediarithmics.com/v1/datamarts/1759/queries/{}?organisation_id=1695'.format(query_id)
        header = {'Authorization': self.mics_api_key}

        response = requests.get(endpoint, headers=header)

        return response.json()

    def get_segments_dataframe(self, iterations, max_result):
        intermediate_dfs = []

        for i in range(0, iterations):
            segments = self.get_segment_info(first_result=i * max_result)

            for j in range(0, max_result):
                segment_data = pd.DataFrame.from_dict([segments['data'][j]], orient='columns')

                if 'query_id' in segment_data:
                    # trimming strings in the whole DataFrame
                    df_obj = segment_data.select_dtypes(['object'])
                    segment_data[df_obj.columns] = df_obj.apply(lambda x: x.str.strip())

                    segment_data = segment_data[['id',
                                                 'name',
                                                 'short_description',
                                                 'query_id']]
                    intermediate_dfs.append(segment_data)

        raw_data = pd.DataFrame()
        raw_data = pd.concat(intermediate_dfs)
        raw_data.drop_duplicates(inplace=True)
        raw_data.reset_index(inplace=True, drop=True)

        return raw_data

    def clean_segments(self, segments):
        segments = segments.drop_duplicates(['name'], keep='first')
        segments.reset_index(inplace=True, drop=True)

        return segments

    def save_to_gcs(self, csv_content, filename):
        with NamedTemporaryFile(mode='w+') as tmpfile:
            tmpfile.write(csv_content)
            self.log.info('The temp filename is %s', tmpfile.name)
            bucket = self.gcs_bucket
            object = self.gcs_object_path + filename
            # flush to force close file
            tmpfile.flush()
            gcs_hook = GCSHook(
                gcp_conn_id=self.gcs_conn_id,
                delegate_to=self.delegate_to
            )
            gcs_hook.upload(bucket_name=bucket, object_name=object, filename=tmpfile.name, mime_type='text/csv')

    def execute(self, context):
        inter_dfs = []
        segment_info = self.get_segment_info()
        total_segment = segment_info['total']
        max_result = segment_info['max_results']
        iterations = int((total_segment / max_result))
        raw_data = self.get_segments_dataframe(iterations=iterations, max_result=max_result)

        for query_id in raw_data['query_id']:
            query_data = self.get_segment_query(query_id=query_id)

            if 'data' in query_data:
                inter_dfs.append(
                    [query_data['data']['id'], query_data['data']['query_language'], query_data['data']['query_text']])

        queries = pd.DataFrame(inter_dfs, columns=['queryid', 'query_language', 'query_text'])
        segments = self.clean_segments(raw_data)
        segments_df = pd.merge(segments, queries, left_on='query_id', right_on='queryid', how='left')
        segments_df.drop(['queryid'], axis=1, inplace=True)
        segments_df.dropna(subset=['id', 'query_id'], inplace=True)
        segments_df['id'] = segments_df['id'].astype('int64')
        segments_df['query_id'] = segments_df['query_id'].astype('int64')
        self.save_to_gcs(csv_content=segments_df.to_csv(index=False, sep=';'), filename='segment_queries.csv')

        return True
