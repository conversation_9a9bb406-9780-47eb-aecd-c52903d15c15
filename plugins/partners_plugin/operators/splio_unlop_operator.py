# -*- coding: utf-8 -*-
#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
# gsutil cp plugins/partners_plugin/operators/splio_unlop_operator.py gs://europe-west1-prod-mozart-co-2ef7e904-bucket/plugins/partners_plugin/operators/

import json
from collections import defaultdict

import itdata_plugin
from airflow.models import BaseOperator
from airflow.providers.google.cloud.hooks.bigquery import BigQueryHook
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from airflow.providers.google.cloud.operators.bigquery import BigQueryGetDataOperator
from airflow.providers.http.hooks.http import HttpHook
from airflow.providers.postgres.hooks.postgres import PostgresHook


class SplioUnLopOperator(BaseOperator):
    """
        Call Splio DELETE BLACKLIST API for identified profile with CNIL rule,
        If success, log unlop into matrix__email_splio.splio_unblacklist_event table

        The dags take as input a BQ table with necessarily
        the two columns at least:
            - universe_id (integer, universe.id)
            - email (string)
            - then other columns

        :param cancel_profiles_dataset: Name of the dataset
        :type cancel_profiles_dataset: string
        :param cancel_profiles_table: Name of the table to check
        :type cancel_profiles_table: bases
        :param cancel_selected_fields: cancel selected fields from source table.
        :type cancel_selected_fields: string
        :param cancel_max_profiles: number of profiles to delete from blacklist endpoint
            (templated)
        :type cancel_max_profiles: integer
        :param http_splio_api: reference to splio api conn id
        :type http_splio_api: string
        :param authenticate_endpoint: reference to splio authenticate token endpoint
        :type authenticate_endpoint: string
        :param blacklists_endpoint: reference to splio delete blacklist endpoint
        :type blacklists_endpoint: string
        :param database: reference to a specific postgres database
        :type database: string
        :param postgres_conn_id: reference to a specific postgres database
        :type postgres_conn_id: string
        :param gcp_conn_id: (Optional) The connection ID used to connect to Google Cloud Platform.
        :type gcp_conn_id: Str
    """

    template_fields = 'cancel_profiles_table'
    template_ext = ('.sql', '.html')
    ui_color = '#4f5z6s'

    def __init__(self,
                 cancel_profiles_dataset,
                 cancel_profiles_table,
                 cancel_selected_fields,
                 cancel_max_profiles=1,
                 http_splio_api='http_splio_api',
                 authenticate_endpoint='authenticate',
                 blacklists_endpoint='data/blacklists/email/all',
                 # Touchpoint source : custom or unsub (only available for email channel) or all (custom + unsub).
                 database='matrix',
                 postgres_conn_id='psql_matrix_email_app',
                 postgres_portal_conn_id='psql_portal_app',
                 splio_unblacklist_event_table='splio_unblacklist_event',
                 splio_blacklist_api_chunk_size=1000,
                 gcp_conn_id='gcp_default',
                 delegate_to=None,
                 *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.cancel_profiles_dataset = cancel_profiles_dataset
        self.cancel_profiles_table = cancel_profiles_table
        self.cancel_max_profiles = cancel_max_profiles
        self.selected_fields = cancel_selected_fields

        # gcp config
        self.gcp_conn_id = gcp_conn_id
        self.delegate_to = delegate_to

        # psql config
        self.postgres_conn_id = postgres_conn_id
        self.postgres_portal_conn_id = postgres_portal_conn_id
        self.database = database
        self.pg_hook = PostgresHook(postgres_conn_id=self.postgres_conn_id,
                                    database=self.database)
        self.splio_unblacklist_event_table = splio_unblacklist_event_table

        # BQ config
        self.bq_hook = BigQueryHook(gcp_conn_id=self.gcp_conn_id, delegate_to=self.delegate_to)
        self.gcs_hook = GCSHook(gcp_conn_id=self.gcp_conn_id)

        # Splio Api
        self.http_splio_api = http_splio_api
        self.authenticate_endpoint = authenticate_endpoint
        self.blacklists_endpoint = blacklists_endpoint
        self.splio_blacklist_api_chunk_size = splio_blacklist_api_chunk_size

        self.http_splio_token_hook = HttpHook('POST', http_conn_id=self.http_splio_api)
        self.http_splio_unlop_hook = HttpHook('DELETE', http_conn_id=self.http_splio_api)

    def execute(self, context):
        universe_user_api_keys = self.get_splio_api_keys()

        # get lop profiles to cancel per universe
        get_profiles_to_cancel = BigQueryGetDataOperator(
            task_id='get_profiles_to_cancel',
            dataset_id=self.cancel_profiles_dataset,
            table_id=self.cancel_profiles_table,
            max_results=self.cancel_max_profiles,
            selected_fields=self.selected_fields,
            gcp_conn_id=self.gcp_conn_id,
        )
        profiles_row = get_profiles_to_cancel.execute(context)
        # self.log.info('profiles_row: {}'.format(profiles_row))
        universes_config = defaultdict(list)
        for profile_row in profiles_row:
            # [universe_id, email, pmi]
            universe_id = int(profile_row[0])
            email = profile_row[1]

            if universe_id in universes_config:
                # Get the last sublist (or create it if it doesn't exist yet)
                if not universes_config[universe_id]:  # if list is empty create the first sublist
                    universes_config[universe_id].append([])

                current_sublist = universes_config[universe_id][-1]

                if len(current_sublist) < self.splio_blacklist_api_chunk_size:
                    current_sublist.append(email)
                else:
                    # Start a new sublist
                    universes_config[universe_id].append([email])  # Start a new sublist with the current email
            else:
                universes_config[universe_id] = [[email]]  # Initialize with a list containing a list
            # self.log.info('universes_config: {} '.format(universes_config))

        not_found_api_key = []
        for universe_id, universe_email_lists in universes_config.items():
            print(f" --- Universe {universe_id} --- ")
            print(f"Universe sublist length: {len(universe_email_lists)}")

            if universe_user_api_keys.get(universe_id) is None:
                not_found_api_key.append(str(universe_id))
                continue

            api_key = universe_user_api_keys[universe_id]
            token = self.splio_get_token(universe_id, api_key)

            for i, email_list in enumerate(universe_email_lists):
                print(f"  Sublist {i + 1} length {len(email_list)}")

                unlop_payload = json.dumps({
                    "source": "all",
                    "channel": "email",
                    "data": email_list
                })
                #self.log.info('unlop_payload: {}'.format(unlop_payload))

                self.splio_delete_lop(token, universe_id, api_key, unlop_payload)

        if len(not_found_api_key) > 0:
            html_content = 'Api key not found for for following universe ids : ' + ', '.join(not_found_api_key)
            title = 'Splio Lop Operator Alerte'
            itdata_plugin.send_technical_alerte(title, html_content)

    def get_splio_api_keys(self):
        pg_hook_portal = PostgresHook(postgres_conn_id=self.postgres_portal_conn_id,
                                      database=self.database)

        query = '''
            SELECT
                univ.id AS universe_id,
                apikey.user_api_key AS user_api_key
            FROM matrix__email_splio.universe AS univ
            JOIN portal.splio_api_key AS apikey
                ON apikey.universe_id = univ.id
            WHERE univ.is_active = TRUE	
            ;
        '''

        universe_user_api_keys = pg_hook_portal.get_records(sql=query, parameters=self.params)

        universe_keys = {universe_id: user_api_key for universe_id, user_api_key in universe_user_api_keys}

        for output in pg_hook_portal.conn.notices:
            self.log.info(output)

        return universe_keys

    def splio_get_token(self, universe_id, api_key):
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }

        token_response = self.http_splio_token_hook.run(
            endpoint=self.authenticate_endpoint + '?api_key={}'.format(api_key),
            data=None,
            extra_options={'check_response': False},
            headers=headers
        )

        if token_response.status_code != 200:
            self.log.info('Splio api authentication error for universe_id = {}'.format(universe_id))
            return None
        else:
            return token_response.json()['token']

    def splio_delete_lop(self, token, universe_id, api_key, unlop_payload):

        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}"
        }

        unlop_response = self.http_splio_unlop_hook.run(
            endpoint=self.blacklists_endpoint + '?api_key={}'.format(api_key),
            data=unlop_payload,
            extra_options={'check_response': False},
            headers=headers
        )

        self.log.info('unlop_response status_code: {}'.format(unlop_response.status_code))

        # 200 => All emails were successfully deleted from the Splio blacklist
        # 207 => Some emails were not found in the Splio blacklist, other emails were deleted successfully
        # 400 => None of the emails were found in the Splio blacklist.
        if unlop_response.status_code not in [200, 207, 400]:
            self.log.warning('An error occurred with the Splio delete blacklist API !!')
            self.log.info('unlop_response text: {}'.format(unlop_response.text))
        #else:
            #self.log.warning('All Ok!!')
            #self.__log_unlop_from_splio_api_response(unlop_response.json(), universe_id)


    def __log_unlop_from_splio_api_response(self, unlop_response, universe_id, lop_type='unsub'):
        """
        Splio Api response example:

        unlop_response: {
           "successes":[
                "<EMAIL>",
                "<EMAIL>"
           ],
           "failures":[
              {
                 "touchpoint":"<EMAIL>",
                 "error":"Touchpoint <EMAIL> not found in blacklist.",
                 "error_key":"touchpoint_not_blacklisted"
              }
           ]
        }
        """

        if 'successes' in unlop_response:
            successes = unlop_response.get('successes')
            for email in successes:
                # @todo her add tracking unlop event
                print('email : {}'.format(email))
                print('universe_id : {}'.format(universe_id))

                event_payload = {
                    "app": "mozart",
                    "source": "matrix",
                    "medium": "cnil: purge lop tech - {lop_type}".format(lop_type=lop_type)
                }

                unblacklist_event_query = '''
                    INSERT INTO matrix__email_splio.{splio_unblacklist_event_table}
                    (type, profile_master_id, email_hash, universe_id, payload, create_date)
                    VALUES (
                        'splio_unblacklist'::matrix__email_splio.splio_unblacklist_event_type,
                        cast(NULL as bigint),
                        substring(matrix__email_queue.digest(lower(trim('{email}')), 'sha256')::varchar(255), 3, 64), 
                        {universe_id},
                        CAST('{payload}' AS jsonb),
                        NOW()
                    );
                    
                    UPDATE matrix__email_splio.{splio_unblacklist_event_table} AS sue
                    SET profile_master_id = pmi.id
                    FROM  matrix__email.profile_master_id AS pmi
                    WHERE pmi.email_sha256 = substring(matrix__email_queue.digest(lower(trim('{email}')), 'sha256')::varchar(255), 3, 64)
                    AND sue.email_hash = pmi.email_sha256
                    ;
                '''.format(splio_unblacklist_event_table=self.splio_unblacklist_event_table,
                           universe_id=universe_id,
                           email=email,
                           payload=json.dumps(event_payload).replace("'", '\\"'))

                self.pg_hook.run(unblacklist_event_query, parameters=self.params)

