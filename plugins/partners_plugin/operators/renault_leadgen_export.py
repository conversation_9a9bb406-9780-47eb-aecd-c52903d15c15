# gsutil cp plugins/partners_plugin/operators/renault_leadgen_export.py gs://europe-west1-prod-mozart-co-2ef7e904-bucket/plugins/partners_plugin/operators/

import json
import logging
import os
import time
from datetime import datetime
from airflow.providers.google.cloud.hooks.bigquery import BigQueryHook


import google.auth
import requests
from airflow.models import BaseOperator
from airflow.utils.email import send_email
from google.cloud import bigquery
from google.cloud.exceptions import NotFound
import re



class RenaultLeadGenExportOperator(BaseOperator):
    template_fields = ('bigquery_leadgen_tables', 'bigquery_dataset')

    ui_color = '#4fff6s'

    def __init__(self,
                 renault_campaign_ids=None,
                 bigquery_leadgen_interval_day=3,
                 bigquery_leadgen_data_is_full=False,
                 is_test=True,
                 error_api_sender='<EMAIL>',
                 bigquery_dataset='store_leadgen',
                 bq_project='pm-prod-matrix',
                 leadgen_api_uri='https://renault-alh.oneomg.com/renfr/webhooks/generic',
                 bigquery_leadgen_tables=None,  # ['leadgen_renault_cap_raw_data','leadgen_renault_hbz_raw_data']
                 delegate_to=None,
                 exception=None,
                 *args, **kwargs):
        super().__init__(*args, **kwargs)

        if bigquery_leadgen_tables is None:
            bigquery_leadgen_tables = []
        self.bq_project = bq_project
        self.delegate_to = delegate_to
        self.exception = exception

        gcp_project_id = os.environ.get("GCP_PROJECT_ID")
        impersonation_chain = f'composer-node@{gcp_project_id}.iam.gserviceaccount.com'

        # Get credentials from Airflow connection
        bq_hook = BigQueryHook(gcp_conn_id='bq_matrix', use_legacy_sql=False, impersonation_chain=impersonation_chain)
        credentials = bq_hook.get_credentials()

        # Add scopes to credentials if possible
        if hasattr(credentials, 'with_scopes'):
            logging.info('Add scopes to credentials')
            scopes = [
                'https://www.googleapis.com/auth/bigquery',
                'https://www.googleapis.com/auth/cloud-platform',
                'https://www.googleapis.com/auth/drive'
            ]
            credentials = credentials.with_scopes(scopes)

        # Create BigQuery client with these credentials
        self.bq_client = bigquery.Client(
            project=self.bq_project,
            location="EU",
            credentials=credentials
        )

        self.bigquery_leadgen_tables = bigquery_leadgen_tables
        self.bigquery_leadgen_data_is_full = bigquery_leadgen_data_is_full
        self.bigquery_leadgen_interval_day = bigquery_leadgen_interval_day
        self.bigquery_dataset = bigquery_dataset

        self.leadgen_api_uri = leadgen_api_uri
        self.is_test = is_test

        self.error_api_sender = error_api_sender

        self.domaine_campaignId = renault_campaign_ids

        self.data_gender = {
            'female': 'MRS',
            'feminin': 'MRS',
            'féminin': 'MRS',
            'f': 'MRS',
            'male': 'MR',
            'masculin': 'MR',
            'm': 'MR',
            '': ''
        }

    def execute(self, context):
        error_api = []
        for table in self.bigquery_leadgen_tables:
            # like : leadgen_renault_cap_raw_data
            logging.info('table : ' + str(table))

            # pm-prod-matrix.store_leadgen.leadgen_renault_cap_raw_data
            data_table_name = '{project}.{dataset}.{table}'.format(
                project=self.bq_project,
                dataset=self.bigquery_dataset,
                table=table,
            )
            logging.info('data_table_name : ' + str(data_table_name))

            logs_table_name = '{project}.{dataset}.{table}'.format(
                project=self.bq_project,
                dataset=self.bigquery_dataset,
                table=table.replace('_raw_data', '_logs')
            )
            logging.info('logs_table_name : ' + str(logs_table_name))

            logs_response_table_name = '{project}.{dataset}.{table}'.format(
                project=self.bq_project,
                dataset=self.bigquery_dataset,
                table=table.replace('_raw_data', '_logs_api_response')
            )
            logging.info('logs_response_table_name : ' + str(logs_response_table_name))

            # Regular expression to extract the brand
            regex_brand = r"_(.{3})_raw_data$"
            match = re.search(regex_brand, data_table_name)
            if match:
                brand = match.group(1)
            else:
                logging.info('La table {table} ne respecte pas le regex! skip table. '.format(table=data_table_name))
                continue

            # Extract the brand
            logging.info('brand : ' + str(brand))

            vehicle_model = 'RENAULT 5 E-TECH 100% ÉLECTRIQUE'
            vehicle_type = 'Electric'

            if brand == 'cap':
                client_token = '5EFcQ7StVJL9NVY82hw4ftuNpW5hIsnJ'
                code_campaign = '2025-01_renault vp_r5_capital'
            elif brand == 'hbz':
                client_token = 'MbzEUIWwV7q6W2PR5hflLd68EBrpqUQr'
                code_campaign = '2025-01_renault vp_r5_harper'
            else:
                logging.warning(f"Exception !! Unidentified brand = {brand}! Skip")
                continue
            # create log table if it doesn't exist
            self.create_log_table(logs_table_name)
            self.create_log_response_table(logs_response_table_name)

            data_table = self.bq_client.get_table(data_table_name)
            schema = data_table.schema
            # Extraire les noms de champs
            field_names = [field.name for field in schema]
            logging.info('field_names : ' + str(field_names))

            date_condition_sql = ''
            if not self.bigquery_leadgen_data_is_full:
                date_condition_sql = f"""
                    AND DATE(source.created_time) >= DATE_SUB(CURRENT_DATE(), interval {self.bigquery_leadgen_interval_day} DAY)
                
                """
            # Get the data from the table
            data_sql = f"""
            -- mozart-id : {self.dag.dag_id}.{self.task_id}
            SELECT 
                source.* 
            
            FROM `{data_table_name}` AS source
            LEFT JOIN `{logs_table_name}` AS log
                ON log.email = source.email
                AND log.brand = '{brand}'
            WHERE log.email IS NULL
            {date_condition_sql}
            ORDER BY source.created_time
            """

            logging.info('get data sql : ' + str(data_sql))

            query_job = self.bq_client.query(data_sql)
            table_rows = list(query_job.result())
            logging.info('data table_rows : ' + str(table_rows))

            # Send the data to the API
            for row in table_rows:

                # Get the current time in seconds since the epoch
                current_time_seconds = int(time.time())
                timestamp_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(current_time_seconds))

                logging.info('row : ' + str(row))
                dict_row = {field_names[i]: value for i, value in enumerate(row)}
                logging.info('new_list : ' + str(dict_row))

                # field_names:['id', 'created_time', 'ad_id', 'ad_name', 'adset_id', 'adset_name', 'campaign_id',
                # 'campaign_name', 'form_id', 'form_name', 'is_organic', 'platform', 'gender', 'first_name',
                # 'last_name', 'email', 'phone_number', 'post_code', 'city', 'oui', 'is_qualified',
                # 'is_quality', 'is_converted']

                leadgen_id = dict_row.get('id')
                email = dict_row.get('email')
                if not email:
                    logging.info("Invalid email {email} for leadgen_id {leadgen_id}! skipped!!".format(email=email, leadgen_id=leadgen_id))
                    continue
                logging.info('email : ' + str(email))

                email_regex = r"^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,4})+$"
                if not re.match(email_regex, email):
                    logging.info("Invalid email {email} for leadgen_id {leadgen_id}! skipped!!".format(email=email, leadgen_id=leadgen_id))
                    continue

                form_id = dict_row.get('form_id')
                created_time = dict_row.get('created_time')
                if not created_time:
                    continue
                created_time_ts = int(created_time.timestamp())

                genre = dict_row.get('gender', '').lower()
                gender = self.data_gender.get(genre, '')

                city = dict_row.get('city')[:40],

                telephone = re.sub(r"p:", "", dict_row.get('phone_number'))
                formulaire = re.sub(r"f:", "", dict_row.get('form_id'))
                zipcode = re.sub(r"z:", "", dict_row.get('post_code'))
                lead_id = re.sub(r"l:", "", leadgen_id)

                if self.is_test:
                    city = 'GROIX'
                    zipcode = 56590

                data_profile = {
                    'client_id': 'omg-prisma',
                    'client_token': client_token, # old value 'EfIc2Kvn7mq14YbzrNgB1lyFzKXmRZ9v',
                    'lead_id': 'prisma_lead_{}'.format(lead_id),
                    'code_campaign': code_campaign, # old value '2024-10_renault vp_r5',
                    'firstname': dict_row.get('first_name'),
                    'lastname': dict_row.get('last_name'),
                    'civility': gender,
                    'phone': telephone,
                    'email': email,
                    'city': str(city[0]) if isinstance(city, tuple) and isinstance(city[0], str) else city,
                    'zipcode': str(zipcode[0]) if isinstance(zipcode, tuple) and isinstance(zipcode[0], str) else zipcode,
                    'is_test': self.is_test,
                    'vehicle_model': vehicle_model,
                    'vehicle_type': vehicle_type,
                    'client.clientType': 'PERSONAL',
                    'contact_optin': False,
                    'comment': '…',
                }

                logging.info('data_profile infos: ' + str(data_profile))

                created_time_str = created_time.strftime('%Y-%m-%d %H:%M:%S')
                row_dict = dict(row.items())
                row_dict['created_time'] = created_time_str

                log_data = [
                    {
                        'email': email,
                        'form_id': form_id,
                        'row': json.dumps(row_dict),
                        'brand': brand,
                        'created_time': created_time_ts,
                        'exported_date': timestamp_str
                    }
                ]

                headers = {
                    'Content-Type': 'application/json',
                }

                response = requests.request("POST", self.leadgen_api_uri, headers=headers,
                                            data=json.dumps(data_profile))

                response_code = response.status_code

                json_data = json.dumps(response.text) if response.text else '{"reponse": "vide"}}'

                if response_code == 200:
                    logging.info('Renault response : {}'.format(response.text))

                    try:
                        # Insert the row into the log table
                        insert_log = self.bq_client.insert_rows_json(logs_table_name, log_data)
                        if insert_log:
                            logging.error(f"Errors inserting row: {insert_log}")

                        log_response_data = {
                            'leadgen_id': leadgen_id,
                            'form_id': form_id,
                            'response': json_data,
                            'status': response_code,
                            'brand': brand,
                            'created_date': timestamp_str,
                        }
                        self.add_log_response_table(logs_response_table_name, log_response_data, response_code)
                    except:
                        logging.info(f"Exception !! unable to save log response for brand {brand}: {leadgen_id}")

                        insert_errors = self.bq_client.insert_rows_json(logs_table_name, log_data)
                        if insert_errors:
                            logging.error(f"Errors inserting row: {insert_errors}")

                else:
                    error_dict = {
                        'brand': brand,
                        'response': json_data,
                        'response_code': response_code,
                        'id': dict_row.get('id'),
                        'created_time': created_time_str
                    }
                    error_api.append(error_dict)
                    logging.error(f"Exception !! Error sending data for brand {brand}: {json_data}")

        # errors notifications
        if len(error_api) > 0:
            logging.info(f"Error api: {str(error_api)}")

            # Format the error dictionaries as HTML strings
            error_api_html = [
                f"<br> Brand= {item.get('brand')}, Leadgen Id= {item.get('id')}, Created Time: {item.get('created_time')}, Error Message= {item.get('error_message')}, Champs={item.get('champs')}"
                for item in
                error_api]
            html_content = ''.join(error_api_html)

            title = '[renault Leadgen Export] Error: Lignes non acceptés !'
            body = (
                    "Bonjour,"
                    "<br> Vous trouverez ci-dessous le récapitulatif des lignes rejetées par l'API Renault :"
                    "<br>" + html_content +
                    "<br>"
                    "<br> Cordialement,"
                    "<br> Mozart (dag_id= " + self.dag.dag_id + ") <br>"
            )

            send_email(to=self.error_api_sender,
                       subject=title,
                       html_content=body,
                       cc=[], mime_charset='utf-8',
                       conn_id='sendgrid_default')

    def create_log_table(self, logs_table_name):
        # Check if the log table exists
        try:
            self.bq_client.get_table(logs_table_name)
        except NotFound:
            # If the table doesn't exist, create it
            logs_table = bigquery.Table(logs_table_name)
            logs_table.schema = [
                bigquery.SchemaField('email', 'STRING'),
                bigquery.SchemaField('form_id', 'STRING'),
                bigquery.SchemaField('row', 'STRING'),
                bigquery.SchemaField('brand', 'STRING'),
                bigquery.SchemaField('created_time', 'TIMESTAMP'),
                bigquery.SchemaField('exported_date', 'TIMESTAMP')
            ]
            self.bq_client.create_table(logs_table)

    def create_log_response_table(self, logs_response_table_name):
        # Check if the log table exists
        try:
            self.bq_client.get_table(logs_response_table_name)
        except NotFound:
            # If the table doesn't exist, create it
            logs_table = bigquery.Table(logs_response_table_name)
            logs_table.schema = [
                bigquery.SchemaField('leadgen_id', 'STRING'),
                bigquery.SchemaField('form_id', 'STRING'),
                bigquery.SchemaField('response', 'STRING'),
                bigquery.SchemaField('status', 'STRING'),
                bigquery.SchemaField('brand', 'STRING'),
                bigquery.SchemaField('created_date', 'TIMESTAMP'),
            ]
            self.bq_client.create_table(logs_table)

    def add_log_response_table(self, logs_response_table_name, row_data, response_statut_code):

        logging.info('Try to insert response data : ' + str(row_data))

        try:
            # Check if the row with the given leadgen_id exists
            query = f"""
                -- mozart-id : {self.dag.dag_id}.{self.task_id}
                        
                SELECT * FROM `{logs_response_table_name}` 
                WHERE leadgen_id = '{row_data['leadgen_id']}'
                AND form_id = '{row_data['form_id']}'
                """

            logging.info('logs query : ' + str(query))

            query_job = self.bq_client.query(query)
            results = query_job.result()
            table_rows = list(results)
            logging.info('logs table_rows : ' + str(table_rows))

            if len(table_rows) == 0:
                # Insert the row if it doesn't exist
                errors = self.bq_client.insert_rows_json(logs_response_table_name, [row_data])
                if not errors:
                    logging.info(
                        f"Row with leadgen_id={row_data['leadgen_id']}, brand={row_data['brand']} inserted successfully.")
                else:
                    logging.error(f"Exception !! Error inserting row: {errors}")
            elif response_statut_code:
                # Update the row if it exists
                update_query = f"""
                    -- mozart-id : {self.dag.dag_id}.{self.task_id}
                    
                    UPDATE `{logs_response_table_name}`
                    SET {", ".join([f"{column} = '{row_data[column]}'" for column in row_data if column != "leadgen_id"])}
                    WHERE leadgen_id = '{row_data['leadgen_id']}'
                    """

                logging.info('Logs update_query : ' + str(update_query))

                update_job = self.bq_client.query(update_query)
                update_job.result()
                logging.info(
                    f"Row with leadgen_id={row_data['leadgen_id']}, brand={row_data['brand']} updated successfully.")
        except Exception as e:
            logging.exception(f"Exception !! {e}")

    def detect_and_format_birthdate(self, birthdate_string):
        """Detects and formats a birthdate string to %Y-%m-%d.

        Args:
            birthdate_string (str): The birthdate string to be formatted.

        Returns:
            str: The formatted birthdate string in %Y-%m-%d format
            If the birthdate string cannot be recognized as a valid format return empty string

        """

        # Regular expressions to match different birthdate formats
        mm_dd_yyyy_pattern = r"(\d{2})/(\d{2})/(\d{4})"
        mm_dd_yy_pattern = r"(\d{2})/(\d{2})/(\d{2})"
        yyyy_dd_mm_pattern = r"(\d{4})-(\d{2})-(\d{2})"

        # Try matching each pattern
        match = re.match(mm_dd_yyyy_pattern, birthdate_string)
        if match:
            return f"{match.group(3)}-{match.group(1)}-{match.group(2)}"

        match = re.match(mm_dd_yy_pattern, birthdate_string)
        if match:
            # Check if the two-digit year represents the current century or the previous one
            current_year = int(datetime.now().year)
            base_year = current_year // 100 * 100
            year = int(f"{base_year}{match.group(3)}")
            if year > current_year:
                year -= 100  # Adjust for years in the previous century
            return f"{year}-{match.group(1)}-{match.group(2)}"

        match = re.match(yyyy_dd_mm_pattern, birthdate_string)
        if match:
            return birthdate_string

        logging.warning("Exception: Invalid birthdate format {}".format(birthdate_string))
        return ''
