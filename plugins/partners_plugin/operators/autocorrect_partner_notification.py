from typing import Any, Dict
from airflow.models import BaseOperator, Variable
from airflow.providers.google.cloud.hooks.bigquery import <PERSON><PERSON><PERSON><PERSON>H<PERSON>
from airflow.utils.email import send_email


class PartnerNotificationOperator(BaseOperator):
    """
    Operator that handles notifications for partner data reconciliation.

    This operator queries BigQuery for sub/unsub statistics and sends formatted
    email notifications to relevant stakeholders.

    :param partner_name: Name of the partner ('riviera', 'webrivage', or 'splio')
    :param bigquery_conn_id: Connection ID for BigQuery
    :param gcp_conn_id: Connection ID for GCP
    :param email_conn_id: Connection ID for email service (default: 'sendgrid_default')
    :param limit_datahub: Dictionary containing limit for datahub contacts (required for Splio)
    """

    template_fields = ('partner_name',)

    # Partner-specific configurations
    PARTNER_CONFIGS = {
        'riviera': {
            'additional_recipients': ["<EMAIL>", "<EMAIL>"],
            'details_link': "https://docs.google.com/spreadsheets/d/12yJt6YQkjDAQsTycQdPhsojri8Jbvr5j-C8e6mA-C7c/edit#gid=1099959400"
        },
        'webrivage': {
            'additional_recipients': ["<EMAIL>", "<EMAIL>"],
            'details_link': "https://docs.google.com/spreadsheets/d/11LVplmIOiEbsVNgPgJR-T8T4VyFTCjqp7YvM4JsbheU/edit#gid=104443414"
        },
        'splio': {
            'additional_recipients': [],
            'details_link': "https://docs.google.com/spreadsheets/d/1V75PZxpPqllwVGYdJ9yOdP36ZhbLtXqcotKxqMhoheg/edit#gid=484505693"
        }
    }

    def __init__(
            self,
            partner_name: str,
            bigquery_conn_id: str = 'bq_matrix',
            gcp_conn_id: str = 'gcs_matrix',
            email_conn_id: str = 'sendgrid_default',
            limit_datahub: Dict[str, Any] = None,
            **kwargs
    ) -> None:
        super().__init__(**kwargs)
        self.partner_name = partner_name
        self.bigquery_conn_id = bigquery_conn_id
        self.gcp_conn_id = gcp_conn_id
        self.email_conn_id = email_conn_id
        self.limit_datahub = limit_datahub or {}

    def _get_sql_queries(self) -> Dict[str, str]:
        """Returns appropriate SQL queries based on partner type"""
        if self.partner_name == 'splio':
            return {
                'total': """
                    SELECT 
                        SUM(CHAR_LENGTH(subscriptions) - CHAR_LENGTH(REPLACE(subscriptions, '+', ''))) AS nb_sub,
                        SUM(CHAR_LENGTH(subscriptions) - CHAR_LENGTH(REPLACE(subscriptions, '-', ''))) AS nb_unsub
                    FROM `pm-prod-matrix.store_partner.order_to_splio`
                """,
                'sub_breakdown': """
                    SELECT 
                        COUNT(DISTINCT IF (subscriptions LIKE '%+%', otp.email,NULL)) AS nb_profiles,
                        SUM(CHAR_LENGTH(subscriptions) - CHAR_LENGTH(REPLACE(subscriptions, '+', ''))) AS nb_sub,
                        name
                    FROM `pm-prod-matrix.store_partner.order_to_splio` AS otp
                    JOIN `pm-prod-matrix.store_karinto.universe` AS u ON otp.universe_id = u.id
                    GROUP BY name
                    ORDER BY name
                """,
                'unsub_breakdown': """
                    SELECT 
                        COUNT(DISTINCT IF (subscriptions LIKE '%-%', otp.email,NULL)) AS nb_profiles,
                        SUM(CHAR_LENGTH(subscriptions) - CHAR_LENGTH(REPLACE(subscriptions, '-', ''))) AS nb_unsub,
                        name
                    FROM `pm-prod-matrix.store_partner.order_to_splio` AS otp
                    JOIN `pm-prod-matrix.store_karinto.universe` AS u ON otp.universe_id = u.id
                    GROUP BY name
                    ORDER BY name
                """
            }
        else:
            return {
                'total': f"""
                    SELECT 
                        (SELECT count(*) FROM `store_partner.order_to_{self.partner_name}` 
                         WHERE order_to_sent = 'sub' AND email IS NOT NULL) AS nb_sub,
                        (SELECT count(*) FROM `store_partner.order_to_{self.partner_name}`
                         WHERE order_to_sent = 'unsub') AS nb_unsub
                """,
                'sub_breakdown': f"""
                    SELECT count(*), public_ref
                    FROM `store_partner.order_to_{self.partner_name}` AS otp
                    JOIN `store_karinto.email_consent` AS kec ON otp.email_consent_id = kec.id
                    WHERE order_to_sent = 'sub' AND email IS NOT NULL
                    GROUP BY public_ref
                    ORDER BY public_ref ASC
                """,
                'unsub_breakdown': f"""
                    SELECT count(*), public_ref
                    FROM `store_partner.order_to_{self.partner_name}` AS otp
                    JOIN `store_karinto.email_consent` AS kec ON otp.email_consent_id = kec.id
                    WHERE order_to_sent = 'unsub'
                    GROUP BY public_ref
                    ORDER BY public_ref ASC
                """
            }

    def _format_breakdown_results(self, results: list, is_sub: bool = True) -> str:
        """Formats breakdown results based on partner type"""
        if self.partner_name == 'splio':
            return "<br>".join(
                map(lambda x: str(x[1]) + ' consentements / ' + str(x[0]) + ' profils pour ' + str(x[2]) +
                              ('(envoyé par batchs quotidiens de ' + str(self.limit_datahub["limit"]) + ')'
                               if x[0] > self.limit_datahub["limit"] else ''),
                    results)
            )
        else:
            return "<br>".join(
                map(lambda x: '{:,}'.format(x[0]).replace(',', ' ') + ' ' + str(x[1]), results)
            )

    def execute(self, context: Dict[str, Any]) -> Any:
        config = self.PARTNER_CONFIGS[self.partner_name]

        # Setup recipients
        recipient = (eval(Variable.get("airflow_email_alertes"))
                     if self.partner_name != 'splio'
                     else '<EMAIL>')
        cc = ", ".join(["<EMAIL>"] + config['additional_recipients'])

        # Setup BigQuery connection
        hook = BigQueryHook(
            gcp_conn_id=self.gcp_conn_id if self.partner_name == 'splio' else self.bigquery_conn_id,
            delegate_to=None,
            use_legacy_sql=False
        )
        conn = hook.get_conn()
        cursor_total = conn.cursor()
        cursor_sub = conn.cursor()
        cursor_unsub = conn.cursor()

        # Get appropriate SQL queries
        queries = self._get_sql_queries()

        # Execute queries
        cursor_total.execute(queries['total'])
        result = cursor_total.fetchall()
        nb_sub, nb_unsub = result[0][0], result[0][1]

        cursor_sub.execute(queries['sub_breakdown'])
        sub_list = self._format_breakdown_results(cursor_sub.fetchall(), True)

        cursor_unsub.execute(queries['unsub_breakdown'])
        unsub_list = self._format_breakdown_results(cursor_unsub.fetchall(), False)

        # Get recurrent stats
        recurrent_sql = f"""
            SELECT 
                SUM(CASE WHEN payload.order_to_send = 'sub' AND payload.monitoring_recurrence >= 3 THEN 1 ELSE 0 END) AS recurrent_subs,
                SUM(CASE WHEN payload.order_to_send = 'unsub' AND payload.monitoring_recurrence >= 3 THEN 1 ELSE 0 END) AS recurrent_unsubs
            FROM `generated_data.autocorrect_history` ah
            WHERE partner = '{self.partner_name}'
            GROUP BY observation_date
            QUALIFY RANK() OVER (ORDER BY observation_date DESC) = 1
        """
        cursor_total.execute(recurrent_sql)
        result = cursor_total.fetchall()
        nb_recurrent_sub, nb_recurrent_unsub = result[0][0], result[0][1]

        if nb_sub > 0 or nb_unsub > 0:
            # Prepare email
            title = f"{self.partner_name.capitalize()} diff report"
            body = (
                "Bonjour, <br> "
                f"<br><br> Ci-après le nombre de redressement {'Prisma vs ' + self.partner_name} :<br><br> "
                f"<br> Répartition des {'{:,}'.format(nb_sub).replace(',', ' ')} sub "
                f"(dont {'{:,}'.format(nb_recurrent_sub).replace(',', ' ')} envoyés 3+ jours consecutifs) : <br> "
                f"<br> {sub_list} <br> "
                f"<br> Répartition des {'{:,}'.format(nb_unsub).replace(',', ' ')} unsub "
                f"(dont {'{:,}'.format(nb_recurrent_unsub).replace(',', ' ')} envoyés 3+ jours consecutifs): <br> "
                f"<br> {unsub_list} <br> "
                "<br> Details dans ce fichier : <br> "
                "<br> Il faut actualiser l'aperçu <br> "
                f"<br> {config['details_link']} <br> "
                "<br><br>Cordialement, <br> "
                "L'équipe technique IT-Data"
            )

            send_email(
                to=recipient,
                subject=title,
                html_content=body,
                cc=cc,
                mime_charset='utf-8',
                conn_id=self.email_conn_id
            )
            self.log.info('Email sent successfully')

        return True