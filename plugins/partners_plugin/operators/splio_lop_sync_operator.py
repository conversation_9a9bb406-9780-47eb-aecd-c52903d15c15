# -*- coding: utf-8 -*-
#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
from tempfile import NamedTemporaryFile

from airflow.models import BaseOperator
from airflow.providers.google.cloud.hooks.bigquery import BigQueryHook
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow.providers.sftp.hooks.sftp import SFTPHook
from airflow.utils.email import send_email


class SplioLopSyncOperator(BaseOperator):
    template_fields = ('sql', 'lop_filename', 'unlop_filename')
    template_ext = ('.sql', '.html')
    ui_color = '#4f5z6s'

    def __init__(self,
                 sql,
                 universes_conn_list,
                 source_lop='event',
                 ftp_dest='/lop/',
                 lop_filename='lopajout_DATE.csv',
                 unlop_filename='lopsupp_DATE.csv',
                 separator=';',
                 postgres_conn_id='postgres_default',
                 bigquery_conn_id='bigquery_default',
                 gcs_conn_id='gcs_default',
                 monitoring=False,
                 notify_on_threshold=50,
                 notify_to=None,
                 gcs_bucket=None,
                 gcs_folder=None,
                 delegate_to=None,
                 *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.sql = sql
        self.universes_conn_list = universes_conn_list
        self.source_lop = source_lop
        self.ftp_dest = ftp_dest
        self.lop_filename = lop_filename
        self.unlop_filename = unlop_filename
        self.separator = separator
        self.gcs_conn_id = gcs_conn_id
        self.gcs_bucket = gcs_bucket
        self.gcs_folder = gcs_folder
        self.postgres_conn_id = postgres_conn_id
        self.bigquery_conn_id = bigquery_conn_id
        self.monitoring = monitoring
        self.notify_on_threshold = notify_on_threshold
        self.notify_to = notify_to

        self.delegate_to = delegate_to

    def execute(self, context):

        if not self.universes_conn_list:
            raise NameError('No univers sets, please check your DAG "universes_conn_list" definition ')

        if not self.sql:
            raise NameError('No sql source sets, please check your DAG "sql" query')

        if self.gcs_bucket is None:
            raise NameError('No bucket sets, please check your DAG "gcs_bucket" and "gcs_folder" params')

        # useful to be returned on xcom for monitoring or somthing else
        data_return = {}

        if self.source_lop == 'event':
            data_source = PostgresHook(postgres_conn_id=self.postgres_conn_id, database='matrix')
        elif self.source_lop == 'lop_state':
            data_source = BigQueryHook(gcp_conn_id=self.bigquery_conn_id, use_legacy_sql=False,
                                       delegate_to=self.delegate_to)
        else:
            raise NameError('No data source set')

        gcs_hook = GCSHook(gcp_conn_id=self.gcs_conn_id, delegate_to=self.delegate_to)
        """ load all the mess into pandas DF """
        df = data_source.get_pandas_df(sql=self.sql)

        data_return[self.source_lop] = {}

        if len(df.values) > 0:
            data_return['to_notify'] = {}

            for lop in self.universes_conn_list:
                """ filter in place on DF """
                univers_lop = df[df['universe_name'] == lop]

                if len(univers_lop.index) > 0:
                    data_return[self.source_lop][lop] = {}

                    self.log.info('Processing {} LOP/UNLOP of {} universe '.format(len(univers_lop.index), lop))
                    sftp = SFTPHook(ftp_conn_id=self.universes_conn_list[lop])

                    for event, filename in zip(['blacklist', 'unblacklist'], [self.lop_filename, self.unlop_filename]):
                        items = univers_lop[univers_lop['event_type'] == event]
                        data_return[self.source_lop][lop][event] = len(items.values)

                        """ no items no file """
                        if len(items.index) > 0:
                            tmpfile = NamedTemporaryFile(delete=True, mode='r+')
                            items = items[['email', 'date', 'source']]
                            items.to_csv(tmpfile.name, index=False, sep=self.separator)
                            """ upload backup of the file to the gcs """
                            bucket = self.gcs_bucket
                            object = "{}/{}/{}".format(self.gcs_folder, lop, filename)
                            self.log.info('Uploading {} to {} {} '.format(filename, bucket, object))
                            gcs_hook.upload(bucket_name=bucket, object_name=object, filename=tmpfile.name,
                                            mime_type="text/csv")

                            """ upload the file to the sftp """
                            self.log.info(
                                'Uploading {} to {}  ( /lop ) '.format(filename, self.universes_conn_list[lop]))
                            sftp.store_file(self.ftp_dest + lop + '_' + filename, tmpfile.name)
                            tmpfile.flush()
                        else:
                            self.log.info('No {} record for {} '.format(event, lop))

                    # Close the SFTP connection explicitly
                    if hasattr(sftp, 'conn') and sftp.conn is not None:
                        sftp.conn.close()

                    # monitoring cond
                    if len(univers_lop.index) > self.notify_on_threshold:
                        data_return['to_notify'][lop] = len(univers_lop.index)
                else:
                    self.log.info('No lop/unlop for {} '.format(lop))
        else:
            self.log.info('No records found')

        data_return['total'] = len(df.index)

        if self.monitoring is True:
            self.save_lop_result(data_return)
            self.notify(data_return)

        return data_return

    def save_lop_result(self, data):
        # save the monitoring logs to the monitoring table !
        pg_sql = PostgresHook(postgres_conn_id=self.postgres_conn_id, database='matrix')
        if data['total'] > 0:
            query = ''
            for univer_name, lop in data[self.source_lop].items():
                query += "INSERT INTO matrix__email_splio.lop_monitoring " \
                         "SELECT u.id AS universe_id,{add_volume} AS volume_add, {del_volume} AS volume_del, 'now()' AS send_date " \
                         "FROM matrix__email_splio.universe AS u WHERE name='{univers}';" \
                    .format(add_volume=lop.get('blacklist'), del_volume=lop.get('unblacklist'), univers=univer_name)
            # insert the logs
            self.log.info('Insert the lop monitoring matrix__email_splio.lop_monitoring ')
            pg_sql.run(query)
        else:
            self.log.info('no record to insert to matrix__email_splio.lop_monitoring ')

    def notify(self, data):
        univers_to_notify = data['to_notify']
        total_lop = data['total']
        if len(univers_to_notify.keys()) > 0:
            message = "\n"
            message += "<table><tr> <td>universe</td> <td>LOP</td> <td>UNLOP</td> </tr>"

            for univer_name, lop in data[self.source_lop].items():
                if univer_name in univers_to_notify.keys():
                    message += "<tr><td>" + str(univer_name) + "</td><td>" + str(lop.get('blacklist')) + "</td><td>" + \
                               str(lop.get('unblacklist')) + "</td></tr>"

            message += "</table>"

            body = " \
            Bonjour, <br> \
            <br> \
            Nous avons pu synchroniser / redresser " + str(total_lop) + \
                   " profiles dans splio , Merci de surveiller les univers ci-dessous " \
                   "qui ont generer plus que " + str(self.notify_on_threshold) + " lop/unlops d'ecart <br/><br/><br/> : \
            <br><br> " + str(message) + " \
            <br> <br>\
            Cordialement,<br> \
            Mozart <br> \
            "
            send_email(self.notify_to, 'SPLIO - Monitoring LOP', body, conn_id='sendgrid_default')
            self.log.info('send notification to {} ', self.notify_to)
