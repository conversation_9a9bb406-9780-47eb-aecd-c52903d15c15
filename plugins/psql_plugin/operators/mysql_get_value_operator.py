# gsutil cp plugins/psql_plugin/operators/mysql_get_value_operator.py gs://europe-west1-mozart-cluster-2e910a39-bucket/plugins/psql_plugin/operators/mysql_get_value_operator.py
# gsutil cp plugins/psql.py gs://europe-west1-mozart-cluster-2e910a39-bucket/plugins/psql.py
######
# Copyright 2019 Apache Software fundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
########

# Airflow Operator to download results of a sql query to a file on the worker
# Pass chunksize parameter to download large tables without the
# worker running out of memory

from airflow.exceptions import AirflowException
from airflow.hooks.base import BaseHook
from airflow.models import BaseOperator

ALLOWED_CONN_TYPE = {
    "google_cloud_platform",
    "jdbc",
    "mssql",
    "mysql",
    "odbc",
    "oracle",
    "postgres",
    "presto",
    "snowflake",
    "sqlite",
    "vertica",
}


class MySqlGetValueOperator(BaseOperator):
    """
    Executes sql code in a specific database & return just one value

    :param sql: the sql code to be executed. (templated)
    :type sql: Can receive a str representing a sql statement or reference to a template file.
               Template reference are recognized by str ending in '.sql'.
               Expected SQL query to return Boolean (True/False), integer (0 = False, Otherwise = 1)
               or string (true/y/yes/1/on/false/n/no/0/off).
    :param conn_id: reference to a specific database
    :type conn_id: str
    :param database: name of database which overwrite defined one in connection
    :param parameters: (optional) the parameters to render the SQL query with.
    :type parameters: mapping or iterable
    """

    template_fields = ("sql",)
    template_ext = (".sql",)
    ui_color = "#a22034"
    ui_fgcolor = "#F7F7F7"

    def __init__(
            self,
            sql,
            conn_id="default_conn_id",
            database=None,
            parameters=None,
            *args,
            **kwargs
    ):
        super(MySqlGetValueOperator, self).__init__(*args, **kwargs)
        self.conn_id = conn_id
        self.sql = sql
        self.parameters = parameters
        self.database = database
        self._hook = None

    def _get_hook(self):
        self.log.debug("Get connection for %s", self.conn_id)
        conn = BaseHook.get_connection(self.conn_id)

        if conn.conn_type not in ALLOWED_CONN_TYPE:
            raise AirflowException(
                "The connection type is not supported by BranchSQLOperator.\
                Supported connection types: {}".format(list(ALLOWED_CONN_TYPE))
            )

        if not self._hook:
            self._hook = conn.get_hook()
            if self.database:
                self._hook.schema = self.database

        return self._hook

    def execute(self, context):
        # get supported hook
        self._hook = self._get_hook()

        if self._hook is None:
            raise AirflowException(
                "Failed to establish connection to '%s'" % self.conn_id
            )

        if self.sql is None:
            raise AirflowException("Expected 'sql' parameter is missing.")

        self.log.info(
            "Executing: %s (with parameters %s) with connection: %s",
            self.sql,
            self.parameters,
            self._hook,
        )
        record = self._hook.get_first(self.sql, self.parameters)
        if not record:
            raise AirflowException(
                "No rows returned from sql query. Operator expected True or False return value."
            )

        if isinstance(record, list):
            if isinstance(record[0], list):
                query_result = record[0][0]
            else:
                query_result = record[0]
        elif isinstance(record, tuple):
            query_result = record[0]
        else:
            query_result = record

        self.log.info("Query returns %s, type '%s'", query_result, type(query_result))

        return query_result
