# gsutil cp production/plugins/psql_plugin/operators/psql_get_value_operator.py gs://europe-west1-mozart-cluster-4128dbb8-bucket/plugins/psql_plugin/operators/

from typing import Iterable, Mapping, Optional, Union

from airflow.models import BaseOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook


class PostgresGetValueOperator(BaseOperator):
    """
    Executes sql code in a specific Postgres database

    :param sql: the sql code to be executed. (templated)
    :type sql: Can receive a str representing a sql statement,
        a list of str (sql statements), or reference to a template file.
        Template reference are recognized by str ending in '.sql'
    :param postgres_conn_id: reference to a specific postgres database
    :type postgres_conn_id: str
    :param autocommit: if True, each command is automatically committed.
        (default value: False)
    :type autocommit: bool
    :param parameters: (optional) the parameters to render the SQL query with.
    :type parameters: mapping or iterable
    :param database: name of database which overwrite defined one in connection
    :type database: str
    """

    template_fields = ('sql',)
    template_ext = ('.sql',)
    ui_color = '#ededed'

    def __init__(
            self,
            sql: str,
            postgres_conn_id: str = 'postgres_default',
            autocommit: bool = False,
            parameters: Optional[Union[Mapping, Iterable]] = None,
            database: Optional[str] = None,
            *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.sql = sql
        self.postgres_conn_id = postgres_conn_id
        self.autocommit = autocommit
        self.parameters = parameters
        self.database = database
        self.hook = PostgresHook(postgres_conn_id=self.postgres_conn_id,
                                 database=self.database)

    def execute(self, context):
        self.log.info('Executing: %s', self.sql)

        results = self.hook.get_records(self.sql, self.parameters)
        for output in self.hook.conn.notices:
            self.log.info(output)

        if results:
            return results[0][0]
        return None
