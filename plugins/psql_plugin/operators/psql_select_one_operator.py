import json

from airflow.models import BaseOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook


class PostgresSelectOneOperator(BaseOperator):
    """
        Executes sql code in a specific Postgres database
        :param postgres_conn_id: reference to a specific postgres database
        :type postgres_conn_id: string
        :param sql: the sql code to be executed. (templated)
        :type sql: Can receive a str representing a sql statement,
            a list of str (sql statements), or reference to a template file.
            Template reference are recognized by str ending in '.sql'
        :param database: name of database which overwrite defined one in connection
        :type database: string
    """

    template_fields = ('sql',)
    template_ext = ('.sql',)
    ui_color = '#ededed'

    def __init__(
            self,
            sql,
            postgres_conn_id='postgres_default',
            parameters=None,
            database=None,
            *args, **kwargs):
        super(PostgresSelectOneOperator, self).__init__(*args, **kwargs)
        self.sql = sql
        self.postgres_conn_id = postgres_conn_id
        self.parameters = parameters
        self.database = database

    def execute(self, context):
        self.log.info('Executing: %s', self.sql)
        self.hook = PostgresHook(postgres_conn_id=self.postgres_conn_id,
                                 database=self.database)
        result = self.hook.get_first(self.sql, parameters=self.parameters)
        for output in self.hook.conn.notices:
            self.log.info(output)

        return json.dumps(result, separators=(',', ':'))
