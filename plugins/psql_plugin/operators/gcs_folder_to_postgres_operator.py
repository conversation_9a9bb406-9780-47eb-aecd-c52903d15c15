# gsutil cp preproduction/plugins/psql_plugin/operators/gcs_folder_to_postgres_operator.py gs://europe-west1-mozart-cluster-fdc38c29-bucket/plugins/psql_plugin/operators/
# gsutil cp production/plugins/psql_plugin/operators/gcs_folder_to_postgres_operator.py gs://europe-west1-mozart-cluster-4128dbb8-bucket/plugins/psql_plugin/operators/


import csv
import os
import shutil
import tempfile

from airflow.models import BaseOperator
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from airflow.providers.postgres.hooks.postgres import PostgresHook


class GoogleCloudStorageFolderToPostgresOperator(BaseOperator):
    """
        Load a Google cloud storage file to postgres

        :param schema: name of the schema
        :type schema: string
        :param table: name of the table to dump
        :type table: bases
        :param bucket: The Google cloud storage bucket where the object is. (templated)
        :type bucket: string
        :param object: The name of the object to download in the Google cloud
            storage bucket. (templated)
        :type object: string
        :param postgres_conn_id: reference to a specific postgres database
        :type postgres_conn_id: string
        :param gcp_conn_id: (Optional) The connection ID used to connect to Google Cloud Platform.
        :type gcp_conn_id: str
    """

    template_fields = ('gcs_path', 'table', 'delimiter',)
    ui_color = '#ededed'

    def __init__(
            self,
            bucket,
            database,
            schema,
            table,
            gcs_path,
            delimiter='.csv',
            postgres_conn_id='postgres_default',
            gcp_conn_id='google_cloud_default',
            truncate_before_import=False,
            delegate_to=None,
            *args, **kwargs):
        super(GoogleCloudStorageFolderToPostgresOperator, self).__init__(*args, **kwargs)

        # psql infos
        self.database = database
        self.schema = schema
        self.table = table
        self.truncate_before_import = truncate_before_import

        # gcs infos
        self.gcs_path = gcs_path
        self.bucket = bucket
        self.delimiter = delimiter

        # conn id
        self.gcp_conn_id = gcp_conn_id
        self.postgres_conn_id = postgres_conn_id

    def execute(self, context):

        gcs_hook = GCSHook(gcp_conn_id=self.gcp_conn_id)

        # First Step List Files
        list_files = gcs_hook.list(bucket_name=self.bucket,
                                   prefix=self.gcs_path,
                                   delimiter=self.delimiter)

        self.log.info('Files to import: {}'.format(str(list_files)))
        if not list_files:
            print("List is empty")
            return []

        pg_hook = PostgresHook(
            postgres_conn_id=self.postgres_conn_id,
            database=self.database
        )

        if self.truncate_before_import:
            full_table_name = '{schema}.{table}'.format(schema=self.schema, table=self.table)
            pg_hook.run('TRUNCATE TABLE {}'.format(full_table_name))

        for file in list_files:
            head, file_name = os.path.split(file)
            self.log.info(file_name)
            fd, local_filename = tempfile.mkstemp(text=True)

            download_from_gcs = gcs_hook.download(bucket_name=self.bucket,
                                                  object_name=self.gcs_path + file_name,
                                                  filename=local_filename)

            local_filename = self.clean_csv(local_filename)

            gcs_hook.upload(
                bucket_name=self.bucket,
                object_name=self.gcs_path + 'new/' + file_name,
                filename=local_filename,
                mime_type='application/octet-stream',
                gzip=False,
            )

            # load the file into postgres
            pg_hook.bulk_load(self.schema + '.' + self.table, local_filename)

            # output errors
            for output in pg_hook.conn.notices:
                self.log.info(output)

            # remove local file
            os.close(fd)
            os.unlink(local_filename)

    def clean_csv(self, intput_filename):
        csv_delimiter = '\t'
        fd, output_filename = tempfile.mkstemp(text=True)

        with open(intput_filename) as csvin:
            readfile = csv.reader(csvin, delimiter=csv_delimiter)
            with open(output_filename, 'w') as csvout:
                writefile = csv.writer(csvout, delimiter=csv_delimiter, lineterminator='\n')
                for index, row in enumerate(readfile):
                    # self.log.info('row    :' + str(row))
                    row = [i.replace("\"", "") for i in row]
                    row = [i.replace("\'", "") for i in row]
                    row = [i.replace('""', '"') for i in row]
                    # row = [i.replace(' ', None) for i in row]
                    writefile.writerow(row)

        shutil.copy(output_filename, intput_filename)
        os.unlink(output_filename)
        os.close(fd)

        return intput_filename
