from airflow.sensors.base import BaseSensorOperator


class DateSensor(BaseSensorOperator):
    """
    Waits until the specified date.

    :param target_date: date after which the job succeeds
    :type target_date: datetime
    """

    def __init__(self, target_date, *args, **kwargs):
        super().__init__(
            soft_fail=True,
            timeout=0,
            poke_interval=1,
            *args, **kwargs)

        self.target_date = target_date

    def poke(self, context):
        self.log.info('Checking if the date (%s) has come', self.target_date)
        if context['execution_date'] is None:
            self.log.error('Cannot get execution_date from context. set `provide_context` to True')

        return context['execution_date'] > self.target_date
