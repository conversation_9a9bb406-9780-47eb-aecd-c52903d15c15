# -*- coding: utf-8 -*-
#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

import csv
import io
import json

from airflow.models import BaseOperator
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from airflow.providers.google.cloud.hooks.gcs import _parse_gcs_url
from airflow.providers.postgres.hooks.postgres import PostgresHook

open_options = {
    'newline': '',
    'encoding': 'utf-8'
}

csv_options = {
    'delimiter': ';',
    'quotechar': '"',
    'quoting': csv.QUOTE_MINIMAL
}

pandas_options = {
    'sep': ';',
    'index_col': 'email_sha256',
    'usecols': ['email_sha256', 'unsubscribe_date', 'unsubscribe_type', 'consent_id'],
    'parse_dates': ['unsubscribe_date']
}

"""
The role of the operator is to interact with the journal ( matrix email event ) 

 - @types :  sub / unsub / blacklist / change_email
 
:param sql_conn_id: the id of postgres connection
:type sql_conn_id: string

:param event_type:  check @types 
:type event_type: string

:param event_source: the source of the event ( to be injected on payload ) 
:type event_source: string

:param event_medium: the source of the event ( to be injected on payload ) 
:type event_medium: string

:param event_process: the process which generates this event, dag_id ( to be injected on payload ) 
:type event_process: string

:param src : the source of the event ( to be injected on payload ) 
:type src: string

"""


def generate_payload(**kwargs):
    master_payload = {'ev': '100', 'app': 'mozart', 'medium': kwargs['medium'], 'source': kwargs['source'],
                      'process': kwargs['process'], 'consent_ids': [],
                      'ip': '127.0.0.1'}

    return master_payload


class EmailEventOperator(BaseOperator):
    template_fields = ['event_medium', 'src']

    def __init__(self,
                 sql_conn_id='postgres_default',
                 gcs_conn_id='gcs_matrix',
                 event_type=None,
                 event_source=None,
                 event_medium=None,
                 event_process=None,
                 src=None,
                 delegate_to=None,
                 *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.sql_conn_id = sql_conn_id
        self.gcs_conn_id = gcs_conn_id
        self.event_type = event_type
        self.event_source = event_source
        self.event_medium = event_medium
        self.event_process = event_process
        self.src = src

        # xcom from the previous task []
        self.delegate_to = delegate_to

    def execute(self, context, kwargs=None):
        lines_count = 0
        # init gcs hook
        gcs_hook = GCSHook(
            gcp_conn_id=self.gcs_conn_id,
            delegate_to=self.delegate_to)
        # init postgresHook
        pg_sql = PostgresHook(postgres_conn_id=self.sql_conn_id, database='matrix')
        # prepare payload skeleton
        payload = generate_payload(medium=self.event_medium, source=self.event_source, process=self.event_process)

        if self.src is not None:
            bucket = _parse_gcs_url(self.src)[0]
            object = _parse_gcs_url(self.src)[1]

            if gcs_hook.exists(bucket, object):
                # get file from gcs ( type returned : Bytes )
                body = gcs_hook.download(bucket, object)
                # convert / unicode Bytes returned files to StringIO
                body_uncoded = io.StringIO(body.decode(open_options['encoding']))
                # parse CSV from the StringIO
                csv_content = csv.reader(body_uncoded, **csv_options)
                headers = next(csv_content)
                rows_to_insert = []
                target_columns = ['create_date', 'type', 'email_hash', 'payload']
                for line in csv_content:
                    # prepare consents ( convert from string to list to make possible json dump )
                    consents_string = line[2].replace("'", "")
                    consents_list = consents_string.strip('][').split(', ')
                    # Using list comprehension
                    consents = [int(x) for x in consents_list]
                    # using sort function
                    consents.sort()
                    payload['consent_ids'] = consents

                    # fields to insert
                    create_date = line[1]
                    event_type = self.event_type
                    email_hash = line[0]
                    payload_json = json.dumps(payload)

                    rows_to_insert.append([create_date, event_type, email_hash, payload_json])

                # bulk insert into table
                pg_sql.insert_rows('matrix__email_queue.email_event', rows_to_insert, target_columns)

            else:
                return False

        return True
