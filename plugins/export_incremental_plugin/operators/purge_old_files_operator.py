# gsutil cp production/plugins/export_incremental_plugin/operators/purge_old_files_operator.py gs://europe-west1-mozart-cluster-4128dbb8-bucket/plugins/export_incremental_plugin/operators/

import re

from airflow.models import BaseOperator
from airflow.providers.sftp.hooks.sftp import SFTPHook
from airflow.utils.email import send_email


class PurgeOldFilesOperator(BaseOperator):
    """
    old comment : Call API Splio report and get all campaign statistics from date 

    New:
    
    Delete files according to a pattern inside the partners ftp.
    If files are deleted, we received a notification by email (to modify)

    New:
    :param protocol: protocol if it is a SFTP
    :type param protocol: string
    :param conf: config information about partners
    :type param conf: dict
    :param pattern: delete file with this pattern, exemple: '.*' + '{{ ds }}' + '.*'
    :type param pattern: string
    
    Old
    :param postgres_conn_id: PostgreSQL connection id
    :type param postgres_conn_id: string
    :param database: PostgreSQL database
    :type database: string
    :param gcs_conn_id: GCS connection id of HTML source 
    :type database: string
    :param bucket: Bucket of HTML source 
    :type database: string
    """
    template_fields = ['pattern']

    def __init__(self,
                 protocol,
                 conf,
                 pattern,
                 *args,
                 **kwargs):
        super().__init__(*args, **kwargs)

        self.protocol = protocol
        self.conf = conf
        self.pattern = pattern

    def execute(self, context):
        older_files = []

        if len(self.conf) == 0:
            raise NameError('No configuration found')

        for ftp_conn_id, config in self.conf.items():
            if self.protocol == 'SFTP':
                ftp = SFTPHook(ftp_conn_id=ftp_conn_id)

            # Have to add more FTP Protocols
            # if self.protocol == 'FTPS':

            self.log.info('Connection to SFTP using conn_id : {}'.format(ftp_conn_id))
            list_files = ftp.list_directory(config['path_root'])

            self.log.info('Pattern : ' + str(self.pattern))
            self.log.info('List sftp files : {}'.format(str(list_files)))
            list_files = self.filter_files(list_files, patterns=[self.pattern], mode_include=True)

            for file in list_files:
                older_files.append(file)
                ftp.delete_file('{}/{}'.format(config['path_root'], file))

            if len(older_files) > 0:
                self.log.info('Deleting file : {}'.format(str(older_files)))
                self.notify_email(older_files)

    def filter_files(self, files, patterns=[], mode_include=True):
        """
        Filter a liste of files, against pattern_list, including or excluding files depending on mode_include.

        :param file: list of file names
        :type file: list
        :param patterns: list of regexp
        :type patterns: list
        :param mode_include: True : include, False ; exclude
        :type mode_include: bool
        """

        files_to_keep = []

        for file in files:
            keep = not mode_include
            for pattern in patterns:
                if re.match(pattern, file):
                    keep = mode_include
                    continue

            if keep:
                files_to_keep.append(file)

        return files_to_keep

    def notify_email(self, files):
        list_files = " <br>-".join(files)
        title = "(Information) Suppression des fichiers de la veille"
        body = ("Bonjour, <br> "
                "<br><br> blablabla message :<br><br>"
                "<br> " + str(list_files) + "<br> "
                                            "<br> <br/>Cordialement, <br> "
                                            "L'équipe technique IT-Data"
                )

        to = ("<EMAIL>")
        # to = ('<EMAIL>', '<EMAIL>', ' <EMAIL>')
        # to = ('<EMAIL>', '<EMAIL>', '<EMAIL>')
        send_email(to, title, body, conn_id='sendgrid_default')
        print('email html + ' + body)
