# gsutil cp plugins/export_plugin/operators/export_splio_bigquery_operator.py gs://europe-west1-mozart-cluster-2e910a39-bucket/plugins/export_plugin/operators/

import os
import subprocess
import tempfile
from datetime import timed<PERSON>ta

from airflow.operators.bash import BashOperator
from airflow.providers.google.cloud.hooks.bigquery import BigQueryHook
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow.providers.sftp.hooks.sftp import SFTPHook


class ExportSplioBigQueryOperator(BigQueryExecuteQueryOperator):
    """
        Export a postgres table to google cloud storage

        :param schema: name of the schema
        :type schema: string
        :param table: name of the table to dump
        :type table: bases
        :param universes: universe list with their ids
        :type universes: dict
        :param universes_sftp: universe list with SFTP conn_id list
        :type universes_sftp: dict
        :param bucket: The Google cloud storage bucket where the object is. (templated)
        :type bucket: string
        :param object: The name of the object to upload in the Google cloud
            storage bucket. (templated)
        :type object: string
        :param postgres_conn_id: reference to a specific postgres database
        :type postgres_conn_id: string
        :param gcp_conn_id: (Optional) The connection ID used to connect to Google Cloud Platform.
        :type gcp_conn_id: str
    """

    template_fields = ('splio_sequence', 'sql', 'sql_before_export', 'sql_after_export', 'bucket_path',)
    template_ext = ('.sql',)
    ui_color = '#ededed'

    def __init__(
            self,
            destination_cloud_storage_path_uris,
            bq_project,
            bucket_path,
            bucket,
            universes,
            universes_sftp,
            destination_dataset_table_temporary='temp.{universe}_contacts_007_{splio_sequence}',
            datahub_file_template='{universe}_contacts_007_{splio_sequence}.csv',
            splio_sequence='{{ execution_date.strftime("%Y%m%d_%H%M") }}',
            write_disposition_before='WRITE_TRUNCATE',
            write_disposition_after='WRITE_TRUNCATE',
            create_disposition_before='CREATE_IF_NEEDED',
            create_disposition_after='CREATE_IF_NEEDED',
            flux_ref=7,
            splio_remote_dir='imports/',
            gcp_conn_id='google_cloud_default',
            store_monitoring_datahub=True,
            sql_before_export=None,
            sql_after_export=None,
            destination_dataset_table_before_script=None,
            monitoring_database='matrix',
            monitoring_postgres_conn_id='psql_matrix_email_app',
            destination_dataset_table_after_script=None,
            compression='NONE',
            export_format='CSV',
            field_delimiter=';',
            print_header=False,
            delegate_to=None,
            *args,
            **kwargs):
        super(ExportSplioBigQueryOperator, self).__init__(*args, **kwargs)
        ######################################
        #
        #    parent BigQueryExecuteQueryOperator configs
        #
        #    bql=None,
        #    sql=None,
        #    destination_dataset_table=False,
        #    write_disposition='WRITE_EMPTY',
        #    allow_large_results=False,
        #    flatten_results=None,
        #    bigquery_conn_id='bigquery_default',
        #    delegate_to=None,
        #    udf_config=False,
        #    use_legacy_sql=True,
        #    maximum_billing_tier=None,
        #    maximum_bytes_billed=None,
        #    create_disposition='CREATE_IF_NEEDED',
        #    schema_update_options=(),
        #    query_params=None,
        #    labels=None,
        #    priority='INTERACTIVE',
        #    time_partitioning={},
        #
        ######################################

        # Splio configs
        self.universes = universes
        self.universes_sftp = universes_sftp
        self.splio_sequence = splio_sequence
        self.splio_remote_dir = splio_remote_dir
        self.datahub_file_template = datahub_file_template
        self.monitoring_database = monitoring_database

        # export configs
        self.destination_cloud_storage_path_uris = destination_cloud_storage_path_uris
        self.compression = compression
        self.export_format = export_format
        self.field_delimiter = field_delimiter
        self.print_header = print_header
        self.delegate_to = delegate_to

        # templates sql / query
        # self.sql=sql
        self.sql_before_export = sql_before_export
        self.sql_after_export = sql_after_export
        self.destination_dataset_table_before_script = destination_dataset_table_before_script
        self.destination_dataset_table_after_script = destination_dataset_table_after_script
        self.destination_dataset_table_temporary = destination_dataset_table_temporary
        # monitoring
        self.store_monitoring_datahub = store_monitoring_datahub
        self.flux_ref = flux_ref
        # conn id
        self.write_disposition_before = write_disposition_before
        self.write_disposition_after = write_disposition_after

        self.create_disposition_after = create_disposition_after
        self.create_disposition_before = create_disposition_before

        self.monitoring_postgres_conn_id = monitoring_postgres_conn_id
        self.gcp_conn_id = gcp_conn_id
        self.bq_project = bq_project
        self.bucket = bucket
        self.bucket_path = bucket_path
        self.bq_cursor = None

        self.bq_hook = BigQueryHook(
            gcp_conn_id=self.gcp_conn_id,
            use_legacy_sql=self.use_legacy_sql,
            delegate_to=self.delegate_to)

    def execute(self, context):

        # STEP 1: execute before export script
        if self.sql_before_export:
            destination_before_script = self.bq_project + ':' + self.destination_dataset_table_before_script
            self.log.info('Execute before export script to table: '.format(destination_before_script))

            self.bq_hook.run_query(
                self.sql_before_export,
                destination_dataset_table=destination_before_script,
                write_disposition=self.write_disposition_before,
                allow_large_results=self.allow_large_results,
                flatten_results=self.flatten_results,
                udf_config=self.udf_config,
                maximum_billing_tier=self.maximum_billing_tier,
                maximum_bytes_billed=self.maximum_bytes_billed,
                create_disposition=self.create_disposition_before,
                query_params=self.query_params,
                labels=self.labels,
                schema_update_options=self.schema_update_options,
                priority=self.priority,
                time_partitioning=self.time_partitioning
            )

        # STEP 2: process export to splio
        for universe, universe_id in self.universes.items():
            csv_filename = self.datahub_file_template.format(universe=universe, splio_sequence=self.splio_sequence)
            gcs_filename = os.path.join(self.bucket_path.rstrip('/'), csv_filename)
            gcs_filename_uri = os.path.join(self.destination_cloud_storage_path_uris.rstrip('/'), gcs_filename)
            sftp_filename = (os.path.join(self.splio_remote_dir.rstrip('/'), csv_filename).split("*")[
                                 0] + ".csv" if "*" in os.path.join(self.splio_remote_dir.rstrip('/'),
                                                                    csv_filename) else os.path.join(
                self.splio_remote_dir.rstrip('/'), csv_filename))
            sftp_conn_id = self.universes_sftp.get(universe)
            self.log.info('Current Universe: {} for id:{}'.format(
                universe,
                universe_id
            ))

            self.log.info('Processing %s', csv_filename)

            # create temp file
            fd, tmp_filename = tempfile.mkstemp(text=True)

            # create temporary table for export
            destination_dataset_temporary_table = self.bq_project + ':' + self.destination_dataset_table_temporary.format(
                universe=universe,
                splio_sequence=self.splio_sequence
            )

            query = self.sql.format(universe_id=universe_id)
            self.log.info('Execute Query {} for universe_id:{} to: {}'.format(
                query,
                universe_id,
                destination_dataset_temporary_table
            ))

            self.bq_hook.run_query(
                query,
                destination_dataset_table=destination_dataset_temporary_table,
                write_disposition=self.write_disposition,
                allow_large_results=self.allow_large_results,
                flatten_results=self.flatten_results,
                udf_config=self.udf_config,
                maximum_billing_tier=self.maximum_billing_tier,
                maximum_bytes_billed=self.maximum_bytes_billed,
                create_disposition=self.create_disposition,
                # query_params=self.query_params,
                labels=self.labels,
                schema_update_options=self.schema_update_options,
                # priority=self.priority,
                # time_partitioning=self.time_partitioning
            )

            # dump temporary data from bq to gcs
            self.bq_hook.run_extract(
                destination_dataset_temporary_table,
                gcs_filename_uri,
                self.compression,
                self.export_format,
                self.field_delimiter,
                self.print_header,
                self.labels)

            # GCS compose
            final_gcs_filename = (gcs_filename.split("*")[0] + ".csv" if "*" in gcs_filename else gcs_filename)
            final_gcs_filename_uri = (
                gcs_filename_uri.split("*")[0] + ".csv" if "*" in gcs_filename_uri else gcs_filename_uri)

            # self.log.info('Executing Compose')
            # self.log.info('*** {}'.format(self.destination_dataset_table_temporary.format(
            #         universe=universe,
            #         splio_sequence=self.splio_sequence
            #     )))
            # self.log.info('### {}'.format(self.destination_dataset_table_temporary.format(
            #         universe=universe,
            #         splio_sequence=self.splio_sequence
            #     ).split(".")[1]))

            script_args = '/home/<USER>/gcs/data/scripts/gcs_compose.sh {} {} {} {} {} {}'.format(
                os.path.join(self.destination_cloud_storage_path_uris.rstrip("/"), self.bucket_path.rstrip("/")),
                gcs_filename_uri,
                final_gcs_filename_uri,
                self.flux_ref,
                self.bq_project,
                self.destination_dataset_table_temporary.format(
                    universe=universe,
                    splio_sequence=self.splio_sequence
                ).split(".")[1]
            )
            gcs_compose = BashOperator(
                task_id='gcs_compose',
                bash_command=script_args,
                execution_timeout=timedelta(hours=6),
                email_on_failure=False,

            )
            gcs_compose.execute(context)
            self.log.info('End Compose')

            # download file from gcs
            # create temp file
            fd, tmp_filename = tempfile.mkstemp(text=True)

            self.log.info('Executing download: %s, %s, %s',
                          self.bucket,
                          final_gcs_filename,
                          tmp_filename)

            # Upload fils to temp file
            gcs_hook = GCSHook(
                gcp_conn_id=self.gcp_conn_id,
                delegate_to=self.delegate_to)

            file_bytes = gcs_hook.download(
                bucket_name=self.bucket,
                object_name=final_gcs_filename,
                filename=tmp_filename)

            self.log.debug(file_bytes)

            if not self.__check_has_data(tmp_filename):
                # Emtpy are deleted (local & gcs) and ignored
                os.close(fd)
                os.unlink(tmp_filename)

                # delete empty files from GCS Bucket
                script = '''
                gsutil rm -r {gcs_filename_uri}
                '''.format(
                    gcs_filename_uri=gcs_filename_uri
                )
                self.log.info('Delete Script: %s', script)
                subprocess.call(script, shell=True)
                continue

            # upload file to splio sftp
            if sftp_conn_id:
                # Create SFTP hook without using context manager
                sftp = SFTPHook(ftp_conn_id=sftp_conn_id)
                try:
                    self.log.info('Uploading using {} conn_id : local={} remote={}'.format(
                        sftp_conn_id,
                        tmp_filename,
                        sftp_filename))
                    sftp.store_file(sftp_filename, tmp_filename)

                    # ADD Monitoring Infos
                    if self.store_monitoring_datahub is True:
                        volume = self.count_file_lignes(tmp_filename)

                        monitoring_query = '''
                            INSERT INTO matrix__email_splio.datahub_monitoring
                            (universe_id, flux_ref, splio_sequence, sent_date, volume)
                            VALUES(
                                {universe_id},
                                {flux_ref},
                                {splio_sequence},
                                NOW(),
                                {volume}
                            )
                        ;
                        '''.format(
                            universe_id=universe_id,
                            flux_ref=self.flux_ref,
                            splio_sequence=self.splio_sequence.replace('_', ''),
                            volume=volume,
                        )

                        monitoring_hook = PostgresHook(postgres_conn_id=self.monitoring_postgres_conn_id,
                                                       database=self.monitoring_database)
                        monitoring_hook.run(monitoring_query)
                        # output psql errors
                        for output in monitoring_hook.conn.notices:
                            self.log.info(output)
                finally:
                    # Close the connection explicitly
                    if hasattr(sftp, 'conn') and sftp.conn is not None:
                        sftp.conn.close()

            # remove temp file
            os.close(fd)
            os.unlink(tmp_filename)

        # STEP 3: execute after export script
        if self.sql_after_export:
            if self.destination_dataset_table_after_script is not None:
                destination_after_script = self.bq_project + ':' + self.destination_dataset_table_after_script
                self.log.info('Execute after export script to table: '.format(destination_after_script))
                self.bq_hook.run_query(
                    self.sql_after_export,
                    destination_dataset_table=destination_after_script,
                    write_disposition=self.write_disposition_after,
                    allow_large_results=self.allow_large_results,
                    flatten_results=self.flatten_results,
                    udf_config=self.udf_config,
                    maximum_billing_tier=self.maximum_billing_tier,
                    maximum_bytes_billed=self.maximum_bytes_billed,
                    create_disposition=self.create_disposition_after,
                    query_params=self.query_params,
                    labels=self.labels,
                    schema_update_options=self.schema_update_options,
                    priority=self.priority,
                    time_partitioning=self.time_partitioning
                )
            else:
                self.log.info('Execute after export script to table')
                self.log.info('After Query: %s', self.sql_after_export)
                self.bq_hook.run_query(self.sql_after_export)

    def __check_has_data(self, file):
        count = 0
        with open(file) as fd:
            for _ in fd:
                count += 1
                if count == 2:
                    return True

        return False

    def count_file_lignes(self, filename):
        lines = 0
        for _ in open(filename):
            lines += 1
        if lines > 0:
            lines -= 1  # remove header
        return lines
