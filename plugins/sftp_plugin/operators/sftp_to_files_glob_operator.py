# gsutil cp production/plugins/sftp_plugin/operators/sftp_to_files_glob_operator.py gs://europe-west1-mozart-cluster-4128dbb8-bucket/plugins/sftp_plugin/operators/

import os
import re

from airflow.models import BaseOperator
from airflow.providers.sftp.hooks.sftp import SFTPHook


class SftpToFilesGlobOperator(BaseOperator):
    """
    Upload local path to remove SFTP server

    :param sftp_conn_id: Ftp connection id
    :type sftp_conn_id: str
    :param local_filename: local file path
    :type local_filename: str
    :param sftp_filename: remove file path
    :type sftp_filename: str
    """

    def __init__(self,
                 sftp_conn_id,
                 sftp_path,
                 local_path,
                 file_pattern=None,
                 file_exclude_pattern=None,
                 *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.sftp_conn_id = sftp_conn_id
        self.sftp_path = sftp_path
        self.local_path = local_path
        self.file_pattern = file_pattern
        self.file_exclude_pattern = file_exclude_pattern

    def execute(self, context):

        if not os.path.isdir(self.local_path):
            os.makedirs(self.local_path)
            self.log.info('create directory : ' + self.local_path)

        sftp = SFTPHook(ftp_conn_id=self.sftp_conn_id)
        try:
            self.log.info('Connection to SFTP using conn_id : {}'.format(
                self.sftp_conn_id
            ))
            # sftp.store_file(self.sftp_filename, self.local_filename)
            list_files = sftp.list_directory(self.sftp_path);

            self.log.info('Pattern : ' + str(self.file_pattern))
            self.log.info('Files bebore filter apply' + str(list_files))
            # filter files on self.file_pattern and self.file_exclude_pattern
            list_files = self.filter_files(list_files, patterns=self.file_pattern, mode_include=True)
            self.log.info('Files after  filter pattern apply' + str(list_files))
            list_files = self.filter_files(list_files, patterns=self.file_exclude_pattern, mode_include=False)
            self.log.info('Files after  filter pattern exclude apply' + str(list_files))

            for file in list_files:
                local_filename = self.local_path + file
                sftp_filename = self.sftp_path + file
                self.log.info('Downloading using {} conn_id : remote={} local={}'.format(
                    self.sftp_conn_id,
                    sftp_filename,
                    local_filename
                ))
                sftp.retrieve_file(sftp_filename, local_filename)
        finally:
            # Close the connection explicitly
            if hasattr(sftp, 'conn') and sftp.conn is not None:
                sftp.conn.close()

        # return self.sftp_filename
        return list_files

        # return self.sftp_filename

    """
    Filter a list of files, against pattern_list, including or excluding files depending on mode_include.

    :param file: list of file names
    :type file: list
    :param patterns: list of regexp
    :type patterns: list
    :param mode_include: True : include, False ; exclude
    :type mode_include: bool
    """

    def filter_files(self, files, patterns=[], mode_include=True):
        files_to_keep = []

        for file in files:
            keep = not mode_include
            for pattern in patterns:
                if re.match(pattern, file):
                    keep = mode_include
                    continue

            if keep:
                files_to_keep.append(file)

        return files_to_keep
