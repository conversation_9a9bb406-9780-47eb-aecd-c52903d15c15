from airflow.models import BaseOperator
from airflow.providers.ftp.hooks.ftp import FTPHook


class FileToFtpOperator(BaseOperator):
    """
    Upload local path to remove FTP server

    :param ftp_conn_id: Ftp connection id
    :type ftp_conn_id: str
    :param local_filename: local file path
    :type local_filename: str
    :param ftp_filename: remove file path
    :type ftp_filename: str
    """

    def __init__(self,
                 ftp_conn_id,
                 local_filename,
                 ftp_filename,
                 *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.ftp_conn_id = ftp_conn_id
        self.local_filename = local_filename
        self.ftp_filename = ftp_filename

    def execute(self, context):
        with FTPHook(ftp_conn_id=self.ftp_conn_id) as ftp:
            print('Uploading using {} conn_id : local={} remote={}'.format(
                self.ftp_conn_id,
                self.local_filename,
                self.ftp_filename
            ))
            ftp.store_file(self.ftp_filename, self.local_filename)
        return self.ftp_filename
