# gsutil cp production/plugins/sftp_plugin/operators/list_files_sftp_operator.py gs://europe-west1-mozart-cluster-4128dbb8-bucket/plugins/sftp_plugin/operators/
from airflow.models import BaseOperator
from airflow.providers.sftp.hooks.sftp import SFTPHook


class ListFilesSftpOperator(BaseOperator):
    """
    List remote files

    :param ftp_conn_id: Ftp connection id
    :type ftp_conn_id: str
    :param ftp_directory: remove directory path
    :type ftp_directory: str
    """

    def __init__(self,
                 sftp_conn_id,
                 sftp_directory,
                 *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.sftp_conn_id = sftp_conn_id
        self.sftp_directory = sftp_directory

    def execute(self, context):
        self.log.info('Listing directory using {} conn_id : remote={}'.format(
            self.sftp_conn_id,
            self.sftp_directory
        ))

        sftp = SFTPHook(ftp_conn_id=self.sftp_conn_id)
        try:
            files = sftp.list_directory(self.sftp_directory)
        finally:
            # Close the connection explicitly
            if hasattr(sftp, 'conn') and sftp.conn is not None:
                sftp.conn.close()

        self.log.info("SFTP directory contains files : {}".format(files))

        return files
