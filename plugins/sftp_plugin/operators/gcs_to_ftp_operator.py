# gsutil cp production/plugins/sftp_plugin/operators/gcs_to_ftp_operator.py gs://europe-west1-mozart-cluster-4128dbb8-bucket/plugins/sftp_plugin/operators/
import os
import tempfile

from airflow.models import BaseOperator
from airflow.providers.ftp.hooks.ftp import FTPHook
from airflow.providers.google.cloud.hooks.gcs import GCSHook


class GoogleCloudStorageToFtpOperator(BaseOperator):
    """
    Upload local path to remote FTP server

    :param ftp_conn_id: Ftp connection id
    :type ftp_conn_id: str
    :param gcs_filename: local file path
    :type gcs_filename: str
    :param ftp_filename: remove file path
    :type ftp_filename: str
    """

    template_fields = ('gcs_filename', 'ftp_filename')

    def __init__(self,
                 gcs_bucket,
                 ftp_conn_id,
                 gcs_filename,
                 ftp_filename,
                 gcs_conn_id='google_cloud_default',
                 delegate_to=None,
                 *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.ftp_conn_id = ftp_conn_id
        self.ftp_filename = ftp_filename

        self.gcs_conn_id = gcs_conn_id
        self.gcs_bucket = gcs_bucket
        self.gcs_filename = gcs_filename
        self.delegate_to = delegate_to

    def execute(self, context):
        print('Uploading using {} conn_id : local=gs://{}/{} remote={}'.format(
            self.ftp_conn_id,
            self.gcs_bucket,
            self.gcs_filename,
            self.ftp_filename
        ))

        # get gcs file to local
        fd, local_filename = tempfile.mkstemp(text=True)
        hook = GCSHook(gcp_conn_id=self.gcs_conn_id, delegate_to=self.delegate_to)
        hook.download(bucket_name=self.gcs_bucket, object_name=self.gcs_filename, filename=local_filename)

        # upload local file to ftp
        with FTPHook(ftp_conn_id=self.ftp_conn_id) as ftp:
            ftp.store_file(self.ftp_filename, local_filename)

        # remove local file
        os.close(fd)
        os.unlink(local_filename)

        return self.ftp_filename
