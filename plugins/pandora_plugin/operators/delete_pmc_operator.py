
from airflow.models import <PERSON><PERSON>perator
from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow.providers.http.hooks.http import HttpHook

import json
from datetime import datetime
from pprint import pprint


class DeletePmcOperator(BaseOperator):
    get_events = """
    -- mozart-id : {dag_id}.{task_id}
    WITH email_event_seq AS (
        -- get last processed email event id
        SELECT email_event_id 
        FROM matrix__email.email_event_sequence
        WHERE process_name = 'process_email_event_end'
    ), delete_pmc_events AS (
        -- get delete pmc events to handle
        SELECT DISTINCT dpe.id, dpe.email, dpe.email_hash
        FROM pandora.delete_pmc_event AS dpe
        CROSS JOIN email_event_seq AS ees
        LEFT JOIN pandora.delete_pmc_prune dpp ON dpp.delete_pmc_event_id = dpe.id
        WHERE dpe.has_deleted = 'f'
            AND dpe.retry_count < 5
            -- skip already events
            AND dpp.delete_pmc_event_id IS NULL
            -- unsub welcome is already processed
            AND dpe.email_event_id <= ees.email_event_id
    ), events_to_process AS (
        -- make sure concerned profile does not have sub consents
        SELECT dpe.id, dpe.email, dpe.email_hash, COUNT(CASE WHEN pec.status = 'sub' THEN 1 ELSE NULL END) AS sub_consent
        FROM delete_pmc_events AS dpe
        JOIN matrix__email.profile_master_id AS pmi 
        ON dpe.email_hash = pmi.email_sha256
        LEFT JOIN matrix__email.profiles_email_consents AS pec 
        ON pmi.id = pec.profile_master_id
        LEFT JOIN karinto.email_consent AS kec 
        ON pec.email_consent_id = kec.id 
        AND kec.type IN ('nl', 'alert', 'nl-paid', 'alert-paid', 'prg') 
        AND kec.active = true
        GROUP BY 1,2,3
    )
    SELECT *
    FROM events_to_process
    WHERE sub_consent = 0;
    """

    update_success_statement = """
    -- mozart-id : {dag_id}.{task_id}
    UPDATE pandora.delete_pmc_event
    SET has_deleted = 't',
    response_code = '{response_code}',
    response_message = '{response_message}',
    last_api_call_date = '{api_call_date}'
    WHERE id = {event_id};
    """

    update_failure_statement = """
    -- mozart-id : {dag_id}.{task_id}
    UPDATE pandora.delete_pmc_event
    SET retry_count = retry_count + 1,
    response_code = '{response_code}',
    response_message = '{response_message}',
    last_api_call_date = '{api_call_date}'
    WHERE id = {event_id};
    """

    insert_prune_statement = """
    -- mozart-id : {dag_id}.{task_id}
    INSERT INTO pandora.delete_pmc_prune
    (delete_pmc_event_id, create_date)
    VALUES ({event_id}, '{call_date}');
    """

    def __init__(self,
                 postgres_conn_id,
                 http_conn_id,
                 pmc_endpoint,
                 *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.hook = PostgresHook(postgres_conn_id=postgres_conn_id, database='matrix')
        self.pmc_endpoint = pmc_endpoint
        self.http_hook = HttpHook('POST', http_conn_id=http_conn_id)

    def events_to_process(self):
        pprint(self.get_events.format(dag_id=self.dag_id, task_id=self.task_id))
        return self.hook.get_records(self.get_events.format(dag_id=self.dag_id, task_id=self.task_id))

    def handle_response(self, event_id, response):
        response_obj = response.json()
        call_date = datetime.utcnow()

        if response.status_code == 200:
            self.hook.run(self.update_success_statement.format(
                response_code=response_obj['code'],
                response_message=response_obj['message'],
                api_call_date=call_date,
                event_id=event_id,
                dag_id=self.dag_id, task_id=self.task_id
            ))
            self.hook.run(self.insert_prune_statement.format(
                event_id=event_id,
                call_date=call_date,
                dag_id=self.dag_id, task_id=self.task_id
            ))
        else:
            self.hook.run(self.update_failure_statement.format(
                response_code=response_obj['error']['code'],
                response_message=response_obj['error']['msg'],
                api_call_date=call_date,
                event_id=event_id,
                dag_id=self.dag_id, task_id=self.task_id
            ))

    def execute(self, context):
        events_to_process = self.events_to_process()
        print('Events to process : ')
        pprint(events_to_process)

        for event in events_to_process:
            print('Event')
            pprint(event)
            response = self.http_hook.run(
                self.pmc_endpoint,
                data=json.dumps({
                    'email': event[1]
                }),
                extra_options={'check_response': False}
            )
            print('Response : ')
            pprint(response)

            self.handle_response(event_id=event[0], response=response)

        return True
