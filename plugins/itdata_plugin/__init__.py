r"""IT Data routines for airflow dag and plugins.

Useful cases:
  - send custom alerte to technical It Data team
  - stores a variable in Google Cloud Secret Manager.

"""
import json
import logging
import os
import base64

from airflow.utils.email import send_email
from google.cloud import secretmanager
from google.api_core.exceptions import PermissionDenied


env = os.environ.get("ENV")
location = "europe-west1"
default_project_id = os.environ.get("GCP_PROJECT_ID")

def send_technical_alerte(title, html_content, recipient=None, cc=None):
    """
    Sends a technical alert email to the IT Data team.

    Args:
        title (str): The title of the email.
        html_content (str): The HTML content of the email body.
        recipient (str, optional): The email address of the recipient.
            Defaults to '<EMAIL>' for production,
            '<EMAIL>' tech email otherwise.
        cc (list, optional): A list of email addresses to be added in CC.
            Defaults to None.

    Returns:
        None
    """
    if not recipient:
        recipient = '<EMAIL>' if env.lower() == 'prod' else "<EMAIL>"

    title = '[AIRFLOW - {env}] {title}'.format(env=env, title=title)
    footer = ('<br/>'
              'Cordialement, <br/>'
              'Mozart - {env} <br/>').format(env=env, title=title)
    send_email(to=recipient, subject=title, html_content=html_content + footer, cc=cc,
               mime_charset='utf-8', conn_id='sendgrid_default')

def set_variable_in_secret_manager(secret_name, secret_value, serialize_json=False, pretty_print=True):
    """
    Stores a variable in Google Cloud Secret Manager.

    The secret name will be prefixed with 'airflow-variables-' to follow
    Airflow's convention for Secret Manager.

    Args:
        secret_name (str): The name of the secret (variable).
        secret_value (str): The value of the secret.
        project_id (str, optional): The Google Cloud project ID.
            If not provided, it defaults to the value of the
            GCP_PROJECT_ID environment variable.
        location (str, optional): The Google Cloud region where the secret will be stored.
            Defaults to "europe-west1".
        serialize_json (bool, optional): Whether to serialize the secret_value to JSON.
            Defaults to False.
        pretty_print (bool, optional): Whether to format the JSON with indentation and sorted keys.
            Only applies when serialize_json is True. Defaults to True.
    Returns:
        str: A message indicating that the variable has been stored in Secret Manager.
    """
    client = secretmanager.SecretManagerServiceClient()
    project_id = os.environ.get("GCP_PROJECT_ID")

    # Nom du secret suivant la convention d'Airflow pour Secret Manager
    var_secret_name = f"airflow-variables-{secret_name}"
    parent = f"projects/{project_id}"
    default_labels = {"source": "itdata", "type": "variables", "used_by": "airflow"}

    # Handle JSON serialization if requested
    if serialize_json:
        # Set JSON formatting options based on pretty_print parameter
        json_kwargs = {}
        if pretty_print:
            json_kwargs = {'indent': 2, 'sort_keys': True}
            logging.info("Using pretty print formatting for JSON")

        # If it's already a string and looks like JSON, parse and reformat it
        if isinstance(secret_value, str) and (secret_value.startswith('{') or secret_value.startswith('[')):
            try:
                # Parse the JSON string to a Python object
                parsed_json = json.loads(secret_value)
                # Re-serialize with formatting options
                secret_value = json.dumps(parsed_json, **json_kwargs)
                logging.info("Reformatted JSON string")
            except json.JSONDecodeError:
                # It's not valid JSON, treat it as a regular string
                logging.info("String is not valid JSON, treating as regular string")
                # Don't serialize strings that aren't JSON to avoid adding quotes
                if not isinstance(secret_value, str):
                    secret_value = str(secret_value)
        else:
            # It's not a string that looks like JSON, check if it's a dict or list
            if isinstance(secret_value, (dict, list)):
                # It's a JSON-serializable object, serialize it
                logging.info("Serializing object to JSON")
                secret_value = json.dumps(secret_value, **json_kwargs)
            else:
                # It's not JSON-serializable, just convert to string
                logging.info("Converting non-JSON value to string")
                secret_value = str(secret_value)
    else:
        # For non-JSON values, ensure it's a string
        secret_value = str(secret_value)


    secret_path = f"{parent}/secrets/{var_secret_name}"

    logging.info(f'Adding version to variable={secret_path}')
    try:
        # Check if the secret exists
        client.get_secret(request={"name": secret_path})
        secret_exist = True
    except Exception as e:
        if "not found" in str(e):
            secret_exist = False
        else:
            error_message = f"Error while checking secret '{var_secret_name}' in project '{project_id}': {e}"
            logging.error(error_message)
            return error_message

    logging.info(f'Secret exist: {secret_exist}')
    # Create the secret if it doesn't exist
    if not secret_exist:
        # Create the secret with managed replication and labels
        try:
            secret = client.create_secret(
                request={
                    "parent": parent,
                    "secret_id": var_secret_name,
                    "secret": {
                        "replication": {
                            "user_managed": {
                                "replicas": [{"location": location}],
                            }
                        },
                        "labels": default_labels if default_labels else {},  # Add labels here
                    },
                }
            )
            logging.info(f"Secret '{secret.name}' created with replication in {location} and labels.")
        except PermissionDenied as e:
            error_message = f"Permission denied while creating secret '{var_secret_name}' in project '{project_id}': {e}"
            logging.error(error_message)
            return error_message
        except Exception as e:
            error_message = f"Error while creating secret '{var_secret_name}' in project '{project_id}': {e}"
            logging.error(error_message)
            return error_message

    try:
        # Add a version to the secret
        version = client.add_secret_version(
            request={
                "parent": secret_path,
                "payload": {"data": secret_value.encode("utf-8")},
            }
        )
        logging.info(f"Version of secret '{version.name}' added.")

    except PermissionDenied as e:
        error_message = f"Permission denied while adding version to secret '{var_secret_name}' in project '{project_id}': {e}"
        logging.error(error_message)
        return error_message
    except Exception as e:
        error_message = f"Error while adding version to secret '{var_secret_name}': {e}"
        logging.error(error_message)
        return error_message

    return f"Variable {var_secret_name} updated successfully in secret manager."