from airflow.models import BaseOperator
from airflow.providers.mongo.hooks.mongo import MongoHook
from dateutil.parser import isoparse


class MongoDeleteOperator(BaseOperator):
    """
    Delete documents from mongo collection

    :param mongo_conn_id: The connection ID used to connect to MongoDB
    :type mongo_conn_id: str
    :param mongo_collection: The collection to query
    :type mongo_collection: str
    :param mongo_query: The query
    :param mongo_query: dict or list
    :param mongo_db: The mongo Database to use
    :type mongo_db: str
    :param delete_many: Do you want to delete many document ?
    :type delete_many: bool
    :param datetime_query_fields: List of path to query field to cast from string to datetime. This is usefull when
        you use macros for date filters. Pymongo need datetime and not string.
    :type datetime_query_fields: list
    """

    template_fields = ['mongo_query']

    def __init__(self,
                 mongo_conn_id,
                 mongo_collection,
                 mongo_query,
                 mongo_db=None,
                 delete_many=False,
                 datetime_query_fields=[],
                 *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.delete_many = delete_many

        # Conn Ids
        self.mongo_conn_id = mongo_conn_id
        # Mongo Query Settings
        self.mongo_db = mongo_db
        self.mongo_collection = mongo_collection
        # Grab query and determine if we need to run an aggregate pipeline
        self.mongo_query = mongo_query
        self.is_pipeline = True if isinstance(self.mongo_query, list) else False
        self.datetime_query_fields = datetime_query_fields

        if self.is_pipeline:
            raise ValueError("You must provide a simple filter, not an aggregate")

    def execute(self, context):
        self._prepare_query()
        self.log.info("Mongo delete filter : {}".format(str(self.mongo_query)))

        collection = MongoHook(self.mongo_conn_id).get_collection(
            self.mongo_collection,
            mongo_db=self.mongo_db
        )

        if self.delete_many:
            result = collection.delete_many(self.mongo_query)
        else:
            result = collection.delete_one(self.mongo_query)

        self.log.info("Number of deleted documents = {}".format(result.deleted_count))

    def _cast_to_datetime(self, key_path):
        """
        Cast string to datetime for asked fields
        """
        query_ptr = self.mongo_query

        for key in key_path[:-1]:
            try:
                query_ptr = query_ptr[key]
            except ValueError:
                raise ValueError("field '{}' does not exists in query".format('.'.join(key_path)))

        if not isinstance(query_ptr[key_path[-1]], str):
            raise ValueError("query field '{}' is not a string".format('.'.join(key_path)))

        query_ptr[key_path[-1]] = isoparse(query_ptr[key_path[-1]])

    def _prepare_query(self):
        """
        Cast query fields from string to datetime
        """
        for key_path in self.datetime_query_fields:
            self._cast_to_datetime(key_path)

        return self.mongo_query
