from airflow.models import BaseOperator
from google.cloud import bigquery
from concurrent.futures import ThreadPoolExecutor, as_completed


class ComputeSegmentWebsiteVisitorsOperator(BaseOperator):
    template_fields = ('bq_project', 'bq_project_userhub', 'bigquery_conn_id',
                       'universe_type', 'period',)

    def __init__(self,
                 delegate_to=None,
                 bq_project='',
                 bq_project_userhub='',
                 bigquery_conn_id='bq_matrix',
                 brand_trigram='',
                 universe_type='',
                 period='',
                 *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.bq_project = bq_project
        self.bq_project_userhub = bq_project_userhub
        self.bigquery_conn_id = bigquery_conn_id
        self.universe_type = universe_type
        self.period = period
        # init BQ Client
        self.client = bigquery.Client(project=self.bq_project)

    def get_universe_brand(self):
        client = self.client
        bq_project = self.bq_project
        universe_type = self.universe_type
        universe_brand_query = """
            SELECT
                ueb.brand_trigram,
                ARRAY_AGG(DISTINCT ueb.universe_name)  AS universe_names,
                ARRAY_AGG(DISTINCT ueb.universe_id)    AS universe_ids
            FROM `{bq_project}.refined_data.universe_email_base`  AS ueb
            JOIN `{bq_project}.refined_data.email_base`           AS eb
                ON eb.consent_public_ref = ueb.email_consent_public_ref
            JOIN `{bq_project}.store_karinto.brand` AS kb
                ON  kb.is_active
                AND kb.id = eb.brand_id
            WHERE eb.consent_type IN UNNEST({universe_type})
            GROUP BY 1;
        """.format(bq_project=bq_project, universe_type=universe_type)
        self.log.info("{universe_brand_query}".format(universe_brand_query=universe_brand_query))
        universe_brand = client.query(universe_brand_query, location="EU")
        return universe_brand

    def generate_segment_query(self, brand_trigram, period):
        bq_project = self.bq_project
        bq_project_userhub = self.bq_project_userhub
        brand_trigram_lower = brand_trigram.lower()


        compute_segment_query = """
            -- Create segment table
            CREATE TABLE IF NOT EXISTS `{bq_project}.store_email_segment.segment-sha256_web_activity_{brand_trigram_lower}_{period}_days` (
                email_sha256     STRING     NOT NULL    OPTIONS(description="Email SHA256"),
                update_date      DATE       NOT NULL    OPTIONS(description="Update date")
            )
            OPTIONS(description="Web activity on {brand_trigram}'s website in last {period} days. DAG: {{ dag.dag_id }}");
            
            -- Delete all profiles
            TRUNCATE TABLE `{bq_project}.store_email_segment.segment-sha256_web_activity_{brand_trigram_lower}_{period}_days`;
            
            -- Insert profiles into segment table
            INSERT INTO `{bq_project}.store_email_segment.segment-sha256_web_activity_{brand_trigram_lower}_{period}_days`
            -- {period} day visitors.
            SELECT DISTINCT
              p360.id.email_sha256,
              CURRENT_DATE() AS update_date
            FROM `{bq_project_userhub}.generated_data.pmc_profile_journey` AS ppj , UNNEST(activity) AS a
            JOIN `{bq_project}.business_data.profile_digital_360` AS p360 ON p360.id.pmc_uuid = ppj.pmc_uuid
            WHERE
              -- activity on the last {period} days.
              a.date >= DATE_SUB(CURRENT_DATE (), INTERVAL {period} DAY)
              AND
              -- ON "{brand_trigram}" brand.
              a.brand_trigram = "{brand_trigram}";


        """.format(bq_project=bq_project, bq_project_userhub=bq_project_userhub,
                   brand_trigram_lower=brand_trigram_lower, brand_trigram=brand_trigram,
                   period=period)
        self.log.info("{compute_segment_query}".format(compute_segment_query=compute_segment_query))
        return compute_segment_query

    def generate_tag_query(self, brand_trigram, period):
        bq_project = self.bq_project
        universe_type = self.universe_type
        brand_trigram_lower = brand_trigram.lower()
        tag_query = """
            INSERT INTO `{bq_project}.store_email_segment.tag_universe`
                WITH new_tag AS (
                    SELECT
                        CONCAT("web_activity_","{brand_trigram_lower}","_","{period}","_days") AS tag,
                        [ub.universe_id],
                        [ub.universe_name]
                    FROM `{bq_project}.store_karinto.universe_brand` AS ub   
                    WHERE universe_type IN UNNEST({universe_type})
                        AND ub.brand_trigram = "{brand_trigram}" 
                ), tag_universe AS (
                      SELECT *, 1 AS check
                      FROM `{bq_project}.store_email_segment.tag_universe`
                )
                SELECT nt.*
                FROM new_tag AS nt
                LEFT JOIN tag_universe AS tu USING(tag)
                WHERE tu.check IS NULL;
        """.format(bq_project=bq_project,
                   brand_trigram_lower=brand_trigram_lower, brand_trigram=brand_trigram,
                   period=period, universe_type=universe_type)
        self.log.info("{tag_query}".format(tag_query=tag_query))
        return tag_query

    def generate_thread_for_segment(self):
        universe_brand = self.get_universe_brand()
        thread_segment_query = []
        thread_segment_query = [
            self.generate_segment_query(brand_trigram=row["brand_trigram"], period=_period)
            for _period in self.period
            for row in universe_brand.result()
        ]
        return thread_segment_query

    def generate_thread_for_tag(self):
        universe_brand = self.get_universe_brand()
        thread_tag_query = [
            self.generate_tag_query(brand_trigram=row["brand_trigram"], period=_period)
            for _period in self.period
            for row in universe_brand.result()
        ]
        return thread_tag_query

# Run all threads [drop, create, insert, tag]
    def run_threads(self):
        client = self.client
        executor = ThreadPoolExecutor(5)

        # Build list of queries -> by brand, one for segment and another for the tag
        queries = []

        # Compute queries for segment tables
        queries_segment = self.generate_thread_for_segment()

        # Compute queries for tags
        queries_tag = self.generate_thread_for_tag()

        # Concatenate queries lists into a unique list
        queries += queries_segment
        queries += queries_tag

        threads = []
        results = []
        threads = [
            executor.submit(job.result)
            for job in [
                client.query(query, location="EU")
                for query in queries
            ]
        ]
        results = [
            list(future.result())
            for future in as_completed(threads)
        ]

        return results

#    def execute(self): # for test
    def execute(self, context):
        exec_result = self.run_threads()
        return exec_result



#if __name__ == '__main__':
#    period = [90, 120]
#    activity_type = ['openers']
#    activity_universe = ['nl']
#    test = ComputeSegmentActiveUsersOperator()
#    test.period = period
#    test.activity_type = activity_type
#    test.execute()
