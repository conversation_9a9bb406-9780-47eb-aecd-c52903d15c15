# -*- coding: utf-8 -*-
#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

import os

from airflow.models import BaseOperator
from airflow.providers.ftp.hooks.ftp import FTPHook
from airflow.providers.google.cloud.hooks.gcs import _parse_gcs_url


class EdgeweareCleanOperator(BaseOperator):

    def __init__(self,
                 clean_files_task_id,
                 ftp_conn_id='ftp_default',
                 ftp_path='/TREATED/',
                 delegate_to=None,
                 *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.clean_files_task_id = clean_files_task_id

        self.ftp_conn_id = ftp_conn_id
        self.ftp_path = ftp_path
        self.delegate_to = delegate_to

    def execute(self, context):
        cleaned_files = []

        # init ftp
        ftp_hook = FTPHook(ftp_conn_id=self.ftp_conn_id)

        # get files from the xcom
        ftp_files = context['ti'].xcom_pull(task_ids=self.clean_files_task_id)

        # get gcs file to local
        local_files = {}
        local_fds = []
        for file in ftp_files:
            object = _parse_gcs_url(file)[1]

            head, input_fname = os.path.split(object)

            ftp_hook.rename(os.path.join('IN', input_fname), os.path.join(self.ftp_path, input_fname))
            cleaned_files.append(os.path.join(self.ftp_path, input_fname))

        return cleaned_files
