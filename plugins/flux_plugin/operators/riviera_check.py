# -*- coding: utf-8 -*-
#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

import csv
import hashlib
import io
import os
import re

from airflow.models import BaseOperator
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from airflow.providers.postgres.hooks.postgres import PostgresHook


open_options = {
    'newline': '',
    'encoding': 'utf-8'
}
csv_options = {
    'delimiter': ';',
    'quotechar': '"',
    'quoting': csv.QUOTE_MINIMAL
}

pandas_options = {
    'sep': ';',
    'index_col': 'email_md5',
    'usecols': ['email_md5', 'email_sha256', 'unsubscribe_date', 'unsubscribe_type', 'consent_id'],
    'parse_dates': ['unsubscribe_date']
}

"""
The role of the operator is to pepare to purge all profiles sent by webrivage 
and then save aggreagated files in one of our storage to prepare insertion on event queue

:param gcs_conn_id: the id of google cloud storage connection
:type gcs_conn_id: string

:param gcs_bucket: the bucket where we will store enriched files
:type gcs_bucket: string

:param gcs_object_path: the path of files ( objects ) will be stored on bucket
:type gcs_object_path: string



"""


class RivieraCheckOperator(BaseOperator):
    template_fields = ['gcs_object_path', 'gcs_bucket_prefix', 'matrix_tmp_table']

    
    def __init__(self,
                 gcs_bucket,
                 gcs_bucket_prefix,
                 gcs_delimiter,
                 gcs_conn_id='gcs_default',
                 postgres_conn_id='postgres_default',
                 gcs_object_path=None,
                 matrix_tmp_table=None,
                 delegate_to=None,
                 *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.gcs_bucket = gcs_bucket
        self.gcs_bucket_prefix = gcs_bucket_prefix
        self.gcs_delimiter = gcs_delimiter
        self.postgres_conn_id = postgres_conn_id
        self.matrix_tmp_table = matrix_tmp_table
        self.gcs_conn_id = gcs_conn_id
        self.gcs_object_path = gcs_object_path
        # xcom from the previous task []
        self.delegate_to = delegate_to

    def execute(self, context):
        files_to_prepare = []

        # init gcs hook
        gcs_hook = GCSHook(
            gcp_conn_id=self.gcs_conn_id,
            delegate_to=self.delegate_to)
        # init postgresHook
        pg_sql = PostgresHook(postgres_conn_id=self.postgres_conn_id, database='matrix')

        gcs_files = gcs_hook.list(bucket_name=self.gcs_bucket,
                                  prefix=self.gcs_bucket_prefix,
                                  delimiter=self.gcs_delimiter)

        bucket = self.gcs_bucket
        target_columns = ['email_md5', 'unsub_date', 'unsub_type', 'consent']
        rows_to_insert = []

        for gcs_file in gcs_files:
            self.log.info('The GCS filename is %s', gcs_file)
            # get file from gcs ( type returned : Bytes )
            body = gcs_hook.download(bucket, gcs_file)
            # convert / unicode Bytes returned files to StringIO
            body_uncoded = io.StringIO(body.decode(open_options['encoding']))

            # parse the consent public ref from the filename
            file_to_insert = os.path.basename(gcs_file)
            consent = re.search('unsubs_(.+?)_([0-9]+)', file_to_insert)

            # parse CSV from the StringIO
            csv_content = csv.DictReader(body_uncoded, **csv_options)
            """
               source file format:
               - email 
               - unsubDate
               - unsubType
               - idCampaign
               - campaignName
               """

            for line in csv_content:
                email_clear = line['email'].encode()
                result = hashlib.md5(email_clear)
                # fields
                email_md5 = result.hexdigest()
                unsub_date = line['unsubDate']
                unsub_type = line['unsubType']
                public_ref = consent.group(1)

                rows_to_insert.append([email_md5, unsub_date, unsub_type, public_ref])

        # bulk insert into table
        pg_sql.insert_rows(self.matrix_tmp_table, rows_to_insert, target_columns)

        return True

    """
    Uploads a local temp file to Google Cloud Storage.

    input : params ( gcs , file name to return , gcs_hook handler ... ) 
            csv_content : got the return from the panda/df 

    return : true or false ( object stored OR not to the gcs )         
    """

    def save_to_gcs(self, **kwargs):
        csv_content = kwargs['csv_content']
        gcs = kwargs['params']['gcs']
        try:
            from tempfile import NamedTemporaryFile

            with NamedTemporaryFile(mode="w+") as tmpfile:
                tmpfile.write(csv_content)
                self.log.info('The temp filename is %s', tmpfile.name)
                bucket = self.gcs_bucket
                object = self.gcs_object_path
                # flush to force close file
                tmpfile.flush()
                sent_to_gcs = gcs.upload(bucket_name=bucket, object_name=object, filename=tmpfile.name, mime_type="text/csv")
                if sent_to_gcs:
                    return True
        except:
            return False
