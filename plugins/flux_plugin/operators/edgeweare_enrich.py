# -*- coding: utf-8 -*-
#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

import csv
import io
import os

import pandas as pd
from airflow.models import BaseOperator
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from airflow.providers.google.cloud.hooks.gcs import _parse_gcs_url
from airflow.providers.postgres.hooks.postgres import PostgresHook

# @fixme : the schema doesn't match the query, the Ville field is suppored to go last
templateFields = {

    'adres':
        "SELECT civility as civilite, firstname as prenom, lastname as nom, "
        "regexp_replace(addr.city, E'[\\n\\r\\f\\u000B\\u0085\\u2028\\u2029]+', ' ', 'g' ) as Ville, "
        "regexp_replace(addr.rnvp_2, E'[\\n\\r\\f\\u000B\\u0085\\u2028\\u2029]+', ' ', 'g' ) as V2, "
        "regexp_replace(addr.rnvp_3, E'[\\n\\r\\f\\u000B\\u0085\\u2028\\u2029]+', ' ', 'g' ) as V3, "
        "regexp_replace(addr.rnvp_4, E'[\\n\\r\\f\\u000B\\u0085\\u2028\\u2029]+', ' ', 'g' ) as V4, "
        "regexp_replace(addr.rnvp_5, E'[\\n\\r\\f\\u000B\\u0085\\u2028\\u2029]+', ' ', 'g' ) as V5, "
        "addr.zipcode as CP "
        "FROM matrix__postal.profile "
        "INNER JOIN matrix__postal.address as addr"
        " ON addr.profile_master_id = matrix__postal.profile.profile_master_id "
        "WHERE matrix__postal.profile.profile_master_id IN "
    ,

    'email':
        "SELECT  gender as civilite, firstname as prenom, lastname as nom, master.email as email, kec.public_ref as header "
        "FROM matrix__email.consolidated_profile "
        "JOIN matrix__email.profile_master_id master  "
        "ON master.id = matrix__email.consolidated_profile.profile_master_id " \
        "JOIN matrix__email.profiles_email_consents pec " \
        "ON pec.profile_master_id = matrix__email.consolidated_profile.profile_master_id  "
        "JOIN karinto.email_consent AS kec ON pec.email_consent_id = kec.id " \
        "WHERE kec.public_id IS NOT NULL AND master.id  IN "

}

open_options = {
    'newline': '',
    'encoding': 'utf-8'
}
csv_options = {
    'delimiter': ';',
    'quotechar': '"',
    'quoting': csv.QUOTE_MINIMAL
}

"""
The role of the operator is to find the records in the database that 
correspond to the request for enhancement and then save it in one of our storage.

:param sql_conn_id: the id of postgres connection
:type sql_conn_id: string

:param gcs_conn_id: the id of google cloud storage connection
:type gcs_conn_id: string

:param gcs_bucket: the bucket where we will store enriched files
:type gcs_bucket: string

:param gcs_object_path: the path of files ( objects ) will be stored on bucket
:type gcs_object_path: string
"""


class EdgeweareEnrichOperator(BaseOperator):

    def __init__(self,
                 enrich_files_task_id,
                 sql_conn_id='postgres_default',
                 gcs_conn_id='google_cloud_default',
                 gcs_bucket='',
                 gcs_object_path='',
                 delegate_to=None,
                 *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.sql_conn_id = sql_conn_id
        self.enrich_files_task_id = enrich_files_task_id

        self.gcs_conn_id = gcs_conn_id
        self.gcs_bucket = gcs_bucket
        self.gcs_object_path = gcs_object_path
        self.delegate_to = delegate_to

    def execute(self, context):
        enriched_files = []

        # init gcs hook
        gcs_hook = GCSHook(
            gcp_conn_id=self.gcs_conn_id,
            delegate_to=self.delegate_to)
        # init postgresHook
        pg_sql = PostgresHook(postgres_conn_id=self.sql_conn_id, database='matrix')
        # params to send to static func
        params = {
            'sql': pg_sql,
            'gcs': gcs_hook
        }

        # get files from the xcom
        gcs_files = context['ti'].xcom_pull(task_ids=self.enrich_files_task_id)

        for file in gcs_files:
            bucket = _parse_gcs_url(file)[0]
            object = _parse_gcs_url(file)[1]

            head, input_fname = os.path.split(object)
            params['return_filename'] = "retour_" + input_fname

            if gcs_hook.exists(bucket, object):
                # get file from gcs ( type returned : Bytes )
                body = gcs_hook.download(bucket, object)
                # convert / unicode Bytes returned files to StringIO
                body_uncoded = io.StringIO(body.decode(open_options['encoding']))
                # parse CSV from the StringIO
                csv_content = csv.reader(body_uncoded, **csv_options)
                headers = next(csv_content)
                # check if the file is for addres enrichissement or else:

                if "_adres_" in object:
                    store_file = self.process_adres(csv_content, params)
                    enriched_files.append('gs://{}/{}{}'.format(
                        self.gcs_bucket,
                        self.gcs_object_path,
                        params['return_filename']
                    ))

                if "_email_" in object:
                    store_file = self.process_email(csv_content, params)
                    enriched_files.append('gs://{}/{}{}'.format(
                        self.gcs_bucket,
                        self.gcs_object_path,
                        params['return_filename']
                    ))

            print(enriched_files)

        return enriched_files

    """
    Process the addresses enrichissement operation 
    """

    def process_adres(self, body, params):
        pg_sql = params['sql']
        # init DF
        retour_postal = pd.DataFrame(
            columns=['IdString', 'repoName', 'idRepo', 'civilite', 'nom', 'prenom', 'V2', 'V3', 'V4', 'V5', 'CP',
                     'Ville']
        )

        for row in body:

            query = "SELECT id FROM matrix__postal.profile_master_id WHERE customer_ref='{hash}'".format(hash=row[2])
            if len(pg_sql.get_records(query)) > 0:
                self.log.info('we found % records can be enriched ', len(pg_sql.get_records(query)))
                full_query = "{global_query} ({query_part})".format(query_part=query,
                                                                    global_query=templateFields['adres'])

                postal = pg_sql.get_first(full_query)
                if postal is not None:
                    retour_postal = retour_postal.append({'IdString': row[0],
                                                          'repoName': row[1],
                                                          'idRepo': row[2],
                                                          'civilite': postal[0],
                                                          'nom': postal[1],
                                                          'prenom': postal[2],
                                                          'V2': postal[3],
                                                          'V3': postal[4],
                                                          'V4': postal[5],
                                                          'V5': postal[6],
                                                          'CP': postal[7],
                                                          'Ville': postal[8]}, ignore_index=True)

        csv_content = retour_postal.to_csv(index=False, sep=';')
        return self.save_to_gcs(params=params, csv_content=csv_content)

    """
    Process the email enrichissement operation 
    """

    def process_email(self, body, params):
        pg_sql = params['sql']
        # init DF
        retour_email = pd.DataFrame(
            columns=['IdString', 'repoName', 'idRepo', 'civilite', 'nom', 'prenom', 'header', 'optin', 'email']
        )
        for row in body:
            query = "SELECT id FROM matrix__email.profile_master_id WHERE email_md5='{hash}'".format(hash=row[2])
            if len(pg_sql.get_records(query)) > 0:
                self.log.info('we found % records can be enriched ', len(pg_sql.get_records(query)))
                full_query = "{global_query} ({query_part}) AND pec.status='sub'" \
                             " ORDER BY pec.update_date DESC " \
                             "LIMIT 1 ".format(query_part=query, global_query=templateFields['email'])

                email = pg_sql.get_first(full_query)
                if email is not None:
                    retour_email = retour_email._append({'IdString': row[0],
                                                        'repoName': row[1],
                                                        'idRepo': row[2],
                                                        'civilite': email[0],
                                                        'nom': email[2],
                                                        'prenom': email[1],
                                                        'header': email[4],
                                                        'optin': 1,
                                                        'email': email[3]}, ignore_index=True)

        csv_content = retour_email.to_csv(index=False, sep=';')
        return self.save_to_gcs(params=params, csv_content=csv_content)

    """
    Uploads a local temp file to Google Cloud Storage.
    
    input : params ( gcs , file name to return , gcs_hook handler ... ) 
            csv_content : got the return from the panda/df 
    
    return : true or false ( object stored OR not to the gcs )         
    """

    def save_to_gcs(self, **kwargs):
        params = kwargs['params']
        csv_content = kwargs['csv_content']
        gcs = kwargs['params']['gcs']
        try:
            from tempfile import NamedTemporaryFile

            with NamedTemporaryFile(mode="w+") as tmpfile:
                tmpfile.write(csv_content)
                self.log.info('The temp filename is %s', tmpfile.name)
                bucket = self.gcs_bucket
                object = self.gcs_object_path + params['return_filename']
                # flush to force close file
                tmpfile.flush()
                sent_to_gcs = gcs.upload(bucket_name=bucket, object_name=object, filename=tmpfile.name,
                                         mime_type="text/csv")
                if sent_to_gcs:
                    return True
        except:
            return False
