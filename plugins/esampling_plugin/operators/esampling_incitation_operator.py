# gsutil cp plugins/esampling_plugin/operators/esampling_incitation_operator.py gs://europe-west1-mozart-cluster-2e910a39-bucket/plugins/esampling_plugin/operators/

import hashlib
import json
from datetime import datetime

import requests
from airflow.exceptions import AirflowException
from airflow.models import BaseOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook


class EsamplingIncitationOperator(BaseOperator):
    """
        Send esampling email to qualifio contact
            & update status using sql query

        :param schema: name of the schema
        :type schema: string
        :param table: name of the table to dump
        :type table: bases
        :param universes: universe list with their ids
        :type universes: dict
        :param universes_sftp: universe list with SFTP conn_id list
        :type universes_sftp: dict
        :param bucket: The Google cloud storage bucket where the object is. (templated)
        :type bucket: string
        :param object: The name of the object to upload in the Google cloud
            storage bucket. (templated)
        :type object: string
        :param postgres_conn_id: reference to a specific postgres database
        :type postgres_conn_id: string
        :param gcp_conn_id: (Optional) The connection ID used to connect to Google Cloud Platform.
        :type gcp_conn_id: str
    """

    template_fields = ('sql_update_contact_status', 'sql_get_qualifio_contacts',
                       'sql_insert_contact_incitation', 'sql_get_active_campaigns')
    template_ext = ('.sql')
    ui_color = '#ededed'

    def __init__(
            self,
            database,
            sql_get_qualifio_contacts,
            sql_insert_contact_incitation,
            template_name,
            sql_get_active_campaigns='0_get_active_campaigns.sql',
            esampling_conn_id='psql_esampling_app',
            gcp_conn_id='google_cloud_default',
            tmail_api_uri='https://tmail.preprod.prismadata.fr',
            tmail_api_end_point='/api/email',
            tmail_api_identity='prisma-connect',
            tmail_api_secret='Pr1sm4-C0nn3ct.',
            sql_update_contact_status=None,
            campaign_public_refs_ignored=[],
            *args, **kwargs):
        super(EsamplingIncitationOperator, self).__init__(*args, **kwargs)

        # conn id psql
        self.gcp_conn_id = gcp_conn_id
        self.esampling_conn_id = esampling_conn_id
        self.tmail_api_uri = tmail_api_uri.rstrip('/')
        self.tmail_api_end_point = tmail_api_end_point.rstrip('/')
        self.tmail_api_identity = tmail_api_identity
        self.tmail_api_secret = tmail_api_secret
        self.template_name = template_name
        # esampling campaign
        self.database = database
        # sql query
        self.sql_update_contact_status = sql_update_contact_status
        self.sql_get_qualifio_contacts = sql_get_qualifio_contacts
        self.sql_insert_contact_incitation = sql_insert_contact_incitation
        self.sql_get_active_campaigns = sql_get_active_campaigns
        self.campaign_public_refs_ignored = campaign_public_refs_ignored

        self.pg_hook = PostgresHook(
            postgres_conn_id=self.esampling_conn_id, database=self.database)

    def execute(self, context):
        campaigns = self.get_active_campaigns()
        for campaign_id, campaign_data in campaigns.items():
            public_ref = campaign_data.get('public_ref')
            template_tmail_name = campaign_data['campaign_data'][self.template_name]['template']

            if public_ref in self.campaign_public_refs_ignored:
                continue
            # qualifio_campaign_ref = campaign_data.get('qualifio_campaign_ref')
            qualifio_profiles_query = self.sql_get_qualifio_contacts.format(
                public_ref=public_ref,
                template_name=self.template_name,
                template_tmail_name=template_tmail_name)
            self.log.info('sql_qualifio_profiles_query {} '.format(str(qualifio_profiles_query)))
            result_emails = self.pg_hook.get_records(
                qualifio_profiles_query, parameters=self.params)
            for output in self.pg_hook.conn.notices:
                self.log.info(output)
            self.log.info('result_emails {} '.format(str(result_emails)))
            list_emails = json.dumps(
                result_emails, separators=(',', ':'), default=str)
            self.log.info('list_emails {} '.format(str(list_emails)))
            for email in result_emails:
                # prepare email for api
                email_format = {
                    "firstname": email[0] or "",
                    "lastname": email[1] or "",
                    "address1": email[2] or "",
                    "address2": email[3] or "",
                    "address3": email[4] or "",
                    "postal_code": email[5] or "",
                    "city": email[6] or "",
                    "email": email[7] or "",
                    "recipient": email[7] or "",
                    "qualifio_ref": email[8] or ""
                }
                self.log.info('email_data {} '.format(str(email_format)))

                email_data = self.get_template_from_qualifio_data(email_format, campaign_data, public_ref)

                response_tmail = self.send_email(email_data)
                if response_tmail.status_code == 200:
                    self.add_contact_incitation_logs(public_ref, email_format['qualifio_ref'], self.template_name,
                                                     template_tmail_name)
                    self.update_contact_status(public_ref, email_format['qualifio_ref'])

                # output errors
                for output in self.pg_hook.conn.notices:
                    self.log.info(output)

        self.log.info("Success")
        return []

    def get_active_campaigns(self):
        all_campaigns = self.pg_hook.get_records(self.sql_get_active_campaigns, self.params)
        campaigns = all_campaigns[0][0]

        self.log.info('campaign = {}'.format(str(campaigns)))
        return json.loads(campaigns)

    def get_template_from_qualifio_data(self, email_data, campaign_infos, public_ref):
        data = {**email_data, **campaign_infos, **campaign_infos['campaign_data']}
        email_template_config = campaign_infos['campaign_data'][self.template_name]
        if email_template_config is None:
            raise AirflowException(
                '[Incitation Operator] Template not found for campaign with public_ref= {public_ref} '
                + ' in [campaign_data -> {template_name}] '.format(
                    public_ref=public_ref,
                    template_name=self.template_name))

        tmail_template_name = email_template_config['template']
        tmail_template_vars = "{%s}" % json.dumps(email_template_config['vars'])
        email_template = tmail_template_vars.format(**data)
        email_template_obj = json.loads(email_template)

        email = {
            "type": "template",
            "template_name": tmail_template_name,
            "tracking_key": "esampling",
            "mode": "email_transac",
            "tags": "esampling",
        }
        email.update({"recipient": email_data['email']})
        email.update({"vars": email_template_obj})
        return json.dumps(email)

    def send_email(self, email):
        tmail_host = self.tmail_api_uri + self.tmail_api_end_point
        self.log.info('[Tmail Api] Using Host: {}'.format(tmail_host))
        date = datetime.now()
        atomDate = date.strftime('%Y-%m-%dT%H:%M:00')
        self.log.info('[Tmail Api] email data = {} '.format(str(email)))
        toHash = '|' + atomDate + '|' + 'POST' + '|' + \
                 self.tmail_api_end_point + '|' + \
                 self.tmail_api_secret + '|' + str(email) + '|'
        x_auth_token = hashlib.sha256(toHash.encode('utf-8')).hexdigest()
        headers = {
            'Content-Type': 'application/json',
            'X-Auth-Token': '%s' % x_auth_token,
            'X-Auth-identity': '%s' % self.tmail_api_identity
        }
        response = requests.request(
            "POST", tmail_host, headers=headers, data=email)

        self.log.info('[Tmail Api] Response Status Code: {}'.format(response.status_code))
        if response.status_code != 200:
            raise AirflowException('[Tmail Api] Response Error: {}'.format(response.text))

        return response

    def add_contact_incitation_logs(self, public_ref, qualifio_ref, template_name, template_tmail_name):
        incitation_info = self.sql_insert_contact_incitation.format(
            public_ref=public_ref,
            qualifio_ref=qualifio_ref,
            template_name=template_name,
            template_tmail_name=template_tmail_name
        )
        self.log.info('sql_incitation_info: {} '.format(incitation_info))
        self.pg_hook.run(incitation_info)
        self.log.info('incitation logs successfully updated')

    def update_contact_status(self, public_ref, qualifio_ref):
        if self.sql_update_contact_status is None:
            self.log.info("No update to do !! All OK: ")
            return True
        else:
            update_query = self.sql_update_contact_status.format(
                public_ref=public_ref,
                qualifio_ref=qualifio_ref)
            self.log.info('sql_update_query  {} '.format(update_query))

            self.pg_hook.run(update_query)
            self.log.info('contact status successfully updated')
