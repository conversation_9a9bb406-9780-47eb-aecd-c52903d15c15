# gsutil cp plugins/esampling_plugin/operators/esampling_import_processed_files_operator.py gs://europe-west1-mozart-cluster-2e910a39-bucket/plugins/esampling_plugin/operators/
# gsutil cp plugins/esampling.py gs://europe-west1-mozart-cluster-2e910a39-bucket/plugins/

import csv
import json
import os
import re
import tempfile

import pandas as pd
from airflow.models import BaseOperator
from airflow.operators.email import EmailOperator
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from airflow.providers.postgres.hooks.postgres import PostgresHook


class EsamplingImportProcessedFilesOperator(BaseOperator):
    """
        Import files processed by holy sampling from sftp import bucket

        :param schema: name of the schema
        :type schema: string
        :param table: name of the table to dump
        :type table: bases
        :param universes: universe list with their ids
        :type universes: dict
        :param universes_sftp: universe list with SFTP conn_id list
        :type universes_sftp: dict
        :param bucket: The Google cloud storage bucket where the object is. (templated)
        :type bucket: string
        :param object: The name of the object to upload in the Google cloud
            storage bucket. (templated)
        :type object: string
        :param postgres_conn_id: reference to a specific postgres database
        :type postgres_conn_id: string
        :param gcp_conn_id: (Optional) The connection ID used to connect to Google Cloud Platform.
        :type gcp_conn_id: str
    """

    template_fields = (
        'sql_insert_history_file', 'sql_insert_holy_sampling_contact', 'sql_update_contact_status',
        'sql_get_campaign_from_qualifio_campaign_ref', 'sftp_esampling_prefix', 'sql_check_exist_qualifio_ref_list',
        'sftp_esampling_import_path_format', 'sftp_esampling_dest_folder', 'import_notification_date')
    template_ext = ('.sql',)
    ui_color = '#ededed'

    def __init__(
            self,
            bucket,
            sql_insert_history_file,
            sql_insert_holy_sampling_contact,
            sql_update_contact_status,
            sql_get_campaign_from_qualifio_campaign_ref,
            alert_error_emails,
            alert_error_emails_cc='',
            sftp_esampling_prefix='home/uploads/esampling/imports/',
            sftp_esampling_file_regex=r"home/uploads/esampling/imports/\d+/fichiers_traites/(.*)csv$",
            sftp_esampling_import_path_format='home/uploads/esampling/imports/{qualifio_campaign_ref}/fichiers_traites/',
            sftp_esampling_dest_folder='fichiers_traites_history',
            sftp_esampling_dest_error_folder='fichiers_traites_error',
            sftp_esampling_delimiter='.csv',
            database='matrix',
            psql_esampling_conn_id='psql_esampling_app',
            import_notification_date='{{ execution_date.strftime("%Y-%m-%d") }}',
            gcp_conn_id='google_cloud_default',
            sql_check_exist_qualifio_ref_list=None,
            delegate_to=None,
            *args, **kwargs):
        super(EsamplingImportProcessedFilesOperator, self).__init__(*args, **kwargs)

        # conn id psql
        self.gcp_conn_id = gcp_conn_id
        self.psql_esampling_conn_id = psql_esampling_conn_id
        self.bucket = bucket
        self.sftp_esampling_prefix = sftp_esampling_prefix
        self.sftp_esampling_delimiter = sftp_esampling_delimiter
        self.sftp_esampling_import_path_format = sftp_esampling_import_path_format
        self.sftp_esampling_dest_folder = sftp_esampling_dest_folder
        self.sftp_esampling_dest_error_folder = sftp_esampling_dest_error_folder
        self.sftp_esampling_file_regex = sftp_esampling_file_regex

        # import params
        self.sql_insert_holy_sampling_contact = sql_insert_holy_sampling_contact
        self.sql_insert_history_file = sql_insert_history_file
        self.sql_update_contact_status = sql_update_contact_status
        self.sql_get_campaign_from_qualifio_campaign_ref = sql_get_campaign_from_qualifio_campaign_ref
        self.sql_check_exist_qualifio_ref_list = sql_check_exist_qualifio_ref_list
        self.delegate_to = delegate_to
        self.alert_error_emails_cc = alert_error_emails_cc
        self.alert_error_emails = alert_error_emails
        self.import_notification_date = import_notification_date

        # self.sftp_import_dir = sftp_import_dir.rstrip('/')
        self.database = database

        self.gcs_hook = GCSHook(
            gcp_conn_id=self.gcp_conn_id,
            delegate_to=self.delegate_to)
        self.pg_hook = PostgresHook(
            postgres_conn_id=self.psql_esampling_conn_id, database=self.database)

    def execute(self, context):
        self.log.info('Getting list of the files. Bucket: %s; Delimiter: %s; Prefix: %s',
                      self.bucket, self.sftp_esampling_delimiter, self.sftp_esampling_prefix)

        list_files = self.gcs_hook.list(bucket_name=self.bucket,
                                        prefix=self.sftp_esampling_prefix,
                                        delimiter=self.sftp_esampling_delimiter)
        self.log.info('list_files : {} '.format(str(list_files)))

        for file_path in list_files:
            match_regex = bool(re.match(self.sftp_esampling_file_regex, file_path))
            if not match_regex:
                continue  # continue here
            file_name = os.path.basename(file_path)
            qualifio_campaign_ref = self.get_campaign_id_from_path(file_path)
            campaign = self.retrieve_campaign_data(qualifio_campaign_ref)
            public_ref = campaign['public_ref']

            self.log.info('file_name : {}'.format(str(file_name)))

            # create temp file
            fd, tmp_filename = tempfile.mkstemp(text=True)

            self.log.info('Executing download: %s, %s, %s',
                          self.bucket,
                          file_path,
                          tmp_filename)

            # download file
            self.gcs_hook.download(bucket_name=self.bucket, object_name=file_path, filename=tmp_filename)
            csv_options = {
                'delimiter': ';',
                'quotechar': '"',
                'quoting': csv.QUOTE_MINIMAL
            }

            fields_mapping = {
                # prisma table ->  holy sampling
                'qualifio_ref': 'ref_lead',
                'firstname': 'nom',
                'lastname': 'prenom',
                'address1': 'adresse_1',
                'address2': 'adresse_2',
                'address3': 'adresse_3',
                'address4': 'adresse_4',
                'address5': 'adresse_5',
                'address6': 'adresse_6',
                'postal_code': 'code_postal',
                'city': 'ville',
            }

            fields_mapping_keys = list(fields_mapping.values())
            ignored_columns = ['adresse_4', 'adresse_5', 'adresse_6']
            valid_header = [column for column in fields_mapping_keys if column not in ignored_columns]

            is_valid_file = self.is_valid_file_data(tmp_filename, valid_header,
                                                    delimiter=';', na_values=None,
                                                    keep_default_na=False)
            if not is_valid_file:
                dst_error_file_path = file_path.replace('fichiers_traites', self.sftp_esampling_dest_error_folder)
                self.log.info('dst_error_file_path = {}'.format(str(dst_error_file_path)))
                self.notify_error_email(public_ref, qualifio_campaign_ref, file_path, file_name)
                # copy file to other folder then delete file
                self.gcs_hook.copy(self.bucket, file_path, self.bucket, dst_error_file_path)
                self.gcs_hook.delete(self.bucket, file_path)

                continue

            df = pd.read_csv(tmp_filename, delimiter=';', na_values=None, keep_default_na=False)
            df.columns = df.columns.str.replace(' ', '_')
            contacts = df.to_dict(orient='records')
            self.log.info('contacts : {}'.format(str(contacts)))
            """
               source file format:
                    NumTHT;
                    REF LEAD;
                    CIV;
                    NOM;
                    PRENOM;
                    ADRESSE1;
                    ADRESSE2;
                    ADRESSE3;
                    ADRESSE4;
                    ADRESSE5;
                    CP;
                    VILLE;
                    Email;
                    MARQUE;
                    OFFRE;
                    OPTIN;
                    DATE_ET_HEURE_DE_RECRUTEMENT;
                    dkeyi;
                    diag_adrs;
                    dkeyf
               """
            # qualifio_ref, firstname, lastname, address1, address2, address3, address4,
            # address5, address6, postal_code, city, holy_sampling_data, received_date
            # ID;ref_lead;marque;prenom;nom;adresse_1;adresse_2;adresse_3;code_postal;ville;offre;date_collecte;KEY;EMAIL

            qualifio_refs_list = []
            contacts_data = []
            for contact in contacts:
                self.log.info('contact : {}'.format(str(contact)))
                holy_sampling_data = dict()
                for sql_key, holy_sampling_key in fields_mapping.items():
                    holy_sampling_data[sql_key] = contact.get(holy_sampling_key, '')

                holy_sampling_data["holy_sampling_data"] = json.dumps(contact)
                self.log.info('holy_sampling_data = {}'.format(str(holy_sampling_data)))
                contacts_data.append(holy_sampling_data)
                qualifio_ref = holy_sampling_data['qualifio_ref']
                if qualifio_ref is None:
                    continue
                self.log.info('qualifio_ref : {}'.format(str(qualifio_ref)))
                qualifio_refs_list.append(qualifio_ref)

            import_log = {
                'file_name': file_name,
                'qualifio_campaign_ref': qualifio_campaign_ref,
                'public_ref': public_ref,
                'rows_number': len(qualifio_refs_list),
                'valid_rows_number': len(qualifio_refs_list),
                'error': '',
            }

            self.log.info('qualifio_refs_list = {}'.format(str(qualifio_refs_list)))

            if self.sql_check_exist_qualifio_ref_list:
                qualifio_refs_list_valid = self.valid_qualifio_ref_list(public_ref, qualifio_refs_list)
                unmatched_element = list(set(qualifio_refs_list).difference(qualifio_refs_list_valid))
                if len(unmatched_element) != 0:
                    message = ' Le fichier contient ' + str(len(unmatched_element)) + \
                              ' profils inconnus dans notre base de donnée. <br> '

                    import_log['valid_rows_number'] = len(qualifio_refs_list) - len(unmatched_element)
                    import_log['unmatched_elements'] = '%s' % (', '.join(str(x) for x in unmatched_element))
                    import_log['error'] = message

                    self.notify_alert_email(public_ref, qualifio_campaign_ref, file_path, file_name, message=message)

            if len(qualifio_refs_list_valid) > 0:
                # exist des profils valident dans le fichier
                self.insert_holy_sampling_contact(public_ref, contacts_data, qualifio_refs_list_valid)
                self.update_qualifio_contacts(public_ref, qualifio_refs_list_valid)

            dst_file_path = file_path.replace('fichiers_traites', self.sftp_esampling_dest_folder)
            self.log.info('dst_file_path = {}'.format(str(dst_file_path)))

            # mark file as processed
            self.insert_history_file(qualifio_campaign_ref, file_name, import_log=import_log)

            # copy file to another folder then delete file
            self.gcs_hook.copy(self.bucket, file_path, self.bucket, dst_file_path)
            self.gcs_hook.delete(self.bucket, file_path)

    # ----------------------------------------------------------------
    def valid_qualifio_ref_list(self, public_ref, qualifio_ref_list):
        qualifio_ref_list_str = "(%s)" % (','.join("'" + str(x) + "'" for x in qualifio_ref_list))
        sql_check_exist_qualifio_ref_list = self.sql_check_exist_qualifio_ref_list.format(
            qualifio_ref_list=qualifio_ref_list_str, public_ref=public_ref)

        self.log.info('sql_check_exist_qualifio_ref_list = {}'.format(str(sql_check_exist_qualifio_ref_list)))
        all_records = self.pg_hook.get_records(sql_check_exist_qualifio_ref_list, self.params)
        result = []
        for record in all_records:
            print(record[0])
            result.append(record[0])

        return result

    def insert_history_file(self, qualifio_campaign_ref, file_name, import_log=None):
        sql_insert_history_file = self.sql_insert_history_file.format(
            file_name=file_name, qualifio_campaign_ref=qualifio_campaign_ref, import_log=json.dumps(import_log))

        self.log.info('sql_insert_history_file = {}'.format(str(sql_insert_history_file)))

        self.pg_hook.run(sql_insert_history_file, parameters=self.params)
        self.log.info('history successfully inserted')
        return True

    def insert_holy_sampling_contact(self, public_ref, contacts, qualifio_refs_list_valid):
        insert_query = ''
        pos = -1
        end = len(qualifio_refs_list_valid) - 1
        for contact in contacts:
            if contact['qualifio_ref'] not in qualifio_refs_list_valid:
                continue
            pos += 1
            sep = ', '
            if pos == end:
                sep = ' '
            map_insert_value = "('{qualifio_ref}', '{firstname}', '{lastname}', '{address1}', " \
                               "'{address2}', '{address3}', '{address4}', '{address5}', '{address6}'," \
                               " '{postal_code}', '{city}', '{holy_sampling_data}', NOW())".format(**contact)
            insert_query = insert_query + map_insert_value + sep

        self.log.info('insert_query = {}'.format(str(insert_query)))
        sql_insert_holy_sampling_contact = self.sql_insert_holy_sampling_contact.format(
            public_ref=public_ref, contacts_values_sql=insert_query)

        self.log.info('sql_insert_holy_sampling_contact = {}'.format(str(sql_insert_holy_sampling_contact)))

        self.pg_hook.run(sql_insert_holy_sampling_contact, parameters=self.params)
        self.log.info('Holy sampling contacts has been successfully inserted')
        return True

    def update_qualifio_contacts(self, public_ref, contacts_ids_list):
        qualifio_ref_list_str = "(%s)" % (','.join("'" + str(x) + "'" for x in contacts_ids_list))
        sql_update_contact_status = self.sql_update_contact_status.format(public_ref=public_ref,
                                                                          qualifio_ref_list=qualifio_ref_list_str)
        self.log.info('sql_update_contact_status = {}'.format(str(sql_update_contact_status)))

        self.pg_hook.run(sql_update_contact_status, parameters=self.params)
        self.log.info('Qualifio campaign contacts has been successfully updated')
        return True

    def get_campaign_id_from_path(self, path):
        result = re.split(r'[\\/]', path)
        filter(None, result)
        campaign_id = result[-3]
        self.log.info('result = {}'.format(str(result)))
        self.log.info('campaign_id = {}'.format(str(campaign_id)))
        return campaign_id

    def retrieve_campaign_data(self, qualifio_campaign_ref):
        sql_get_campaign_from_id = self.sql_get_campaign_from_qualifio_campaign_ref.format(
            qualifio_campaign_ref=int(qualifio_campaign_ref))
        self.log.info('sql_get_campaign_from_id = {}'.format(str(sql_get_campaign_from_id)))

        results = self.pg_hook.get_records(sql_get_campaign_from_id, self.params)
        campaign_record = results[0]
        # id, public_ref, advertiser, offer, is_active, qualifio_campaign_ref
        campaign = {
            'id': campaign_record[0],
            'public_ref': campaign_record[1],
            'advertiser': campaign_record[2],
            'offer': campaign_record[3],
            'is_active': campaign_record[4],
            'qualifio_campaign_ref': campaign_record[5],
        }

        self.log.info('campaign = {}'.format(str(campaign)))
        return campaign

    def is_valid_file_data(self, tmp_filename, valid_header, delimiter=';', na_values=None, keep_default_na=False):

        # validate header
        df_header = pd.read_csv(tmp_filename, delimiter=delimiter, na_values=na_values,
                                keep_default_na=keep_default_na)

        df_header_list = df_header.columns.to_list()

        self.log.info('df_header_list : {}'.format(str(df_header_list)))
        self.log.info('valid_header : {}'.format(str(valid_header)))

        compare = set(valid_header).issubset(df_header_list)

        self.log.info('compare !! : {} '.format(str(compare)))
        if compare:
            self.log.info('valid file !! ')
            return True
        self.log.info('not valid file !! ')
        return False

    def notify_error_email(self, public_ref, qualifio_campaign_ref, file_path, file_name, message=None):
        if not message:
            message = 'L\'en-tête du fichier ne respecte pas le format attendu.'

        title = "[E-sampling - Campaign Import Error: " + str(public_ref) + \
                " du " + str(self.import_notification_date) + "]: Erreur importation du fichier"

        body = (
                "Bonjour,"
                "<br> Nous rencontrons un erreur lors de l'import du fichier suivant: <br>"
                "<br>   Date import: " + str(self.import_notification_date) + ""
                                                                              "<br>   Nom du Campagne: " + str(
            public_ref) + ""
                          "<br>   Campagne Id: " + str(qualifio_campaign_ref) + ""
                                                                                "<br>   Nom du fichier: " + str(
            file_name) + ""
                         "<br>   Path: " + str(file_path) + ""
                                                            "<br>   Erreur : <b>" + str(message) + "</b>"
                                                                                                   "<br>   Remarque : <i> Le fichier a été déplacé vers le dossier " +
                str(self.sftp_esampling_dest_error_folder) + " du campagne. </i>"
                                                             "<br>"
                                                             "<br> Toute notre équipe reste à votre disposition pour d'éventuelles questions concernant cette information. <br>"
                                                             "<br> Cordialement, "
                                                             "<br>IT-DATA - PRISMA MEDIA <br> ")

        # Send email confirmation
        email_notif = EmailOperator(
            task_id='email_notify',
            to=self.alert_error_emails,
            subject=title,
            cc=self.alert_error_emails_cc,
            html_content=body,
            conn_id='sendgrid_default')
        email_notif.execute(self)

    def notify_alert_email(self, public_ref, qualifio_campaign_ref, file_path, file_name, message=None):

        if not message:
            message = 'Erreur importation du fichier'

        title = "[E-sampling - Campaign Import Alert: " + str(public_ref) + " du " + \
                str(self.import_notification_date) + "]: Information importante lors d'importation fichier "
        body = (
                "Bonjour,"
                "<br> Nous avons détecté des erreurs lors d'importation du fichier suivant: <br>"
                "<br>   Date import: " + str(self.import_notification_date) + ""
                                                                              "<br>   Nom du Campagne: " + str(
            public_ref) + ""
                          "<br>   Campagne Id: " + str(qualifio_campaign_ref) + ""
                                                                                "<br>   Nom du fichier: " + str(
            file_name) + ""
                         "<br>   Path: " + str(file_path) + ""
                                                            "<br>   Erreur : <b>" + str(message) + "</b>"
                                                                                                   "<br>   <i> Remarque: Ces erreurs ne bloquent pas l'importation du fichier.</i>"
                                                                                                   "<br>   <i> Pour plus d'informations sur ces profils, veuillez contacter notre équipe technique </i>"
                                                                                                   "<br><br> Cordialement, "
                                                                                                   "<br> IT-DATA - PRISMA MEDIA <br> ")

        print(body)
        # Send email confirmation
        email_notif = EmailOperator(
            task_id='email_notify',
            to=self.alert_error_emails,
            subject=title,
            cc=self.alert_error_emails_cc,
            html_content=body,
            conn_id='sendgrid_default')
        email_notif.execute(self)
