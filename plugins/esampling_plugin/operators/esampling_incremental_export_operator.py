# gsutil cp plugins/esampling_plugin/operators/esampling_incremental_export_operator.py gs://europe-west1-mozart-cluster-2e910a39-bucket/plugins/esampling_plugin/operators/
# gsutil cp plugins/esampling.py gs://europe-west1-mozart-cluster-2e910a39-bucket/plugins/
import json
import os
import tempfile

from airflow.models import BaseOperator
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow.utils.email import send_email


class EsamplingIncrementalExportOperator(BaseOperator):
    """
        Export Postgres Query Result To Holy Sampling Partner

        :param schema: name of the schema
        :type schema: string
        :param table: name of the table to dump
        :type table: bases
        :param universes: universe list with their ids
        :type universes: dict
        :param universes_sftp: universe list with SFTP conn_id list
        :type universes_sftp: dict
        :param bucket: The Google cloud storage bucket where the object is. (templated)
        :type bucket: string
        :param object: The name of the object to upload in the Google cloud
            storage bucket. (templated)
        :type object: string
        :param postgres_conn_id: reference to a specific postgres database
        :type postgres_conn_id: string
        :param gcp_conn_id: (Optional) The connection ID used to connect to Google Cloud Platform.
        :type gcp_conn_id: str
    """

    template_fields = (
        'sql_incremental_export', 'sql_get_contacts_ids', 'sql_check_campaign', 'sftp_export_path',
        'sql_update_contact_status', 'sftp_export_file_name', 'sql_get_active_campaigns', 'export_notification_date')
    template_ext = ('.sql',)
    ui_color = '#ededed'

    def __init__(
            self,
            bucket,
            sql_incremental_export,
            sql_check_campaign,
            sql_update_contact_status,
            sql_get_contacts_ids,
            export_notification_emails,
            export_notification_emails_cc='',
            sql_get_active_campaigns='0_get_active_campaigns.sql',
            sftp_export_file_name='contacts_{qualifio_campaign_ref}__{{ execution_date.strftime("%Y%m%d_%H%M") }}.csv',
            sftp_export_path='home/uploads/esampling/imports/{qualifio_campaign_ref}/fichiers_envoyes/',
            esampling_conn_id='psql_esampling_app',
            export_notification_date='{{ execution_date.strftime("%Y-%m-%d") }}',
            gcp_conn_id='google_cloud_default',
            mime_type='text/csv',
            database='matrix',
            delegate_to=None,
            gzip=False,
            campaign_public_refs_ignored=None,
            *args, **kwargs):
        super(EsamplingIncrementalExportOperator, self).__init__(*args, **kwargs)

        # conn id psql
        self.gcp_conn_id = gcp_conn_id
        self.esampling_conn_id = esampling_conn_id
        self.bucket = bucket
        self.mime_type = mime_type
        self.delegate_to = delegate_to
        self.gzip = gzip
        # export params
        self.sql_check_campaign = sql_check_campaign
        self.sql_incremental_export = sql_incremental_export
        self.sql_update_contact_status = sql_update_contact_status
        self.sql_get_contacts_ids = sql_get_contacts_ids
        self.sql_get_active_campaigns = sql_get_active_campaigns
        self.export_notification_emails = export_notification_emails
        self.export_notification_emails_cc = export_notification_emails_cc
        self.export_notification_date = export_notification_date

        # esampling campaign
        self.campaign_public_refs_ignored = campaign_public_refs_ignored
        self.sftp_export_file_name = sftp_export_file_name
        self.sftp_export_path = sftp_export_path.rstrip('/')
        self.database = database

        self.gcs_hook = GCSHook(
            gcp_conn_id=self.gcp_conn_id,
            delegate_to=self.delegate_to)
        self.pg_hook = PostgresHook(
            postgres_conn_id=self.esampling_conn_id, database=self.database)

    def execute(self, context):
        campaigns = self.get_active_campaigns()
        for campaign_id, campaign_data in campaigns.items():
            public_ref = campaign_data.get('public_ref')
            qualifio_campaign_ref = campaign_data.get('qualifio_campaign_ref')
            self.log.info(
                'campaign item  campaign_id = {campaign_id}, public_ref = {public_ref}, '
                'qualifio_campaign_ref = {qualifio_campaign_ref} '.format(
                    campaign_id=campaign_id,
                    public_ref=public_ref,
                    qualifio_campaign_ref=qualifio_campaign_ref))

            if self.campaign_public_refs_ignored is None or public_ref not in self.campaign_public_refs_ignored:
                # check campaign rows:
                is_threshold_reached = self.check_campaign_threshold(public_ref)  # bool
                self.log.info('is_threshold_reached = {} '.format(is_threshold_reached))

                if is_threshold_reached:
                    contacts_ids_list = self.get_contacts_ids_list(public_ref)
                    self.log.info('contacts_ids_list = {} '.format(str(contacts_ids_list)))
                    fd, tmp_filename = tempfile.mkstemp(text=True)
                    tmp_filename = self.bulk_date_to_tmp_file(public_ref, contacts_ids_list, tmp_filename)

                    # upload to esampling sftp (gcs)
                    dest_gcs_dir = self.sftp_export_path.format(qualifio_campaign_ref=qualifio_campaign_ref)
                    sftp_export_file_name = self.sftp_export_file_name.format(
                        qualifio_campaign_ref=qualifio_campaign_ref)

                    dest_gcs_object = '{dir}/{filename}'.format(dir=dest_gcs_dir.strip('/'),
                                                                filename=sftp_export_file_name)
                    self.log.info('export file to sftp object = {}'.format(str(dest_gcs_object)))
                    self.gcs_hook.upload(
                        bucket_name=self.bucket,
                        object_name=dest_gcs_object,
                        mime_type=self.mime_type,
                        filename=tmp_filename,
                        gzip=self.gzip,
                    )

                    self.notify_email(public_ref, qualifio_campaign_ref, dest_gcs_dir.strip('/'), sftp_export_file_name,
                                      len(contacts_ids_list))
                    # remove temp file
                    os.close(fd)
                    os.unlink(tmp_filename)

                    # update contact status:
                    self.update_qualifio_contacts(public_ref, contacts_ids_list)

                    # end campaign
        self.log.info('End Export')
        return []

    def get_active_campaigns(self):
        all_campaigns = self.pg_hook.get_records(self.sql_get_active_campaigns, self.params)
        campaigns = all_campaigns[0][0]

        self.log.info('campaign = {}'.format(str(campaigns)))
        return json.loads(campaigns)

    def check_campaign_threshold(self, public_ref):
        sql_check_campaign = self.sql_check_campaign.format(public_ref=public_ref)
        self.log.info('sql_incremental_export = {}'.format(str(sql_check_campaign)))
        results = self.pg_hook.get_records(sql_check_campaign, self.params)
        return results[0][0]

    def get_contacts_ids_list(self, public_ref):
        sql_get_contacts_ids = self.sql_get_contacts_ids.format(public_ref=public_ref)
        self.log.info('sql_get_contacts_ids = {}'.format(str(sql_get_contacts_ids)))
        all_records = self.pg_hook.get_records(sql_get_contacts_ids, self.params)
        result = []
        for contact_id in all_records:
            print(contact_id[0])
            result.append(contact_id[0])
        return result

    def bulk_date_to_tmp_file(self, public_ref, contacts_ids_list, dst_file):
        qualifio_ref_list_str = "(%s)" % (','.join("'" + str(x) + "'" for x in contacts_ids_list))
        self.log.info('qualifio_ref_list_str = {}'.format(str(qualifio_ref_list_str)))

        sql_incremental_export = self.sql_incremental_export.format(public_ref=public_ref,
                                                                    qualifio_ref_list=qualifio_ref_list_str)
        self.log.info('sql_incremental_export = {}'.format(str(sql_incremental_export)))
        self.pg_hook.copy_expert(sql_incremental_export, dst_file)
        return dst_file

    def get_campaign_contacts(self, public_ref):
        sql_incremental_export = self.sql_incremental_export.format(public_ref=public_ref)
        results = self.pg_hook.get_records(sql_incremental_export, self.params)

        return json.dumps(results, separators=(',', ':'))

    def update_qualifio_contacts(self, public_ref, contacts_ids_list):
        qualifio_ref_list_str = "(%s)" % (','.join("'" + str(x) + "'" for x in contacts_ids_list))
        sql_update_contact_status = self.sql_update_contact_status.format(public_ref=public_ref,
                                                                          qualifio_ref_list=qualifio_ref_list_str)
        self.log.info('sql_update_contact_status = {}'.format(str(sql_update_contact_status)))

        self.pg_hook.run(sql_update_contact_status, parameters=self.params)
        self.log.info('Qualifio campaign contacts has been successfully updated')
        return True

    def notify_email(self, public_ref, qualifio_campaign_ref, file_path, file_name, nbr_profiles):

        title = "[E-sampling - Campagne Export: " + str(public_ref) + " du " + str(self.export_notification_date) \
                + "]: Nouveau fichier disponible dans votre espace sftp Prisma"
        body = ("Bonjour,"
                "<br> Nous venons de déposer un nouveau fichier dans votre espace sftp Prisma "
                "<br><br> Voici les informations de cette livraison: <br>"
                "<br>   Date de livraison: " + str(self.export_notification_date) + ""
                                                                                    "<br>   Nom du Campagne: " + str(
            public_ref) + ""
                          "<br>   Campagne Id: " + str(qualifio_campaign_ref) + ""
                                                                                "<br>   Nom du fichier: " + str(
            file_name) + ""
                         "<br>   Path: imports/" + str(qualifio_campaign_ref) + "/fichiers_envoyes "
                                                                                "<br>   Nombre de lignes: " + str(
            nbr_profiles) + ""
                            "<br>"
                            "<br> Toute notre équipe reste à votre disposition pour d'éventuelles questions concernant cette livraison. <br>"
                            "<br>Cordialement, "
                            "<br>IT-DATA - PRISMA MEDIA <br> "
                )

        print(body)

        send_email(to=self.export_notification_emails,
                   subject=title,
                   html_content=body,
                   mime_charset='utf-8',
                   cc=self.export_notification_emails_cc,
                   conn_id='sendgrid_default')
