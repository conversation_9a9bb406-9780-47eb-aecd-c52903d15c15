# gsutil cp production/plugins/dc_plugin/hooks/extract_service.py gs://europe-west1-mozart-cluster-4128dbb8-bucket/plugins/dc_plugin/hooks/

import re

from airflow.providers.postgres.hooks.postgres import PostgresHook


class ExtractService:

    def __init__(self):
        self.hook = PostgresHook(postgres_conn_id='psql_datacenter_app', database='matrix')

    def create_extract_campaign(self, extract_id):
        segment_fetch = "SELECT segment_definition->>'full_sql' " \
                        "  FROM datacenter.campaign_extract " \
                        " WHERE id = {id} " \
                        "   AND status = 'NEW';".format(id=extract_id)
        print('Query : ' + str(segment_fetch))
        segment = self.hook.get_first(segment_fetch)

        prunes_fetch = "SELECT p.campaign_extract_history_id " \
                       "  FROM datacenter.campaign_extract_prune p " \
                       "  JOIN datacenter.campaign_extract_history h " \
                       "    ON h.id = p.campaign_extract_history_id " \
                       " WHERE p.campaign_extract_id = {id} " \
                       "   AND h.status IN ('IN PROGRESS', 'COMPLETED');".format(id=extract_id)
        print('Query : ' + str(prunes_fetch))
        prunes = self.hook.get_records(prunes_fetch)

        prune_statement = ''
        for prune_id in prunes:
            prune_statement += "EXCEPT " \
                               "    SELECT pmi.id " \
                               "      FROM matrix__postal.profile_master_id AS pmi" \
                               "      JOIN matrix__postal_extract_stockpile.campaign_extract_history_{id} AS e " \
                               "        ON e.customer_ref = pmi.customer_ref ".format(id=prune_id[0])

        from_table = re.search('(?<=FROM )([A-Za-z._-]+)', segment[0])

        create_segment_tmp_table = "CREATE TABLE matrix__postal_extract_stockpile.campaign_extract_{id}_tmp AS " \
                                   "SELECT DISTINCT({from_table}.profile_master_id) AS profile_master_id " \
                                   "{segment_query} " \
                                   "{prune};" \
                                   "ALTER TABLE matrix__postal_extract_stockpile.campaign_extract_{id}_tmp OWNER TO datacenter; ".format(
            id=extract_id,
            from_table=from_table.group(),
            segment_query=str(segment[0]),
            prune=prune_statement)
        create_segment_table = "CREATE TABLE matrix__postal_extract_stockpile.campaign_extract_{id} AS " \
                               "SELECT p.customer_ref AS customer_ref " \
                               "  FROM matrix__postal.profile_master_id AS p " \
                               "  JOIN matrix__postal_extract_stockpile.campaign_extract_{id}_tmp AS tmp " \
                               "    ON tmp.profile_master_id = p.id;" \
                               " ALTER TABLE matrix__postal_extract_stockpile.campaign_extract_{id} OWNER TO datacenter; " \
                               "".format(id=extract_id)
        segment_table_prikey = "ALTER TABLE matrix__postal_extract_stockpile.campaign_extract_{id} " \
                               "ADD PRIMARY KEY (customer_ref)".format(id=extract_id)
        segment_table_rights = "ALTER TABLE matrix__postal_extract_stockpile.campaign_extract_{id} " \
                               "OWNER TO datacenter ".format(id=extract_id)

        update_parent_table = "UPDATE datacenter.campaign_extract " \
                              "   SET status = 'IN PROGRESS', " \
                              "       volume = (" \
                              "         SELECT COUNT(*) " \
                              "           FROM matrix__postal_extract_stockpile.campaign_extract_{id} " \
                              "       )" \
                              " WHERE id = {id};".format(id=extract_id)
        clean_tmp_table = "DROP TABLE matrix__postal_extract_stockpile.campaign_extract_{id}_tmp;".format(id=extract_id)

        try:
            print('Tmp query : ' + str(create_segment_tmp_table))
            self.hook.run(create_segment_tmp_table, autocommit=True)
            print('Create query : ' + str(create_segment_table))
            self.hook.run(create_segment_table, autocommit=True)
            print('Alter query : ' + str(segment_table_prikey))
            self.hook.run(segment_table_prikey, autocommit=True)
            print('Alter query : ' + str(segment_table_rights))
            self.hook.run(segment_table_rights, autocommit=True)
            print('Update query : ' + str(update_parent_table))
            self.hook.run(update_parent_table, autocommit=True)
            print('Drop tmp query : ' + str(clean_tmp_table))
            self.hook.run(clean_tmp_table, autocommit=True)
        except BaseException as e:
            msg, = e.args
            print('Error creating matrix__postal_extract_stockpile.campaign_extract_{id} ; '
                  'detail: {detail} '.format(id=extract_id, detail=msg))
            return []

        return 'matrix__postal_extract_stockpile.campaign_extract_{id}'.format(id=extract_id)

    def create_volume_extract(self, history_id, volume, parent_id):
        exclusions_fetch = "SELECT id " \
                           "  FROM datacenter.campaign_extract_history " \
                           " WHERE campaign_extract_id = {parent} " \
                           "   AND id != {selfid} " \
                           "   AND status IN ('IN PROGRESS', 'COMPLETED');".format(parent=parent_id, selfid=history_id)
        print('Query : ' + str(exclusions_fetch))
        exclusions = self.hook.get_records(exclusions_fetch)

        exclusion_statement = ''
        for exclude_id in exclusions:
            exclusion_statement += "EXCEPT " \
                                   "    SELECT customer_ref " \
                                   "      FROM matrix__postal_extract_stockpile.campaign_extract_history_{id} " \
                                   "".format(id=exclude_id[0])

        create_segment_table = "CREATE TABLE matrix__postal_extract_stockpile.campaign_extract_history_{id} AS " \
                               "SELECT DISTINCT(customer_ref) " \
                               "  FROM matrix__postal_extract_stockpile.campaign_extract_{parent} " \
                               "{exclusion_statement} " \
                               " LIMIT {limit};" \
                               "ALTER TABLE matrix__postal_extract_stockpile.campaign_extract_history_{id} OWNER TO datacenter;".format(
            id=history_id,
            parent=parent_id,
            exclusion_statement=exclusion_statement,
            limit=volume)
        segment_table_history_prikey = "ALTER TABLE matrix__postal_extract_stockpile.campaign_extract_history_{id} " \
                                       "ADD PRIMARY KEY (customer_ref)".format(id=history_id)
        segment_table_history_rights = "ALTER TABLE matrix__postal_extract_stockpile.campaign_extract_history_{id} " \
                                       "OWNER TO datacenter;".format(id=history_id)

        update_history_table = "UPDATE datacenter.campaign_extract_history " \
                               "   SET status = 'IN PROGRESS', " \
                               "       volume = (" \
                               "         SELECT COUNT(*) " \
                               "           FROM matrix__postal_extract_stockpile.campaign_extract_history_{id} " \
                               "       )" \
                               " WHERE id = {id};".format(id=history_id)

        try:
            print('Create query : ' + str(create_segment_table))
            self.hook.run(create_segment_table, autocommit=True)
            print('Alter query : ' + str(segment_table_history_prikey))
            self.hook.run(segment_table_history_prikey, autocommit=True)
            print('Alter query : ' + str(segment_table_history_rights))
            self.hook.run(segment_table_history_rights, autocommit=True)
            print('Update query : ' + str(update_history_table))
            self.hook.run(update_history_table, autocommit=True)
        except BaseException as e:
            msg, = e.args
            print('Error creating matrix__postal_extract_stockpile.campaign_extract_history_{id} ; '
                  'detail: {detail}'.format(id=history_id, detail=msg))
            return []

        return 'matrix__postal_extract_stockpile.campaign_extract_history_{id}'.format(id=history_id)
