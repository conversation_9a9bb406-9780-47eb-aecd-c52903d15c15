# gsutil cp production/plugins/import_plugin/operators/bmk_import_operator.py gs://europe-west1-mozart-cluster-4128dbb8-bucket/plugins/import_plugin/operators/


import os
import shutil
import tempfile

import pandas as pd
from airflow.models import BaseOperator
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from airflow.providers.postgres.hooks.postgres import PostgresHook


class BmkImportOperator(BaseOperator):
    """
        Load a Google cloud storage file to postgres
        :param schema: name of the schema
        :type schema: string
        :param table: name of the table to dump
        :type table: bases
        :param bucket: The Google cloud storage bucket where the object is. (templated)
        :type bucket: string
        :param prefix: prefix of object we can to downlaod. /path/to/dir/pattern* files will be download (templated)
        :type prefix: List of string prefix
        :param postgres_conn_id: reference to a specific postgres database
        :type postgres_conn_id: string
        :param gcp_conn_id: (Optional) The connection ID used to connect to Google Cloud Platform.
        :type gcp_conn_id: str
    """
    template_fields = ('prefix', 'table',)
    ui_color = '#ededed'

    def __init__(
            self,
            bucket,
            prefix,
            database,
            schema,
            table,
            postgres_conn_id='postgres_default',
            gcp_conn_id='google_cloud_default',
            *args, **kwargs):
        super(BmkImportOperator, self).__init__(*args, **kwargs)
        self.database = database
        self.schema = schema
        self.table = table
        self.bucket = bucket
        self.prefix = prefix
        # conn id
        self.gcp_conn_id = gcp_conn_id
        self.postgres_conn_id = postgres_conn_id

    def execute(self, context):
        fd, tmp_daily_data = tempfile.mkstemp(text=True)
        # download all the current day files
        gcs_hook = GCSHook(gcp_conn_id=self.gcp_conn_id)
        files = []
        for pref in self.prefix:
            files += gcs_hook.list(bucket_name=self.bucket, prefix=pref, delimiter='.csv')

        pg_hook = PostgresHook(postgres_conn_id=self.postgres_conn_id,
                               database=self.database)
        # merge all files into one file
        with open(tmp_daily_data, 'wb') as fd_out:
            for object in files:
                # Add row to sql table
                file_name = os.path.basename(object)
                fd_one_file, tmp_one_file = tempfile.mkstemp(text=True)
                self.log.info('Import %s/%s to table %s.%s', self.bucket, object, self.schema, self.table)
                gcs_hook.download(bucket_name=self.bucket, object_name=object, filename=tmp_one_file)
                is_valid = self.is_valid_file(filename=tmp_one_file)
                self.log.info('is_valid %s', is_valid)
                if not is_valid:
                    continue

                # if valid file => proceed
                # Add / Update file to log table
                query_file_log = '''
                INSERT INTO  matrix__pmc_import.files_processed_logs(file_name, create_date, update_date)
                VALUES ('{file_name}' , NOW(), NOW())
                ON CONFLICT (file_name) DO UPDATE
                SET update_date = NOW();
                '''.format(file_name=file_name)
                self.log.info('query_file_log: %s ', query_file_log)

                pg_hook.run(query_file_log)
                # output errors
                for output in pg_hook.conn.notices:
                    self.log.info(output)
                self.log.info('Insert/Update file %s to matrix__pmc_import.files_processed_logs table ', file_name)

                with open(tmp_one_file, 'rb') as fd_in:
                    shutil.copyfileobj(fd_in, fd_out)
                os.close(fd_one_file)
                os.unlink(tmp_one_file)
        del gcs_hook
        # load the file into postgres
        full_table_name = '{schema}.{table}'.format(schema=self.schema, table=self.table)

        pg_hook.run('TRUNCATE TABLE {}'.format(full_table_name))

        query = "COPY {table} (user_id, email, brand, content_id, update_at) FROM STDIN WITH CSV DELIMITER AS ',' ".format(
            table=full_table_name)
        pg_hook.copy_expert(query, tmp_daily_data)

        # output errors
        for output in pg_hook.conn.notices:
            self.log.info(output)
        # remove temp file
        os.close(fd)
        os.unlink(tmp_daily_data)

    def is_valid_file(self, filename, delimiter=','):
        if os.stat(filename).st_size == 0:
            return False
        # validate header
        df_header = pd.read_csv(filename, delimiter=delimiter, na_values=None,
                                keep_default_na=False, nrows=1)

        df_header_list = df_header.columns.to_list()

        if 'email' not in df_header_list:
            self.log.info('not valid file : {}!! '.format(filename))
            return False

        return True
