# gsutil cp plugins/import_plugin/operators/pmc_import_to_bq_operator.py gs://europe-west1-mozart-cluster-2e910a39-bucket/plugins/import_plugin/operators/
# gsutil -m cp -R data/sql/matrix_pmc/*  gs://europe-west1-mozart-cluster-2e910a39-bucket/data/sql/matrix_pmc/

import os
import shutil
import tempfile

# hooks
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.models import BaseOperator
from airflow.providers.google.cloud.hooks.bigquery import BigQueryHook
from airflow.providers.google.cloud.hooks.gcs import GCSHook


class PmcImportFullToBigQueryOperator(BaseOperator):
    """
    Synchronizes an S3 keys, Specific Files (full dump),
    with a Google Cloud Storage destination path
    To BQ table.

    Example of use case:

    PmcImportFullToBigQueryOperator(
        task_id='import_full_deleted',
        destination_dataset_table='import.pmc_file_deleted_data',
        s3_file_pattern=['dcExport_{{next_ds}}_full-dump_deleted'],
        schema_fields=schema_fields
    )

    :param s3_file_pattern: Files names to be imported from PMC s3 bucket
        Example full daily files, full dmp etc... (templated)
    :type s3_file_pattern: string

    :param s3_bucket: The S3 bucket where to find the objects. (templated)
    :type s3_bucket: string
    :param delimiter: the delimiter marks key hierarchy. (templated)
    :type delimiter: string
    :param aws_conn_id: The source S3 connection
    :type aws_conn_id: string
    :param dest_gcs_conn_id: The destination connection ID to use
        when connecting to Google Cloud Storage.
    :type dest_gcs_conn_id: string
    :param dest_gcs_bucket: The destination Google Cloud Storage bucket
        where you want to store the files. (templated)
    :type dest_gcs_bucket: string
    :param dest_gcs_dir: The destination Google Cloud Storage folder
        where you want to store the files. (templated)
    :type dest_gcs_dir: string
    :param destination_dataset_table: table where file data will be saved in bigquery
    :type destination_dataset_table: string
        Example 'import.pmc_file_active_data'

    """

    template_fields = ('s3_bucket', 'dest_gcs_bucket',
                       'dest_gcs_dir', 's3_file_pattern',
                       'destination_dataset_table')
    ui_color = '#e09411'

    schema_fields = [
        {
            "mode": "REQUIRED",
            "name": "data",
            "type": "STRING"
        },
    ]

    def __init__(self,
                 s3_bucket,
                 s3_file_pattern,
                 bq_project,
                 destination_dataset_table,
                 dest_gcs_bucket,
                 dest_gcs_dir,
                 delimiter='\t',
                 quote_character='',
                 dest_gcs_conn_id='gcs_matrix',
                 aws_conn_id='aws_s3_prisma_connect',
                 bigquery_conn_id='bq_matrix',
                 schema_fields=schema_fields,
                 autodetect=False,
                 delegate_to=None,
                 *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.delegate_to = delegate_to

        # S3
        self.aws_conn_id = aws_conn_id
        self.s3_bucket = s3_bucket
        self.s3_file_pattern = s3_file_pattern

        # BIGQUERY
        self.bigquery_conn_id = bigquery_conn_id
        self.bq_project = bq_project
        self.destination_dataset_table = destination_dataset_table

        # GCS
        self.dest_gcs_conn_id = dest_gcs_conn_id
        self.dest_gcs_bucket = dest_gcs_bucket
        self.dest_gcs_dir = dest_gcs_dir
        self.delimiter = delimiter
        self.quote_character = quote_character
        self.autodetect = autodetect
        self.schema_fields = schema_fields

        # Hooks
        self.s3_hook = S3Hook(aws_conn_id=self.aws_conn_id)
        self.gcs_hook = GCSHook(
            gcp_conn_id=self.dest_gcs_conn_id)
        self.bq_hook = BigQueryHook(gcp_conn_id=self.bigquery_conn_id,
                                    delegate_to=self.delegate_to)

    def execute(self, context):
        file_exist = self.sensor_file_s3(file_name=self.s3_file_pattern)
        if not file_exist:
            return 'Exit !! No file was found with the given pattern'

        self.log.info('Files To import : ' + str(self.s3_file_pattern))

        # if file exist:
        # - Download file from S3 bucket
        file_object = self.s3_hook.get_key(
            key=self.s3_file_pattern, bucket_name=self.s3_bucket)
        fd, local_filepath = tempfile.mkstemp(text=False)

        with open(local_filepath, 'wb') as f:
            file_object.download_fileobj(f)
            f.flush()
            local_filepath = self.remove_hash(local_filepath)
        self.log.info('File downloaded from S3 bucket : {}/{} to {} '.format(
            self.s3_bucket, self.s3_file_pattern, local_filepath))

        file_name = os.path.basename(self.s3_file_pattern)

        # -------------------------------
        # Upload file to CGS
        # -------------------------------
        dest_gcs_object = '{dir}/{filename}'.format(
            dir=self.dest_gcs_dir.strip('/'), filename=file_name)

        size_file = os.path.getsize(local_filepath)
        self.log.info(
            "Size File Imported After Remove Hash: {} ".format(size_file))

        self.gcs_hook.upload(bucket_name=self.dest_gcs_bucket, object_name=dest_gcs_object,
                             filename=local_filepath)

        self.log.info(
            "File uploaded to gs://{}/{}".format(self.dest_gcs_bucket, dest_gcs_object))

        # -------------------------------
        # import file to BQ from GCS
        # -------------------------------
        source_uris = ["gs://{}/{}".format(self.dest_gcs_bucket, dest_gcs_object)]

        destination_project_dataset_table = "{}.{}".format(self.bq_project, self.destination_dataset_table)

        # import to BQ table
        load_file_to_bq = self.bq_hook.run_load(
            destination_project_dataset_table=destination_project_dataset_table,
            source_uris=source_uris,
            schema_fields=self.schema_fields,
            source_format='CSV',
            autodetect=self.autodetect,
            write_disposition='WRITE_TRUNCATE',
            create_disposition='CREATE_IF_NEEDED',
            # skip_leading_rows=self.skip_leading_rows,
            field_delimiter=self.delimiter,
            # max_bad_records=self.max_bad_records,
            quote_character=self.quote_character,
            # ignore_unknown_values=self.ignore_unknown_values,
            allow_quoted_newlines=True,
            allow_jagged_rows=True,
            encoding="UTF-8",

            # schema_update_options=['ALLOW_FIELD_ADDITION'],
            # src_fmt_configs=self.src_fmt_configs,
            # time_partitioning=self.time_partitioning,
            # cluster_fields=self.cluster_fields,
            # encryption_configuration=self.encryption_configuration
        )

        self.log.info('File has been loaded successfully to table %s', destination_project_dataset_table)

        os.close(fd)
        os.unlink(local_filepath)

        return self.s3_file_pattern

    def sensor_file_s3(self, file_name, **_):
        self.log.info('Checking for file name: {} in bucket s3://{}'.format(
            file_name,
            self.s3_bucket))

        check_file = self.s3_hook.check_for_key(
            bucket_name=self.s3_bucket,
            key=file_name)

        return check_file

    def remove_hash(self, filepath, **_):
        self.log.info('Remove hash from file: {} '.format(filepath))
        cut_pos = None
        with open(filepath, 'r') as fd_in:
            first_line = fd_in.readline()
            fd_in.seek(0)  # reset file descriptor position

            if len(first_line) == 0:
                # if the file is empty
                return filepath

            if first_line[40] == '|':
                cut_pos = 41
            elif first_line[32] == '|':
                cut_pos = 33
            else:
                self.log.info('we don\'t have hash, continue ')
                self.log.info('32 fields ' + first_line[32])
                self.log.info('40 fields ' + first_line[40])
                return filepath

            # remove hash and write result into a temp file
            fd, tmp_filepath = tempfile.mkstemp(text=True)
            with open(tmp_filepath, 'w') as fd_out:
                for line in fd_in:
                    fd_out.write(line[cut_pos:])

        # swap temp and original file content
        os.unlink(filepath)
        shutil.move(tmp_filepath, filepath)

        return filepath
