# gsutil cp production/plugins/import_plugin/operators/pmc_import_operator.py gs://europe-west1-mozart-cluster-4128dbb8-bucket/plugins/import_plugin/operators/ 

import os
import shutil
import tempfile

from airflow.exceptions import AirflowException
# hooks
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.models import BaseOperator
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from airflow.providers.postgres.hooks.postgres import PostgresHook


class PmcImportOperator(BaseOperator):
    """
    Synchronizes an S3 key, possibly a prefix, with a Google Cloud Storage
    destination path.

    :param s3_bucket: The S3 bucket where to find the objects. (templated)
    :type s3_bucket: string
    :param database: Prefix string which filters objects whose name begin with
        such database. (templated)
    :type prefix: string
    :param delimiter: the delimiter marks key hierarchy. (templated)
    :type delimiter: string
    :param aws_conn_id: The source S3 connection
    :type aws_conn_id: string
    :param dest_gcs_conn_id: The destination connection ID to use
        when connecting to Google Cloud Storage.
    :type dest_gcs_conn_id: string
    :param dest_gcs_bucket: The destination Google Cloud Storage bucket
        where you want to store the files. (templated)
    :type dest_gcs_bucket: string
    :param dest_gcs_dir: The destination Google Cloud Storage folder
        where you want to store the files. (templated)
    :type dest_gcs_dir: string

    """

    template_fields = ('s3_bucket', 'dest_gcs_bucket', 'dest_gcs_dir', 'file_name', 'table',)
    ui_color = '#e09411'

    def __init__(self,
                 s3_bucket,
                 file_name,
                 database,
                 table,
                 postgres_conn_id='postgres_default',
                 aws_conn_id='aws_default',
                 dest_gcs_conn_id=None,
                 dest_gcs_bucket=None,
                 dest_gcs_dir=None,
                 *args, **kwargs):
        super().__init__(*args, **kwargs)

        # S3
        self.aws_conn_id = aws_conn_id
        self.s3_bucket = s3_bucket
        self.file_name = file_name

        # PSQL
        self.postgres_conn_id = postgres_conn_id
        self.database = database
        self.table = table

        # GCS
        self.dest_gcs_conn_id = dest_gcs_conn_id
        self.dest_gcs_bucket = dest_gcs_bucket
        self.dest_gcs_dir = dest_gcs_dir

    def execute(self, context):
        # -------------------------------
        # AWS S3
        # -------------------------------

        s3_hook = S3Hook(aws_conn_id=self.aws_conn_id)
        self.log.info('Check file {} from bucket: {} '.format(self.file_name, self.s3_bucket))

        file_exist = self.sensor_file_s3(s3_hook)

        if not file_exist:
            raise AirflowException(' File {} not found '.format(self.file_name))

        # if file exist
        # Download file from S3 bucket
        # GCS hook builds its own in-memory file, so we have to create
        # and pass the path

        file_object = s3_hook.get_key(key=self.file_name, bucket_name=self.s3_bucket)
        fd, local_filepath = tempfile.mkstemp(text=False)

        with open(local_filepath, 'wb') as f:
            file_object.download_fileobj(f)
            f.flush()
            local_filepath = self.remove_hash(local_filepath)
        self.log.info(
            'File downloaded from S3 bucket : {}/{} to {} '.format(self.s3_bucket, self.file_name, local_filepath))

        # -------------------------------
        # Postgres
        # -------------------------------
        pg_hook = PostgresHook(postgres_conn_id=self.postgres_conn_id, database=self.database)
        conn = pg_hook.get_conn()
        cursor = conn.cursor()

        # Add row to sql table
        file_name = os.path.basename(self.file_name)
        # Add / Update file to log table
        query_file_log = '''
            TRUNCATE TABLE {table} ;

            INSERT INTO  matrix__pmc_import.pmc_imported_files
            (file_name, create_date, update_date)
            VALUES ('{file_name}' , NOW(), NOW())
            ON CONFLICT (file_name) DO UPDATE
            SET update_date = NOW();

        '''.format(
            file_name=file_name,
            table=self.table)

        self.log.info('query_file_log: %s ', query_file_log)

        pg_hook.run(query_file_log)
        # output errors
        for output in pg_hook.conn.notices:
            self.log.info(output)

        # Truncate Table
        # cursor.execute('TRUNCATE TABLE {} ;'.format(self.table))

        cursor.close()
        conn.close()

        query = "COPY {table} (data) FROM STDIN WITH CSV QUOTE e'\x01' DELIMITER AS e'\x02' ".format(table=self.table)
        pg_hook.copy_expert(query, local_filepath)
        # output psql errors
        for output in pg_hook.conn.notices:
            self.log.info(output)
        self.log.info('File loaded into table %s.%s', self.database, self.table)

        # -------------------------------
        # GCS
        # -------------------------------
        dest_gcs_object = '{dir}/{filename}'.format(dir=self.dest_gcs_dir.strip('/'), filename=self.file_name)
        gcs_hook = GCSHook(gcp_conn_id=self.dest_gcs_conn_id)

        gcs_hook.upload(bucket_name=self.dest_gcs_bucket, object_name=dest_gcs_object, filename=local_filepath)

        self.log.info("File uploaded to gs://{}/{}".format(self.dest_gcs_bucket, dest_gcs_object))

        os.close(fd)
        os.unlink(local_filepath)

        return []

    def sensor_file_s3(self, s3_hook, **_):
        self.log.info('Checking for file name: {} in bucket s3://{}'.format(
            self.file_name,
            self.s3_bucket))

        return s3_hook.list_keys(
            bucket_name=self.s3_bucket,
            prefix=self.file_name,
            delimiter='/')

    def remove_hash(self, filepath, **_):
        self.log.info('Remove hash from file: {} '.format(filepath))
        cut_pos = None
        with open(filepath, 'r') as fd_in:
            first_line = fd_in.readline()
            fd_in.seek(0)  # reset file descriptor position

            if len(first_line) == 0:
                # if the file is empty
                return filepath

            if first_line[40] == '|':
                cut_pos = 41
            elif first_line[32] == '|':
                cut_pos = 33
            else:
                self.log.info('we don\'t have hash, continue ')
                self.log.info('32 fields ' + first_line[32])
                self.log.info('40 fields ' + first_line[40])
                return filepath

            # remove hash and write result into a temp file
            fd, tmp_filepath = tempfile.mkstemp(text=True)
            with open(tmp_filepath, 'w') as fd_out:
                for line in fd_in:
                    fd_out.write(line[cut_pos:])

        # swap temp and original file content
        os.unlink(filepath)
        shutil.move(tmp_filepath, filepath)

        return filepath
