# gsutil cp plugins/import_plugin/operators/partner_snapshot_import_operator.py gs://europe-west1-prod-mozart-co-2ef7e904-bucket/plugins/import_plugin/operators/partner_snapshot_import_operator.py

import os
import re

from airflow.models import BaseOperator
from airflow.providers.ftp.hooks.ftp import FTPSHook
from airflow.providers.google.cloud.hooks.bigquery import BigQueryHook
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from airflow.providers.sftp.hooks.sftp import SFTPHook


class PartnerSnapshotImportOperator(BaseOperator):
    """
    Call API Splio report and get all campaign statistics from date

    :param partner_name: Name of partner
    :type partner_name: string
    :parm partner_conf: Configuration of partner
    :type partner_conf: dict
    :parm remote_path: Dirname of server FTP
    :type remote_path: string
    :parm file_pattern: pattern of file search
    :type file_pattern: string (templated)
    :param parse_remote_filepath_func: a function which extract the consent public ref from remote conn_id and filename
    :type parse_remote_filepath_func: callable
    :param gcs_conn_id: GCS connection id of HTML source
    :type gcs_conn_id: string
    :param gcs_bucket: Bucket of HTML source
    :type gcs_bucket: string
    :param gcs_filepath_template: GCS full filepath, templated with format-like style for injecting consent_public_ref
    :type gcs_filepath_template: string
    :param bq_schema_fields: Schema of table loaded
    :type bq_schema_fields: list
    :param bq_table_template: BQ table name, in which we inject consent_public_Ref (format-like style)
    :type bq_table_template: string
    :param bq_conn_id:
    :type bq_conn_id: string
    """
    template_fields = ['file_pattern', 'gcs_filepath_template', 'bq_table_template']

    def __init__(self,
                 partner_name,
                 partner_conf,
                 remote_path,
                 file_pattern,
                 parse_remote_filepath_func,
                 gcs_conn_id,
                 gcs_bucket,
                 gcs_filepath_template,
                 bq_schema_fields,
                 bq_table_template,
                 bq_conn_id='bigquery_default',
                 *args,
                 **kwargs):
        super().__init__(*args, **kwargs)

        self.partner_name = partner_name
        self.partner_conf = partner_conf
        self.remote_path = remote_path
        self.file_pattern = file_pattern
        self.parse_remote_filepath_func = parse_remote_filepath_func

        # GCS Hook
        self.gcs_conn_id = gcs_conn_id
        self.gcs_bucket = gcs_bucket
        self.gcs_filepath_template = gcs_filepath_template

        # BQ Hook
        self.bq_conn_id = bq_conn_id
        self.bq_schema_fields = bq_schema_fields
        self.bq_table_template = bq_table_template

    def execute(self, context):
        local_path = '/home/<USER>/gcs/data/tmp' + self.partner_name
        if not os.path.exists(local_path):
            os.makedirs(local_path)

        self.log.info('file_pattern : {}'.format(self.file_pattern))

        gcs_hook = GCSHook(gcp_conn_id=self.gcs_conn_id)

        bq_hook = BigQueryHook(gcp_conn_id=self.bq_conn_id, use_legacy_sql=True)

        conf_items = self.partner_conf['data'].items()
        for conf_item_nb, (remote_conn_id, data) in enumerate(conf_items):
            # 1. List files on remote, and make a check
            # ----------------------------------------- #
            remote_path = os.path.normpath(data['path_root'] + '/' + self.remote_path)
            self.log.info('remote_path : ' + remote_path)

            remote_hook = self.get_remote_hook(remote_conn_id)
            list_files = remote_hook.list_directory(remote_path)

            list_files = [os.path.basename(e) for e in list_files if os.path.basename(e) not in ('.', '..')]
            self.log.info('list_files : {}'.format(list_files))

            # filter files on file_pattern
            list_files = self.filter_files(list_files, patterns=[self.file_pattern], mode_include=True)
            self.log.info('Files after filter pattern apply' + str(list_files))

            if len(list_files) == 0:
                raise ValueError(
                    'Directory files not found from partner {} dir : {} '.format(remote_conn_id, remote_path))

            for i, file in enumerate(list_files):
                remote_filepath = remote_path + '/' + file
                consent_public_ref = self.parse_remote_filepath_func(remote_conn_id, remote_filepath)
                self.log.info(
                    "[sftp conf item {:0>5d} / {:0>5d} - consent {:0>5d} / {:0>5d}] \'{:s}\'" \
                        .format(conf_item_nb + 1, len(conf_items),
                                i + 1, len(list_files),
                                consent_public_ref
                                )
                )
                gcs_filepath = self.gcs_filepath_template.format(consent_public_ref=consent_public_ref)
                bq_table = self.bq_table_template.format(consent_public_ref=consent_public_ref)
                local_filename = os.path.basename(gcs_filepath)

                # 2. Download remote file
                # ----------------------- #
                self.log.info('Downloading using {} conn_id : {} to {}'.format(
                    remote_conn_id,
                    remote_filepath,
                    local_filename))
                remote_hook.retrieve_file(remote_filepath, '{}/{}'.format(local_path, local_filename))
                remote_hook.close_conn()

                # 3. Save to GCS
                # -------------- #
                self.log.info('Uploading to GCS gs://{}/{}'.format(self.gcs_bucket, gcs_filepath))
                gcs_hook.upload(
                    bucket_name=self.gcs_bucket,
                    object_name=gcs_filepath,
                    mime_type='application/octet-stream',
                    filename='{}/{}'.format(local_path, local_filename),
                    gzip=False
                )

                # 4. Load data to BQ
                # ------------------ #
                self.log.info('Loading to BQ table {}'.format(bq_table))
                bq_hook.run_load(
                    destination_project_dataset_table=bq_table,
                    schema_fields=self.bq_schema_fields,
                    source_uris=['gs://{}/{}'.format(self.gcs_bucket, gcs_filepath)],
                    skip_leading_rows=1,
                    write_disposition='WRITE_TRUNCATE',
                    field_delimiter=';'
                )

                # clean
                os.remove('{}/{}'.format(local_path, local_filename))

    def get_remote_hook(self, remote_conn_id):
        """
        Try to get the hook to connect to remote host, depending on configuration.


        :param remote_conn_id: Remote connection airflow id
        :type remote_conn_id: str
        :return: Connection hook
        :rtype: SFTPHook|FTPSHook
        """
        self.log.info('Connection to {} using conn_id : {}'.format(self.partner_conf['protocol'], remote_conn_id))
        if self.partner_conf['protocol'] == 'sftp':
            hook = SFTPHook(ftp_conn_id=remote_conn_id)
        elif self.partner_conf['protocol'] == 'ftps':
            hook = FTPSHook(ftp_conn_id=remote_conn_id)
        else:
            raise ValueError("Cannot create connexion hook for {}".format(remote_conn_id))

        return hook

    def filter_files(self, files, patterns=[], mode_include=True):
        """
        Filter a list of files, against pattern_list, including or excluding files depending on mode_include.

        :param files: list of file names
        :type files: list
        :param patterns: list of regexp
        :type patterns: list
        :param mode_include: True : include, False ; exclude
        :type mode_include: bool
        """

        files_to_keep = []

        for file in files:
            keep = not mode_include
            for pattern in patterns:
                if re.match(pattern, file):
                    keep = mode_include
                    continue

            if keep:
                files_to_keep.append(file)

        return files_to_keep
