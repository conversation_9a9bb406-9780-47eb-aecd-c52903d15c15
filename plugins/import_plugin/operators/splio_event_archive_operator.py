#
# gsutil cp plugins/import_plugin/operators/splio_event_import_operator.py gs://europe-west1-prod-mozart-co-fd73c443-bucket/plugins/import_plugin/operators/
#
import gzip
import os
import re
import shutil

from airflow.models import BaseOperator
from airflow.providers.google.cloud.hooks.bigquery import BigQueryHook
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from import_plugin.hooks.big_sftp_hook import BigSFTPHook
from airflow.utils.email import send_email

env = os.environ.get("ENV")

class SplioEventArchiveOperator(BaseOperator):
    """
    Download et ingest Splio events into BQ
    ...

    Attributes
    ----------
    gcs_conn_id : string
        GCS connection id of report source

    bucket : string
        Bucket of report source

    mapping_sftp : string
        List splio universes connection_id

    bq_schema_fields : dict
        Schema of BQ tables

    bigquery_conn_id : string
        GCS bigquery connection id of report source

    Methods
    -------
    filter_files(self, files, patterns=[], mode_include=True) : list
        Filter a list of files, against pattern_list, including or excluding files depending on mode_include.
    """

    template_fields = ['execution_date']

    def __init__(self,
                 execution_date,
                 gcs_conn_id,
                 bucket,
                 mapping_sftp,
                 bq_schema_fields,
                 bigquery_conn_id='bigquery_default',
                 *args,
                 **kwargs):
        super().__init__(*args, **kwargs)

        self.execution_date = execution_date
        self.mapping_sftp = mapping_sftp
        self.schema_fields = bq_schema_fields

        # GCS Hook
        self.gcs_conn_id = gcs_conn_id
        self.bucket = bucket

        # BQ Hook
        self.bigquery_conn_id = bigquery_conn_id

    def execute(self, context):
        local_path = '/home/<USER>/gcs/data/tmp/splio_event/' + self.execution_date
        sftp_path = '/events'
        splio_error_filename = []
        splio_errors = []

        if not os.path.exists(local_path):
            os.makedirs(local_path, exist_ok=False)

        self.log.info('local_path : {}'.format(local_path))

        gcs_hook = GCSHook(
            gcp_conn_id=self.gcs_conn_id,
            delegate_to=None)

        bq_hook = BigQueryHook(gcp_conn_id=self.bigquery_conn_id,
                               delegate_to=None,
                               use_legacy_sql=True)

        for universe_name, ftp_conn_id in self.mapping_sftp.items():
            file_pattern = universe_name + '_.*.gz'

            # Create a single SFTP hook for all operations
            sftp = None
            try:
                # Initialize the SFTP connection
                sftp = BigSFTPHook(ftp_conn_id=ftp_conn_id)
                self.log.info('Connection to SFTP using conn_id : {}'.format(ftp_conn_id))

                # Function to refresh connection if needed
                def ensure_connection():
                    nonlocal sftp
                    try:
                        # Test if connection is still alive
                        if hasattr(sftp, 'conn') and sftp.conn is not None:
                            # Try a simple operation to test connection
                            sftp.conn.get_channel()
                            return True
                    except Exception as e:
                        self.log.warning(f"Connection test failed: {str(e)}. Will refresh connection.")
                        # Close old connection if it exists
                        try:
                            if hasattr(sftp, 'conn') and sftp.conn is not None:
                                sftp.conn.close()
                        except Exception:
                            pass  # Ignore errors when closing

                        # Create a new connection
                        sftp = BigSFTPHook(ftp_conn_id=ftp_conn_id)
                        self.log.info("SFTP connection refreshed")
                        return True
                    return True

                # Get the connection
                ensure_connection()
                pysftp_conn = sftp.get_conn()
                # fix for getting large quantity of big files
                # see https://github.com/paramiko/paramiko/issues/151#issuecomment-572797949
                # Very ugly, but we don't have another choice to fix this paramiko issue
                # pysftp_conn._transport.default_window_size = paramiko.common.MAX_WINDOW_SIZE
                # pysftp_conn._transport.packetizer.REKEY_BYTES = pow(2, 40)
                # pysftp_conn._transport.packetizer.REKEY_PACKETS = pow(2, 40)
                # end of fix

                # Check for events directory
                ensure_connection()
                list_dir = sftp.list_directory('/')
                if list_dir.count('events') == 0:
                    splio_error_filename.append('Directory /events not found for ' + universe_name + ' universe')
                    continue

                # Check and create archive directory if needed
                ensure_connection()
                list_dir = sftp.list_directory('/events/')
                if list_dir.count('archive') == 0:
                    try:
                        ensure_connection()
                        pysftp_conn = sftp.get_conn()
                        pysftp_conn.mkdir('/events/archive')
                        self.log.info(f"Created directory /events/archive for {universe_name}")
                    except Exception as e:
                        error_msg = f"Can't create folder /events/archive for universe {universe_name}. Error: {str(e)}"
                        self.log.error(error_msg)
                        splio_errors.append(error_msg)
                        continue
                    splio_error_filename.append(
                        'Directory /events/archive not found for ' + universe_name + ' universe. Created now')

                # Get list of files to process
                ensure_connection()
                list_files = sftp.list_directory(sftp_path)

                # filter files on file_pattern
                list_files = self.filter_files(list_files, patterns=[file_pattern], mode_include=True)

                if list_files:
                    self.log.info('Files after filter pattern apply' + str(list_files))
                else:
                    self.log.info('No files found for universe_name ={universe_name} with pattern = {pattern} '.format(
                        universe_name=universe_name,
                        pattern=file_pattern
                    ))
                    splio_errors.append('No files found for universe_name ={universe_name} with pattern = {pattern} '.format(
                        universe_name=universe_name,
                        pattern=file_pattern
                    ))

                for file in list_files:
                    local_filename = local_path + '/' + file
                    sftp_filename = sftp_path + '/' + file
                    self.log.info('Move file using {} conn_id : remote={} local={}'.format(
                        ftp_conn_id,
                        sftp_filename,
                        local_filename
                    ))

                    # Step 2: Move the file to archive (using rename directly with pysftp_conn)
                    try:
                        # Use pysftp_conn directly to rename the file
                        ensure_connection()
                        pysftp_conn = sftp.get_conn()
                        self.log.info(f"Moving file {sftp_filename} to {sftp_path + '/archive/' + file}")

                        # Try to use rename directly
                        try:
                            self.log.info(f"Try to rename file using pysftp_conn.rename.")
                            pysftp_conn.rename(sftp_filename, sftp_path + '/archive/' + file)
                            self.log.info(f"Successfully moved file {file} to archive using rename")
                        except Exception as rename_e:
                            self.log.warning(f"Rename operation failed: {str(rename_e)}. Trying alternative method.")

                            # If rename fails, try to download and re-upload
                            # First check if we already have the file locally
                            if not os.path.exists(local_filename):
                                # Download the file first
                                self.log.info(f"Downloading file to {local_filename}")
                                pysftp_conn.get(sftp_filename, local_filename)
                                self.log.info(f"Downloaded file to {local_filename}")

                            # Upload to archive location
                            self.log.info(f"Try to rename file using pysftp_conn.put.")
                            self.log.info(f"Uploading file to archive at {sftp_path + '/archive/' + file}")
                            pysftp_conn.put(local_filename, sftp_path + '/archive/' + file)
                            self.log.info(f"Uploaded file to archive")

                            # Delete the original file
                            self.log.info(f"Removing original file at {sftp_filename}")
                            pysftp_conn.remove(sftp_filename)
                            self.log.info(f"Removed original file")

                            self.log.info(f"Successfully moved file {file} to archive using get/put/remove")
                    except Exception as e:
                        error_msg = f"Can't archive file {sftp_filename} for universe {universe_name}. Error: {str(e)}"
                        self.log.error(error_msg)
                        self.log.error(f"Error details: {type(e).__name__}")

                        # Add specific handling for the 'Socket is closed' error
                        if "Socket is closed" in str(e) or "End Of File (EOF)" in str(e):
                            self.log.warning(f"Detected connection error: {str(e)}. Will refresh connection and retry.")
                            try:
                                # Force connection refresh
                                if hasattr(sftp, 'conn') and sftp.conn is not None:
                                    try:
                                        sftp.conn.close()
                                    except Exception:
                                        pass  # Ignore errors when closing

                                # Create a new connection
                                sftp = BigSFTPHook(ftp_conn_id=ftp_conn_id)
                                pysftp_conn = sftp.get_conn()

                                # Try the simplest approach first - rename
                                try:
                                    self.log.info(f"Retrying move with rename for {file}")
                                    pysftp_conn.rename(sftp_filename, sftp_path + '/archive/' + file)
                                    self.log.info(f"Successfully moved file {file} to archive on retry")
                                except Exception as retry_rename_e:
                                    self.log.warning(f"Retry rename failed: {str(retry_rename_e)}. Using alternative method.")

                                    # If rename fails, try to download and re-upload
                                    if not os.path.exists(local_filename):
                                        # Download the file first
                                        self.log.info(f"Downloading file to {local_filename}")
                                        pysftp_conn.get(sftp_filename, local_filename)

                                    # Upload to archive location
                                    pysftp_conn.put(local_filename, sftp_path + '/archive/' + file)

                                    # Delete the original file
                                    pysftp_conn.remove(sftp_filename)

                                    self.log.info(f"Successfully moved file {file} to archive on retry using get/put/remove")
                            except Exception as retry_e:
                                error_msg = f"Archive retry failed for {sftp_filename}: {str(retry_e)}"
                                self.log.error(error_msg)
                                splio_errors.append(error_msg)
                                # Continue processing since we already have the file locally
                        else:
                            splio_errors.append(error_msg)
                            continue
            finally:
                # Close the SFTP connection
                try:
                    if sftp and hasattr(sftp, 'conn') and sftp.conn is not None:
                        self.log.info(f"Closing SFTP connection for {universe_name}")
                        sftp.conn.close()
                        self.log.info(f"Successfully closed SFTP connection for {universe_name}")
                except Exception as e:
                    error_msg = f"Error closing SFTP connection for {universe_name}: {str(e)}"
                    self.log.error(error_msg)
                    self.log.error(f"Error details: {type(e).__name__}")
                    splio_errors.append(error_msg)

        alert_body = []
        alert_title = '[Airflow - {env}] Erreur d\'archivage des fichiers events dans le SFTP Splio'.format(env=env)
        if splio_error_filename:
            self.log.info("End of import with filename errors !! : {}".format(str(splio_error_filename)))
            alert_body.append(splio_error_filename)
        if splio_errors:
            self.log.info("End of import with errors !! : {}".format(str(splio_errors)))
            alert_body.append(splio_errors)

        if alert_body:
            alert_to = ['<EMAIL>']
            if env != 'prod':
                alert_to = ['<EMAIL>']

            send_email(to=alert_to, subject=alert_title, html_content=str(alert_body),
                       mime_charset='utf-8', conn_id='sendgrid_default')

    def filter_files(self, files, patterns=[], mode_include=True):
        """
        Filter a list of files, against pattern_list, including or excluding files depending on mode_include.

        file: list
            list of file names
        patterns: list
            list of regexp
        mode_include: Bool
            True : include, False ; exclude
        """

        files_to_keep = []

        for file in files:
            keep = not mode_include
            for pattern in patterns:
                if re.match(pattern, file):
                    keep = mode_include
                    continue

            if keep:
                files_to_keep.append(file)

        return files_to_keep
