#
# gsutil cp plugins/import_plugin/hooks/big_sftp_hook.py gs://europe-west1-prod-mozart-co-fd73c443-bucket/plugins/import_plugin/hooks/
#
import datetime
import tempfile

import pexpect
from airflow.providers.sftp.hooks.sftp import SFTPHook


class BigSFTPHook(SFTPHook):
    """
    This hook is inherited from SFTP hook. Please refer to SFTP/SSH hook for the input
    arguments.
    Interact with SFTP to retrieve/store bigs files, using pexpect-u and sftp command line tool.
    Aims to be interchangeable with STPHook.
    Note: does not support sshkey FTM
    """

    def __init__(self, ftp_conn_id='sftp_default', *args, **kwargs):
        super(BigSFTPHook, self).__init__(ftp_conn_id, *args, **kwargs)
        # deal with timeout for pexpect
        self.timeout = 30
        if 'execution_timeout' in kwargs:
            if type(kwargs['execution_timeout']) == datetime.timedelta:
                self.timeout = kwargs['execution_timeout'].total_seconds()
            else:
                self.timeout = int(kwargs['execution_timeout'])
        # build sftp/ssh options
        self.sftp_options = ['-oUserKnownHostsFile=/dev/null', '-oPubkeyAuthentication=no']
        if self.no_host_key_check:
            self.sftp_options.append('-oStrictHostKeyChecking=no')
        self.sftp_options = ' '.join(self.sftp_options)

    def _sftp(self, sftp_cmd):
        """
        Launch a SFTP command to send/retrieve a file
        Throws an exception if the command doesn't return 0.
        SFTP outpus are send to airflow logs
        source: https://stackoverflow.com/a/15167643
        see: https://pypi.org/project/pexpect-u/
        Params:
            - sftp_cmd (str) : SFTP command to execute

        For python 3, spawnu should be used instead of spawn 
        according to stackoverflow.com/a/26835855/2118777 to avoid write() argument must be str, not bytes 
        """
        fname = tempfile.mktemp()
        fout = open(fname, 'w')
        child = pexpect.spawnu(sftp_cmd, timeout=None)  # spawnu for Python 3
        child.expect(['[pP]assword: '])
        child.sendline(self.password)
        child.logfile = fout
        child.expect(pexpect.EOF)
        child.close()
        fout.close()
        # send output lines to airflow logs
        with open(fname, 'r') as fin:
            for line in fin.readline():
                self.log.info(line)

        if 0 != child.exitstatus:
            self.log.info('child : ' + str(child))
            self.log.info('child.exitstatus : ' + str(child.exitstatus))
            return Exception('Error in SFTP command line ; %s' % sftp_cmd)

    def retrieve_file(self, remote_full_path, local_full_path):
        """
        Transfers the remote file to a local location.
        If local_full_path is a string path, the file will be put
        at that location
        Params:
            - remote_full_path (str): full path to the remote file
            - local_full_path (str): full path to the local file
        """
        sftp_cmd = 'sftp {options} {user}@{host}:"{remote_file}" "{local_file}"'.format(
            options=self.sftp_options,
            user=self.username,
            host=self.remote_host,
            remote_file=remote_full_path,
            local_file=local_full_path)
        self._sftp(sftp_cmd)

    def store_file(self, remote_full_path, local_full_path):
        """
        Transfers a local file to the remote location.
        If local_full_path_or_buffer is a string path, the file will be read
        from that location
        Params:
            - remote_full_path (str): full path to the remote file
            - local_full_path (str): full path to the local file
        """
        sftp_cmd = 'sftp {options} "{local_file}" {user}@{host}:"{remote_file}" '.format(
            options=self.sftp_options,
            user=self.username,
            host=self.remote_host,
            remote_file=remote_full_path,
            local_file=local_full_path)
        self._sftp(sftp_cmd)
