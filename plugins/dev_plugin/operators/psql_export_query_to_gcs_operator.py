# gsutil cp production/plugins/dev_plugin/operators/psql_export_query_to_gcs_operator.py gs://europe-west1-mozart-cluster-4128dbb8-bucket/plugins/dev_plugin/operators/

# -*- coding: utf-8 -*-
#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

import datetime
import json
import sys
import time
from decimal import Decimal
from tempfile import NamedTemporaryFile

import unicodecsv as csv
from airflow.models import BaseOperator
from airflow.providers.google.cloud.hooks.gcs import G<PERSON>Hook
from airflow.providers.postgres.hooks.postgres import PostgresHook

PY3 = sys.version_info[0] == 3


class PostgresQueryExportToGoogleCloudStorageOperator(BaseOperator):
    """
    Copy data from Postgres to Google Cloud Storage in JSON/CSV format.

    :param sql: The SQL to execute on the Postgres table.
    :type sql: string
    :param bucket: The bucket to upload to.
    :type bucket: string
    :param filename: The filename to use as the object name when uploading
        to Google Cloud Storage. A {} should be specified in the filename
        to allow the operator to inject file numbers in cases where the
        file is split due to size.
    :type filename: string
    :param schema_filename: If set, the filename to use as the object name
        when uploading a .json file containing the BigQuery schema fields
        for the table that was dumped from Postgres.
    :type schema_filename: string
    :param approx_max_file_size_bytes: This operator supports the ability
        to split large table dumps into multiple files (see notes in the
        filenamed param docs above). Google Cloud Storage allows for files
        to be a maximum of 4GB. This param allows developers to specify the
        file size of the splits.
    :type approx_max_file_size_bytes: long
    :param postgres_conn_id: Reference to a specific Postgres hook.
    :type postgres_conn_id: string
    :param delegate_to: The account to impersonate, if any. For this to
        work, the service account making the request must have domain-wide
        delegation enabled.
    :param parameters: a parameters dict that is substituted at query runtime.
    :type parameters: dict

    """
    template_fields = ('sql', 'bucket', 'filename', 'schema_filename',
                       'parameters')
    template_ext = ('.sql',)
    ui_color = '#a0e08c'

    def __init__(self,
                 sql,
                 bucket,
                 filename,
                 database,
                 schema_filename=None,
                 approx_max_file_size_bytes=**********,
                 export_format='csv',
                 field_delimiter=';',
                 gzip=False,
                 postgres_conn_id='postgres_default',
                 gcp_conn_id='google_cloud_default',
                 delegate_to=None,
                 parameters=None,
                 multipart=True,
                 *args, **kwargs):
        super(PostgresQueryExportToGoogleCloudStorageOperator, self).__init__(*args, **kwargs)

        # PSQL Configs
        self.sql = sql
        self.postgres_conn_id = postgres_conn_id
        self.delegate_to = delegate_to
        self.parameters = parameters
        self.database = database
        # GCP/GCS Configs
        self.bucket = bucket
        self.gcp_conn_id = gcp_conn_id
        self.multipart = multipart
        # Export Config
        self.export_format = export_format.lower()
        self.field_delimiter = field_delimiter
        self.gzip = gzip
        self.filename = filename
        self.schema_filename = schema_filename
        self.approx_max_file_size_bytes = approx_max_file_size_bytes

    def execute(self, context):
        cursor = self._query_postgres()
        self.log.info('Prepare local file.')

        files_to_upload = self._write_local_data_files(cursor)
        # If a schema is set, create a BQ schema JSON file.
        if self.schema_filename:
            files_to_upload.update(self._write_local_schema_file(cursor))

        # Flush all files before uploading
        for tmp_file in files_to_upload:
            tmp_file['file_handle'].flush()
        self.log.info('Upload to GCS.')
        self._upload_to_gcs(files_to_upload)

        # Close all temp file handles.
        for tmp_file in files_to_upload:
            tmp_file['file_handle'].close()

        cursor.close()

    def _query_postgres(self):
        """
        Queries Postgres and returns a cursor to the results.
        """
        pg_hook = PostgresHook(postgres_conn_id=self.postgres_conn_id, database=self.database)
        conn = pg_hook.get_conn()
        cursor = conn.cursor()
        self.log.info(str(self.sql))
        cursor.execute(self.sql, self.parameters)
        # output errors
        for output in pg_hook.conn.notices:
            self.log.info(output)
        self.log.info('Query has been executed successfully.')
        return cursor

    def _write_local_data_files(self, cursor):
        """
        Takes a cursor, and writes results to a local file.

        :return: A dictionary where keys are filenames to be used as object
            names in GCS, and values are file handles to local files that
            contain the data for the GCS objects.
        """
        schema = list(map(lambda schema_tuple: schema_tuple[0], cursor.description))
        col_type_dict = self._get_col_type_dict()
        file_no = 0
        tmp_file_handle = NamedTemporaryFile(delete=True)
        if self.export_format == 'csv':
            file_mime_type = 'text/csv'
        else:
            file_mime_type = 'application/json'
        files_to_upload = [{
            'file_name': self.filename.format(file_no),
            'file_handle': tmp_file_handle,
            'file_mime_type': file_mime_type
        }]

        if self.export_format == 'csv':
            csv_writer = self._configure_csv_file(tmp_file_handle, schema)

        for row in cursor:
            # Convert datetime objects to utc seconds, and decimals to floats.
            # Convert binary type object to string encoded with base64.
            row = self.convert_types(schema, col_type_dict, row)

            if self.export_format == 'csv':
                csv_writer.writerow(row)
            else:
                row_dict = dict(zip(schema, row))

                # TODO validate that row isn't > 2MB. BQ enforces a hard row size of 2MB.
                tmp_file_handle.write(json.dumps(row_dict, sort_keys=True).encode('utf-8'))

                # Append newline to make dumps BigQuery compatible.
                tmp_file_handle.write(b'\n')

            # Stop if the file exceeds the file size limit.
            if tmp_file_handle.tell() >= self.approx_max_file_size_bytes:
                self.log.info('file exceeds the file size limit. STOP !!')
                file_no += 1
                tmp_file_handle = NamedTemporaryFile(delete=True)
                files_to_upload.append({
                    'file_name': self.filename.format(file_no),
                    'file_handle': tmp_file_handle,
                    'file_mime_type': file_mime_type
                })

                if self.export_format == 'csv':
                    csv_writer = self._configure_csv_file(tmp_file_handle, schema)

        return files_to_upload

    def _write_local_schema_file(self, cursor):
        """
        Takes a cursor, and writes the BigQuery schema for the results to a
        local file system.

        :return: A dictionary where key is a filename to be used as an object
            name in GCS, and values are file handles to local files that
            contains the BigQuery schema fields in .json format.
        """
        schema = [self.field_to_bigquery(field) for field in cursor.description]

        self.log.info('Using schema for %s: %s', self.schema_filename, schema)
        tmp_schema_file_handle = NamedTemporaryFile(delete=True)
        tmp_schema_file_handle.write(json.dumps(schema, sort_keys=True).encode('utf-8'))
        schema_file_to_upload = {
            'file_name': self.schema_filename,
            'file_handle': tmp_schema_file_handle,
            'file_mime_type': 'application/json',
        }
        return schema_file_to_upload

    def _configure_csv_file(self, file_handle, schema):
        """Configure a csv writer with the file_handle and write schema
        as headers for the new file.
        """
        csv_writer = csv.writer(file_handle, encoding='utf-8',
                                delimiter=self.field_delimiter)
        csv_writer.writerow(schema)
        return csv_writer

    def _upload_to_gcs(self, files_to_upload):
        """
        Upload all the file splits (and optionally the schema .json file) to
        Google Cloud Storage.
        """
        gcs_hook = GCSHook(
            gcp_conn_id=self.gcp_conn_id,
            delegate_to=self.delegate_to)

        for tmp_file in files_to_upload:
            self.log.info("Upload File to gs://{}/{}".format(self.bucket, self.filename))
            gcs_hook.upload(
                bucket_name=self.bucket,
                object_name=tmp_file.get('file_name'),
                filename=tmp_file.get('file_handle').name,
                mime_type=tmp_file.get('file_mime_type'),
                gzip=self.gzip if tmp_file.get('file_name') == self.schema_filename else False)

    def convert_types(self, schema, col_type_dict, row):
        """Convert values from DBAPI to output-friendly formats."""
        return [
            self.convert_type(value, col_type_dict.get(name))
            for name, value in zip(schema, row)
        ]

    def convert_type(self, value, schema_type):
        """
        Takes a value from Postgres, and converts it to a value that's safe for
        JSON/Google Cloud Storage/BigQuery. Dates are converted to UTC seconds.
        Decimals are converted to floats. Times are converted to seconds.
        """
        if isinstance(value, (datetime.datetime, datetime.date)):
            return time.mktime(value.timetuple())
        if isinstance(value, datetime.time):
            formated_time = time.strptime(str(value), "%H:%M:%S")
            return datetime.timedelta(
                hours=formated_time.tm_hour,
                minutes=formated_time.tm_min,
                seconds=formated_time.tm_sec).seconds
        if isinstance(value, Decimal):
            return float(value)
        return value

    @classmethod
    def type_map(cls, postgres_type):
        """
        Helper function that maps from Postgres fields to BigQuery fields. Used
        when a schema_filename is set.
        """
        d = {
            1114: 'TIMESTAMP',
            1184: 'TIMESTAMP',
            1082: 'TIMESTAMP',
            1083: 'TIMESTAMP',
            1005: 'INTEGER',
            1007: 'INTEGER',
            1016: 'INTEGER',
            20: 'INTEGER',
            21: 'INTEGER',
            23: 'INTEGER',
            16: 'BOOLEAN',
            700: 'FLOAT',
            701: 'FLOAT',
            1700: 'FLOAT'
        }

        return d[postgres_type] if postgres_type in d else 'STRING'

    def _get_col_type_dict(self):
        """
        Return a dict of column name and column type based on self.schema_filename if not None.
        """
        schema = []
        if isinstance(self.schema_filename, str):
            schema = json.loads(self.schema_filename)
        elif isinstance(self.schema_filename, list):
            schema = self.schema_filename
        elif self.schema_filename is not None:
            self.log.warning('Using default schema due to unexpected type.'
                             'Should be a string or list.')

        col_type_dict = {}
        try:
            col_type_dict = {col['name']: col['type'] for col in schema}
        except KeyError:
            self.log.warning('Using default schema due to missing name or type. Please '
                             'refer to: https://cloud.google.com/bigquery/docs/schemas'
                             '#specifying_a_json_schema_file')
        return col_type_dict
