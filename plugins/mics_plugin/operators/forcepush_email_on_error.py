import csv
import json

import requests as req
from airflow.models import BaseOperator
from airflow.providers.google.cloud.hooks.bigquery import BigQueryHook


class ForcePushEmailOnError(BaseOperator):
    """
    This operator gathers error logs to extract email_md5 on error, which we remove from mediarithmics snapshots in
    order to push those profiles again.
    ...

    Arguments:
        mics_snapshot_bq_table (str): bq_table from which we delete some email on retry
        is_dry_run (bool): if we run a dry run
        bigquery_conn_id (str): should be an airflow connection id
        delegate_to (str): None
    """

    ui_color = '#f0eee4'

    def __init__(self,
                 mics_snapshot_bq_table,
                 is_dry_run,
                 mics_api_key,
                 bigquery_conn_id='bq_matrix',
                 delegate_to=None,
                 *args,
                 **kwargs):
        super(ForcePushEmailOnError, self).__init__(*args, **kwargs)

        self.mics_snapshot_bq_table = mics_snapshot_bq_table
        self.bigquery_conn_id = bigquery_conn_id
        self.delegate_to = delegate_to
        self.is_dry_run = is_dry_run
        self.bq_hook = BigQueryHook(gcp_conn_id=self.bigquery_conn_id,
                                    delegate_to=self.delegate_to, use_legacy_sql=False)
        self.mics_api_key = mics_api_key

    def execute(self, context, **kwargs):

        # We need to create the table : `pm-preprod-matrix.temp.mediarithmics`
        # If we are in dry run mode

        # We retrieve email with error from mics API

        self.log.info('Retrieving email_md5 from log error')
        email_list = self.get_email_hashes_to_send_again()
        self.log.info('Email_md5 from log error successfully retrieved')

        if len(email_list) == 0:
            self.log.info("Nothing to do, go to next task")
            return

        # The table as to be present otherwize, this operator is useless.
        # We have no problem for dry run but we can have one if the table
        # `pm-prod-matrix.export_dmp.mediarithmics` is deleted

        query = "DELETE FROM `{mics_snapshot_bq_table}` WHERE email_md5 IN ({email_error})".format(
            email_error=",".join(['"{}"'.format(email) for email in email_list]),
            mics_snapshot_bq_table=self.mics_snapshot_bq_table)

        # We deleted email_md5 with error from mediarithmics snapshot table to force a push of this email_md5

        self.log.info("BigQuery query : " + query)

        try:
            self.bq_hook.run_query(sql=query)
        except:
            # DELETE query may not work in dry-run, as we work with `temp` dataset.
            if not self.is_dry_run:
                raise Exception("Can't delete from the prod table")
            if self.is_dry_run:
                raise Exception("Dry is true, temp dataset don't exist")

    def get_exec_error_file_uris(self) -> list:
        """
        List all execution of profile_crm_v2 and extract error file URIs.

        We use API route https://api.mediarithmics.com/v1/datamarts/{datamartId}/document_imports/{importId}/executions
        with datamartId = 1279 (Prisma) and importId = 52729 (profile_crm_v2).

        Returns:
            List of mics:// uris to error files
        """
        API_HEADERS = {
            'Authorization': self.mics_api_key,
            'Cache-Control': 'no-cache',
        }

        api_url = "https://api.mediarithmics.com/v1/datamarts/{datamartId}/document_imports/{importId}/executions".format(
            datamartId=1279, importId=52729)

        response = None
        try:
            response = req.get(api_url, headers=API_HEADERS)
        except:
            raise Exception("Request error")
        finally:
            if response and (response.status_code != 200 or response.json()['status'] != 'ok'):
                print(str(response))
                print(response.headers)
                print(response.text)
                raise Exception("Mediarithmics API returns an error")

        result = []
        for item in response.json().get('data', []):
            try:
                error_file_uri = item.get('result', {}).get('error_file_uri')
                if error_file_uri:
                    result.append(error_file_uri)
                    print(error_file_uri)
            except:
                pass

        return result

    def get_email_hashes_to_send_again(self) -> list:
        """
        Download and parse each provided error files and extract profile_crm_v2 ids from them.
        We will send again those profiles.

        We use api route https://api.mediarithmics.com/v1/data_file/data?uri={error_file_uri}.

        Error file contains lines like this :
        ```
        error_message,input_line
        An unexpected error occurred. Please contact the support team with the error id. Error id = Some(7ffa7354-0541-4368-adfa-ff1c059ad2b3).,"{...""email_hash"":""8118bcb639de64e80f50f10c883e6fe3"",...}"
        An unexpected error occurred. Please contact the support team with the error id. Error id = Some(a7b20b0b-5ae6-4dbd-b0ac-539ac2b3e9b5).,"{... pushed-json-doc ...}"
        ...
        ```

        Returns:
            List of profile_crm_v2 ids to send again
        """

        API_HEADERS = {
            'Authorization': self.mics_api_key,
            'Cache-Control': 'no-cache',
        }

        error_file_uris = self.get_exec_error_file_uris()

        email_hashes = set()

        for uri in error_file_uris:
            api_url = "https://api.mediarithmics.com/v1/data_file/data?uri={error_file_uri}".format(error_file_uri=uri)
            try:
                response = req.get(api_url, headers=API_HEADERS)
            except:
                raise Exception("Request error")
            lines = response.text.splitlines()

            reader = csv.DictReader(lines, delimiter=',')

            for row in reader:
                try:
                    input_line = json.loads(row['input_line'])
                except json.JSONDecodeError:
                    # ignore json decode error
                    continue
                email_hashes.add(input_line.get('email_hash'))

        email_hashes = list(email_hashes)

        self.log.info('We had {} email from error log'.format(len(email_hashes)))

        return email_hashes
