import re
from typing import Iterable

import pandas as pd
from airflow.models import BaseOperator
from airflow.providers.google.cloud.hooks.bigquery import BigQueryHook


class GoogleCloudStorageLineCount(BaseOperator):
    """
    Get all the files in the
    """
    template_fields = ('bucket', 'prefix', 'delimiter')  # type: Iterable[str]

    def __init__(self,
                 bucket,
                 prefix='',
                 delimiter=None,
                 gcp_conn_id='google_cloud_default',
                 bigquery_conn_id='bq_matrix',
                 delegate_to=None,
                 file_list=[],
                 *args,
                 **kwargs):
        super().__init__(*args, **kwargs)

        self.bucket = bucket
        self.prefix = prefix
        self.delimiter = delimiter
        self.gcp_conn_id = gcp_conn_id
        self.delegate_to = delegate_to
        self.file_list = file_list
        self.bigquery_conn_id = bigquery_conn_id

    def execute(self, context):
        self.log.info('File list is ' + str(self.file_list))

        hook = BigQueryHook(gcp_conn_id=self.bigquery_conn_id,
                            delegate_to=None, use_legacy_sql=False)

        conn = hook.get_conn()
        cursor_total = conn.cursor()

        for gcs_file in self.file_list:
            self.log.info('We are processing this file via Pandas : ' + str(gcs_file))

            # Ouvrir le fichier (pandas)
            file_df = pd.read_csv(gcs_file)
            # Compter le nombre de lignes
            linecount = len(file_df)
            # Print le log
            self.log.info('Le fichier ' + str(gcs_file) + ' a ' + str(linecount) + ' lignes selon Pandas.')

            #
            file_date = re.findall('\d{4}.\d{2}.\d{2}', gcs_file)[0]
            partner = re.search('\d{4}.\d{2}.\d{2}/(.*?)/', gcs_file).group(1)
            consent = re.search('sub_(.*?)_', gcs_file).group(1)
            in_out = 'out'
            file_name = gcs_file.split('/')[-1]
            file_path = gcs_file.replace(file_name, '')

            self.log.info('Path : ' + file_path + '/n' +
                          'Name : ' + file_name + '/n' +
                          'Consent : ' + consent + '/n' +
                          'Partner : ' + partner + '/n' +
                          'Date : ' + file_date + '/n' +
                          'Direction : ' + in_out + '. /n')

            sql = """ insert into temp.volume_monitoring (file_name, file_path, date, partner, consent, in_out, line_count)
                            VALUES (
                                '{file_name}',
                                '{file_path}',
                                '{date}',
                                '{partner}',
                                '{consent}',
                                '{in_out}',
                                {line_count})
                        """.format(date=file_date, partner=partner, consent=consent, in_out=in_out,
                                   file_name=file_name, line_count=linecount, file_path=file_path)

            cursor_total.execute(sql)

        self.log.info('Le calcul est terminé.')
