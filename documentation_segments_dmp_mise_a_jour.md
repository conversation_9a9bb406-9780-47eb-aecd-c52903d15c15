# Règles métier sur l'étagère des Segments DMP
---

## Import
---

### Étape 1: Récupération des données initiales des segments

L'opérateur récupère les segments depuis l'API Mediarithmics par pagination :
- **Route API** : `https://api.mediarithmics.com/v1/audience_segments?organisation_id=1695`
- **Méthode** : GET avec authentification via clé API

#### Champs extraits directement de l'API :
- `id` - Identifiant unique du segment
- `name` - Nom technique du segment  
- `short_description` - Description courte utilisée pour la génération des métadonnées
- `creation_ts` - Timestamp de création (converti en datetime)
- `last_modified_ts` - Timestamp de dernière modification (converti en datetime)

#### Métriques d'audience extraites via mapping d'IDs :
Les métriques sont récupérées directement depuis le champ `audience_metrics` de chaque segment avec le mapping suivant :
- **ID 185** → `userpoints` - Nombre de points utilisateur
- **ID 186** → `cookies` - Nombre de cookies desktop et mobile  
- **ID 187** → `emails` - Nombre d'adresses email
- **ID 188** → `first_ids` - Nombre de FirstID
- **ID 191** → `utiq_ids` - Nombre d'identifiants UTIQ

**Sauvegarde** : `raw_data.csv`

### Étape 2: Récupération des connecteurs externes et identifiants Google AdManager

Pour chaque segment, l'opérateur effectue deux appels API consécutifs :

#### 2.1 Récupération des connecteurs
- **Route API** : `https://api.mediarithmics.com/v1/audience_segments/{segment_id}/external_feeds?organisation_id=1695`
- **Objectif** : Obtenir la liste des connecteurs reliés au segment

#### 2.2 Récupération des propriétés des connecteurs  
- **Route API** : `https://api.mediarithmics.com/v1/audience_segments/{segment_id}/external_feeds/{feed_id}/properties?organisation_id=1695`
- **Données extraites** :
  - `entity_id` - Identifiant de l'entité (filtré sur '150605569')
  - `google_user_list_id` - Identifiant Google AdManager (dmp_gam_id)

**Sauvegarde** : `prop_segments.csv`

### Étape 3: Séparation et traitement des segments

#### 3.1 Classification des segments
Les segments sont séparés en deux catégories :
- **Segments standards** : Ne contiennent pas "_ds" dans le nom
- **Segments étendus** : Contiennent "_ds" dans le nom (traitement spécifique)

#### 3.2 Génération des métadonnées à partir du nom technique
Le nom technique est découpé selon le pattern : `DATA_SOURCE_TYPE_VERTICAL_NAME`

**Exemple** : `FIRST_PRISMA_INTERET_SPORT_FORMULE-1`
- `data` = "FIRST"
- `source` = "PRISMA" 
- `segment_type` = "INTERET"
- `vertical` = "SPORT"
- `segment_name` = "FORMULE-1"

#### 3.3 Génération des métadonnées à partir de la description courte
Pour les segments standards, la `short_description` est découpée selon le pattern : `SOURCE - TYPE - CPM - NAME`

**Exemple** : `Prisma - Intérêt - 1€ - Sport - Formule 1`
- `source_new` = "Prisma"
- `type_new` = "Intérêt" 
- `CPM` = "1€"
- `name_new` = "Sport - Formule 1"

#### 3.4 Calcul du CPC (Coût Par Clic)
Règle métier automatique :
- **CPC = 1€** : Si `segment_type` = "INTERET" ET `source` ∈ ["PRISMA", "GENTSIDE", "TELELOISIR", "TELELOISIRS"]
- **CPC = 2€** : Dans tous les autres cas

#### 3.5 Traitements spécifiques
- **Segments étendus** : Ajout du suffixe " DS" au nom du segment
- **Nettoyage** : Remplacement des "-" par des espaces dans les noms
- **Filtrage** : Suppression des segments sans vertical ou nom de segment
- **Déduplication** : Conservation du segment avec le plus de userpoints en cas de doublons

**Sauvegarde** : `split_segments.csv`

### Étape 4: Calcul des métriques combinées

#### 4.1 Regroupement par description courte
Les segments partageant la même `short_description` sont regroupés pour calculer des métriques combinées.

#### 4.2 Requêtes OTQL pour les métriques combinées
**Route API** : `https://api.mediarithmics.com/v1/datamarts/1759/query_executions/otql?use_cache=true`

##### FirstIDs combinés :
```sql
SELECT @count{} 
FROM UserDevicePoint 
WHERE technical_identifiers {registry_id="21"} 
JOIN UserSegment 
WHERE id == "{segment_id1}" OR id == "{segment_id2}" OR id == "{segment_id3}"
```

##### UTIQ IDs combinés :
```sql
SELECT @count{} 
FROM UserAccount 
WHERE compartment_id="20" 
JOIN UserSegment 
WHERE id == "{segment_id1}" OR id == "{segment_id2}" OR id == "{segment_id3}"
```

##### Emails combinés :
```sql
SELECT @count{} 
FROM UserEmail 
JOIN UserSegment 
WHERE id == "{segment_id1}" OR id == "{segment_id2}" OR id == "{segment_id3}"
```

##### UserPoints combinés :
```sql
SELECT @count{} 
FROM UserPoint 
JOIN UserSegment 
WHERE id == "{segment_id1}" OR id == "{segment_id2}" OR id == "{segment_id3}"
```

#### 4.3 Agrégation des identifiants GAM
Les identifiants Google AdManager des segments groupés sont concaténés avec des virgules.

**Sauvegarde** : `grouped_segments.csv`

### Étape 5: Récupération des segments Google AdManager

#### 5.1 Connexion à l'API Google Ad Manager
- **Service** : AudienceSegmentService (version v202408)
- **Authentification** : OAuth2 avec compte de service
- **Compte** : 'Prisma Media - Premium' (ID: *********)
- **Filtre** : Segments first-party uniquement

#### 5.2 Données récupérées pour chaque segment GAM
- `gam_id` - Identifiant Google AdManager
- `gam_name` - Nom du segment dans GAM
- `gam_desc` - Description du segment
- `gam_size` - Taille totale du segment
- `gam_mobile` - Taille mobile web
- `gam_idfa` - Taille IDFA (iOS)
- `gam_adid` - Taille Ad ID (Android)  
- `gam_ppid` - Taille Publisher Provided ID

### Étape 6: Consolidation finale et sauvegarde

#### 6.1 Jointure des données
- Fusion des segments DMP avec les données GAM via `dmp_gam_id` = `gam_id`
- Fusion avec les métriques combinées

#### 6.2 Normalisation des données
- Conversion de toutes les métriques en entiers (remplacement des valeurs nulles par 0)
- Tri par ID de segment et taille GAM (descendant)
- Déduplication en conservant le segment avec la plus grande taille GAM

#### 6.3 Fichiers de sortie
- `full_segments.csv` - Données complètes avant jointure GAM
- `segments.csv` - **Fichier final** avec toutes les données consolidées

---

## Affichage
---

### Étagère de segments DMP

#### Règles d'affichage
- **Sélection administrative** : L'affichage est conditionné par la sélection effectuée dans la page d'administration
- **Connectivité GAM requise** : Les segments DMP non connectés à Google AdManager ne sont pas affichés
- **Contrôle d'accès** : L'affichage de la colonne "ID GAM" est soumis à un rôle spécifique

#### Traitements spéciaux Valiuz
- **Multiplication des UserPoints** : Les segments Valiuz importés dans la DMP affichent un volume de UserPoints multiplié par 2
- **Restrictions d'export** : Les segments Valiuz sans métriques ne sont pas éligibles aux exports CSV/PDF ni à l'ajout au panier

#### Segments principaux
Parmi les segments partageant la même `short_description`, le segment principal est déterminé automatiquement.

**Exemple** :
Pour la description 'Prisma - CRM - 2€ - Sociodémo - Hommes 35+' :
- `FIRST_PRISMA_CRM_SOCIODEMO_HOMMES-35+` ← **Segment principal**
- `FIRST_PRISMA_CRM_SOCIODEMO_HOMMES-35+-NIV-1`

Le segment principal est celui avec le nom technique le plus court (sans suffixes additionnels).
