r"""
**PURPOSE:**

    Download the whole SFTP Splio's events files (open, clicks, sent) & move them into SFTP archive directory (from
    splio) & ingest them into BQ 

**METHODOLOGY:**

For each Splio universes, connect to SFTP splio account and retrieve all event files.  Once retrieved,
the files are moved into remote Splio directory /events/archive &  ingested into bigquery directly to dataset
import  Three types of events are available at Splio: click, open & sent. These three types have a
separate table For each file a table are created  This DAG ingest plain text file or gz files

Once ingest into import, datas are merge by kind into prepare with deduplicated
Finally, insert or update (if data exists) into store_partner

**HOW TO:**

- Whatever the number of files, or the number of days failed, just restart the DAG to ingest
"""

from datetime import datetime, timedelta

from airflow.models import Variable
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from collapsible_doc_dag import CDocDAG
from import_plugin.operators.splio_event_import_operator import SplioEventImportOperator
from import_plugin.operators.splio_event_archive_operator import SplioEventArchiveOperator

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2021, 5, 6, 0, 0, 0),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': True,  # @Hichem fix me
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=12),
    'sla': None,
    'email_on_retry': False,
    'retries': 0,
    'retry_delay': timedelta(minutes=1),
    'depends_on_past': False,
    # GCP CONNECTION
    'gcp_conn_id': 'gcs_matrix',
    # BQ Connection
    'bigquery_conn_id': 'bq_matrix',
}

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)

#### Universes actives ####
# in case we lost it : splio_activity
# {"__comment__": "Download the whole SFTP splio's events files.", "dag_universes_to_include": ["*"], "dag_universes_to_exclude": ["ce_mediego","pm_mediego"]}

dag_variable = Variable.get("splio_activity", deserialize_json=True)

# select valid universe used for the dag
universe_list = dag_variable["active_universes"]

# If SFTP and universes don't have the same name
exception = {"prisme": "pm_b2b_prisme"}

# Create from the active universes the mapping_sftp

mapping_sftp = {f"{universe}": f"sftp_splio_{universe}" for universe in universe_list if
                universe not in exception.values()}

# We inverse here the key and the universe because we use here the sftp conn_id from airflow
for k, u in exception.items():
    mapping_sftp[f"{u}"] = f"sftp_splio_{k}"

#####


# fix me : Put into airflow variables
schema_fields = {
    "open": [
        {
            "mode": "REQUIRED",
            "name": "id_event",
            "type": "INTEGER"
        },
        {
            "mode": "REQUIRED",
            "name": "email",
            "type": "STRING"
        },
        {
            "mode": "REQUIRED",
            "name": "date_event",
            "type": "TIMESTAMP"
        },
        {
            "mode": "REQUIRED",
            "name": "useragent",
            "type": "STRING"
        },
        {
            "mode": "REQUIRED",
            "name": "sendID",
            "type": "STRING"
        },
    ],
    "clicks": [
        {
            "mode": "REQUIRED",
            "name": "id_event",
            "type": "INTEGER"
        },
        {
            "mode": "REQUIRED",
            "name": "email",
            "type": "STRING"
        },
        {
            "mode": "REQUIRED",
            "name": "date_event",
            "type": "TIMESTAMP"
        },
        {
            "mode": "REQUIRED",
            "name": "useragent",
            "type": "STRING"
        },
        {
            "mode": "REQUIRED",
            "name": "sendID",
            "type": "STRING"
        },
        {
            "mode": "REQUIRED",
            "name": "LinkID",
            "type": "INTEGER"
        },
    ],
    "sent": [
        {
            "mode": "REQUIRED",
            "name": "id_event",
            "type": "INTEGER"
        },
        {
            "mode": "REQUIRED",
            "name": "email",
            "type": "STRING"
        },
        {
            "mode": "REQUIRED",
            "name": "date_event",
            "type": "TIMESTAMP"
        },
        {
            "mode": "REQUIRED",
            "name": "sendID",
            "type": "STRING"
        },
    ]
}

with CDocDAG("splio_activity",
             schedule_interval="0 0 * * *",
             tags=["splio"],
             catchup=False,
             max_active_runs=1,
             dagrun_timeout=timedelta(hours=12),
             template_searchpath=['/home/<USER>/gcs/data/sql/splio_event/'],
             description='Ingest Splio Events',
             doc_md=__doc__,
             default_args=default_args) as dag:
    task_doc_md = '''
    Connect to splio accounts SFTP & retrieve files & ingest them <br />
    '''
    import_data_from_splio = SplioEventImportOperator(
        task_id='import_data_from_splio',
        execution_date='{{ next_ds }}',
        gcs_conn_id='gcs_matrix',
        bucket=buckets['matrix'],
        mapping_sftp=mapping_sftp,
        bq_schema_fields=schema_fields,
        retries=3)
    import_data_from_splio.doc_md = task_doc_md

    task_doc_md = '''
    Merged data into prepare <br />
    '''
    prepare_data = BigQueryExecuteQueryOperator(task_id='prepare_data', use_legacy_sql=False, sql='0_prepare_event.sql')
    prepare_data.doc_md = task_doc_md

    task_doc_md = '''
    Merge data into store insert or update data <br />
    '''
    mep_data = BigQueryExecuteQueryOperator(task_id='mep_data', use_legacy_sql=False, sql='1_mep_event.sql')
    mep_data.doc_md = task_doc_md

    task_doc_md = '''
    Insert monitoring data to the history table for open, click, sent <br />
    '''
    insert_monitoring = BigQueryExecuteQueryOperator(
        task_id='insert_monitoring',
        use_legacy_sql=False,
        sql='2_insert_monitoring.sql'
    )
    insert_monitoring.doc_md = task_doc_md

    task_doc_md = '''
        Connect to splio accounts SFTP & archive files <br />
        '''
    archive_data_from_splio = SplioEventArchiveOperator(
        task_id='archive_data_from_splio',
        execution_date='{{ next_ds }}',
        gcs_conn_id='gcs_matrix',
        bucket=buckets['matrix'],
        mapping_sftp=mapping_sftp,
        bq_schema_fields=schema_fields,
        retries=3)
    archive_data_from_splio.doc_md = task_doc_md

    import_data_from_splio >> prepare_data >> mep_data >> insert_monitoring >> archive_data_from_splio
