r"""
# Personalized NL Campaign Workflow

**PURPOSE:**
Update status for current campaign.

**METHODOLOGY:**
- Call Rogue-One API to update campaign status
"""

import os
import logging
from datetime import timedelta
from airflow.models import Variable
from airflow.decorators import task
import requests as req
from collapsible_doc_dag import CD<PERSON>DAG
from datetime import datetime
import json

# ----------------- CONFIG ----------------------
ENV = os.environ.get("ENV")
API_TOKEN = Variable.get('personalized_nl_r1_api_key')
API_HEADERS = {
    'Content-Type': 'application/json',
    'X-Auth-Identity': 'mozart',
    'X-Auth-Token': API_TOKEN
}

dag_name = 'personalized_nl__campaign_workflow'
email_on_failure = True
config = Variable.get("personalized_nl", deserialize_json=True)
update_campaign_url = 'https://rogue-one.prismadata.fr/api/personalized-nl/workflow'
if ENV != 'prod':
    dag_name = dag_name + '_' + ENV
    email_on_failure = False
    update_campaign_url = 'https://rogue-one.preprod.prismadata.fr/api/personalized-nl/workflow'

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2024, 5, 1, 2, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'retries': 0,
    'retry_delay': timedelta(minutes=2),
    'email_on_retry': False,
    'depends_on_past': False,
    'wait_for_downstream': False,
}

with CDocDAG(
        dag_name,
        description='Build personalized NL Per profile',
        tags=["nl", "personalized-nl"],
        doc_md=__doc__,
        schedule_interval=None,  # Only manual trigger
        default_args=default_args,
        max_active_runs=10,
        dagrun_timeout=timedelta(hours=1),
        catchup=False,
) as dag:
    @task
    def update_campaign_status(**kwargs):
        """
        Call Campaign Workflow API <br />
        """
        email_campaign_id = kwargs['dag_run'].conf['email_campaign_id']
        payload = {"cid": email_campaign_id}
        if 'step' in kwargs['dag_run'].conf:
            payload["step"] = kwargs['dag_run'].conf['step']

        logging.info(f"\n Calling campaign workflow API for {email_campaign_id}")
        response = req.request("POST", update_campaign_url, headers=API_HEADERS,
                               data=json.dumps(payload))

        if response.status_code != 200:
            logging.info(f"\n could not update status for {email_campaign_id}")
            logging.info(f"\n Response error code: {response.status_code}")

        json_response = json.loads(response.content)
        if json_response["status"] != 200:
            logging.info(f"\n could not update status for {email_campaign_id}")
            logging.info(f"\n API error code: {json_response['status']}, message: {json_response['detail']}")


    update_campaign_status()