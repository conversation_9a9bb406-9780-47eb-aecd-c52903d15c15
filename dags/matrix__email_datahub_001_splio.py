r"""
**PURPOSE:**
**This dag is used to:**- prepare datahub_001 (pmc bookmark alert tags) using userhub data.
- export datahub_001 to splio.

**METHODOLOGY:**

**COMMAND TO TEST:** 

- gsutil cp dags/matrix__email_datahub_001_splio.py gs://europe-west1-mozart-cluster-2e910a39-bucket/dags/
- gsutil -m cp -R data/sql/matrix_email/*  gs://europe-west1-mozart-cluster-2e910a39-bucket/data/sql/matrix_email/
"""

import os
from datetime import timedelta, datetime

from airflow.models import Variable
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from collapsible_doc_dag import CDocDAG
from export_plugin.operators.export_splio_bigquery_operator import ExportSplioBigQueryOperator

dag_name = 'matrix__email_datahub_001_splio'
email_on_failure = True

env = os.environ.get("ENV")
bq_project = 'pm-{}-matrix'.format(env)
bq_userhub_project = 'pm-{}-userhub'.format(env)
gcs_matrix_uri = 'gs://it-data-prod-matrix-pipeline'
# ------------- FOR PREPROD ENV -----------------
env = os.environ.get("ENV")
if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    bq_project = 'pm-preprod-matrix'
    gcs_matrix_uri = 'gs://it-data-preprod-matrix-preprod-pipeline'
# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2022, 9, 13, 00, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'email_on_retry': False,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=50),
    'sla': None,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
    'depends_on_past': False,

    # PSQL Connection
    'postgres_conn_id': 'psql_matrix_email_app',
    'database': 'matrix',
    'gcp_conn_id': 'gcs_matrix',
    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_TRUNCATE',
    'allow_large_results': True,
    'flatten_results': False,
}

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']

datahub_contact_001 = Variable.get("datahub_contact_001", deserialize_json=True)
splio_universes_sftp = Variable.get("splio_universes_sftp", deserialize_json=True)
splio_universes_all = Variable.get("splio_universes_all", deserialize_json=True)
splio_universes = {u: splio_universes_all[u] for u in datahub_contact_001['universes'] if u in splio_universes_all}

with CDocDAG(dag_name,
             description='Prepare & export datahub_001 (pmc bookmark alert tags) to splio',
             doc_md=__doc__,
             schedule_interval="*/15 * * * *",
             default_args=default_args,
             max_active_runs=1,
             dagrun_timeout=timedelta(hours=1),
             catchup=False,
             tags=["datahub"],
             template_searchpath=['/home/<USER>/gcs/data/sql/matrix_email/']
             ) as dag:
    task_doc_md = """
    Prepare datahub001 full data
    """
    prepare_datahub_011 = BigQueryExecuteQueryOperator(
        task_id="prepare_datahub_001",
        sql="10_prepare_datahub_001.sql",
        params={
            'bq_project': bq_project,
            'bq_userhub_project': bq_userhub_project,
        }
    )
    prepare_datahub_011.doc_md = task_doc_md

    task_doc_md = """
    Export last updated profiles to alert Splio universe.
    """
    export_splio_datahub_001 = ExportSplioBigQueryOperator(
        task_id='export_splio_datahub_001',
        sql='11_export_splio_datahub_001.sql',
        sql_after_export='12_store_datahub_001_previous.sql',
        params={
            'universe_id': '{universe_id}',
            'bq_project': bq_project,
        },
        destination_dataset_table_temporary='temp.{universe}_contacts_001_{splio_sequence}',
        universes=splio_universes,
        universes_sftp=splio_universes_sftp,
        datahub_file_template='{universe}_contacts_001_{splio_sequence}*.csv',
        splio_sequence='{{ next_execution_date.strftime("%Y%m%d_%H%M") }}',
        bucket=matrix_bucket,
        bucket_path='export_splio/datahub_001/{{ next_execution_date.strftime("%Y%m%d_%H%M") }}/',
        destination_cloud_storage_path_uris=gcs_matrix_uri,
        splio_remote_dir='imports/',
        flux_ref=1,
        bq_project=bq_project,
    )
    export_splio_datahub_001.doc_md = task_doc_md

    prepare_datahub_011 >> export_splio_datahub_001
