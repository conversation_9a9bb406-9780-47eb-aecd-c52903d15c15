r"""
# Personalized NL Preparation

**PURPOSE:**
**This dag is used to:**- Check if any ready campaign is supposed to be sent.
- Prepare chunks of emails to handle.
- Call Send NL (personalized_nl_send_nl dag) for each chunk of profiles.

**METHODOLOGY:**
- Chunk emails according to config.
- Trigger personalized_nl_send_nl to send NL for each chunk using Splio SMTP.
"""
import json
import logging
import os
from datetime import timedelta, datetime
from airflow.models import Variable
from airflow.decorators import task
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from psql_plugin.operators.psql_select_many_operator import PostgresSelectManyOperator
from airflow.providers.common.sql.sensors.sql import SqlSensor
from collapsible_doc_dag import CDocDAG
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.utils.task_group import TaskGroup

# ----------------- CONFIG ----------------------
ENV = os.environ.get("ENV")
IS_PROD = ENV == 'prod'
BQ_PROJECT = 'pm-{}-matrix'.format(ENV)
BUCKETS = Variable.get("gcs_bucket_names", deserialize_json=True)
MATRIX_BUCKET = BUCKETS['matrix']
MOZART_BUCKET = BUCKETS['mozart']

dag_name = 'personalized_nl_send_campaign'
email_on_failure = True
config = Variable.get("personalized_nl", deserialize_json=True)

if ENV != 'prod':
    dag_name = dag_name + '_' + ENV
    email_on_failure = False


default_args = {
    'owner': 'airflow',
    'start_date': datetime(2024, 6, 10, 2, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': False,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=120),
    'sla': None,
    'email_on_retry': False,
    'retries': 0,
    'retry_delay': timedelta(minutes=5),
    'depends_on_past': False,

    # BQ CONNECTION
    'gcp_conn_id': 'bq_matrix',
    'use_legacy_sql': False,

    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE'  # possibles values: INTERACTIVE and BATCH.
}

with CDocDAG(
        dag_name,
        description='Prepare personalized NL Campaign',
        tags=["nl", "personalized-nl"],
        doc_md=__doc__,
        schedule_interval="*/30 3-20 * * *",
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=2),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/personalized_nl/']
) as dag:
    sensor_shoot_date = SqlSensor(
        task_id='sensor_shoot_date',
        conn_id='psql_personalized_nl_app',
        timeout=29 * 60,  # 29 minutes
        poke_interval=2 * 60,  # each 2 minutes
        mode='reschedule',
        soft_fail=True,
        sql="09_check_campaigns_to_send.sql"
    )
    sensor_shoot_date.doc_md = "check if campaign's shoot_date is due"

    with TaskGroup("send_nls") as send_nls:
        task_doc_md = """
        Calculate Chunks to bulk send the Newsletter <br />
        """
        calc_chunks = SQLExecuteQueryOperator(
            task_id='calc_chunks',
            conn_id='psql_personalized_nl_app',
            sql="10_calc_send_chunks.sql",
            dag=dag,
            params={
                'chunk_size': config["send_chunk_size"]
            }
        )
        calc_chunks.doc_md = task_doc_md

        task_doc_md = """
        Get chunks of campaign/profiles to handle. <br />
        """
        get_chunks_task = PostgresSelectManyOperator(
            task_id='get_chunks',
            postgres_conn_id='psql_personalized_nl_app',
            sql="10_get_send_chunks.sql",
        )
        get_chunks_task.doc_md = task_doc_md


        @task
        def trigger_send_nl_dags(**kwargs):
            """
            Trigger the send process. <br />
            """
            ti = kwargs['ti']
            result = ti.xcom_pull(task_ids='send_nls.get_chunks')
            rows = json.loads(result)
            send_nl_dag_name = f"personalized_nl__send_nl_{ENV}" if not IS_PROD else 'personalized_nl__send_nl'
            for row in rows:
                chunk_number, email_campaign_id, start_profile_master_id, end_profile_master_id = row
                trigger_task = TriggerDagRunOperator(
                    task_id=f'trigger_dag_{send_nl_dag_name}_{chunk_number}',
                    trigger_dag_id=send_nl_dag_name,
                    trigger_run_id=f'send_nl_{chunk_number}_{email_campaign_id}_{start_profile_master_id}_{end_profile_master_id}_{datetime.now().strftime("%Y-%m-%d_%H:%M:%S")}',
                    conf={
                        'email_campaign_id': email_campaign_id,
                        'start_profile_master_id': start_profile_master_id,
                        'end_profile_master_id': end_profile_master_id,
                    },
                    wait_for_completion=False
                )
                trigger_task.execute(context=kwargs)

        calc_chunks >> get_chunks_task >> trigger_send_nl_dags()

    with TaskGroup("campaign_workflow") as campaign_workflow:
        task_doc_md = """
        Get campaigns to send <br />
        """
        get_campaigns_task = PostgresSelectManyOperator(
            task_id='get_campaigns_to_send',
            postgres_conn_id='psql_personalized_nl_app',
            sql="11_get_campaigns_to_send.sql",
        )
        get_campaigns_task.doc_md = task_doc_md

        @task
        def get_campaigns_to_send_confs(**kwargs):
            ti = kwargs['ti']
            result = ti.xcom_pull(task_ids='campaign_workflow.get_campaigns_to_send')
            rows = json.loads(result)
            confs = []
            for campaign_ids in rows:
                for email_campaign_id in campaign_ids:
                    confs.append({
                        'trigger_run_id': f'workflow_{email_campaign_id}_{datetime.now().strftime("%Y-%m-%d_%H%M%S")}',
                        'conf': {
                            'email_campaign_id': email_campaign_id,
                            'step': 'SENDING'
                        }
                    })
            return confs

        campaign_workflow_confs = get_campaigns_to_send_confs()

        trigger_campaign_workflow_dag = TriggerDagRunOperator.partial(
            task_id="trigger_campaign_workflow_dag",
            trigger_dag_id=(f"personalized_nl__campaign_workflow_{ENV}" if not IS_PROD else 'personalized_nl__campaign_workflow'),
            wait_for_completion=True,
            max_active_tis_per_dag=10
        ).expand_kwargs(campaign_workflow_confs)

        get_campaigns_task >> campaign_workflow_confs >> trigger_campaign_workflow_dag

    sensor_shoot_date >> campaign_workflow >> send_nls
