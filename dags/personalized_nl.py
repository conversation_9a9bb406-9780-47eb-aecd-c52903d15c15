r"""
# Personalized NL Preparation

**PURPOSE:**
**This dag is used to:**- Prepare personalized nl campaign.
- Call prepare targeted population (personalized_nl_prepare_population dag) for each campaign.

**METHODOLOGY:**
- Create Campaign using campaign definition and the available Rogue-One email.
- Trigger personalized_nl_prepare_population to create population by campaign.
"""
import os
from datetime import timedelta, datetime
from airflow.models import Variable
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from collapsible_doc_dag import CDocDAG
from bq_plugin.operators.psql_to_bigquery_operator import PostgresToBigQueryOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.utils.task_group import TaskGroup

# ----------------- CONFIG ----------------------
ENV = os.environ.get("ENV")
IS_PROD = ENV == 'prod'
BQ_PROJECT = 'pm-{}-matrix'.format(ENV)
BUCKETS = Variable.get("gcs_bucket_names", deserialize_json=True)
MATRIX_BUCKET = BUCKETS['matrix']
MOZART_BUCKET = BUCKETS['mozart']
EMAIL_CAMPAIGN_SCHEMA = [{"mode": "REQUIRED", "name": "email_campaign_definition_id", "type": "INTEGER"},
                         {"mode": "REQUIRED", "name": "email_campaign_id", "type": "INTEGER"},
                         {"mode": "REQUIRED", "name": "rogue_one_email_id", "type": "INTEGER"},
                         {"mode": "REQUIRED", "name": "email_base_public_ref", "type": "STRING"},
                         {"mode": "NULLABLE", "name": "segment_ref", "type": "STRING"},
                         {"mode": "REQUIRED", "name": "nl_factory_newsletter_id", "type": "INTEGER"},
                         {"mode": "REQUIRED", "name": "total_articles", "type": "INTEGER"},
                         {"mode": "REQUIRED", "name": "brand_name", "type": "STRING"},
                         {"mode": "REQUIRED", "name": "brand_trigram", "type": "STRING"},
                         {"mode": "NULLABLE", "name": "config", "type": "STRING"},
                         {"mode": "REQUIRED", "name": "shoot_date", "type": "TIMESTAMP"},
                         {"mode": "REQUIRED", "name": "create_date", "type": "TIMESTAMP"}]

dag_name = 'personalized_nl'
email_on_failure = True
config = Variable.get("personalized_nl", deserialize_json=True)

if ENV != 'prod':
    dag_name = dag_name + '_' + ENV
    email_on_failure = False

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2021, 12, 1, 2, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': False,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=120),
    'sla': None,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=5),
    'depends_on_past': False,

    # BQ CONNECTION
    'gcp_conn_id': 'bq_matrix',
    'use_legacy_sql': False,

    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE'  # possibles values: INTERACTIVE and BATCH.
}

schedule_interval = "45 0 * * *"
if ENV != 'prod':
    schedule_interval = "30 6 * * *"

with CDocDAG(
        dag_name,
        description='Prepare personalized NL Campaign',
        tags=["nl", "personalized-nl"],
        doc_md=__doc__,
        schedule_interval=schedule_interval,
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=1),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/personalized_nl/']
) as dag:
    with TaskGroup("campaign_creation") as campaign_creation:
        task_doc_md = """
        Create Email Campaign using campaign definition and the available Rogue-One email. <br />
        """
        create_campaign_in_postgres = SQLExecuteQueryOperator(
            task_id='create_campaign',
            conn_id='psql_personalized_nl_app',
            sql="01_create_campaign.sql",
            dag=dag
        )
        create_campaign_in_postgres.doc_md = task_doc_md

        task_doc_md = """
        Prepare campaign's data to export to BQ. <br />
        """
        prepare_campaign_to_export = SQLExecuteQueryOperator(
            task_id='prepare_campaign_to_export',
            conn_id='psql_personalized_nl_app',
            sql="02_get_email_campaigns.sql",
            dag=dag
        )
        prepare_campaign_to_export.doc_md = task_doc_md

        task_doc_md = """
        Import Email Campaign's data from GCS into BQ store.personalized_nl_campaign
        """
        import_email_campaigns_to_bq = PostgresToBigQueryOperator(
            task_id='import_email_campaigns_to_bq',
            table='personalized_nl.personalized_nl_campaign_snapshot',
            source_object='personalized_nl/email_campaign_{{execution_date.strftime("%Y_%m_%d_%H%M")}}.csv',
            destination_project_dataset_table=BQ_PROJECT + ':store.personalized_nl_campaign',
            schema_fields=EMAIL_CAMPAIGN_SCHEMA,
            field_delimiter='\t',
            allow_quoted_newlines=True,
            quote_character='',
            write_disposition='WRITE_TRUNCATE',
            bucket=MATRIX_BUCKET,
            database='matrix',
            postgres_conn_id='psql_personalized_nl_app',
        )
        import_email_campaigns_to_bq.doc_md = task_doc_md

        create_campaign_in_postgres >> prepare_campaign_to_export >> import_email_campaigns_to_bq

    prepare_population_dag_name = f"personalized_nl__prepare_population_{ENV}" if not IS_PROD else 'personalized_nl__prepare_population'
    trigger_prepare_population_dag_name = TriggerDagRunOperator(
        task_id=f'trigger_{prepare_population_dag_name}',
        trigger_dag_id=prepare_population_dag_name,
        wait_for_completion=True,
        poke_interval=10,
        dag=dag
    )

    campaign_creation >> trigger_prepare_population_dag_name
