r"""
**PURPOSE:**

This DAG is used to import the enriched file from EMB. 
The file is in our SFTP so we can import it via a gcs operator. 
This doccument should be enriched with civility and date of birth.
"""

## gsutil cp dags/partners_emb_import_enriched.py gs://europe-west1-mozart-cluster-2e910a39-bucket/dags/

# gsutil -m cp -R data/sql/partners/enrichissement/*  gs://europe-west1-mozart-cluster-2e910a39-bucket/data/sql/partners/enrichissement/
# gsutil cp gs://pm-prod-sftp-storage/home/<USER>/emailbroker/imports/enriched/enriched_20220808.csv gs://pm-preprod-sftp-storage/home/<USER>/emailbroker/imports/enriched/


import logging
import os
from datetime import date
from datetime import timedelta, datetime

import pendulum
from airflow.models import Variable
from airflow.operators.bash import BashOperator
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.operators.python import BranchPythonOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.google.cloud.transfers.gcs_to_bigquery import GCSToBigQueryOperator
from airflow.utils.email import send_email
from collapsible_doc_dag import CDocDAG
from google.cloud import storage

# ----------------- CONFIG ----------------------
dag_name = 'partners_emb_import_enriched'
email_on_failure = True
email_on_retry = False

path_to_enriched_birthdate = 'home/uploads/emailbroker/imports/enriched/' + 'enriched_birthdate_{}*.csv'.format(
    '{{ next_execution_date.strftime("%Y%m") }}')  # EMB file generated the 11th of the month
path_to_enriched_gender = 'home/uploads/emailbroker/imports/enriched/' + 'enriched_gender_{}*.csv'.format(
    '{{ next_execution_date.strftime("%Y%m") }}')  # EMB file generated the 11th of the month

env = os.environ.get("ENV")

# BQ specific config
bq_project = 'pm-{}-matrix'.format(env)
###bq_dataset_refined = 'refined_data'
bq_dataset_store_partner = 'store_partner'

# GCS specific config
buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
gcs_bucket = buckets['matrix']
gcs_bucket_sftp = buckets['sftp']

date_enriched_file = Variable.get("date_enriched_emb", deserialize_json=True)["date"]

if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    email_on_retry = False
    bq_project = 'pm-{}-matrix'.format(env)
    bq_project = 'pm-preprod-matrix'

# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2021, 8, 5, tzinfo=pendulum.timezone("Europe/Paris")),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=50),
    'sla': None,
    'email_on_retry': email_on_retry,
    'retries': 2,
    'retry_delay': timedelta(minutes=3),
    'depends_on_past': False,
    'bigquery_conn_id': 'bq_matrix',
    'gcp_conn_id': 'bq_matrix',
    'use_legacy_sql': False
}


def gcs_check_birthdate_file(**kwargs):
    client = storage.Client()
    bucket = client.get_bucket(gcs_bucket_sftp)

    # Get the current year and month
    current_year_month = datetime.now().strftime("%Y%m")

    # List files in the bucket
    blobs = bucket.list_blobs(
        prefix='home/uploads/emailbroker/imports/enriched/enriched_birthdate_')  # adjust the prefix as necessary

    # Check for any file that matches the current year and month (can be any day)
    for blob in blobs:
        filename = blob.name
        if current_year_month in filename:
            return 'File exists'

    return 'File does not exist'


def gcs_check_gender_file(**kwargs):
    client = storage.Client()
    bucket = client.get_bucket(gcs_bucket_sftp)

    # Get the current year and month
    current_year_month = datetime.now().strftime("%Y%m")

    # List files in the bucket
    blobs = bucket.list_blobs(
        prefix='home/uploads/emailbroker/imports/enriched/enriched_gender_')  # adjust the prefix as necessary

    # Check for any file that matches the current year and month (can be any day)
    for blob in blobs:
        filename = blob.name
        if current_year_month in filename:
            return 'File exists'

    return 'File does not exist'


def notify_emb_email(**kwargs):
    """
    Returns a function intended to be called from a PythonOperator
    and we send a mail if we find no enriched file
    """
    current_date = date.today()

    ti = kwargs['ti']
    birthdate_file_exists = ti.xcom_pull(task_ids='gcs_check_birthdate_file')
    gender_file_exists = ti.xcom_pull(task_ids='gcs_check_gender_file')
    logging.info("Birthdate file : " + str(birthdate_file_exists))
    logging.info("Gender file : " + str(gender_file_exists))

    # We expect to receive them on the 11th, if not, something's wrong
    if (current_date.day == 11) and not (
            birthdate_file_exists == 'File exists' and gender_file_exists == 'File exists'):
        recipients = ["<EMAIL>", "<EMAIL>"]  # Add EMB's email address
        additional_recipients = ["<EMAIL>", "<EMAIL>"]

        to = ", ".join(recipients)
        cc = ", ".join(additional_recipients)

        # we want to notify that the sample file is not here

        title = "Fichier d'enrichissement non reçu"
        body = ("Bonjour, <br> "
                "<br><br> Si vous recevez cet email, cela signifie que l'on n'a pas reçu"
                "<br>les données dans notre GCS.<br>"
                "<br>Veuillez nous prévenir une fois le fichier généré afin qu'on puisse l'ingérer.<br>"
                "<br><br>Cordialement, <br> "
                "L'équipe technique IT-Data"
                )

        send_email(to=to, subject=title, html_content=body, cc=cc, mime_charset='utf-8', conn_id='sendgrid_default')
        print('email html + ' + body)


def check_files_existence(**kwargs):
    ti = kwargs['ti']
    birthdate_file_exists = ti.xcom_pull(task_ids='gcs_check_birthdate_file')
    gender_file_exists = ti.xcom_pull(task_ids='gcs_check_gender_file')
    logging.info("Birthdate file : " + str(birthdate_file_exists))
    logging.info("Gender file : " + str(gender_file_exists))

    if (birthdate_file_exists == 'File exists') and (gender_file_exists == 'File exists'):
        return 'go_ahead'
    else:
        return 'do_nothing'


with CDocDAG(
        dag_name,
        description='Retrieve emb enriched from stfp',
        tags=["partner"],
        doc_md=__doc__,
        schedule_interval='0 15 4,11,18,25 * *',  # only the 4th at 3PM
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=1),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/partners/enrichissement/']
) as dag:
    task_doc_md = """
            Check if the birthdate CSV is uploaded into GCS by EMB <br />
            """
    gcs_check_birthdate_file = PythonOperator(
        task_id='gcs_check_birthdate_file',
        python_callable=gcs_check_birthdate_file,
        provide_context=True,
        op_kwargs={'path_to_enriched_birthdate': path_to_enriched_birthdate}
    )
    gcs_check_birthdate_file.doc_md = task_doc_md

    task_doc_md = """
                Check if the gender CSV is uploaded into GCS by EMB <br />
                """
    gcs_check_gender_file = PythonOperator(
        task_id='gcs_check_gender_file',
        python_callable=gcs_check_gender_file,
        provide_context=True,
        op_kwargs={'path_to_enriched_gender': path_to_enriched_gender}
    )
    gcs_check_gender_file.doc_md = task_doc_md

    task_doc_md = """
                    Proceed with the ingestion or notify EMB if either of the files is missing<br />
                    """
    check_files_existence_branch = BranchPythonOperator(
        task_id='check_files_existence_branch',
        python_callable=check_files_existence,
        provide_context=True
    )
    check_files_existence_branch.doc_md = task_doc_md

    go_ahead = EmptyOperator(
        task_id='go_ahead',
    )

    do_nothing = EmptyOperator(
        task_id='do_nothing',
    )

    task_doc_md = """
            If we find no file, we send a mail to EMB to notify them <br />
            """
    notify_if_file_absent = PythonOperator(
        task_id='notify_if_file_absent',
        python_callable=notify_emb_email
    )
    notify_if_file_absent.doc_md = task_doc_md

    task_doc_md = """
     This tasks is used to import emb enriched birthdate file from our gcs/sftp to our BQ.<br />
    """

    emb_enrichement_schemas = [{"mode": "REQUIRED", "name": "email_sha256", "type": "STRING"},
                               {"mode": "NULLABLE", "name": "birthdate", "type": "STRING"}]

    enriched_emb_birthdate_to_bq = GCSToBigQueryOperator(
        task_id='enriched_emb_birthdate_to_bq',
        source_objects=[path_to_enriched_birthdate],
        destination_project_dataset_table=bq_project + ':import.import_emb_enrichment_birthdate',
        source_format='CSV',
        field_delimiter=';',
        autodetect=False,
        schema_fields=emb_enrichement_schemas,
        bucket=gcs_bucket_sftp,
        skip_leading_rows=1,
        write_disposition='WRITE_TRUNCATE'
    )

    enriched_emb_birthdate_to_bq.doc_md = task_doc_md

    task_doc_md = """
     This tasks is used to import emb enriched file from our gcs/sftp to our BQ.<br />
    """

    emb_enrichement_schemas = [{"mode": "REQUIRED", "name": "email_sha256", "type": "STRING"},
                               {"mode": "NULLABLE", "name": "civilite", "type": "STRING"}]

    enriched_emb_gender_to_bq = GCSToBigQueryOperator(
        task_id='enriched_emb_gender_to_bq',
        source_objects=[path_to_enriched_gender],
        destination_project_dataset_table=bq_project + ':import.import_emb_enrichment_gender',
        source_format='CSV',
        field_delimiter=';',
        autodetect=False,
        schema_fields=emb_enrichement_schemas,
        bucket=gcs_bucket_sftp,
        skip_leading_rows=1,
        write_disposition='WRITE_TRUNCATE'
    )

    enriched_emb_gender_to_bq.doc_md = task_doc_md

    task_doc_md = """
    merge new emb_enrichment into full EMB table.<br />
    """
    merge_emb_enrichment = BigQueryExecuteQueryOperator(
        task_id='merge_emb_enrichment',
        sql='import/merge_emb_enrichment.sql'
    )
    merge_emb_enrichment.doc_md = task_doc_md

    bash_command = """gsutil mv "gs://""" + gcs_bucket_sftp + """/home/<USER>/emailbroker/imports/enriched/*.csv" gs://""" + gcs_bucket_sftp + """/home/<USER>/emailbroker/imports/enriched/archive"""

    task_doc_md = """
        Purge the folder into the archive so that files only get imported once.<br />
        """
    purge_folder = BashOperator(
        task_id='purge_folder',
        bash_command=bash_command,
    )
    purge_folder.doc_md = task_doc_md

    [gcs_check_birthdate_file,gcs_check_gender_file] >> check_files_existence_branch
    check_files_existence_branch >> go_ahead >> [enriched_emb_birthdate_to_bq, enriched_emb_gender_to_bq ] >> merge_emb_enrichment >> purge_folder
    check_files_existence_branch >> do_nothing >> notify_if_file_absent
