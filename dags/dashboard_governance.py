r"""
**PURPOSE:**

This DAG is intended to enable the feeding of tables required for the Governance Dashboard project. It consists of several tasks:<br />
- "copy_tables": <br />
BatchOperator which will retrieve only the tables present in the dataset "sharing_sifflet_prismamedia" and then copy them with an expiration date of today + 7 days in the dataset "sifflet".<br />
- get_asset_info<br />
Get infos for each table and get the N+1 and N-1 lineage for each table.<br />
- get_lineage<br />
Compile and order the result produced by previous task (get_asset_info) to determine the final table of each table, or its lineage.<br />
- lineage_and_indirect_cost<br />
Connects each table to its final table in order to obtain the complete lineage of each final table, and calculates the indirect cost.<br />

__USEFUL COMMAND TO TEST:__ <br />
    gsutil -m cp -R dags/dashboard_governance.py gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/
    gsutil -m cp -R data/sql/sifflet/generate__tables.sql gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/sifflet/
    gsutil -m cp -R data/sql/sifflet/get_asset_info.sql gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/sifflet/
    gsutil -m cp -R data/sql/sifflet/get_lineage.sql gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/sifflet/
    gsutil -m cp -R data/sql/sifflet/lineage_and_indirect_cost.sql gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/sifflet/
    gsutil -m cp -R data/sql/sifflet/direct_cost.sql gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/sifflet/
    gsutil -m cp -R data/sql/sifflet/dashboard_scan.sql gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/sifflet/
    gsutil -m cp -R data/sql/sifflet/cost_tracking_dashboard_governance.sql gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/sifflet/

"""

import os
from airflow.models import Variable
from collapsible_doc_dag import CDocDAG
from datetime import datetime, timedelta
from airflow.operators.bash import BashOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from google.cloud import secretmanager
from airflow.datasets import Dataset

# ----------------- CONFIG ----------------------
dag_name = 'dashboard_governance'

env = os.environ.get("ENV")
bq_project = 'pm-{}-matrix'.format(env)
email_on_failure = True

if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False


dashboard_governance_var = Variable.get("dashboard_governance", deserialize_json=True)
retention_days = int(dashboard_governance_var["retention_days"])

today = datetime.today().strftime('%Y%m%d')

source_project = dashboard_governance_var["source_project"]
source_dataset = dashboard_governance_var["source_dataset"]
destination_project = dashboard_governance_var["destination_project"]
billing_project = dashboard_governance_var["billing_project"]
table_list = dashboard_governance_var["table_list"]
is_full = dashboard_governance_var["is_full"]
query_cost_project = dashboard_governance_var["query_cost_project"]
query_cost_dataset = dashboard_governance_var["query_cost_dataset"]
bq_cost_by_TB = dashboard_governance_var["bq_cost_by_TB"]

retention_period_sec = retention_days * 24 * 60 * 60 #Convert the number of days we want to keep data, from days to seconds

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2024, 4, 15, 8, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': False,
    'provide_context': True,
    'execution_timeout': timedelta(hours=8),
    'sla': None,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=20),
    'depends_on_past': False,

    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_APPEND',
    # GCP CONNECTION
    'gcp_conn_id': 'gcs_matrix',
    # PSQL Connection
    'postgres_conn_id': 'psql_matrix_email_app',

    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',  # possibles values: INTERACTIVE and BATCH.
}

# ---  DAG ----
with CDocDAG(
        dag_name,
        description="""DAG Dashboard Governance:
        This DAG prepares and analyzes BigQuery tables to support the Governance Dashboard. 
        It snapshots shared tables, tracks their data lineage, and calculates indirect costs. 
        These steps ensure data traceability and improve governance.
        """,
        tags=["monitoring"],
        doc_md=__doc__,
        schedule=[Dataset("bigquery://matrix/generated_data/store__bq_metadata/")],
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=10),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/sifflet/'],
        concurrency=10
) as dag:

# --- TASK ---

# Task: Generate snapshot of the tables in business-abonnement.sharing_sifflet_prismamedia into the dataset `sifflet_import`
    task_doc_md = """
    Task generate__tables:<br />
    Create a snapshot of the tables in business-abonnement.sharing_sifflet_prismamedia into the dataset `sifflet_import`.<br />
    """

    generate__tables = BigQueryExecuteQueryOperator(
        task_id="generate__tables",
        sql="generate__tables.sql",
        params={
            "table_list": table_list, #Var AirFlow
            "bq_project": destination_project, #Var AirFlow - "pm-prod-matrix" .sifflet_import
            "source_project": source_project, #Var AirFlow - "pm-prod-business-abonnement" .sharing_sifflet_prismamedia
            "source_dataset": source_dataset
        }
    )

    generate__tables.doc_md = task_doc_md


# Task: Copy tables into destination_dataset and update their expiration date
    bash_command = f"""
        echo "Get list of tables in dataset"
        TABLES=$(bq ls {destination_project}:sifflet_import | awk 'NR > 2 && $2 == "TABLE" {{print $1}}')
        echo "Tables found: $TABLES"

        for table in $TABLES; do
            echo "Cloning table: ${{table}}"
            source_table="{destination_project}:sifflet_import.$table"
            dest_table="{destination_project}:sifflet.${{table}}_{today}"
            bq cp --force --project_id={billing_project} $source_table $dest_table
            bq update --project_id={billing_project} --expiration={retention_period_sec} $dest_table
        done
        """

    task_doc_md = """
    Task copy_bigquery_tables:<br />
    BatchOperator which will retrieve only the tables present in the dataset "sharing_sifflet_prismamedia" and then copy them with an expiration date of today + 7 days in the dataset "sifflet".<br />
    """
    copy_tables = BashOperator(
        task_id='copy_bigquery_tables',
        bash_command=bash_command,
    )
    copy_tables.doc_md = task_doc_md

# Task: get_asset_info
    task_doc_md = """
    Task get_asset_info:<br />
    Get info for each table and get the N+1 and N-1 lineage for each table.<br />
    """

    get_asset_info = BigQueryExecuteQueryOperator(
        task_id='get_asset_info',
        sql='get_asset_info.sql',
        params={
            'bq_project': destination_project,
            'is_full': is_full,
            'today': today,
            'source_project': source_project,
        }
    )
    get_asset_info.doc_md = task_doc_md

# Task: get_lineage
    task_doc_md = """
    Task get_lineage:<br />
    Compile and order the result produced by previous task (get_asset_info) to determine the final table of each table, or its lineage.<br />
    """

    get_lineage = BigQueryExecuteQueryOperator(
        task_id='get_lineage',
        sql='get_lineage.sql',
        params={
            'bq_project': destination_project,
            'is_full': is_full
        }
    )
    get_lineage.doc_md = task_doc_md

# Task: lineage_and_indirect_cost
    task_doc_md = """
    Task Lineage_&_Indirect_Cost:<br />
    Determine the indirect cost required to set up a table, and categorize by their final tables.<br />
    """

    lineage_and_indirect_cost = BigQueryExecuteQueryOperator(
        task_id='lineage_and_indirect_cost',
        sql='lineage_and_indirect_cost.sql',
        params={
            'bq_project': destination_project,
            'is_full': is_full,
            'query_cost_project': query_cost_project,
            'query_cost_dataset': query_cost_dataset
        }
    )
    lineage_and_indirect_cost.doc_md = task_doc_md

# Task: direct_cost
    task_doc_md = """
    Task Direct_Cost:<br />
    Will retrieve information about requests initiated by the <NAME_EMAIL>.<br />
    Which corresponds to the queries generated during the use of dashboards by users. Then will calculate the total direct cost produced by dashboard.<br />
    """

    direct_cost = BigQueryExecuteQueryOperator(
        task_id='direct_cost',
        sql='direct_cost.sql',
        params={
            'bq_project': destination_project,
            'is_full': is_full,
            'bq_cost_by_TB': bq_cost_by_TB,
            'query_cost_project': query_cost_project
        }
    )
    direct_cost.doc_md = task_doc_md

# Task: dashboard_scan
    task_doc_md = """
    Retrieve, every day, all the dashboards publish with different information that are related.
    """
    dashboard_scan = BigQueryExecuteQueryOperator(
        task_id='dashboard_scan',
        sql='dashboard_scan.sql',
        params={
            'bq_project': destination_project,
            'is_full': is_full,
            'today': today,
        }
    )
    dashboard_scan.doc_md = task_doc_md

# Task: Create/Feed the business data table "cost_tracking_dashboard_governance
    task_doc_md = """
    Will group together all the retrieved and generated data needed to feed the dashboard Dashboard Governance
    """
    cost_tracking_dashboard_governance = BigQueryExecuteQueryOperator(
        task_id='cost_tracking_dashboard_governance',
        sql='cost_tracking_dashboard_governance.sql',
        params={
            'bq_project': destination_project,
            'is_full': is_full,
            'today': today,
        }
    )
    cost_tracking_dashboard_governance.doc_md = task_doc_md
# ---   ---

generate__tables >> copy_tables >> get_asset_info >> get_lineage >> lineage_and_indirect_cost
direct_cost
dashboard_scan
[lineage_and_indirect_cost, direct_cost, dashboard_scan] >> cost_tracking_dashboard_governance
