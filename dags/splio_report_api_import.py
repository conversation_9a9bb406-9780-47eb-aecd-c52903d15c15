# -*- coding: utf-8 -*-
r"""
**PURPOSE:**
**This manual DAG call Splio API report for each universes see:**here
Unlike splio_report.py DAG, this DAG accept in input period paramters. See here

**METHODOLOGY:**

Same as splio_report.py DAG,
**splio_report call Splio API report day by day
splio_report_api_import will:**- Create a temporary table matrix__email_tmp.splio_report_import_past
- It will call Splio Api with several task by parallelizing the universes
- Will take info from the tmp table and store it into matrix_/_email_tmp.splio_report_data and into matrix_/_email_tmp.report_link
- Store both those tabels into BQ into dataset import
- Into prepare choose if we have to insert or to update a new campaing
- Insert or update the new import to the store_tracking splio_report_data and report_link

**HOW TO Launch this dag:**

- Switch off this splio_report.py DAG before started this DAG.
- Splio_report.py and splio_report_api_import.py must not to be launched in the same time 
- Use the airflow variable to select univers for which you want to retrieve information
- Change periode with the Variable Airflow to retrieve all informations inside this range (the last day is exclude)

-**There are many ways to speed up this dag:**- We can parallelize over the period, over universes or over both.
    - For period, we have to defined several period (period_1, period_2,...) and specifiy them inside the task
    - For universes, we have to split them inside a variable universes_frag_* and use them in tasks.

**COMMAND TO TEST:** 

gsutil  cp      dags/splio_report_api_import.py**gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/
gsutil  cp      plugins/import_plugin/operators/splio_reports_import_operator.py**gs://europe-west1-preprod-mozart-2fa49086-bucket/plugins/import_plugin/operators/
gsutil  cp  -R  data/sql/tracking/splio/reports/*  gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/tracking/splio/reports/
"""


import itertools
from datetime import datetime, timedelta

from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.models import Variable
from bq_plugin.operators.psql_to_bigquery_operator import PostgresToBigQueryOperator
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from airflow.operators.python import PythonOperator
from airflow.utils.task_group import TaskGroup
from collapsible_doc_dag import CDocDAG
from import_plugin.operators.splio_reports_import_operator import SplioReportsImportOperator

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2021, 8, 11, 0, 0, 0),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': True,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(days=2),
    'sla': None,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'depends_on_past': False,

    ## PSQL Connection
    'postgres_conn_id': 'psql_matrix_email_app',

    ## GCS Connection
    'gcs_conn_id': 'gcs_matrix',

    ## BQ Connection
    'bigquery_conn_id': 'bq_matrix',
    'gcp_conn_id': 'bq_matrix',
}

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
dag_variable = Variable.get("splio_report_campaign_past", deserialize_json=True)
splio_universes = dag_variable["active_universes"]
period = dag_variable["period"]
dag_name = 'splio_report_campaign_past'

splio_params = {
    'key': '7af061af0f7af8d2bf4542e00b2f41eb8a844494',
    'channel': 'email',
    'after': '{date_from}',
    'before': '{date_to}',
    'universe': '{univers}',
}

universe_fragments = {}
fragment_ranges = [
    (0, 3), (3, 6), (6, 10), (6, 13), (13, 16), (16, 19), (19, 22), (22, 25), (25, 27), (27, 30),
    (30, 33), (33, 36), (36, 39), (39, 42), (42, 45), (45, 47), (47, len(splio_universes))
]
for i, (start, end) in enumerate(fragment_ranges, 1):
    universe_fragments[i] = dict(itertools.islice(splio_universes.items(), start, end))


def call_api_by_day(period, universes, **kwargs):
    start = datetime.strptime(period['start'], "%Y/%m/%d").date()
    end = datetime.strptime(period['end'], "%Y/%m/%d").date()

    start = start - timedelta(days=1)

    while start < end:
        print('after_date : {}'.format(start.strftime('%Y-%m-%d')))
        print('before_date : {}'.format((start + timedelta(days=2)).strftime('%Y-%m-%d')))

        call_api = SplioReportsImportOperator(task_id='call_api',
                                              universes=universes,
                                              splio_connection=splio_params,
                                              before_date=(start + timedelta(days=2)).strftime('%Y-%m-%d'),
                                              after_date=start.strftime('%Y-%m-%d'),
                                              postgres_conn_id='psql_matrix_email_app',
                                              destination_table='splio_report_import_past',
                                              database='matrix',
                                              gcs_conn_id='gcs_matrix',
                                              bucket=buckets['matrix'])

        call_api.execute(dict())
        start = start + timedelta(days=1)


with CDocDAG(dag_id=dag_name,
             description='Retrieve past report information',
             tags=["splio", "lop"],
             doc_md=__doc__,
             schedule_interval="0 0 1 1 *",
             catchup=False,
             max_active_runs=1,
             concurrency=10,
             template_searchpath=['/home/<USER>/gcs/data/sql/tracking/splio/reports/'],
             dagrun_timeout=timedelta(days=2),
             default_args=default_args) as dag:

    with TaskGroup(group_id='preparation', tooltip='Prepare PostgreSQL Import Table') as preparation_tg:
        task_doc_md = '''
        Delete old reports > 7 days and create temporary table <br />
        '''
        prepare_import = SQLExecuteQueryOperator(task_id='prepare_import',
                                          sql='past/sql/0_prepare_import.sql',
                                          conn_id='psql_matrix_email_app',
                                          database='matrix')
        prepare_import.doc_md = task_doc_md


    with TaskGroup(group_id='api_data_fetching', tooltip='Fetch data from Splio API') as api_fetching_tg:
        api_call_tasks = []
        for index, universe_fragment in universe_fragments.items():
            task_doc_md = '''
                Call Splio Api by parallelizing the universes<br />
                '''
            call_api_by_day_frag = PythonOperator(task_id='call_api_by_day_frag_'+str(index),
                                                   python_callable=call_api_by_day,
                                                   op_kwargs={'period': period, 'universes': universe_fragment},
                                                   provide_context=True)
            call_api_by_day_frag.doc_md = task_doc_md
            api_call_tasks.append(call_api_by_day_frag)

    with TaskGroup(group_id='postgresql_processing', tooltip='Process Data in PostgreSQL') as postgresql_processing_tg:
        task_doc_md = '''
        Dispatch data from temporary table into real table <br />
        '''
        build_data_sql = SQLExecuteQueryOperator(task_id='build_data_sql',
                                          sql='past/sql/2_build_reports_past_data.sql',
                                          conn_id='psql_matrix_email_app',
                                          database='matrix')
        build_data_sql.doc_md = task_doc_md

    with TaskGroup(group_id='data_transfer_psql_to_bq', tooltip='Transfer Data from PostgreSQL to BigQuery') as data_transfer_tg:
        # Schemas defined here only for proximity to operators using them, no logic change
        report_schemas = [{"mode": "REQUIRED", "name": "id", "type": "INTEGER"},
                          {"mode": "REQUIRED", "name": "sendout_id", "type": "STRING"},
                          {"mode": "REQUIRED", "name": "campaign_id", "type": "STRING"},
                          {"mode": "REQUIRED", "name": "message_id", "type": "STRING"},
                          {"mode": "NULLABLE", "name": "rogue_one_email_id", "type": "STRING"},
                          {"mode": "REQUIRED", "name": "starttime", "type": "TIMESTAMP"},
                          {"mode": "REQUIRED", "name": "endtime", "type": "TIMESTAMP"},
                          {"mode": "REQUIRED", "name": "create_date", "type": "TIMESTAMP"},
                          {"mode": "REQUIRED", "name": "update_date", "type": "TIMESTAMP"},
                          {"mode": "NULLABLE", "name": "targets", "type": "INTEGER"},
                          {"mode": "NULLABLE", "name": "sent", "type": "INTEGER"},
                          {"mode": "NULLABLE", "name": "delivered", "type": "INTEGER"},
                          {"mode": "NULLABLE", "name": "soft_bounce", "type": "INTEGER"},
                          {"mode": "NULLABLE", "name": "hard_bounces", "type": "INTEGER"},
                          {"mode": "NULLABLE", "name": "clickers", "type": "INTEGER"},
                          {"mode": "NULLABLE", "name": "openers", "type": "INTEGER"},
                          {"mode": "NULLABLE", "name": "opens", "type": "INTEGER"},
                          {"mode": "NULLABLE", "name": "clicks", "type": "INTEGER"},
                          {"mode": "NULLABLE", "name": "spam_complaints", "type": "INTEGER"},
                          {"mode": "NULLABLE", "name": "unsubscribes", "type": "INTEGER"},
                          {"mode": "REQUIRED", "name": "universe_id", "type": "INTEGER"}]

        task_doc_md = '''
        Export PostgreSQL table : report into BQ <br />
        '''
        import_report_to_bq = PostgresToBigQueryOperator(
            task_id='import_report_to_bq',
            database='matrix',
            table='matrix__email_splio.report',
            bucket=buckets['matrix'],
            source_object='splio/campaign_stats/exports/export_tracking_splio_report_{{ ds_nodash }}.csv',
            destination_project_dataset_table='pm-prod-matrix:import.tracking_splio_report_{{ ds_nodash }}',
            schema_fields=report_schemas,
            field_delimiter='\t',
            quote_character='',
            allow_quoted_newlines=True,
            write_disposition='WRITE_TRUNCATE',
        )
        import_report_to_bq.doc_md = task_doc_md

        report_data_schemas = [{"mode": "REQUIRED", "name": "report_id", "type": "INTEGER"},
                               {"mode": "REQUIRED", "name": "data", "type": "STRING"}]
        task_doc_md = '''
        Export PostgreSQL table : report_data into BQ <br />
        '''
        import_report_data_to_bq = PostgresToBigQueryOperator(
            task_id='import_report_data_to_bq',
            database='matrix',
            table='matrix__email_splio.report_data',
            bucket=buckets['matrix'],
            source_object='splio/campaign_stats/exports/export_tracking_splio_report_data_{{ ds_nodash }}.csv',
            destination_project_dataset_table='pm-prod-matrix:import.tracking_splio_report_data_{{ ds_nodash }}',
            schema_fields=report_data_schemas,
            field_delimiter='\t',
            quote_character='',
            allow_quoted_newlines=True,
            write_disposition='WRITE_TRUNCATE',
        )
        import_report_data_to_bq.doc_md = task_doc_md

        report_link_schemas = [{"mode": "REQUIRED", "name": "report_id", "type": "INTEGER"},
                               {"mode": "REQUIRED", "name": "link_id", "type": "INTEGER"},
                               {"mode": "NULLABLE", "name": "clicks", "type": "INTEGER"},
                               {"mode": "REQUIRED", "name": "url", "type": "STRING"},
                               {"mode": "NULLABLE", "name": "name", "type": "STRING"},
                               {"mode": "NULLABLE", "name": "category", "type": "STRING"}]
        task_doc_md = '''
        Export PostgreSQL table : report_link into BQ <br />
        '''
        import_report_link_to_bq = PostgresToBigQueryOperator(
            task_id='import_report_link_to_bq',
            database='matrix',
            table='matrix__email_splio.report_link',
            bucket=buckets['matrix'],
            source_object='splio/campaign_stats/exports/export_tracking_splio_report_link_{{ ds_nodash }}.csv',
            destination_project_dataset_table='pm-prod-matrix:import.tracking_splio_report_link_{{ ds_nodash }}',
            schema_fields=report_link_schemas,
            field_delimiter='\t',
            quote_character='',
            allow_quoted_newlines=True,
            write_disposition='WRITE_TRUNCATE',
        )
        import_report_link_to_bq.doc_md = task_doc_md

        # No explicit dependencies needed here as they run in parallel within the group

    with TaskGroup(group_id='bigquery_processing', tooltip='Process Data in BigQuery') as bigquery_processing_tg:
        task_doc_md = '''
        Merge all imported tables into prepare from BQ <br />
        '''
        prepare_bq_data = BigQueryExecuteQueryOperator(task_id='prepare_bq_data',
                                           use_legacy_sql=False,
                                           sql='sql/2_prepare_reports.sql')
        prepare_bq_data.doc_md = task_doc_md

        task_doc_md = '''
        Update store of data from prepare (BQ)
        <br />
        '''
        mep_bq_data = BigQueryExecuteQueryOperator(task_id='mep_bq_data',
                                       use_legacy_sql=False,
                                       sql='sql/3_mep_reports.sql')
        mep_bq_data.doc_md = task_doc_md

        prepare_bq_data >> mep_bq_data


    preparation_tg >> api_fetching_tg >> postgresql_processing_tg >> data_transfer_tg >> bigquery_processing_tg