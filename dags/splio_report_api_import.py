# -*- coding: utf-8 -*-
r"""
__PURPOSE:__<br />

This manual DAG calls Splio API report for each universe - see : <a href="https://support.splio.com/hc/en-us/articles/360015012839-Report-API">here</a><br />
Unlike splio_report.py DAG, this DAG accepts input period parameters. See <a href="https://j5fc9f889c8d8fba3p-tp.appspot.com/admin/variable/edit/?id=12523&url=%2Fadmin%2Fvariable%2F%3Fsearch%3Dsplio_report_">here</a><br />

<br/>

__METHODOLOGY:__<br />

**BigQuery-Native Architecture** - Eliminates PostgreSQL dependency<br />
splio_report_api_import will :<br />

- Call Splio API in parallel chunks by universe
- Store raw API data directly to BigQuery import tables via GCS
- Process JSON data using BigQuery native functions
- Merge processed data into store_tracking tables using BigQuery MERGE operations
- Save email HTML templates to GCS for reference

**Performance Optimizations:**<br />
- Direct API-to-BigQuery pipeline (no PostgreSQL intermediary)
- Dynamic universe chunking for parallel processing
- Batch JSON processing in BigQuery
- Single MERGE operation for all data types

__HOW TO Launch this dag:__<br />

- Switch off splio_report.py DAG before starting this DAG<br />
- Splio_report.py and splio_report_api_import.py must not run simultaneously <br />
- Use airflow variables to select universes for data retrieval<br />
- Change period with Airflow Variable to retrieve all information in date range<br />

__USEFUL COMMAND TO TEST:__ <br />

gsutil  cp      dags/splio_report_api_import.py                                         gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/
gsutil  cp      plugins/bq_plugin/operators/splio_reports_bq_operator.py                gs://europe-west1-preprod-mozart-2fa49086-bucket/plugins/bq_plugin/operators/
gsutil  cp      data/sql/tracking/splio/reports/past/bq/0_prepare_import_tables.sql     gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/tracking/splio/reports/past/bq/
gsutil  cp      data/sql/tracking/splio/reports/past/bq/1_process_reports_only.sql      gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/tracking/splio/reports/past/bq/
gsutil  cp      data/sql/tracking/splio/reports/past/bq/2_process_data_links.sql        gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/tracking/splio/reports/past/bq/
gsutil  cp      data/sql/tracking/splio/reports/past/bq/3_cleanup_tables.sql            gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/tracking/splio/reports/past/bq/

"""


import os
from datetime import datetime, timedelta

from airflow.providers.google.cloud.operators.bigquery import (
    BigQueryExecuteQueryOperator,
)
from airflow.models import Variable
from airflow.utils.task_group import TaskGroup
from collapsible_doc_dag import CDocDAG
from bq_plugin.operators.splio_reports_bq_operator import SplioReportsBigQueryOperator

# Environment-based configuration
env = os.environ.get("ENV")
bq_project = f"pm-{env}-matrix"
email_on_failure = True

if env != "prod":
    email_on_failure = False
    bq_project = "pm-preprod-matrix"

default_args = {
    "owner": "airflow",
    "start_date": datetime(2021, 8, 11, 0, 0, 0),
    "email": [eval(Variable.get("airflow_email_alertes"))],
    "email_on_failure": email_on_failure,
    "log_response": True,
    "xcom_push": True,
    "provide_context": True,
    "execution_timeout": timedelta(days=2),
    "sla": None,
    "email_on_retry": False,
    "retries": 2,
    "retry_delay": timedelta(minutes=5),
    "depends_on_past": False,
    ## GCS Connection
    "gcs_conn_id": "gcs_matrix",
    ## BQ Connection
    "bigquery_conn_id": "bq_matrix",
    "gcp_conn_id": "bq_matrix",
}

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
dag_variable = Variable.get("splio_report_campaign_past", deserialize_json=True)
splio_universes = dag_variable["active_universes"]
period = dag_variable["period"]
dag_name = "splio_report_campaign_past"

if env != "prod":
    dag_name = dag_name + "_" + env

splio_params = {"key": "7af061af0f7af8d2bf4542e00b2f41eb8a844494", "channel": "email"}


# Dynamic universe chunking for better load distribution
def create_universe_chunks(universes, chunk_size=3):
    """Create dynamic chunks from universes for parallel processing"""
    universe_list = list(universes.items())
    chunks = {}
    for i in range(0, len(universe_list), chunk_size):
        chunk_id = (i // chunk_size) + 1
        chunks[chunk_id] = dict(universe_list[i : i + chunk_size])
    return chunks


universe_chunks = create_universe_chunks(splio_universes, chunk_size=3)


with CDocDAG(
    dag_id=dag_name,
    description="Retrieve past report information - BigQuery Native",
    tags=["splio", "lop", "bigquery"],
    doc_md=__doc__,
    schedule_interval="0 0 1 1 *",
    catchup=False,
    max_active_runs=1,
    concurrency=10,
    template_searchpath=["/home/<USER>/gcs/data/sql/tracking/splio/reports/past/bq/"],
    dagrun_timeout=timedelta(days=2),
    default_args=default_args,
    params={"bq_project": bq_project},
) as dag:

    # Prepare BigQuery tables
    prepare_bq_tables = BigQueryExecuteQueryOperator(
        task_id="prepare_bq_tables",
        use_legacy_sql=False,
        sql="0_prepare_import_tables.sql",
        params={"bq_project": bq_project},
    )
    prepare_bq_tables.doc_md = """
    Create BigQuery import and processing tables for raw API data and structured output
    """

    # Parallel API data fetching directly to BigQuery
    with TaskGroup(
        group_id="api_data_fetching",
        tooltip="Fetch data from Splio API directly to BigQuery",
    ) as api_fetching_tg:
        api_call_tasks = []
        for chunk_id, universe_chunk in universe_chunks.items():
            fetch_task = SplioReportsBigQueryOperator(
                task_id=f"fetch_api_data_chunk_{chunk_id}",
                universes=universe_chunk,
                splio_connection=splio_params,
                period=period,
                bigquery_conn_id="bq_matrix",
                gcs_conn_id="gcs_matrix",
                bucket=buckets["matrix"],
                bq_project=bq_project,
                chunk_id=str(chunk_id),
            )
            fetch_task.doc_md = f"""
            Fetch Splio API data for universe chunk {chunk_id} and store directly in BigQuery<br/>
            Processes {len(universe_chunk)} universes: {list(universe_chunk.keys())}
            """
            api_call_tasks.append(fetch_task)

    # Process reports only (first step)
    process_reports = BigQueryExecuteQueryOperator(
        task_id="process_reports",
        use_legacy_sql=False,
        sql="1_process_reports_only.sql",
        params={"bq_project": bq_project, "chunk_count": len(universe_chunks)},
    )
    process_reports.doc_md = """
    Process raw JSON API data into structured report table only<br/>
    - Extracts campaign metrics from JSON<br/>
    - Determines insert/update actions for reports<br/>
    - Merges reports into store_tracking.splio_report
    """

    # Process data and links (second step - after reports have real IDs)
    process_data_links = BigQueryExecuteQueryOperator(
        task_id="process_data_links", 
        use_legacy_sql=False,
        sql="2_process_data_links.sql",
        params={"bq_project": bq_project, "chunk_count": len(universe_chunks)},
    )
    process_data_links.doc_md = """
    Process report data and links with real IDs from store table<br/>
    - Uses real report IDs from store_tracking.splio_report<br/>
    - Processes JSON payload and link data<br/>
    - Merges into store_tracking tables
    """

    # Cleanup temporary tables
    cleanup_tables = BigQueryExecuteQueryOperator(
        task_id="cleanup_tables",
        use_legacy_sql=False,
        sql="3_cleanup_tables.sql", 
        params={"bq_project": bq_project, "chunk_count": len(universe_chunks)},
    )
    cleanup_tables.doc_md = """
    Clean up all temporary tables created during processing
    """

    # Define task dependencies
    prepare_bq_tables >> api_fetching_tg >> process_reports >> process_data_links >> cleanup_tables
