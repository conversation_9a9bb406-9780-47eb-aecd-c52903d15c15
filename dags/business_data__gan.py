r"""
**PURPOSE:**

This dag is used to compute GAN kpis based on their activity.

**METHODOLOGY:**

__USEFUL COMMAND TO**TEST:**
"""

# for test directly on preprod :
# gsutil -m cp dags/business_data__gan.py gs://europe-west1-preprod-mozart-2fa49086-bucket/dags
# gsutil -m cp -R data/sql/business_data/gan/* gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/business_data/gan/

# gsutil -m cp -R dags/business_data__gan.py gs://europe-west1-prod-mozart-co-2ef7e904-bucket/dags
# gsutil -m cp -R data/sql/business_data/gan/* gs://europe-west1-prod-mozart-co-2ef7e904-bucket/data/sql/business_data/gan/

import os
from datetime import timedelta, datetime
from airflow.models import Variable
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.common.sql.sensors.sql import SqlSensor
from collapsible_doc_dag import CDocDAG


# get full_export and interval values
business_data__gan = Variable.get("business_data__gan", deserialize_json=True)

# ----------------- CONFIG ----------------------
dag_name = 'business_data__gan'
env = os.environ.get("ENV")
bq_project_matrix = 'pm-{}-matrix'.format(env)
bq_project_userhub = 'pm-{}-userhub'.format(env)
bq_project_mirror = 'pm-{}-mirror'.format(env)
bq_project_ga4 = 'pm-{}-ga4'.format(env)
# bq_project_gan_pm = 'prisma-gan-bigquery-ojd-export'
email_on_failure = True

if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    # force preprod on other env
    bq_project_matrix = 'pm-{}-matrix'.format(env)
    bq_project_userhub = 'pm-{}-userhub'.format(env)
    bq_project_mirror = 'pm-{}-mirror'.format(env)

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2023, 1, 25, 00, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': False,
    'provide_context': True,
    'execution_timeout': timedelta(hours=4),
    'sla': None,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=5),
    'depends_on_past': False,

    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_APPEND',
    # GCP CONNECTION
    'gcp_conn_id': 'gcs_matrix',
    # PSQL Connection
    'postgres_conn_id': 'psql_matrix_email_app',

    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',  # possibles values: INTERACTIVE and BATCH.
}

with CDocDAG(
        dag_name,
        description='Compute gan stats',
        tags=["business", "gan"],
        doc_md=__doc__,
        schedule_interval="0 8 * * *",
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=4),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/business_data/gan/'],
        concurrency=10
) as dag:

    task_doc_md = """
        We need to wait for refined gan navigation (all visits) to generate simulated open events
        """
    # @Doc --> https://airflow.readthedocs.io/en/1.9.0/_modules/airflow/operators/sensors.html
    wait_for_refined_navigation = SqlSensor(
        task_id='wait_for_refined_navigation',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                    SELECT CASE WHEN (task_id = 'refined_gan_navigation_all_visits' AND state = 'success') THEN TRUE ELSE FALSE END
                    FROM task_instance
                    WHERE dag_id = 'pmc_gan_navigation{str_env}'
                      AND task_id = 'refined_gan_navigation_all_visits'
                      AND DATE(start_date) = CURRENT_DATE
                """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_refined_navigation.doc_mc = task_doc_md

    task_doc_md = """
        We need to wait for article refined_data__edito to be available
        """
    # @Doc --> https://airflow.readthedocs.io/en/1.9.0/_modules/airflow/operators/sensors.html
    wait_for_refined_article_contents = SqlSensor(
        task_id='wait_for_refined_article_contents',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                    SELECT CASE WHEN (task_id = 'refine_article_contents' AND state = 'success') THEN TRUE ELSE FALSE END
                    FROM task_instance
                    WHERE dag_id = 'refined_data__edito{str_env}'
                      AND task_id = 'refine_article_contents'
                      AND DATE(start_date) = CURRENT_DATE
                """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_refined_article_contents.doc_mc = task_doc_md



    task_doc_md = """
        Compute GAN navigation performances on articles.
        """
    gan_article_performance = BigQueryExecuteQueryOperator(
        task_id="gan_article_performance",
        sql="gan_article_performance.sql",
        params={
            'bq_project_matrix': bq_project_matrix,
            'bq_project_mirror': bq_project_mirror,
            'bq_project_ga4': bq_project_ga4,
            'is_full': business_data__gan['top_article']['is_full'],
            'time_interval': business_data__gan['top_article']['time_interval'],
            'start_date': business_data__gan['top_article']['start_date'],
            'end_date': business_data__gan['top_article']['end_date'],
        }
    )
    gan_article_performance.doc_md = task_doc_md

    wait_for_refined_navigation >> gan_article_performance
    wait_for_refined_article_contents >> gan_article_performance


