r"""
**PURPOSE:**
This dag is used to export Batch data from Batch SFTP and save it into bigquery.**We exract two bases:**-**UserBase:**Each row correspond to bach install. In this data, we find information like last_push_date, last_visit_date and other informations about batch user. 
-**EventBase:**Contains batch event information like event_type, custom_user_id, campaign_token, and other informations about batch event . 
-**Campaign:**Contains campaigns information as name, creation date, segment, etc... 

For more details,
**you can get a look to the EPIC description:**
**https:**//pmdtech.atlassian.net/browse/ITDATA-1104?atlOrigin=eyJpIjoiM2JkNDJkOTU3ODE2NGU5M2I5MDA2ZGJmMDRkMjhkMmEiLCJwIjoiaiJ9**Batch mobile support doc:**
**https:**//docs.google.com/document/d/****************************-HyMfZDzhcB9H2JI/edit?usp=sharing**Batch web support doc:**
**https:**//docs.google.com/document/d/1Z-734MTzbCAJuo6iKDaHkgVKpKgUhgqMpQ0YeT2UDIc/edit?usp=sharing 

**METHODOLOGY:**

Batch export data via SFTP/GCS for events and userbase and call API for campaigns.
For each API KEY, we retreive events/user data from each CSV saved into GCS bucket and we get campaigns data through call API Batch.
Finally, we do the import --> prepare --> store process.

-**Import:**We import CSVs files from GCS to BQ. 
-**Preapre:**Clean data by casting fields to the right data type and extract some fields from the payload. 
-**Store:**Load prepared data into a store_table in store_batch dataset. 

To more understand the process, you can get a look to the draw.**io schema:**
**https:**//drive.google.com/file/d/1LTQzxXwfPU4I0mDHde80NXOXdZ_AvDgZ/view?usp=sharing 

**HOW_TO:**
**Events:**- To active incremental process, you should change "full_export" value to "False" and fix the interval through the key "interval" and "interval_str" 
    - "interval": int is used to generate time slot 
    - "interval_str": str is used to select only BQ rows that are in time slot (used in store SQL file)**Campaigns:**- We fix a start year on the batch_import_var, so you can fix a start year as we want(default value =2021) 
    - To active incremental process, you should change "full_export" value to "False" and fix the interval through the key "interval"
"""

import ast
import json
import logging
import os
import tempfile
from collections import defaultdict
from datetime import timedelta, datetime, date
from time import sleep

import pandas as pd
import requests as req
from airflow.models import Variable
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.providers.google.cloud.hooks.bigquery import BigQueryHook
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.google.cloud.operators.gcs import GCSDeleteObjectsOperator
from airflow.providers.google.cloud.sensors.gcs import GCSObjectExistenceSensor
from airflow.providers.google.cloud.transfers.gcs_to_bigquery import GCSToBigQueryOperator
from collapsible_doc_dag import CDocDAG
from google.cloud import bigquery
from google.cloud import storage

import itdata_plugin

# ---------------------------------------- INIT VARIABLES ------------------------------------------

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
batch_api_key = Variable.get("batch_api_key", deserialize_json=True)
batch_import_var = Variable.get("batch_import_var", deserialize_json=True)
# upload batch REST API KEY
batch_rest_api_key = Variable.get("batch_rest_api_key", deserialize_json=True)

api_key_list = batch_api_key.keys()
sftp_bucket = buckets['sftp']
sftp_bucket_url = 'gs://pm-prod-sftp-storage/'
matrix_bucket = buckets['matrix']
matrix_bucket_url = 'gs://it-data-prod-matrix-pipeline/'
# CURRENT_DATE = '{{ next_ds.strftime("%Y-%m-%d") }}'
# YESTERDAY_DATE = '{{ ds.strftime("%Y-%m-%d") }}'
YESTERDAY_DATE = str(date.today() - timedelta(days=1))
CURRENT_DATE = str(date.today())
dag_name = 'batch_import'
bq_project = 'pm-prod-matrix'
schemas_names = ['batch_event_ios_android.json',
                 'batch_event_web.json',
                 'batch_userbase_ios_android.json',
                 'batch_userbase_web.json',
                 'batch_campaign.json']
searchpath = '/home/<USER>/gcs/data/sql/batch_import/'


env = os.environ.get("ENV")
str_env = '' if env == 'prod' else '_' + env
dag_name = f'batch_import{str_env}'
email_on_failure = False
#True if env == 'prod' else False
bq_project= f'pm-{env}-matrix'
matrix_bucket_url = 'gs://it-data-prod-matrix-pipeline/' if env == 'prod' else 'gs://it-data-preprod-matrix-preprod-pipeline/'


# ---------------------------------------- GLOBAL FUNCTIONS ----------------------------------------

# Loading JSON schemas
def set_schemas_callable(**kwargs):
    """
        Parses JSON schema and setting Airflow variables
        :return:
    """
    for schema in schemas_names:
        basename = schema.rsplit('.', 1)[0]
        load_schema = json.load(
            open('{}schemas/{}'.format(searchpath, schema), 'r'))
        return itdata_plugin.set_variable_in_secret_manager('schema_{}'.format(basename), load_schema)
    return None


# ---------------------------------------- CAMPAIGNS  BASE FUNCTIONS -------------------------------


def download_all_campaigns(key, start_year, is_incremental, interval):
    """
        Download all campaigns for a API key and return a dict
    """
    campaign_dict = defaultdict(lambda: {'payload': []})
    logging.info(f"key {key}".format(key))
    i = 0
    is_after_start_year = True
    is_empty = False
    while is_after_start_year and not is_empty:
        step = 100
        start = step * i
        campaign_api_url = f'https://api.batch.com/1.1/{key}/campaigns/list?from={start}&limit=99'.format(key, start)
        logging.info(f"downlaod start at : {campaign_api_url}".format(campaign_api_url))
        campaign_list = req.request("GET", campaign_api_url,
                                    headers={
                                        'Content-Type': 'application/json',
                                        'X-Authorization': batch_rest_api_key["key"]
                                    }
                                    )
        # load campaigns into json
        campaign_json = json.loads(campaign_list.content)
        # check if response is not empty
        is_empty = len(campaign_json) == 0
        if not is_empty:
            # check if campaign creation date is after start_year
            is_after_start_year = datetime.strptime(
                campaign_json[-1]["created_date"], "%Y-%m-%dT%H:%M:%S").year >= start_year
        # get only campaign after start year
        filtred_campaign_json = [e for e in campaign_json if datetime.strptime(
            e["created_date"], "%Y-%m-%dT%H:%M:%S").year >= start_year]
        if is_incremental.lower() != 'true':
            start_date = str(date.today() - timedelta(days=interval))
            end_date = str(date.today())
            is_after_start_year = str(datetime.strptime(
                campaign_json[-1]["created_date"], "%Y-%m-%dT%H:%M:%S").date()) >= start_date
            filtred_campaign_json = [
                e for e in campaign_json if start_date <= e["created_date"] <= end_date]
        for r in filtred_campaign_json:
            campaign_dict[key]['payload'].append(r)
        i += 1
        # sleep to avoid TOO_MANY_REQUESTS error
        # wait at least for 5 s before trying again
        # cf: https://doc.batch.com/api/in-app-campaigns-api/list#request-structure
        sleep(5)
    logging.info("campaign downlaod is finished !")
    return campaign_dict


def campaign_per_date(campaign_dict, key):
    """
        Dispatch campaign by key, date and return a dict
    """
    campaign_key_date_dict = defaultdict(
        lambda: defaultdict(lambda: {'payload': []}))
    # get all batch campaigns per key
    campaign_list = campaign_dict[key]['payload']
    logging.info("Dispatch campaign per date start !")
    # dispatch campaign corresponding on its creation date
    for campaign in campaign_list:
        date = datetime.strptime(
            campaign["created_date"], '%Y-%m-%dT%H:%M:%S').strftime('%Y-%m-%d')
        # fix unicode conversion
        campaign_key_date_dict[key][date]['payload'].append(
            json.dumps(campaign, ensure_ascii=False).encode('utf-8').decode())
    logging.info("Dispatch campaign per date finish !")
    return campaign_key_date_dict


def import_campaign_to_gcs_callable(key, matrix_bucket, start_year, is_incremental, interval, **kwargs):
    """
        Import Dispatch campaign by key, date to GCS
    """
    logging.info("Import to GCS start !")
    campaign_dict = download_all_campaigns(
        key, start_year, is_incremental, interval)
    campaign_key_date_dict = campaign_per_date(campaign_dict, key)
    for date in campaign_key_date_dict[key]:
        # save content in local & upload to GCS & clean local
        # create tmp file per date and key
        tmp_fd, tmp_file = tempfile.mkstemp(text=True)
        # create temp dataframe based on tmp_file
        tmp_df = pd.DataFrame(campaign_key_date_dict[key][date])
        tmp_df.to_csv(tmp_file, index=False)
        gcs_hook = GCSHook(
            gcp_conn_id='gcs_matrix')
        gcs_hook.upload(
            bucket_name=matrix_bucket,
            object_name=f'temp/campaigns/PRISMA-campaign_{key}_{date}.csv'.format(key, date),
            filename=tmp_file,
            gzip=False,
        )
        os.remove(tmp_file)
        os.close(tmp_fd)
    logging.info("GCS upload finish for all API keys")


def import_campaign_to_bq_callable(key, matrix_bucket, bq_project, schema, current_date, is_incremental, interval,
                                   **kwargs):
    """
        Import downloaded campaigns from GCS to BQ

    """
    logging.info("GCS to BQ import for {} API key".format(key))
    prefix = 'temp/campaigns/'
    # Initiate bq and GCS hooks
    bq_hook = BigQueryHook(gcp_conn_id='bq_matrix')
    gcs_hook = GCSHook(
        gcp_conn_id='gcs_matrix')
    import_table = bq_project + \
                   ':import.batch_campaign_{}_{}'.format(
                       batch_api_key.get(key), current_date)
    path = ['{}PRISMA-campaign_{}_*.csv'.format(prefix, key)]
    source_objects = [matrix_bucket_url + p for p in path]
    valid_source_objects = source_objects
    # generate dates and objects for incremental
    if is_incremental.lower() != 'true':
        source_objects = []
        end_date = date.today()
        start_date = end_date - timedelta(days=interval)
        dates_datetime = [start_date +
                          timedelta(days=i) for i in range(interval)]
        dates_str = [date.strftime('%Y-%m-%d') for date in dates_datetime]
        path = [
            '{}PRISMA-campaign_{}_{}.csv'.format(prefix, key, date) for date in dates_str]
        source_objects = [matrix_bucket_url + p for p in path]
        # get only existing CSV URI
        valid_source_objects = [
            matrix_bucket_url + obj for obj in path if gcs_hook.exists(bucket_name=matrix_bucket, object_name=obj)]
        logging.info("Existing GCS files are {} : ".format(
            valid_source_objects))
    logging.info("CSV files to be imported {}".format(source_objects))
    logging.info("destination table is :" + import_table)
    # create empty destination table in case of there's not a valid source object : No CSV files --> empty table
    creation_query = """
        DROP TABLE IF EXISTS `{import_table}`;
        CREATE TABLE IF NOT EXISTS `{import_table}`
        (
            payload STRING OPTIONS(description="payload contains information about campaign")
        );
    """.format(import_table=import_table.replace(":", "."))
    create_table = BigQueryExecuteQueryOperator(
        task_id='create_table',
        sql=creation_query,
        gcp_conn_id='bq_matrix',
        use_legacy_sql=False,
        write_disposition='WRITE_TRUNCATE',
        allow_large_results=True,
        flatten_results=False,
        priority='INTERACTIVE'
    )
    create_table.execute(kwargs)
    logging.info("Import table for {} API key is created !".format(key))
    if valid_source_objects:
        bq_hook.run_load(
            destination_project_dataset_table=import_table,
            schema_fields=ast.literal_eval(Variable.get(schema)),
            source_uris=valid_source_objects,
            autodetect=False,
            skip_leading_rows=1,
            write_disposition='WRITE_TRUNCATE',
            field_delimiter=';',
            source_format='CSV',
            max_bad_records=0,
            encoding='utf-8'
        )
        logging.info("Import is done !")
    else:
        logging.info("No File is found !")
        pass


# ---------------------------------------- EVENTS BASE FUNCTIONS -----------------------------------


def _load_gcs_files_to_bq(source_objects, bq_project, platform):
    """
        Insert CSV files name's to import into BQ table
        Args:
            * source_objects(list): URI list
            * bq_project(string): Bigquery project name
        return:
    """

    # init BQ client
    client = bigquery.Client(project=bq_project)
    logging.info("Create table ! ")
    sql_query = """
        DROP TABLE IF EXISTS `{bq_project}.workspace.batch_event_selected_files_{platform}`;
        CREATE TABLE IF NOT EXISTS `{bq_project}.workspace.batch_event_selected_files_{platform}`
        (
            object_name STRING OPTIONS(description="selected CSV name to be imported")
        )
        OPTIONS(description="contains selected batch event files to be imported from GCS to BQ");

    """.format(bq_project=bq_project, platform=platform)
    # execute query
    client.query(sql_query, location="EU")
    # to avoid Client API delay
    sleep(5)
    logging.info("Insert into table ! ")
    sql_query = """
            INSERT INTO `{bq_project}.workspace.batch_event_selected_files_{platform}`
                (object_name)
            VALUES(@object_name);
    """.format(bq_project=bq_project, platform=platform)
    for (i, csv_object) in enumerate(source_objects):
        logging.info("object {} : {} ".format(i, csv_object))
        # job config
        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("object_name", "STRING", csv_object)
            ]
        )
        query_result = client.query(
            sql_query, job_config=job_config, location="EU")
        logging.info("Query result is : {}".format(dict(query_result)))
        # to avoid Client API delay
        sleep(5)
    # to avoid Client API delay
    sleep(5)


def _import_event_gcs_to_bq(**kwargs):
    """
        Keep only non imported CSV files name's and load files to import into BQ
        Args:
        return:
    """
    # init variables
    source_objects = kwargs['source_objects']
    bq_project = kwargs['bq_project']
    logging.info("Files Selected {} ".format(source_objects))
    schema = kwargs['schema']
    platform = kwargs['platform']
    current_date = kwargs['current_date']
    client = bigquery.Client(project=bq_project)
    # 1. load gcs files names from GSC to BQ
    _load_gcs_files_to_bq(source_objects, bq_project, platform)

    # 2. prepare BQ tables
    sql_query = """
        -- Files to import Table
        DROP TABLE IF EXISTS `{bq_project}.workspace.batch_event_files_to_import_{platform}`;
        CREATE TABLE IF NOT EXISTS `{bq_project}.workspace.batch_event_files_to_import_{platform}`
        (
            object_name STRING OPTIONS(description="CSV name to import")
        )
        OPTIONS(description="contains batch event files to import from GCS to BQ");

        -- Imported files Table
        CREATE TABLE IF NOT EXISTS `{bq_project}.workspace.batch_event_imported_files_{platform}`
        (
            object_name STRING OPTIONS(description="imported CSV name")
        )
        OPTIONS(description="contains imported batch event files from GCS to BQ");

    """.format(bq_project=bq_project, platform=platform)
    # execute query
    client.query(sql_query, location="EU")
    # to avoid API call delay
    sleep(5)

    # 3. load CSV files
    sql_query = """
        -- get files to import
        INSERT INTO `{bq_project}.workspace.batch_event_files_to_import_{platform}`
        SELECT sf.object_name
        FROM `{bq_project}.workspace.batch_event_selected_files_{platform}` AS sf
        LEFT JOIN `{bq_project}.workspace.batch_event_imported_files_{platform}` AS ifi USING(object_name)
        WHERE ifi.object_name IS NULL;
    """.format(bq_project=bq_project, platform=platform)
    # execute query
    client.query(sql_query, location="EU")
    # to avoid API call delay
    sleep(5)

    # 4. extract files to import into list
    sql_query = """
        SELECT * FROM `{bq_project}.workspace.batch_event_files_to_import_{platform}`;
    """.format(bq_project=bq_project, platform=platform)
    _objects = [row['object_name']
                for row in client.query(sql_query, location="EU")]
    logging.info("Files to Import {}".format(_objects))
    # to avoid API call delay
    sleep(5)

    # 5. import files to import table: GCS to BQ
    import_event_to_bq = GCSToBigQueryOperator(
        task_id='import_event_to_bq',
        source_objects=_objects,
        destination_project_dataset_table=bq_project +
                                          ':import.batch_event_{}_{}'.format(
                                              platform, current_date),
        source_format='CSV',
        field_delimiter=';',
        schema_fields=ast.literal_eval(Variable.get(schema)),
        skip_leading_rows=1,
        autodetect=False,
        max_bad_records=0,
        write_disposition='WRITE_TRUNCATE',
        allow_jagged_rows=True,
        bucket=sftp_bucket,
        gcp_conn_id='gcs_matrix'
    )
    import_event_to_bq.execute(kwargs)

    # 6. add imported files to imported_files table
    # to avoid API call delay
    sql_query = """
        -- insert imported files
        INSERT INTO `{bq_project}.workspace.batch_event_imported_files_{platform}`
        SELECT * FROM `{bq_project}.workspace.batch_event_files_to_import_{platform}`;
    """.format(bq_project=bq_project, platform=platform)
    # execute query
    client.query(sql_query, location="EU")

    return True


def _purge_gcs_event(**kwargs):
    """
        Remove imported files from GCS bucket
        args:
            * csv objects: list of CSV to remove
        reuslt:
            * Purge Bucket
            * Truncate batch_event_files_to_import_{platform} table
    """
    bq_project = kwargs['bq_project']
    platform = kwargs['platform']
    client = bigquery.Client(project=bq_project)

    # 1. get object to remove
    sql_query = """
        SELECT * FROM `{bq_project}.workspace.batch_event_files_to_import_{platform}`
    """.format(bq_project=bq_project, platform=platform)
    _objects = [row['object_name']
                for row in client.query(sql_query, location="EU")]
    logging.info("Files to Purge {}".format(_objects))

    # 2. purge bucket
    purge_gcs_event = GCSDeleteObjectsOperator(
        task_id='purge_gcs_event',
        objects=_objects,
        bucket_name=sftp_bucket,
        gcp_conn_id='gcs_matrix'
    )
    purge_gcs_event.execute(kwargs)

    # 3. truncate files to import table
    sql_query = """
        DROP TABLE IF EXISTS {bq_project}.workspace.batch_event_last_imported_files_{platform};
        ALTER TABLE workspace.batch_event_files_to_import_{platform} RENAME TO batch_event_last_imported_files_{platform};
    """.format(bq_project=bq_project, platform=platform)
    client.query(sql_query, location="EU")
    return True


def list_filtered_blobs(**kwargs):
    """
        List filtered blobs in GCS bucket.
        'kwargs': bucket_name, filter_pattern
    """
    # storage_client = gcs client
    storage_client = storage.Client(project=bq_project)
    logging.info("Bucket to search on it : {bucket_name}".format(bucket_name=kwargs["bucket_name"]))
    blobs = storage_client.list_blobs(kwargs["bucket_name"])
    objects = [blob.name for blob in blobs if kwargs["filter_pattern"] in blob.name]
    logging.info("Filter pattern is {filter_pattern}".format(filter_pattern=kwargs["filter_pattern"]))
    logging.info("Objects are: {objects}".format(objects=objects))
    return objects


# --------------------------------------- DAG ARGS -------------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2021, 3, 22, 4, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=10),
    'sla': None,
    'email_on_retry': False,
    # no need to retries, if no file found on gcs , boom direct
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
    'depends_on_past': False,
    'bucket': sftp_bucket,

    # GCP CONNECTION
    'gcp_conn_id': 'gcs_matrix',

    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_APPEND',
    'create_disposition': 'CREATE_IF_NEEDED',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE'
}

# DAG start
with CDocDAG(
        dag_name,
        schedule_interval="0 5 * * *",
        tags=["batch"],
        default_args=default_args,
        max_active_runs=1,
        concurrency=10,
        dagrun_timeout=timedelta(hours=10),
        catchup=False,
        description='Import Batch data',
        doc_md=__doc__,
        template_searchpath=['/home/<USER>/gcs/data/sql/batch_import/']
) as dag:
    # parallelize task
    # use EmptyOperator to encounter (check, import, prepare, store) tasks by start,end tasks
    for key in api_key_list:
        is_web = False
        # get os name from key name
        if batch_api_key.get(key).split("_")[1] == "web":
            os_name = "webpush"
            is_web = True
        else:
            os_name = batch_api_key.get(key).split("_")[1]
        os_name_upper = os_name.upper()
        task_doc_md = """
        Use EmptyOperator to encounter (check, import, prepare, store) tasks by start,end tasks. <br />
        Start Task.
        """
        start_task = EmptyOperator(
            task_id='start_{}'.format(batch_api_key.get(key)))
        start_task.doc_md = task_doc_md
        task_doc_md = """
        Set JSON schemas in Airflow variables for {}. <br />
        """.format(batch_api_key.get(key))
        set_schemas = PythonOperator(
            task_id='set_schemas_{}'.format(batch_api_key.get(key)),
            python_callable=set_schemas_callable,
            provide_context=True
        )
        set_schemas.doc_md = task_doc_md
        # Part1 : Bacth Event Import
        # check if event files exists into GCS
        task_doc_md = """
        Check if yesterday events CSV is uploaded into Bucket  for {} ! <br />
        """.format(batch_api_key.get(key))
        gcs_check_event_files = GCSObjectExistenceSensor(
            task_id='gcs_check_event_files_{}'.format(
                batch_api_key.get(key)),
            object='home/uploads/batch/imports/events/PRISMA-events_{}@{}_{}.csv'.format(
                key, os_name_upper, YESTERDAY_DATE),
            google_cloud_conn_id='gcs_matrix',
            mode='reschedule',
            poke_interval=15,
            timeout=60 * 60 * 10
        )
        gcs_check_event_files.doc_md = task_doc_md

        # GCS TO BQ for each API key
        # full
        source_objects = list_filtered_blobs(bucket_name=sftp_bucket,
                                             filter_pattern='home/uploads/batch/imports/events/PRISMA-events_{}@{}_'.format(
                                                 key, os_name_upper))
        # source_objects = [
        #   'home/uploads/batch/imports/events/PRISMA-events_{}@{}_*.csv'.format(key, os_name_upper)]
        time_slot = [str(date.today() - timedelta(days=i))
                     for i in range(1, batch_import_var["event"]["interval"] + 1)]
        # incremental
        schema = 'schema_batch_event_ios_android'
        if batch_import_var["event"]["full_export"].lower() != "true":
            source_objects = ['home/uploads/batch/imports/events/PRISMA-events_{}@{}_{}.csv'.format(
                key, os_name_upper, d) for d in time_slot]
        if is_web:
            schema = 'schema_batch_event_web'
        task_doc_md = """
        Import batch events from GCS to BQ for {}. <br />
        """.format(batch_api_key.get(key))
        import_event_gcs_to_bq = PythonOperator(
            task_id="import_event_to_bq_{}".format(batch_api_key.get(key)),
            python_callable=_import_event_gcs_to_bq,
            op_kwargs={
                'source_objects': source_objects,
                'bq_project': bq_project,
                'platform': batch_api_key.get(key),
                'current_date': CURRENT_DATE,
                'schema': schema
            }

        )
        import_event_gcs_to_bq.doc_md = task_doc_md

        # prepare data to be store into BQ for each API key
        task_doc_md = """
        Prepare data to be set in batch store for {}. <br />
        """.format(batch_api_key.get(key))
        prepare_event_to_store = BigQueryExecuteQueryOperator(
            task_id='prepare_event_to_store_{}'.format(batch_api_key.get(key)),
            sql='prepare_events_to_store.sql',
            write_disposition='WRITE_TRUNCATE',
            params={
                'bq_project': bq_project,
                'current_date': CURRENT_DATE,
                'platform': batch_api_key.get(key),
                'is_web': is_web
            }
        )
        prepare_event_to_store.doc_md = task_doc_md

        # store prepared data into a final table
        task_doc_md = """
        Store prepared data into a final table for {}. <br />
        """.format(batch_api_key.get(key))
        store_event_in_bq = BigQueryExecuteQueryOperator(
            task_id='store_event_in_bq_{}'.format(batch_api_key.get(key)),
            sql='store_events_in_bq.sql',
            params={
                'bq_project': bq_project,
                'current_date': CURRENT_DATE,
                'platform': batch_api_key.get(key),
                'full_export': batch_import_var["event"]["full_export"],
                'interval': batch_import_var["event"]["interval_str"],
                'is_web': is_web
            }

        )
        store_event_in_bq.doc_md = task_doc_md

        # purge GCS bucket
        purge_gcs_event = PythonOperator(
            task_id='purge_gcs_event_{}'.format(batch_api_key.get(key)),
            python_callable=_purge_gcs_event,
            op_kwargs={
                'bq_project': bq_project,
                'platform': batch_api_key.get(key)
            }
        )

        # Part2 : Bacth User Import
        # check if userbase files exists into GCS
        task_doc_md = """
        Check if bucket is not empty for {}. <br />
        """.format(batch_api_key.get(key))
        gcs_check_userbase_files = GCSObjectExistenceSensor(
            task_id='gcs_check_userbase_files_{}'.format(
                batch_api_key.get(key)),
            object='home/uploads/batch/imports/userbase/PRISMA-userbase_{}@{}_{}.csv'.format(
                key, os_name_upper, YESTERDAY_DATE),
            google_cloud_conn_id='gcs_matrix',
            mode='reschedule',
            poke_interval=15,
            timeout=60 * 60
        )
        gcs_check_userbase_files.doc_md = task_doc_md

        # GCS TO BQ for each API key
        task_doc_md = """
        Import batch userbase from GCS to BQ for {}. <br />
        """.format(batch_api_key.get(key))
        schema = 'schema_batch_userbase_ios_android'
        if is_web == True:
            schema = 'schema_batch_userbase_web'
        import_userbase_to_bq = GCSToBigQueryOperator(
            task_id='import_userbase_to_bq_{}'.format(batch_api_key.get(key)),
            source_objects=['home/uploads/batch/imports/userbase/PRISMA-userbase_{}@{}_{}.csv'.format(
                key, os_name_upper, YESTERDAY_DATE)],
            destination_project_dataset_table=bq_project +
                                              ':import.batch_userbase_{}_{}'.format(
                                                  batch_api_key.get(key), CURRENT_DATE),
            source_format='CSV',
            field_delimiter=';',
            schema_fields=ast.literal_eval(Variable.get(schema)),
            skip_leading_rows=1,
            autodetect=False,
            max_bad_records=3,
            write_disposition='WRITE_TRUNCATE',
            allow_jagged_rows=True,
            bucket=sftp_bucket,
            gcp_conn_id='gcs_matrix',
        )
        import_userbase_to_bq.doc_md = task_doc_md

        # prepare data to be store into BQ for each API key
        task_doc_md = """
        Prepare data to be set in batch store for {}. <br />
        """.format(batch_api_key.get(key))
        prepare_userbase_to_store = BigQueryExecuteQueryOperator(
            task_id='prepare_userbase_to_store_{}'.format(
                batch_api_key.get(key)),
            sql='prepare_userbase_to_store.sql',
            write_disposition='WRITE_TRUNCATE',
            params={
                'bq_project': bq_project,
                'current_date': CURRENT_DATE,
                'platform': batch_api_key.get(key),
                'is_web': is_web
            }
        )
        prepare_userbase_to_store.doc_md = task_doc_md

        # store prepared data into a final table
        task_doc_md = """
        Store prepared data into a final table for {}. <br />
        """.format(batch_api_key.get(key))
        store_userbase_in_bq = BigQueryExecuteQueryOperator(
            task_id='store_userbase_in_bq_{}'.format(batch_api_key.get(key)),
            sql='store_userbase_in_bq.sql',
            params={
                'bq_project': bq_project,
                'current_date': CURRENT_DATE,
                'platform': batch_api_key.get(key),
                'is_web': is_web
            }

        )
        store_userbase_in_bq.doc_md = task_doc_md

        # purge GCS bucket
        purge_gcs_userbase = GCSDeleteObjectsOperator(
            task_id='purge_gcs_userbase_{}'.format(batch_api_key.get(key)),
            prefix="home/uploads/batch/imports/userbase/PRISMA-userbase_{}@{}".format(
                key, os_name_upper),
            bucket_name=sftp_bucket
        )
        purge_gcs_userbase.doc_md = 'purge gcs'

        # Part3 : Bacth Campaign Import
        task_doc_md = """
        Import campaigns by API calling to GCS for {}. <br />
        """.format(batch_api_key.get(key))
        import_campaign_to_gcs = PythonOperator(
            task_id='import_campaign_to_gcs_{}'.format(batch_api_key.get(key)),
            python_callable=import_campaign_to_gcs_callable,
            op_kwargs={
                'key': key,
                'matrix_bucket': matrix_bucket,
                'start_year': batch_import_var["campaign"]["start_year"],
                'is_incremental': batch_import_var["campaign"]["full_export"],
                'interval': batch_import_var["campaign"]["interval"]
            }
        )
        import_campaign_to_gcs.doc_md = task_doc_md

        # Import campaigns to BQ
        task_doc_md = """
        Import  batch campaign from GCS to BQ for {}. <br />
        """.format(batch_api_key.get(key))
        # set import table schema name
        schema = 'schema_batch_campaign'
        import_campaign_to_bq = PythonOperator(
            task_id='import_campaign_to_bq_{}'.format(batch_api_key.get(key)),
            python_callable=import_campaign_to_bq_callable,
            op_kwargs={
                'key': key,
                'matrix_bucket': matrix_bucket,
                'bq_project': bq_project,
                'schema': schema,
                'current_date': CURRENT_DATE,
                'is_incremental': batch_import_var["campaign"]["full_export"],
                'interval': batch_import_var["campaign"]["interval"]
            }
        )
        import_campaign_to_bq.doc_md = task_doc_md

        # prepare campaigns to be store into BQ for each API key
        task_doc_md = """
        Prepare campaigns to be set in batch store for {}. <br />
        """.format(batch_api_key.get(key))
        prepare_campaign_to_store = BigQueryExecuteQueryOperator(
            task_id='prepare_campaign_to_store_{}'.format(
                batch_api_key.get(key)),
            sql='prepare_campaign_to_store.sql',
            write_disposition='WRITE_TRUNCATE',
            params={
                'bq_project': bq_project,
                'current_date': CURRENT_DATE,
                'platform': batch_api_key.get(key)
            }
        )
        prepare_campaign_to_store.doc_md = task_doc_md

        # store prepared campaign into a final table in BQ
        task_doc_md = """
        Store prepared campaign into a final table in BQ for {}. <br />
        """.format(batch_api_key.get(key))
        store_campaign_in_bq = BigQueryExecuteQueryOperator(
            task_id='store_campaign_in_bq_{}'.format(batch_api_key.get(key)),
            sql='store_campaign_in_bq.sql',
            params={
                'bq_project': bq_project,
                'current_date': CURRENT_DATE,
                'platform': batch_api_key.get(key)
            }

        )
        store_campaign_in_bq.doc_md = task_doc_md

        # finish task
        task_doc_md = """
        Use EmptyOperator to encounter (check, import, prepare, store) tasks by start,end tasks. <br />
        Finish Task.
        """
        end_task = EmptyOperator(
            task_id='end_{}'.format(batch_api_key.get(key)))
        end_task.doc_md = task_doc_md

        # call DAG tasks
        start_task >> set_schemas >> [
            gcs_check_event_files, gcs_check_userbase_files, import_campaign_to_gcs]
        gcs_check_event_files >> import_event_gcs_to_bq >> prepare_event_to_store >> store_event_in_bq >> purge_gcs_event
        gcs_check_userbase_files >> import_userbase_to_bq >> prepare_userbase_to_store >> store_userbase_in_bq >> purge_gcs_userbase
        import_campaign_to_gcs >> import_campaign_to_bq >> prepare_campaign_to_store >> store_campaign_in_bq
        [purge_gcs_event, purge_gcs_userbase, store_campaign_in_bq] >> end_task
