r"""
**PURPOSE:**

This DAG is used to monitor GA4 events (raw and refined).
We pre-calculate some KPIs that will be used later on <PERSON><PERSON><PERSON> to assess the quality of our tables.

A list of the KPIs in use can be found in -->**https:**//docs.google.com/spreadsheets/d/1PpiGxuZfw7HT0idsn0rMHTDMWHoJ97xnBMR7sK4YQ7s/edit?gid=0#gid=0

**METHODOLOGY:**

1. Loop through all properties to compute KPIs (raw and refined)
2. Store computed data in intermediate tables (workspace)
3. Archive data in final table (generated_data)

**COMMAND TO TEST:** 
    gsutil -m cp -R dags/ga4_monitoring.py**gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/
    gsutil -m cp -R data/json/test_ga_config.json**gs://europe-west1-preprod-mozart-2fa49086-bucket/data/json/
    gsutil -m cp -R data/sql/monitoring__data_quality/google_analytics/*  gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/monitoring__data_quality/google_analytics/
"""

from collapsible_doc_dag import CDocDAG
from airflow.models import Variable
from airflow.utils.task_group import TaskGroup
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.common.sql.sensors.sql import SqlSensor
from airflow.operators.empty import EmptyOperator
from datetime import datetime, timedelta
import json
import os

dag_name = 'ga4_monitoring'
env = os.environ.get("ENV")
bq_project = 'pm-{env}-ga4'.format(env=env)
email_on_failure = True if env == 'prod' else False

config_search_path = f'/home/<USER>/gcs/data/json/{env}/'
config_json_file = "google_analytics_data_refinery.json"

# Set Aiflow templates in variables
default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "log_response": True,
    "start_date": datetime(2024, 3, 5, 0, 0),
    "xcom_push": True,
    "provide_context": True,
    "email_on_failure": email_on_failure,
    'email_on_retry': False,
    "email": [eval(Variable.get("airflow_email_alertes"))],
    "gcs_conn_id": "gcs_matrix",
    "bigquery_conn_id": "bq_matrix",
    "gcp_conn_id": "bq_matrix",
    "use_legacy_sql": False,
    "allow_large_results": True,
    "flatten_results": False,
    'execution_timeout': timedelta(minutes=240),
    'retires': 10,
    'retry_delay': timedelta(minutes=2),
    'max_retry_delay': timedelta(minutes=240),
    'location': "EU"
}

# get configuration to refine GA events
f = open(f"{config_search_path}{config_json_file}")
ga4_config = json.load(f)
f.close()

ga_refine_events_config = [config for config in ga4_config
                           if config.get("layer_name") == "refined_data"
                           and config.get("process_name") == "refine__ga4_events"
                           ][0]

# get process mode (partial, incremental) information
ga_refine_events_config_per_property = ga_refine_events_config.get("ga_property")

is_full = ga4_config[0].get('is_full')

start_date = ga4_config[0].get('start_date')
end_date = ga4_config[0].get('end_date')
long_session_duration = ga4_config[0].get('long_session_duration')

with CDocDAG(
        dag_id=dag_name,
        description="Monitor Google Analytics 4 (GA4) Events",
        tags=["monitoring", "ga4"],
        doc_md=__doc__,
        schedule_interval="0 16 * * *",
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=10),
        catchup=False,
        concurrency=5,
        max_active_tasks=10,
        template_searchpath=['/home/<USER>/gcs/data/sql/monitoring__data_quality/google_analytics/']
) as dag:
    start_monitoring = EmptyOperator(task_id='start_monitoring')
    end_monitoring = EmptyOperator(task_id='end_monitoring')

    # TASKS BEGIN #
    task_doc_md = """
        Wait for GA4 refine data process.
    """
    wait_for_ga4_refine_process = SqlSensor(
        task_id="wait_for_refine_process",
        conn_id='airflow_db',
        timeout=60 * 60 * 3,  # 6 hours
        retries=24,
        poke_interval=60 * 20,  # 20 min
        mode="reschedule",
        sql="""
            SELECT CASE WHEN state = 'success' THEN TRUE ELSE FALSE END
            FROM task_instance
            WHERE dag_id = 'refined_data__google_analytics_4{str_env}'
              AND task_id = 'end_refine'
              AND DATE(start_date) = CURRENT_DATE
        """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_ga4_refine_process.doc_md = task_doc_md

    task_doc_md = """
        Wait for GA4 shared data process for Orion.
    """
    wait_for_ga4_share_orion = SqlSensor(
        task_id="wait_for_shared_orion_process",
        conn_id='airflow_db',
        timeout=60 * 60 * 6,  # 6 hours
        retries=24,
        poke_interval=60 * 20,  # 20 min
        mode="reschedule",
        sql="""
            SELECT CASE WHEN MAX(CASE WHEN state = 'success' THEN 1 ELSE 0 END) = 1 THEN TRUE ELSE FALSE END
            FROM task_instance
            WHERE dag_id = 'shared_data__google_analytics_4{str_env}'
              AND task_id = 'share__ga4_events_to__orion.share__ga4_events'
              AND DATE(start_date) = CURRENT_DATE
        """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_ga4_share_orion.doc_md = task_doc_md

    task_doc_md = """
        Archive KPIs from raw data for monitoring purposes.
    """
    archive__raw_events = BigQueryExecuteQueryOperator(
        task_id="archive__raw_events",
        sql="raw_data/archive__raw_events.sql",
        trigger_rule="all_done",
        params={
            "bq_project": bq_project,
            "is_full": is_full,
            "start_date": start_date,
            "end_date": end_date,
            "long_session_duration": long_session_duration
        }
    )
    archive__raw_events.doc_md = task_doc_md

    task_doc_md = """
        Archive KPIs from refined data for monitoring purposes.
    """
    archive__refined_events = BigQueryExecuteQueryOperator(
        task_id="archive__refined_events",
        sql="refined_data/archive__refined_events.sql",
        trigger_rule="all_done",
        params={
            "bq_project": bq_project,
            "is_full": is_full,
            "start_date": start_date,
            "end_date": end_date,
            "long_session_duration": long_session_duration
        }
    )
    archive__refined_events.doc_md = task_doc_md

    task_doc_md = """
        Archive KPIs from Orion shared data for monitoring purposes.
    """
    archive__shared_data_orion = BigQueryExecuteQueryOperator(
        task_id="archive__shared_data_orion",
        sql="shared_data/archive__orion.sql",
        params={
            "bq_project": bq_project,
            "is_full": is_full,
            "start_date": start_date,
            "end_date": end_date,
            "long_session_duration": long_session_duration
        }
    )
    archive__shared_data_orion.doc_md = task_doc_md

    # refine GA events per property
    with TaskGroup("compute__raw_events",
                   tooltip="Tasks to extract kpis from GA4 raw data") as compute__raw_events:
        for config in ga_refine_events_config_per_property:
            # get configurations
            property_id = config.get("property_id")
            brand_trigram = config.get("brand_trigram")
            country = config.get("country")
            platform = config.get("platform")
            section = config.get("section")
            table_prefix = config.get("table_prefix")

            for prefix in table_prefix:
                if prefix == "events_":

                    task_doc_md = """
                        Compute KPIs from raw data for monitoring purpose
                    """
                    compute__raw = BigQueryExecuteQueryOperator(
                        task_id=f"{platform}_{brand_trigram}_{country}_{section}",
                        sql="raw_data/compute__raw_events.sql",
                        params={
                            "property_id": property_id,
                            "platform": platform,
                            "brand_trigram": brand_trigram,
                            "country": country,
                            "section": section,
                            "bq_project": bq_project,
                            "is_full": is_full,
                            "start_date": start_date,
                            "end_date": end_date,
                            "long_session_duration": long_session_duration
                        }
                    )
                    compute__raw.doc_md = task_doc_md

                    start_monitoring >> wait_for_ga4_refine_process >> compute__raw

    with TaskGroup("compute__refined_events",
                   tooltip="Tasks to extract kpis from GA4 refined data") as compute__refined_events:
        for config in ga_refine_events_config_per_property:
            # get configurations
            property_id = config.get("property_id")
            brand_trigram = config.get("brand_trigram")
            country = config.get("country")
            platform = config.get("platform")
            section = config.get("section")
            table_prefix = config.get("table_prefix")

            for prefix in table_prefix:
                if prefix == "events_":

                    task_doc_md = """
                        Compute KPIs from refined data for monitoring purpose
                    """
                    compute__refined = BigQueryExecuteQueryOperator(
                        task_id=f"{platform}_{brand_trigram}_{country}_{section}",
                        sql="refined_data/compute__refined_events.sql",
                        params={
                            "property_id": property_id,
                            "platform": platform,
                            "brand_trigram": brand_trigram,
                            "country": country,
                            "section": section,
                            "bq_project": bq_project,
                            "is_full": is_full,
                            "start_date": start_date,
                            "end_date": end_date,
                            "long_session_duration": long_session_duration
                        }
                    )
                    compute__refined.doc_md = task_doc_md

                    start_monitoring >> wait_for_ga4_refine_process >> compute__refined

    start_monitoring >> wait_for_ga4_refine_process >> [compute__raw_events, compute__refined_events]
    compute__raw_events >> archive__raw_events
    [archive__raw_events, compute__refined_events] >> archive__refined_events >> end_monitoring
    start_monitoring >> wait_for_ga4_share_orion >> archive__shared_data_orion
