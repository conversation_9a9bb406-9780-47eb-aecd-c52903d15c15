r"""
**PURPOSE:**

This DAG is responsible for sending IT Enterprise newsletters to recipients listed in a CSV file.
It processes the recipient data and sends personalized emails using the Tmail API.

**FUNCTIONALITY:**

1. Reads recipient information (email and name) from a CSV file stored in GCS
2. Supports two modes of operation:
   - BAT (test) mode: Sends to a limited test audience
   - Production mode: Sends to the complete recipient list
3. Uses the Tmail API with template-based emails for consistent formatting
4. Includes tracking capabilities via the 'tracking_key' parameter

**CONFIGURATION:**

The DAG uses the 'itdata_nl_it_entreprise' Airflow variable which contains:
- 'bat': Boolean flag to determine if running in test mode
- 'bat_file': Filename for test recipients
- 'all_file': Filename for production recipients
- 'subject': Email subject line

**NOTE:**

This DAG is intended for temporary use and can be removed once the newsletter campaign is complete.
"""

# gsutil cp dags/send_nl_it_entreprise.py gs://europe-west1-prod-mozart-co-2ef7e904-bucket/dags/
# gsutil cp test/nl_it/bat.csv gs://europe-west1-prod-mozart-co-2ef7e904-bucket/data/nl_it/
# gsutil cp test/nl_it/bat_itdata.csv gs://europe-west1-prod-mozart-co-2ef7e904-bucket/data/nl_it/
# gsutil cp test/nl_it/all.csv gs://europe-west1-prod-mozart-co-2ef7e904-bucket/data/nl_it/

import csv
import hashlib
import json
import logging
from datetime import datetime
from datetime import timedelta

import requests
from airflow.models import Variable
from airflow.operators.python import PythonOperator
from collapsible_doc_dag import CDocDAG

dag_name = 'send_nl_it_entreprise'

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2025, 5, 1, 00, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': False,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=2),
    'sla': None,
    'email_on_retry': False,
    'retries': 0,
    'retry_delay': timedelta(minutes=5),
    'depends_on_past': False,
    # PSQL Connection
    'conn_id': 'psql_matrix_email_app',
    'database': 'matrix',
    'gcp_conn_id': 'gcs_matrix',

    # BQ option
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_TRUNCATE',
    'create_disposition': 'CREATE_IF_NEEDED',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE'
}

def __send_it_enterprise_newsletter(**kwargs):
    itdata_nl_it_entreprise = Variable.get('itdata_nl_it_entreprise', deserialize_json=True)
    is_bat_test = bool(itdata_nl_it_entreprise.get('bat', True))
    only_not_bat = bool(itdata_nl_it_entreprise.get('only_not_bat', False))
    bat_file = itdata_nl_it_entreprise.get('bat_file', 'bat.csv')
    all_file = itdata_nl_it_entreprise.get('all_file', 'all.csv')

    subject = itdata_nl_it_entreprise.get('subject', 'Newsletter IT (Data, App & Entreprise)')

    logging.info(f'is_bat_test: {is_bat_test}')
    logging.info(f'bat_file: {bat_file}')
    logging.info(f'all_file: {all_file}')
    logging.info(f'subject: {subject}')

    file = all_file if is_bat_test == False else bat_file
    filename = f'/home/<USER>/gcs/data/nl_it/{file}'
    logging.info(f'filename: {filename}')

    # Collect all emails from the bat file
    bat_emails = []
    with open(f'/home/<USER>/gcs/data/nl_it/{bat_file}', 'r') as bat_csvfile:
        bat_reader = csv.DictReader(bat_csvfile)
        for row in bat_reader:
            bat_emails.append(row['email'])

    logging.info(f"Emails in BAT file: {bat_emails}")

    tmail_api_end_point = '/api/email'
    tmail_api_identity = 'it_entreprise'
    tmail_api_secret = 'WoSt6pCZSeX7GmiV58Djgt4w6HRBEb2u'
    tmail_api_uri = 'https://tmail.prismadata.fr'

    with open(filename, 'r') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            email = row['email']
            if not is_bat_test and only_not_bat and email in bat_emails:
                logging.info('Not sending email to {} as it is in the BAT file'.format(email))
                continue

            name = row['name']

            print('email: {} \n'.format(email))
            print('name: {} \n'.format(name))

            payload = {
                "type": "template",
                "template_name": "nlitentreprise",
                "tracking_key": "nl_it_entreprise",
                "recipient": email,
                "vars": {
                    "name": name,
                    "subject": f"{subject}"
                }
            }

            payload = json.dumps(payload)

            tmail_host = tmail_api_uri + tmail_api_end_point
            logging.info('[Tmail Api] Using Host: {}'.format(tmail_host))
            date = datetime.now()
            atomDate = date.strftime('%Y-%m-%dT%H:%M:00')

            logging.info('[Tmail Api] email data = {} '.format(str(payload)))

            toHash = '|' + atomDate + '|' + 'POST' + '|' + \
                     tmail_api_end_point + '|' + \
                     tmail_api_secret + '|' + str(payload) + '|'

            x_auth_token = hashlib.sha256(toHash.encode('utf-8')).hexdigest()
            headers = {
                'Content-Type': 'application/json',
                'X-Auth-Token': '%s' % x_auth_token,
                'X-Auth-identity': '%s' % tmail_api_identity
            }
            response = requests.request(
                "POST", tmail_host, headers=headers, data=payload)

            logging.info('[Tmail Api] Response Status Code: {}'.format(response.status_code))
            if response.status_code != 200:
                print('**** Response Error: {} \n'.format(response.text))
            else:
                print('---> Email successfully sent to : {} \n'.format(email))
                print(response.text)


with CDocDAG(
        dag_name,
        description='Send IT Enterprise Newsletter to Recipients from CSV',
        tags=["app-data"],
        doc_md=__doc__,
        schedule_interval=None,
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=2),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/scripts/']
) as dag:
    send_it_newsletter = PythonOperator(
        task_id='send_it_enterprise_newsletter',
        python_callable=__send_it_enterprise_newsletter,
        # op_kwargs={'source_dags_id': 'get_karinto_consents'},
    )
    send_it_newsletter.doc_md = '''Send IT Enterprise newsletter emails to recipients.

    This task reads recipient data from a CSV file and sends personalized emails
    using the Tmail API. It supports both test (BAT) and production modes based
    on the 'itdata_nl_it_entreprise' Airflow variable configuration.'''
