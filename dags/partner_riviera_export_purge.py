r"""
**PURPOSE**:
This DAG is used to send weakly all purged profil to riviera.

**METHODOLOGY:**

After the dag clin,
**we insert profil to purge into a table:**export_matrix_email.profile_to_anonymise_events

Then each week, we will take profil for the last 7 days and send them to riviera in order to purge them.

**GOOD TO KNOW:** 

We delete all profil from export_matrix_email.profile_to_anonymise_events when they are inside for more than 10 days.
"""

# gsutil cp dags/partner_riviera_export_purge.py gs://europe-west1-mozart-cluster-2e910a39-bucket/dags/

# gsutil -m cp -R data/sql/*  gs://europe-west1-mozart-cluster-2e910a39-bucket/data/sql/

import os
from datetime import timedelta, datetime

from airflow.models import Variable
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.google.cloud.transfers.bigquery_to_gcs import BigQueryToGCSOperator
from collapsible_doc_dag import CDocDAG
from sftp_plugin.operators.gcs_to_sftp_operator import GoogleCloudStorageToSftpOperator

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']

dag_name = 'partner_riviera_export_purge'
email_on_failure = True
email_on_retry = False

bq_project = 'pm-prod-matrix'
gcs_uri = 'gs://it-data-prod-matrix-pipeline/'

# Get today's date and the day of the month
today = datetime.today()
day_of_month = today.day
# Check if today is one of the first 7 days of the month
is_autocorrect_run = day_of_month <= 7  # If so, we'll also send the autocorrect

# ------------- FOR PREPROD ENV -----------------
env = os.environ.get("ENV")
if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    email_on_retry = False
    bq_project = 'pm-preprod-matrix'
    gcs_uri = 'gs://it-data-preprod-matrix-preprod-pipeline/'
# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'start_date': datetime(2019, 12, 16, 00, 00, 00),
    'retries': 5,
    'retry_delay': timedelta(minutes=5),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'email_on_retry': email_on_retry,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=8),
    'sla': None,
    # postgres
    'database': 'matrix',
    'schema': 'matrix__email_generic_export',
    # conn id
    'postgres_conn_id': 'psql_matrix_email_app',
    'gcp_conn_id': 'gcs_matrix',
    # export format & destination
    'export_format': 'csv',
    'field_delimiter': ';',  # common param for bQ also
    'gzip': False,
    'parameters': None,
    'bucket': matrix_bucket,  # common param for bQ also

    ## BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_TRUNCATE',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',
    'source_format': 'CSV',
    # 'skip_leading_rows':0, # skeep header
    'autodetect': False,
    'max_bad_records': "0",
    'allow_jagged_rows': True,
    'ignore_unknown_values': True,
    'quote_character': '',

    # google cloud storage for export
    'gcs_conn_id': 'gcs_matrix',
    'gcs_bucket': matrix_bucket,
}

path_export_purge_riviera = gcs_uri + 'partners/purge_export/riviera/{{ next_ds }}/'
short_path_export_purge_riviera = 'partners/purge_export/riviera/{{ next_ds }}/'

with CDocDAG(dag_name,
             description='send purge profil to riviera',
             tags=["partner"],
             doc_md=__doc__,
             schedule_interval="30 12 * * MON",  # all monday at 12H30
             catchup=False,
             max_active_runs=1,
             dagrun_timeout=timedelta(hours=15),
             template_searchpath=['/home/<USER>/gcs/data/sql/partners/exports/purge/'],
             default_args=default_args) as dag:
    # ------------------------------------------------
    # Generate fingerprints table for J and J-1
    # ------------------------------------------------

    task_doc_md = """
    This query is used to select and create the table to store profil to purge before send them to riviera<br />
    into : we use this table to store those profil: export_partner.gdpr_purge_riviera<br />
    """

    compute_profil_to_purge_riviera = BigQueryExecuteQueryOperator(
        task_id='compute_profil_to_purge_riviera',
        sql='compute_profil_to_purge_riviera.sql',
        params={
            'is_autocorrect_run': is_autocorrect_run
        },
        use_legacy_sql=False
    )
    compute_profil_to_purge_riviera.doc_md = task_doc_md

    task_doc_md = """
    We export the table fro riviera to purge into Google Cloud Storage.<br />
    """
    export_purge_riviera = BigQueryToGCSOperator(
        task_id='export_purge_riviera',
        destination_cloud_storage_uris=[
            path_export_purge_riviera + 'export_purge_riviera_{}.csv'.format('{{ next_ds_nodash }}')],
        source_project_dataset_table='{}.export_partner.gdpr_purge_riviera'.format(bq_project),
        print_header=False,
        field_delimiter='',
        export_format='CSV'
    )
    export_purge_riviera.doc_md = task_doc_md

    task_doc_md = """
    We export the purge file from gcs to riviera sftp: /export/purge<br />
    """

    export_purge_to_riviera = GoogleCloudStorageToSftpOperator(
        task_id='export_purge_to_riviera',
        gcs_bucket=buckets['matrix'],
        gcs_filename=short_path_export_purge_riviera + 'export_purge_riviera_{}.csv'.format('{{ next_ds_nodash }}'),
        sftp_conn_id='sftp_riviera_femme_actuelle_part',
        sftp_filename='IN/purge/export_purge_riviera_{}.csv'.format(
            '{{ next_ds_nodash }}')
    )

    export_purge_to_riviera.doc_md = task_doc_md

    compute_profil_to_purge_riviera >> export_purge_riviera >> export_purge_to_riviera
