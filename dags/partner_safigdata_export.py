from datetime import datetime, timedelta
from airflow import DAG
from airflow.models import Variable

from sftp_plugin.operators.gcs_to_sftp_glob_operator import GoogleCloudStorageToSftpGlobOperator as GcsToSftpGlobOperator

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2021, 4, 1, 0, 0, 0),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': False,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=90),
    'sla': None,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=1),
    'depends_on_past': False
}

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix-prod']

with DAG("partner_safigdata_export",
         schedule_interval="0 10 1 * *",
         tags=["partner"],
         catchup=False,
         max_active_runs=1,
         dagrun_timeout=timedelta(hours=2),
         default_args=default_args) as dag:

    # Business Insider
    upload_export_monthly_bi = GcsToSftpGlobOperator(
       task_id='export_monthly_bi',
       gcs_bucket=matrix_bucket,
       sftp_conn_id='sftp_safigdata',
       file_pattern=[".*business_part.*", ".*bi_alert_part.*"],
       file_exclude_pattern=[],
       gcs_dirname='export-generic/{}/full-export/'.format('{{ ds }}'),
       sftp_dirname='/IN/mensuel/',
       gzip=True
    ) 

    # Ça m'intéresse
    upload_export_monthly_cm = GcsToSftpGlobOperator(
       task_id='export_monthly_cm',
       gcs_bucket=matrix_bucket,
       sftp_conn_id='sftp_safigdata',
       file_pattern=[".*ca_minteresse_part.*"],
       file_exclude_pattern=[],
       gcs_dirname='export-generic/{}/full-export/'.format('{{ ds }}'),
       sftp_dirname='/IN/mensuel/',
       gzip=True
    )

    # Capital
    upload_export_monthly_cap = GcsToSftpGlobOperator(
       task_id='export_monthly_cap',
       gcs_bucket=matrix_bucket,
       sftp_conn_id='sftp_safigdata',
       file_pattern=[".*capital_part.*"],
       file_exclude_pattern=[],
       gcs_dirname='export-generic/{}/full-export/'.format('{{ ds }}'),
       sftp_dirname='/IN/mensuel/',
       gzip=True
    )

    # Cuisine Actuelle
    upload_export_monthly_ca = GcsToSftpGlobOperator(
       task_id='export_monthly_ca',
       gcs_bucket=matrix_bucket,
       sftp_conn_id='sftp_safigdata',
       file_pattern=[".*cuisine_actuelle_part.*"],
       file_exclude_pattern=[],
       gcs_dirname='export-generic/{}/full-export/'.format('{{ ds }}'),
       sftp_dirname='/IN/mensuel/',
       gzip=True
    )

    # Femme Actuelle
    upload_export_monthly_fa = GcsToSftpGlobOperator(
       task_id='export_monthly_fa',
       gcs_bucket=matrix_bucket,
       sftp_conn_id='sftp_safigdata',
       file_pattern=[".*femme_actuelle_part.*", ".*serengo_part.*"],
       file_exclude_pattern=[],
       gcs_dirname='export-generic/{}/full-export/'.format('{{ ds }}'),
       sftp_dirname='/IN/mensuel/',
       gzip=True
    )

    # Gala
    upload_export_monthly_gala = GcsToSftpGlobOperator(
       task_id='export_monthly_gala',
       gcs_bucket=matrix_bucket,
       sftp_conn_id='sftp_safigdata',
       file_pattern=[".*gala_part.*"],
       file_exclude_pattern=[],
       gcs_dirname='export-generic/{}/full-export/'.format('{{ ds }}'),
       sftp_dirname='/IN/mensuel/',
       gzip=True
    )

    # Géo
    upload_export_monthly_geo = GcsToSftpGlobOperator(
       task_id='export_monthly_geo',
       gcs_bucket=matrix_bucket,
       sftp_conn_id='sftp_safigdata',
       file_pattern=[".*geo_part.*"],
       file_exclude_pattern=[],
       gcs_dirname='export-generic/{}/full-export/'.format('{{ ds }}'),
       sftp_dirname='/IN/mensuel/',
       gzip=True
    )

    # Harvard Business Review
    upload_export_monthly_hbr = GcsToSftpGlobOperator(
       task_id='export_monthly_hbr',
       gcs_bucket=matrix_bucket,
       sftp_conn_id='sftp_safigdata',
       file_pattern=[".*harvard_business_part.*"],
       file_exclude_pattern=[],
       gcs_dirname='export-generic/{}/full-export/'.format('{{ ds }}'),
       sftp_dirname='/IN/mensuel/',
       gzip=True
    )

    # Listen
    upload_export_monthly_listen = GcsToSftpGlobOperator(
       task_id='export_monthly_listen',
       gcs_bucket=matrix_bucket,
       sftp_conn_id='sftp_safigdata',
       file_pattern=[".*listen_part.*"],
       file_exclude_pattern=[],
       gcs_dirname='export-generic/{}/full-export/'.format('{{ ds }}'),
       sftp_dirname='/IN/mensuel/',
       gzip=True
    )

    # Neon
    upload_export_monthly_neon = GcsToSftpGlobOperator(
       task_id='export_monthly_neon',
       gcs_bucket=matrix_bucket,
       sftp_conn_id='sftp_safigdata',
       file_pattern=[".*neon_part.*"],
       file_exclude_pattern=[],
       gcs_dirname='export-generic/{}/full-export/'.format('{{ ds }}'),
       sftp_dirname='/IN/mensuel/',
       gzip=True
    )

    # Oh My Mag
    upload_export_monthly_ohm = GcsToSftpGlobOperator(
       task_id='export_monthly_ohm',
       gcs_bucket=matrix_bucket,
       sftp_conn_id='sftp_safigdata',
       file_pattern=[".*ohmymag_part.*"],
       file_exclude_pattern=[],
       gcs_dirname='export-generic/{}/full-export/'.format('{{ ds }}'),
       sftp_dirname='/IN/mensuel/',
       gzip=True
    )

    # Prismaconnect
    #upload_export_monthly_prismaconnect = GcsToSftpGlobOperator(
    #   task_id='export_monthly_prismaconnect',
    #   gcs_bucket=matrix_bucket,
    #   sftp_conn_id='sftp_safigdata',
    #   file_pattern=[".*prisma_connect.*"],
    #   file_exclude_pattern=[".*prisma_connect_produit_nl_test.*"],
    #   gcs_dirname='export-generic/{}/full-export/'.format('{{ ds }}'),
    #   sftp_dirname='/IN/mensuel/',
    #   gzip=True
    #)

    # Prisma Media
    #upload_export_monthly_pm = GcsToSftpGlobOperator(
    #   task_id='export_monthly_pm',
    #   gcs_bucket=matrix_bucket,
    #   sftp_conn_id='sftp_safigdata',
    #   file_pattern=[".*b2b_prisme_crm.*"],
    #   file_exclude_pattern=[],
    #   gcs_dirname='export-generic/{}/full-export/'.format('{{ ds }}'),
    #   sftp_dirname='/IN/mensuel/',
    #   gzip=True
    #)

    # Télé 2 Semaines
    upload_export_monthly_t2s = GcsToSftpGlobOperator(
       task_id='export_monthly_t2s',
       gcs_bucket=matrix_bucket,
       sftp_conn_id='sftp_safigdata',
       file_pattern=[".*tele_2_semaines_part.*"],
       file_exclude_pattern=[],
       gcs_dirname='export-generic/{}/full-export/'.format('{{ ds }}'),
       sftp_dirname='/IN/mensuel/',
       gzip=True
    )

    # Télé Loisirs
    upload_export_monthly_tl = GcsToSftpGlobOperator(
       task_id='export_monthly_tl',
       gcs_bucket=matrix_bucket,
       sftp_conn_id='sftp_safigdata',
       file_pattern=[".*tele_loisirs_part.*"],
       file_exclude_pattern=[],
       gcs_dirname='export-generic/{}/full-export/'.format('{{ ds }}'),
       sftp_dirname='/IN/mensuel/',
       gzip=True
    )

    # Voici
    upload_export_monthly_voici = GcsToSftpGlobOperator(
       task_id='export_monthly_voici',
       gcs_bucket=matrix_bucket,
       sftp_conn_id='sftp_safigdata',
       file_pattern=[".*voici_part.*"],
       file_exclude_pattern=[],
       gcs_dirname='export-generic/{}/full-export/'.format('{{ ds }}'),
       sftp_dirname='/IN/mensuel/',
       gzip=True
    )

    upload_export_monthly_bi \
    >> upload_export_monthly_cm >> upload_export_monthly_cap \
    >> upload_export_monthly_ca >>  upload_export_monthly_fa \
    >> upload_export_monthly_gala >> upload_export_monthly_geo \
    >> upload_export_monthly_hbr >> upload_export_monthly_listen \
    >> upload_export_monthly_neon >> upload_export_monthly_ohm \
    >> upload_export_monthly_t2s >> upload_export_monthly_tl \
    >> upload_export_monthly_voici  