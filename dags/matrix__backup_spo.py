r"""
**PURPOSE:**
**backup spo table from BQ to psql:**-**Source:**pm-$-matrix.store_smart_path.spo_matrix 
-**Destination:**smart_path.spo_matrix 

**METHODOLOGY:**

- Full Backup from BQ to psql daily

- # gsutil cp dags/matrix__backup_spo.py gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/
- # gsutil -m cp -R data/sql/backup/spo/*  gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/backup/spo/
"""
import os
from datetime import timedelta, datetime

from airflow.models import Variable
from airflow.providers.google.cloud.operators.bigquery import BigQueryCheckOperator
from bq_plugin.operators.bigquery_to_psql_operator import BigQueryToPostgresOperator
from collapsible_doc_dag import CDocDAG

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']
mozart_bucket = buckets['mozart']

dag_name = 'matrix__backup_spo'
email_on_failure = True
bq_project = 'pm-prod-matrix'
# ------------- FOR PREPROD ENV -----------------
env = os.environ.get("ENV")
str_env = ''
if env != 'prod':
    str_env = '_' + env
    dag_name = dag_name + '_' + env
    email_on_failure = False
    bq_project = 'pm-preprod-matrix'
# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2022, 12, 9, 10, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=3),
    'sla': None,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=1),
    'depends_on_past': False,

    'bucket': matrix_bucket,

    # PSQL Connection
    'conn_id': 'psql_smart_path_app',
    'postgres_conn_id': 'psql_smart_path_app',
    'database': 'matrix',
    'schema': 'matrix__email',

    # GCP CONNECTION
    'gcp_conn_id': 'gcs_matrix',
    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_TRUNCATE',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',
    'allow_jagged_rows': True,
    'quote_character': '',
}

with CDocDAG(
        dag_name,
        description='Backup Bq table store_smart_path.spo_matrix To psql table:  smart_path.spo_matrix',
        doc_md=__doc__,
        schedule_interval="40 6 * * *",
        default_args=default_args,
        tags=["backup"],
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=6),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/backup/spo/']
) as dag:
    # check table volume
    check_table = BigQueryCheckOperator(
        task_id='check_table',
        sql='0_check_table.sql',
    )
    check_table.doc_md = 'check spo table before export to psql'
    
    spo_create_tmp_table = '''
        DROP TABLE IF EXISTS smart_path.spo_matrix_import;
        CREATE TABLE IF NOT EXISTS smart_path.spo_matrix_import (
            supply_vendor varchar(200),
            supply_vendor_publisher_id varchar(200),
            seller_name varchar(200),
            direct_reseller varchar(200),
            bidder_ssp_prisma varchar(200),
            integration_prisma varchar(200),
            tt varchar(200),
            synthese varchar(200),
            fees_open_ssp varchar(200), -- integer,
            transparence varchar(200), -- boolean,
            ca_prismadex varchar(200), -- boolean,
            reach varchar(200), -- integer,
            note_fees varchar(200), -- integer,
            direct_reseller_note varchar(200), -- integer,
            transparence_note varchar(200), -- integer,
            integration_prisma_note varchar(200), -- integer,
            note_dv360 varchar(200), -- integer,
            note_ttd varchar(200), -- integer,
            note_meilleurs_id varchar(200), -- boolean,
            deal_direct varchar(200), -- boolean,
            choix_ssp varchar(200), -- boolean
            format varchar(200) -- boolean
        );
        -- ddl-end --
        ALTER TABLE smart_path.spo_matrix_import OWNER TO smart_path;
    '''

    query_expert = "COPY {table} (supply_vendor, supply_vendor_publisher_id, seller_name, direct_reseller, " \
                   "bidder_ssp_prisma, integration_prisma, tt, synthese, fees_open_ssp, transparence, ca_prismadex, " \
                   "reach, note_fees, direct_reseller_note, transparence_note, integration_prisma_note, note_dv360, " \
                   "note_ttd, note_meilleurs_id, deal_direct, choix_ssp, format) FROM STDIN WITH CSV QUOTE e'\"' " \
                   "DELIMITER AS '\t' "

    task_doc_md = """
        Backup Bq table store_smart_path.spo_matrix To psql table:  smart_path.spo_matrix <br />
    """
    backup_spo_to_psql = BigQueryToPostgresOperator(
        task_id='backup_spo_to_psql',
        # psql destination table
        create_destination_table_script=spo_create_tmp_table,
        destination_table='smart_path.spo_matrix_import',
        # truncate destination_table ?
        truncate_destination_table=True,
        bucket=matrix_bucket,
        database='matrix',
        destination_cloud_storage_object_path='tmp_exports/{{ next_execution_date.strftime("%Y%m%d") }}/spo_matrix_{{ next_execution_date.strftime("%Y%m%d") }}.csv',
        postgres_conn_id='psql_smart_path_app',
        bigquery_conn_id='bq_matrix',
        gcp_conn_id='gcs_matrix',
        params={
            'bq_project': 'pm-{}-matrix'.format(env)
        },
        # source_query=spo_query,
        # source_query_destination_table='pm-{}-matrix'.format(
        #    env) + ':export_matrix_email.spo_matrix_{{ next_execution_date.strftime("%Y%m%d")}}',
        # bq table to export to psql (same as source_query_destination_table in this case)
        source_export_dataset_table='pm-{}-matrix:export_matrix_email.spo_matrix'.format(env),
        # query used to load csv file into psql table
        copy_expert_query=query_expert.format(table='smart_path.spo_matrix_import'),
        temp_bucket=mozart_bucket,
        prepare_query='1_prepare_and_update_store.sql'
    )
    backup_spo_to_psql.doc_md = task_doc_md

    check_table >> backup_spo_to_psql
