r"""
# Personalized NL Preparation

**PURPOSE:**
**This dag is used to:**- Sync content shared by itbi for each campaign/profile to Postgres.
- Chunk emails to call and trigger build_nl dag.

**METHODOLOGY:**
- Sync content shared by itbi for each campaign/profile to Postgres.
- Chunk emails according to config.
- <PERSON><PERSON> personalized_nl_build_nl to build NL for each chunk and save html to the bucket.
"""
import logging
import os
import json
from datetime import timedelta, datetime
from airflow.models import Variable
from airflow.exceptions import AirflowFailException
from airflow.decorators import task
from google.cloud import bigquery
from bq_plugin.sensors.bigquery_sql_sensor import BigQuerySqlSensor
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from psql_plugin.operators.psql_select_many_operator import PostgresSelectManyOperator
from bq_plugin.operators.bigquery_to_psql_operator import BigQueryToPostgresOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from collapsible_doc_dag import CDocDAG
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.utils.task_group import TaskGroup

# ----------------- CONFIG ----------------------
ENV = os.environ.get("ENV")
IS_PROD = ENV == 'prod'
BQ_PROJECT = 'pm-{}-matrix'.format(ENV)
BUCKETS = Variable.get("gcs_bucket_names", deserialize_json=True)
MATRIX_BUCKET = BUCKETS['matrix']
MOZART_BUCKET = BUCKETS['mozart']

# Initialize clients
bq_client = bigquery.Client(project=BQ_PROJECT)
dag_name = 'personalized_nl_sync_content'
email_on_failure = True
config = Variable.get("personalized_nl", deserialize_json=True)

if ENV != 'prod':
    dag_name = dag_name + '_' + ENV
    email_on_failure = False


default_args = {
    'owner': 'airflow',
    'start_date': datetime(2021, 12, 1, 2, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': False,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=130),
    'sla': None,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=5),
    'depends_on_past': False,

    # BQ CONNECTION
    'gcp_conn_id': 'bq_matrix',
    'use_legacy_sql': False,

    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE'  # possibles values: INTERACTIVE and BATCH.
}
# for the moment we check only till 9h as we do not have a lot of campaigns per day
# this can be adapted later on to continuously check for new campaigns' recommendations till 17h for ex
schedule_interval = "0 2-9 * * *"
if ENV != 'prod':
    schedule_interval = "0 8-15 * * *"

with CDocDAG(
        dag_name,
        description='Prepare personalized NL Campaign',
        tags=["nl", "personalized-nl"],
        doc_md=__doc__,
        schedule_interval=schedule_interval,
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=3),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/personalized_nl/']
) as dag:
    wait_for_recommendations = BigQuerySqlSensor(
        task_id='wait_for_recommendations',
        gcp_conn_id='bq_matrix',
        timeout=59 * 60,  # 59 minutes
        poke_interval=25 * 60,  # every 25 minutes
        mode='reschedule',
        soft_fail=True,
        sql="05_check_recommended_content.sql",
    )
    wait_for_recommendations.doc_md = "check if new recommended content is available"

    with TaskGroup("content_preparation") as content_preparation:
        @task()
        def has_nl_shopping(**kwargs):
            sql = "SELECT COUNTIF(email_base_public_ref = 'nl-shopping') > 0 AS has_nl_shopping FROM store.personalized_nl_campaign;"
            records = bq_client.query(sql, location="EU")
            results = [tuple(row) for row in records]
            logging.info(results)
            return json.dumps(results)

        task_doc_md = """
        Prepare recommended content to export
        """
        prepare_recommended_content = BigQueryExecuteQueryOperator(
            task_id="prepare_recommended_content",
            sql="06_export_recommended_content.sql",
            params={
                'is_full': config["is_full"]
            }
        )
        prepare_recommended_content.doc_md = task_doc_md

        create_destination_table_script = '''
                DROP TABLE IF EXISTS personalized_nl.import_personalized_nl_recommended_content;
                CREATE TABLE IF NOT EXISTS personalized_nl.import_personalized_nl_recommended_content (
                    rogue_one_email_id integer NOT NULL,
                    email_profile_master_id integer NOT NULL,
                    variables JSON,
                    subject varchar(500),
                    preheader varchar(500),
                    content json,
                    personalized_subject_variation varchar(50)
                );
                ALTER TABLE personalized_nl.import_personalized_nl_recommended_content OWNER TO personalized_nl;
                GRANT SELECT ON TABLE personalized_nl.import_personalized_nl_recommended_content TO all_read_only;
            '''
        task_doc_md = """
        Export recommended content by campaign/profile to postgres
        """
        export_recommended_content = BigQueryToPostgresOperator(
            task_id='export_recommended_content',
            destination_table='personalized_nl.import_personalized_nl_recommended_content',
            bucket=MATRIX_BUCKET,
            database='matrix',
            create_destination_table_script=create_destination_table_script,
            destination_cloud_storage_object_path='tmp_exports/{{ next_execution_date.strftime("%Y%m%d") }}/personalized_nl/recommended_content_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}.csv',
            truncate_destination_table=True,
            postgres_conn_id='psql_personalized_nl_app',
            bigquery_conn_id='bq_matrix',
            gcp_conn_id='gcs_matrix',
            # bq table to export to psql
            source_export_dataset_table='pm-{}-matrix.export_matrix_email.personalized_nl_recommended_content'.format(
                ENV),
            copy_expert_query="COPY {} (rogue_one_email_id, email_profile_master_id, subject, preheader, content, personalized_subject_variation, variables) FROM STDIN WITH CSV QUOTE e'\"' DELIMITER AS e'\t'".format(
                'personalized_nl.import_personalized_nl_recommended_content'),
            temp_bucket=MOZART_BUCKET,
        )
        export_recommended_content.doc_md = task_doc_md

        task_doc_md = """
        Store built HTML in final table personalized_nl.email <br />
        """
        store_recommended_content = SQLExecuteQueryOperator(
            task_id='store_recommended_content',
            conn_id='psql_personalized_nl_app',
            sql="07_store_recommended_content.sql",
            dag=dag
        )
        store_recommended_content.doc_md = task_doc_md

        has_nl_shopping() >> prepare_recommended_content >> export_recommended_content >> store_recommended_content


    with TaskGroup("campaign_workflow") as campaign_workflow:
        task_doc_md = """
        Get campaigns to prepare <br />
        """
        get_campaigns_task = PostgresSelectManyOperator(
            task_id='get_campaigns_to_prepare',
            postgres_conn_id='psql_personalized_nl_app',
            sql="08_get_campaigns_to_prepare.sql",
        )
        get_campaigns_task.doc_md = task_doc_md

        @task
        def get_campaigns_to_prepare_confs(**kwargs):
            ti = kwargs['ti']
            result = ti.xcom_pull(task_ids='campaign_workflow.get_campaigns_to_prepare')
            rows = json.loads(result)
            logging.info(rows)
            confs = []
            for campaign_ids in rows:
                for email_campaign_id in campaign_ids:
                    confs.append({
                        'trigger_run_id': f'workflow_{email_campaign_id}_{datetime.now().strftime("%Y-%m-%d_%H%M%S")}',
                        'conf': {
                            'email_campaign_id': email_campaign_id,
                            'step': 'PREPARING'
                        }
                    })
            return confs

        campaign_workflow_confs = get_campaigns_to_prepare_confs()

        trigger_campaign_workflow_dag = TriggerDagRunOperator.partial(
            task_id="trigger_campaign_workflow_dag",
            trigger_dag_id=(
                f"personalized_nl__campaign_workflow_{ENV}" if not IS_PROD else 'personalized_nl__campaign_workflow'),
            wait_for_completion=True,
            max_active_tis_per_dag=10
        ).expand_kwargs(campaign_workflow_confs)

        get_campaigns_task >> campaign_workflow_confs >> trigger_campaign_workflow_dag


    with TaskGroup("build_nls") as build_nls:
        task_doc_md = """
        Calculate Chunks to bulk building the Newsletter <br />
        """
        calc_chunks = SQLExecuteQueryOperator(
            task_id='calc_chunks',
            conn_id='psql_personalized_nl_app',
            sql="08_calc_chunks.sql",
            dag=dag,
            params={
                'chunk_size': config["chunk_size"]
            }
        )
        calc_chunks.doc_md = task_doc_md

        task_doc_md = """
        Get chunks of campaign/profiles to handle. <br />
        """
        get_chunks_task = PostgresSelectManyOperator(
            task_id='get_chunks',
            postgres_conn_id='psql_personalized_nl_app',
            sql="08_get_process_chunks.sql",
        )
        get_chunks_task.doc_md = task_doc_md

        @task
        def get_build_nl_confs(**kwargs):
            ti = kwargs['ti']
            result = ti.xcom_pull(task_ids='build_nls.get_chunks')
            rows = json.loads(result)
            confs = []
            for row in rows:
                chunk_number, email_campaign_id, start_profile_master_id, end_profile_master_id = row
                confs.append({
                    'trigger_run_id': f'build_nl_{chunk_number}_{email_campaign_id}_{start_profile_master_id}_{end_profile_master_id}_{datetime.now().strftime("%Y-%m-%d_%H%M%S")}',
                    'conf': {
                        'email_campaign_id': email_campaign_id,
                        'start_profile_master_id': start_profile_master_id,
                        'end_profile_master_id': end_profile_master_id,
                    }
                })
            return confs

        build_nl_confs = get_build_nl_confs()

        trigger_build_nl_dags = TriggerDagRunOperator.partial(
            task_id="trigger_build_nl_dag",
            trigger_dag_id=(f"personalized_nl__build_nl_v4_{ENV}" if not IS_PROD else 'personalized_nl__build_nl_v4'),
            wait_for_completion=False,
            max_active_tis_per_dag=30
        ).expand_kwargs(build_nl_confs)

        calc_chunks >> get_chunks_task >> build_nl_confs >> trigger_build_nl_dags


    @task
    def validate_chunk_size(**kwargs):
        chunk_size = config.get('chunk_size', 3000)
        # Get the chunk size from the dag run conf
        # This controls the number of profiles per chunk
        sub_chunk_size = config.get('sub_chunk_size', 150)
        if chunk_size / sub_chunk_size > 1024:
            logging.info(
                f"Chunk size: {chunk_size}, sub_chunk_size: {sub_chunk_size}, sub_chunk_count: {chunk_size / sub_chunk_size}")
            raise AirflowFailException(
                "Chunk parameters are not valid, we should not have more than 1024 sub chunk, reduce chunk_size or increase sub_chunk size!")

    wait_for_recommendations >> content_preparation >> validate_chunk_size() >> campaign_workflow
    campaign_workflow >> build_nls

