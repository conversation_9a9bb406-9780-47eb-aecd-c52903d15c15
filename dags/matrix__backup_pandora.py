r"""
**PURPOSE**:
This DAG backup Pandora tables to BQ store_pandora using sequence table.

-**Pandora tables to backup:**- `pandora.event`
- `pandora.pandora_pmc_event`
- `pandora.cancel_pandora_event`
- `pandora.kickbox_event`
- `pandora.partner`
- `pandora.context`
"""
# Commands for PREPROD ENV
# gsutil cp dags/matrix__backup_pandora.py gs://europe-west1-prod-mozart-co-2ef7e904-bucket/dags/

# Transfers sql to gcs (preprod) using:
# gsutil -m cp -R data/sql/backup/pandora/*  gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/backup/pandora/

import os
from datetime import timedelta, datetime

from airflow.models import Variable
from airflow.operators.python import PythonOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryGetDataOperator
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from bq_plugin.operators.psql_to_bigquery_operator import PostgresToBigQueryOperator
from collapsible_doc_dag import CDocDAG

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']

dag_name = 'matrix__backup_pandora'
bq_project = 'pm-prod-matrix'

email_on_failure = True
# ------------- FOR PREPROD ENV -----------------
env = os.environ.get("ENV")
if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    bq_project = 'pm-preprod-matrix'
# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2020, 5, 7, 00, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=3),
    'sla': None,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
    'depends_on_past': False,

    'bucket': matrix_bucket,

    # PSQL Connection
    'conn_id': 'psql_matrix_email_app',
    'postgres_conn_id': 'psql_matrix_email_app',
    'database': 'matrix',
    'schema': 'matrix__email_queue',

    # GCP CONNECTION
    'gcp_conn_id': 'gcs_matrix',
    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_TRUNCATE',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',
}

CURRENT_DATE = '{{ ds }}'
CURRENT_DATEHOUR = '{{execution_date.strftime("%Y_%m_%d_%H%M")}}'

pandora_backup_params = Variable.get('pandora_backup_params', deserialize_json=True)
chunk_size = int(pandora_backup_params['event_chunk'])

snapshot_folder = 'backup_matrix/pandora/' + CURRENT_DATE + '/'
schemas_folder = 'backup_matrix/pandora/schemas/'

with CDocDAG(
        dag_name,
        description='Pandora backup tables from psql to BQ Process',
        doc_md=__doc__,
        schedule_interval="40 * * * *",
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=3),
        tags=["backup"],
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/backup/pandora/']
) as dag:
    # Get sequence value
    get_sequence = SQLExecuteQueryOperator(
        task_id='get_sequence',
        sql='00_get_sequence.sql',
        params={'chunk_size': chunk_size},
        conn_id='psql_pandora_app',
    )

    # -----------------------------------------
    # TIME SNAPSHOT FOR PMC TABLES
    # -----------------------------------------
    pandora_snapshot = SQLExecuteQueryOperator(
        task_id='time_snapshot',
        sql='01_pandora_snapshot.sql',
    )

    # ------------------------------------------------
    # Compute instant snapshots
    # ------------------------------------------------

    # ----------------------------------------------------------------------------------------
    # Table pandora.event
    event_schemas = [{"mode": "NULLABLE", "name": "id", "type": "INTEGER"},
                     {"mode": "NULLABLE", "name": "response", "type": "STRING"},
                     {"mode": "NULLABLE", "name": "create_date", "type": "TIMESTAMP"},
                     {"mode": "NULLABLE", "name": "payload", "type": "STRING"},
                     {"mode": "NULLABLE", "name": "cancel", "type": "STRING"},
                     {"mode": "NULLABLE", "name": "email_event_id", "type": "STRING"},
                     {"mode": "NULLABLE", "name": "context_id", "type": "STRING"},
                     {"mode": "NULLABLE", "name": "partner_id", "type": "STRING"}]
    import_event = PostgresToBigQueryOperator(
        task_id='import_event',
        database='matrix',
        table='matrix__email_tmp.pandora_event_snapshot',
        bucket=matrix_bucket,
        source_object=snapshot_folder + 'event_' + CURRENT_DATEHOUR + '.csv',
        destination_project_dataset_table=bq_project + ':import.pandora_event_' + CURRENT_DATEHOUR,
        schema_fields=event_schemas,
    )

    # Format Data Overview  BQ -> BQ
    prepare_event = BigQueryExecuteQueryOperator(
        task_id='prepare_event',
        sql='02_prepare_event.sql',
        params={
            'import_table': bq_project + '.import.pandora_event',
            'store_table': bq_project + '.store_pandora.event',
            'store_email': bq_project + '.store_matrix_email',
            'pmi_global': bq_project + '.store_matrix_email.profile_master_id'
        },
        destination_dataset_table=bq_project + ':prepare.pandora_event_' + CURRENT_DATEHOUR,
    )

    # Upsert Event
    upsert_event = BigQueryExecuteQueryOperator(
        task_id='upsert_event',
        sql='03_upsert_event.sql',
        params={
            'prepare_table': bq_project + '.prepare.pandora_event'
        },
        destination_dataset_table=bq_project + '.store_pandora.event',
        write_disposition='WRITE_APPEND',
    )

    import_event >> prepare_event >> upsert_event
    # ----------------------------------------------------------------------------------------

    # Table pandora.pmc_event
    pmc_event_schemas = [{"mode": "NULLABLE", "name": "event_id", "type": "INTEGER"},
                         {"mode": "NULLABLE", "name": "response", "type": "STRING"},
                         {"mode": "NULLABLE", "name": "api_message", "type": "STRING"},
                         {"mode": "NULLABLE", "name": "brand_id", "type": "STRING"}]
    import_pmc_event = PostgresToBigQueryOperator(
        task_id='import_pmc_event',
        database='matrix',
        table='matrix__email_tmp.pandora_pmc_event_snapshot',
        bucket=matrix_bucket,
        source_object=snapshot_folder + 'pmc_event_' + CURRENT_DATEHOUR + '.csv',
        destination_project_dataset_table=bq_project + ':import.pandora_pmc_event_' + CURRENT_DATEHOUR,
        schema_fields=pmc_event_schemas,
    )

    # Format Data Overview  BQ -> BQ
    prepare_pmc_event = BigQueryExecuteQueryOperator(
        task_id='prepare_pmc_event',
        sql='02_prepare_pmc_event.sql',
        params={
            'import_table': bq_project + '.import.pandora_pmc_event',
            'store_table': bq_project + '.store_pandora.pmc_event'
        },
        destination_dataset_table=bq_project + ':prepare.pandora_pmc_event_' + CURRENT_DATEHOUR,
    )

    # Upsert Events
    upsert_pmc_event = BigQueryExecuteQueryOperator(
        task_id='upsert_pmc_event',
        sql='03_upsert_pmc_event.sql',
        params={
            'prepare_table': bq_project + '.prepare.pandora_pmc_event'
        },
        destination_dataset_table=bq_project + '.store_pandora.pmc_event',
        write_disposition='WRITE_APPEND',
    )

    import_pmc_event >> prepare_pmc_event >> upsert_pmc_event
    # ----------------------------------------------------------------------------------------
    # Table pandora.kickbox_event
    kickbox_event_schemas = [{"mode": "NULLABLE", "name": "id", "type": "INTEGER"},
                             {"mode": "NULLABLE", "name": "create_date", "type": "TIMESTAMP"},
                             {"mode": "NULLABLE", "name": "email", "type": "STRING"},
                             {"mode": "NULLABLE", "name": "response", "type": "STRING"},
                             {"mode": "NULLABLE", "name": "return_http_code", "type": "INTEGER"},
                             {"mode": "NULLABLE", "name": "return_payload", "type": "STRING"}]
    import_kickbox_event = PostgresToBigQueryOperator(
        task_id='import_kickbox_event',
        database='matrix',
        table='matrix__email_tmp.kickbox_event_snapshot',
        bucket=matrix_bucket,
        source_object=snapshot_folder + 'kickbox_event_' + CURRENT_DATEHOUR + '.csv',
        destination_project_dataset_table=bq_project + ':import.pandora_kickbox_event_' + CURRENT_DATEHOUR,
        schema_fields=kickbox_event_schemas,
    )

    # Format Data Overview  BQ -> BQ
    prepare_kickbox_event = BigQueryExecuteQueryOperator(
        task_id='prepare_kickbox_event',
        sql='02_prepare_kickbox_event.sql',
        params={
            'import_table': bq_project + '.import.pandora_kickbox_event',
            'store_table': bq_project + '.store_pandora.kickbox_event',
            'store_email': bq_project + '.store_matrix_email',
            'pmi_global': bq_project + '.store_matrix_email.profile_master_id'
        },
        destination_dataset_table=bq_project + ':prepare.pandora_kickbox_event_' + CURRENT_DATEHOUR,
    )

    # Upsert Events
    upsert_kickbox_event = BigQueryExecuteQueryOperator(
        task_id='upsert_kickbox_event',
        sql='03_upsert_kickbox_event.sql',
        params={
            'prepare_table': bq_project + '.prepare.pandora_kickbox_event'
        },
        destination_dataset_table=bq_project + '.store_pandora.kickbox_event',
        write_disposition='WRITE_APPEND',
    )

    import_kickbox_event >> prepare_kickbox_event >> upsert_kickbox_event
    # ----------------------------------------------------------------------------------------

    # Table
    cancel_pandora_event_schemas = [{"mode": "NULLABLE", "name": "pandora_event_id", "type": "INTEGER"},
                                    {"mode": "NULLABLE", "name": "unsub_event_id", "type": "INTEGER"},
                                    {"mode": "NULLABLE", "name": "create_date", "type": "TIMESTAMP"}]
    import_cancel_pandora_event = PostgresToBigQueryOperator(
        task_id='import_cancel_pandora_event',
        database='matrix',
        table='matrix__email_tmp.cancel_pandora_event_snapshot',
        bucket=matrix_bucket,
        source_object=snapshot_folder + 'cancel_pandora_event_' + CURRENT_DATEHOUR + '.csv',
        destination_project_dataset_table=bq_project + ':import.cancel_pandora_event_' + CURRENT_DATEHOUR,
        schema_fields=cancel_pandora_event_schemas,
    )

    # Format Data Overview  BQ -> BQ
    prepare_cancel_pandora_event = BigQueryExecuteQueryOperator(
        task_id='prepare_cancel_pandora_event',
        sql='02_prepare_cancel_pandora_event.sql',
        params={
            'import_table': bq_project + '.import.cancel_pandora_event',
            'store_table': bq_project + '.store_pandora.cancel_pandora_event'
        },
        destination_dataset_table=bq_project + ':prepare.cancel_pandora_event_' + CURRENT_DATEHOUR,
    )

    # Upsert Events
    upsert_cancel_pandora_event = BigQueryExecuteQueryOperator(
        task_id='upsert_cancel_pandora_event',
        sql='03_upsert_cancel_pandora_event.sql',
        params={
            'prepare_table': bq_project + '.prepare.cancel_pandora_event'
        },
        destination_dataset_table=bq_project + '.store_pandora.cancel_pandora_event',
        write_disposition='WRITE_APPEND',
    )

    import_cancel_pandora_event >> prepare_cancel_pandora_event >> upsert_cancel_pandora_event
    # ----------------------------------------------------------------------------------------

    # Table pandora.context
    context_schemas = [{"mode": "NULLABLE", "name": "id", "type": "INTEGER"},
                       {"mode": "NULLABLE", "name": "name", "type": "STRING"},
                       {"mode": "NULLABLE", "name": "create_date", "type": "TIMESTAMP"},
                       {"mode": "NULLABLE", "name": "update_date", "type": "TIMESTAMP"},
                       {"mode": "NULLABLE", "name": "require_validation", "type": "BOOLEAN"},
                       {"mode": "NULLABLE", "name": "kickbox_validation", "type": "BOOLEAN"},
                       {"mode": "NULLABLE", "name": "brand_id", "type": "STRING"},
                       {"mode": "NULLABLE", "name": "lever", "type": "STRING"},
                       {"mode": "NULLABLE", "name": "destination", "type": "STRING"},
                       {"mode": "NULLABLE", "name": "has_pmc", "type": "BOOLEAN"},
                       {"mode": "NULLABLE", "name": "has_welcome", "type": "BOOLEAN"},
                       {"mode": "NULLABLE", "name": "interaction", "type": "STRING"}]
    import_context = PostgresToBigQueryOperator(
        task_id='import_context',
        database='matrix',
        table='matrix__email_tmp.pandora_context_snapshot',
        bucket=matrix_bucket,
        source_object=snapshot_folder + 'context_' + CURRENT_DATEHOUR + '.csv',
        destination_project_dataset_table=bq_project + ':import.pandora_context_' + CURRENT_DATEHOUR,
        schema_fields=context_schemas,
    )

    # Format Data Overview  BQ -> BQ
    prepare_context = BigQueryExecuteQueryOperator(
        task_id='prepare_context',
        sql='02_prepare_context.sql',
        params={
            'import_table': bq_project + '.import.pandora_context',
            'store_table': bq_project + '.store_pandora.context'
        },
        destination_dataset_table=bq_project + ':prepare.pandora_context_' + CURRENT_DATEHOUR,
    )

    # Upsert Events
    upsert_context = BigQueryExecuteQueryOperator(
        task_id='upsert_context',
        sql='03_upsert_context.sql',
        params={
            'prepare_table': bq_project + '.prepare.pandora_context'
        },
        destination_dataset_table=bq_project + '.store_pandora.context',
    )

    import_context >> prepare_context >> upsert_context
    # ----------------------------------------------------------------------------------------

    # Table pandora.partner
    partner_schemas = [{"mode": "NULLABLE", "name": "id", "type": "INTEGER"},
                       {"mode": "NULLABLE", "name": "name", "type": "STRING"},
                       {"mode": "NULLABLE", "name": "create_date", "type": "TIMESTAMP"},
                       {"mode": "NULLABLE", "name": "update_date", "type": "TIMESTAMP"}]
    import_partner = PostgresToBigQueryOperator(
        task_id='import_partner',
        database='matrix',
        table='matrix__email_tmp.pandora_partner_snapshot',
        bucket=matrix_bucket,
        source_object=snapshot_folder + 'partner_' + CURRENT_DATEHOUR + '.csv',
        destination_project_dataset_table=bq_project + ':import.pandora_partner_' + CURRENT_DATEHOUR,
        schema_fields=partner_schemas,
    )

    # Format Data Overview  BQ -> BQ
    prepare_partner = BigQueryExecuteQueryOperator(
        task_id='prepare_partner',
        sql='02_prepare_partner.sql',
        params={
            'import_table': bq_project + '.import.pandora_partner',
            'store_table': bq_project + '.store_pandora.partner'
        },
        destination_dataset_table=bq_project + ':prepare.pandora_partner_' + CURRENT_DATEHOUR,
    )

    # Upsert Events
    upsert_partner = BigQueryExecuteQueryOperator(
        task_id='upsert_partner',
        sql='03_upsert_partner.sql',
        params={
            'prepare_table': bq_project + '.prepare.pandora_partner'
        },
        destination_dataset_table=bq_project + '.store_pandora.partner',

    )

    import_partner >> prepare_partner >> upsert_partner
    # ----------------------------------------------------------------------------------------

    # --------------------------------------------------
    # update sequence values psql
    # --------------------------------------------------
    update_sequence_value = SQLExecuteQueryOperator(
        task_id='update_sequence_value',
        sql='10_update_sequence_value.sql',
        conn_id='psql_pandora_app',
    )

    # ----------------------------------------------------------------------------------------
    get_sequence >> pandora_snapshot

    pandora_snapshot >> [import_partner, import_context, import_event, import_pmc_event,
                         import_kickbox_event, import_cancel_pandora_event]

    [upsert_partner, upsert_context, upsert_event, upsert_kickbox_event, upsert_pmc_event,
     upsert_cancel_pandora_event] >> update_sequence_value

    task_doc_md = """
        Update cancel field in store pandora event table.<br />
    """
    store_pandora_update_cancel_field = BigQueryExecuteQueryOperator(
        task_id='store_pandora_update_cancel_field',
        sql='09_store_pandora_update_cancel_field.sql',
        params={
            'bq_project': bq_project
        }
    )
    store_pandora_update_cancel_field.doc_md = task_doc_md

    update_sequence_value >> store_pandora_update_cancel_field

    # ----------------------------------------------------------------------------------------
    # Compute custom table to update events in store_pandora and with pmi = NULL
    # ----------------------------------------------------------------------------------------

    def __fix_event_without_pmi(**kwargs):
        dags_dth = kwargs['datehour']
        dags_dt = kwargs['date']
        sql_header = kwargs['sql_header']
        # get pandora events ids where pmi is null
        print('interval : ' + pandora_backup_params['interval_check_pmi_null'])
        print('full_fix_pmi_null : ' + pandora_backup_params['full_fix_pmi_null'])

        query_cdt = ''
        if pandora_backup_params['full_fix_pmi_null'].lower() == 'false':
            query_cdt = 'AND store.create_date > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL {interval_check_pmi_null})'.format(
                interval_check_pmi_null=pandora_backup_params['interval_check_pmi_null'])

        query = """
        -- mozart-id : {sql_header}
             SELECT
                STRING_AGG(CAST(store.id AS STRING)) AS ids
            FROM `store_pandora.event` AS store
            WHERE store.profile_master_id IS NULL
                AND store.response IN ('REA', 'DPL', 'NEW')
                {query_cdt}
            ;
        """.format(sql_header=sql_header,
                   query_cdt=query_cdt)

        export_pandora_ids_without_pmi = BigQueryExecuteQueryOperator(
            task_id='export_pandora_ids_without_pmi',
            use_legacy_sql=False,
            write_disposition='WRITE_TRUNCATE',
            allow_large_results=True,
            flatten_results=False,
            sql=query,  # '100_get_event_without_pmi.sql',
            destination_dataset_table=bq_project + ':export_matrix_email.pandora_ids_without_pmi',
            gcp_conn_id='bq_matrix',
            params={
                'interval_check_pmi_null': pandora_backup_params['interval_check_pmi_null'],
                'full_fix_pmi_null': pandora_backup_params['full_fix_pmi_null']
            }
        )
        export_pandora_ids_without_pmi.execute(kwargs)

        get_data_pandora_ids_without_pmi = BigQueryGetDataOperator(
            task_id='get_data_pandora_ids_without_pmi',
            dataset_id='export_matrix_email',
            table_id='pandora_ids_without_pmi',
            gcp_conn_id='bq_matrix',
            max_results=1,
            selected_fields='ids',
        ).execute(kwargs)

        data_pandora_ids_without_pmi = get_data_pandora_ids_without_pmi[0][0]
        print('get_data_pandora_ids_without_pmi: ' + str(data_pandora_ids_without_pmi))
        if not data_pandora_ids_without_pmi:
            return []

        snapshot_query = '''
            -- mozart-id : {sql_header}
            -- ---------------------------------------------
            --  get pandora_ids without pmi snapshot (Ref pandora id: store BQ)
            -- ---------------------------------------------
            DROP TABLE IF EXISTS matrix__email_tmp.pandora_with_fixed_pmi_snapshot;
            CREATE TABLE IF NOT EXISTS matrix__email_tmp.pandora_with_fixed_pmi_snapshot AS
            SELECT
                pe.id,
                pmi.id AS profile_master_id
            FROM pandora.event AS pe
            JOIN matrix__email.profile_master_id AS pmi ON pmi.email = pe.payload->>'email'
            WHERE pe.id IN ({data_pandora_ids_without_pmi})
            ;
            
            ALTER TABLE matrix__email_tmp.pandora_with_fixed_pmi_snapshot
            OWNER TO matrix_email;
            -- Time:
        '''.format(sql_header=sql_header,
                   data_pandora_ids_without_pmi=data_pandora_ids_without_pmi)

        prepare_snapshot_pandora_event_with_fixed_pmi = SQLExecuteQueryOperator(
            task_id='prepare_snapshot_pandora_event_with_fixed_pmi',
            sql=snapshot_query,
            conn_id='psql_matrix_email_app',
        ).execute(kwargs)

        # backup Computed Table pandora_ids_without_pmi_snapshot
        pandora_ids_without_pmi_schemas = [
            {"mode": "NULLABLE", "name": "id", "type": "INTEGER"},
            {"mode": "NULLABLE", "name": "profile_master_id", "type": "STRING"}
        ]

        import_pandora_with_fixed_pmi = PostgresToBigQueryOperator(
            task_id='import_pandora_with_fixed_pmi',
            database='matrix',
            table='matrix__email_tmp.pandora_with_fixed_pmi_snapshot',
            write_disposition='WRITE_TRUNCATE',
            bucket=matrix_bucket,
            source_object='backup_matrix/pandora/' + dags_dt + '/' + 'event_' + dags_dth + '.csv',
            destination_project_dataset_table=bq_project + '.import.pandora_with_fixed_pmi_snapshot_' + dags_dth,
            gcp_conn_id='bq_matrix',
            bigquery_conn_id='bq_matrix',
            postgres_conn_id='psql_matrix_email_app',
            schema_fields=pandora_ids_without_pmi_schemas,
        ).execute(kwargs)

        get_data_pandora_with_fixed_pmi = BigQueryGetDataOperator(
            task_id='get_data_pandora_with_fixed_pmi',
            dataset_id='import',
            table_id='pandora_with_fixed_pmi_snapshot_' + dags_dth,
            gcp_conn_id='bq_matrix',
            max_results=1,
            selected_fields='id',
        )
        data_pandora_with_fixed_pmi = get_data_pandora_with_fixed_pmi.execute(kwargs)
        print('data_pandora_with_fixed_pmi' + str(data_pandora_with_fixed_pmi))

        if not data_pandora_with_fixed_pmi:
            return []

        update_query = '''
            -- mozart-id : {sql_header}
            MERGE `store_pandora.event` AS store
                USING `{import_table}` AS prepare
                ON store.id = CAST(prepare.id AS INT64)
                AND store.profile_master_id IS NULL
            WHEN MATCHED THEN
            UPDATE SET profile_master_id = CAST(prepare.profile_master_id AS INT64)
            ;
        '''.format(sql_header=sql_header,
                   import_table=bq_project + '.import.pandora_with_fixed_pmi_snapshot_' + dags_dth)
        # update_event_without_pmi
        update_pandora_event_without_pmi = BigQueryExecuteQueryOperator(
            task_id='update_event_without_pmi',
            sql=update_query,
            params={
                'import_table': bq_project + '.import.pandora_with_fixed_pmi_snapshot'
            },
            gcp_conn_id='bq_matrix',
            use_legacy_sql=False,
            write_disposition='WRITE_TRUNCATE',
            allow_large_results=True,
            flatten_results=False,
        ).execute(kwargs)


    fix_store_pandora_events_without_pmi = PythonOperator(
        task_id='fix_store_pandora_events_without_pmi',
        python_callable=__fix_event_without_pmi,
        provide_context=True,
        op_kwargs={
            'datehour': CURRENT_DATEHOUR,
            'date': CURRENT_DATE,
            'sql_header': '{{ dag.dag_id }}.{{ task.task_id }}'
        }
    )
    fix_store_pandora_events_without_pmi.doc_md = 'fix events with pmi null in the pandora events store'

    upsert_event >> fix_store_pandora_events_without_pmi

    import_pandora_context_email_consent_schema = [
        {"mode": "NULLABLE", "name": "email_consent_id", "type": "INTEGER"},
        {"mode": "NULLABLE", "name": "context_id", "type": "INTEGER"}
    ]
    import_pandora_context_email_consent = PostgresToBigQueryOperator(
        task_id='import_pandora_context_email_consent',
        database='matrix',
        table='pandora.contexts_email_consents',
        bucket=matrix_bucket,
        source_object=snapshot_folder + 'context_email_consent_{{next_execution_date.strftime("%Y_%m_%d_%H%M")}}.csv',
        destination_project_dataset_table=bq_project + ':import.context_email_consent',
        schema_fields=import_pandora_context_email_consent_schema,
        field_delimiter='\t',
        allow_quoted_newlines=True,
        quote_character='',
        trigger_rule='none_failed'
    )
    import_pandora_context_email_consent.doc_md = 'import_pandora_context_consent_ids'

    task_doc_md = """
    Update store_pandora_context_email_consent table.<br />
    """
    store_pandora_context_email_consent = BigQueryExecuteQueryOperator(
        task_id='store_pandora_context_email_consent',
        sql='store_pandora_context_email_consent.sql',
        params={
            'bq_project': bq_project
        }
    )
    store_pandora_context_email_consent.doc_md = task_doc_md

    import_pandora_context_email_consent >> store_pandora_context_email_consent
