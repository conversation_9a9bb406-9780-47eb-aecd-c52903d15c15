r"""
**PURPOSE:**

Check Elisa api & send alertes.

**METHODOLOGY:**

- Checks if there are new publications for Capital PayWall
- Then triggers a tmail alert api for only new ids
- keep in table ids already executed
"""
# gsutil cp dags/elisa_email.py gs://europe-west1-mozart-cluster-2e910a39-bucket/dags/
# gsutil -m cp -R plugins/prisma.py gs://europe-west1-mozart-cluster-2e910a39-bucket/plugins/
# gsutil -m cp -R plugins/prisma_plugin/*  gs://europe-west1-mozart-cluster-2e910a39-bucket/plugins/prisma_plugin/

import os
from datetime import timedelta, datetime

from airflow.models import Variable
from prisma_plugin.operators.elisa_operator import ElisaOperator
from collapsible_doc_dag import CDocDAG

dag_name = 'elisa_email'

email_on_failure = True
# ------------- FOR PREPROD ENV -----------------
env = os.environ.get("ENV")
env_str = ''
tmail_api_uri = 'https://tmail.prismadata.fr'
if env != 'prod':
    env_str = '_' + env
    tmail_api_uri = 'https://tmail.preprod.prismadata.fr'
    email_on_failure = False
    bq_project = 'pm-preprod-matrix'
# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2022, 4, 1, 00, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=4),
    'sla': None,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'depends_on_past': False,

    # PSQL Connection
    'postgres_conn_id': 'psql_matrix_email_app',
    'database': 'matrix',
    'gcp_conn_id': 'gcs_matrix'
}

with CDocDAG(dag_name,
             schedule_interval="27 9 * * *",
             description='Check Elisa api & send alertes',
             doc_md=__doc__,
             default_args=default_args,
             max_active_runs=1,
             dagrun_timeout=timedelta(hours=8),
             catchup=False,
             template_searchpath=['/home/<USER>/gcs/data/sql/matrix_email/']) as dag:
    task_doc_md = """
        - Checks if there are new publications for Capital PayWall<br />
        - Then triggers a tmail alert for only new ids<br />
        - keep in table ids already executed<br />
    """
    elisa_process_items = ElisaOperator(
        task_id='elisa_process_items',
        tmail_api_uri=tmail_api_uri

    )
    elisa_process_items.doc_md = task_doc_md
