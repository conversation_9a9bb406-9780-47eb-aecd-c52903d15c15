r"""
**PURPOSE**:
This DAG is used to send daily all purged profiles to webrivage.

**METHODOLOGY:**

Each day, we will take every profile which have received a shopping NL over the past 30 days, and send them to Webrivage
"""

# gsutil cp dags/partner_export_nls_repoussoir.py gs://europe-west1-mozart-cluster-2e910a39-bucket/dags/

# gsutil -m cp -R data/sql/*  gs://europe-west1-mozart-cluster-2e910a39-bucket/data/sql/

import os
from datetime import timedelta, datetime

from airflow.models import Variable
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.google.cloud.transfers.bigquery_to_gcs import BigQueryToGCSOperator
from collapsible_doc_dag import CDocDAG
from sftp_plugin.operators.gcs_to_ftp_operator import GoogleCloudStorageToFtpOperator
from sftp_plugin.operators.gcs_to_sftp_operator import GoogleCloudStorageToSftpOperator

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']

dag_name = 'partner_export_nls_repoussoir'
email_on_failure = True
email_on_retry = False

bq_project = 'pm-prod-matrix'
gcs_uri = 'gs://it-data-prod-matrix-pipeline/'

# ------------- FOR PREPROD ENV -----------------
env = os.environ.get("ENV")
if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    email_on_retry = False
    bq_project = 'pm-preprod-matrix'
    gcs_uri = 'gs://it-data-preprod-matrix-preprod-pipeline/'
# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'start_date': datetime(2019, 12, 16, 00, 00, 00),
    'retries': 5,
    'retry_delay': timedelta(minutes=5),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'email_on_retry': email_on_retry,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=8),
    'sla': None,
    # postgres
    'database': 'matrix',
    'schema': 'matrix__email_generic_export',
    # conn id
    'postgres_conn_id': 'psql_matrix_email_app',
    'gcp_conn_id': 'gcs_matrix',
    # export format & destination
    'export_format': 'csv',
    'field_delimiter': ';',  # common param for bQ also
    'gzip': False,
    'parameters': None,
    'bucket': matrix_bucket,  # common param for bQ also

    ## BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_TRUNCATE',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',
    'source_format': 'CSV',
    # 'skip_leading_rows':0, # skeep header
    'autodetect': False,
    'max_bad_records': "0",
    'allow_jagged_rows': True,
    'ignore_unknown_values': True,
    'quote_character': '',

    # google cloud storage for export
    'gcs_conn_id': 'gcs_matrix',
    'gcs_bucket': matrix_bucket,
}

path_export_purge = 'gs://it-data-prod-matrix-pipeline/partners/repoussoir_export/{{ next_ds }}/'
short_path_export_purge = 'partners/repoussoir_export/{{ next_ds }}/'


with CDocDAG(dag_name,
             description='Send NL shopping receiving profiles to Webrivage and Dcode',
             doc_md=__doc__,
             tags=["partner"],
             schedule_interval="30 12 * * *",  # daily at 12H30
             catchup=False,
             max_active_runs=1,
             dagrun_timeout=timedelta(hours=15),
             template_searchpath=['/home/<USER>/gcs/data/sql/partners/exports/repoussoir/'],
             default_args=default_args) as dag:
    task_doc_md = """
    We compute every profile to have received a shopping NL over the past 30 days
    """

    compute_nls_profiles_30d = BigQueryExecuteQueryOperator(
        task_id='compute_nls_profiles_30d',
        sql='compute_nls_profiles_30d.sql',
        use_legacy_sql=False,
        params={
            'bq_project': bq_project
        }
    )
    compute_nls_profiles_30d.doc_md = task_doc_md

    task_doc_md = """
    We export the table fro webrivage to purge into Google Cloud Storage.<br />
    """
    export_nls_30d = BigQueryToGCSOperator(
        task_id='export_nls_30d',
        destination_cloud_storage_uris=[
            path_export_purge + 'nl_shopping_{}.csv'.format('{{ next_ds_nodash }}')],
        source_project_dataset_table='{}.export_partner.nls_30j'.format(bq_project),
        print_header=False,
        field_delimiter='',
        export_format='CSV'
    )
    export_nls_30d.doc_md = task_doc_md

    task_doc_md = """
    We export the purge file from gcs to webrivage sftp: /export/purge<br />
    """
    export_nls_30d_to_webrivage = GoogleCloudStorageToFtpOperator(
        task_id='export_nls_30d_to_webrivage',
        gcs_bucket=buckets['matrix'],
        gcs_filename=short_path_export_purge + 'nl_shopping_{}.csv'.format('{{ next_ds_nodash }}'),
        ftp_conn_id='ftp_webrivage',
        ftp_filename='export/repoussoir/nl_shopping_{}.csv'.format('{{ next_ds_nodash }}')
    )
    export_nls_30d_to_webrivage.doc_md = task_doc_md

    export_nls_30d_to_dcode = GoogleCloudStorageToSftpOperator(
        task_id='export_nls_30d_to_dcode',
        gcs_bucket=buckets['matrix'],
        gcs_filename=short_path_export_purge + 'nl_shopping_{}.csv'.format('{{ next_ds_nodash }}'),
        sftp_conn_id='sftp_dcode',
        sftp_filename='{}_nl_shopping.csv'.format('{{ next_ds_nodash }}')
    )
    export_nls_30d_to_dcode.doc_md = task_doc_md

    compute_nls_profiles_30d >> export_nls_30d >> [export_nls_30d_to_webrivage, export_nls_30d_to_dcode]
