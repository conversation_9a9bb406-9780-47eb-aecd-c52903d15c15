r"""
**PURPOSE:**
- This process push received lead gen from Meta to Studi (Car Data Api) using sheets
and bigquery external tables defined in airflow variable leadgen studi data

**METHODOLOGY:**

- Daily get data from `store_leadgen.leadgen_data_XXX tables`
-**Check if row data already treated using log table:**`store_leadgen.leadgen_data_XXX_logs`
- If not exist in logs, push profile to Car Data Api
- If response statut = OK => add row to logs table

- # gsutil cp dags/leadgen.py gs://europe-west1-prod-mozart-co-2ef7e904-bucket/dags/
# gsutil cp plugins/partners_plugin/operators/car_data_leadgen_export.py gs://europe-west1-prod-mozart-co-2ef7e904-bucket/plugins/partners_plugin/operators/
"""
import os
from datetime import timedelta, datetime

from airflow.models import Variable
from partners_plugin.operators.car_data_leadgen_export import CarDataLeadGenExportOperator

from collapsible_doc_dag import CDocDAG

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']
mozart_bucket = buckets['mozart']

env = os.environ.get("ENV")
dag_name = 'leadgen{str_env}'.format(str_env='' if env == 'prod' else '_' + env)
email_on_failure = True if env == 'prod' else False
bq_project = 'pm-{}-matrix'.format(env)

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2024, 8, 9, 10, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=3),
    'sla': None,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=1),
    'depends_on_past': False,

    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_TRUNCATE',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',
    'allow_jagged_rows': True,
}

leadgen_data_params = Variable.get("leadgen_data", deserialize_json=True)

leadgen_renault = leadgen_data_params.get('renault')

with CDocDAG(
        dag_name,
        description='Export leads from store_leadgen.leadgen_data_* to partners API',
        doc_md=__doc__,
        schedule_interval="0 7 * * MON",
        default_args=default_args,
        tags=["partner", "leadgen"],
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=2),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/leadgen/']
) as dag:
    """
        {
           "dacia": {
              "brand":"dacia",
              "type":"Cross Energy",
              "model":"Jogger",
              "campaign_brands":{
                 "cap":{
                    "client_token":"XX",
                    "code_campaign":"2025-06_prisma_dacia vp_jogger",
                    "table":"leadgen_dacia_cap_raw_data"
                 },
                 "geo":{
                    "client_token":"XX",
                    "code_campaign":"2025-06_prisma_dacia vp_jogger",
                    "table":"leadgen_dacia_geo_raw_data"
                 }
              },
              "data_interval_day":25,
              "is_full":false,
              "error_api_sender":"XX",
              "is_test":false
           }
        }
    """
    for brand, brand_config in leadgen_data_params.items():
        export_leads_to_car_data = CarDataLeadGenExportOperator(
            task_id=f'export_leads_{brand}',
            bq_project=bq_project,
            brand_config=brand_config
        )
        export_leads_to_car_data.doc_md = f"""
            Export leads for brand = {brand}
        """
