r"""
**PURPOSE**:
This DAG is used to send incremental informations to partners about sub, unsub and blacklist.
It will run each day at 1 AM UTC and need snapshot prisma (inside refined_data__email) in order to work properly.

- Also, Daily export selected consents subs to Ownpage (sftp prismadata)

**METHODOLOGY:**

From snapshot J and J-2, we create a table store_matrix_email.generic_export_inc
which is all difference between snapshot J and J-2 in the last 5 days.

From this table, we isolate Sub / unsub and blacklist. 
Before we export table to gcs, we saved them into BQ "import.generic_incremental_(un)sub_" 1 for each partners/consent/status.**Then into GCS we saved them:**-**sub:**/incremental/webrivage/' + 'sub_{val}_' + CURRENT_DATE + '.csv' (for webrivage) 
-**unsub:**'/incremental/_global_unsub/' + 'unsub_{val}_' + CURRENT_DATE + '.csv' 
-**blacklist:**incremental/_global_blacklist/blacklist_'+ public_ref + '_' + CURRENT_DATE + '.csv'  

In the end, we send needed informations according to the partner for sub and files for unsub 
and blacklist are independant of the partners. 

We also used a variable created in this dags config_stats to export information 
about monitoring inside store_monitoring.generic_export_stats

**GOOD TO KNOW:** 

We can use variable like export_generic_partnersname to add or remove consent or informationn that needed to be send
We used to send data to Cartegie, but we cut the process on Dec. 5th 2023.
We can relaunch task if they fail and we might need to delete some file into gcs in order to regenerate them.
"""

# preprod Airflow instance
# gsutil cp dags/partners_export.py gs://europe-west1-prod-mozart-co-2ef7e904-bucket/dags/
# gsutil -m cp -R data/sql/partners/exports/*  gs://europe-west1-prod-mozart-co-2ef7e904-bucket/data/sql/partners/exports/


import json
import logging
import os
from datetime import datetime, timedelta, date

from airflow.decorators import task
from airflow.models import Variable
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import BranchPythonOperator
from airflow.operators.python import PythonOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.google.cloud.transfers.bigquery_to_gcs import BigQueryToGCSOperator
from collapsible_doc_dag import CDocDAG
from export_full_plugin.operators.bigquery_export_to_gcs_operator import BQExportToGcsOperator
from export_incremental_plugin.operators.generic_delivery_export_operator import GenericDeliveryExportOperator
from export_incremental_plugin.operators.generic_export_stats_operator import GenericExportStatsOperator
from export_incremental_plugin.operators.purge_old_files_operator import PurgeOldFilesOperator
from gcs_plugin.operators.gcs_zip_file import GCSZipFileOperator
from psql_plugin.operators.psql_query_to_gcs_operator import PostgresQueryToGoogleCloudStorageOperator
import itdata_plugin

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']

dag_name = 'partners_export'
email_on_failure = True
email_on_retry = False

bq_project = 'pm-prod-matrix'
gcs_uri = 'gs://it-data-prod-matrix-pipeline/'

# ------------- FOR PREPROD ENV -----------------
env = os.environ.get("ENV")
if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    email_on_retry = False
    bq_project = 'pm-preprod-matrix'
    gcs_uri = 'gs://it-data-preprod-matrix-preprod-pipeline/'
# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'start_date': datetime(2019, 12, 16, 00, 00, 00),
    'retries': 5,
    'retry_delay': timedelta(minutes=5),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'email_on_retry': email_on_retry,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=8),
    'sla': None,
    # postgres
    'database': 'matrix',
    'schema': 'matrix__email_generic_export',
    # conn id
    'postgres_conn_id': 'psql_matrix_email_app',
    'gcp_conn_id': 'gcs_matrix',
    # export format & destination
    'export_format': 'csv',
    'field_delimiter': ';',  # common param for bQ also
    'gzip': False,
    'parameters': None,
    'bucket': matrix_bucket,  # common param for bQ also

    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_TRUNCATE',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',
    'source_format': 'CSV',
    # 'skip_leading_rows':0, # skeep header
    'autodetect': False,
    'max_bad_records': "0",
    'allow_jagged_rows': True,
    'ignore_unknown_values': True,
    'quote_character': '',

    # google cloud storage for export
    'gcs_conn_id': 'gcs_matrix',
    'gcs_bucket': matrix_bucket,
}

CURRENT_DATE = '{{next_execution_date.strftime("%Y-%m-%d")}}'

gcs_uri_partners_export = 'gs://it-data-prod-matrix-pipeline/partners_export/'
path_to_export_incremental = 'partners/export_incremental/{{ next_ds }}/'
full_path_to_export_incremental = 'gs://it-data-prod-matrix-pipeline/' + path_to_export_incremental
path_to_export_full = 'partners/export_full/{{ next_ds }}/'
full_path_to_export_full = 'gs://it-data-prod-matrix-pipeline/' + path_to_export_full

# read config from json file
f = open('/home/<USER>/gcs/data/json/export_generic_cloudmedia.json')
export_generic_cloudmedia = json.load(f)
f.close()


@task
def variable_set():
    """
    This task reads the configs inside the `export_generic_cloudmedia` variable (from a json file)
    and updates `export_generic_cloudmedia` Airflow variable
    """
    itdata_plugin.set_variable_in_secret_manager(secret_name="export_generic_cloudmedia", secret_value=export_generic_cloudmedia, serialize_json=True)


# Deal with time checks


def _time_branch_squadata(**kwargs):
    # if we are the 2 of the month
    current_date = date.today()

    if int(current_date.strftime("%d")) == 8:
        # we take the day of the month
        return 'export_generic_full_part_squadata'
    else:
        return 'do_nothing_squadata'


def _time_branch_cloudmedia(**kwargs):
    # if we are the 1st of the month
    current_date = date.today()

    # if int(current_date.strftime("%d")) == 1:
    # This flow is disabled
    if 1==0:
        # we take the day of the month
        return 'export_generic_full_part_cloudmedia'
    else:
        return 'do_nothing_cloudmedia'


def _check_full_user_info(**kwargs):
    enable_full_user_info = kwargs.get('params').get('full_user_info')

    if enable_full_user_info.lower() == 'true':
        # we take the day of the month
        return 'export_consents_user_info_webrivage'
    else:
        return 'do_nothing_full_user_info'


def enable_safigdata(**kwargs):
    enable_safigdata = kwargs.get('params').get('enable_safigdata', False)
    if enable_safigdata:
        return 'upload_to_safigdata'
    else:
        return 'do_nothing_safigdata'


# blacklist

def generate_blacklist_csv(ds, **kwargs):
    CURRENT_DATE = kwargs.get('templates_dict').get('CURRENT_DATE', None)

    query = (
        "SELECT DISTINCT CAST(NULL AS TEXT) AS email_md5, email_sha256, CAST(NULL AS TEXT) AS email, blacklist_date "
        "FROM "
        "(SELECT "
        "    email_sha256, "
        "    to_char(bl.create_date, 'YYYY-MM-DD') AS blacklist_date, "
        "    brand_id "
        "FROM "
        "    matrix__email.blacklist AS bl "
        "    JOIN matrix__email.profile_master_id AS pmi ON bl.profile_master_id = pmi.id "
        "WHERE bl.create_date >= CURRENT_DATE - INTERVAL '10 days') AS data "
        "JOIN "
        "(SELECT brand_id, public_ref, em.id AS consent_id "
        "FROM karinto.brand "
        "    JOIN karinto.email_consent AS em ON brand.id = em.brand_id) AS consent_list "
        "ON data.brand_id = consent_list.brand_id "
        "WHERE consent_id = ")

    for consent_id, public_ref in consents_dict_part.items():
        logging.info('public_ref : {}, consent_id: {} '.format(
            public_ref, consent_id))
        logging.info('current_date : {} '.format(CURRENT_DATE))

        export_consents_blacklist = PostgresQueryToGoogleCloudStorageOperator(
            task_id='export_consents_blacklist_' + public_ref,
            sql=query + consent_id,
            postgres_conn_id='psql_rogue_one_app',
            bucket=buckets['matrix'],
            gcp_conn_id='gcs_matrix',
            filename='partners/export_incremental/' + CURRENT_DATE +
                     '/_global_blacklist/blacklist_' + public_ref + '_' + CURRENT_DATE + '.csv',
            export_format='csv',
            field_delimiter=';',
            gzip=False
        )

        export_consents_blacklist.execute(context=kwargs)


# consent for all partners
consents_dict_part = Variable.get(
    "karinto_email_consents_part", deserialize_json=True)

# config for export delivery
config_webrivage = Variable.get(
    "export_generic_webrivage", deserialize_json=True)  # No gala_part
config_riviera = Variable.get(
    "export_generic_riviera", deserialize_json=True)  # No gala_part
config_cloudmedia = Variable.get(
    "export_generic_cloudmedia", deserialize_json=True)
config_squadata = Variable.get(
    "export_generic_squadata", deserialize_json=True)
config_safigdata = Variable.get(
    "export_generic_safigdata", deserialize_json=True)
config_ownpage = Variable.get(
    "export_generic_ownpage", deserialize_json=True)

# create the variable for monitoring, we retrieve useful consent id to export stat about export to generic_export_stats
# ex :  { "riviera": [8, 12, 16, 19, 20, 22, 24, 25, 27, 80, 84, 107, 108], "webrivage": [12, 16, 19, 20, 22, 24, 25, 27, 80, 107, 108].. }

config_stats = {}
config_stats["webrivage"] = [
    int(i) for i in config_webrivage['needed_consent'].keys()]
config_stats["riviera"] = [int(i)
                           for i in config_riviera['needed_consent'].keys()]
config_stats["cloudmedia"] = [int(i)
                              for i in config_cloudmedia['needed_consent'].keys()]

config_stats["squadata"] = [int(i)
                            for i in config_squadata['needed_consent'].keys()]
config_stats["safigdata"] = [int(i)
                             for i in config_safigdata['needed_consent'].keys()]

# generic_export_full_destination_path = gcs_uri + '/partners_export/' + CURRENT_DATE + '/full-export/sub_{val}_' + CURRENT_DATE + '.csv'
# gcs_uri_partners_export + CURRENT_DATE +'/incremental/safigdata/' + 'sub_{val}_' + CURRENT_DATE + '.csv',

with CDocDAG(dag_name,
             description='send generic export to partners',
             doc_md=__doc__,
             schedule_interval="0 1 * * *",
             tags=['export', 'partner'],
             catchup=False,
             max_active_runs=1,
             dagrun_timeout=timedelta(hours=15),
             template_searchpath=[
                 '/home/<USER>/gcs/data/sql/partners/exports/'],
             default_args=default_args) as dag:
    # ------------------------------------------------
    # Generate fingerprints table for J and J-1
    # ------------------------------------------------

    task_doc_md = """
    This task is used to create two table fingerprint with rich and poor profiles.<br />
    We only used lastnae, firstname,birthdate and gender for our finger print<br />
    """

    generate_fingerprint_table = BigQueryExecuteQueryOperator(
        task_id='generate_fingerprint_table',
        sql='generate_fingerprint_diff.sql'
    )
    generate_fingerprint_table.doc_md = task_doc_md

    # ------------------------------------------------
    # Generate incremental generic export
    # ------------------------------------------------

    task_doc_md = """
    This task is used to create a generic_export_incremental <br />
    It will be generated thanks to an incremental thanks to prisma_snapshot J-2 and J<br />
    This table will be used before filtring and sending information for the partners<br />
    """

    generate_incremental_table = BigQueryExecuteQueryOperator(
        task_id='generate_incremental_table',
        sql='generate_generic_export_incremental.sql',
        params={
            'destination_table_inc': bq_project + '.store_matrix_email.generic_export_inc',
        }
    )
    generate_incremental_table.doc_md = task_doc_md

    # ------------------------------------------------
    # Export to GCS incremental blacklist for all partners into : `partners_export/CURRENT_DATE/incremental/blacklist/blacklist_*_+CURRENT_DATE+.csv`
    # ------------------------------------------------

    task_doc_md = """
    Used to export all blacklist from the last 10 days for consents_dict_part, brand<br />
    This task will use the python function: generate_blacklist_csv<br />
    This function will execute a loop over all consents inside the consents_dict_part from the airflow variable: karinto_email_consents<br />
    For each consent_id, it will save blacklist information which is the result of a psql query inside:
    `partners_export/CURRENT_DATE/incremental/blacklist/blacklist_*_+CURRENT_DATE+.csv`<br />
    """

    export_blacklist = PythonOperator(
        task_id='export_blacklist',
        python_callable=generate_blacklist_csv,
        templates_dict={
            'CURRENT_DATE': CURRENT_DATE
        },
        provide_context=True
    )
    export_blacklist.doc_md = task_doc_md

    # ------------------------------------------------
    # Export to GCS incremental unsub for all partners into : `partners_export/CURRENT_DATE/incremental/unsub/unsub_*_+CURRENT_DATE+.csv`
    # ------------------------------------------------

    task_doc_md = """
    Used to export sub from the last 2 days for all consents_dict_part to create tables which will be sent<br />
    This task will use a specific operator.<br />
    It will use all consents in consents_dict_part from the airflow variable: karinto_email_consents<br />
    and will use them using BQ and unsub.sql to create temporary bq tables:<br />
    - `import.generic_incremental_unsub_*_date`<br />
    before exporting to gcs: `partners_export/CURRENT_DATE/incremental/unsub/unsub_*_+CURRENT_DATE+.csv`<br />
    """

    export_consents_unsub = BQExportToGcsOperator(
        task_id='export_consent_unsub',
        destination_cloud_storage_regex_uri=full_path_to_export_incremental +
                                            '_global_unsub/' + 'unsub_{val}_' + CURRENT_DATE + '.csv',
        compression='NONE',
        export_format='CSV',
        field_delimiter=';',
        print_header=True,
        list_items=consents_dict_part,
        destination_dataset_table='pm-prod-matrix:import.generic_incremental_unsub_{val}_{{next_execution_date.strftime("%Y%m%d")}}',
        sql='unsub.sql',
        params={
            'generic_export_inc': bq_project + '.store_matrix_email.generic_export_inc',
            'consent_id': '{consent_id} '  # keep the space after '}'
        },
    )
    export_consents_unsub.doc_md = task_doc_md

    ############################
    ###      WEBRIVAGE      ####
    ############################

    # prepare sub files

    task_doc_md = """
    Used to export sub from the last 2 days for all consents_dict_part to create tables which will be sent<br />
    This task will use a specific operator.<br />
    It will use all consents in consents_dict_part from the airflow variable: karinto_email_consents<br />
    and will use them using BQ and sub.sql to create temporary bq tables:<br />
    - `import.generic_incremental_sub_*_date`<br />
    before exporting to gcs: `export-generic/CURRENT_DATE/incremental/webrivage/sub_*_+CURRENT_DATE+.csv`<br />
    """

    export_consents_sub_webrivage = BQExportToGcsOperator(
        task_id='export_consents_sub_webrivage',
        destination_cloud_storage_regex_uri=full_path_to_export_incremental +
                                            'webrivage/' + 'sub_{val}_' + CURRENT_DATE + '.csv',
        compression='NONE',
        export_format='CSV',
        field_delimiter=';',
        print_header=True,
        list_items=config_webrivage["needed_consent"],
        destination_dataset_table='pm-prod-matrix:import.generic_incremental_webrivage_sub_{val}_{{next_execution_date.strftime("%Y%m%d")}}',
        sql='sub_export/sub_webrivage.sql',
        params={
            'generic_export_inc': bq_project + '.store_matrix_email.generic_export_inc',
            'consent_id': '{consent_id} ',  # keep the space after '}'
            'full_user_info' : False
        },
    )
    export_consents_sub_webrivage.doc_md = task_doc_md

    # prepare user info files

    task_doc_md = """
        Check whether we want to send a full base of our profile info <br />
        """
    check_full_user_info = BranchPythonOperator(
        task_id="check_full_user_info",
        python_callable=_check_full_user_info,
        params={
            'full_user_info' : config_webrivage["full_user_info"]
        }
    )
    check_full_user_info.doc_md = task_doc_md

    do_nothing_full_user_info = EmptyOperator(task_id='do_nothing_full_user_info')
    do_nothing_full_user_info.doc_md = 'Do nothing'


    task_doc_md = """
        Used to export a full base of our profile info per brand<br />
        """

    export_consents_user_info_webrivage = BQExportToGcsOperator(
        task_id='export_consents_user_info_webrivage',
        destination_cloud_storage_regex_uri=full_path_to_export_incremental +
                                            'webrivage/' + 'profile_{val}_' + CURRENT_DATE + '.csv',
        compression='NONE',
        export_format='CSV',
        field_delimiter=';',
        print_header=True,
        list_items=config_webrivage["needed_consent"],
        destination_dataset_table='pm-prod-matrix:import.generic_incremental_webrivage_profile_{val}_{{next_execution_date.strftime("%Y%m%d")}}',
        sql='sub_export/sub_webrivage.sql',
        params={
            'generic_export_inc': bq_project + '.store_matrix_email.generic_export_inc',
            'consent_id': '{consent_id} ',  # keep the space after '}'
            'full_user_info': True
        },
    )
    export_consents_user_info_webrivage.doc_md = task_doc_md

    # send files to partners

    task_doc_md = """
    Upload requested files from `export-generic/CURRENT_DATE/incremental/`
    to webrivage.<br />
    """

    upload_to_webrivage = GenericDeliveryExportOperator(
        task_id='upload_to_webrivage',
        configuration=config_webrivage['webrivage'],
        default_args={'gcs_dirname': path_to_export_incremental}
    )
    upload_to_webrivage.doc_md = task_doc_md

    task_doc_md = """
        Upload requested full profile info files from `export-generic/CURRENT_DATE/incremental/`
        to webrivage.<br />
        """

    upload_profile_info_to_webrivage = GenericDeliveryExportOperator(
        task_id='upload_profile_info_to_webrivage',
        configuration=config_webrivage['webrivage_user_info'],
        default_args={'gcs_dirname': path_to_export_incremental}
    )
    upload_profile_info_to_webrivage.doc_md = task_doc_md

    ############################
    ###       RIVIERA       ####
    ############################

    # prepare sub files

    task_doc_md = """
    Used to export sub from the last 2 days for all consents_dict_part to create tables which will be sent<br />
    This task will use a specific operator.<br />
    It will use all consents in consents_dict_part from the airflow variable: karinto_email_consents<br />
    and will use them using BQ and sub.sql to create temporary bq tables:<br />
    - `import.generic_incremental_sub_*_date`<br />
    before exporting to gcs: `export-generic/CURRENT_DATE/incremental/riviera/sub_*_+CURRENT_DATE+.csv`<br />
    """

    export_consents_sub_riviera = BQExportToGcsOperator(
        task_id='export_consents_sub_riviera',
        destination_cloud_storage_regex_uri=full_path_to_export_incremental +
                                            'riviera/' + 'sub_{val}_' + CURRENT_DATE + '.csv',
        compression='NONE',
        export_format='CSV',
        field_delimiter=';',
        print_header=True,
        list_items=config_riviera["needed_consent"],
        destination_dataset_table='pm-prod-matrix:import.generic_incremental_riviera_sub_{val}_{{next_execution_date.strftime("%Y%m%d")}}',
        sql='sub_export/sub_riviera.sql',
        params={
            'generic_export_inc': bq_project + '.store_matrix_email.generic_export_inc',
            'consent_id': '{consent_id} ',  # keep the space after '}'
            'full_user_info': config_riviera["full_user_info"]
        },
    )
    export_consents_sub_riviera.doc_md = task_doc_md

    # send files to partners

    task_doc_md = """
    Upload requested files from `export-generic/CURRENT_DATE/incremental/`
    to riviera.<br />
    """

    upload_to_riviera = GenericDeliveryExportOperator(
        task_id='upload_to_riviera',
        configuration=config_riviera['riviera'],
        default_args={'gcs_dirname': path_to_export_incremental},
    )
    upload_to_riviera.doc_md = task_doc_md

    # Purge old file from riviera

    task_doc_md = """
    Purge old files from riviera and notify by email if this task delete at least one file.<br />
    """

    riviera_purge_old_file = PurgeOldFilesOperator(
        task_id='riviera_purge_old_file',
        protocol=config_riviera['riviera']['protocol'].upper(),
        conf=config_riviera['riviera']['data'],
        pattern='.*' + '{{ ds }}' + '.*'
    )
    riviera_purge_old_file.doc_md = task_doc_md

    ######################################
    ###      CLOUDMEDIA (DISABLED)    ####
    ######################################

    # prepare sub files

    task_doc_md = """
    Used to export sub from the last 2 days for all consents_dict_part to create tables which will be sent<br />
    This task will use a specific operator.<br />
    It will use all consents in consents_dict_part from the JSON config file: karinto_email_consents<br />
    and will use them using BQ and sub.sql to create temporary bq tables:<br />
    - `import.generic_incremental_sub_*_date`<br />
    before exporting to gcs: `export-generic/CURRENT_DATE/incremental/cloudmedia/sub_*_+CURRENT_DATE+.csv`<br />
    """

    export_consents_sub_cloudmedia = BQExportToGcsOperator(
        task_id='export_consents_sub_cloudmedia',
        destination_cloud_storage_regex_uri=full_path_to_export_incremental + 'cloudmedia/' + 'sub_{val}_' + CURRENT_DATE + '.csv',
        compression='NONE',
        export_format='CSV',
        field_delimiter=';',
        print_header=True,
        list_items=config_cloudmedia["needed_consent"],
        destination_dataset_table='pm-prod-matrix:import.generic_incremental_cloudmedia_sub_{val}_{{next_execution_date.strftime("%Y%m%d")}}',
        sql='sub_export/sub_cloudmedia.sql',
        params={
            'generic_export_inc': bq_project + '.store_matrix_email.generic_export_inc',
            'consent_id': '{consent_id} ',  # keep the space after '}'
            'full_user_info': config_cloudmedia["full_user_info"]
        },
    )
    export_consents_sub_cloudmedia.doc_md = task_doc_md

    # send files to partners

    task_doc_md = """
    Upload requested files from `export-generic/CURRENT_DATE/incremental/`
    to cloudmedia.<br />
    """

    upload_to_cloudmedia = GenericDeliveryExportOperator(
        task_id='upload_to_cloudmedia',
        configuration=config_cloudmedia['cloudmedia'],
        default_args={'gcs_dirname': path_to_export_incremental},
    )
    upload_to_cloudmedia.doc_md = task_doc_md

    ############################
    ###       SQUADATA      ####
    ############################

    # prepare sub files

    task_doc_md = """
    Used to export sub from the last 2 days for all consents_dict_part to create tables which will be sent<br />
    This task will use a specific operator.<br />
    It will use all consents in consents_dict_part from the airflow variable: karinto_email_consents<br />
    and will use them using BQ and sub.sql to create temporary bq tables:<br />
    - `import.generic_incremental_sub_*_date`<br />
    before exporting to gcs: `export-generic/CURRENT_DATE/incremental/squadata/sub_*_+CURRENT_DATE+.csv`<br />
    """

    export_consents_sub_squadata = BQExportToGcsOperator(
        task_id='export_consents_sub_squadata',
        destination_cloud_storage_regex_uri=full_path_to_export_incremental +
                                            'squadata/' + 'sub_{val}_' + CURRENT_DATE + '.csv',
        compression='NONE',
        export_format='CSV',
        field_delimiter=';',
        print_header=True,
        list_items=config_squadata["needed_consent"],
        destination_dataset_table='pm-prod-matrix:import.generic_incremental_squadata_sub_{val}_{{next_execution_date.strftime("%Y%m%d")}}',
        sql='sub_export/sub_squadata.sql',
        params={
            'generic_export_inc': bq_project + '.store_matrix_email.generic_export_inc',
            'consent_id': '{consent_id} '  # keep the space after '}'
        },
    )
    export_consents_sub_squadata.doc_md = task_doc_md

    # send files to partners

    task_doc_md = """
    Upload requested files from `export-generic/CURRENT_DATE/incremental/`
    to squadata.<br />
    """

    upload_to_squadata = GenericDeliveryExportOperator(
        task_id='upload_to_squadata',
        configuration=config_squadata['squadata'],
        default_args={'gcs_dirname': path_to_export_incremental},
    )
    upload_to_squadata.doc_md = task_doc_md

    ############################
    ###      SAFIGDATA      ####
    ############################

    # prepare sub files

    task_doc_md = """
    Used to export sub from the last 2 days for all consents_dict_part to create tables which will be sent<br />
    This task will use a specific operator.<br />
    It will use all consents in consents_dict_part from the airflow variable: karinto_email_consents<br />
    and will use them using BQ and sub.sql to create temporary bq tables:<br />
    - `import.generic_incremental_sub_*_date`<br />
    before exporting to gcs: `export-generic/CURRENT_DATE/incremental/safigdata/sub_*_+CURRENT_DATE+.csv`<br />
    """

    export_consents_sub_safigdata = BQExportToGcsOperator(
        task_id='export_consents_sub_safigdata',
        destination_cloud_storage_regex_uri=full_path_to_export_incremental +
                                            'safigdata/' + 'sub_{val}_' + CURRENT_DATE + '.csv',
        compression='NONE',
        export_format='CSV',
        field_delimiter=';',
        print_header=True,
        list_items=config_safigdata["needed_consent"],
        destination_dataset_table='pm-prod-matrix:import.generic_incremental_safigdata_sub_{val}_{{next_execution_date.strftime("%Y%m%d")}}',
        sql='sub_export/sub_safigdata.sql',
        params={
            'generic_export_inc': bq_project + '.store_matrix_email.generic_export_inc',
            'consent_id': '{consent_id} '  # keep the space after '}'
        },
    )
    export_consents_sub_safigdata.doc_md = task_doc_md

    # Disable or enable Safigdata
    task_doc_md = """
        Allows us to disable the HSK upload without removing the code<br />
        """
    enable_safigdata = BranchPythonOperator(
        task_id='enable_safigdata',
        python_callable=enable_safigdata,
        provide_context=True,
        # Set to True to enable the safigdata flow to run
        params={'enable_safigdata': False}
    )

    do_nothing_safigdata = EmptyOperator(task_id='do_nothing_safigdata')
    do_nothing_safigdata.doc_md = 'nothing'

    # send files to partners

    task_doc_md = """
    Upload requested files from `export-generic/CURRENT_DATE/incremental/`
    to safigdata.<br />
    """

    upload_to_safigdata = GenericDeliveryExportOperator(
        task_id='upload_to_safigdata',
        configuration=config_safigdata['safigdata'],
        default_args={'gcs_dirname': path_to_export_incremental},
    )
    upload_to_safigdata.doc_md = task_doc_md

    ##########################################
    ###     Build Full Export Generic     ####
    ##########################################

    ############################
    ###       SQUADATA      ####
    ############################

    # we can keep the same date for the moment
    task_doc_md = """
    check time if we need to send the full to squadata<br />
    date is : 8 of each month <br />
    """
    check_time_squadata = BranchPythonOperator(
        task_id="check_time_squadata", python_callable=_time_branch_squadata)
    check_time_squadata.doc_md = task_doc_md

    do_nothing_squadata = EmptyOperator(task_id='do_nothing_squadata')
    do_nothing_squadata.doc_md = 'nothing'

    task_doc_md = """
    This process is launch only when we reach a right date <br />
    Used to know sub from all part consents and brand to create tables <br />
    This task will use a specific operator.<br />
    It will use all part consents in part_items from the airflow variable: karinto_email_consents<br />
    and will use them using BQ and 5_full_export_consent.sql<br />
    to create temporary bq tables: `import.matrix_sub_*_date`'<br />
    before exporting to gcs: `partners-export/CURRENT_DATE/full-export/sub_*_+CURRENT_DATE+.csv`<br />
    It will used prisma snapshot and profile_email to generate this data.
    """

    export_generic_full_part_squadata = BQExportToGcsOperator(
        task_id='export_generic_full_part_squadata',
        destination_cloud_storage_regex_uri=full_path_to_export_full +
                                            'squadata/' + 'sub_{val}_' + CURRENT_DATE + '.csv',
        compression='NONE',
        export_format='CSV',
        field_delimiter=';',
        print_header=True,
        list_items=consents_dict_part,
        destination_dataset_table=bq_project +
                                  ':import.matrix_squadata_sub_full_{val}_{{ next_execution_date.strftime("%Y%m%d") }}',
        sql='full_sub_export/squadata_full_export_consent.sql',
        params={
            'consent_id': '{consent_id} '  # keep the space after '}'
        },
    )
    export_generic_full_part_squadata.doc_md = task_doc_md

    task_doc_md = """
    If the time has come, we send full base to squadata<br />
    We exclude shopping part<br />
    """

    upload_to_squadata_full = GenericDeliveryExportOperator(
        task_id='upload_to_squadata_full',
        configuration=config_squadata['squadata_full'],
        default_args={'gcs_dirname': path_to_export_full + 'squadata/'},
    )
    upload_to_squadata_full.doc_md = task_doc_md

    ############################
    ###      Cloudmedia      ###
    ############################

    # we can keep the same date for the moment
    task_doc_md = """
    Check time to see if we need to send the full sub file to Cloudmedia<br />
    Date is: 1st of each month <br />
    """
    check_time_cloudmedia = BranchPythonOperator(
        task_id="check_time_cloudmedia", python_callable=_time_branch_cloudmedia)
    check_time_cloudmedia.doc_md = task_doc_md

    do_nothing_cloudmedia = EmptyOperator(task_id='do_nothing_cloudmedia')
    do_nothing_cloudmedia.doc_md = 'Do nothing'

    task_doc_md = """
    This process is launched on the first of the month to make sure the 2 bases are up to date with each other <br />
    """

    export_generic_full_part_cloudmedia = BQExportToGcsOperator(
        task_id='export_generic_full_part_cloudmedia',
        destination_cloud_storage_regex_uri=full_path_to_export_full +
                                            'cloudmedia/' + 'sub_full_{val}_' + CURRENT_DATE + '.csv',
        compression='NONE',
        export_format='CSV',
        field_delimiter=';',
        print_header=True,
        list_items=consents_dict_part,
        destination_dataset_table=bq_project +
                                  ':import.matrix_cloudmedia_sub_full_{val}_{{ next_execution_date.strftime("%Y%m%d") }}',
        sql='full_sub_export/cloudmedia_full_export_consent.sql',
        params={
            'consent_id': '{consent_id} '  # keep the space after '}'
        },
    )
    export_generic_full_part_cloudmedia.doc_md = task_doc_md

    task_doc_md = """
    If the time has come, we send full base to cloudmedia<br />
    We exclude shopping part<br />
    """

    upload_to_cloudmedia_full = GenericDeliveryExportOperator(
        task_id='upload_to_cloudmedia_full',
        configuration=config_cloudmedia['cloudmedia_full'],
        default_args={'gcs_dirname': path_to_export_full + 'cloudmedia/'},
    )
    upload_to_cloudmedia_full.doc_md = task_doc_md

    ############################
    ###     Build stats     ####
    ############################

    task_doc_md = """
    Upload stats about sub/unsub/blacklist about the export inside :<br />
        - `store_monitoring.generic_export_stats`<br />
    We need the table generic_export_inc to be created first
    """

    build_stats = GenericExportStatsOperator(
        task_id='build_stats',
        configuration=config_stats,
        bigquery_conn_id='gcs_matrix'
    )
    build_stats.doc_md = task_doc_md

    variable_set()
    generate_fingerprint_table >> generate_incremental_table >> \
    [export_blacklist, export_consents_unsub, export_consents_sub_riviera, check_full_user_info,
     export_consents_sub_webrivage, \
     export_consents_sub_cloudmedia,
     export_consents_sub_safigdata, \
     export_consents_sub_squadata, \
     build_stats]
    [export_blacklist, export_consents_unsub,
     export_consents_sub_webrivage] >> upload_to_webrivage
    [export_blacklist, export_consents_unsub,
     export_consents_sub_riviera] >> upload_to_riviera >> riviera_purge_old_file
    [export_blacklist, export_consents_unsub, export_consents_sub_safigdata] >> enable_safigdata >> [
        upload_to_safigdata, do_nothing_safigdata]
    [export_blacklist, export_consents_unsub,
     export_consents_sub_squadata] >> upload_to_squadata

    # full export

    check_time_squadata >> [
        export_generic_full_part_squadata, do_nothing_squadata]
    export_generic_full_part_squadata >> upload_to_squadata_full

    check_time_cloudmedia >> [
        export_generic_full_part_cloudmedia, do_nothing_cloudmedia]

    export_generic_full_part_cloudmedia >> [upload_to_cloudmedia_full, upload_to_cloudmedia]

    check_full_user_info >> [
        export_consents_user_info_webrivage, do_nothing_full_user_info]

    export_consents_user_info_webrivage >> upload_profile_info_to_webrivage

    # ----------------------------------------------------------------------------
    # Ownpage: daily export engaged sub  profiles to ownpage
    # ----------------------------------------------------------------------------
    ownpage_recette_profile = ["'" + profile + "'" for profile in config_ownpage['recette_profile']]
    today = datetime.utcnow().strftime('%Y-%m-%d')
    generate_actifs_sub_ownpage = BigQueryExecuteQueryOperator(
        task_id='generate_actifs_sub_ownpage',
        sql='ownpage/ownpage_export_actifs.sql'
    )
    generate_actifs_sub_ownpage.doc_md = 'generate actifs profiles to Ownpage'

    export_actifs_to_ownpage = BigQueryToGCSOperator(
        task_id='export_actifs_to_ownpage',
        source_project_dataset_table='export_partner.ownpage_actifs',
        destination_cloud_storage_uris=[
            'gs://{sftp_uri}/home/<USER>/ownpage/exports/{today}/sub_voici_pause_nl_actifs_{today}.csv'.format(
                sftp_uri=buckets['sftp'],
                today=today)],
        field_delimiter=';',
        export_format='CSV',
        force_rerun=True
    )
    export_actifs_to_ownpage.doc_md = 'Export csv sub to sftp ownpage'

    compress_actifs_ownpage_file = GCSZipFileOperator(
        task_id='compress_actifs_ownpage_file',
        bucket=buckets['sftp'],
        gcs_filename_path='home/uploads/ownpage/exports/{today}/sub_voici_pause_nl_actifs_{today}.csv'.format(
            sftp_uri=buckets['sftp'],
            today=today),
        gcs_delete_source_file=True,
        gcp_conn_id='gcs_sftp',
    )
    compress_actifs_ownpage_file.doc_md = 'Compress ownpage export actifs file to zip format and delete csv file'

    generate_actifs_sub_ownpage >> export_actifs_to_ownpage >> compress_actifs_ownpage_file

    # ----------------------------------------------------------------------------
    # Ownpage: daily export recette file to ownpage
    # ----------------------------------------------------------------------------
    if config_ownpage['enable_export_recette_file'].lower() == 'true':
        # export recette profile for ownpage
        export_recette_ownpage = BigQueryToGCSOperator(
            task_id='export_recette_ownpage',
            source_project_dataset_table='export_partner.ownpage_recette_liste',
            destination_cloud_storage_uris=[
                'gs://{sftp_uri}/home/<USER>/ownpage/exports/{today}/recette_voici_pause_nl_{today}.csv'.format(
                    sftp_uri=buckets['sftp'],
                    today=today)],
            field_delimiter=';',
            export_format='CSV',
            force_rerun=True
        )
        export_recette_ownpage.doc_md = 'Export csv recette sub profiles to sftp ownpage'

        compress_recette_ownpage_file = GCSZipFileOperator(
            task_id='compress_recette_ownpage_file',
            bucket=buckets['sftp'],
            gcs_filename_path='home/uploads/ownpage/exports/{today}/recette_voici_pause_nl_{today}.csv'.format(
                sftp_uri=buckets['sftp'],
                today=today),
            gcs_delete_source_file=True,
            gcp_conn_id='gcs_sftp',
        )
        compress_recette_ownpage_file.doc_md = 'Compress recette ownpage export file to zip format and delete csv file'

        generate_actifs_sub_ownpage >> export_recette_ownpage >> compress_recette_ownpage_file
