r"""
**PURPOSE:**
**LOT 1:**We detect events that are never applied in PEC.
If all consents in the consent_ids field of the payload are never applied in this case, 
the event will be deleted from the store.email_event. 
Otherwise, we modify the payload, keeping only the consents that are in PEC 
without checking whether the status in PEC is good or not.**LOT 2:**In lot 2, we detect events where the consent belongs to the profile 
but has a different status than the one in PEC. **INFO:**
We are ignoring events created in the last 30 days.

**COMMAND TO TEST:** 
- Commands for PREPROD ENV 
- gsutil cp dags/pec_plc_clean.py gs://europe-west1-mozart-cluster-2e910a39-bucket/dags/ 
- gsutil -m cp -R data/sql/pec_plc/*  gs://europe-west1-mozart-cluster-2e910a39-bucket/data/sql/pec_plc/
"""
import os
from datetime import timedelta, datetime

from airflow.models import Variable
from airflow.providers.common.sql.sensors.sql import SqlSensor
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.utils.task_group import TaskGroup
from bq_plugin.sensors.bigquery_sql_sensor import BigQuerySqlSensor
from collapsible_doc_dag import CDocDAG
from airflow.operators.empty import EmptyOperator


# -----------------------------------------------
buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']
env = os.environ.get("ENV")

bq_project = f'pm-{env}-matrix'
str_env = '' if env == 'prod' else '_' + env
dag_name = f'pec_plc_clean{str_env}'
email_on_failure = True if env == 'prod' else False
gcs_uri = 'gs://it-data-prod-matrix-pipeline/' if env == 'prod' else 'gs://it-data-preprod-matrix-preprod-pipeline/'
# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2025, 3, 1, 9, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=2),
    'sla': None,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=1),
    'depends_on_past': False,
    'bucket': matrix_bucket,
    # GCP CONNECTION
    'gcp_conn_id': 'gcs_matrix',
    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_TRUNCATE',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',
    'allow_jagged_rows': True,
    'quote_character': '',
}

pec_plc_clean = Variable.get("pec_plc_clean", deserialize_json=True)
interval_days = pec_plc_clean['interval_days']
is_test_run = pec_plc_clean['is_test_run']

with (CDocDAG(
        dag_name,
        description='pec plc clean process',
        doc_md=__doc__,
        schedule_interval="15 9 * * MON",
        default_args=default_args,
        tags=["pec_plc"],
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=6),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/pec_plc/']) as dag):
    sensor_email_event_backup_dags = SqlSensor(
        task_id='sensor_email_event_backup_dags',
        conn_id='airflow_db',
        timeout=20 * 60,
        poke_interval=5 * 60,
        retries=3,
        mode='reschedule',
        soft_fail=True,
        sql="""
            SELECT CASE WHEN COUNT(*) > 0 THEN FALSE ELSE TRUE END
            FROM dag_run
            WHERE dag_id = 'matrix__backup_from_psql{str_env}'
            AND state = 'running'
        """.format(str_env=str_env)
    )
    sensor_email_event_backup_dags.doc_md = 'check running email_event backup dags !'

    init_tables = BigQueryExecuteQueryOperator(
        task_id='init_tables',
        sql='0_init_tables.sql',
        params={
            'interval_days': interval_days,
            'is_test_run': is_test_run
        },
    )
    init_tables.doc_md = 'init_tables'

    prepare_profiles_email_all_consents = BigQueryExecuteQueryOperator(
        task_id='prepare_profiles_email_all_consents',
        sql='1_get_profiles_email_all_consents.sql',
        params={
            'interval_days': interval_days,
            'is_test_run': is_test_run
        },
    )
    prepare_profiles_email_all_consents.doc_md = 'prepare profiles email all consents'

    with TaskGroup("pec_plc_lot1") as pec_plc_lot1:
        prepare_lot1_events = BigQueryExecuteQueryOperator(
            task_id='prepare_lot1_events',
            sql='2_prepare_events_lot1.sql',
            params={
                'interval_days': interval_days,
                'is_test_run': is_test_run
            },
        )
        prepare_lot1_events.doc_md = 'prepare lot1 events'

        pre_clean_events_lot1 = BigQueryExecuteQueryOperator(
            task_id='pre_clean_events_lot1',
            sql='3_preclean_events_lot1.sql',
            params={
                'interval_days': interval_days,
                'is_test_run': is_test_run
            },
        )
        pre_clean_events_lot1.doc_md = 'Execute pre cleanup events lot 1'
        prepare_lot1_events >> pre_clean_events_lot1

        if is_test_run != 'true':
            clean_events_lot1 = BigQueryExecuteQueryOperator(
                task_id='clean_events_lot1',
                sql='4_clean_events_lot1.sql',
                params={
                    'interval_days': interval_days,
                    'is_test_run': is_test_run
                },
            )
            clean_events_lot1.doc_md = 'Execute cleanup events lot 2'
            pre_clean_events_lot1 >> clean_events_lot1

    with TaskGroup("pec_plc_lot2") as pec_plc_lot2:
        prepare_lot2_events = BigQueryExecuteQueryOperator(
            task_id='prepare_lot2_events',
            sql='5_prepare_events_lot2.sql',
            params={
                'interval_days': interval_days,
                'is_test_run': is_test_run
            },
        )
        prepare_lot2_events.doc_md = 'prepare lot2 events'

        pre_clean_events_lot2 = BigQueryExecuteQueryOperator(
            task_id='pre_clean_events_lot2',
            sql='6_preclean_events_lot2.sql',
            params={
                'interval_days': interval_days,
                'is_test_run': is_test_run
            },
        )
        pre_clean_events_lot2.doc_md = 'Execute cleanup events lot 2'
        prepare_lot2_events >> pre_clean_events_lot2

        if is_test_run != 'true':
            clean_events_lot2 = BigQueryExecuteQueryOperator(
                task_id='clean_events_lot2',
                sql='7_clean_events_lot2.sql',
                params={
                    'interval_days': interval_days,
                    'is_test_run': is_test_run
                },
            )
            clean_events_lot2.doc_md = 'Execute cleanup events lot 2'
            pre_clean_events_lot2 >> clean_events_lot2
    next_step = EmptyOperator(task_id='next_step')

    sensor_email_event_backup_dags >> init_tables >>  next_step
    sensor_email_event_backup_dags >> prepare_profiles_email_all_consents >> next_step

    next_step >> pec_plc_lot1 >> pec_plc_lot2