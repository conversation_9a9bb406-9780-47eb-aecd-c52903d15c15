r"""
# Personalized NL Monitoring

**PURPOSE:**
**This dag is used to:**- Check the status of close to send campaigns and send alerts if needed,
**in following situations:**- If the campaign is not yet prepared.
- If the campaign is being prepared but not finished.

**METHODOLOGY:**
"""
import os
import json
import logging
from datetime import timedel<PERSON>, datetime
from jinja2 import Template
from airflow.models import Variable
from airflow.operators.empty import EmptyOperator
from airflow.decorators import task
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from psql_plugin.operators.psql_select_many_operator import PostgresSelectManyOperator
from collapsible_doc_dag import CDocDAG
from airflow.utils.email import send_email


# ----------------- CONFIG ----------------------
ENV = os.environ.get("ENV")
IS_PROD = ENV == 'prod'
BQ_PROJECT = 'pm-{}-matrix'.format(ENV)
# Email Configuration
EMAIL_SUBJECT = "[Personalized NL] - Des campaigns non préparés"
HTML_TEMPLATE_PATH = "/home/<USER>/gcs/data/templates/personalized_nl/unprepared_campaigns_alert.html"

dag_name = 'personalized_nl_monitoring'
email_on_failure = True

if ENV != 'prod':
    dag_name = dag_name + '_' + ENV
    email_on_failure = False

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2025, 3, 22, 2, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': False,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=120),
    'sla': None,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=5),
    'depends_on_past': False,

    # BQ CONNECTION
    'gcp_conn_id': 'bq_matrix',
    'use_legacy_sql': False,

    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE'  # possibles values: INTERACTIVE and BATCH.
}

schedule_interval = "*/10 5-20 * * *"
if ENV != 'prod':
    schedule_interval = "0 7-18 * * *"

with CDocDAG(
        dag_name,
        description='Prepare personalized NL Campaign',
        tags=["nl", "personalized-nl", "monitoring"],
        doc_md=__doc__,
        schedule_interval=schedule_interval,
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=1),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/personalized_nl/monitoring/']
) as dag:
    task_doc_md = """
    Get campaigns newly created but not yet prepared <br />
    OR <br />
    campaigns that are being prepared but not yet finished
    """
    get_unprepared_campaigns_task = PostgresSelectManyOperator(
        task_id='get_unprepared_campaigns',
        postgres_conn_id='psql_personalized_nl_app',
        sql="get_unprepared_campaigns.sql",
    )
    get_unprepared_campaigns_task.doc_md = task_doc_md

    @task.branch
    def has_campaigns(**kwargs):
        ti = kwargs['ti']
        result = ti.xcom_pull(task_ids='get_unprepared_campaigns')
        rows = json.loads(result)
        if rows:
            campaign_ids = [
                row[0]
                for row in rows
            ]
            logging.info(campaign_ids)
            ti.xcom_push(key='campaign_ids', value=campaign_ids)
            return 'send_alert'

        return 'skip_email'


    @task
    def send_alert(**kwargs):
        ti = kwargs['ti']
        result = ti.xcom_pull(task_ids='get_unprepared_campaigns')
        rows = json.loads(result)
        logging.info(rows)
        campaigns = [
            {"id": row[0], "rogue_one_email_id": row[1], "status": row[2], "shoot_date": row[3], "email_status": row[4], "total_profiles": row[5]}
            for row in rows
        ]
        with open(HTML_TEMPLATE_PATH, "r") as file:
            template_content = file.read()
        # Render the HTML with Jinja
        template = Template(template_content)
        body = template.render(campaigns=campaigns, env=ENV)
        alert_cc = ['<EMAIL>']
        alert_to = ['<EMAIL>']

        if ENV != 'prod':
            alert_to = ['<EMAIL>']
            alert_cc = []
        if rows:
            send_email(to=alert_to, subject=EMAIL_SUBJECT, html_content=body, cc=alert_cc, mime_charset='utf-8',
                   conn_id='sendgrid_default')

    task_doc_md = """
    Save sent alert in order to not send it too frequently <br /> 
    """
    save_alert = SQLExecuteQueryOperator(
        task_id='save_alert',
        conn_id='psql_personalized_nl_app',
        sql="save_sent_alert.sql",
        dag=dag,
    )
    save_alert.doc_md = task_doc_md

    skip_email_task = EmptyOperator(task_id='skip_email')

    send_alert_task = send_alert()

    get_unprepared_campaigns_task >> has_campaigns() >> [send_alert_task, skip_email_task]
    send_alert_task >> save_alert