r"""
**PURPOSE:**
This dag is used to retrieve the scores and signal spams from J-1 from Splio SFTP and save it into bigquery.
The scores are uploaded to sftp by splio around 23:30 each day and we retrieve them at 00:00 am of following day

**METHODOLOGY:**

We store gcs THEN we store in big query : import dataset
-import.prisma_senderscores_scores_(dateOfDagRun ds_nodash)
-import.prisma_senderscores_signalspams_(dateOfDagRun ds_nodash)

then we upload to store_partner :
- store_partner.splio_signalspams (scores day J-1)
- store_partner.splio_scores (scores day J-1).

Remenber : ds in airflow is already yersterday.

- Preprod: 
gsutil -m cp -R dags/splio_scores_import.py gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/
gsutil -m cp -R data/sql/splio_scores/* gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/splio_scores/
gsutil -m cp -R plugins/sftp_plugin/operators/bulk_sftp_to_gcs_operator.py gs://europe-west1-preprod-mozart-2fa49086-bucket/plugins/sftp_plugin/operators/

Prod: 
gsutil -m cp -R dags/splio_scores_import.py gs://europe-west1-prod-mozart-co-2ef7e904-bucket/dags/
gsutil rm gs://europe-west1-prod-mozart-co-2ef7e904-bucket/dags/splio_scores_import.py
gsutil -m cp -R data/sql/splio_scores/* gs://europe-west1-prod-mozart-co-2ef7e904-bucket/data/sql/splio_scores/

**HOW TO:**

- If you relaunch this dag take care you may delete the scores J-1. 
- Make sure you save this scores before relaunch the dag (you might truncate it)
"""

import os
from datetime import timedelta, datetime

from airflow.models import Variable
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.google.cloud.transfers.gcs_to_bigquery import GCSToBigQueryOperator
from collapsible_doc_dag import CDocDAG
from sftp_plugin.operators.bulk_sftp_to_gcs_operator import BulkSftpToGoogleCloudStorageOperator

env = os.environ.get("ENV")
bq_project = 'pm-{}-matrix'.format(env)
dag_name = 'splio_complaints_import'

# Create variables for the load from prisma sftp to gcs bucket
conn_id_list = {f"sftp_splio_prisma_senderscore": f""}
files_prefix = 'sftp_splio_prisma_senderscore'
buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']
day = '{{ ds_nodash  }}'
gcs_scores_path = 'splio/prisma_senderscore/scores/{}'.format(day)
sftp_scores_file = 'report_prisma_senderscore_{}.csv'.format(day)
gcs_signalspam_path = 'splio/prisma_senderscore/signalspam/{}'.format(day)
sftp_signals_file = 'report_prisma_signalspam_{}.csv'.format(day)

# Create schema for the load from gcs bucket to big query import dataset
scores_schema = [
    {"mode": "NULLABLE", "name": "day", "type": "STRING"},
    {"mode": "NULLABLE", "name": "ip", "type": "STRING"},
    {"mode": "NULLABLE", "name": "recipients", "type": "STRING"},
    {"mode": "NULLABLE", "name": "complaints", "type": "STRING"},
    {"mode": "NULLABLE", "name": "spamtraps", "type": "STRING"},
    {"mode": "NULLABLE", "name": "color", "type": "STRING"},
    {"mode": "NULLABLE", "name": "senderscore", "type": "STRING"}
]

signalspams_schema = [
    {"mode": "NULLABLE", "name": "day", "type": "STRING"},
    {"mode": "NULLABLE", "name": "ip", "type": "STRING"},
    {"mode": "NULLABLE", "name": "isp", "type": "STRING"},
    {"mode": "NULLABLE", "name": "complaints", "type": "STRING"}
]

email_on_failure = True
# ------------- FOR PREPROD ENV -----------------

if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False

# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2024, 4, 3, 00, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=30),
    'sla': None,
    'email_on_retry': False,
    # no need to retries, if no file found on gcs , boom direct
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
    'depends_on_past': False,

    # GCP CONNECTION
    'gcp_conn_id': 'gcs_matrix',
    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_APPEND',
    # possibles values: WRITE_DISPOSITION_UNSPECIFIED, WRITE_EMPTY, WRITE_TRUNCATE OR WRITE_APPEND (default: 'WRITE_EMPTY')
    'create_disposition': 'CREATE_IF_NEEDED',
    # possibles values: CREATE_DISPOSITION_UNSPECIFIED , CREATE_NEVER OR CREATE_IF_NEEDED (default: 'CREATE_IF_NEEDED')
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',  # possibles values: INTERACTIVE and BATCH.

}

# -------------------------------------------
# DAG
# -------------------------------------------
with CDocDAG(
        dag_name,
        schedule_interval="0 0 * * *",
        tags=["splio"],
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(minutes=50),
        catchup=False,
        description='Retrieve the daily Splio Scores and signals of spam',
        doc_md=__doc__,
        template_searchpath=['/home/<USER>/gcs/data/sql/splio_scores/']
) as dag1:
    task_doc_md = '''
    Take from sftp the scores for the previous day and save it into GCS<br />
    '''

    download_scores_files = BulkSftpToGoogleCloudStorageOperator(
        task_id='download_scores_files',
        sftp_conn_list=conn_id_list,
        sftp_filename=sftp_scores_file + '*',
        sftp_isfilepattern=True,
        gcs_conn_id='gcs_matrix',
        gcs_bucket=buckets['matrix'],
        gcs_folder=gcs_scores_path,
        gcs_filename=sftp_scores_file
    )

    download_scores_files.doc_md = task_doc_md

    task_doc_md = """
    Importing splio scores in BQ import table
    """
    import_scores_to_bq = GCSToBigQueryOperator(
        task_id='import_scores_to_bq',
        bucket=buckets['matrix'],
        source_objects=['{}/{}-{}'.format(gcs_scores_path, files_prefix, sftp_scores_file)],
        destination_project_dataset_table='{}:import.prisma_senderscores_scores_{}'.format(bq_project, day),
        source_format='CSV',
        field_delimiter=',',
        skip_leading_rows=1,
        autodetect=True,
        compression='gzip',
        max_bad_records=0,
        schema_fields=scores_schema,
        write_disposition='WRITE_TRUNCATE',
        dag=dag1
    )
    import_scores_to_bq.doc_md = task_doc_md

    task_doc_md = """
    Import to store partner the scores in BQ import table.
    """
    scores_import_to_store = BigQueryExecuteQueryOperator(
        task_id='scores_import_to_store',
        sql='scores_import_to_store.sql',
        write_disposition='WRITE_APPEND',
        params={
            'bq_project': bq_project
        }
    )
    scores_import_to_store.doc_md = task_doc_md

    task_doc_md = '''
    Take from sftp the signals of spam for the previous day and save it into GCS<br />
    '''
    download_signalspam_files = BulkSftpToGoogleCloudStorageOperator(
        task_id='download_signalspam_files',
        # sftp
        sftp_conn_list=conn_id_list,
        sftp_filename=sftp_signals_file + '*',
        sftp_isfilepattern=True,
        gcs_conn_id='gcs_matrix',
        gcs_bucket=buckets['matrix'],
        gcs_folder=gcs_signalspam_path,
        gcs_filename=sftp_signals_file
    )
    download_signalspam_files.doc_md = task_doc_md

    task_doc_md = """
    Importing in BQ  the spams signal of yesterday
    """
    import_signalspams_to_bq = GCSToBigQueryOperator(
        task_id='import_signalspams_to_bq',
        bucket=buckets['matrix'],
        source_objects=['{}/{}-{}'.format(gcs_signalspam_path, files_prefix, sftp_signals_file)],
        destination_project_dataset_table='{}:import.prisma_senderscores_signalspams_{}'.format(bq_project, day),
        source_format='CSV',
        field_delimiter=',',
        skip_leading_rows=1,
        autodetect=True,
        compression='gzip',
        max_bad_records=0,
        schema_fields=signalspams_schema,
        write_disposition='WRITE_TRUNCATE',
        dag=dag1
    )
    import_signalspams_to_bq.doc_md = task_doc_md

    task_doc_md = """
    Import to store partner the signalspams in BQ import table.
    """
    signalspams_import_to_store = BigQueryExecuteQueryOperator(
        task_id='signalspams_import_to_store',
        sql='signalspams_import_to_store.sql',
        write_disposition='WRITE_APPEND',
        params={
            'bq_project': bq_project
        }
    )
    signalspams_import_to_store.doc_md = task_doc_md

    (download_signalspam_files >> import_signalspams_to_bq >> signalspams_import_to_store)

    (download_scores_files >> import_scores_to_bq >> scores_import_to_store)
