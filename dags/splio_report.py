r"""
**PURPOSE:**

This daily DAG call Splio API report for each universes see : here  
Also & independently, synchronise rogue-one email consent from PostgreSQL to Biquery

**METHODOLOGY:**

For each Splio universes, call API with period params (return JSON value). 
For each Splio sendouts (from API return), grab & save (to GCS) template email sent to recover rogue-one id from HTML source 
All Splio responses are injected into PostgreSQL temporary table (matrix _ _ email_tmp.splio_report_import) 
The real tables are supplied (matrix _ _ email_splio.{report_data,report,report_link}) from temporary table created 
All PostgreSQL tables are synchronized to BQ query dataset  : store_tracking

This DAG is composed of 3 parts  :

- One operator responsible for calls API, grab rogue-one id, inserted to PosgreSQL temporary table
- Building data part, to load data from temporary table into real tables (PostgreSQL)
- Sync PostgreSQL to BigQuery data

**HOW TO:**

- To restart DAG if failed, just clear DAG from airflow the desired date 
- To launch over a long period, prefer the DAG splio_report_api_import.py. You can chosse period from airflow variables
"""
from datetime import datetime, timed<PERSON>ta

from airflow.models import Variable
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from bq_plugin.operators.psql_to_bigquery_operator import PostgresToBigQueryOperator
from collapsible_doc_dag import CDocDAG
from import_plugin.operators.splio_reports_import_operator import SplioReportsImportOperator

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2020, 6, 8, 0, 0, 0),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': True,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=120),
    'sla': None,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=1),
    'depends_on_past': False,

    ## PSQL Connection
    'postgres_conn_id': 'psql_matrix_email_app',

    ## GCS Connection
    'gcs_conn_id': 'gcs_matrix',

    ## BQ Connection
    'bigquery_conn_id': 'bq_matrix',
    'gcp_conn_id': 'bq_matrix',
}

#### Universes actives ####
# in case we lost it : splio_report_campaign
# {"__comment__": "This daily DAG call Splio API report for each universes.","rogue_one_theme": {"full_export": "False"}, "dag_universes_to_include": ["*"], "dag_universes_to_exclude": ["prisma_template"]}

dag_variable = Variable.get("splio_report_campaign", deserialize_json=True)

# select valid universe used for the dag
splio_universes = dag_variable["active_universes"]

rogue_one_theme = dag_variable["rogue_one_theme"]

####

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)

splio_params = {
    'key': '7af061af0f7af8d2bf4542e00b2f41eb8a844494',
    'channel': 'email',
    'after': '{date_from}',
    'before': '{date_to}',
    'universe': '{univers}',
}

with CDocDAG("splio_report_campaign",
             schedule_interval="0 0 * * *",
             catchup=False,
             description='Call Splio API report',
             tags=["splio", "lop"],
             doc_md=__doc__,
             max_active_runs=1,
             template_searchpath=['/home/<USER>/gcs/data/sql/'],
             # template_searchpath=['/usr/local/airflow/sql/tracking_splio/reports/'],
             dagrun_timeout=timedelta(hours=2),
             default_args=default_args) as dag:
    task_doc_md = '''
    Delete old reports > 7 days and create temporary table <br />
    '''
    prepare_import = SQLExecuteQueryOperator(
        task_id='prepare_import',
        sql='tracking/splio/reports/sql/0_prepare_import.sql',
        conn_id='psql_matrix_email_app',
        database='matrix'
    )
    prepare_import.doc_md = task_doc_md

    task_doc_md = '''
    Call Splio Api, grab rogue-one id & save it to GCS, load data into PostgreSQL <br />
    '''
    call_splio_api = SplioReportsImportOperator(
        task_id='call_api',
        universes=splio_universes,
        splio_connection=splio_params,
        before_date='{{ next_ds }}',
        after_date='{{ macros.ds_add(ds, -4) }}',
        postgres_conn_id='psql_matrix_email_app',
        database='matrix',
        gcs_conn_id='gcs_matrix',
        bucket=buckets['matrix']
    )
    call_splio_api.doc_md = task_doc_md

    task_doc_md = '''
    Dispatch data from temporary table into real table <br />
    '''
    build_data_sql = SQLExecuteQueryOperator(
        task_id='build_data_sql',
        sql='tracking/splio/reports/sql/1_build_reports_data.sql',
        conn_id='psql_matrix_email_app',
        database='matrix'
    )
    build_data_sql.doc_md = task_doc_md

    # Export PostgreSQL tables : report, report_data & report_link to BQ
    report_schemas = [{"mode": "REQUIRED", "name": "id", "type": "INTEGER"},
                      {"mode": "REQUIRED", "name": "sendout_id", "type": "STRING"},
                      {"mode": "REQUIRED", "name": "campaign_id", "type": "STRING"},
                      {"mode": "REQUIRED", "name": "message_id", "type": "STRING"},
                      {"mode": "NULLABLE", "name": "rogue_one_email_id", "type": "STRING"},
                      {"mode": "REQUIRED", "name": "starttime", "type": "TIMESTAMP"},
                      {"mode": "REQUIRED", "name": "endtime", "type": "TIMESTAMP"},
                      {"mode": "REQUIRED", "name": "create_date", "type": "TIMESTAMP"},
                      {"mode": "REQUIRED", "name": "update_date", "type": "TIMESTAMP"},
                      {"mode": "NULLABLE", "name": "targets", "type": "INTEGER"},
                      {"mode": "NULLABLE", "name": "sent", "type": "INTEGER"},
                      {"mode": "NULLABLE", "name": "delivered", "type": "INTEGER"},
                      {"mode": "NULLABLE", "name": "soft_bounce", "type": "INTEGER"},
                      {"mode": "NULLABLE", "name": "hard_bounces", "type": "INTEGER"},
                      {"mode": "NULLABLE", "name": "clickers", "type": "INTEGER"},
                      {"mode": "NULLABLE", "name": "openers", "type": "INTEGER"},
                      {"mode": "NULLABLE", "name": "opens", "type": "INTEGER"},
                      {"mode": "NULLABLE", "name": "clicks", "type": "INTEGER"},
                      {"mode": "NULLABLE", "name": "spam_complaints", "type": "INTEGER"},
                      {"mode": "NULLABLE", "name": "unsubscribes", "type": "INTEGER"},
                      {"mode": "REQUIRED", "name": "universe_id", "type": "INTEGER"}]
    task_doc_md = '''
    Export PostgreSQL table : report into BQ <br />
    '''
    import_report_to_bq = PostgresToBigQueryOperator(
        task_id='import_report_to_bq',
        database='matrix',
        table='matrix__email_splio.report',
        bucket=buckets['matrix'],
        source_object='splio/campaign_stats/exports/export_tracking_splio_report_{{ ds_nodash }}.csv',
        destination_project_dataset_table='pm-prod-matrix:import.tracking_splio_report_{{ ds_nodash }}',
        schema_fields=report_schemas,
        field_delimiter='\t',
        quote_character='',
        allow_quoted_newlines=True,
        write_disposition='WRITE_TRUNCATE',
    )
    import_report_to_bq.doc_md = task_doc_md

    report_data_schemas = [{"mode": "REQUIRED", "name": "report_id", "type": "INTEGER"},
                           {"mode": "REQUIRED", "name": "data", "type": "STRING"}]
    task_doc_md = '''
    Export PostgreSQL table : report_data into BQ <br />
    '''
    import_report_data_to_bq = PostgresToBigQueryOperator(
        task_id='import_report_data_to_bq',
        database='matrix',
        table='matrix__email_splio.report_data',
        bucket=buckets['matrix'],
        source_object='splio/campaign_stats/exports/export_tracking_splio_report_data_{{ ds_nodash }}.csv',
        destination_project_dataset_table='pm-prod-matrix:import.tracking_splio_report_data_{{ ds_nodash }}',
        schema_fields=report_data_schemas,
        field_delimiter='\t',
        quote_character='',
        allow_quoted_newlines=True,
        write_disposition='WRITE_TRUNCATE',
    )
    import_report_data_to_bq.doc_md = task_doc_md

    report_link_schemas = [
        {"mode": "REQUIRED", "name": "report_id", "type": "INTEGER"},
        {"mode": "REQUIRED", "name": "link_id", "type": "INTEGER"},
        {"mode": "NULLABLE", "name": "clicks", "type": "INTEGER"},
        {"mode": "REQUIRED", "name": "url", "type": "STRING"},
        {"mode": "NULLABLE", "name": "name", "type": "STRING"},
        {"mode": "NULLABLE", "name": "category", "type": "STRING"}]
    task_doc_md = '''
    Export PostgreSQL table : report_link into BQ <br />
    '''
    import_report_link_to_bq = PostgresToBigQueryOperator(
        task_id='import_report_link_to_bq',
        database='matrix',
        table='matrix__email_splio.report_link',
        bucket=buckets['matrix'],
        source_object='splio/campaign_stats/exports/export_tracking_splio_report_link_{{ ds_nodash }}.csv',
        destination_project_dataset_table='pm-prod-matrix:import.tracking_splio_report_link_{{ ds_nodash }}',
        schema_fields=report_link_schemas,
        field_delimiter='\t',
        quote_character='',
        allow_quoted_newlines=True,
        write_disposition='WRITE_TRUNCATE',
    )
    import_report_link_to_bq.doc_md = task_doc_md

    task_doc_md = '''
    Merge all imported tables into prepare from BQ <br />
    '''
    prepare_bq_data = BigQueryExecuteQueryOperator(
        task_id='prepare_bq_data',
        use_legacy_sql=False,
        sql='tracking/splio/reports/sql/2_prepare_reports.sql')
    prepare_bq_data.doc_md = task_doc_md

    task_doc_md = '''
    Update store of data from prepare (BQ) 
    <br />
    '''
    mep_bq_data = BigQueryExecuteQueryOperator(
        task_id='mep_bq_data',
        use_legacy_sql=False,
        sql='tracking/splio/reports/sql/3_mep_reports.sql')
    mep_bq_data.doc_md = task_doc_md

    # export rogue_one_email_consent
    task_doc_md = '''
    Create temporary & filled table matrix__email_tmp.rogue_one_email_consent<br />
    '''
    export_rogue_one_email_consent = SQLExecuteQueryOperator(
        task_id='export_rogue_one_email_consent',
        sql='rogue_one/email_consent/sql/0_export_rogue_one_email_consent.sql',
        conn_id='psql_matrix_email_app',
        database='matrix')
    export_rogue_one_email_consent.doc_md = task_doc_md

    # export rogue_one_theme for NL shopping
    task_doc_md = '''
    Create temporary & filled table matrix__email_tmp.rogue_one_theme<br />
    '''
    export_rogue_one_theme = SQLExecuteQueryOperator(
        task_id='export_rogue_one_theme',
        sql='rogue_one/theme/sql/0_export_rogue_one_theme.sql',
        conn_id='psql_matrix_email_app',
        database='matrix',
        params={
            "full_export": rogue_one_theme["full_export"]
        })
    export_rogue_one_theme.doc_md = task_doc_md

    rogue_one_email_consent_schemas = [
        {"mode": "REQUIRED", "name": "rogue_one_shoot_date", "type": "TIMESTAMP"},
        {"mode": "REQUIRED", "name": "rogue_one_email_id", "type": "INTEGER"},
        {"mode": "REQUIRED", "name": "email_consent_id", "type": "INTEGER"}]
    task_doc_md = '''
    Import postgreSQL temporary table : matrix__email_tmp.rogue_one_email_consent into BQ<br />
    '''
    import_bq_rogue_one_email_consent = PostgresToBigQueryOperator(
        task_id='import_bq_rogue_one_email_consent',
        database='matrix',
        table='matrix__email_tmp.rogue_one_email_consent_{{ ds_nodash }}',
        bucket=buckets['matrix'],
        source_object='rogue_one/email_consent/export/export_rogue_one_email_consent_{{ ds_nodash }}.csv',
        destination_project_dataset_table='pm-prod-matrix:import.rogue_one_email_consent_{{ ds_nodash }}',
        schema_fields=rogue_one_email_consent_schemas,
        field_delimiter='\t',
        quote_character='',
        allow_quoted_newlines=True,
        write_disposition='WRITE_TRUNCATE',
    )
    import_bq_rogue_one_email_consent.doc_md = task_doc_md

    rogue_one_theme_schemas = [
        {"mode": "REQUIRED", "name": "rogue_one_shoot_date", "type": "TIMESTAMP"},
        {"mode": "REQUIRED", "name": "rogue_one_email_id", "type": "INTEGER"},
        {"mode": "REQUIRED", "name": "theme", "type": "STRING"}]
    task_doc_md = '''
    Import postgreSQL temporary table : matrix__email_tmp.rogue_one_theme into BQ<br />
    '''
    import_bq_rogue_one_theme = PostgresToBigQueryOperator(
        task_id='import_bq_rogue_one_theme',
        database='matrix',
        table='matrix__email_tmp.rogue_one_theme_{{ ds_nodash }}',
        bucket=buckets['matrix'],
        source_object='rogue_one/theme/export/export_rogue_one_theme_{{ ds_nodash }}.csv',
        destination_project_dataset_table='pm-prod-matrix:import.rogue_one_theme_{{ ds_nodash }}',
        schema_fields=rogue_one_theme_schemas,
        field_delimiter='\t',
        quote_character='',
        allow_quoted_newlines=True,
        write_disposition='WRITE_TRUNCATE',
    )
    import_bq_rogue_one_theme.doc_md = task_doc_md

    task_doc_md = '''
    Dump data from import dataset into prepare <br />
    '''
    prepare_bq_rogue_one_email_consent = BigQueryExecuteQueryOperator(
        task_id='prepare_bq_rogue_one_email_consent_data',
        use_legacy_sql=False,
        sql='rogue_one/email_consent/sql/1_prepare_rogue_one_email_consent.sql')
    prepare_bq_rogue_one_email_consent.doc_md = task_doc_md

    task_doc_md = '''
    Dump data from import dataset into prepare <br />
    '''
    prepare_bq_rogue_one_theme = BigQueryExecuteQueryOperator(
        task_id='prepare_bq_rogue_one_theme_data',
        use_legacy_sql=False,
        sql='rogue_one/theme/sql/1_prepare_rogue_one_theme.sql')
    prepare_bq_rogue_one_theme.doc_md = task_doc_md

    task_doc_md = '''
    Dump data from prepare dataset into store <br />
    '''
    mep_bq_rogue_one_email_consent = BigQueryExecuteQueryOperator(
        task_id='mep_bq_rogue_one_email_consent',
        use_legacy_sql=False,
        sql='rogue_one/email_consent/sql/2_mep_rogue_one_email_consent.sql')
    mep_bq_rogue_one_email_consent.doc_md = task_doc_md

    task_doc_md = '''
    Dump data from prepare dataset into store <br />
    '''
    mep_bq_rogue_one_theme = BigQueryExecuteQueryOperator(
        task_id='mep_bq_rogue_one_theme',
        use_legacy_sql=False,
        sql='rogue_one/theme/sql/2_mep_rogue_one_theme.sql')
    mep_bq_rogue_one_theme.doc_md = task_doc_md

    clean_tmp_table_query = """
        DROP TABLE IF EXISTS matrix__email_tmp.rogue_one_email_consent_{{ ds_nodash }}; 
        DROP TABLE IF EXISTS matrix__email_tmp.rogue_one_theme_{{ ds_nodash }}; 
    """
    task_doc_md = '''
        Delete temporary table matrix__email_tmp.rogue_one_*_{{ ds_nodash }}<br />
    '''
    clean_tmp_table_query = SQLExecuteQueryOperator(
        task_id='clean_tmp_table_query',
        sql=clean_tmp_table_query,
        database='matrix',
        conn_id='psql_matrix_email_app',
    )
    clean_tmp_table_query.doc_md = task_doc_md

    prepare_import \
        >> call_splio_api \
        >> build_data_sql \
        >> [import_report_to_bq, import_report_data_to_bq, import_report_link_to_bq] \
        >> prepare_bq_data \
        >> mep_bq_data

    export_rogue_one_email_consent \
        >> import_bq_rogue_one_email_consent \
        >> prepare_bq_rogue_one_email_consent \
        >> mep_bq_rogue_one_email_consent     

    export_rogue_one_theme \
        >> import_bq_rogue_one_theme \
        >> prepare_bq_rogue_one_theme \
        >> mep_bq_rogue_one_theme

    [prepare_bq_rogue_one_email_consent, prepare_bq_rogue_one_theme] >> clean_tmp_table_query
