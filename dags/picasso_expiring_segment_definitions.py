r"""
# Picasso Expiring Segment Definitions Management DAG

**PURPOSE:**

This DAG automates the lifecycle management of segmentation definitions within the Picasso platform. It ensures that segment definitions that are nearing expiration or have already expired are processed accordingly to maintain data integrity and operational efficiency.**The DAG encompasses the following processes:**1. **Alerting on Expiring Segments**: Identifies segment definitions approaching their expiration and triggers alerts for proactive management.
2. **Archiving Expired Segments**: Updates the status of expired segment definitions to archive them within Picasso, preventing their use in future analyses or campaigns.
3. **Cleaning Up BigQuery Resources**: Removes BigQuery tables linked to segment definitions that are not marked as "PUBLISHED", freeing up resources and maintaining a clean data environment.

## Key Points

- **Daily Schedule**: Executes once daily to ensure timely management of segment definitions.
- **Integration with Picasso API**: Leverages custom API operations to interact with the Picasso platform, facilitating seamless status updates and data management.
- **Postgres and BigQuery Interaction**: Engages with both Postgres (for segment metadata) and BigQuery (for segment data tables), demonstrating a multi-service orchestration capability.
- **Custom Hooks and Operators**: Utilizes specialized hooks and operators designed for specific tasks within the Picasso ecosystem, enhancing the DAG's efficiency and specificity.

## Operational Details

- **Environment Sensitivity**: Configures its operation based on the `ENV` environment variable, allowing for flexible deployment across development and production environments.
- **Failure Alerts**: Configured to alert stakeholders via email in case of task failures, particularly in the production environment.
- **Dependency Management**: Ensures that all tasks are executed in a predefined order, maintaining logical sequence and data consistency.
"""
import json
import os
from datetime import datetime, timedelta
from collapsible_doc_dag import CDocDAG
from airflow.models import Variable
from segmentmanager_plugin.operators.picasso_api_operator import PicassoApiOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from psql_plugin.operators.psql_select_many_operator import PostgresSelectManyOperator
from airflow.operators.python import PythonOperator

# Environment and DAG Configuration
ENV = os.environ.get("ENV", 'dev')
IS_PROD = ENV == 'prod'
DAG_NAME = f'picasso_expiring_segment_definitions_{ENV}' if not IS_PROD else 'picasso_expiring_segment_definitions'
EMAIL_ON_FAILURE = IS_PROD
BQ_PROJECT_ID = f'pm-{ENV}-matrix'
TEMPLATE_PATH = '/home/<USER>/gcs/data/sql/segment_manager/expiring_segment_definition/'

# Database and API Configuration
POSTGRES_CONN_ID = 'psql_picasso_app'
MATRIX_BUCKET = Variable.get("gcs_bucket_names", deserialize_json=True)['matrix']
SNAPSHOT_FOLDER = 'picasso/backup_matrix/'


default_args = {
    'owner': 'airflow',
    'start_date': datetime(2024, 3, 1),
    'email_on_failure': IS_PROD,
    'log_response': True,
    'xcom_push': False,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=120),
    'sla': None,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=20),
    'depends_on_past': False,

    # PSQL Connection
    'conn_id': POSTGRES_CONN_ID,
    'postgres_conn_id': POSTGRES_CONN_ID,
    'database': 'matrix',
    'schema': 'picasso',

    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'gcp_conn_id': 'bq_matrix',
    'use_legacy_sql': False,

    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',
}

with CDocDAG(
        DAG_NAME,
        tags=["segment_manager"],
        default_args=default_args,
        doc_md=__doc__,
        description='Manage picasso expiring segment definitions',
        schedule_interval=timedelta(days=1),
        catchup=False,
        template_searchpath=[TEMPLATE_PATH]
) as dag:
    task_doc_md = """
    Check alert expiring segment definition via picasso api call
    """
    check_alert_expiring_segment_definitions = PicassoApiOperator(
        task_id='check_alert_expiring_segment_definitions',
        endpoint='expiry-alert',
        method='POST',
        data={},
        dag=dag,
    )
    check_alert_expiring_segment_definitions.doc_md = task_doc_md

    task_doc_md = """
    Change status of segment definition via picasso api
    """
    archive_expired_segment_definitions = PicassoApiOperator(
        task_id='archive_expired_segment_definitions',
        endpoint='archive-expired-segment-definitions',
        method='POST',
        data={},
        dag=dag,
    )
    archive_expired_segment_definitions.doc_md = task_doc_md

    def get_unpublished(**context):
        sql = """
            SELECT md.big_query_table
            FROM picasso.segment_meta_data AS md 
            JOIN picasso.segment_definition AS sd ON md.segment_definition_id = sd.id
            WHERE status != 'PUBLISHED';
            """
        get_values = PostgresSelectManyOperator(
            task_id='get_unpublished_tables',
            sql=sql,
            postgres_conn_id=POSTGRES_CONN_ID,
            database='matrix'
        )
        query_result = json.loads(get_values.execute(context))
        unpublished_tables = [result[0] for result in query_result] if query_result else []
        context['ti'].xcom_push(key='unpublished_tables', value=unpublished_tables)

    task_doc_md = """
    Remove all big query segments tables that are not published in picasso app.
    """
    remove_not_published_big_query_tables = BigQueryExecuteQueryOperator(
        task_id="remove_not_published_big_query_tables",
        sql="bq_delete_not_published_segments.sql",
        params={
            'bq_project': BQ_PROJECT_ID
        }
    )
    remove_not_published_big_query_tables.doc_md = task_doc_md


    unpublished_tables = PythonOperator(task_id='get_unpublished_tables', python_callable=get_unpublished)

    unpublished_tables >> remove_not_published_big_query_tables
