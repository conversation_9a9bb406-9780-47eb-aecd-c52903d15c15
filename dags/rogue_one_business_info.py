r"""
**PURPOSE:**

This daily DAG will extract daily rogue_one information as 'editor', 'plateform' AND 'advertiser' and then do the backup to BQ.

**METHODOLOGY:**

The DAG will load current date rogue_one email Ids with their informations. After that, it prepares data to be imported at a final table into BQ.**This DAG is composed of 2 parts:**- Building data part, to load data from temporary table into real tables (PostgreSQL)
- Sync PostgreSQL to BigQuery data

**HOW_TO:**

- To restart DAG if failed, just clear DAG from airflow the desired date
"""
# gsutil cp dags/rogue_one_business_info.py gs://europe-west1-mozart-cluster-2e910a39-bucket/dags/

import os
from datetime import datetime, timedelta

from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.models import Variable
from bq_plugin.operators.psql_to_bigquery_operator import PostgresToBigQueryOperator
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from collapsible_doc_dag import CDocDAG

dag_name = 'rogue_one_business_information'
env = os.environ.get("ENV")
bq_project = 'pm-{}-matrix'.format(env)
buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
rogue_one_business_information = Variable.get("rogue_one_business_information", deserialize_json=True)
if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2020, 6, 8, 2, 0, 0),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': True,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=120),
    'sla': None,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=1),
    'depends_on_past': False,
    # PSQL Connection
    'postgres_conn_id': 'psql_matrix_email_app',
    # GCS Connection
    'gcs_conn_id': 'gcs_matrix',
    # BQ Connection
    'bigquery_conn_id': 'bq_matrix',
    'gcp_conn_id': 'bq_matrix',
}

with CDocDAG(
        dag_name,
        schedule_interval="0 0 * * *",
        tags=["app-data"],
        catchup=False,
        description='Do rogue_one information backup from Postgres to BQ.',
        doc_md=__doc__,
        max_active_runs=1,
        template_searchpath=['/home/<USER>/gcs/data/sql/rogue_one/'],
        dagrun_timeout=timedelta(hours=2),
        default_args=default_args) as dag:
    # export rogue_one information AS 'editor', 'plateform' AND 'advertiser'
    task_doc_md = '''
        Create temporary & filled table matrix__email_tmp.rogue_one_theme<br />
        '''
    export_rogue_one_business_information = SQLExecuteQueryOperator(
        task_id='export_rogue_one_business_information',
        sql='information/sql/export_rogue_one_business_information.sql',
        database='matrix',
        params={
            "full_export": rogue_one_business_information["full_export"]
        },
        conn_id='psql_matrix_email_app',
    )
    export_rogue_one_business_information.doc_md = task_doc_md

    rogue_one_business_information_schemas = [{"mode": "REQUIRED", "name": "rogue_one_email_id", "type": "INTEGER"},
                                              {"mode": "REQUIRED", "name": "editor", "type": "STRING"},
                                              {"mode": "NULLABLE", "name": "plateform", "type": "STRING"},
                                              {"mode": "NULLABLE", "name": "advertiser", "type": "STRING"}]
    # import prepared data from Postgres to BQ
    task_doc_md = '''
        Import postgreSQL temporary table : rogue_one.rogue_one_business_information into BQ<br />
        '''
    import_bq_rogue_one_business_information = PostgresToBigQueryOperator(
        task_id='import_bq_rogue_one_business_information',
        database='matrix',
        table='matrix__email_tmp.rogue_one_business_information_{{ ds_nodash }}',
        bucket=buckets['matrix'],
        source_object='rogue_one/information/export/export_rogue_one_business_information_{{ ds_nodash }}.csv',
        destination_project_dataset_table=bq_project + ':import.rogue_one_business_information_{{ ds_nodash }}',
        schema_fields=rogue_one_business_information_schemas,
        field_delimiter='\t',
        quote_character='',
        allow_quoted_newlines=True,
        write_disposition='WRITE_TRUNCATE',
    )
    import_bq_rogue_one_business_information.doc_md = task_doc_md

    # prepare data to be inserted into a store table
    task_doc_md = '''
        Dump data from import dataset into prepare <br />.
        '''
    prepare_bq_rogue_one_business_information = BigQueryExecuteQueryOperator(task_id='prepare_bq_rogue_one_business_information',
                                                                 use_legacy_sql=False,
                                                                 sql='information/sql/prepare_rogue_one_business_information.sql',
                                                                 params={
                                                                     'bq_project': bq_project
                                                                 })
    prepare_bq_rogue_one_business_information.doc_md = task_doc_md

    # mep prepared data into store table
    task_doc_md = '''
        Mep prepared data into store table into BQ <br />.
        '''
    mep_bq_rogue_one_business_information = BigQueryExecuteQueryOperator(task_id='mep_bq_rogue_one_business_information',
                                                             use_legacy_sql=False,
                                                             sql='information/sql/mep_rogue_one_business_information.sql',
                                                             params={
                                                                 'bq_project': bq_project
                                                             })
    mep_bq_rogue_one_business_information.doc_md = task_doc_md

    drop_tmp_table_query = """
        DROP TABLE IF EXISTS matrix__email_tmp.rogue_one_business_information_{{ ds_nodash }}; 
    """
    task_doc_md = '''
        Delete temporary table matrix__email_tmp.rogue_one_theme_{{ ds_nodash }}<br />
    '''
    delete_rogue_one_business_information_tmp_table = SQLExecuteQueryOperator(
        task_id='delete_rogue_one_business_information_tmp_table',
        sql=drop_tmp_table_query,
        database='matrix',
        conn_id='psql_matrix_email_app',
    )
    delete_rogue_one_business_information_tmp_table.doc_md = task_doc_md
    import_bq_rogue_one_business_information >> delete_rogue_one_business_information_tmp_table

    export_rogue_one_business_information >> import_bq_rogue_one_business_information >> \
        prepare_bq_rogue_one_business_information >> mep_bq_rogue_one_business_information
