r"""
**PURPOSE:**

- This DAG compute daily refined Google Analytics data as Events / profile / .... 
From GA4 exported Data events_, we create refined data process in order to extract and keep only useful information in structured way .
- In order to get more information about Export process 👉🏻**https:**//app.diagrams.net/#G19LC6n0-n-svBWwcrR_EQOPY8eGG2Xp7G  .
- Project Plan 👉🏻**https:**//docs.google.com/document/d/********************************************/ .
- We launch parallel refinery process for each Google Analytics property. A property is linked to brand by platform ("web", "app").

**METHODOLOGY:**

1. For each property, we initialize table with CREATE STATEMENT like a templated table `pm-prod-ga4.refined_data.ga_events__TEMPLATE`.
2. From exported Google Analytics daily events, we re-**organize it with this hierarchy:**visit date
        |
        user
            |
            [sessions]
                    |
                    [pages] / [screens]
                        |
                        [events]

Actually, for each day we check if events_ table is available. If it's not the case, we will took events_intraday_ table. For the next Run, we will update refined tables by using events_ tables.

3. We update session information across the last 7 days. So why ? Because a session can be started at day(1) and closed at day(N).So what we do:we update (start, end) session datetime and (logged / not) session.

**CONFIGURATION:**

-Example:[
    {
        "layer_name": "refined_data",
        "process_name": "refine__ga4_events",
        "ga_property": [
        {
            "property_id": 257568745,
            "brand_trigram": "CAC",
            "country": "ALL",
            "platform": "WEB",
            "section": "ALL",
            "partial_mode": {
              "is_on": false,
              "date_range": [
                "2023-01-01",
                "2024-01-01"
              ]
            },
            "incremental_mode": {
              "is_on": true,
              "day_interval": 2
            },
            "table_prefix": ["events_", "events_intraday_*"]
        },
        {...}
    }
]

- layer_name:  The layer of destination dataset as ("refined_data", "generated_data", "business_data").
- process_name:  The name of the process as ("refine__*", "generate__*", ....)
- ga_property:  Google Analytics Property information. It's optional for each config {}.
- property_id:  Google Analytics property Id.
- brand_trigram: Google Analytics property brand trigram.
- country:  Google Analytics property country.It can be:**("ALL" : default value, "DE", "UK", ...).
- platform:  Google Analytics property country.It can be:**("WEB", "APP").
- section:  Google Analytics property section.It can be:("SPORT", "TRIP", "SEXO", ...). It's very specific to Gentside brand.
- partial_mode:  Process partial mode defined by state "is_on" and "date_range".
- incremental_mode:  Process incremental mode defined by state "is_on" and "day_interval".
- table_prefix:  Google Analytics table prefix like "events_*" OR "events_intraday_*".

**HOW_TO:**

**EXECUTE PARTIAL FULL (start, end):**

- In "partial_mode", change "is_on" key to true and set "date_range" values.

**EXECUTE INCREMENTAL:**

-In "incremental_mode", change "is_on" key to true and set "day_interval" value in days.

**COMMAND TO TEST:** 
    - PREPROD ENV:
        - gsutil -m cp -R dags/refined_data__google_analytics.py**gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/
        - gsutil -m cp -R data/json/preprod/google_analytics_data_refinery.json**gs://europe-west1-preprod-mozart-2fa49086-bucket/data/json/preprod/
        - gsutil -m cp -R data/sql/refined_data/google_analytics/*  gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/refined_data/google_analytics/
    - PROD ENV:
        - gsutil -m cp -R dags/refined_data__google_analytics.py**gs://europe-west1-prod-mozart-co-2ef7e904-bucket/dags/
        - gsutil -m cp -R data/json/prod/google_analytics_data_refinery.json**gs://europe-west1-prod-mozart-co-2ef7e904-bucket/data/json/prod/
        - gsutil -m cp -R data/sql/refined_data/google_analytics/*  gs://europe-west1-prod-mozart-co-2ef7e904-bucket/data/sql/refined_data/google_analytics/
"""
from airflow.operators.bash import BashOperator
from collapsible_doc_dag import CDocDAG
from airflow.utils.trigger_rule import TriggerRule
from airflow.models import Variable
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryGetDataOperator
from airflow.datasets import Dataset
from bq_plugin.sensors.bigquery_sql_sensor import BigQuerySqlSensor
from airflow.operators.bash import BashOperator
from airflow.operators.empty import EmptyOperator
from datetime import datetime, date, timedelta
import json
import logging
import os
from jinja2 import Template

dag_name = "refined_data__google_analytics_4"
env = os.environ.get("ENV")
bq_project = "pm-{env}-ga4".format(env=env)
bq_project_matrix = "pm-{env}-matrix".format(env=env)
email_on_failure = True if env == "prod" else False
sql_search_path = "/home/<USER>/gcs/data/sql/refined_data/google_analytics/"
monitoring_sql_search_path = "/home/<USER>/gcs/data/sql/monitoring__data_quality/google_analytics/"
config_search_path = f"/home/<USER>/gcs/data/json/{env}/"
config_json_file = "google_analytics_data_refinery.json"
# Set Aiflow templates in variables
execution_date_nodash = "{{ next_ds_nodash }}"
execution_date = "{{ next_ds }}"
default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "log_response": True,
    "start_date": datetime(2024, 3, 5, 0, 0),
    "xcom_push": True,
    "provide_context": True,
    "email_on_failure": email_on_failure,
    "email_on_retry": False,
    "email": [eval(Variable.get("airflow_email_alertes"))],
    "gcs_conn_id": "gcs_matrix",
    "bigquery_conn_id": "bq_matrix",
    "gcp_conn_id": "bq_matrix",
    "use_legacy_sql": False,
    "allow_large_results": True,
    "flatten_results": False,
    "execution_timeout": timedelta(minutes=240),
    "retires": 10,
    "retry_delay": timedelta(minutes=2),
    "max_retry_delay": timedelta(minutes=240),
    "location": "EU"
}


def generate_table_suffix(**kwargs):
    """
        Generate table suffix list.
        :param kwargs: "start_date", "end_date", "input_date_format", output_date_format, "table_prefix"
        :return: list of dates prefixed by "_"
    """

    # format dates
    start_dt = datetime.strptime(kwargs["start_date"], kwargs["input_date_format"])
    end_dt = datetime.strptime(kwargs["end_date"], kwargs["input_date_format"])
    try:
        assert (end_dt - start_dt).days >= 0, "Start date cannot be greater than end date!"
        # store the dates between two dates in a list as "%Y%m%d"
        table_suffix = [
            "_" + (start_dt + timedelta(days=x)).strftime(kwargs["output_date_format"]) for x in
            range((end_dt - start_dt).days + 1)]

        print("Tables suffix between", start_dt.strftime(kwargs["output_date_format"]), "and",
              end_dt.strftime(kwargs["output_date_format"]),
              ": ", table_suffix)
        return table_suffix
    # the error_message provided by the user gets printed
    except AssertionError as msg:
        print(msg)


def load_json(**kwargs):
    """
        Load json file.
        :param kwargs: "input_file"
        :return: nested json
    """
    input_file = kwargs["input_file"]
    with open(input_file) as json_file:
        json_data = json.load(json_file)
    return json_data


def read_sql(sql_file_path):
    """
      Read SQL file
      :param sql_file_path: SQL file path
      :return: None
    """
    return open(sql_file_path, mode="r", encoding="utf-8-sig").read()


def render_sql(sql_file_path, **kwargs):
    """
        Render sql template by parameters.
        :param sql_file_path: SQL file path
        :param kwargs: "params", the parameters to render
        :return: rendered sql template
    """
    templated__sql_file = read_sql(sql_file_path=sql_file_path)
    return Template(templated__sql_file).render(**kwargs)


def generate_date_range_str(**kwargs):
    """
        Generate date range
        :param kwargs: config as dict
        :return: date range bounds : start and end
    """
    start_date = kwargs["config"].get("partial_mode").get("date_range")[0] if kwargs["is_partial_mode"] else (
            date.today() - timedelta(
        days=kwargs["config"].get("incremental_mode").get("day_interval"))).strftime("%Y-%m-%d")
    end_date = kwargs["config"].get("partial_mode").get("date_range")[
        1] if kwargs["is_partial_mode"] else (date.today() - timedelta(days=1)).strftime("%Y-%m-%d")
    return start_date, end_date


# get configuration to refine GA events
ga_data_refinery_list = load_json(
    input_file="{config_search_path}{config_json_file}".format(config_search_path=config_search_path,
                                                               config_json_file=config_json_file))
ga_refine_events_config = [config for config in ga_data_refinery_list if
                           config.get("layer_name") == "refined_data" and config.get(
                               "process_name") == "refine__ga4_events"][0]
# get process mode (partial, incremental) information
ga_refine_events_config_per_property = ga_refine_events_config.get("ga_property")

# list_table_prefix = set(entry["table_prefix"] for entry in ga_refine_events_config_per_property)
list_table_prefix = set(["events_", "events_intraday_"])

is_full = ga_data_refinery_list[0].get("is_full")

with CDocDAG(
        dag_id=dag_name,
        description="Refine Google Analytics Events (GA4)",
        tags=["refined", "ga4"],
        doc_md=__doc__,
        schedule_interval="0 1 * * *",
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=10),
        catchup=False,
        concurrency=5,
        max_active_tasks=10,
        template_searchpath=[sql_search_path, config_search_path, monitoring_sql_search_path]
) as dag:
    start_init = EmptyOperator(task_id="start_init")
    end_init = EmptyOperator(task_id="end_init")
    start_refine = EmptyOperator(task_id="start_refine", trigger_rule="all_done")
    end_refine = EmptyOperator(task_id="end_refine", trigger_rule="all_done")

    task_doc_md = """
        Initiate Template table for websites.
    """
    init__refined_ga4_events_WEB = BigQueryExecuteQueryOperator(
        task_id="init__refined_ga4_events_WEB",
        sql="init__refined_ga4_events_WEB.sql",
        params={
            "bq_project": bq_project
        }
    )
    init__refined_ga4_events_WEB.doc_md = task_doc_md

    task_doc_md = """
        Initiate Template table for apps.
    """
    init__refined_ga4_events_APP = BigQueryExecuteQueryOperator(
        task_id="init__refined_ga4_events_APP",
        sql="init__refined_ga4_events_APP.sql",
        params={
            "bq_project": bq_project
        }
    )
    init__refined_ga4_events_APP.doc_md = task_doc_md

    task_doc_md = """
        List all User Pseudo IDs and their PMC WEB IDs 
    """
    refine__google_analytics_profile = BigQueryExecuteQueryOperator(
        task_id="refine__google_analytics_profile",
        sql="refine__google_analytics_profile.sql",
        params={
            "bq_project": bq_project,
            "bq_project_matrix": bq_project_matrix,
            "start_date": ga_refine_events_config.get("users").get("start_date"),
            "end_date": ga_refine_events_config.get("users").get("end_date"),
            "interval": ga_refine_events_config.get("users").get("interval"),
            "is_full": ga_refine_events_config.get("users").get("is_full")
        },
        trigger_rule=TriggerRule.ALL_SUCCESS,
        outlets=[Dataset("bigquery://ga4/refined_data/google_analytics_profile/")]
    )
    refine__google_analytics_profile.doc_md = task_doc_md

    # refine GA events per property
    for config in ga_refine_events_config_per_property:
        # get configurations
        property_id = config.get("property_id")
        brand_trigram = config.get("brand_trigram")
        country = config.get("country")
        platform = config.get("platform")
        section = config.get("section")
        is_partial_mode = config.get("partial_mode").get("is_on")
        ga_events_table_prefix = config.get("table_prefix")

        logging.info("Refine GA4 events for {} {} {} {}".format(brand_trigram, country, platform, section))

        # get start and end dates
        start_date, end_date = generate_date_range_str(config=config, is_partial_mode=is_partial_mode)

        logging.info("Date range is : ({}, {})".format(start_date, end_date))

        # generate table's name list as ["_<date>"]. e.g: ["_20231201", "_20231202"]
        table_suffix = generate_table_suffix(start_date=start_date, end_date=end_date, input_date_format="%Y-%m-%d",
                                             output_date_format="%Y%m%d")

        # render check tables SQL query
        rendered__check_sql = render_sql(sql_file_path="{}check__sql_template.sql".format(sql_search_path),
                                         bq_project=bq_project,
                                         property_id=property_id, table_suffix=table_suffix,
                                         execution_date=execution_date,
                                         dag={"dag_id": dag_name},
                                         task={
                                             "task_id": "check__raw_ga4_events_{platform}_{brand_trigram}_{country}_{section}".format(
                                                 platform=platform, brand_trigram=brand_trigram, country=country,
                                                 section=section)})

        ############################## TASKS BEGIN ##############################
        task_doc_md = """
            Get table name in date range and store it into a BigQuery metadata table.
        """
        store__table_name = BigQueryExecuteQueryOperator(
            task_id="store__table_name_ga4_events_{platform}_{brand_trigram}_{country}_{section}".format(
                platform=platform, brand_trigram=brand_trigram, country=country, section=section),
            sql="store__table_name.sql",
            params={
                "property_id": property_id,
                "bq_project": bq_project,
                "table_suffix": table_suffix
            }
        )
        store__table_name.doc_md = task_doc_md

        task_doc_md = """
            Check Google Analytics events table"s availability for a specific date range.
        """
        check__raw_ga4_tables = BigQuerySqlSensor(
            task_id="check__raw_ga4_events_{platform}_{brand_trigram}_{country}_{section}".format(
                platform=platform, brand_trigram=brand_trigram, country=country, section=section),
            gcp_conn_id="bq_conn_id_ga4",
            timeout=60 * 60 * 1,    # 1 hour
            retries=24,
            poke_interval=60 * 20,  # 20 min
            mode="reschedule",
            #parameters={"location": "EU", "project": "pm-prod-ga4", "use_legacy_sql": False},
            sql=rendered__check_sql
        )
        check__raw_ga4_tables.doc_md = task_doc_md

        task_doc_md = """
           Get table name from metadata table per property for each RUN.
        """
        get__table_name = BigQueryExecuteQueryOperator(
            task_id="get__table_name_ga4_events_{platform}_{brand_trigram}_{country}_{section}".format(
                platform=platform, brand_trigram=brand_trigram, country=country, section=section),
            sql="get__table_name.sql",
            params={
                "property_id": property_id,
                "bq_project": bq_project
            }
        )
        get__table_name.doc_md = task_doc_md

        task_doc_md = """
            Fetch table into an XCOM variable.
        """
        fetch__table_name_into_xcom = BigQueryGetDataOperator(
            task_id="fetch__table_name_into_xcom_{platform}_{brand_trigram}_{country}_{section}".format(
                platform=platform, brand_trigram=brand_trigram, country=country, section=section),
            table_project_id=bq_project,
            dataset_id="temp",
            table_id="table_name_{property_id}_{execution_date}".format(property_id=property_id,
                                                                        execution_date=execution_date_nodash),
            selected_fields="table_name",
            max_results=100000
        )
        fetch__table_name_into_xcom.doc_md = task_doc_md

        task_doc_md = """
            Refine Google Analytics Events for a specific date range.
            The result will be stored in a TEMPORARY TABLE.
        """
        refine__ga4_events = BigQueryExecuteQueryOperator(
            task_id="refine__ga4_events_{platform}_{brand_trigram}_{country}_{section}".format(
                platform=platform, brand_trigram=brand_trigram, country=country, section=section),
            sql="refine__ga4_events.sql",
            params={
                "property_id": property_id,
                "platform": platform,
                "brand_trigram": brand_trigram,
                "country": country,
                "section": section,
                "bq_project": bq_project,
                "task_name": "fetch__table_name_into_xcom_{platform}_{brand_trigram}_{country}_{section}".format(
                    platform=platform, brand_trigram=brand_trigram, country=country, section=section),
                "start_date": start_date,
                "end_date": end_date
            }
        )
        refine__ga4_events.doc_md = task_doc_md

        # @We use "bq cp" because for some properties we are faced to Memory BQ limitation with classic DDL statements.
        task_doc_md = """
            Merge incremental events into the final refined data table.
        """
        merge__ga4_events = BashOperator(
            task_id="merge__ga4_events_{platform}_{brand_trigram}_{country}_{section}".format(
                platform=platform, brand_trigram=brand_trigram, country=country, section=section),
            bash_command=f"""
                echo "Merge start from {start_date} to {end_date} !!" 
                echo "Merge from {bq_project}:temp.ga_events_{platform}_{brand_trigram}_{country}_{section}_{execution_date_nodash} to {bq_project}:refined_data.ga_events_{platform}_{brand_trigram}_{country}_{section}"
                bq --location=EU --project_id={bq_project} cp -a {bq_project}:temp.ga_events_{platform}_{brand_trigram}_{country}_{section}_{execution_date_nodash} {bq_project}:refined_data.ga_events_{platform}_{brand_trigram}_{country}_{section}
                echo "Merge finish !!"
            """
        )
        merge__ga4_events.doc_md = task_doc_md

        task_doc_md = """
            Update refined events by changing start, end and logging information for each session across sliding 7 days. 
        """
        update__session_data = BigQueryExecuteQueryOperator(
            task_id="update__session_data_{platform}_{brand_trigram}_{country}_{section}".format(
                platform=platform, brand_trigram=brand_trigram, country=country, section=section),
            sql="update__session_data.sql",
            params={
                "bq_project": bq_project,
                "platform": platform,
                "brand_trigram": brand_trigram,
                "country": country,
                "section": section,
                "start_date": start_date,
                "end_date": end_date
            }
        )
        update__session_data.doc_md = task_doc_md

        # call tasks
        start_init >> [init__refined_ga4_events_WEB,
                       init__refined_ga4_events_APP] >> end_init >> store__table_name >> check__raw_ga4_tables >> get__table_name \
        >> fetch__table_name_into_xcom >> start_refine >> refine__ga4_events >> merge__ga4_events >> update__session_data >> end_refine >> refine__google_analytics_profile
