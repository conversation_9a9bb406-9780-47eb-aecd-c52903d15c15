r"""
**PURPOSE:**

This dag is used to monitor export file for riviera and webrivage
"""

# gsutil cp dags/partners_monitoring_export_ingested.py gs://europe-west1-mozart-cluster-2e910a39-bucket/dags/

import json
import logging
from datetime import datetime, timedelta

from airflow.models import Variable
from airflow.operators.python import PythonOperator
from airflow.utils.email import send_email
from collapsible_doc_dag import CDocDAG
from sftp_plugin.operators.ftps_files_count_operator import FtpsFilesCountOperator
from sftp_plugin.operators.list_files_ftps_operator import ListFilesFtpsOperator
from sftp_plugin.operators.list_files_sftp_operator import ListFilesSftpOperator
from sftp_plugin.operators.sftp_files_count_operator import SftpFilesCountOperator

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2020, 5, 7, 0, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': True,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=5),
    'sla': None,
    'email_on_retry': False,
    'retries': 0,
    'depends_on_past': False,
}


################################################
############        RIVIERA          ###########
################################################

def notify_email_riviera(files):
    list_files = ['- {} : {} <br>'.format(key, value) for key, value in files.items()]
    list_files = json.dumps(list_files).replace(']', '').replace('[', '')

    title = "(Erreur) Import des fichiers PrismaMedia"
    body = ("Bonjour, <br> "
            "<br> Nous avons déposé des fichiers qui n'ont pas été intégrés. Il s'agit des fichiers suivant : <br>"
            "<br><br> Ci-après la liste des fichiers non ingérés <br><br>"
            "<br> " + list_files + "<br> "
                                   "<br> Pour toute remarque et question, vous pouvez nous transférer cet email à <EMAIL>.<br> "
                                   "<br> <br/>Cordialement, <br> "
                                   "L'équipe technique IT-Data"
            )

    # to = ('<EMAIL>', '<EMAIL>', '<EMAIL>')
    to = ("<EMAIL>")
    send_email(to, title, body, conn_id='sendgrid_default')
    print('email html + ' + body)


def runCheckFiles_riviera(ds, **kwargs):
    error = []
    conn_id_list = {'sftp_riviera_cuisine_actuelle_part': '/IN/flux/', \
                    'sftp_riviera_capital_part': '/IN/flux/', \
                    'sftp_riviera_ca_minteresse_part': '/IN/flux/', \
                    'sftp_riviera_femme_actuelle_part': '/IN/flux/', \
                    'sftp_riviera_gala_part': '/IN/flux/', \
                    'sftp_riviera_gentside_part': '/IN/flux/', \
                    'sftp_riviera_geo_part': '/IN/flux/', \
                    'sftp_riviera_neon_part': '/IN/flux/', \
                    'sftp_riviera_ohmymag_part': '/IN/flux/', \
                    'sftp_riviera_serengo_part': '/IN/flux/', \
                    'sftp_riviera_tele_loisirs_part': '/IN/flux/', \
                    'sftp_riviera_tele_2_semaines_part': '/IN/flux/', \
                    'sftp_riviera_voici_part': '/IN/flux/'}

    list_files = {}
    for conn_id, path in conn_id_list.items():

        print('Conn_id : ' + conn_id)
        print('Path : ' + path)

        count_files_task = SftpFilesCountOperator(task_id='count_files_riviera',
                                                  sftp_conn_id=conn_id,
                                                  sftp_directory=path)

        nb_files = count_files_task.execute(context=kwargs)
        logging.info("nb_files_found : {}".format(str(nb_files)))

        if nb_files > 2:
            list_files_task = ListFilesSftpOperator(task_id='list_files_riviera',
                                                    sftp_conn_id=conn_id,
                                                    sftp_directory=path)

            list_files[conn_id] = list_files_task.execute(context=kwargs)
            logging.info("files_found {}".format(str(list_files)))
            # remove directories
            list_files[conn_id].remove('ARCHIVES')
            list_files[conn_id].remove('errors')

    if len(list_files) > 0:
        notify_email_riviera(list_files)

    ################################################


###########       WEBRIVAGE         ############
################################################


def notify_email_webrivage(files):
    title = "[Webrivage monitoring]: Ingestion de l'export non terminé"
    body = ("Bonjour <br> "
            "<br> L'Ingestion de l'export générique par Webrivage n'est pas terminé ce jour à midi <br>"
            "<br><br> Ci-après la liste des fichiers non ingérés <br><br>"
            "<br>" + str(files) + "<br> "
                                  "<br> <br/>Cordialement, <br> "
                                  "Mozart"
            )

    print(body);

    send_email("<EMAIL>", title, body, conn_id='sendgrid_default')


def runCheckFiles_webrivage(ds, **kwargs):
    count_files_task = FtpsFilesCountOperator(task_id='count_files_webrivage',
                                              ftp_conn_id='ftp_webrivage',
                                              ftp_directory='export/')

    nb_files = count_files_task.execute(context=kwargs)
    logging.info("nb_files_found : {}".format(str(nb_files)))

    if nb_files > 0:
        list_files_task = ListFilesFtpsOperator(task_id='list_files_webrivage',
                                                ftp_conn_id='ftp_webrivage',
                                                ftp_directory='export/')

        list_files = list_files_task.execute(context=kwargs)
        logging.info("files_found {}".format(str(list_files)))
        notify_email_webrivage(list_files)


with CDocDAG(
        'partners_monitoring_export_ingested',
        description="monitoring partners dag export",
        tags=["partner"],
        doc_md=__doc__,
        schedule_interval="0 8 * * *",
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(minutes=160),
        catchup=False) as dag:
    task_doc_md = """
    Check for each sftp if the files are present otherwise, send a mail<br />
    """
    riviera_monitoring = PythonOperator(
        task_id='riviera_monitoring_export_ingested_task',
        python_callable=runCheckFiles_riviera,
        provide_context=True
    )

    riviera_monitoring.doc_md = task_doc_md

    task_doc_md = """
    Check for webrivage sftp if all files are ingested<br />
    """
    webrivage_monitoring = PythonOperator(
        task_id='webrivage_monitoring_export_ingested_task',
        python_callable=runCheckFiles_webrivage,
    )

    webrivage_monitoring.doc_md = task_doc_md
