r"""
**PURPOSE:**

This DAG programmatically updates the min-instances parameter for multiple Google Cloud Run services 
across different Google Cloud projects.**The main goal is to ensure that certain services have at least one running instance at 7:**00 AM UTC (for availability)**and scale down to zero after 18:**00 PM UTC for cost savings.

**METHODOLOGY:**

The DAG uses a parameter named itdata_config_cloud_run_services, 
which contains a list of service configurations. Each entry specifies a project_id and a service_id for a Cloud Run service.

For each service, the DAG constructs and executes a gcloud command to update the service'**s minimum instances:**gcloud run services update {service_id} --min-instances {min_instances} --region europe-west1 --project {project_id} --quiet 

**COMMAND TO TEST:** 

gsutil -m cp -R dags/gcp_cloud_run_services_update.py gs://europe-west1-prod-mozart-co-2ef7e904-bucket/dags/
"""

import os
from datetime import timedelta, datetime

from airflow.decorators import task
from airflow.models import Variable
from airflow.operators.bash import BashOperator
from collapsible_doc_dag import CDocDAG
import logging

env = os.environ.get("ENV")
str_env = '' if env == 'prod' else '_' + env
dag_name = f'gcp_cloud_run_services_update{str_env}'
email_on_failure = True if env == 'prod' else False

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2025, 4, 15, 00, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=10),
    'sla': None,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=1),
    'depends_on_past': False,
}

config_cloud_run_services = Variable.get("itdata_config_cloud_run_services", deserialize_json=True)

# From 7h UTC to 17h UTC => min_instances = 1 else => min_instances = 0
current_hour = datetime.now().hour
min_instances = 1 if 7 <= current_hour < 18 else 0

with CDocDAG(dag_name,
             schedule_interval="0 7,18 * * *",
             description='Update cloud run services config',
             doc_md=__doc__,
             tags=["gcp", "app-data"],
             default_args=default_args,
             max_active_runs=1,
             dagrun_timeout=timedelta(minutes=15),
             catchup=False,
             template_searchpath=['/home/<USER>/gcs/data/sql/matrix_email/']) as dag:

    @task(task_id="update_cloud_run_service")
    def update_cloud_run_service(service_config, min_instance):
        """
        Updates the min-instances for a specific Cloud Run service using curl.

        :param service_config: The service configuration dictionary.
        :param min_instance: The desired minimum number of instances.
        """
        project_id = f'pm-{env}-' + service_config.get('project_id', 'app-data')
        service_id = service_config.get('service_id')

        logging.info(f"Updating Cloud Run service: {service_id} in project: {project_id} with min_instance: {min_instance}")

        # @todo with composer 3 (with recent Google Cloud SDK only that support --min option)
        # bash_command = (
        #    f"gcloud run services update {service_id} "
        #    f"--region=europe-west1 "
        #    f"--min={min_instances} "
        #    f"--project={project_id} "
        #    f"--quiet"
        # )

        bash_command = (
            f"""
            # Get the access token
            ACCESS_TOKEN=$(gcloud auth print-access-token)

            # Execute the PATCH request with curl
            curl -H "Content-Type: application/json" \\
                 -H "Authorization: Bearer $ACCESS_TOKEN" \\
                 -X PATCH \\
                 -d '{{ "scaling": {{ "minInstanceCount": {min_instance} }} }}' \\
                 "https://run.googleapis.com/v2/projects/{project_id}/locations/europe-west1/services/{service_id}?update_mask=scaling.minInstanceCount"
            """
        )

        logging.info(f"Executing bash command: {bash_command}")

        result = BashOperator(
            task_id=f'update_{service_id}',
            bash_command=bash_command,
        )
        result.doc_md = f'Set min_instance for cloud run service= {service_id} in project={project_id}'
        logging.info(f"Bash command result : {result.bash_command}")
        return result.doc_md

    update_cloud_run_service.partial(min_instance=min_instances).expand(
        service_config=config_cloud_run_services['services']
    )
