import os

from airflow import DAG
from airflow.models import Variable
from datetime import datetime, timedelta
from psql_plugin.operators.clean_temporary_table_postgres_operator import CleanTemporaryTablePostgresOperator

env = os.environ.get("ENV")

# BQ specific config
bq_project = 'pm-{}-matrix'.format(env)

# GCS specific config
buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
gcs_bucket = buckets['matrix']

alert_recipient = eval(Variable.get("airflow_email_alertes"))
if env != 'prod':
    alert_recipient = '<EMAIL>'

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2020, 12, 1, 0, 0, 0),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': False,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=90),
    'sla': None,
    'email_on_retry': False,
    'retries': 0,
    'retry_delay': timedelta(minutes=1),
    'depends_on_past': False,

    # PSQL Connection
    'postgres_conn_id': 'psql_matrix_email_app',

    # GCP CONNECTION
    'gcp_conn_id': 'gcs_matrix',
}

with DAG("clean_pg_tables",
         schedule_interval="0 6 10 * *",
         catchup=False,
         max_active_runs=1,
         dagrun_timeout=timedelta(hours=2),
         default_args=default_args) as dag:
    clean_pg_temporary_tables = CleanTemporaryTablePostgresOperator(
        task_id='clean_pg_temporary_tables',
        alert_recipient=alert_recipient
    )
