r"""
**PURPOSE:**

This dag is used clean all temp definitions generated by live calculating volume on affinity tags.
"""

import os
from datetime import datetime, timedelta
from collapsible_doc_dag import CDocDAG
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from airflow.providers.google.cloud.hooks.bigquery import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from jinja2 import Environment, FileSystemLoader
from pprint import pprint
from airflow.operators.python import PythonOperator

# Constants and Globals
ENV = os.environ.get("ENV", 'prod')
DAG_NAME = 'picasso_clean_temp_segments'
DAG_NAME = f"{DAG_NAME}_{ENV}" if ENV != 'prod' else DAG_NAME

BQ_PROJECT = f'pm-{ENV}-matrix'
BQ_LOCATION = 'EU'
TEMPLATE_PATH = '/home/<USER>/gcs/data/sql/segment_manager/segment_definition/'

# SQL Templates
SQL_PG_CLEAN_TEMP_DEFINITIONS = 'pg_clean_temp_definitions.sql'
SQL_BQ_EXPORT_TEMP_TABLES_TO_CLEAN = 'bq_export_tables_to_clean.sql'

# Configuration for table prefixes
TEMP_BQ_TABLES_PREFIX = 'segment-sha256_profile_score_temp_to_remove_'
TEMP_PG_TABLES_PREFIX = 'temp_to_remove_'

# Default args for the DAG
default_args = {
    'owner': 'airflow',
    'start_date': datetime(2023, 11, 28),
    'email_on_failure': ENV == 'prod',
    'retries': 3,
    'retry_delay': timedelta(minutes=20),
    'execution_timeout': timedelta(minutes=120),
}

with CDocDAG(
        DAG_NAME,
        default_args=default_args,
        doc_md=__doc__,
        description='Clean temp affinity tables in postgres and clean generated tables in bigQuery affinity store.',
        schedule_interval=timedelta(days=1),
        catchup=False,
        tags=["segment_manager"],
        template_searchpath=[TEMPLATE_PATH],
) as dag:
    task_doc_md = """
    Task to clean temporary definitions in PostgreSQL.
    """
    clean_temp_pg_definitions = SQLExecuteQueryOperator(
        task_id='clean_temp_pg_definitions',
        sql=SQL_PG_CLEAN_TEMP_DEFINITIONS,
        autocommit=True,
        conn_id='psql_picasso_app',
        params={
            'bq_project': BQ_PROJECT,
            'TEMP_PG_TABLES_PREFIX': TEMP_PG_TABLES_PREFIX
        }
    )
    clean_temp_pg_definitions.doc_md = task_doc_md

    # Function to render SQL templates
    def render_template(template_file, template_args):
        template_loader = FileSystemLoader(TEMPLATE_PATH)
        environment = Environment(loader=template_loader)
        template = environment.get_template(template_file)
        return template.render(template_args)

    # Function to execute BigQuery query and fetch results
    def execute_and_fetch_bq_query(sql):
        hook = BigQueryHook(gcp_conn_id='bq_matrix', location=BQ_LOCATION, use_legacy_sql=False)
        conn = hook.get_conn()
        cursor = conn.cursor()
        cursor.execute(sql)
        results = cursor.fetchall()
        cursor.close()
        return results

    # Function to drop temporary BigQuery tables
    def drop_temp_bq_tables():
        sql = render_template(SQL_BQ_EXPORT_TEMP_TABLES_TO_CLEAN, {
            'dag': dag, 'task': {'task_id': 'drop_temp_bq_tables'},
            'params': {
                'bq_project': BQ_PROJECT,
                'TEMP_BQ_TABLES_PREFIX': TEMP_BQ_TABLES_PREFIX
            }
        })
        query_result = execute_and_fetch_bq_query(sql)

        if not query_result:
            print("No tables to drop. The query result is None or empty.")
            return

        for table_name in query_result:
            drop_table_sql = f"DROP TABLE IF EXISTS `{table_name[0]}`"
            pprint(drop_table_sql)
            execute_and_fetch_bq_query(drop_table_sql)

    task_doc_md = """
    Task to drop temporary BigQuery tables.
    """
    drop_temp_bq_tables_task = PythonOperator(
        task_id='drop_temp_bq_tables',
        python_callable=drop_temp_bq_tables,
        dag=dag
    )
    drop_temp_bq_tables_task.doc_md = task_doc_md

    # Task dependencies
    clean_temp_pg_definitions >> drop_temp_bq_tables_task
