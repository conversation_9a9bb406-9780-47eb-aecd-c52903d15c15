r"""
**PURPOSE:**

This dag export the last reactivity date of sub profiles to Splio universes

**COMMAND TO TEST:** 

- gsutil cp dags/matrix__email_datahub_004_splio.py gs://europe-west1-prod-mozart-co-2ef7e904-bucket/dags/ 
- gsutil -m cp -R data/sql/matrix_email/11_export_splio_datahub_004.sql**gs://europe-west1-prod-mozart-co-2ef7e904-bucket/data/sql/matrix_email/
"""

import os
from datetime import timedelta, datetime

from airflow.models import Variable
from collapsible_doc_dag import CDocDAG
from export_plugin.operators.export_splio_bigquery_operator import ExportSplioBigQueryOperator

env = os.environ.get("ENV")
bq_project = f'pm-{env}-matrix'
str_env = '' if env == 'prod' else '_' + env
dag_name = f'matrix__email_datahub_004_splio{str_env}'
email_on_failure = True if env == 'prod' else False
gcs_uri = 'gs://it-data-prod-matrix-pipeline/' if env == 'prod' else 'gs://it-data-preprod-matrix-preprod-pipeline/'

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2023, 6, 11, 19, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=3),
    'sla': None,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=1),
    'depends_on_past': False,

    'gcp_conn_id': 'gcs_matrix',
    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_TRUNCATE',
    'allow_large_results': True,
    'flatten_results': False,
}

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']

datahub_contact_004 = Variable.get("datahub_contact_004", deserialize_json=True)
splio_universes_sftp = Variable.get("splio_universes_sftp", deserialize_json=True)
splio_universes_all = Variable.get("splio_universes_all", deserialize_json=True)
splio_universes = {u: splio_universes_all[u] for u in datahub_contact_004['universes'] if u in splio_universes_all}

with CDocDAG(dag_name,
             description='Export the last reactivity date to Splio universes',
             doc_md=__doc__,
             schedule_interval="0 19 * * *",
             default_args=default_args,
             max_active_runs=1,
             dagrun_timeout=timedelta(hours=3),
             catchup=False,
             tags=["datahub"],
             template_searchpath=['/home/<USER>/gcs/data/sql/matrix_email/']) as dag:
    export_splio_datahub_004 = ExportSplioBigQueryOperator(
        task_id='export_splio_datahub_004',
        sql='11_export_splio_datahub_004.sql',
        params={
            'universe_id': '{universe_id}',
            'full_export': datahub_contact_004["full_export"],
            'interval': datahub_contact_004["interval"],
        },
        universes=splio_universes,
        universes_sftp=splio_universes_sftp,
        datahub_file_template='{universe}_contacts_004_{splio_sequence}*.csv',
        splio_sequence='{{ next_execution_date.strftime("%Y%m%d_%H%M") }}',
        bucket=matrix_bucket,
        bucket_path='export_splio/datahub_004/{{ next_ds }}/',
        destination_dataset_table_temporary='temp.{universe}_contacts_004_{splio_sequence}',
        destination_cloud_storage_path_uris=gcs_uri,
        splio_remote_dir='imports/',
        flux_ref=4,
        bq_project=bq_project,
    )
    export_splio_datahub_004.doc_md = 'Export the last reactivity date to Splio universes using datahub 004 files'
