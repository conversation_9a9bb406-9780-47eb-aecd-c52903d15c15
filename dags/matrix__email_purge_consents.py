r"""
**PURPOSE:**

This dag is used to clean sub nl profiles to deprecated consents.
When the proccess is over, we can delete this dag.
"""
# gsutil cp dags/matrix__email_purge_consents.py gs://europe-west1-prod-mozart-co-2ef7e904-bucket/dags/
# gsutil cp dags/matrix__email_purge_consents.py gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/
# gsutil -m cp -R data/sql/matrix_email_purge/*  gs://europe-west1-prod-mozart-co-2ef7e904-bucket/data/sql/matrix_email_purge/

import os
from datetime import timedelta, datetime

from airflow.decorators import task
from airflow.models import Variable
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryGetDataOperator
from airflow.utils.email import send_email
from airflow.utils.task_group import TaskGroup
from collapsible_doc_dag import CDocDAG
from matrix_plugin.operators.bigquery_to_email_event import BigQueryToEmailEventOperator

dag_name = 'matrix__email_purge_consents'

email_on_failure = True

env = os.environ.get("ENV")

# BQ specific config
bq_project = 'pm-{}-matrix'.format(env)
bq_dataset = 'export_matrix_email'

# GCS specific config
buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
gcs_matrix = buckets['matrix']

# ------------- FOR PREPROD ENV -----------------
env = os.environ.get("ENV")
if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    bq_project = 'pm-preprod-matrix'
    gcs_matrix_uri = 'gs://it-data-preprod-matrix-preprod-pipeline'
# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2022, 2, 7, 00, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=2),
    'sla': None,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'depends_on_past': False,
    # PSQL Connection
    'postgres_conn_id': 'psql_matrix_email_app',
    'database': 'matrix',
    'gcp_conn_id': 'gcs_matrix',

    # BQ option
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_TRUNCATE',
    'create_disposition': 'CREATE_IF_NEEDED',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE'
}

purge_deprecated_consent = Variable.get("purge_deprecated_consent", deserialize_json=True)

"""
{
   "alert_to":"<EMAIL>",
   "event_configs":[
      {
         "source":"matrix",
         "medium":"MATRIX-489 - purge inactifs consents",
         "limit":10000,
         "public_refs":[
            "ohmymag_oh_my_home_nl",
            "tele_loisirs_lepass_nl"
         ]
      },
      {
         "source":"matrix",
         "medium":"MATRIX-499 - purge coaching consents",
         "limit":80000,
         "public_refs":[
            "cuisine_actuelle_coaching_antigaspi_nl",
            "cuisine_actuelle_coaching_maison_nl",
            "cuisine_actuelle_coaching_patisserie_nl",
            "femme_actuelle_coaching_sante_nl",
            "femme_actuelle_coaching_grossesse_nl",
            "femme_actuelle_coaching_minceur_nl",
            "femme_actuelle_coaching_maison_nl"
         ]
      }
   ]
}
"""

config_consents = purge_deprecated_consent['event_configs']

# every day at 12h12
with CDocDAG(dag_name,
             description='purge profiles sub to deprecated consents',
             doc_md=__doc__,
             schedule_interval="11 11 * * *",
             tags=["temporary", "purge"],
             default_args=default_args,
             max_active_runs=1,
             dagrun_timeout=timedelta(hours=2),
             catchup=False,
             template_searchpath=['/home/<USER>/gcs/data/sql/matrix_email_purge/']) as dag:
    all_consents_public_refs = []
    nbr_events = []
    with TaskGroup("generate_unsub_events", tooltip="Generate Unsub Events.") as generate_unsub_events:

        for index, consent_info in enumerate(config_consents):
            """ Example:
              {
                 "source":"matrix",
                 "medium":"MATRIX-489 - purge inactifs consents",
                 "limit":2,
                 "public_refs":[
                    "ohmymag_oh_my_home_nl",
                    "tele_loisirs_lepass_nl"
                 ]
              },
            
            """
            medium = consent_info.get('medium')
            source = consent_info.get('source')
            limit = consent_info.get('limit', 50_000)
            public_refs = consent_info.get('public_refs')

            all_consents_public_refs += public_refs

            deprecated_consents_str = "'" + "','".join(public_refs) + "'"

            generate_unsub_from_deprecated_consents = BigQueryExecuteQueryOperator(
                task_id=f'generate_unsub_from_deprecated_consents_{index}',
                sql='1_generate_unsub_from_deprecated_consents.sql',
                use_legacy_sql=False,
                destination_dataset_table='temp.unsub_from_deprecated_consents_{{ next_execution_date.strftime("%Y%m%d%H") }}_' + str(
                    index),
                write_disposition='WRITE_TRUNCATE',
                params={
                    'limit': limit,
                    'medium': medium,
                    'source': source,
                    'deprecated_consents': deprecated_consents_str,
                    'bq_project': bq_project
                }
            )
            generate_unsub_from_deprecated_consents.doc_md = """Generate unsub events for profiles sub to deprecated 
            consents."""

            export_unsub_deprecated_consent = BigQueryToEmailEventOperator(
                task_id=f'export_unsub_deprecated_consent_{index}',
                postgres_conn_id='psql_matrix_email_app',
                database='matrix',
                bigquery_conn_id='bq_matrix',
                destination_cloud_storage_object_path=
                'tmp_exports/{{ next_execution_date.strftime("%Y%m%d") }}/unsub_from_deprecated_consents_{{ next_execution_date.strftime("%Y%m%d") }}_' + str(
                    index) + '.csv',
                import_table='matrix__email_tmp.unsub_from_deprecated_consents_' + str(index),
                source_project_dataset_table='temp.unsub_from_deprecated_consents_{{ next_execution_date.strftime("%Y%m%d%H") }}_' + str(
                    index),
                bucket=gcs_matrix,
            )
            export_unsub_deprecated_consent.doc_md = """Send unsub events to PostgreSQL email_event queue (matrix__email_queue.email_event)."""

            generate_unsub_from_deprecated_consents >> export_unsub_deprecated_consent

    all_deprecated_consents_str = "'" + "','".join(all_consents_public_refs) + "'"

    count_sub_to_deprecated_consents = BigQueryExecuteQueryOperator(
        task_id='count_sub_to_deprecated_consents',
        sql='0_count_sub_to_deprecated_consents.sql',
        destination_dataset_table='temp.count_sub_to_deprecated_consents_{{ next_execution_date.strftime("%Y%m%d%H") }}',
        params={
            'bq_project': bq_project,
            'deprecated_consents': all_deprecated_consents_str
        }
    )
    count_sub_to_deprecated_consents.doc_md = """ count sub profile to deprecated consents"""

    get_count_sub_to_deprecated_consents = BigQueryGetDataOperator(
        task_id='get_count_sub_to_deprecated_consents',
        dataset_id='temp',
        table_id='count_sub_to_deprecated_consents_{{ next_execution_date.strftime("%Y%m%d%H") }}',
        max_results=1000,
        selected_fields='public_ref, count',
    )
    get_count_sub_to_deprecated_consents.doc_md = 'get count value'


    @task(task_id='send_notif')
    def send_alert(**context):
        task_instance = context['ti']
        nbr_profiles_by_consent = task_instance.xcom_pull(task_ids=['get_count_sub_to_deprecated_consents'])[0]
        print('nbr_profiles_by_consent: ' + str(nbr_profiles_by_consent))
        # [
        #   ['femme_actuelle_jeux_reflexion_nl', '649995'],
        #   ['gentside_voyage_nl', '491059'],
        #   ['cuisine_actuelle_passion_apero_nl', '457883'],
        #   ['gala_break_nl', '260126']
        # ]
        count_generated_events = 0

        for ind in range(len(config_consents)):
            task_instance = context['ti']
            task_id = f'generate_unsub_events.export_unsub_deprecated_consent_{ind}'
            print('nbr event : ' + str(task_instance.xcom_pull(task_ids=[task_id])))

            count_generated_events += int(task_instance.xcom_pull(task_ids=[task_id])[0])


        if len(nbr_profiles_by_consent) == 0:
            return []
        wording_profiles = """<br><b>Nombre de profils ayant souscrit à des consentements obsolètes avant ce traitement</b>:"""

        wording_consent = ''
        nbr_consent = []
        for count_consent in nbr_profiles_by_consent:
            tmp_wording = """<br> - <b>{consent}</b> :  {nbr_profiles} profils """.format(
                consent=str(count_consent[0]),
                nbr_profiles="{:_}".format(int(count_consent[1]))
            )
            nbr_consent.append(int(count_consent[1]))
            wording_consent = wording_consent + tmp_wording

        total_unsub_remaining = sum(nbr_consent)
        title = "[Suivi] - Suppression des profils ayant souscrit à des consentements obsolètes"

        body = ("Bonjour,"
                "<br> Nous venons de générer "
                + f"<b>{count_generated_events}</b>"
                + " événements de désinscription pour les profils ayant souscrit à des consentements <b>obsolètes</b>.<br>"
                + "Ci-dessous, vous trouverez le récapitulatif: <br>"
                + wording_profiles
                + wording_consent
                + "<br>Nombre total de profils à désabonner : " + f"<b>{total_unsub_remaining}</b>"
                + "<br>"
                  "<br> Cordialement,"
                  f"<br> Mozart (env= {env}) <br> ")

        alert_to = purge_deprecated_consent['alert_to']
        alert_cc = []
        # if env != 'prod':
        #    alert_to = ['<EMAIL>']
        #    alert_cc = []
        send_email(to=alert_to, subject=title, html_content=body, cc=alert_cc, mime_charset='utf-8',
                   conn_id='sendgrid_default')


    send_alert.doc_md = 'send notification'
    generate_unsub_events >> count_sub_to_deprecated_consents >> get_count_sub_to_deprecated_consents >> send_alert()
