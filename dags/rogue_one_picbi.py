# gsutil cp production/dags/rogue_one_picbi.py gs://europe-west1-mozart-cluster-4128dbb8-bucket/dags/

from datetime import datetime, timedelta, time

from airflow import DAG
from airflow.models import Variable
from console_plugin.operators.http_console import HttpConsoleOperator

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'start_date': datetime(2019, 8, 20, 9, 0),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': True,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=5),

    # http connection id
    'http_conn_id': 'http-rogue-one'
}


def _time_branch(execution_date, **_):
    if time(2, 0) < execution_date.time() < time(3, 0):
        return 'exports_at_2am'
    elif time(8, 0) < execution_date.time() < time(9, 0):
        return 'sync_stat_invoice_at_8am'
    else:
        return 'do_nothing'


with DAG('rogue_one_picbi',
         tags=["app-data"],
         schedule_interval="10 * * * *",
         catchup=False,
         max_active_runs=1,
         dagrun_timeout=timedelta(hours=1),
         default_args=default_args,
         template_searchpath=['/home/<USER>/gcs/data/sql/rogue_one/']) as dag:
    buckets = Variable.get("gcs_bucket_names", deserialize_json=True)

    t1 = HttpConsoleOperator(task_id='sync_picbi_society_new', command='sync-picbi-society --sync-new')
    t2 = HttpConsoleOperator(task_id='sync_picbi_society_modify', command='sync-picbi-society --sync-modify')
    t3 = HttpConsoleOperator(command='sync-picbi-actor')

    t1 >> t2 >> t3
