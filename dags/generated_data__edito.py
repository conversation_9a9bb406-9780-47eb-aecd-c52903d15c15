r"""
**PURPOSE:**

This DAG compute monthly (on start month) generated data from edito contents (bookmarks and alerts)**at 00:**00 UTC+2.
Data comes from bookmarks, alerts and person tables stored into pm-prod-mirror.refined_data dataset.
The main goal of this DAG is to generate new fields as kpis from bookmark, alerts and person contents.

**METHODOLOGY:**

We compute same KPIs at tag scale,
**using the following tables:**
**Edito data:**- `pm-prod-mirror.refined_data.bookmark`
- `pm-prod-mirror.refined_data.alert`
- `pm-prod-mirror.refined_data.person`
- `pm-prod-mirror.store_one_person.personBiography`**Splio tracking:**- `pm-prod-matrix.refined_data.splio_report`**Tmail:**- `pm-prod-matrix.refined_data.tmail_alert`**Tags:**- `pm-prod-matrix.store_one_article.tag`

**COMMAND TO TEST:** 

    - gsutil -m cp -R dags/generated_data__edito.py gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/ 
    - gsutil -m cp -R data/json/generated_data__edito.json gs://europe-west1-preprod-mozart-2fa49086-bucket/data/json/ 
    - gsutil -m cp -R data/sql/generated_data/edito/*  gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/generated_data/edito/ 

**NB:**

This DAG will be affected by refined_data.splio_report/tmail_alert and refined_data.bookmark/person/alert
"""

import os
import json
from datetime import timedelta, datetime
from airflow.decorators import task
from airflow.providers.common.sql.sensors.sql import SqlSensor
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.models import Variable
from collapsible_doc_dag import CDocDAG
import itdata_plugin


dag_name = 'generated_data__edito'
email_on_failure = True
bq_project_mirror = 'pm-prod-mirror'
bq_project_matrix = 'pm-prod-matrix'
bq_project_ga4 = 'pm-prod-ga4'

# read config from json file
f = open('/home/<USER>/gcs/data/json/generated_data__edito.json')
generated_data__edito = json.load(f)
f.close()

# ------------- FOR PREPROD ENV -----------------
env = os.environ.get("ENV")
if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    bq_project_mirror = 'pm-preprod-mirror'
    bq_project_matrix = 'pm-preprod-matrix'
    bq_project_ga4 = 'pm-preprod-ga4'
# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2021, 9, 29, 00, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=3),
    'sla': None,
    'email_on_retry': False,
    'retries': 0,
    'retry_delay': timedelta(minutes=1),
    'depends_on_past': False,
    # BQ CONNECTION
    'gcp_conn_id': 'bq_matrix',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',
    'use_legacy_sql': False
}

with CDocDAG(
    dag_name,
    description="Generate kpis to track stars following evolution monthly per brand",
    tags=["generated", "edito"],
    doc_md=__doc__,
    schedule_interval="40 8 * * *",
    default_args=default_args,
    max_active_runs=1,
    dagrun_timeout=timedelta(minutes=60*4),
    catchup=False,
    template_searchpath=['/home/<USER>/gcs/data/sql/generated_data/']
) as dag:

    @task
    def variable_set():
        """
        This task reads the configs inside the `last_activity_web` variable (from a json file)
        and updates `last_activity_web` Airflow variable
        """
        itdata_plugin.set_variable_in_secret_manager(secret_name="last_activity_web", secret_value=generated_data__edito, serialize_json=True)

    # generate follow tags kpis per month per brand per tag
    task_doc_md = """
    compute follow tags kpis per month per brand per tag and store the result into pm-prod-mirror.generated_data.follow_tag_kpi_brand_monthly.<br />
    """
    follow_tags_kpis = BigQueryExecuteQueryOperator(
        task_id='follow_tags_kpis',
        sql='edito/follow_tag_kpi_brand_monthly.sql',
        params={
            'bq_project': {'mirror': bq_project_mirror, 'matrix': bq_project_matrix}
        }
    )
    follow_tags_kpis.doc_md = task_doc_md

    # generate follow tags kpis per month per brand per tag
    task_doc_md = """
    compute follow tags volume of followers, optin and optout by brand by date for datastudio.<br />
    """
    follow_star_optin = BigQueryExecuteQueryOperator(
        task_id='follow_star_optin',
        sql='edito/follow_star_optin.sql',
        params={
            'bq_project': {'mirror': bq_project_mirror, 'matrix': bq_project_matrix}
        }
    )
    follow_star_optin.doc_md = task_doc_md

    task_doc_md = """
    We need to wait for refined bookmark to continue.
    """
    wait_for_refined_bookmark = SqlSensor(
        task_id='wait_for_refined_bookmark',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 180,  # 3 hours
        mode="reschedule",
        sql="""
                SELECT CASE WHEN state = 'success' THEN TRUE ELSE FALSE END
                FROM task_instance
                WHERE dag_id = 'refined_data__edito{str_env}'
                  AND task_id = 'refine_bookmark_contents'
                  AND DATE(start_date) = CURRENT_DATE
            """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_refined_bookmark.doc_mc = task_doc_md

    task_doc_md = """
    Compute profile status (alive, new sub, new unsub) for by bookmark
    """
    profile_bookmark_content_status = BigQueryExecuteQueryOperator(
        task_id='profile_bookmark_content_status',
        sql='edito/profile_bookmark_content_status.sql',
        params={
            'bq_project': bq_project_mirror,
            'is_full': generated_data__edito.get('profile_bookmark_content_status').get('is_full')
        }
    )
    profile_bookmark_content_status.doc_md = task_doc_md

    task_doc_md = """
    Compute active and inactive bookmark volume by profile and brand
    """
    active_bookmark_by_profile = BigQueryExecuteQueryOperator(
        task_id='active_bookmark_by_profile',
        sql='edito/active_bookmark_by_profile.sql',
        params={
            'bq_project': bq_project_mirror
        }
    )
    active_bookmark_by_profile.doc_md = task_doc_md

    # generate follow tags kpis per month per brand per tag
    task_doc_md = """
    compute last web activity global and by brand by pmc profile master id.<br />
    """
    last_activity_web = BigQueryExecuteQueryOperator(
        task_id='last_activity_web',
        sql='pmc/last_activity_web.sql',
        params={
            'bq_project': bq_project_matrix,
            'ga4_project':bq_project_ga4,
            'full_export': generated_data__edito.get("full_export"),
            'interval': generated_data__edito.get("interval"),
            'start_date': generated_data__edito.get("start_date")
        }
    )
    last_activity_web.doc_md = task_doc_md

    task_doc_md = """
        We need to wait for refined article to continue.
    """
    wait_for_refined_article = SqlSensor(
        task_id='wait_for_refined_article',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 180,  # 3 hours
        mode="reschedule",
        sql="""
                    SELECT CASE WHEN state = 'success' THEN TRUE ELSE FALSE END
                    FROM task_instance
                    WHERE dag_id = 'refined_data__edito{str_env}'
                      AND task_id = 'refine_article_contents'
                      AND DATE(start_date) = CURRENT_DATE
                """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_refined_article.doc_mc = task_doc_md

    # generate article lifecycle
    task_doc_md = """
        generate article lifecycle. Each record in the table represents article status.  
    """
    generate_article_lifecycle = BigQueryExecuteQueryOperator(
        task_id='generate_article_lifecycle',
        sql='edito/generate_article_lifecycle.sql',
        params={
            'bq_project': bq_project_mirror
        }
    )
    generate_article_lifecycle.doc_md = task_doc_md

    variable_set() >> [wait_for_refined_bookmark, follow_tags_kpis, follow_star_optin, last_activity_web]
    wait_for_refined_bookmark >> profile_bookmark_content_status >> active_bookmark_by_profile
    wait_for_refined_article >> generate_article_lifecycle