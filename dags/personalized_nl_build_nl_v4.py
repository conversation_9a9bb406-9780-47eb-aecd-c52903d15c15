r"""
# Personalized NL Build Newsletter using NL-Factory

__PURPOSE:__<br />
Build the newsletter for each profile in the chunk related to the current campaign.

__METHODOLOGY:__<br />
- Get the list of Profile IDs for current main chunk.<br />
- Calculate sub chunks of profiles depending on the sub_chunk_size and the list of profiles we have got.<br />
- Call NL-Factory API to bulk build emails for the current sub chunk.<br />
- html will be saved to NLF bucket and personalized_nl.email table will be updated.<br />
- Call Rogue-One API to bulk track emails for the current sub chunk.<br />
- html will be saved to Rogue-One Personalized NL bucket and personalized_nl.email table will be updated (status=TRACKED).<br />
"""

import os
import logging
from datetime import timedelta
from airflow import AirflowException
from airflow.models import Variable
from airflow.decorators import task
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from psql_plugin.operators.psql_select_many_operator import PostgresSelectManyOperator
import requests as req
from collapsible_doc_dag import CDocDAG
from datetime import datetime
import json

# ----------------- CONFIG ----------------------
ENV = os.environ.get("ENV")
NLF_API_HEADERS = {
    'Content-Type': 'application/json',
    'X-Auth-Identity': 'mozart',
    'X-Auth-Token': Variable.get('personalized_nl_nlf_api_key')
}

R1_API_HEADERS = {
    'Content-Type': 'application/json',
    'X-Auth-Identity': 'mozart',
    'X-Auth-Token': Variable.get('personalized_nl_r1_api_key')
}

dag_name = 'personalized_nl__build_nl_v4'
campaign_workflow_dag_name = 'personalized_nl__campaign_workflow'
email_on_failure = True
config = Variable.get("personalized_nl", deserialize_json=True)
nlf_build_nl_url = 'https://nl-factory.prismadata.fr/api/personalized-newsletter/bulk'
rogue_one_track_nl_url = 'https://rogue-one.prismadata.fr/api/personalized-nl/track'
if ENV != 'prod':
    dag_name = dag_name + '_' + ENV
    campaign_workflow_dag_name = campaign_workflow_dag_name + '_' + ENV
    email_on_failure = False
    nlf_build_nl_url = 'https://nl-factory.preprod.prismadata.fr/api/personalized-newsletter/bulk'
    rogue_one_track_nl_url = 'https://rogue-one.preprod.prismadata.fr/api/personalized-nl/track'


def call_api(url, headers, payload):
    """
    Call an API and return the response.
    """
    try:
        response = req.request("POST", url, headers=headers, json=payload, timeout=240)
        if not 200 <= response.status_code <= 299:
            logging.error(f"API call failed. Status: {response.status_code}")
            raise AirflowException(f"API call failed. Status: {response.status_code}")
        if response.status_code == 204 or not response.text.strip():
            # no content to parse
            return None
        json_response = response.json()
        if not 200 <= json_response.get("status") <= 299:
            logging.error(f"API call error: {json_response.get('title')}")
            raise AirflowException(f"API call error: {json_response.get('title')}")
        return json_response
    except req.exceptions.Timeout:
        logging.error("Request has expired.")
        raise AirflowException("Request has expire.")
    except req.exceptions.ConnectionError:
        logging.error("Could not connect to server.")
        raise AirflowException("Could not connect to server.")
    except Exception as e:
        logging.error(f"Unknown Error : {str(e)}")
        raise AirflowException(f"Unknown Error : {str(e)}")

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2024, 5, 1, 2, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'retries': 2,
    'retry_delay': timedelta(seconds=30),
    'execution_timeout': timedelta(minutes=5),
    'email_on_retry': False,
    'depends_on_past': False,
    'wait_for_downstream': False,
}

with CDocDAG(
        dag_name,
        description='Build & Track personalized NL Per profile',
        tags=["nl", "personalized-nl"],
        doc_md=__doc__,
        schedule_interval=None,  # Only manual trigger
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(minutes=20),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/personalized_nl/']
) as dag:
    task_doc_md = """
    Get the list of PMIs for current chunk. <br />
    """
    get_chunk_pmi_task = PostgresSelectManyOperator(
        task_id='get_chunk_pmis',
        postgres_conn_id='psql_personalized_nl_app',
        sql="08_get_process_chunk_pmis.sql"
    )
    get_chunk_pmi_task.doc_md = task_doc_md

    @task
    def generate_sub_chunks(chunk_size, **kwargs):
        """
        Create sub-chunks from a list of IDs.
        """
        ti = kwargs['ti']
        result = ti.xcom_pull(task_ids='get_chunk_pmis')
        rows = json.loads(result)
        if rows:
            ids = [
                row[0]
                for row in rows
            ]

            return [ids[i:i + chunk_size] for i in range(0, len(ids), chunk_size)]
        return []

    def build_nl(campaign_id, chunk):
        """
        Build the newsletter for a single chunk by calling NL-Factory API.
        We use max_active_tis_per_dag to perform rate limiting and not overload API with too many concurrent requests.
        """
        bulk_id = f'{campaign_id}_{chunk[0]}_{chunk[-1]}'
        logging.info(f"Building NL for sub-chunk: {bulk_id} with {len(chunk)} profiles.")

        # Call the build_nl API
        build_payload = {"cid": campaign_id,
                         "start_pmi": chunk[0],
                         "end_pmi": chunk[-1],
                         }
        logging.info(f"Calling build_nl API for {bulk_id}")
        call_api(nlf_build_nl_url, NLF_API_HEADERS, build_payload)
        logging.info(f"Successfully built NL for {bulk_id}")

    def track_nl(campaign_id, chunk):
        """
        Track the newsletter for a single chunk by calling Rogue-One API.
        We use max_active_tis_per_dag to perform rate limiting and not overload API with too many concurrent requests.
        """
        bulk_id = f'{campaign_id}_{chunk[0]}_{chunk[-1]}'
        logging.info(f"Tracking NL for sub-chunk: {bulk_id} with {len(chunk)} profiles.")

        # Call the track_nl API
        track_payload = {"cid": campaign_id,
                         "start_pmi": chunk[0],
                         "end_pmi": chunk[-1],
                         }
        logging.info(f"Calling track_nl API for {bulk_id}")
        call_api(rogue_one_track_nl_url, R1_API_HEADERS, track_payload)
        logging.info(f"Successfully tracked NL for {bulk_id}")

    @task(max_active_tis_per_dag=80, map_index_template="{{ chunk[0] }}__{{ chunk[-1] }}")
    def build_and_track(chunk, **kwargs):
        campaign_id = kwargs['dag_run'].conf['email_campaign_id']
        build_nl(campaign_id, chunk)
        track_nl(campaign_id, chunk)


    trigger_campaign_workflow_task = TriggerDagRunOperator(
        task_id=f'trigger_campaign_workflow_dag',
        trigger_dag_id=campaign_workflow_dag_name,
        conf={
            'email_campaign_id': "{{ dag_run.conf['email_campaign_id'] }}",
        },
        wait_for_completion=False
    )

    # Get the chunk size from the dag run conf
    # This controls the number of profiles per chunk
    sub_chunk_size = config.get('sub_chunk_size', 150)

    # Create sub chunks
    generate_sub_chunks_task = generate_sub_chunks(sub_chunk_size)
    # Build and track the newsletter for each chunk
    build_and_track_tasks = build_and_track.expand(chunk=generate_sub_chunks_task)

    get_chunk_pmi_task >> generate_sub_chunks_task
    build_and_track_tasks >> trigger_campaign_workflow_task
