# gsutil cp dags/matrix__email_datahub_007_splio.py gs://europe-west1-mozart-cluster-fdc38c29-bucket/dags/
# gsutil -m cp -R data/sql/matrix_email/*  gs://europe-west1-mozart-cluster-fdc38c29-bucket/data/sql/matrix_email/

# gsutil cp plugins/export.py gs://europe-west1-mozart-cluster-fdc38c29-bucket/plugins/
# gsutil  -m cp -R plugins/export_plugin/operators/* gs://europe-west1-mozart-cluster-fdc38c29-bucket/plugins/export_plugin/operators/

import os
from datetime import timedelta, datetime

from airflow import DAG
from airflow.models import Variable
from export_plugin.operators.export_splio_bigquery_operator import ExportSplioBigQueryOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryCheckOperator

dag_name = 'matrix__email_datahub_007_splio'

email_on_failure = True
bq_project = 'pm-prod-matrix'
gcs_matrix_uri = 'gs://it-data-prod-matrix-pipeline'
# ------------- FOR PREPROD ENV -----------------
env = os.environ.get("ENV")
if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    bq_project = 'pm-preprod-matrix'
    gcs_matrix_uri = 'gs://it-data-preprod-matrix-preprod-pipeline'
# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2020, 11, 15, 00, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=3),
    'sla': None,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'depends_on_past': False,

    # PSQL Connection
    'postgres_conn_id': 'psql_matrix_email_app',
    'database': 'matrix',
    'gcp_conn_id': 'gcs_matrix',
    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_TRUNCATE',
    'allow_large_results': True,
    'flatten_results': False,
}

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']

datahub_contact_007 = Variable.get("datahub_contact_007", deserialize_json=True)
splio_universes_sftp = Variable.get("splio_universes_sftp", deserialize_json=True)
splio_universes_all = Variable.get("splio_universes_all", deserialize_json=True)
splio_universes = {u: splio_universes_all[u] for u in datahub_contact_007['universes'] if u in splio_universes_all}

with DAG(dag_name,
         schedule_interval="20 5 * * *",
         default_args=default_args,
         max_active_runs=1,
         dagrun_timeout=timedelta(hours=2),
         catchup=False,
         tags=["datahub"],
         template_searchpath=['/home/<USER>/gcs/data/sql/matrix_email/',  '/home/<USER>/gcs/data/scripts/']) as dag:
    # check table generated_date.segmentation_tag isn't empty ?
    check_segmentation_tag_table = BigQueryCheckOperator(
        task_id='check_segmentation_tag_table',
        sql='0_check_snapshot_table_datahub_007.sql',
    )

    export_splio_datahub_007 = ExportSplioBigQueryOperator(
        task_id='export_splio_datahub_007',
        sql='11_export_splio_datahub_007.sql',
        sql_after_export='12_store_prepare_datahub_007.sql',
        params={
            'interval': datahub_contact_007["interval"],
            'universe_id': '{universe_id}',
            'bq_project': bq_project,
        },
        destination_dataset_table_temporary='temp.{universe}_contacts_007_{splio_sequence}',
        universes=splio_universes,
        universes_sftp=splio_universes_sftp,
        datahub_file_template='{universe}_contacts_007_{splio_sequence}*.csv',
        splio_sequence='{{ next_execution_date.strftime("%Y%m%d_%H%M") }}',
        bucket=matrix_bucket,
        bucket_path='export_splio/datahub_007/{{ next_execution_date.strftime("%Y%m%d_%H%M") }}/',
        destination_cloud_storage_path_uris=gcs_matrix_uri,
        splio_remote_dir='imports/',
        flux_ref=7,
        bq_project=bq_project,
    )

    check_segmentation_tag_table >> export_splio_datahub_007
