r"""
**PURPOSE:**

This dag is used to compute Email kpis based on their reactivity.

**METHODOLOGY:**

**COMMAND TO TEST:** 

-**Preprod:**gsutil cp -R data/sql/business_data/email/*  gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/business_data/email/
gsutil cp    dags/business_data__email.py gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/
gsutil cp -R data/json/preprod/business_data__email.json gs://europe-west1-preprod-mozart-2fa49086-bucket/data/json/preprod/**Prod:**gsutil cp -R data/sql/business_data/email/*  gs://europe-west1-prod-mozart-co-2ef7e904-bucket/data/sql/business_data/email/
gsutil cp -R dags/business_data__email.py gs://europe-west1-prod-mozart-co-2ef7e904-bucket/dags/
gsutil cp -R data/json/prod/business_data__email.json gs://europe-west1-prod-mozart-co-2ef7e904-bucket/data/json/prod/
"""
import os
import json
from airflow.decorators import task
from datetime import timedelta, datetime, date
from airflow.models import Variable
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.operators.python import BranchPythonOperator
from airflow.operators.empty import EmptyOperator
from airflow.providers.common.sql.sensors.sql import SqlSensor
from collapsible_doc_dag import CDocDAG
from env_plugin.EnvironmentVariableManager import EnvironmentVariableManager
import itdata_plugin

# ----------------- CONFIG ----------------------
env = os.environ.get("ENV")
dag_name = "business_data__email" if env == "prod" else "business_data__email_{}".format(env)
bq_project = "pm-{}-matrix".format(env)
email_on_failure = True if env == "prod" else False


# Load JSON configurations
def load_json(**kwargs):
    """
    Load json file.
    :param kwargs: "input_file"
    :return: nested json
    """
    input_file = kwargs["input_file"]
    with open(input_file) as json_file:
        json_data = json.load(json_file)
    return json_data


file_path = "/home/<USER>/gcs/data/json/{}/business_data__email.json".format(env)
business_data__email = load_json(input_file=file_path)


@task
def variable_set():
    """
    This task reads the configs inside the `business_data__email` variable (from a json file)
    and updates `business_data__email` Airflow variable
    """
    itdata_plugin.set_variable_in_secret_manager(secret_name="business_data__email", secret_value=business_data__email, serialize_json=True)


def _date_branch_pandora_ltv_monthly(**kwargs):
    # get current date
    current_date = date.today()
    if current_date == current_date.replace(day=1):
        return 'wait_for_pandora_profile_activity_by_consent'
    else:
        return 'do_nothing'


def _date_branch_crm_transition_duration(**kwargs):
    # get current date
    current_date = date.today()
    if current_date == current_date.replace(day=1):
        return 'start_crm_monthly_tasks'
    else:
        return 'do_nothing'


default_args = {
    'owner': 'airflow',
    'start_date': datetime(2023, 1, 25, 00, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': False,
    'provide_context': True,
    'execution_timeout': timedelta(hours=4),
    'sla': None,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=5),
    'depends_on_past': False,

    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_APPEND',
    # GCP CONNECTION
    'gcp_conn_id': 'gcs_matrix',
    # PSQL Connection
    'postgres_conn_id': 'psql_matrix_email_app',

    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',  # possibles values: INTERACTIVE and BATCH.

    'dataset_suffix': EnvironmentVariableManager.get_clone_config(dag_name)  # possibles values: '' and '_clone'
}

with CDocDAG(
        dag_name,
        description='Compute profile email stats',
        tags=["business", "email"],
        doc_md=__doc__,
        schedule_interval="0 8 * * *",
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=4),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/business_data/email/'],
        concurrency=10
) as dag:
    task_doc_md = """
    Compute email profile indicators daily
    """
    email_indicators_history = BigQueryExecuteQueryOperator(
        task_id="email_indicators_history",
        sql="email_indicators_history.sql",
        params={
            'bq_project': bq_project,
            'is_full': business_data__email.get('email_indicators_history').get('is_full'),
            'start_date': business_data__email.get('email_indicators_history').get('start_date')
        }
    )
    email_indicators_history.doc_md = task_doc_md

    task_doc_md = """
    We need to wait for email_reactivity_by_base to generate activity KPIs as below 👇🏻.
    """
    # @Doc --> https://airflow.readthedocs.io/en/1.9.0/_modules/airflow/operators/sensors.html
    wait_for_email_reactivity_by_base = SqlSensor(
        task_id='wait_for_email_reactivity_by_base',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                SELECT CASE WHEN state = 'success' THEN TRUE ELSE FALSE END
                FROM task_instance
                WHERE dag_id = 'generated_data__email{str_env}'
                  AND task_id = 'compute_reactivity.email_reactivity_by_base'
                  AND DATE(start_date) = CURRENT_DATE
            """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_email_reactivity_by_base.doc_mc = task_doc_md

    task_doc_md = """
    We need to wait for last activity by base to generate activity KPIs as below 👇🏻.
    """
    # @Doc --> https://airflow.readthedocs.io/en/1.9.0/_modules/airflow/operators/sensors.html
    wait_for_email_reactivity_by_brand = SqlSensor(
        task_id='wait_for_email_reactivity_by_brand',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                SELECT CASE WHEN (task_id = 'email_reactivity_by_brand' AND state = 'success') THEN TRUE ELSE FALSE END
                FROM task_instance
                WHERE dag_id = 'generated_data__email{str_env}'
                  AND task_id = 'email_reactivity_by_brand'
                  AND DATE(start_date) = CURRENT_DATE
            """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_email_reactivity_by_brand.doc_mc = task_doc_md

    task_doc_md = """
    Compute active profile based on 'open reactivity' on the last 90 days by consent & cross-consent.
    """
    cross_consent_reactivity = BigQueryExecuteQueryOperator(
        task_id="cross_consent_reactivity",
        sql="cross_consent_reactivity.sql",
        params={
            'bq_project': bq_project
        }
    )
    cross_consent_reactivity.doc_md = task_doc_md

    task_doc_md = """
    Compute active profile based on 'open reactivity' on the last 90 days by brand & cross-brand.
    """
    cross_brand_reactivity = BigQueryExecuteQueryOperator(
        task_id="cross_brand_reactivity",
        sql="cross_brand_reactivity.sql",
        params={
            'bq_project': bq_project
        }
    )
    cross_brand_reactivity.doc_md = task_doc_md

    task_doc_md = """
    We need to wait for refined_data organic acquisition.
    """
    # @Doc --> https://airflow.readthedocs.io/en/1.9.0/_modules/airflow/operators/sensors.html
    wait_for_refine_organic_acquisition = SqlSensor(
        task_id='wait_for_refine_organic_acquisition',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                SELECT CASE WHEN (task_id = 'refine_organic_acquisition' AND state = 'success') THEN TRUE ELSE FALSE END
                FROM task_instance
                WHERE dag_id = 'refined_data__email{str_env}'
                  AND task_id = 'refine_organic_acquisition'
                  AND DATE(start_date) = CURRENT_DATE
            """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_refine_organic_acquisition.doc_mc = task_doc_md

    task_doc_md = """
    Compute business_data for organic acquisition by brand
    """
    organic_acquisition_monthly_kpi_by_brand = BigQueryExecuteQueryOperator(
        task_id='organic_acquisition_monthly_kpi_by_brand',
        sql='organic_acquisition_monthly_kpi_by_brand.sql',
        params={
            'bq_project': bq_project,
            'is_full': business_data__email.get('organic_acquisition_monthly_kpi').get('is_full'),
            'start_date': business_data__email.get('organic_acquisition_monthly_kpi').get('start_date')
        }
    )
    organic_acquisition_monthly_kpi_by_brand.doc_md = task_doc_md

    task_doc_md = """
    Compute business_data for monthly organic acquisition by consent
    """
    organic_acquisition_monthly_kpi_by_consent = BigQueryExecuteQueryOperator(
        task_id='organic_acquisition_monthly_kpi_by_consent',
        sql='organic_acquisition_monthly_kpi_by_consent.sql',
        params={
            'bq_project': bq_project,
            'is_full': business_data__email.get('organic_acquisition_monthly_kpi').get('is_full'),
            'start_date': business_data__email.get('organic_acquisition_monthly_kpi').get('start_date')
        }
    )
    organic_acquisition_monthly_kpi_by_consent.doc_md = task_doc_md

    task_doc_md = """
    Compute business_data for organic acquisition by consent
    """
    organic_acquisition_kpi_by_consent = BigQueryExecuteQueryOperator(
        task_id='organic_acquisition_kpi_by_consent',
        sql='organic_acquisition_kpi_by_consent.sql',
        params={
            'bq_project': bq_project,
            'is_full': business_data__email.get('organic_acquisition_kpi').get('is_full'),
            'start_date': business_data__email.get('organic_acquisition_kpi').get('start_date'),
            'end_date': business_data__email.get('organic_acquisition_kpi').get('end_date')
        }
    )
    organic_acquisition_kpi_by_consent.doc_md = task_doc_md

    # check if it's the first day of month
    task_doc_md = """
        check date to update lifetime_value<br />
        """
    check_date = BranchPythonOperator(
        task_id="check_date", python_callable=_date_branch_pandora_ltv_monthly)
    check_date.doc_md = task_doc_md

    do_nothing = EmptyOperator(task_id='do_nothing')
    do_nothing.doc_md = 'do_nothing'

    start_crm_monthly_tasks = EmptyOperator(task_id='start_crm_monthly_tasks')
    start_crm_monthly_tasks.doc_md = 'start crm monthly tasks'

    task_doc_md = """
    We need to wait for generated data pandora_profile_activity.
    """
    wait_for_pandora_profile_activity_by_consent = SqlSensor(
        task_id='wait_for_pandora_profile_activity_by_consent',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                SELECT CASE WHEN state = 'success' THEN TRUE ELSE FALSE END
                FROM task_instance
                WHERE dag_id = 'generated_data__email{str_env}'
                  AND task_id = 'pandora_profile_activity_by_consent'
                  AND DATE(start_date) = CURRENT_DATE
            """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_pandora_profile_activity_by_consent.doc_mc = task_doc_md

    task_doc_md = """
    Compute profile lifetime value (ltv) by brand and acquisition month.
    """
    compute_pandora_profile_ltv_by_brand_acquisition = BigQueryExecuteQueryOperator(
        task_id='compute_pandora_profile_ltv_by_brand_acquisition',
        sql='pandora_ltv/pandora_profile_ltv_by_brand_acquisition.sql',
        params={
            'bq_project': bq_project
        }
    )
    compute_pandora_profile_ltv_by_brand_acquisition.doc_md = task_doc_md

    task_doc_md = """
    Compute profile lifetime value (ltv) monthly by brand.
    """
    compute_pandora_profile_ltv_by_brand_monthly = BigQueryExecuteQueryOperator(
        task_id='compute_pandora_profile_ltv_by_brand_monthly',
        sql='pandora_ltv/pandora_profile_ltv_by_brand_monthly.sql',
        params={
            'bq_project': bq_project
        }
    )
    compute_pandora_profile_ltv_by_brand_monthly.doc_md = task_doc_md

    task_doc_md = """
    Compute profile lifetime value (ltv) by consent and acquisition month.
    """
    compute_pandora_profile_ltv_by_consent_acquisition = BigQueryExecuteQueryOperator(
        task_id='compute_pandora_profile_ltv_by_consent_acquisition',
        sql='pandora_ltv/pandora_profile_ltv_by_consent_acquisition.sql',
        params={
            'bq_project': bq_project
        }
    )
    compute_pandora_profile_ltv_by_consent_acquisition.doc_md = task_doc_md

    task_doc_md = """
    Compute profile lifetime value (ltv) monthly by consent.
    """
    compute_pandora_profile_ltv_by_consent_monthly = BigQueryExecuteQueryOperator(
        task_id='compute_pandora_profile_ltv_by_consent_monthly',
        sql='pandora_ltv/pandora_profile_ltv_by_consent_monthly.sql',
        params={
            'bq_project': bq_project
        }
    )
    compute_pandora_profile_ltv_by_consent_monthly.doc_md = task_doc_md

    task_doc_md = """
    Compute profile lifetime value (ltv) by acquisition month.
    """
    compute_pandora_profile_ltv_global_acquisition = BigQueryExecuteQueryOperator(
        task_id='compute_pandora_profile_ltv_global_acquisition',
        sql='pandora_ltv/pandora_profile_ltv_global_acquisition.sql',
        params={
            'bq_project': bq_project
        }
    )
    compute_pandora_profile_ltv_global_acquisition.doc_md = task_doc_md

    task_doc_md = """
    Compute profile lifetime value (ltv) monthly.
    """
    compute_pandora_profile_ltv_global_monthly = BigQueryExecuteQueryOperator(
        task_id='compute_pandora_profile_ltv_global_monthly',
        sql='pandora_ltv/pandora_profile_ltv_global_monthly.sql',
        params={
            'bq_project': bq_project
        }
    )
    compute_pandora_profile_ltv_global_monthly.doc_md = task_doc_md

    task_doc_md = """
    We need to wait for refined sent event to compute campaign Kpi's for email scope.
    """
    # @Doc --> https://airflow.readthedocs.io/en/1.9.0/_modules/airflow/operators/sensors.html
    wait_for_refined_sent = SqlSensor(
        task_id='wait_for_refined_sent',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                SELECT CASE WHEN (task_id = 'refine_sent_events' AND state = 'success') THEN TRUE ELSE FALSE END
                FROM task_instance
                WHERE dag_id = 'refined_data__email{str_env}'
                  AND task_id =  'refine_sent_events'
                  AND DATE(start_date) = CURRENT_DATE
            """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_refined_sent.doc_mc = task_doc_md

    task_doc_md = """
    We need to wait for refined fai complaints.
    """
    # @Doc --> https://airflow.readthedocs.io/en/1.9.0/_modules/airflow/operators/sensors.html
    wait_for_refined_fai_complaints = SqlSensor(
        task_id='wait_for_refined_fai_complaints',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                SELECT CASE WHEN (task_id = 'splio_fai_complaints_stats_daily' AND state = 'success') THEN TRUE ELSE FALSE END
                FROM task_instance
                WHERE dag_id = 'refined_data__email{str_env}'
                  AND task_id =  'splio_fai_complaints_stats_daily'
                  AND DATE(start_date) = CURRENT_DATE
            """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_refined_fai_complaints.doc_mc = task_doc_md

    task_doc_md = """
    We need to wait for refined mail complaints.
    """
    # @Doc --> https://airflow.readthedocs.io/en/1.9.0/_modules/airflow/operators/sensors.html
    wait_for_refined_mail_complaints = SqlSensor(
        task_id='wait_for_refined_mail_complaints',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                SELECT CASE WHEN (task_id = 'splio_mail_complaints_stats_daily' AND state = 'success') THEN TRUE ELSE FALSE END
                FROM task_instance
                WHERE dag_id = 'refined_data__email{str_env}'
                  AND task_id =  'splio_mail_complaints_stats_daily'
                  AND DATE(start_date) = CURRENT_DATE
            """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_refined_mail_complaints.doc_mc = task_doc_md

    task_doc_md = """
    We need to wait for merge_refined_open to compute campaign Kpi's for email scope.
    """
    # @Doc --> https://airflow.readthedocs.io/en/1.9.0/_modules/airflow/operators/sensors.html
    wait_for_merge_refined_open = SqlSensor(
        task_id='wait_for_merge_refined_open',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                SELECT CASE WHEN state = 'success' THEN TRUE ELSE FALSE END
                FROM task_instance
                WHERE dag_id = 'refined_data__email{str_env}'
                  AND task_id = 'refine_open.merge_refined_open'
                  AND DATE(start_date) = CURRENT_DATE
            """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_merge_refined_open.doc_mc = task_doc_md

    task_doc_md = """
    We need to wait for refined click event to compute campaign Kpi's for email scope.
    """
    # @Doc --> https://airflow.readthedocs.io/en/1.9.0/_modules/airflow/operators/sensors.html
    wait_for_refined_click = SqlSensor(
        task_id='wait_for_refined_click',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                SELECT CASE WHEN state = 'success' THEN TRUE ELSE FALSE END
                FROM task_instance
                WHERE dag_id = 'refined_data__email{str_env}'
                  AND task_id = 'refine_click.merge_refined_click'
                  AND DATE(start_date) = CURRENT_DATE
            """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_refined_click.doc_mc = task_doc_md

    task_doc_md = """
    We need to wait for refined Splio report to compute campaign Kpi's for email scope.
    """
    # @Doc --> https://airflow.readthedocs.io/en/1.9.0/_modules/airflow/operators/sensors.html
    wait_for_refined_splio_report = SqlSensor(
        task_id='wait_for_refined_splio_report',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                SELECT CASE WHEN (task_id = 'refine_splio_report' AND state = 'success') THEN TRUE ELSE FALSE END
                FROM task_instance
                WHERE dag_id = 'refined_data__email{str_env}'
                  AND task_id = 'refine_splio_report'
                  AND DATE(start_date) = CURRENT_DATE
            """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_refined_splio_report.doc_mc = task_doc_md

    task_doc_md = """
    Compute VNO (pm-b2b-prisme universe) Kpi's by NIM (Kiosque/Point Relais) on email campaigns.
    """
    compute_email_kpis_by_nim = BigQueryExecuteQueryOperator(
        task_id='compute_email_kpis_by_nim',
        sql='compute_email_kpis_by_nim.sql',
        params={
            'bq_project': bq_project,
            'is_full': business_data__email.get("vno_kpi").get("is_full"),
            'interval': business_data__email.get("vno_kpi").get("interval")
        }
    )
    compute_email_kpis_by_nim.doc_md = task_doc_md

    task_doc_md = """
    We need to wait for generated data Pandora profile activation NL Shopping Splio.
    """
    # @Doc --> https://airflow.readthedocs.io/en/1.9.0/_modules/airflow/operators/sensors.html
    wait_for_pandora_profile_activation_nl_shopping = SqlSensor(
        task_id='wait_for_pandora_profile_activation_nl_shopping',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                SELECT CASE WHEN (task_id = 'pandora_activation_nl_shopping' AND state = 'success') THEN TRUE ELSE FALSE END
                FROM task_instance
                WHERE dag_id = 'generated_data__email{str_env}'
                  AND task_id = 'pandora_activation_nl_shopping'
                  AND DATE(start_date) = CURRENT_DATE
            """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_pandora_profile_activation_nl_shopping.doc_mc = task_doc_md

    task_doc_md = """
    Compute VNO (pm-b2b-prisme universe) Kpi's by NIM (Kiosque/Point Relais) on email campaigns.
    """
    pandora_activation_nl_shopping = BigQueryExecuteQueryOperator(
        task_id='pandora_activation_nl_shopping',
        sql='pandora_activation_nl_shopping.sql',
        params={
            'bq_project_matrix': bq_project,
            'is_full': business_data__email.get("pandora_activation_nl_shopping").get("is_full"),
            'start_date': business_data__email.get("pandora_activation_nl_shopping").get("start_date"),
            'end_date': business_data__email.get("pandora_activation_nl_shopping").get("end_date"),
            'time_interval': business_data__email.get("pandora_activation_nl_shopping").get("time_interval")
        }
    )
    pandora_activation_nl_shopping.doc_md = task_doc_md

    task_doc_md = """
    Build business data for heatmap NL
    """
    heatmap_nl_block_click = BigQueryExecuteQueryOperator(
        task_id='heatmap_nl_block_click',
        sql='heatmap_nl_block_click.sql',
        params={
            'bq_project': bq_project,
            'truncate_table': business_data__email.get("heatmap_nl_block_click").get("truncate_table"),
            'is_full': business_data__email.get("heatmap_nl_block_click").get("is_full"),
            'start_date': business_data__email.get("heatmap_nl_block_click").get("start_date"),
            'end_date': business_data__email.get("heatmap_nl_block_click").get("end_date"),
            'time_interval': business_data__email.get("heatmap_nl_block_click").get("time_interval")
        }
    )
    heatmap_nl_block_click.doc_md = task_doc_md

    subject_email_params = {
        'bq_project': bq_project,
        'partial': business_data__email.get("email_subject_stats").get("partial"),
        'start_date': business_data__email.get("email_subject_stats").get("start_date"),
        'end_date': business_data__email.get("email_subject_stats").get("end_date"),
        'time_interval': business_data__email.get("email_subject_stats").get("time_interval")
    }

    task_doc_md = """
        Build business data for ARPU revenue by NL
        """
    arpu_revenue_by_nl = BigQueryExecuteQueryOperator(
        task_id='arpu_revenue_by_nl',
        sql='arpu_revenue_by_nl.sql',
        params={
            'bq_project': bq_project,
            'is_full': business_data__email.get("arpu").get("is_full"),
            'start_date': business_data__email.get("arpu").get("start_date"),
            'end_date': business_data__email.get("arpu").get("end_date"),
            'time_interval': business_data__email.get("arpu").get("time_interval")
        }
    )
    arpu_revenue_by_nl.doc_md = task_doc_md

    task_doc_md = """
        Build business data for ARPU revenue by acquisition partners
        """
    arpu_revenue_acquisition = BigQueryExecuteQueryOperator(
        task_id='arpu_revenue_acquisition',
        sql='arpu_revenue_by_acquisition_partner.sql',
        params={
            'bq_project': bq_project,
            'is_full': business_data__email.get("arpu").get("is_full"),
            'start_date': business_data__email.get("arpu").get("start_date"),
            'end_date': business_data__email.get("arpu").get("end_date"),
            'time_interval': business_data__email.get("arpu").get("time_interval")
        }
    )
    arpu_revenue_acquisition.doc_md = task_doc_md

    task_doc_md = """
            Build business data for ARPU revenue by acquisition partners
            """
    monitor_autocorrect_daily = BigQueryExecuteQueryOperator(
        task_id='monitor_autocorrect_daily',
        sql='monitor_autocorrect_daily.sql',
        params={
            'bq_project': bq_project
        }
    )
    monitor_autocorrect_daily.doc_md = task_doc_md

    task_doc_md = """
    Build daily business data for top object dashboard
    """

    # Daily Email Subject Stats
    email_subject_stats_daily = BigQueryExecuteQueryOperator(
        task_id='email_subject_stats_daily',
        sql='email_subject_stats_daily.sql',
        params=subject_email_params
    )

    email_subject_stats_daily.doc_md = task_doc_md

    task_doc_md = """
    Build daily FAI complaints stats to refined data upload
    """

    # Daily Email Subject Stats
    splio_fai_complaints_stats_by_brand = BigQueryExecuteQueryOperator(
        task_id='splio_fai_complaints_stats_by_brand',
        sql='splio_fai_complaints_stats_by_brand.sql',
        params=subject_email_params
    )

    splio_fai_complaints_stats_by_brand.doc_md = task_doc_md

    task_doc_md = """
    Build daily email complaints stats to business data upload
    """

    # Daily Email Complaints stats
    splio_mail_complaints_stats_by_brand = BigQueryExecuteQueryOperator(
        task_id='splio_mail_complaints_stats_by_brand',
        sql='splio_mail_complaints_stats_by_brand.sql',
        params=subject_email_params
    )

    splio_mail_complaints_stats_by_brand.doc_md = task_doc_md

    task_doc_md = """
    Build monthly business data for top object dashboard
    """
    # Monthly Email Subject Stats
    email_subject_stats_monthly = BigQueryExecuteQueryOperator(
        task_id='email_subject_stats_monthly',
        sql='email_subject_stats_monthly.sql',
        params=subject_email_params
    )
    email_subject_stats_monthly.doc_md = task_doc_md

    task_doc_md = """
    We need to wait for generated data to complete for the subject stats related tasks.
    """
    # @Doc --> https://airflow.readthedocs.io/en/1.9.0/_modules/airflow/operators/sensors.html
    wait_for_generated_email_profile_subject_stats = SqlSensor(
        task_id='wait_for_generated_email_profile_subject_stats',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
            SELECT CASE WHEN state = 'success' THEN TRUE ELSE FALSE END
            FROM task_instance
            WHERE dag_id = 'generated_data__email{str_env}'
              AND task_id = 'compute_email_profile_subject_stats'
              AND DATE(start_date) = CURRENT_DATE
        """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_generated_email_profile_subject_stats.doc_mc = task_doc_md

    task_doc_md = """
    Generate splio business data from splio report.
    """

    # Define parameters for the tasks
    splio_params = {
        'bq_project': bq_project,
        'partial': business_data__email.get("splio_stats").get("partial"),
        'start_date': business_data__email.get("splio_stats").get("start_date"),
        'end_date': business_data__email.get("splio_stats").get("end_date"),
        'time_interval': business_data__email.get("splio_stats").get("time_interval")
    }

    # Define the consent task
    splio_daily_follow_up_by_consent = BigQueryExecuteQueryOperator(
        task_id='splio_daily_follow_up_by_consent',
        sql='splio_daily_follow_up_by_consent.sql',
        params=splio_params
    )

    splio_daily_follow_up_by_consent.doc_mc = task_doc_md

    # Define the other SQL tasks
    splio_stats_sql_files = [
        "splio_daily_follow_up_global.sql",
        "splio_daily_follow_up_by_brand.sql",
        "splio_daily_follow_up_by_universe.sql",
        "splio_daily_follow_up_by_type.sql",
        "splio_daily_follow_up_by_owner.sql",
        "splio_daily_follow_up_by_nl.sql"
    ]

    splio_stats_seq_tasks = []
    for sql_file in splio_stats_sql_files:
        task_id = f"{sql_file.replace('.sql', '')}"
        task = BigQueryExecuteQueryOperator(
            task_id=task_id,
            sql=sql_file,
            params=splio_params
        )
        task.doc_mc = task_doc_md
        splio_stats_seq_tasks.append(task)

    task_doc_md = """
    We need to wait for refine persona segment affinity as below 👇🏻.
    """
    # @Doc --> https://airflow.readthedocs.io/en/1.9.0/_modules/airflow/operators/sensors.html
    wait_for_persona_segment_activity = SqlSensor(
        task_id='wait_for_persona_segment_activity',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql=""" 
        SELECT CASE WHEN state = 'success' THEN TRUE ELSE FALSE END
            FROM task_instance
        WHERE dag_id = 'refined_data__email{str_env}'
            AND task_id ='refine_persona_affinity'
            AND DATE(start_date) = CURRENT_DATE
            """.format(str_env='' if env == 'prod' else '_' + env)
    )

    # Define the SQL tasks for business data : persona affinity
    pa_sql_files = [
        "persona_segment_affinity_per_brand.sql",
        "persona_segment_affinity_socio_demo.sql"
    ]

    pa_seq_tasks = []
    for sql_file in pa_sql_files:
        task_id = f"{sql_file.replace('.sql', '')}"
        task = BigQueryExecuteQueryOperator(
            task_id=task_id,
            sql=sql_file,
            params={
                'bq_project': bq_project
            }
        )
        task.doc_mc = f"""
            Generate persona affinity business data : {task_id}
            """
        pa_seq_tasks.append(task)

    task_doc_md = """
        We need to wait for refine email event sent for personalized NL task's.
    """
    # @Doc --> https://airflow.readthedocs.io/en/1.9.0/_modules/airflow/operators/sensors.html
    wait_for_refined_sent_personalized_nl = SqlSensor(
        task_id='wait_for_refined_sent_personalized_nl',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                SELECT CASE WHEN (task_id = 'refine_personalized_nl_email_event_sent' AND state = 'success') THEN TRUE ELSE FALSE END
                FROM task_instance
                WHERE dag_id = 'refined_data__email{str_env}'
                  AND task_id =  'refine_personalized_nl_email_event_sent'
                  AND DATE(start_date) = CURRENT_DATE
            """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_refined_sent_personalized_nl.doc_mc = task_doc_md

    task_doc_md = """
        Compute business metrics that describes personalized NL activity daily.
    """
    compute_personalized_nl_metrics_daily = BigQueryExecuteQueryOperator(
        task_id='compute_personalized_nl_metrics_daily',
        sql='personalized_nl_metrics_daily.sql',
        params={
            "bq_project": bq_project
        }
    )

    compute_personalized_nl_metrics_daily.doc_mc = task_doc_md

    task_doc_md = """
        Compute business metrics that describes personalized NL activity by content type daily.
    """
    compute_personalized_nl_metrics_by_content_type_daily = BigQueryExecuteQueryOperator(
        task_id='compute_personalized_nl_metrics_by_content_type_daily',
        sql='personalized_nl_metrics_by_content_type_daily.sql',
        params={
            "bq_project": bq_project
        }
    )

    compute_personalized_nl_metrics_by_content_type_daily.doc_mc = task_doc_md

    task_doc_md = """
    We need to wait for refine pandora events.
    """
    wait_for_pandora_events = SqlSensor(
        task_id='wait_for_pandora_events',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql=""" 
        SELECT CASE WHEN state = 'success' THEN TRUE ELSE FALSE END
        FROM task_instance
        WHERE 
            dag_id = 'refined_data__email{str_env}'
            AND task_id ='refine_pandora_events'
            AND DATE(start_date) = CURRENT_DATE
        """.format(str_env='' if env == 'prod' else '_' + env)
    )

    wait_for_pandora_events.doc_mc = task_doc_md

    task_doc_md = """
        We need to wait for refined Splio technical blacklist table.
    """
    wait_for_refine_splio_technical_blacklist = SqlSensor(
        task_id='wait_for_refine_splio_technical_blacklist',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql=""" 
            SELECT CASE WHEN state = 'success' THEN TRUE ELSE FALSE END
            FROM task_instance
            WHERE 
                dag_id = 'partners_apply_external_lop{str_env}'
                AND task_id ='refine_technical_blacklist'
                AND DATE(start_date) = CURRENT_DATE
            """.format(str_env='' if env == 'prod' else '_' + env)
    )

    wait_for_refine_splio_technical_blacklist.doc_mc = task_doc_md

    task_doc_md = """
        Compute Pandora acquisition stats by UTM.
    """
    compute_pandora_acquisition_utm = BigQueryExecuteQueryOperator(
        task_id='compute_pandora_acquisition_utm',
        sql='pandora_acquisition_utm.sql',
        params={
            "bq_project": bq_project,
            'is_full': business_data__email.get("pandora_acquisition_utm").get("is_full"),
            'start_date': business_data__email.get("pandora_acquisition_utm").get("start_date"),
            'end_date': business_data__email.get("pandora_acquisition_utm").get("end_date"),
            'time_interval': business_data__email.get("pandora_acquisition_utm").get("time_interval")
        }
    )

    compute_pandora_acquisition_utm.doc_mc = task_doc_md

    task_doc_md = """
        Compute Pandora acquisition stats by UTM and status.
    """
    compute_pandora_acquisition_utm_by_status = BigQueryExecuteQueryOperator(
        task_id='compute_pandora_acquisition_utm_by_status',
        sql='pandora_acquisition_utm_by_status.sql',
        params={
            "bq_project": bq_project,
            'is_full': business_data__email.get("pandora_acquisition_utm_by_status").get("is_full"),
            'start_date': business_data__email.get("pandora_acquisition_utm_by_status").get("start_date"),
            'end_date': business_data__email.get("pandora_acquisition_utm_by_status").get("end_date"),
            'time_interval': business_data__email.get("pandora_acquisition_utm_by_status").get("time_interval")
        }
    )

    compute_pandora_acquisition_utm_by_status.doc_mc = task_doc_md

    task_doc_md = """
        Analyze user segments based on navigation behavior and subscription status.
        """
    compute_ga4_valuation_kpis_global = BigQueryExecuteQueryOperator(
        task_id='compute_ga4_valuation_kpis_global',
        sql='ga4_valuation_kpis_global.sql',
        params={
            'bq_project': bq_project,
            'is_full': business_data__email.get("ga4_valuation_kpis_global").get("is_full", False),
            'start_date': business_data__email.get("ga4_valuation_kpis_global").get("start_date"),
            'end_date': business_data__email.get("ga4_valuation_kpis_global").get("end_date")
        }
    )
    compute_ga4_valuation_kpis_global.doc_md = task_doc_md

    task_doc_md = """
    Analyze user segments based on navigation behavior and subscription status.
    """
    compute_ga4_valuation_kpis_by_brand = BigQueryExecuteQueryOperator(
        task_id='compute_ga4_valuation_kpis_by_brand',
        sql='ga4_valuation_kpis_by_brand.sql',
        params={
            'bq_project': bq_project,
            'is_full': business_data__email.get("ga4_valuation_kpis_by_brand").get("is_full", False),
            'start_date': business_data__email.get("ga4_valuation_kpis_by_brand").get("start_date"),
            'end_date': business_data__email.get("ga4_valuation_kpis_by_brand").get("end_date")
        }
    )
    compute_ga4_valuation_kpis_by_brand.doc_md = task_doc_md
    
    task_doc_md = """
        Compute Soft LOP Splio profiles indicators by ESP and brand daily. 
    """
    compute_splio_lop_profile_indicators_by_brand_daily = BigQueryExecuteQueryOperator(
        task_id='compute_splio_lop_profile_indicators_by_brand_daily',
        sql='splio_lop_profile_indicators_by_brand_daily.sql',
        params={
            "bq_project": bq_project
        }
    )
    compute_splio_lop_profile_indicators_by_brand_daily.doc_mc = task_doc_md

    task_doc_md = """
        Compute Soft LOP Splio profiles indicators by ESP daily. 
    """
    compute_splio_lop_profile_indicators_by_esp_daily = BigQueryExecuteQueryOperator(
        task_id='compute_splio_lop_profile_indicators_by_esp_daily',
        sql='splio_lop_profile_indicators_by_esp_daily.sql',
        params={
            "bq_project": bq_project
        }
    )
    compute_splio_lop_profile_indicators_by_esp_daily.doc_mc = task_doc_md

    task_doc_md = """
        Compute profile reactivity indicators by universe and ESP daily. 
    """
    compute_splio_profile_reactivity_indicators_by_esp_daily = BigQueryExecuteQueryOperator(
        task_id='compute_splio_profile_reactivity_indicators_by_esp_daily',
        sql='splio_profile_reactivity_indicators_by_esp_daily.sql',
        params={
            "bq_project": bq_project,
            "is_full": business_data__email.get("splio_reactivity_by_esp").get("is_full"),
            "start_date": business_data__email.get("splio_reactivity_by_esp").get("start_date"),
            "interval": business_data__email.get("splio_reactivity_by_esp").get("interval")
        }
    )
    compute_splio_profile_reactivity_indicators_by_esp_daily.doc_mc = task_doc_md

    task_doc_md = """
    We need to wait for archive workflows transitions to aggregate data.
    """
    wait_for_archive_workflows_transitions = SqlSensor(
        task_id='wait_for_archive_workflows_transitions',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                SELECT CASE WHEN state = 'success' THEN TRUE ELSE FALSE END
                FROM task_instance
                WHERE dag_id = 'crm_tag{str_env}'
                  AND task_id = 'archive_workflows_transitions'
                  AND DATE(start_date) = CURRENT_DATE
            """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_archive_workflows_transitions.doc_mc = task_doc_md

    # check if it's the first day of month
    task_doc_md = """
        Check date to update crm transition duration.
        """
    check_date_crm_transition_duration = BranchPythonOperator(
        task_id="check_date_crm_transition_duration",
        python_callable=_date_branch_crm_transition_duration
    )
    check_date_crm_transition_duration.doc_md = task_doc_md

    task_doc_md = """
        Compute CRM Tag transition history at universe-level. 
    """
    compute_crm_tag_transition_history_by_universe = BigQueryExecuteQueryOperator(
        task_id='compute_crm_tag_transition_history_by_universe',
        sql='crm_tag_transition_history_by_universe.sql',
        params={
            "bq_project": bq_project,
            "is_full": business_data__email.get("crm_tag_transition_history_by_universe").get("is_full"),
            "interval": business_data__email.get("crm_tag_transition_history_by_universe").get("interval")
        }
    )
    compute_crm_tag_transition_history_by_universe.doc_mc = task_doc_md

    task_doc_md = """
        Compute CRM Tag transition duration at universe-level. 
    """
    compute_crm_tag_transition_duration_by_universe = BigQueryExecuteQueryOperator(
        task_id='compute_crm_tag_transition_duration_by_universe',
        sql='crm_tag_transition_duration_by_universe.sql',
        params={
            "bq_project": bq_project,
            "start_date": business_data__email.get("crm_tag_transition_duration_by_universe").get("start_date")
        }
    )
    compute_crm_tag_transition_duration_by_universe.doc_mc = task_doc_md

    task_doc_md = """
        Compute CRM Tag history at universe-level. 
    """
    compute_crm_tag_history_by_universe = BigQueryExecuteQueryOperator(
        task_id='compute_crm_tag_history_by_universe',
        sql='crm_tag_history_by_universe.sql',
        params={
            "bq_project": bq_project,
            "is_full": business_data__email.get("crm_tag_history_by_universe").get("is_full"),
            "interval": business_data__email.get("crm_tag_history_by_universe").get("interval")
        }
    )
    compute_crm_tag_history_by_universe.doc_mc = task_doc_md

    task_doc_md = """
        Compute CRM Tag history at universe-level. 
    """
    compute_crm_tag_volume_monthly_by_universe = BigQueryExecuteQueryOperator(
        task_id='compute_crm_tag_volume_monthly_by_universe',
        sql='crm_tag_volume_monthly_by_universe.sql',
        params={
            "bq_project": bq_project,
            "is_full": business_data__email.get("crm_tag_volume_monthly_by_universe").get("is_full"),
            "start_date": business_data__email.get("crm_tag_volume_monthly_by_universe").get("start_date")
        }
    )
    compute_crm_tag_volume_monthly_by_universe.doc_mc = task_doc_md

    task_doc_md = """
        Compute business metrics that describes classic NL activity daily.
     """
    compute_classic_nl_metrics_daily = BigQueryExecuteQueryOperator(
        task_id='compute_classic_nl_metrics_daily',
        sql='compute_classic_nl_metrics_daily.sql',
        params={
            "bq_project": bq_project
        }
    )

    compute_classic_nl_metrics_daily.doc_mc = task_doc_md

    task_doc_md = """
        Compute business metrics that describes classic NL activity by segment daily.
    """
    compute_classic_nl_metrics_by_segment_daily = BigQueryExecuteQueryOperator(
        task_id='compute_classic_nl_metrics_by_segment_daily',
        sql='compute_classic_nl_metrics_by_segment_daily.sql',
        params={
            "bq_project": bq_project
        }
    )

    compute_classic_nl_metrics_by_segment_daily.doc_mc = task_doc_md

    task_doc_md = """
        Compute business metrics that describes classic NL activity by email subject daily.
    """
    compute_classic_nl_metrics_by_email_subject_daily = BigQueryExecuteQueryOperator(
        task_id='compute_classic_nl_metrics_by_email_subject_daily',
        sql='compute_classic_nl_metrics_by_email_subject_daily.sql',
        params={
            "bq_project": bq_project
        }
    )

    compute_classic_nl_metrics_by_email_subject_daily.doc_mc = task_doc_md

    task_doc_md = """
        Compute Prismashop campaign metric's.  
    """
    compute_prismashop_metrics_by_campaign = BigQueryExecuteQueryOperator(
        task_id='compute_prismashop_metrics_by_campaign',
        sql='prismashop/compute_prismashop_metrics_by_campaign.sql',
        params={
            "bq_project": bq_project
        }
    )
    compute_prismashop_metrics_by_campaign.doc_mc = task_doc_md


    variable_set()

    wait_for_archive_workflows_transitions >> [compute_crm_tag_transition_history_by_universe,
                                               compute_crm_tag_history_by_universe,
                                               check_date_crm_transition_duration]

    check_date_crm_transition_duration >> [start_crm_monthly_tasks, do_nothing]
    start_crm_monthly_tasks >> [compute_crm_tag_transition_duration_by_universe, compute_crm_tag_volume_monthly_by_universe]

    wait_for_generated_email_profile_subject_stats >> email_subject_stats_daily >> email_subject_stats_monthly

    email_indicators_history

    compute_ga4_valuation_kpis_global
    compute_ga4_valuation_kpis_by_brand

    wait_for_persona_segment_activity >> pa_seq_tasks

    wait_for_refined_splio_report >> splio_daily_follow_up_by_consent >> splio_stats_seq_tasks

    wait_for_email_reactivity_by_base >> cross_consent_reactivity
    wait_for_email_reactivity_by_brand >> cross_brand_reactivity

    wait_for_pandora_events >> [compute_pandora_acquisition_utm, compute_pandora_acquisition_utm_by_status]

    wait_for_refine_organic_acquisition >> [organic_acquisition_monthly_kpi_by_consent,
                                            organic_acquisition_monthly_kpi_by_brand,
                                            organic_acquisition_kpi_by_consent]

    check_date >> [do_nothing, wait_for_pandora_profile_activity_by_consent]

    wait_for_pandora_profile_activity_by_consent >> [compute_pandora_profile_ltv_by_brand_acquisition, compute_pandora_profile_ltv_by_brand_monthly,
                                          compute_pandora_profile_ltv_by_consent_acquisition, compute_pandora_profile_ltv_by_consent_monthly,
                                          compute_pandora_profile_ltv_global_acquisition, compute_pandora_profile_ltv_global_monthly]

    [wait_for_refined_sent, wait_for_merge_refined_open, wait_for_refined_click,
     wait_for_refined_splio_report] >> compute_email_kpis_by_nim

    wait_for_pandora_profile_activation_nl_shopping >> pandora_activation_nl_shopping

    heatmap_nl_block_click
    arpu_revenue_acquisition
    arpu_revenue_by_nl
    monitor_autocorrect_daily

    wait_for_refined_fai_complaints >> splio_fai_complaints_stats_by_brand
    wait_for_refined_mail_complaints >> splio_mail_complaints_stats_by_brand

    [wait_for_refined_sent_personalized_nl, wait_for_merge_refined_open,
     wait_for_refined_click] >> compute_personalized_nl_metrics_daily >> compute_personalized_nl_metrics_by_content_type_daily
    wait_for_refine_splio_technical_blacklist >> [compute_splio_lop_profile_indicators_by_brand_daily,
                                                  compute_splio_lop_profile_indicators_by_esp_daily]
    wait_for_merge_refined_open >> compute_splio_profile_reactivity_indicators_by_esp_daily
    wait_for_refined_splio_report >> [compute_classic_nl_metrics_daily, compute_classic_nl_metrics_by_segment_daily, compute_classic_nl_metrics_by_email_subject_daily]
    compute_prismashop_metrics_by_campaign




