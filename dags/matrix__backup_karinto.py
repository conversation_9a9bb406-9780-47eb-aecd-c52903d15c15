r"""
**PURPOSE:**

Backup karinto schema table to store_karinto 

**USEFUL_COMMAND:**
- gsutil cp dags/matrix__backup_karinto.py gs://europe-west1-prod-mozart-co-2ef7e904-bucket/dags/ 
- gsutil -m cp -R data/sql/backup/karinto/*  gs://europe-west1-mozart-cluster-d3558445-bucket/data/sql/backup/karinto/
"""

import os
from datetime import timedelta, datetime

from airflow.models import Variable
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.google.cloud.transfers.gcs_to_bigquery import GCSToBigQueryOperator
from bq_plugin.operators.psql_to_bigquery_operator import PostgresToBigQueryOperator
from collapsible_doc_dag import CDocDAG
from dev_plugin.operators.psql_export_query_to_gcs_operator import PostgresQueryExportToGoogleCloudStorageOperator

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']

dag_name = 'matrix__backup_karinto'
bq_project = 'pm-prod-matrix'

email_on_failure = True
# ------------- FOR PREPROD ENV -----------------
env = os.environ.get("ENV")
if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    bq_project = 'pm-preprod-matrix'
# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2020, 5, 6, 00, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=3),
    'sla': None,
    'email_on_retry': False,
    'retries': 0,
    'retry_delay': timedelta(minutes=1),
    'depends_on_past': False,

    'bucket': matrix_bucket,

    # PSQL Connection
    'conn_id': 'psql_karinto_app',
    'postgres_conn_id': 'psql_karinto_app',
    'database': 'matrix',
    'schema': 'matrix__email_queue',

    # GCP CONNECTION
    'gcp_conn_id': 'gcs_matrix',
    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_TRUNCATE',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',
}

# Table karinto
CURRENT_DATEHOUR = '{{next_execution_date.strftime("%Y-%m-%d-%H")}}'
snapshot_folder_karinto = 'backup_matrix/karinto/'
schemas_folder_pref_center = 'backup_matrix/pref_center/schemas/'
schemas_folder_karinto = 'backup_matrix/karinto/schemas/'

with CDocDAG(
        dag_name,
        description='backup karinto schema to BQ store_karinto',
        doc_md=__doc__,
        schedule_interval="25 */4 * * *",
        default_args=default_args,
        tags=["backup"],
        max_active_runs=1,
        dagrun_timeout=timedelta(minutes=40),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/backup/']
) as dag:
    # --------------------------------------------------------------------
    # ----- Table refined_data.email_base  -------------------------------
    refined_data_email_base = BigQueryExecuteQueryOperator(
        task_id='refined_data_email_base',
        sql='karinto/01_refined_data_email_base.sql',
        params={
            'bq_project': bq_project,
        }
    )

    # --------------------------------------------------------------------
    # ---- Backup Tables pref-centers  -------------
    pref_schemas = [
        {
            "mode": "NULLABLE",
            "name": "consent_id",
            "type": "INTEGER"
        },
        {
            "mode": "NULLABLE",
            "name": "public_ref",
            "type": "STRING"
        },
        {
            "mode": "NULLABLE",
            "name": "data_direct_name",
            "type": "STRING"
        },
        {
            "mode": "NULLABLE",
            "name": "pref_center_name",
            "type": "STRING"
        }
    ]
    export_pf_to_gcs = PostgresQueryExportToGoogleCloudStorageOperator(
        task_id='export_pf_to_gcs',
        sql='pref_center/backup_pref_centers.sql',
        filename='backup_matrix/pref_center/pref_centers_list_{{execution_date.strftime("%Y_%m_%d_%H%M")}}.csv',
        postgres_conn_id='psql_pref_center_app',
        bucket=matrix_bucket,
        export_format='csv',
        field_delimiter='\t',
        gzip=False,
    )

    # consolidated_table
    import_backup_pf_to_bq = GCSToBigQueryOperator(
        task_id='import_backup_pf_to_bq',
        source_objects=['backup_matrix/pref_center/pref_centers_list_{{execution_date.strftime("%Y_%m_%d_%H%M")}}.csv', ],
        destination_project_dataset_table=bq_project + ':store_karinto.pref_centers_list',
        schema_fields=pref_schemas,
        field_delimiter='\t',
        skip_leading_rows=1,
        autodetect=False,
        max_bad_records=0,
        write_disposition='WRITE_TRUNCATE',
        bucket=matrix_bucket,
    )

    export_pf_to_gcs >> import_backup_pf_to_bq

    # get all newsletters names for all consents and themes
    newsletters_names_schemas = [
        {
            "mode": "REQUIRED",
            "name": "consent_label",
            "type": "STRING"
        },
        {
            "mode": "REQUIRED",
            "name": "public_ref",
            "type": "STRING"
        },
        {
            "mode": "REQUIRED",
            "name": "nl_name",
            "type": "STRING"
        }
    ]
    export_newsletters_names_to_gcs = PostgresQueryExportToGoogleCloudStorageOperator(
        task_id='export_newsletters_names_to_gcs',
        sql='nlf/newsletters_names.sql',
        filename='backup_matrix/nlf/nlf_names_{{execution_date.strftime("%Y_%m_%d_%H%M")}}.csv',
        postgres_conn_id='psql_pref_center_app',
        bucket=matrix_bucket,
        export_format='csv',
        field_delimiter='\t',
        gzip=False,
    )

    # consolidated_table
    import_newsletters_names_to_bq = GCSToBigQueryOperator(
        task_id='import_newsletters_names_to_bq',
        source_objects=['backup_matrix/nlf/nlf_names_{{execution_date.strftime("%Y_%m_%d_%H%M")}}.csv', ],
        destination_project_dataset_table=bq_project + ':store_karinto.newsletters_names',
        schema_fields=newsletters_names_schemas,
        field_delimiter='\t',
        skip_leading_rows=1,
        autodetect=False,
        max_bad_records=0,
        write_disposition='WRITE_TRUNCATE',
        bucket=matrix_bucket,
    )

    export_newsletters_names_to_gcs >> import_newsletters_names_to_bq
