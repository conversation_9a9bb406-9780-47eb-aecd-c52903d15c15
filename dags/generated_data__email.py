r"""
**PURPOSE:**

This dag is used to generate new data into generated_data BigQuery dataset for Email scope.

**METHODOLOGY:**

    - Build Service Card tables (nl_free and pmc),
**generate sub events to loy bases and export oppositions to Postgres:**-**TASKS:**`generate_service_card >> loy_event >> [export_events_to_queue, export_event_seq, export_service_card_oppositions]` 
        -**TABLES:**
            - `generated_data.prisma_connect_service` : PMC service card
            - `generated_data.service_nl_free` : Newsletter service card, with free subscription

    - PMC Socio-**demo fill:**-**TASKS:**`pmc_fill` 
        -**TABLES:**
            - `store_matrix_email.profiles_email_consents`
            - `store_matrix_email.profile_master_id`
            - `refined_data.email_base`
            - `refined_data.email_event_click`

    - Segmentation Tag(Email): 
        -**TASKS:**`segmentation_tags >> segmentation_tags_snapshots` 
        -**TABLES:**

    -**Email Tracking:**-**TASKS:**`simulate_email_event_open` 
        -**TABLES:**
            - `refined_data.email_event_open`
            - `refined_data.email_event_click`

    -**Email profile lifecycle:**-**TASKS:**`compute_email_profile_lifecycle_by_base` >> `compute_last_sub_unsub_by_universe` 
        -**TABLES:**`refined_data.email_event_sub_unsub_*` 

    -**Email reactivity:**-**TASKS:**[`email_reactivity_by_theme`, `email_reactivity_by_base`] >> [`email_reactivity_by_brand`, `email_reactivity_by_universe`] >> `email_reactivity_global` 
        -**TABLES:**
            - `refined_data.email_event_open` 
            - `refined_data.email_event_click` 

    -**Email sessions:**-**TASKS:**`compute_email_profile_subject_stats` 
        -**TABLES:**
            - `refined_data.email_sessions_by_object` 

    -**Splio campaigns:**- `refined_data.splio_report` 
        - `store_tracking.splio_report_link` 
        - `refined_data.email_event_click` 
        - `refined_data.email_event_open` 

__RESULT**TABLE:**

    -**Service Card:**- `generated_data.prisma_connect_service`: PMC service card
        - `generated_data.service_nl_free`: Newsletter service card, with free subscription

    - PMC Socio-**demo fill:**- `generated_data.pmc_data_fill_with_last_activity` 

    -**Segmentation Tag:**- `store_email_segment.*` 
        - `generated_data.segmentation_tag`: Segment tags per profile 
        - `generated_data.segmentation_tag_snapshot`: Daily snapshot of Segment tags per profile

    -**Email Tracking:**- `generated_data.email_event_simulated_open` 

    -**Email profile lifecycle:**- `generated_data.email_profile_lifecycle_by_base`
        - `generated_data.last_sub_unsub_by_universe`

    -**Email reactivity:**- `generated_data.last_activity_by_base`: Email profile Last activity per consent 
        - `generated_data.last_activity_by_theme`: Email profile Last activity per theme(NL Shopping, NL-Bestof, ...) 
        - `generated_data.last_activity_by_universe`: Email profile Last activity per Splio universe 
        - `generated_data.last_activity_by_brand`: Email profile  Last activity per brand 
        - `generated_data.last_activity_global`: Email profile Last activity by profile_master_id cross-consents(NL, Part, Loy, ...) 
        - `generated_data.email_reactivity_history_by_base`: Email profile open & click volume per consent 
        - `generated_data.compute_email_profile_subject_stats`: Email sessions and views of pages volume Compute email by object of mail, rogueone, profile and date of visit.

    -**Email Profile lifecycle:**- `generated_data.last_sub_unsub_by_universe`: last sub/unsub dates by universe 
        - `generated_data.email_profile_lifecycle_by_base`: sub/unsub events by profile by base 

    -**Splio Campaigns:**- `generated_data.campaign_links_clicks` 
        - `generated_data.splio_event_open_ios15` 

**COMMAND TO TEST:** 

    gsutil -m cp -R dags/generated_data__email.py gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/
    gsutil -m cp -R data/sql/generated_data/service_card/*  gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/generated_data/service_card
    gsutil -m cp -R data/sql/generated_data/email/*  gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/generated_data/email
    gsutil -m cp plugins/matrix_plugin/operators/bigquery_to_email_event.py gs://europe-west1-preprod-mozart-2fa49086-bucket/plugins/matrix_plugin/operators/
    gsutil -m cp -R data/json/*  gs://europe-west1-preprod-mozart-2fa49086-bucket/data/json/
"""

import os
import json
import logging
from airflow.decorators import task
from datetime import timedelta, datetime, date
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryGetDataOperator
from airflow.models import Variable
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from matrix_plugin.operators.bigquery_to_email_event import BigQueryToEmailEventOperator
from bq_plugin.operators.bigquery_to_psql_operator import BigQueryToPostgresOperator
from airflow.utils.task_group import TaskGroup
from airflow.operators.python import BranchPythonOperator
from airflow.operators.python import PythonOperator
from airflow.operators.empty import EmptyOperator
from collapsible_doc_dag import CDocDAG
from airflow.utils.email import send_email
from airflow.providers.common.sql.sensors.sql import SqlSensor
import itdata_plugin


# ----------------- CONFIG ----------------------
dag_name = 'generated_data__email'

env = os.environ.get("ENV")
bq_project = 'pm-{}-matrix'.format(env)
ga4_project = 'pm-{}-ga4'.format(env)
userhub_project = 'pm-{}-userhub'.format(env)
bq_ba_b2b_dataset = 'pm-prod-datab2b.NL_segments'
bq_vno_dataset = 'pm-prod-vente-au-numero.export_it_data'
email_on_failure = True

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']
mozart_bucket = buckets['mozart']

# read config from json file
f = open(f'/home/<USER>/gcs/data/json/{env}/generated_data__email.json')
generated_data__email = json.load(f)
f.close()


if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    # force preprod on other env
    bq_project = 'pm-preprod-matrix'
    ga4_project = 'pm-preprod-ga4'

default_args = {
    'owner': 'airflow',
    "start_date": datetime(2020, 8, 5, 2, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': False,
    'provide_context': True,
    'execution_timeout': timedelta(hours=8),
    'sla': None,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=20),
    'depends_on_past': False,

    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_APPEND',
    # GCP CONNECTION
    'gcp_conn_id': 'gcs_matrix',
    # PSQL Connection
    'postgres_conn_id': 'psql_matrix_email_app',

    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',  # possibles values: INTERACTIVE and BATCH.
}


@task
def variable_set():
    """
    This task reads the configs inside the `generated_data__email` variable (from a json file)
    and updates `generated_data__email` Airflow variable
    """
    itdata_plugin.set_variable_in_secret_manager(secret_name="generated_data__email", secret_value=generated_data__email, serialize_json=True)


def _date_branch_profile_reactivity(**kwargs):
    # get current date
    current_date = date.today()
    if current_date == current_date.replace(day=1):
        return 'email_profile_consent_reactivity_by_month'
    else:
        return 'do_nothing'


def _date_branch_pandora_profile_activity(**kwargs):
    # get current date
    current_date = date.today()
    if current_date == current_date.replace(day=1):
        return 'pandora_profile_activity_by_consent'
    else:
        return 'do_nothing'


def alert_on_unmapped_revenue(**kwargs):
    """
    We get all alerts that are triggered by unmapped revenue
    """

    task_instance = kwargs['task_instance']
    env = os.environ.get("ENV")

    profiles_from_prisma_base = task_instance.xcom_pull(task_ids='retrieve_unmapped_revenue', key='return_value')

    logging.info('profiles_from_prisma_base : {}'.format(profiles_from_prisma_base))

    if not profiles_from_prisma_base or env != 'prod':
        logging.info('No alerts to send')

    else:
        logging.info('Alerts to send for unmapped revenue : {}'.format(profiles_from_prisma_base))

        recipient = ["<EMAIL>","<EMAIL>", "<EMAIL>"]

        additional_recipients = []
        cc = ", ".join(additional_recipients)

        title = "ARPU - Revenus non mappés"

        body = (
            "Bonjour, <br>"
            "<br> Si vous recevez cet email, cela signifie qu'il y a des revenus attribués à des ad_names non mappés.<br><br>"
            "<br> Par conséquent, ces revenus ne remonteront pas dans le DB ARPU.<br>"
            "<br> Voici la liste des alertes remontées : <a href='https://docs.google.com/spreadsheets/d/11NR31L-jHcPZ3v4SLDb2qJWG7pGPWnGlW72MPJnxi3I/edit?usp=sharing'>Alertes ARPU</a>.<br>"
            "<br><br>Cordialement, <br>"
            "L'équipe technique IT-Data"
        )

        send_email(to=recipient, subject=title, html_content=body, cc=cc, mime_charset='utf-8', conn_id='sendgrid_default')

with CDocDAG(
        dag_name,
        description='Generated valuable data for email',
        tags=["generated", "email"],
        doc_md=__doc__,
        schedule_interval="1 4 * * *",
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=10),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/generated_data/'],
        concurrency=10
) as dag:
    # store max email event id in temporary table
    export_event_seq = BigQueryExecuteQueryOperator(
        task_id="export_event_seq",
        sql="email/service_card_loy_opposition_email_event_seq.sql",
    )
    export_event_seq.doc_md = 'export events'

    output_event_seq = BigQueryGetDataOperator(
        task_id='output_event_seq',
        dataset_id='store',
        table_id='service_card_loy_opposition_email_event_seq',
        max_results=1,
        selected_fields='id',
    )
    output_event_seq.doc_md = 'output events'

    update_loy_opposition_email_event_seq = SQLExecuteQueryOperator(
        task_id='update_loy_opposition_email_event_seq',
        sql='service_card/export/00_loy_opposition_email_event_seq.sql',
        conn_id = 'psql_matrix_email_app'
    )
    update_loy_opposition_email_event_seq.doc_md = 'update loy opposition email events'

    task_doc_md = """
    Generate prisma_connect_service card and service_nl_free service card
    """
    generate_service_card = BigQueryExecuteQueryOperator(
        task_id="generate_service_card",
        sql="service_card/generate_service_card.sql",
        params={
            "bq_project": bq_project
        }
    )
    generate_service_card.doc_md = task_doc_md

    task_doc_md = """
    Generate sub email_event to send to PostgreSQL email_event queue.
    """
    loy_event = BigQueryExecuteQueryOperator(
        task_id='loy_event',
        sql='service_card/loy_event.sql',
        params={
            "bq_project": bq_project
        }
    )
    loy_event.doc_md = task_doc_md

    task_doc_md = """
    Send email event to PostgreSQL email_event queue (matrix__email_queue.email_event).
    """
    export_events_to_queue = BigQueryToEmailEventOperator(
        task_id='export_events_to_queue',
        postgres_conn_id='psql_matrix_email_app',
        database='matrix',
        bigquery_conn_id='bq_matrix',
        source_project_dataset_table='export_matrix_email.service_card_loy_event',
        bucket=matrix_bucket,
    )
    export_events_to_queue.doc_md = task_doc_md

    query = "COPY {table} (email_profile_master_id, brand_trigram, service, email_consent_id, opposition_date) FROM STDIN WITH CSV QUOTE e'\"' DELIMITER AS '\t' "
    # Export service card oppositions to postgres
    task_doc_md = """
    Export service card loy oppositions to Postgres.<br />
    """
    export_service_card_oppositions = BigQueryToPostgresOperator(
        task_id='export_service_card_oppositions',
        # psql destination table
        destination_table='matrix__email_stockpile.service_card_loy_opposition',
        # truncate destination_table ?
        truncate_destination_table=True,
        bucket=matrix_bucket,
        database='matrix',
        destination_cloud_storage_object_path='tmp_exports/{{ next_execution_date.strftime("%Y%m%d") }}/import_service_card_opposition_{{ next_execution_date.strftime("%Y%m%d") }}.csv',

        postgres_conn_id='psql_matrix_email_app',
        bigquery_conn_id='bq_matrix',
        gcp_conn_id='gcs_matrix',
        params={
            "bq_project": 'pm-{}-matrix'.format(env)
        },
        source_query='service_card/export/01_export_oppositions.sql',
        source_query_destination_table='pm-{}-matrix'.format(
            env) + ':prepare.service_card_loy_opposition_{{ next_execution_date.strftime("%Y%m%d")}}',
        # bq table to export to psql (same as source_query_destination_table in this case)
        source_export_dataset_table='pm-{}-matrix'.format(
            env) + ':prepare.service_card_loy_opposition_{{ next_execution_date.strftime("%Y%m%d")}}',
        # query used to load csv file into psql table
        copy_expert_query=query.format(
            table='matrix__email_stockpile.service_card_loy_opposition'),
        temp_bucket=mozart_bucket,
    )
    export_service_card_oppositions.doc_md = task_doc_md

    task_doc_md = """
    Compute pmc fields fill as gender, zipcode based on their activitiy.
    """
    pmc_fill = BigQueryExecuteQueryOperator(
        task_id='pmc_fill',
        sql='email/pmc_data_fill_with_last_activity.sql',
        write_disposition='WRITE_TRUNCATE',
        params={
            "bq_project": bq_project
        }
    )
    pmc_fill.doc_md = task_doc_md

    task_doc_md = """
    Generate segmentation_tag.
    """
    segmentation_tags = BigQueryExecuteQueryOperator(
        task_id='segmentation_tags',
        sql='segmentation_tags/generate_segmentation_tags.sql',
        params={
            "bq_project": bq_project,
            "bq_ba_b2b_dataset": bq_ba_b2b_dataset,
            "bq_vno_dataset": bq_vno_dataset,
            "env": env,
        }
    )
    segmentation_tags.doc_md = task_doc_md

    task_doc_md = """
    Generate segmentation_tag_snapshot.
    """
    segmentation_tags_snapshots = BigQueryExecuteQueryOperator(
        task_id='segmentation_tag_snapshots',
        sql='segmentation_tags/generate_segmentation_tags_snapshot.sql'
    )
    segmentation_tags_snapshots.doc_md = task_doc_md

    task_doc_md = """
    We need to wait for refined sub/unsub event to generate profile lifecycle < /br>.
    """
    # @Doc --> https://airflow.readthedocs.io/en/1.9.0/_modules/airflow/operators/sensors.html
    wait_for_refined_sub_unsub = SqlSensor(
        task_id='wait_for_refined_sub_unsub',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                SELECT CASE WHEN state = 'success' THEN TRUE ELSE FALSE END
                FROM task_instance
                WHERE dag_id = 'refined_data__email{str_env}'
                  AND task_id = 'refine_sub_unsub_events'
                  AND DATE(start_date) = CURRENT_DATE
            """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_refined_sub_unsub.doc_mc = task_doc_md

    task_doc_md = """
    We need to wait for refined_sub/unsub_event_new to generate profile lifecycle < /br>.
    """
    # @Doc --> https://airflow.readthedocs.io/en/1.9.0/_modules/airflow/operators/sensors.html
    wait_for_refined_sub_unsub_new = SqlSensor(
        task_id='wait_for_refined_sub_unsub_new',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                SELECT CASE WHEN state = 'success' THEN TRUE ELSE FALSE END
                FROM task_instance
                WHERE dag_id = 'refined_data__email{str_env}'
                  AND task_id = 'refine_sub_unsub_events_new'
                  AND DATE(start_date) = CURRENT_DATE
            """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_refined_sub_unsub_new.doc_mc = task_doc_md

    task_doc_md = """
    We need to wait for refined Splio report to generate campaign
    """
    # @Doc --> https://airflow.readthedocs.io/en/1.9.0/_modules/airflow/operators/sensors.html
    wait_for_refined_splio_report = SqlSensor(
        task_id='wait_for_refined_splio_report',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                SELECT CASE WHEN (task_id = 'refine_splio_report' AND state = 'success') THEN TRUE ELSE FALSE END
                FROM task_instance
                WHERE dag_id = 'refined_data__email{str_env}'
                  AND task_id = 'refine_splio_report'
                  AND DATE(start_date) = CURRENT_DATE
            """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_refined_splio_report.doc_mc = task_doc_md

    task_doc_md = """
    Use EmptyOperator to flag end of end merge_refined_open/click and Splio report tasks. <br />
    """
    end_refined = EmptyOperator(task_id='end_refined')
    end_refined.doc_md = task_doc_md

    with TaskGroup("simulate_open", tooltip="Tasks to simulate opens for clicks without opens.") as simulate_open:

        task_doc_md = """
        Use EmptyOperator to flag end of start refined open/click and Splio report tasks. <br />
        """
        start_refined = EmptyOperator(task_id='start_refined')
        start_refined.doc_md = task_doc_md

        task_doc_md = """
        We need to wait for refined open event to generate simulated open events
        """
        # @Doc --> https://airflow.readthedocs.io/en/1.9.0/_modules/airflow/operators/sensors.html
        wait_for_refined_open = SqlSensor(
            task_id='wait_for_refined_open',
            conn_id='airflow_db',
            timeout=60 * 60 * 4,  # 4 hours
            retries=24,
            poke_interval=60 * 10,  # 10 min
            mode="reschedule",
            sql="""
                    SELECT CASE WHEN (task_id = 'end_refine_open' AND state = 'success') THEN TRUE ELSE FALSE END
                    FROM task_instance
                    WHERE dag_id = 'refined_data__email{str_env}'
                      AND task_id = 'end_refine_open'
                      AND DATE(start_date) = CURRENT_DATE
                """.format(str_env='' if env == 'prod' else '_' + env)
        )
        wait_for_refined_open.doc_mc = task_doc_md

        task_doc_md = """
        We need to wait for refined open event to generate simulated open events
        """
        # @Doc --> https://airflow.readthedocs.io/en/1.9.0/_modules/airflow/operators/sensors.html
        wait_for_refined_click = SqlSensor(
            task_id='wait_for_refined_click',
            conn_id='airflow_db',
            timeout=60 * 60 * 4,  # 4 hours
            retries=24,
            poke_interval=60 * 10,  # 10 min
            mode="reschedule",
            sql="""
                    SELECT CASE WHEN (task_id = 'end_refine_click' AND state = 'success') THEN TRUE ELSE FALSE END
                    FROM task_instance
                    WHERE dag_id = 'refined_data__email{str_env}'
                      AND task_id = 'end_refine_click'
                      AND DATE(start_date) = CURRENT_DATE
                """.format(str_env='' if env == 'prod' else '_' + env)
        )
        wait_for_refined_click.doc_mc = task_doc_md

        task_doc_md = """
        Generate simulated clicks without opens from open/click refined data version.
        """
        simulate_email_event_open = BigQueryExecuteQueryOperator(
            task_id='simulate_email_event_open',
            sql='email/email_event_simulated_open.sql',
            params={
                "bq_project": bq_project,
                "is_full": generated_data__email.get("simulated_open").get("is_full"),
                "start_date": generated_data__email.get("simulated_open").get("start_date"),
                "end_date": generated_data__email.get("simulated_open").get("end_date"),
                "interval": generated_data__email.get("simulated_open").get("interval")
            }
        )
        simulate_email_event_open.doc_md = task_doc_md

        start_refined >> [wait_for_refined_open, wait_for_refined_click] >> end_refined \
            >> simulate_email_event_open

    with TaskGroup("compute_reactivity", tooltip="Tasks to compute reactivity.") as compute_reactivity:

        task_doc_md = """
        Use EmptyOperator to flag reactivity start. <br />
        """
        start_compute_reactivity = EmptyOperator(
            task_id='start_compute_reactivity')
        start_compute_reactivity.doc_md = task_doc_md

        task_doc_md = """
        Compute profile email last open & click date by base(nl, part, loy, ...).
        """
        email_reactivity_by_base = BigQueryExecuteQueryOperator(
            task_id='email_reactivity_by_base',
            sql='email/reactivity/base.sql',
            params={
                "bq_project": bq_project,
                "is_full": generated_data__email.get("email_reactivity").get("current").get("base").get("is_full"),
                "interval": generated_data__email.get("email_reactivity").get("current").get("base").get("interval")
            }
        )
        email_reactivity_by_base.doc_md = task_doc_md

        task_doc_md = """
        Compute profile email last open & click date by theme(NL Shopping, NL-Bestof, ...) .
        """
        email_reactivity_by_theme = BigQueryExecuteQueryOperator(
            task_id='email_reactivity_by_theme',
            sql='email/reactivity/theme.sql',
            params={
                "bq_project": bq_project,
                "is_full": generated_data__email.get("email_reactivity").get("current").get("theme").get("is_full"),
                "interval": generated_data__email.get("email_reactivity").get("current").get("theme").get("interval")
            }
        )
        email_reactivity_by_theme.doc_md = task_doc_md

        task_doc_md = """
        Use EmptyOperator to flag end of reactivity by email base(consent and theme). <br />
        """
        end_reactivity_base = EmptyOperator(task_id='end_reactivity_base')
        end_reactivity_base.doc_md = task_doc_md

        task_doc_md = """
        Compute profile email last open & click date by Splio universe.
        """
        email_reactivity_by_universe = BigQueryExecuteQueryOperator(
            task_id='email_reactivity_by_universe',
            sql='email/reactivity/universe.sql',
            params={
                "bq_project": bq_project,
                "is_full": generated_data__email.get("email_reactivity").get("current").get("universe").get("is_full"),
                "interval": generated_data__email.get("email_reactivity").get("current").get("universe").get("interval")
            }
        )
        email_reactivity_by_universe.doc_md = task_doc_md

        task_doc_md = """
        Use EmptyOperator to flag reactivity end. <br />
        """
        end_compute_reactivity = EmptyOperator(
            task_id='end_compute_reactivity')
        end_compute_reactivity.doc_md = task_doc_md

        start_compute_reactivity >> [email_reactivity_by_theme, email_reactivity_by_base] \
            >> end_reactivity_base \
            >> email_reactivity_by_universe \
            >> end_compute_reactivity

    task_doc_md = """
    Compute profile email last open & click date by user agent (ios, android, windows ...) and by base.
    """
    email_reactivity_by_agent_by_base = BigQueryExecuteQueryOperator(
        task_id='email_reactivity_by_agent_by_base',
        sql='email/reactivity/agent.sql',
        params={
            "bq_project": bq_project,
            "is_full": generated_data__email.get("email_reactivity").get("current").get("base").get("is_full"),
            "interval": generated_data__email.get("email_reactivity").get("current").get("base").get("interval")
        }
    )
    email_reactivity_by_agent_by_base.doc_md = task_doc_md

    task_doc_md = """
    Compute profile email last open & click date by brand.
    """
    email_reactivity_by_brand = BigQueryExecuteQueryOperator(
        task_id='email_reactivity_by_brand',
        sql='email/reactivity/brand.sql',
        params={
            "bq_project": bq_project,
            "is_full": generated_data__email.get("email_reactivity").get("current").get("brand").get("is_full"),
            "interval": generated_data__email.get("email_reactivity").get("current").get("brand").get("interval")
        }
    )
    email_reactivity_by_brand.doc_md = task_doc_md

    task_doc_md = """
    Compute profile email last open & click date global (cross-brand).
    """
    email_reactivity_global = BigQueryExecuteQueryOperator(
        task_id='email_reactivity_global',
        sql='email/reactivity/global.sql',
        params={
            "bq_project": bq_project,
            "is_full": generated_data__email.get("email_reactivity").get("current").get("global").get("is_full"),
            "interval": generated_data__email.get("email_reactivity").get("current").get("global").get("interval")
        }
    )
    email_reactivity_global.doc_md = task_doc_md

    task_doc_md = """
    Generate profile lifecycle by base based on their sub/unsub events.
    """
    compute_email_profile_lifecycle_by_base = BigQueryExecuteQueryOperator(
        task_id='compute_email_profile_lifecycle_by_base',
        sql='email/email_profile_lifecycle_by_base.sql',
        params={
            "bq_project": bq_project
        }
    )
    compute_email_profile_lifecycle_by_base.doc_md = task_doc_md

    task_doc_md = """
    Generate profile lifecycle by base based on their sub/unsub events.
    """
    compute_email_profile_lifecycle_by_base_new = BigQueryExecuteQueryOperator(
        task_id='compute_email_profile_lifecycle_by_base_new',
        sql='email/email_profile_lifecycle_by_base_new.sql',
        params={
            "bq_project": bq_project
        }
    )
    compute_email_profile_lifecycle_by_base_new.doc_md = task_doc_md

    task_doc_md = """
    Compute email reactivity history by consent.
    """
    email_reactivity_history_by_base = BigQueryExecuteQueryOperator(
        task_id='email_reactivity_history_by_base',
        sql='email/email_reactivity_history_by_base.sql',
        params={
            "bq_project": bq_project,
            "is_full": generated_data__email.get("email_reactivity_history").get("consent").get("is_full")
        }
    )
    email_reactivity_history_by_base.doc_md = task_doc_md

    task_doc_md = """
    Compute email reactivity history by brand.
    """
    email_reactivity_history_by_brand = BigQueryExecuteQueryOperator(
        task_id='email_reactivity_history_by_brand',
        sql='email/email_reactivity_history_by_brand.sql',
        params={
            "bq_project": bq_project
        }
    )
    email_reactivity_history_by_brand.doc_md = task_doc_md

    task_doc_md = """
    Compute email reactivity history globally.
    """
    email_reactivity_history_global = BigQueryExecuteQueryOperator(
        task_id='email_reactivity_history_global',
        sql='email/email_reactivity_history_global.sql',
        params={
            "bq_project": bq_project
        }
    )
    email_reactivity_history_global.doc_md = task_doc_md

    task_doc_md = """
    Compute email reactivity (sessions and viewed pages) history by object of mail, rogueone, profile and date of visit.
    """
    compute_email_profile_subject_stats = BigQueryExecuteQueryOperator(
        task_id='compute_email_profile_subject_stats',
        sql='email/email_profile_subject_stats.sql',
        params={
            "bq_project": bq_project,
            "ga4_project": ga4_project,
            "partial": generated_data__email.get("email_reactivity").get("object").get("partial"),
            "interval": generated_data__email.get("email_reactivity").get("object").get("interval"),
            "shoot_interval": generated_data__email.get("email_reactivity").get("object").get("shoot_interval"),
            "start_date": generated_data__email.get("email_reactivity").get("object").get("start_date"),
            "end_date": generated_data__email.get("email_reactivity").get("object").get("end_date")
        }
    )
    email_reactivity_history_global.doc_md = task_doc_md

    task_doc_md = """
            Generate daily counts of opens and openers with iOS15 device
        """
    splio_event_open_ios15 = BigQueryExecuteQueryOperator(
        task_id='splio_event_open_ios15',
        sql='email/splio_event_open_ios15.sql',
        params={
            "bq_project": bq_project,
            "is_full": generated_data__email.get("splio_event_open_ios15").get("is_full")
        }
    )
    splio_event_open_ios15.doc_md = task_doc_md

    task_doc_md = """
    Compute global last click datetime event by nl shopping theme for each profile_master_id.
    """
    nl_shopping_reactivity = BigQueryExecuteQueryOperator(
        task_id='nl_shopping_reactivity',
        sql='email/nl_shopping_theme_reactivity.sql',
        write_disposition='WRITE_APPEND',
        params={
            "bq_project": bq_project,
            "is_full": generated_data__email.get("email_reactivity").get("current").get("nl_shopping").get("is_full"),
            "interval": generated_data__email.get("email_reactivity").get("current").get("nl_shopping").get("interval")
        }
    )
    nl_shopping_reactivity.doc_md = task_doc_md

    task_doc_md = """
    Get last sub/unsub datetime by universe by profile
    """
    compute_last_sub_unsub_by_universe = BigQueryExecuteQueryOperator(
        task_id='compute_last_sub_unsub_by_universe',
        sql='email/last_sub_unsub_by_universe.sql',
        params={
            "bq_project": bq_project
        }
    )
    compute_last_sub_unsub_by_universe.doc_md = task_doc_md

    task_doc_md = """
    Clean duplicated and fake links in refined_data.splio_report_link
    """
    clean_link_nl = BigQueryExecuteQueryOperator(
        task_id='01_clean_link_nl',
        sql='email/01_clean_link_nl.sql',
        params={
            "bq_project": bq_project,
            "is_full": generated_data__email.get('clean_link_nl').get("is_full"),
            "time_interval": generated_data__email.get('clean_link_nl').get("time_interval"),
            "default_start_date": generated_data__email.get('clean_link_nl').get("default_start_date")
        }
    )
    clean_link_nl.doc_md = task_doc_md

    task_doc_md = """
    Compute block and structure parts in NL
    """
    generate_block_nl = BigQueryExecuteQueryOperator(
        task_id='02_generate_block_nl',
        sql='email/02_generate_block_nl.sql',
        params={
            "bq_project": bq_project,
            "truncate_table": generated_data__email.get('heatmap_block_nl').get("truncate_table"),
            "is_full": generated_data__email.get('heatmap_block_nl').get("is_full"),
            "time_interval": generated_data__email.get('heatmap_block_nl').get("time_interval"),
            "default_start_date": generated_data__email.get('heatmap_block_nl').get("default_start_date")
        }
    )
    generate_block_nl.doc_md = task_doc_md

    task_doc_md = """
        Compute revenue for NL clicks and article views per user (Disabled until the SA is granted access)
        """
    compute_revenue = BigQueryExecuteQueryOperator(
        task_id='compute_revenue',
        sql='email/compute_revenue.sql',
        params={
            "bq_project": bq_project
        }
    )
    compute_revenue.doc_md = task_doc_md

    task_doc_md = "Retrieve the unmapped revenue"
    retrieve_unmapped_revenue = BigQueryGetDataOperator(
        task_id='retrieve_unmapped_revenue',
        dataset_id='business_data',
        table_id='arpu_unmapped_revenue',
        gcp_conn_id='bq_matrix',
        max_results=100,
        selected_fields='alert, partner, month, ad_name, threshold, alert_value'
    )
    retrieve_unmapped_revenue.doc_md = task_doc_md

    alert_on_unmapped_revenue = PythonOperator(
        task_id='alert_on_unmapped_revenue',
        python_callable=alert_on_unmapped_revenue,
        provide_context=True
    )
    alert_on_unmapped_revenue.doc_md = task_doc_md

    task_doc_md = """
    Compute lifetime value (LTV) indicators for Pandora acquisitions
    Run only the first day of month on the previous month
    """
    compute_pandora_acquired_profiles = BigQueryExecuteQueryOperator(
        task_id='compute_pandora_acquired_profiles',
        sql='email/pandora_acquired_profiles.sql',
        params={
            "bq_project": bq_project,
            "is_full": generated_data__email.get('acquired_profiles').get("is_full"),
            "start_date": generated_data__email.get('acquired_profiles').get("start_date"),
            "end_date": generated_data__email.get('acquired_profiles').get("end_date")
        }
    )
    compute_pandora_acquired_profiles.doc_md = task_doc_md

    task_doc_md = """
    We need to wait for refined pandora events new to generate pandora activation NL-Shopping
    """
    # @Doc --> https://airflow.readthedocs.io/en/1.9.0/_modules/airflow/operators/sensors.html
    wait_for_refined_pandora_events = SqlSensor(
        task_id='wait_for_refined_pandora_events',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                SELECT CASE WHEN (task_id = 'refine_pandora_events' AND state = 'success') THEN TRUE ELSE FALSE END
                FROM task_instance
                WHERE dag_id = 'refined_data__email{str_env}'
                  AND task_id = 'refine_pandora_events'
                  AND DATE(start_date) = CURRENT_DATE
            """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_refined_pandora_events.doc_mc = task_doc_md

    task_doc_md = """
    Compute activation on NL-Shopping after acquisition on Part context
    """
    pandora_activation_nl_shopping = BigQueryExecuteQueryOperator(
        task_id='pandora_activation_nl_shopping',
        sql='email/pandora_activation_nl_shopping.sql',
        params={
            "bq_project": bq_project,
            "is_full": generated_data__email.get('pandora_activation_nl_shopping').get("is_full"),
            "start_date": generated_data__email.get('pandora_activation_nl_shopping').get("start_date"),
            "end_date": generated_data__email.get('pandora_activation_nl_shopping').get("end_date"),
            "default_start_date": generated_data__email.get('pandora_activation_nl_shopping').get("default_start_date"),
            "time_interval": generated_data__email.get('pandora_activation_nl_shopping').get("time_interval")
        }
    )
    pandora_activation_nl_shopping.doc_md = task_doc_md

    task_doc_md = """
    Wait for refined navigation data
    """
    wait_for_refined_navigation = SqlSensor(
        task_id='wait_for_refined_navigation',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                    SELECT CASE WHEN (task_id = 'refined_gan_navigation' AND state = 'success') THEN TRUE ELSE FALSE END
                    FROM task_instance
                    WHERE dag_id = 'pmc_gan_navigation{str_env}'
                      AND task_id = 'refined_gan_navigation'
                      AND DATE(start_date) = CURRENT_DATE
                """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_refined_navigation.doc_mc = task_doc_md

    task_doc_md = """
    Wait for refined cerise navigation data
    """
    wait_for_refined_navigation_cerise = SqlSensor(
        task_id='wait_for_refined_navigation_cerise',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                    SELECT CASE WHEN (task_id = 'refined_gan_navigation_cerise' AND state = 'success') THEN TRUE ELSE FALSE END
                    FROM task_instance
                    WHERE dag_id = 'pmc_gan_navigation{str_env}'
                      AND task_id = 'refined_gan_navigation_cerise'
                      AND DATE(start_date) = CURRENT_DATE
                """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_refined_navigation_cerise.doc_mc = task_doc_md

    task_doc_md = """
    Calculate acquisition cost for Pandora acquired/reactivated profiles.
    """
    arpu_acquisition_cost = BigQueryExecuteQueryOperator(
        task_id='arpu_acquisition_cost',
        sql='email/arpu_acquisition_cost.sql',
        params={
            "bq_project": bq_project
        }
    )
    arpu_acquisition_cost.doc_md = task_doc_md

    check_date = BranchPythonOperator(
        task_id="check_date",
        python_callable=_date_branch_profile_reactivity
    )
    check_date.doc_md = task_doc_md

    check_date_pandora_profile_activity = BranchPythonOperator(
        task_id="check_date_pandora_profile_activity",
        python_callable=_date_branch_pandora_profile_activity
    )
    check_date_pandora_profile_activity.doc_md = task_doc_md

    do_nothing = EmptyOperator(task_id='do_nothing')
    do_nothing.doc_md = 'do_nothing'

    task_doc_md = """
    Compute profile reactivity by month and consent
    """
    email_profile_consent_reactivity_by_month = BigQueryExecuteQueryOperator(
        task_id='email_profile_consent_reactivity_by_month',
        sql='email/email_profile_consent_reactivity_by_month.sql',
        params={
            "bq_project": bq_project,
            "is_full": generated_data__email.get('email_profile_consent_reactivity_by_month').get("is_full")
        }
    )
    email_profile_consent_reactivity_by_month.doc_md = task_doc_md

    task_doc_md = """
    Compute profile status (active / alive) by month and consent using PLC and reactivity data
    """
    email_profile_consent_status_by_month = BigQueryExecuteQueryOperator(
        task_id='email_profile_consent_status_by_month',
        sql='email/email_profile_consent_status_by_month.sql',
        params={
            "bq_project": bq_project,
            "open_activity_range": generated_data__email.get('email_profile_consent_status_by_month').get("open_activity_range"),
            "click_activity_range": generated_data__email.get('email_profile_consent_status_by_month').get("click_activity_range"),
            "start_date": generated_data__email.get('email_profile_consent_status_by_month').get("start_date"),
            "end_date": generated_data__email.get('email_profile_consent_status_by_month').get("end_date"),
            "is_full": generated_data__email.get('email_profile_consent_status_by_month').get("is_full")
        }
    )
    email_profile_consent_status_by_month.doc_md = task_doc_md

    task_doc_md = """
    Generate GA4 profile reactivity daily statistics by property
    """
    ga4_profile_reactivity = BigQueryExecuteQueryOperator(
        task_id='ga4_profile_reactivity_by_property_daily',
        sql='email/ga4_profile_reactivity_by_property_daily.sql',
        params={
            "bq_project": bq_project,
            "ga4_project": ga4_project,
            "userhub_project": userhub_project,
            "is_full": generated_data__email.get("ga4_profile_reactivity").get("is_full"),
            "start_date": generated_data__email.get("ga4_profile_reactivity").get("start_date"),
            "end_date": generated_data__email.get("ga4_profile_reactivity").get("end_date")
        }
    )
    ga4_profile_reactivity.doc_md = task_doc_md

    task_doc_md = """
    Generate PMC profile by subscription type
    """
    pmc_profile_subs = BigQueryExecuteQueryOperator(
        task_id='pmc_profile_subs',
        sql='email/pmc_profile_subs.sql',
        params={
            "bq_project": bq_project,
            "ga4_project": ga4_project,
            "userhub_project": userhub_project
        }
    )
    pmc_profile_subs.doc_md = task_doc_md

    task_doc_md = """
    Compute Pandora profiles activity history
    """
    pandora_profile_activity_by_consent = BigQueryExecuteQueryOperator(
        task_id='pandora_profile_activity_by_consent',
        sql='pandora/pandora_profile_activity_by_consent.sql',
        params={
            "bq_project": bq_project,
            "start_date": generated_data__email.get("pandora_profile_activity_by_consent").get("start_date")
        }
    )
    pandora_profile_activity_by_consent.doc_md = task_doc_md

    # TASKS
    variable_set()

    generate_service_card >> loy_event >> [
        export_events_to_queue, export_event_seq,
        export_service_card_oppositions]

    export_event_seq >> output_event_seq >> update_loy_opposition_email_event_seq

    segmentation_tags >> segmentation_tags_snapshots

    simulate_open >> compute_reactivity

    ga4_profile_reactivity
    pmc_profile_subs

    wait_for_refined_sub_unsub >> compute_email_profile_lifecycle_by_base >> start_refined \
        >> [wait_for_refined_open, wait_for_refined_click, wait_for_refined_splio_report] \
        >> end_refined >> [email_reactivity_history_by_base, splio_event_open_ios15, clean_link_nl]

    [wait_for_refined_open, wait_for_refined_click] >> end_refined >> [pmc_fill, nl_shopping_reactivity]

    email_reactivity_history_by_base >> [email_reactivity_history_by_brand, email_reactivity_history_global, check_date_pandora_profile_activity] #>> compute_revenue

    check_date_pandora_profile_activity >> [pandora_profile_activity_by_consent, do_nothing]

    [wait_for_refined_navigation, wait_for_refined_navigation_cerise, wait_for_refined_click, wait_for_refined_splio_report] \
        >> compute_email_profile_subject_stats

    compute_email_profile_lifecycle_by_base >> compute_last_sub_unsub_by_universe

    wait_for_refined_sub_unsub_new >> compute_email_profile_lifecycle_by_base_new

    clean_link_nl >> generate_block_nl

    retrieve_unmapped_revenue >> alert_on_unmapped_revenue

    simulate_email_event_open >> email_reactivity_by_agent_by_base >> end_reactivity_base \
        >> [email_reactivity_by_brand, email_reactivity_global]

    [wait_for_refined_pandora_events, compute_email_profile_lifecycle_by_base_new] >> compute_pandora_acquired_profiles

    [compute_pandora_acquired_profiles, email_reactivity_history_by_base] >> check_date \
        >> [email_profile_consent_reactivity_by_month, do_nothing]

    email_profile_consent_reactivity_by_month >> email_profile_consent_status_by_month

    [wait_for_refined_pandora_events, wait_for_refined_open, wait_for_refined_click] \
        >> pandora_activation_nl_shopping
