# gsutil cp dags/partner_webrivage_import_reactivity.py gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/
# gsutil cp dags/partner_webrivage_import_reactivity.py gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/

import os
from datetime import timedelta, datetime

from airflow import DAG
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.google.cloud.transfers.gcs_to_bigquery import GCSToBigQueryOperator
from airflow.models import Variable
from sftp_plugin.operators.ftp_folder_export_to_gcs_operator import FtpFolderExportToGoogleCloudStorageOperator

list_items = Variable.get("karinto_email_consents", deserialize_json=True)
buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']

path_import_reactivity_webrivage = 'partners/reactivity_import/webrivage/'

dag_name = 'partner_webrivage_import_reactivity'
bq_project = 'pm-prod-matrix'

email_on_failure = False
# ------------- FOR PREPROD ENV -----------------
env = os.environ.get("ENV")
if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    bq_project = 'pm-preprod-matrix'
# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2020, 7, 5, 00, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': True,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=40),
    'sla': None,
    'email_on_retry': False,
    'retries': 5,
    'retry_delay': timedelta(minutes=10),
    'depends_on_past': False,
    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'gcp_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_TRUNCATE',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',  # possibles values: INTERACTIVE and BATCH.
    'skip_leading_rows': 0,  # skeep header
    'autodetect': False,
    'max_bad_records': 0,
    'bucket': matrix_bucket,
    'allow_jagged_rows': True,
    'ignore_unknown_values': True,
    'allow_quoted_newlines': True,
}

CURRENT_DATE = '{{execution_date.strftime("%Y_%m_%d")}}'
NODASH_DATE = '{{execution_date.strftime("%Y%m%d")}}'

with DAG(
        dag_name,
        schedule_interval="0 0 * * *",
        tags=["partner"],
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=2),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/partners/import_tracking/webrivage/']
) as dag:
    # transfers folder to gcs
    ftp_to_gcs = FtpFolderExportToGoogleCloudStorageOperator(
        task_id='ftp_to_gcs',
        gcs_bucket=matrix_bucket,
        ftp_conn_id='ftp_webrivage',
        gcs_folder=path_import_reactivity_webrivage + '{{next_execution_date.strftime("%Y-%m-%d")}}',
        ftp_directory='import/reactivity',
        gcs_conn_id='gcs_matrix',
        file_pattern=[
            'event_(.+?)-{{ execution_date.strftime("%Y%m%d") }}',
            # 'event_(.+?)-{{(execution_date - macros.timedelta(days=1)).strftime("%Y%m%d")}}',
            # 'event_(.+?)-{{(execution_date - macros.timedelta(days=2)).strftime("%Y%m%d")}}',
            # 'event_(.+?)-{{(execution_date - macros.timedelta(days=3)).strftime("%Y%m%d")}}',
            # 'event_(.+?)-{{(execution_date - macros.timedelta(days=4)).strftime("%Y%m%d")}}',
            # 'event_(.+?)-{{(execution_date - macros.timedelta(days=5)).strftime("%Y%m%d")}}',
        ],
        add_if_missed='webrivage_id',
        list_items=list_items,
    )

    # gcs_check_file_exist = GCSObjectsWithPrefixExistenceSensor(
    #    task_id='gcs_check_file_exist',
    #    bucket=bucket,
    #    prefix= FILE_GCS_PATTERN
    # )

    ###
    # Import Brand: GCS CSV -> BQ
    ###
    import_schemas = [{"mode": "NULLABLE", "name": "email_sha256", "type": "STRING"},
                      {"mode": "NULLABLE", "name": "event_date", "type": "STRING"},
                      {"mode": "NULLABLE", "name": "event_type", "type": "STRING"},
                      {"mode": "NULLABLE", "name": "webrivage_id", "type": "STRING"},
                      {"mode": "NULLABLE", "name": "consent_id", "type": "STRING"}]


    import_to_bq = GCSToBigQueryOperator(
        task_id='import_to_bq',
        source_objects=[
            path_import_reactivity_webrivage + '{{(next_execution_date - macros.timedelta(days=5)).strftime("%Y-%m-%d")}}/event_*.csv',
            path_import_reactivity_webrivage + '{{(next_execution_date - macros.timedelta(days=4)).strftime("%Y-%m-%d")}}/event_*.csv',
            path_import_reactivity_webrivage + '{{(next_execution_date - macros.timedelta(days=3)).strftime("%Y-%m-%d")}}/event_*.csv',
            path_import_reactivity_webrivage + '{{(next_execution_date - macros.timedelta(days=2)).strftime("%Y-%m-%d")}}/event_*.csv',
            path_import_reactivity_webrivage + '{{(next_execution_date - macros.timedelta(days=1)).strftime("%Y-%m-%d")}}/event_*.csv',
            path_import_reactivity_webrivage + '{{next_execution_date.strftime("%Y-%m-%d")}}/event_*.csv'
        ],
        destination_project_dataset_table=bq_project + ':import.tracking_webrivage_' + CURRENT_DATE,
        source_format='CSV',
        field_delimiter=';',
        schema_fields=import_schemas,
    )

    # Format Data
    prepare_import = BigQueryExecuteQueryOperator(
        task_id='prepare_import',
        sql='00_prepare_import.sql',
        params={
            'table_import': bq_project + '.import.tracking_webrivage',
            'pmi': bq_project + '.store_matrix_email.profile_master_id',
        },
        destination_dataset_table=bq_project + ':prepare.tracking_webrivage_' + CURRENT_DATE,
    )

    # Store Data
    store_data = BigQueryExecuteQueryOperator(
        task_id='store_data',
        sql='01_store_data.sql',
        params={
            'prepare_table': bq_project + '.prepare.tracking_webrivage',
            'store_table': bq_project + '.store_tracking.webrivage_full_data',
        },
        # destination_dataset_table= bq_project + ':store_tracking.webrivage_full_data',
    )

    ftp_to_gcs >> import_to_bq >> prepare_import >> store_data
