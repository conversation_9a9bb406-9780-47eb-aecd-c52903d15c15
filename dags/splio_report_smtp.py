r"""
__PURPOSE:__<br />
This dag is used to retrieve the SMTP campaign reports from Splio SFTP and save it into bigquery.
<br/>

__METHODOLOGY:__<br />

We use the following tables in bigquery for this dag:

- store_partner.splio_report_smtp (for importing the relevant data)
- refined_data.splio_report (for merging into the overall data)

Remember: ds in airflow is already yesterday.<br />

"""

# gsutil cp dags/splio_report_smtp.py gs://europe-west1-prod-mozart-co-96b002ce-bucket/dags/

import json
import os
from datetime import timedelta, datetime
import json
from airflow.datasets import Dataset
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.google.cloud.transfers.gcs_to_bigquery import GCSToBigQueryOperator
from airflow.models import Variable
from airflow.operators.python import PythonOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.google.cloud.transfers.gcs_to_bigquery import GCSToBigQueryOperator
from collapsible_doc_dag import CDocDAG
from sftp_plugin.operators.bulk_sftp_to_gcs_operator import BulkSftpToGoogleCloudStorageOperator

# ---------------- Global Var -------------------
env = os.environ.get("ENV")
bq_project = f'pm-{env}-matrix'
str_env = '' if env == 'prod' else '_' + env
dag_name = f'splio_report_smtp{str_env}'
email_on_failure = True if env == 'prod' else False
gcs_matrix_uri = 'gs://it-data-prod-matrix-pipeline' if env == 'prod' else 'gs://it-data-preprod-matrix-preprod-pipeline'

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']

# -----------------------------------------------

#### Universes actives ####
# in case we lost it: splio_report_smtp
# {"__comment__": "This dag is used to import all snapshot for all splio universes.", "dag_universes_to_include": ["*"], "dag_universes_to_exclude": ["prisma_template","ce_mediego","pm_mediego"]}

dag_variable = Variable.get("splio_report_smtp", deserialize_json=True)

# select a valid universe used for the dag
universe_list = dag_variable["active_universes"]

# Create from the active universes the conn_id_list
conn_id_list = {f"sftp_splio_{universe}_fwd": f"/campaigns/{universe}_fwd_campaign_report_" for universe in universe_list}

# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2021, 3, 22, 4, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=30),
    'sla': None,
    'email_on_retry': False,
    # no need to retries, if no file found on gcs , boom direct
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
    'depends_on_past': False,

    # GCP CONNECTION
    'gcp_conn_id': 'gcs_matrix',
    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_APPEND',
    # possibles values: WRITE_DISPOSITION_UNSPECIFIED, WRITE_EMPTY, WRITE_TRUNCATE OR WRITE_APPEND (default: 'WRITE_EMPTY')
    'create_disposition': 'CREATE_IF_NEEDED',
    # possibles values: CREATE_DISPOSITION_UNSPECIFIED , CREATE_NEVER OR CREATE_IF_NEEDED (default: 'CREATE_IF_NEEDED')
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',  # possibles values: INTERACTIVE and BATCH.

}

f = open('/home/<USER>/gcs/data/json/splio_report_smtp_schemas.json')
splio_report_smtp_schemas = json.load(f)


def import_gcs_bulk_to_bq(ds_nodash, **context):
    files = [file.replace(gcs_matrix_uri + '/', '') for file in
             context['task_instance'].xcom_pull(task_ids='download_smtp_report_files')]

    import_to_bq = GCSToBigQueryOperator(
        task_id='import_to_bq',
        bucket=buckets['matrix'],
        source_objects=files,
        destination_project_dataset_table=bq_project + ':import.tracking_splio_smtp_report_data_' + ds_nodash,
        source_format='CSV',
        field_delimiter=';',
        skip_leading_rows=1,
        schema_fields=splio_report_smtp_schemas,
        autodetect=True,
        compression='gzip',
        max_bad_records=0,
        write_disposition='WRITE_TRUNCATE',
        dag=dag1

    )
    res = import_to_bq.execute(context)


# -------------------------------------------
# DAG
# -------------------------------------------
with CDocDAG(
        dag_name,
        schedule_interval="0 3 * * *",
        tags=["splio"],
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(minutes=50),
        catchup=False,
        description='Retrieve Splio SMTP Reports',
        doc_md=__doc__,
        template_searchpath=['/home/<USER>/gcs/data/sql/tracking/splio/smtp_reports/sql/']
) as dag1:
    task_doc_md = '''
    Save the SMTP campaign files into GCS<br />
    '''

    download_smtp_report_files = BulkSftpToGoogleCloudStorageOperator(
        task_id='download_smtp_report_files',
        # sftp
        sftp_conn_list=conn_id_list,
        sftp_filename='{{ ds_nodash }}_0130.csv',
        # gcs
        gcs_conn_id='gcs_matrix',
        gcs_bucket=buckets['matrix'],
        gcs_folder='splio/campaigns',
    )

    download_smtp_report_files.doc_md = task_doc_md

    task_doc_md = '''
    Take the campaign file from gcs and save it into BQ: import.tracking_splio_smtp_report_data_{{ ds_nodash }}<br />
    '''

    import_to_bq = PythonOperator(
        task_id='import_snapshots_to_bq',
        python_callable=import_gcs_bulk_to_bq,
        provide_context=True,
    )
    import_to_bq.doc_md = task_doc_md

    task_doc_md = '''
    Prepare the snapshot from import to prepare, with an expiration time at 5 days<br />
    '''
    store_smtp_reports = BigQueryExecuteQueryOperator(
        task_id='store_smtp_reports',
        sql='01_store_smtp_reports.sql',
        params={
            'bq_project': bq_project
        }
    )
    store_smtp_reports.doc_md = task_doc_md



    download_smtp_report_files \
        >> import_to_bq \
        >> store_smtp_reports
