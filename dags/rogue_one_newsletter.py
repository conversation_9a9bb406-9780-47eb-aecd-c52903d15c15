# gsutil cp dags/rogue_one_newsletter.py gs://europe-west1-mozart-cluster-2e910a39-bucket/dags/
import os
from datetime import datetime, timedelta

from airflow import DAG
from airflow.models import Variable
from console_plugin.operators.http_console import HttpConsoleOperator

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'start_date': datetime(2019, 9, 23),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': True,
    'email_on_retry': False,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'sla': timedelta(hours=5),
    'retries': 15,
    'retry_delay': timedelta(minutes=15),
    # http connection id
    'http_conn_id': 'http-rogue-one'
}

schedule_interval = "1 1 * * *"
env = os.environ.get("ENV")
if env != 'prod':
    schedule_interval = "30 5 * * *"

with DAG("rogue_one_newsletter",
         tags=["app-data"],
         schedule_interval=schedule_interval,
         catchup=False,
         max_active_runs=1,
         dagrun_timeout=timedelta(hours=5),
         default_args=default_args) as dag:
    update_nl_emails = HttpConsoleOperator(command='update-nl-emails')
    auto_validate_email = HttpConsoleOperator(command='auto-validate-nl')
    auto_create_nl = HttpConsoleOperator(command='auto-create-nl')

    update_nl_emails >> auto_create_nl >> auto_validate_email
