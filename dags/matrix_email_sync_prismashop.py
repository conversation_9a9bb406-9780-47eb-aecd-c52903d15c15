r"""
**PURPOSE**:
This DAG synchronizes data from BQ view provided by ITBI to consents in Matrix**It uses data from BQ table:**- `pm-prod-business-**abonnement:**export_it_data.email_prismashop_subscriptions`

**METHODOLOGY:**
Firstly, we snapshot data to make a diff with previous period and create sub and unsub events
Secondly, we insert data in PGSQL matrix__email_queue.email_event
"""

import os
from datetime import timedelta, datetime

from airflow.models import Variable
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from collapsible_doc_dag import CDocDAG
from matrix_plugin.operators.bigquery_to_email_event import BigQueryToEmailEventOperator

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']

dag_name = 'matrix_email_sync_prismashop'
bq_project = 'pm-prod-matrix'
bi_bq_project = 'pm-prod-business-abonnement'
email_on_failure = True
# ------------- FOR PREPROD ENV -----------------
env = os.environ.get('ENV')
if env != 'prod':
    dag_name = '{}_{}'.format(dag_name, env)
    email_on_failure = False
    bq_project = 'pm-preprod-matrix'
# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2022, 7, 31, 4, 00, 00),
    'email': [eval(Variable.get('airflow_email_alertes'))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=50),
    'sla': None,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'depends_on_past': False,
    'bucket': matrix_bucket,
    # BQ
    'bigquery_conn_id': 'bq_matrix',
    'gcp_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_TRUNCATE',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',  # possibles values: INTERACTIVE and BATCH.
    # PSQL Connection
    'postgres_conn_id': 'psql_matrix_email_app',
    'dbname': 'matrix',
}

searchpath = '/home/<USER>/gcs/data/sql/matrix_email_prismashop/'

with CDocDAG(
        dag_name,
        description='Sync Prismashop consents in Matrix Email',
        doc_md=__doc__,
        schedule_interval="20 2 * * *",
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=2),
        catchup=False,
        template_searchpath=[searchpath]
) as dag:
    task_doc_md = """
    Generate sub/unsub events
    unsub event generation will check refined_data.profile_email active consents to not generate dead events
    """
    prepare_bq_data = BigQueryExecuteQueryOperator(
        task_id='prepare_bq_data',
        sql='prepare_bq_data.sql',
        params={
            'bq_project': bq_project,
            'bi_bq_project': bi_bq_project
        },
    )
    prepare_bq_data.doc_md = task_doc_md

    task_doc_md = """
    Export sub/unsub/update_profile events to email_event queue
    """
    export_events = BigQueryToEmailEventOperator(
        task_id='export_events',
        destination_cloud_storage_object_path=
        'tmp_exports/{{ next_execution_date.strftime("%Y%m%d") }}/prismashop_email_event_{{ next_execution_date.strftime("%Y%m%d") }}.csv',
        import_table='matrix__email_tmp.prismashop_email_event',
        source_project_dataset_table='export_matrix_email.prismashop_email_event',
        bucket=matrix_bucket,
        trigger_rule='all_done'
    )
    export_events.doc_md = task_doc_md

    prepare_bq_data >> export_events
