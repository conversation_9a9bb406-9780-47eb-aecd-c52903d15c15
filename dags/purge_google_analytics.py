r"""
**PURPOSE:**

This DAG is used to purge GA raw data in order to save storage costs

**METHODOLOGY:**

We loop through each project in the JSON config file and create a TaskGroup

1. List datasets from each project
2. Check if there are datasets to process
    - if there are, continue the process
    - otherwise, stop processing the current project
3. List eligible tables inside each dataset
4. Check if there are tables to delete
    - if there are, continue the process
    - otherwise, stop processing the current dataset
5. Use multithread processing to delete tables
6. Log deleted tables inside a workspace table
7. Once all projects and datasets have been processed, collect data from workspace tables into a history table
8. Check if there are tables that have not been deleted

As of now,
**the current projects are included in the purge project:**- pm-prod-ga4
- prisma-gan-bigquery-ojd-export

**COMMAND TO TEST:** 

gsutil -m cp -R data/sql/purge/google_analytics/*  gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/purge/google_analytics/
gsutil -m cp -R dags/purge_google_analytics.py gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/
gsutil -m cp -R data/json/preprod/google_analytics_purge.json gs://europe-west1-preprod-mozart-2fa49086-bucket/data/json/preprod/
"""

import os
import json
import logging
from datetime import timedelta, datetime
from jinja2 import Template
import pandas as pd
from concurrent.futures import ThreadPoolExecutor, as_completed
from collapsible_doc_dag import CDocDAG
from google.cloud import bigquery

from airflow.utils.task_group import TaskGroup
from airflow.models import Variable
from airflow.operators.python import PythonOperator
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import BranchPythonOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryCheckOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator

# ------------ UTILITY FUNCTIONS -----------
def read_sql(sql_file_path):
    """
    Reads a SQL file.
    Args:
        sql_file_path: SQL file path
    Returns:
        A SQL template
    """
    try:
        with open(f"/home/<USER>/gcs/data/sql/purge/google_analytics/{sql_file_path}", mode="r",
                  encoding="utf-8-sig") as _f:
            sql_template = _f.read()
            return sql_template
    except Exception as _e:
        logging.error(f"Error reading file {sql_file_path}. Error: {str(_e)}")
        raise

def render_sql(sql_file_path, **kwargs):
    """
    Renders a SQL template using provided parameters.
    Args:
        sql_file_path: SQL file path
        kwargs: "params", the parameters to render
    Returns:
        A rendered SQL template
    """
    try:
            sql_template = read_sql(sql_file_path)
            logging.info(f"Rendered query:\n\n {Template(sql_template).render(**kwargs)}")  # Log query for debugging
            return Template(sql_template).render(**kwargs)
    except Exception as _e:
        logging.error(f"Error rendering SQL file {sql_file_path}: {str(_e)}")
        return ""


def list_assets(query):
    """
    List available assets (datasets or tables) in a BQ project.
    Args:
        query: rendered SQL query
    Returns:
        A list of assets (result from the query)
    """
    try:
        rows = bq_client.query(query).result()
        asset_list = [row[0] for row in rows]
        logging.info(f"Total assets to process: {len(asset_list)}\n")
        return asset_list
    except Exception as _e:
        logging.error(f"Failed to retrieve assets.\n\n Query: {query} \n\n Error: {str(_e)}")
        raise


def check_asset(_project_name, **kwargs):
    """
    Checks if there are tables or datasets to be processed.
    Args:
        _project_name: rendered SQL query
    Returns:
        The name of the branch to be executed next
    """
    task_instance = kwargs.get("task_instance")
    _dataset = kwargs.get("dataset", [])

    if kwargs["asset_type"] == "dataset":
        _list_datasets = task_instance.xcom_pull(task_ids=[f"{_project_name}.get_datasets"], key="return_value")[0]
        if _list_datasets:
            return f"{_project_name}.start_project"
        return f"{_project_name}.end_project"
    else:
        _list_tables = task_instance.xcom_pull(task_ids=[f"{_project_name}.{_dataset}.get_tables"], key="return_value")[0]
        if _list_tables:
            return f"{_project_name}.{_dataset}.delete_table"
        return f"{_project_name}.{_dataset}.end_dataset"


def delete_table(table_ref):
    """
    Deletes a table using the BigQuery hook.
    Args:
        table_ref: full name of the table to be deleted (project_id.dataset_id.table_name)
    Returns:
        A dict with the status of the API request (deleted: table deleted, error: error in deleting table)
    """
    try:
        bq_client.delete_table(table=table_ref)
        logging.info(f"Successfully deleted table: {table_ref}")
        return {"table": table_ref, "status": "deleted", "error": "None"}
    except Exception as _e:
        logging.error(f"Failed to delete table: {table_ref}. Error: {str(_e)}")
        return {"table": table_ref, "status": "failed", "error": str(_e)}


def insert_rows(target_table, data):
    """
    Inserts the payload from the delete table API call
    Args:
        target_table: full name of the table where the data will be stored
        data: the data to be inserted
    """
    bq_client.delete_table(table=target_table, not_found_ok=True)

    try:
        # Delete table if already exists
        bq_client.create_table(table=target_table)
        logging.info(f"Table {target_table} successfully created\n")

        # Use pd dataframe to improve performance
        df = pd.DataFrame(data)
        bq_client.load_table_from_dataframe(df, target_table).result()
        logging.info(f"Inserted {len(df)} rows into {target_table}")
    except Exception as _e:
        logging.error(f"Failed to insert rows into table {target_table}: {str(_e)}")
        raise


def prepare_payload(_future):
    """
    Prepares the payload from the delete table API call
    Args:
        _future: future object
    Returns:
        Formatted payload from the API call
    """
    payload = {
        "table": _future.result()["table"],
        "status": _future.result()["status"],
        "error": _future.result()["error"],
        "timestamp": datetime.now()
    }

    logging.info(f"Payload created successfully!\n\n {payload}")

    return payload


def log_processed_tables(_project_name, _dataset_id, **kwargs):
    """
    Logs payload from the API call into a BQ intermediate table
    Args:
        _project_name: name of the project (same as the TaskGroup id)
        _dataset_id: name of the dataset (same as the sub TaskGroup id)
    """
    _data = kwargs["task_instance"].xcom_pull(task_ids=[f"{_project_name}.{_dataset_id}.delete_table"], key="return_value")[0]
    insert_rows(target_table=f"{bq_project}.workspace.purge_ga_{_project_name}_{_dataset_id}", data=_data)


def process_tables(_project_name, _project_id, _dataset_id, **kwargs):
    """
    For each project and dataset, deletes BQ tables following a strict pattern by making API calls
    Args:
        _project_name: name of the project
        _dataset_id: dataset id to be used to render SQL file
        _project_id: id of the project
    Returns:
        List with the status of the processed API calls
    """

    # Extract project_dict from kwargs to use in delete_table function (inside ThreadPoolExecutor)
    project_dataset = f"{_project_name}.{_dataset_id}"
    tables_task_id = f"{project_dataset}.get_tables"
    task_instance = kwargs["task_instance"]
    tables_to_delete = task_instance.xcom_pull(task_ids=[tables_task_id], key="return_value")[0]

    processed_tables = []

    with ThreadPoolExecutor(max_workers=5) as executor:
        future_to_table = {executor.submit(delete_table,f"{_project_id}.{_dataset_id}.{table}"): table for table in tables_to_delete}

        for future in as_completed(future_to_table):
            processed_tables.append(prepare_payload(future))

    logging.info(f"Processed tables: {len(processed_tables)}")
    return processed_tables


# ------------ DAG VARIABLES -----------
dag_name = 'purge_google_analytics'
env = os.environ.get("ENV")
email_on_failure = True
gcp_conn_id = 'bq_matrix'
bigquery_conn_id = 'bq_matrix'
bq_project = f'pm-{env}-ga4'
ga_purge_json_path = f'/home/<USER>/gcs/data/json/{env}/google_analytics_purge.json'

if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False

try:
    with open(ga_purge_json_path, "r") as f:
        projects_config = json.load(f)
except Exception as e:
    logging.error(f"Error reading the configuration file at {ga_purge_json_path}: {str(e)}")
    raise

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2025, 1, 10, 4, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=10),
    'sla': None,
    'email_on_retry': False,
    # no need to retries, if no file found on gcs , boom direct
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
    'depends_on_past': False,

    # GCP CONNECTION
    'gcp_conn_id': gcp_conn_id,

    # BQ CONNECTION
    'bigquery_conn_id': bigquery_conn_id,
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_APPEND',
    'create_disposition': 'CREATE_IF_NEEDED',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE'
}

with CDocDAG(
        dag_name,
        description='Automated deletion of GA raw data',
        tags=["GA", "GA4"],
        doc_md=__doc__,
        schedule_interval="0 8 15 1 *",
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=4),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/purge/google_analytics/'],
        concurrency=10
) as dag:

    wait_all_projects = EmptyOperator(
        task_id="wait_all_projects",
        trigger_rule="all_done"  # Ensures it runs after all process_projects tasks
    )
    wait_all_projects.doc_md = "Empty task to indicate the end of all projects"

    ga_purge_history = BigQueryExecuteQueryOperator(
        task_id="ga_purge_history",
        sql="ga_purge_history.sql",
        params={
            'bq_project': bq_project
        },
        trigger_rule="all_done"
    )
    ga_purge_history.doc_md = "Backup data from intermediate tables into a history table"

    check_purge = BigQueryCheckOperator(
        task_id='check_purge',
        sql='check_purge.sql',
        params={
            'bq_project': bq_project
        }
    )
    check_purge.doc_md = "Check if there are tables that have not been deleted"

    for project_config in projects_config:
        project_id = project_config.get("project_id")
        project_name = project_config.get("project_name")

        with TaskGroup(group_id=f"{project_name}", tooltip=f"Group tasks for {project_name}") as process_projects:

            bq_client = bigquery.Client(project=project_id)

            dataset_query = render_sql('get_datasets.sql',
                                       project_dict=project_config,
                                       dag={"dag_id": dag_name},
                                       task={"task_id": f'{project_name}.get_datasets'}
                                       )

            get_datasets = PythonOperator(
                task_id="get_datasets",
                python_callable=list_assets,
                op_kwargs={
                    "query": dataset_query,
                },
                provide_context=True
            )
            get_datasets.doc_md = f"Get datasets from {project_id} project"

            check_dataset = BranchPythonOperator(
                task_id="check_dataset",
                python_callable=check_asset,
                op_kwargs={
                    '_project_name': project_name,
                    'asset_type': 'dataset'
                }
            )
            check_dataset.doc_md = f"Check datasets. 1 or more = continue purge"

            start_project = EmptyOperator(task_id='start_project')
            start_project.doc_md = "Empty task to indicate the start of the project processing"

            end_project = EmptyOperator(task_id='end_project')
            end_project.doc_md = "Empty task to indicate the end of the project processing"

            for dataset in list_assets(dataset_query):
                with TaskGroup(group_id=f"{dataset}", tooltip=f"Group to process {dataset}") as process_dataset:

                    table_query = render_sql('get_tables.sql',
                                             project_dict=project_config,
                                             dataset_id=dataset,
                                             dag={"dag_id": dag_name},
                                             task={"task_id": f'{project_name}.{dataset}.get_datasets'}
                                                )

                    get_tables = PythonOperator(
                        task_id="get_tables",
                        python_callable=list_assets,
                        op_kwargs={
                            "query": table_query,
                            "project_dict": project_config,
                            "dataset_id": dataset,
                        },
                        provide_context=True
                    )
                    get_tables.doc_md = f"Get tables from {project_id}.{dataset} to delete"

                    check_tables = BranchPythonOperator(
                        task_id="check_tables",
                        python_callable=check_asset,
                        op_kwargs={
                            '_project_name': project_name,
                            'asset_type': 'table',
                            'dataset': dataset
                        }
                    )
                    check_tables.doc_md = f"Check tables in {project_id}.{dataset}. 1 or more = continue purge"

                    delete_tables = PythonOperator(
                        task_id=f"delete_table",
                        python_callable=process_tables,
                        op_kwargs={
                            'sql_file_path': 'get_tables.sql',
                            '_project_name': project_name,
                            '_project_id': project_id,
                            '_dataset_id': dataset
                        },
                        provide_context=True
                    )
                    delete_tables.doc_md = f"Delete tables using an API call"

                    log_into_bq = PythonOperator(
                        task_id=f"log_into_bq",
                        python_callable=log_processed_tables,
                        op_kwargs={
                            '_project_name': project_name,
                            '_dataset_id': dataset
                        }
                    )
                    log_into_bq.doc_md = "Log deleted tables into an intermediate table"

                    end_dataset = EmptyOperator(task_id='end_dataset', trigger_rule='one_success')
                    end_dataset.doc_md = "Empty task to indicate the end of the dataset processing"

                    start_project >> get_tables >> check_tables >> [delete_tables, end_dataset]
                    delete_tables >> log_into_bq >> end_dataset

                process_dataset >> end_project

            get_datasets >> check_dataset >> [start_project, end_project]
            start_project >> process_dataset

        process_projects >> wait_all_projects

    wait_all_projects >> ga_purge_history >> check_purge
