r"""
_PICASSO_ update DMP segments

**PURPOSE:** :
This DAG takes data from BigQuery to push segment updates via API to the Mediarithmics DMP.**BQ tables are connected to a Google Spreadsheet https:**//docs.google.com/spreadsheets/d/1Vx-aJw-lZlv3R8m1wERmw8tyMdvkDr-H1pn5Z22O-3Y/edit#gid=1595419291

__METHODOLOGY__ :
The table segment_queries is filled by retrieving data from DMP API. (@see DAG `segment_manager_import`)
Users make updates on segments on the spreadsheet.
Updated Data is synchronized on BQ table segment_updates.

This DAG firstly checks if their are changes made in `segment_updates` table. If not, the process stops.
Then, the DAG performs the diff and fills `segment_actions` table.
After that, another check is applied on `segment_actions` table. If the table is empty, the process stops.

Table `segment_actions` is then transferred to GCS
Finally the operator is called to process the updates orders.
- It composes the updates files on GCS to a local file.
- It reads the CSV file
- And for each line it updates both the query and the segment
"""

from collapsible_doc_dag import CDocDAG
from airflow.models import Variable
from airflow.operators.python import <PERSON><PERSON>perator, BranchPythonOperator
from airflow.operators.dummy import DummyOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryCheckOperator
from airflow.providers.google.cloud.transfers.bigquery_to_gcs import BigQueryToGCSOperator
from airflow.providers.google.cloud.transfers.gcs_to_bigquery import GCSToBigQueryOperator
from airflow.utils.task_group import TaskGroup
from segmentmanager_plugin.operators.update_segment_operator import UpdateSegmentOperator
from segmentmanager_plugin.operators.create_segment_operator import CreateSegmentOperator
from segmentmanager_plugin.operators.delete_segment_operator import DeleteSegmentOperator
from segmentmanager_plugin.operators.get_segments_queries_operator import GetSegmentsQueriesOperator

import ast
import json
import os
from datetime import timedelta, datetime

import itdata_plugin

buckets = Variable.get('gcs_bucket_names', deserialize_json=True)
matrix_bucket = buckets['matrix']
bucket_path = 'dmp_segment_action/{day}'.format(day='{{ next_execution_date.strftime("%Y%m%d") }}')

dag_name = 'dmp_segment_action'
bq_project = 'pm-prod-matrix'
email_on_failure = False
# ------------- FOR PREPROD ENV -----------------
env = os.environ.get('ENV')
if env != 'prod':
    dag_name = '{}_{}'.format(dag_name, env)
    email_on_failure = False
    bq_project = 'pm-preprod-matrix'
# -----------------------------------------------
default_args = {
    'owner': 'airflow',
    'start_date': datetime(2023, 1, 21, 23, 0, 0),
    'email': [eval(Variable.get('airflow_email_alertes'))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=50),
    'sla': None,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'depends_on_past': False,
    'bucket': matrix_bucket,
    'gcs_bucket': matrix_bucket,
    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'gcp_conn_id': 'gcs_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_TRUNCATE',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',  # possibles values: INTERACTIVE and BATCH.
}

day = '{{ next_ds_nodash }}'
gcs_path = 'segment_manager/{}/'.format(day)
searchpath = '/home/<USER>/gcs/data/sql/segment_manager/'
schemas_names = ['dmp_segment_query.json']

def check_and_log_progress(**context):
    """Log progress and decide whether to continue processing"""
    import logging

    # Get results from previous check
    ti = context['task_instance']
    check_result = ti.xcom_pull(task_ids='initial_check_update')

    logging.info(f"Found {check_result} changes to process")

    if check_result > 0:
        return 'compute_diff_update'
    else:
        return 'no_changes_to_process'

with CDocDAG(
        dag_name,
        description='DMP Segment Action',
        doc_md=__doc__,
        schedule_interval='0 7-20 * * 1-5',
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=6),
        catchup=False,
        template_searchpath=[searchpath]
) as dag:
    # Loading JSON schemas
    def set_schemas():
        """
        Parses JSON schema and setting Airflow variables
        :return:
        """
        for schema in schemas_names:
            basename = schema.rsplit('.', 1)[0]
            load_schema = json.load(open('{}schemas/{}'.format(searchpath, schema), 'r'))
            var = itdata_plugin.set_variable_in_secret_manager('schema_{}'.format(basename), load_schema)

    task_doc_md = """
    Setting JSON schemas in Airflow variables 
    """
    set_schemas = PythonOperator(
        task_id='set_schemas',
        python_callable=set_schemas,
    )
    set_schemas.doc_md = task_doc_md

    with TaskGroup("data_preparation") as data_prep:
        task_doc_md = """
        Querying DMP to get Segment Queries in GCS CSV File
        """
        get_segments_queries_file = GetSegmentsQueriesOperator(
            task_id='get_segments_queries_file',
            mics_api_key=Variable.get('mics_apikey'),
            gcs_object_path=gcs_path
        )
        get_segments_queries_file.doc_md = task_doc_md

        task_doc_md = """
        Importing Queries in BQ import table
        """
        import_segments_queries = GCSToBigQueryOperator(
            task_id='import_segments_queries',
            source_objects=['{}segment_queries.csv'.format(gcs_path)],
            destination_project_dataset_table='{}:import.picasso_segment_queries_{}'.format(bq_project, day),
            schema_fields=ast.literal_eval(Variable.get('schema_dmp_segment_query')),
            field_delimiter=';',
            quote_character='"',
            allow_quoted_newlines=True,
            skip_leading_rows=1
        )
        import_segments_queries.doc_md = task_doc_md

        task_doc_md = """
        Truncate and replace segment queries data
        """
        insert_segments_queries = BigQueryExecuteQueryOperator(
            task_id='insert_segments_queries',
            sql='insert_segments_queries.sql',
            params={
                'bq_project': bq_project
            }
        )
        insert_segments_queries.doc_md = task_doc_md

        # Task dependencies within data_preparation group
        get_segments_queries_file >> import_segments_queries >> insert_segments_queries

    with TaskGroup("change_detection") as change_detection:
        task_doc_md = """
        Checking whether there are changes to process (updates, creates, or deletes)
        Otherwise, DAG stops
        """
        initial_check = BigQueryCheckOperator(
            task_id='initial_check_update',
            sql='actions/00_check_changes.sql',
            params={
                'bq_project': bq_project,
                'check_type': 'initial'
            }
        )
        initial_check.doc_md = task_doc_md

        task_doc_md = """
        Log processing progress and branch based on available changes
        """
        log_and_branch = BranchPythonOperator(
            task_id='log_and_branch',
            python_callable=check_and_log_progress,
            provide_context=True
        )
        log_and_branch.doc_md = task_doc_md

        no_changes = DummyOperator(
            task_id='no_changes_to_process'
        )

        task_doc_md = """
        Compute diff between BQ tables
        """
        compute_diff = BigQueryExecuteQueryOperator(
            task_id='compute_diff_update',
            sql='actions/02_compute_diff.sql',
            params={
                'bq_project': bq_project
            }
        )
        compute_diff.doc_md = task_doc_md

        task_doc_md = """
        Snapshot of the previous operations made
        We don't want to push the same orders day after day
        """
        snapshot = BigQueryExecuteQueryOperator(
            task_id='snapshot_update',
            sql='actions/01_snapshot.sql',
            params={
                'bq_project': bq_project
            }
        )
        snapshot.doc_md = task_doc_md

        task_doc_md = """
        Checking if computed actions are ready for execution
        Otherwise, DAG stops
        """
        execution_check = BigQueryCheckOperator(
            task_id='execution_check',
            sql='actions/00_check_changes.sql',
            params={
                'bq_project': bq_project,
                'check_type': 'execution'
            }
        )
        execution_check.doc_md = task_doc_md

        # Task dependencies within change_detection group
        initial_check >> log_and_branch >> [compute_diff, no_changes]
        compute_diff >> snapshot >> execution_check

    with TaskGroup("action_dispatch") as action_dispatch:
        task_doc_md = """
        Dispatching segment_actions table content depending on the action
        """
        dispatch_queries = BigQueryExecuteQueryOperator(
            task_id='dispatch_queries',
            sql='actions/04_dispatch.sql',
            params={
                'bq_project': bq_project
            }
        )
        dispatch_queries.doc_md = task_doc_md

        # Common GCS export configuration
        gcs_export_config = {
            'compression': 'NONE',
            'export_format': 'CSV',
            'field_delimiter': ';',
            'print_header': False
        }

        def create_gcs_export_task(action_type):
            task_doc_md = f"""
            Transferring picasso_segment_{action_type} table content to GoogleCloud Storage
            """
            task = BigQueryToGCSOperator(
                task_id=f'export_{action_type}_to_gcs',
                source_project_dataset_table=f'{bq_project}:store_picasso.picasso_segment_{action_type}',
                destination_cloud_storage_uris=[f'gs://{matrix_bucket}/{bucket_path}/{action_type}s_*.csv'],
                **gcs_export_config
            )
            task.doc_md = task_doc_md
            return task

        export_updates = create_gcs_export_task('update')
        export_creates = create_gcs_export_task('create')
        export_deletes = create_gcs_export_task('delete')

        # Task dependencies within action_dispatch group
        dispatch_queries >> [export_updates, export_creates, export_deletes]

    with TaskGroup("api_execution") as api_execution:
        task_doc_md = """
        Using the operator to post updates via mics API
        """
        execute_updates = UpdateSegmentOperator(
            task_id='execute_updates',
            mics_api_key=Variable.get('mics_apikey'),
            gcs_object_path=bucket_path + '/',
            gcs_object='updates_*.csv',
            exec_date='{{ next_execution_date.strftime("%Y%m%d") }}'
        )
        execute_updates.doc_md = task_doc_md

        task_doc_md = """
        Using the operator to post creates via mics API
        """
        execute_creates = CreateSegmentOperator(
            task_id='execute_creates',
            mics_api_key=Variable.get('mics_apikey'),
            gcs_object_path=bucket_path + '/',
            gcs_object='creates_*.csv',
            exec_date='{{ next_execution_date.strftime("%Y%m%d") }}'
        )
        execute_creates.doc_md = task_doc_md

        task_doc_md = """
        Using the operator to post deletes via mics API
        """
        execute_deletes = DeleteSegmentOperator(
            task_id='execute_deletes',
            mics_api_key=Variable.get('mics_apikey'),
            gcs_object_path=bucket_path + '/',
            gcs_object='deletes_*.csv',
            exec_date='{{ next_execution_date.strftime("%Y%m%d") }}'
        )
        execute_deletes.doc_md = task_doc_md

    # Updated dependencies with task groups
    set_schemas >> data_prep >> change_detection >> action_dispatch >> api_execution

    # Individual task dependencies within api_execution group
    export_updates >> execute_updates
    export_creates >> execute_creates
    export_deletes >> execute_deletes
