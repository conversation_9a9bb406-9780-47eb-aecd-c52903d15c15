r"""
**PURPOSE:**

This dag is used to create CRM tags by workflow. CRM tags are translated by : states and transitions. 
States and Transitions will be shared later through Splio (Datahub and Interaction) Batch Custom API, Profile 360, .... 
These tags are computed daily by using profile lifecycle and reactivity: 
    - profile_lifecycle_by_ 
    - last_activity_by_ 
    - last_activty_web_by_brand 

**TABLES:**

1. Email: 
    - `pm-prod-matrix.generated_data.email_profile_lifecycle_by_universe`
    - `pm-prod-matrix.generated_data.last_activity_by_universe`
    - `pm-prod-matrix.generated_data.email_profile_lifecycle_by_base`
    - `pm-prod-matrix.generated_data.last_activity_by_base`
    - `pm-prod-matrix.generated_data.email_profile_lifecycle_by_brand`
    - `pm-prod-matrix.generated_data.last_activity_by_brand`
    - `pm-prod-matrix.generated_data.email_profile_lifecycle_by_global`
    - `pm-prod-matrix.generated_data.last_activity_by_global`

2. Karinto: 
    - `pm-prod-matrix.store_karinto.splio_list`
    - `pm-prod-matrix.refined_data.email_base`
    - `pm-prod-matrix.store_karinto.universe`

3. Web:
    - `pm-prod-matrix.generated_data.last_activity_web_by_brand`

**METHODOLOGY:**

1. Compute truth table by workflow (Daily/Weekly/Monthly/....) (BQOperator) :

    - Tables: 
        - last sub/unsub by granularity by profile 
        - last open/click by granularity by profile 
        - last navigation by brand by profile 
    - Result: 
        - Generate state by profile by workflow based on reactivities 
        - Count number of state by profile by workflow 
        - Store result into `pm-prod-matrix.workspace.crm_state_count_by_` 

2. Check truth table(BranchPythonOperator & Python Operator):

    - Table: 
        - `pm-prod-matrix.workspace.crm_state_count_by_` 
        Result: 
        - If profile does not have any state --> Error --> notify by Email 
        - If profile at day D has more than 1 state --> Error  --> notify by Email 
        - Else, It's OK 

4. If OK: 

    - States:
        1. Generate States by workflow:   = _ 
            - `pm-prod-matrix.store_email_workflow.workflow-previous_` 
            - `pm-prod-matrix.store_email_workflow.workflow_` 

        2. Merge States --> Archive States daily 
            - `pm-prod-matrix.generated_data.workflow_state` 
            - `pm-prod-matrix.generated_data.workflow_state_history` 

    - Transitions:
        1. Generate Transitions by workflow:   = _ 
            - `pm-prod-matrix.store_email_workflow.workflow-transition_` 

        2. Merge Transitions --> Archive Transitions daily 
            - `pm-prod-matrix.generated_data.workflow_transition` 
            - `pm-prod-matrix.generated_data.workflow_transition_history` 

**More details:**

    - EPIC description: https://pmdtech.atlassian.net/browse/ITDATA-1548?atlOrigin=eyJpIjoiMThkYTkzMWUwZTYwNDQ2YThiMDk1MWY4ZDg1MDlhYWIiLCJwIjoiaiJ9 
    - Draw.io: https://drive.google.com/file/d/1slT1oPvGchZIoNksY9Ot6Ht890XdizgN/view?usp=sharing 
    - G-sheet: https://docs.google.com/spreadsheets/d/1pe4FaVgNxt4W981B_-Gi2XFHkQNj9Yb5agGCoRq8aAw/edit?usp=sharing 

**HOW TO:**

    - Airflow Variable : [{"granularity": "universe", "name_suffix":"voici", "column_name":"universe_name", "brand_trigram": ["voi"]] 
    - column_name: Name of column declared in perimeter CTE (data/sql/crm_tags/workspace/crm_state_count.sql#perimeter) 
    - granularity: universe, base, brand Email reactivity(last open & click) and lifecycle(last sub/unsub)
    - name_suffix: consent name/brand trigram/universe name. In case of multiple consents/brands/... concerned by the same workflow, use '|' between variables
    - brand_trigram: web navigation brand trigram
"""

import os
from datetime import timedelta, datetime, date
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from export_plugin.operators.batch_export_operator import BatchExportOperator
from airflow.operators.python import PythonOperator
from airflow.operators.python import BranchPythonOperator
from google.cloud import bigquery
from airflow.utils.email import send_email
from airflow.models import Variable
from collections import defaultdict
from collapsible_doc_dag import CDocDAG
import logging


# ------------- INIT VARIABLES -----------------

workflows_configs = Variable.get("crm_tags_configs", deserialize_json=True)
# get granularities list
granularities = list(set([config["granularity"] for config in workflows_configs]))
dag_name = 'crm_tag'
bq_project = 'pm-prod-matrix'
email_on_failure = True
recipient = '<EMAIL>'

# ------------- FOR PREPROD ENV -----------------

env = os.environ.get("ENV")
if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    bq_project = 'pm-preprod-matrix'
    recipient = '<EMAIL>'

# ------------- PUTYHON FUNCTIONS -----------------


def get_non_valid_pmi(**kwargs):
    """
        Return pmi with invalid state count by workflow name
        params:
            * bq_project: pm-<prod/preprod>-matrix
            * workflow_configs: crm_tags_configs Airflow variable
        return:
            * dict : {"<workflow_name>": {"<email_profile_master_id>": []}}
    """
    global dag_name

    # init BQ Client
    client = bigquery.Client(project=kwargs['bq_project'])
    # init payload
    payload = defaultdict(lambda: {})
    for workflow_config in kwargs['workflows_configs']:
        # execute SQL Query to get TOP 5 profiles with invalid states
        sql_query = """
            SELECT *
            FROM `{bq_project}.workspace.crm_state_count_by_{granularity}`
            WHERE
                -- non valid profiles
                nb_state > 1
                OR
                nb_state = 0
                LIMIT 5;
        """.format(bq_project=kwargs['bq_project'],
                   granularity=workflow_config['granularity'])
        sql_query = "-- mozart-id : {}.notify\n".format(dag_name) + sql_query
        query_result = client.query(sql_query)
        # fill payload
        payload[workflow_config['granularity'] + "_" + workflow_config['name_suffix']] = {
            row['email_profile_master_id']: row['states']
            for row in query_result}

    return payload


def _send_alert(**kwargs):
    """
        Send alert Email
        params:
            * _
        return:
            * Alert Email
    """

    # get non valid pmi
    payload = get_non_valid_pmi(**kwargs)
    # print non valid pmi
    logging.info("result is : {}".format(payload))
    # email subject
    subject = "CRM Pathway - Invalid Workflow"
    # email header
    email_header = ("Hello ! <br> " +
                    "<br><br> There are invalid pmi regarding to Workflow conditions ! " +
                    "Details are below ! <br><br>")

    # email Footer
    email_footer = ("<br><br>Kind regards, <br> "
                    "IT-Data"
                    )
    dynamic_content = ""
    first_part = ""
    for workflow_name, data in payload.items():
        first_part = "<br>Workflow <b>" + workflow_name + " </b> : </br>"
        for profile, state in data.items():
            logging.info('profile is :{}'.format(profile))
            logging.info('state is :{}'.format(state))
            dynamic_content += (
                "<ul>"
                "<li> profile: " +
                str(profile) + ", states: " + str(state) + "</li>"
                "</ul>"
            )
        dynamic_content = first_part + dynamic_content
    # email body
    email_body = email_header + dynamic_content + email_footer
    logging.info('Email is: {}'.format(email_body))
    send_email(to=recipient, subject=subject,
               html_content=email_body, mime_charset='utf-8', conn_id='sendgrid_default')
    return True


def _check_workflow(**kwargs):
    """
        Select Task based on workflow validity
        params:
            * _
        return:
            * send_alert TASK : Send alert for invalid pmi
            * compute_workflows_states TASK : Otherwise, compute workflow states
    """

    # get non valid pmi
    payload = get_non_valid_pmi(**kwargs)
    for workflow_name, invalid_pmi in payload.items():
        if not invalid_pmi:
            return 'compute_workflows_states'
        else:
            return 'notify'

# ------------- INIT DAG ITEMS -----------------


default_args = {
    'owner': 'airflow',
    'start_date': datetime(2022, 9, 29, 00, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=3),
    'sla': None,
    'email_on_retry': False,
    # no need to retries, if no file found on gcs , boom direct
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
    'depends_on_past': False,
    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'gcp_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_APPEND',
    'create_disposition': 'CREATE_IF_NEEDED',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE'
}

# ------------- DAG START ! -----------------

with CDocDAG(
        dag_name,
        schedule_interval="30 4 * * *",
        default_args=default_args,
        max_active_runs=1,
        concurrency=10,
        dagrun_timeout=timedelta(hours=3),
        catchup=False,
        description='Create CRM tags : states and transitions by workflow',
        doc_md=__doc__,
        template_searchpath=['/home/<USER>/gcs/data/sql/crm_tags/']
) as dag:

    # ------------- TASK 1 -----------------
    task_doc_md = """
    Count state number by profile fo each workflow
    """
    count_state_by_profile = BigQueryExecuteQueryOperator(
        task_id='count_state_by_profile',
        sql='workspace/crm_state_count.sql',
        params={
            'bq_project': bq_project,
            'granularities': granularities,
            'workflows_configs': workflows_configs
        }
    )
    count_state_by_profile.doc_md = task_doc_md

    # ------------- TASK 2 -----------------
    task_doc_md = """
    Check workflow Validity: <br />
        - KO: state number = 0 OR state number > 1 <br />
        - OK: state number = 1 <br />
    """
    branch = BranchPythonOperator(
        task_id="branch",
        python_callable=_check_workflow,
        op_kwargs={
            'bq_project': bq_project,
            'workflows_configs': workflows_configs
        }
    )
    branch.doc_md = task_doc_md

    # ------------- TASK 3 -----------------
    task_doc_md = """
    Send alert in case of invalid workflow
    """
    notify = PythonOperator(
        task_id="notify",
        python_callable=_send_alert,
        op_kwargs={
            'bq_project': bq_project,
            'workflows_configs': workflows_configs
        }
    )
    notify.doc_md = task_doc_md

    # ------------- TASK 4 -----------------
    task_doc_md = """
    - Generate CRM states by profile and workflow at Execution date.</ br>
    - Generate CRM states previous by profile and workflow.</ br>
    """
    compute_workflows_states = BigQueryExecuteQueryOperator(
        task_id='compute_workflows_states',
        sql='email_workflow/generate_state.sql',
        params={
            'bq_project': bq_project,
            'workflows_configs': workflows_configs
        }
    )
    compute_workflows_states.doc_md = task_doc_md

    # ------------- TASK 5 -----------------
    task_doc_md = """
    Merge CRM states by profile at Execution date.</ br>
    """
    merge_workflows_states = BigQueryExecuteQueryOperator(
        task_id='merge_workflows_states',
        sql='generated_data/merge_state.sql',
        params={
            'bq_project': bq_project,
            'workflows_configs': workflows_configs
        }
    )
    merge_workflows_states.doc_md = task_doc_md

    # ------------- TASK 6 -----------------
    task_doc_md = """
    Archive CRM states by profile Daily.</ br>
    """
    archive_workflows_states = BigQueryExecuteQueryOperator(
        task_id='archive_workflows_states',
        sql='generated_data/archive_state.sql',
        params={
            'bq_project': bq_project}
    )
    archive_workflows_states.doc_md = task_doc_md

    # ------------- TASK 7 -----------------
    task_doc_md = """
    Generate CRM transitions by profile and workflow at Execution date.</ br>
    """
    compute_workflows_transitions = BigQueryExecuteQueryOperator(
        task_id='compute_workflows_transitions',
        sql='email_workflow/generate_transition.sql',
        params={
            'bq_project': bq_project,
            'workflows_configs': workflows_configs
        }
    )
    compute_workflows_transitions.doc_md = task_doc_md

    # ------------- TASK 8 -----------------
    task_doc_md = """
    Merge CRM transitions by profile at Execution date.</ br>
    """
    merge_workflows_transitions = BigQueryExecuteQueryOperator(
        task_id='merge_workflows_transitions',
        sql='generated_data/merge_transition.sql',
        params={
            'bq_project': bq_project,
            'workflows_configs': workflows_configs
        }
    )
    merge_workflows_transitions.doc_md = task_doc_md

    # ------------- TASK 9 -----------------
    task_doc_md = """
    Archive CRM transitions by profile Daily.</ br>
    """
    archive_workflows_transitions = BigQueryExecuteQueryOperator(
        task_id='archive_workflows_transitions',
        sql='generated_data/archive_transition.sql',
        params={
            'bq_project': bq_project
        }
    )
    archive_workflows_transitions.doc_md = task_doc_md

    # Call TASKS
    count_state_by_profile >> branch >> [
        notify, compute_workflows_states]
    compute_workflows_states >> [
        merge_workflows_states,
        compute_workflows_transitions]
    merge_workflows_states >> archive_workflows_states
    compute_workflows_transitions >> \
        merge_workflows_transitions >> archive_workflows_transitions
