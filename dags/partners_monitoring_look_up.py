r"""
**PURPOSE:**
**https:**//app.diagrams.net/#G17_zp_WckZsCMIu3mc_RIF1UmqIdeP54u

Dag is used to generate external table and after we can query for an email, md5,
sha256 in order to understand where the profils went trough in term of proccessus.
"""

## gsutil cp dags/partners_monitoring_look_up.py gs://europe-west1-mozart-cluster-2e910a39-bucket/dags/

# gsutil -m cp -R data/sql/partners/monitoring/*  gs://europe-west1-mozart-cluster-2e910a39-bucket/data/sql/partners/monitoring/

from airflow.models import Variable
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from collapsible_doc_dag import CDocDAG

import os
from datetime import timedelta, datetime

# ----------------- CONFIG ----------------------
dag_name = 'partners_monitoring_look_up'
email_on_failure = True
email_on_retry = False

env = os.environ.get("ENV")

bq_project = 'pm-{}-matrix'.format(env)

# GCS specific config
buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
gcs_bucket = buckets['matrix']

# brand_variable = Variable.get("gcs_bucket_names", deserialize_json=True)['brand']
brand_variable = "gala"

if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    email_on_retry = False


# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2021, 8, 5, 00, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=50),
    'sla': None,
    'email_on_retry': email_on_retry,
    'retries': 2,
    'retry_delay': timedelta(hours=3),
    'depends_on_past': False,
    'bigquery_conn_id': 'bq_matrix',
    'gcp_conn_id': 'bq_matrix',
}

with CDocDAG(
        dag_name,
        description='Generate external table to be ready to query them',
        doc_md=__doc__,
        tags=["partner"],
        schedule_interval='0 1 * * *',  # once a day, at 2AM UTC
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=15),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/partners/']
) as dag:
    task_doc_md = """
    Only webrivage for the moment.<br />
    """
    generate_external_partners_table = BigQueryExecuteQueryOperator(
        task_id='generate_external_partners_table',
        sql='monitoring/00_generate_external_table.sql',
        use_legacy_sql=False,
        params={
            'bq_project': bq_project,
            'brand': brand_variable
        }
    )
    generate_external_partners_table.doc_md = task_doc_md
