r"""
**PURPOSE:**

Dag used to monitor webrivage export generic
"""

# gsutil cp dags/partner_webrivage_monitoring_exports.py  gs://europe-west1-mozart-cluster-2e910a39-bucket/dags/
#

# TO TEST IN preprod
# copy file from prod to preprod
# gsutil cp -r gs://it-data-prod-matrix-pipeline/partners_export/2022-07-28 gs://it-data-preprod-matrix-preprod-pipeline/partners_export/2022-07-28

import logging
import re
from datetime import datetime, timedelta

from airflow.models import Variable
from airflow.operators.python import PythonOperator
from airflow.utils.email import send_email
from collapsible_doc_dag import CDocDAG
from gcs_plugin.operators.gcs_list_operator import GoogleCloudStorageListOperator
from sftp_plugin.operators.list_files_ftps_operator import ListFilesFtpsOperator

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2020, 3, 12, 0, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': True,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=5),
    'sla': None,
    'email_on_retry': False,
    'retries': 0,
    'depends_on_past': False,
    'gcp_conn_id': 'bq_matrix',
}


def notify_email(files):
    title = "[Webrivage monitoring]: Ingestion de l'export non terminé"
    body = ("Bonjour <br> "
            "<br><br> L'Ingestion de partners_export par Webrivage n'est pas terminé ce jour à 8H30 UTC <br><br>"
            "<br><br>Ci-dessous les fichiers manquant <br><br>"
            "<br>" + str(files) + "<br> "
                                  "<br> <br>Cordialement, <br> "
                                  "Mozart"
            )

    print(body);

    recipient = eval(Variable.get("airflow_email_alertes"))
    cc = ", ".join(["<EMAIL>"])
    send_email(to=recipient, subject=title, html_content=body, cc=cc, mime_charset='utf-8', conn_id='sendgrid_default')


def filter_files(files, patterns=None, mode_include=True):
    """
    Filter a liste of files, against pattern_list, including or excluding files depending on mode_include.

    :param file: list of file names
    :type file: list
    :param patterns: list of regexp
    :type patterns: list
    :param mode_include: True : include, False ; exclude
    :type mode_include: bool
    """
    if patterns is None:
        patterns = []
    files_to_keep = []

    for file in files:
        keep = not mode_include
        for pattern in patterns:
            if re.match(pattern, file):
                keep = mode_include
                continue

        if keep:
            files_to_keep.append(file)

    return files_to_keep


def drop_prefix(prefix=None, list_files=None):
    files_list = False
    if prefix is not None and list_files is not None:
        files_list = []
        for file in list_files:
            files_list.append(file.replace(prefix, ''))

    return files_list


def get_missing_files(list_ftps_files, list_gcs_files):
    missing_files = []
    for file_gcs in list_gcs_files:
        check = False
        for file_ftps in list_ftps_files:
            if file_gcs == file_ftps:
                check = True

        if not check:
            missing_files.append(file_gcs)

    return missing_files


def run_check_files(ds, **kwargs):
    """
    Used to check if all needed files (sub, unsub, blacklist) are inside 
    the webrivage sftp inside archives/export

    :param ds: date of the file we need to check inside the gcs bucket (partners_export)
    :type ds: date
    """
    buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
    CURRENT_DATE = kwargs.get('templates_dict').get('CURRENT_DATE', None)
    logging.info("CURRENT_DATE : {}".format(str(CURRENT_DATE)))

    #####################################
    ### Retriveing sub files from gcs ###
    #####################################

    # We list all files for the generic export incremental for webrivage (only sub file)
    list_gcs_files_task_sub = GoogleCloudStorageListOperator(task_id='GCS_Files',
                                                             bucket=buckets["matrix"],
                                                             prefix='partners/export_incremental/' + CURRENT_DATE + '/webrivage/',
                                                             delimiter='|',
                                                             gcp_conn_id='gcs_matrix')

    list_gcs_files_sub = list_gcs_files_task_sub.execute(context=kwargs)
    logging.info("list_gcs_files_sub : {}".format(str(list_gcs_files_sub)))

    # we drop the prefix
    list_gcs_files_sub = drop_prefix(prefix='partners/export_incremental//' + CURRENT_DATE + '/webrivage/',
                                     list_files=list_gcs_files_sub)
    logging.info("list_gcs_files_sub after drop prefix : {}".format(str(list_gcs_files_sub)))

    # we don't need filter for sub file, inside gcs, we have only files that we need inside the sftp

    #######################################
    ### Retriveing unsub files from gcs ###
    #######################################

    # We list all files for the generic export incremental for webrivage (only unsub file)
    list_gcs_files_task_unsub = GoogleCloudStorageListOperator(task_id='GCS_Files',
                                                               bucket=buckets["matrix"],
                                                               prefix='partners/export_incremental/' + CURRENT_DATE + '/_global_unsub/',
                                                               delimiter='|',
                                                               gcp_conn_id='gcs_matrix')

    list_gcs_files_unsub = list_gcs_files_task_unsub.execute(context=kwargs)
    logging.info("list_gcs_files_unsub : {}".format(str(list_gcs_files_unsub)))

    # We need only keep part consent for webrivage (we exclude some after)
    patterns = [".*_part_.*"]
    list_gcs_files_unsub = filter_files(list_gcs_files_unsub, patterns=patterns, mode_include=True)
    logging.info("list_gcs_files_unsub after include filter : {}".format(str(list_gcs_files_unsub)))

    # we exclude neon, caminterrese and shopping part
    patterns = [".*_neon_.*", ".*_ca_minteresse_.*", ".*_shopping_part_.*"]
    list_gcs_files_unsub = filter_files(list_gcs_files_unsub, patterns=patterns, mode_include=False)
    logging.info("list_gcs_files_unsub after exclude filter : {}".format(str(list_gcs_files_unsub)))

    # we drop the prefix 
    list_gcs_files_unsub = drop_prefix(prefix='partners/export_incremental/' + CURRENT_DATE + '/_global_unsub/',
                                       list_files=list_gcs_files_unsub)
    logging.info("list_gcs_files_unsub after drop prefix : {}".format(str(list_gcs_files_unsub)))

    ##################################################
    ### Retriveing sftp files from archives/export ###
    ##################################################

    # We list here all files inside the repository archive/export
    list_ftps_files_task = ListFilesFtpsOperator(task_id='list_files_webrivage',
                                                 ftp_conn_id='ftp_webrivage',
                                                 ftp_directory='archive/export/')

    # We list sftp files from archives/export
    list_ftps_files = list_ftps_files_task.execute(context=kwargs)
    logging.info("list_ftps_files {}".format(str(list_ftps_files)))

    ##########################################################################
    ### Same process for blacklist but their are inside export/ repository ###
    ##########################################################################

    # We list all files for the generic export incremental for webrivage (only unsub file)
    list_gcs_files_task_blacklist = GoogleCloudStorageListOperator(task_id='GCS_Files',
                                                                   bucket=buckets["matrix"],
                                                                   prefix='partners/export_incremental/' + CURRENT_DATE + '/_global_blacklist/',
                                                                   delimiter='|',
                                                                   gcp_conn_id='gcs_matrix')

    list_gcs_files_blacklist = list_gcs_files_task_blacklist.execute(context=kwargs)
    logging.info("list_gcs_files_blacklist : {}".format(str(list_gcs_files_blacklist)))

    # We need only keep part consent for webrivage (we exclude some after)
    patterns = [".*_part_.*"]
    list_gcs_files_blacklist = filter_files(list_gcs_files_blacklist, patterns=patterns, mode_include=True)
    logging.info("list_gcs_files_blacklist after include filter : {}".format(str(list_gcs_files_blacklist)))

    # we exclude neon, caminterrese and shopping part
    patterns = [".*_neon_.*", ".*_ca_minteresse_.*", ".*_shopping_part_.*"]
    list_gcs_files_blacklist = filter_files(list_gcs_files_blacklist, patterns=patterns, mode_include=False)
    logging.info("list_gcs_files_blacklist after exclude filter : {}".format(str(list_gcs_files_blacklist)))

    # we drop the prefix 
    list_gcs_files_blacklist = drop_prefix(prefix='partners/export_incremental/' + CURRENT_DATE + '/_global_blacklist/',
                                           list_files=list_gcs_files_blacklist)
    logging.info("list_gcs_files_blacklist after drop prefix : {}".format(str(list_gcs_files_blacklist)))

    # We list here all files inside the repository export
    list_ftps_files_task_blacklist = ListFilesFtpsOperator(task_id='list_files_webrivage',
                                                           ftp_conn_id='ftp_webrivage',
                                                           ftp_directory='export/')

    # We list sftp files from archives/export
    list_ftps_files_blacklist = list_ftps_files_task_blacklist.execute(context=kwargs)
    logging.info("list_ftps_files_blacklist {}".format(str(list_ftps_files_blacklist)))

    #########################################################################
    ### We check if gcs files are inside the sftp for sub/unsub/blacklist ###
    #########################################################################

    # we concat sub and unsub to check if it is inside archive/export
    list_gcs_files = list_gcs_files_sub + list_gcs_files_unsub
    logging.info("all files needed inside sftp {}".format(str(list_gcs_files)))

    # we verify that all items selected from gcs are inside the sftp into the repository archives/export
    archives_diff = all(item in list_ftps_files for item in list_gcs_files)
    logging.info("archives_diff {}".format(str(archives_diff)))

    # we verify that all items selected from gcs blacklist are inside the sftp into the repository export
    blacklist_diff = all(item in list_ftps_files_blacklist for item in list_gcs_files_blacklist)
    logging.info("blacklist_diff {}".format(str(blacklist_diff)))

    # if no archives_diff then all good, otherwize, we send the mail with all missing files
    if archives_diff is False or blacklist_diff is False:
        archives_diff_files = get_missing_files(list_ftps_files, list_gcs_files)
        blacklist_diff_files = get_missing_files(list_ftps_files_blacklist, list_gcs_files_blacklist)
        logging.info("archives_diff files : {}".format(str(archives_diff_files)))
        logging.info("blacklist_diff files : {}".format(str(blacklist_diff_files)))
        diff_files = archives_diff_files + blacklist_diff_files
        logging.info("ALL diff files : {}".format(str(diff_files)))
        logging.info("email sent")
        notify_email(diff_files)


with CDocDAG('partner_webrivage_monitoring_exports',
             description='partner_webrivage_monitoring_exports',
             doc_md=__doc__,
             tags=["partner"],
             schedule_interval="30 8 * * *",
             default_args=default_args,
             max_active_runs=1,
             dagrun_timeout=timedelta(minutes=160),
             catchup=False) as dag:
    task_doc_md = """
    Trigger a python function to check if sub and unsub<br />
    files are all inside archives/export and if blacklist are inside export/<br />
    """

    webrivage_monitoring_delivery_task = PythonOperator(
        task_id='webrivage_monitoring_delivery_task',
        templates_dict={
            'CURRENT_DATE': '{{ next_ds }}'
        },
        python_callable=run_check_files,
        provide_context=True
    )

    webrivage_monitoring_delivery_task.doc_md = task_doc_md
