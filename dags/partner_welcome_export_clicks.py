r"""
**PURPOSE:**

This DAG is used to send sha256 email to welcome email for profil with clicks on the last 37 days for all brand, as well as the openers but non_clickers over the last 37 days
Careful, trigram is different for us.
"""

## gsutil cp dags/partner_welcome_export_clicks.py gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/

# gsutil -m cp -R data/sql/partners/exports/*  gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/partners/exports/

import os
from datetime import timedelta, datetime

import pendulum
from airflow.models import Variable
from airflow.operators.bash import BashOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.google.cloud.transfers.bigquery_to_gcs import BigQueryToGCSOperator
from collapsible_doc_dag import CDocDAG
from sftp_plugin.operators.gcs_to_ftp_operator import GoogleCloudStorageToFtpOperator

# ----------------- CONFIG ----------------------
dag_name = 'partner_welcome_export_clicks'
email_on_failure = True
email_on_retry = False

env = os.environ.get("ENV")

# BQ specific config
bq_project = 'pm-{}-matrix'.format(env)
bq_dataset_refined = 'refined_data'

# GCS specific config
buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
gcs_bucket = buckets['matrix']
sftp_bucket = buckets['sftp']
path_welcome_export = 'gs://it-data-prod-matrix-pipeline/partners/welcome_export/{{ next_ds }}/'
small_path_welcome_export = 'partners/welcome_export/{{ next_ds }}/'

if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    email_on_retry = False
    bq_project = 'pm-{}-matrix'.format(env)
    path_welcome_export = 'gs://it-data-preprod-matrix-preprod-pipeline/partners/welcome_export/{{ next_ds }}/'

# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2021, 8, 5, tzinfo=pendulum.timezone("Europe/Paris")),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=50),
    'sla': None,
    'email_on_retry': email_on_retry,
    'retries': 2,
    'retry_delay': timedelta(minutes=3),
    'depends_on_past': False,
    'bigquery_conn_id': 'bq_matrix',
    'gcp_conn_id': 'bq_matrix',
}


brands = [
    ("gala", "GAL", "GAL"),
    ("voici", "VCI", "VOI"),
    ("tele_loisirs", "TVL", "TEL"),
    ("capital", "CAP", "CAP"),
    ("ohmymag_fr", "OMM", "OMM"),
    ("cuisineactuelle", "CAC", "CAC"),
    ("femme_actuelle", "FAC", "FAC"),
    ("gentside_fr", "GNT", "GEN"),
    ("neon", "NEO", "NEO"),
    ("caminteresse", "CMI", "CAM"),
    ("programme_tv", "T2S", "T2S"),
    ("geo", "GEO", "GEO")
]



with CDocDAG(
        dag_name,
        description='export email_sha256 to welcome email for recent clickers',
        tags=["partner"],
        doc_md=__doc__,
        schedule_interval='0 4 * * *',  # Every day at 4AM CET
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=1),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/partners/exports/welcome/']
) as dag:


    for brand, trigram, trigram_internal in brands:


        ############# COMPUTE TABLES ##############
        task_doc_md = """
        This query is used to create table to export all sha256 to welcome email <br />
        According to their clicks activities<br />
        Table created for trigram is export_partner.export_welcome_trigram<br />
        """
        compute_clickers_each_brands = BigQueryExecuteQueryOperator(
            task_id='compute_clickers_{}'.format(trigram),
            sql='compute_clickers_by_brand.sql',
            use_legacy_sql=False,
            params={
                'bq_project': bq_project,
                'bq_table_brand': "export_welcome_{}".format(trigram),
                'brand': brand,
            }
        )
        compute_clickers_each_brands.doc_md = task_doc_md
        task_doc_md = """
        This query is used to create table to export all sha256 to welcome email <br />
        If they opened an email but didn't click on it<br />
        Table created for trigram is export_partner.export_welcome_trigram<br />
        """
        compute_non_clickers_each_brands = BigQueryExecuteQueryOperator(
            task_id='compute_openers_non_clickers_{}'.format(trigram),
            sql='compute_openers_non_clickers_by_brand.sql',
            use_legacy_sql=False,
            params={
                'bq_project': bq_project,
                'bq_table_brand': "export_welcome_non_clickers_{}".format(trigram),
                'brand': brand,
                'brand_trigram': trigram_internal
            }
        )
        compute_non_clickers_each_brands.doc_md = task_doc_md
        task_doc_md = """
                            This query is used to create table to export all sha256 to welcome email <br />
                            If they opened an email in a non iOS device but didn't click on it<br />
                            Table created for trigram is export_partner.export_welcome_trigram<br />
                            """
        compute_non_ios_non_clickers_each_brands = BigQueryExecuteQueryOperator(
            task_id='compute_non_ios_openers_non_clickers_{}'.format(trigram),
            sql='compute_non_ios_openers_non_clickers_by_brand.sql',
            use_legacy_sql=False,
            params={
                'bq_project': bq_project,
                'bq_table_brand': "export_welcome_non_ios_non_clickers_{}".format(trigram),
                'brand': brand,
                'brand_trigram': trigram_internal
            }
        )
        compute_non_ios_non_clickers_each_brands.doc_md = task_doc_md




        ############# EXPORT TO GCS ##############
        task_doc_md = """
        We export this table inside our GCS.<br />
        Care the file might to huge to go inside GCS in one shot.<br />
        """
        welcome_clicks_to_gcs = BigQueryToGCSOperator(
            task_id='welcome_{}_to_gcs'.format(trigram),
            destination_cloud_storage_uris=[
                path_welcome_export + 'import_bq/sha256_{}_{}_*.csv'.format(trigram, '{{ next_ds_nodash }}')],
            source_project_dataset_table='{}.export_partner.export_welcome_{}'.format(bq_project, trigram),
            print_header=False,
            field_delimiter='',
            force_rerun=True,
            export_format='CSV'
        )
        welcome_clicks_to_gcs.doc_md = task_doc_md
        task_doc_md = """
        We export this table inside our GCS.<br />
        Care the file migth to huge to go inside GCS in one shot.<br />
        """
        welcome_non_clickers_to_gcs = BigQueryToGCSOperator(
            task_id='welcome_{}_non_clickers_to_gcs'.format(trigram),
            destination_cloud_storage_uris=[
                path_welcome_export + 'import_bq/sha256_non_clickers_{}_{}_*.csv'.format(trigram, '{{ next_ds_nodash }}')],
            source_project_dataset_table='{}.export_partner.export_welcome_non_clickers_{}'.format(bq_project, trigram),
            print_header=False,
            field_delimiter='',
            force_rerun=True,
            export_format='CSV'
        )
        welcome_non_clickers_to_gcs.doc_md = task_doc_md
        task_doc_md = """
        We export this table inside our GCS.<br />
        Care the file migth to huge to go inside GCS in one shot.<br />
        """

        welcome_non_ios_non_clickers_to_gcs = BigQueryToGCSOperator(
            task_id='welcome_{}_non_ios_non_clickers_to_gcs'.format(trigram),
            destination_cloud_storage_uris=[
                path_welcome_export + 'import_bq/sha256_non_ios_non_clickers_{}_{}_*.csv'.format(trigram, '{{ next_ds_nodash }}')],
            source_project_dataset_table='{}.export_partner.export_welcome_non_ios_non_clickers_{}'.format(bq_project, trigram),
            print_header=False,
            field_delimiter='',
            force_rerun=True,
            export_format='CSV'
        )
        welcome_non_ios_non_clickers_to_gcs.doc_md = task_doc_md





        ############# COMPOSE FILES INTO ONE ##############
        # if the file is to important
        bash_command_compose = f"gsutil compose {path_welcome_export + 'import_bq/sha256_{}_{}_*.csv'.format(trigram, '{{ next_ds_nodash }}')} \
                        {path_welcome_export + 'sha256_{}_{}.csv'.format(trigram, '{{ next_ds_nodash }}')}"

        task_doc_md = """
        Used to concat all files into one<br />
        """
        compose_welcome = BashOperator(
            task_id='compose_welcome_{}'.format(trigram),
            bash_command=bash_command_compose,
        )
        compose_welcome.doc_md = task_doc_md

        bash_command_compose_non_clickers = f"gsutil compose {path_welcome_export + 'import_bq/sha256_non_clickers_{}_{}_*.csv'.format(trigram, '{{ next_ds_nodash }}')} \
                                {path_welcome_export + 'sha256_non_clickers_{}_{}.csv'.format(trigram, '{{ next_ds_nodash }}')}"
        task_doc_md = """
        Used to concat all files into one<br />
        """
        compose_welcome_non_clickers = BashOperator(
            task_id='compose_welcome_non_clickers_{}'.format(trigram),
            bash_command=bash_command_compose_non_clickers,
        )
        compose_welcome_non_clickers.doc_md = task_doc_md

        bash_command_compose_ios_non_clickers = f"gsutil compose {path_welcome_export + 'import_bq/sha256_ios_non_clickers_{}_{}_*.csv'.format(trigram, '{{ next_ds_nodash }}')} \
                                        {path_welcome_export + 'sha256_ios_non_clickers_{}_{}.csv'.format(trigram, '{{ next_ds_nodash }}')}"
        bash_command_compose_non_ios_non_clickers = f"gsutil compose {path_welcome_export + 'import_bq/sha256_non_ios_non_clickers_{}_{}_*.csv'.format(trigram, '{{ next_ds_nodash }}')} \
                                        {path_welcome_export + 'sha256_non_ios_non_clickers_{}_{}.csv'.format(trigram, '{{ next_ds_nodash }}')}"

        task_doc_md = """
                Used to concat all files into one<br />
                """
        compose_welcome_non_ios_non_clickers = BashOperator(
            task_id='compose_welcome_non_ios_non_clickers_{}'.format(trigram),
            bash_command=bash_command_compose_non_ios_non_clickers,
        )
        compose_welcome_non_ios_non_clickers.doc_md = task_doc_md



        ############# SEND TO SFTP ##############
        task_doc_md = """
        We export the brand file to the sftp <br />
        """
        export_welcome_to_ftp = GoogleCloudStorageToFtpOperator(
        task_id='export_welcome_{}_to_ftp'.format(trigram),
        gcs_bucket=buckets['matrix'],
        gcs_filename=small_path_welcome_export + 'sha256_{}_{}.csv'.format(trigram, '{{ next_ds_nodash }}'),
        ftp_conn_id='ftp_welcome_email',
        ftp_filename='foilist_{}_{}.csv'.format(trigram, '{{ next_ds_nodash }}')
        )
        export_welcome_to_ftp.doc_md = task_doc_md

        task_doc_md = """
                We export the non clickers brand file to the sftp <br />
                """
        export_welcome_non_clickers_to_ftp = GoogleCloudStorageToFtpOperator(
            task_id='export_welcome_non_clickers_{}_to_ftp'.format(trigram),
            gcs_bucket=buckets['matrix'],
            gcs_filename=small_path_welcome_export + 'sha256_non_clickers_{}_{}.csv'.format(trigram, '{{ next_ds_nodash }}'),
            ftp_conn_id='ftp_welcome_email',
            ftp_filename='ouvreurs_non_cliqueurs_30j_{}_{}.csv'.format(trigram, '{{ next_ds_nodash }}')
        )
        export_welcome_non_clickers_to_ftp.doc_md = task_doc_md

        task_doc_md = """
                        We export the ios non clickers brand file to the sftp <br />
                        """

        export_welcome_non_ios_non_clickers_to_ftp = GoogleCloudStorageToFtpOperator(
            task_id='export_welcome_non_ios_non_clickers_{}_to_ftp'.format(trigram),
            gcs_bucket=buckets['matrix'],
            gcs_filename=small_path_welcome_export + 'sha256_non_ios_non_clickers_{}_{}.csv'.format(trigram,
                                                                                         '{{ next_ds_nodash }}'),
            ftp_conn_id='ftp_welcome_email',
            ftp_filename='ouvreurs_non_ios_non_cliqueurs_30j_{}_{}.csv'.format(trigram, '{{ next_ds_nodash }}')
        )
        export_welcome_non_ios_non_clickers_to_ftp.doc_md = task_doc_md

        #One branch for the clickers, and one branch for the openers non clickers
        compute_clickers_each_brands >> welcome_clicks_to_gcs >> compose_welcome >> export_welcome_to_ftp
        compute_non_clickers_each_brands >> welcome_non_clickers_to_gcs >> compose_welcome_non_clickers >> export_welcome_non_clickers_to_ftp
        compute_non_ios_non_clickers_each_brands >> welcome_non_ios_non_clickers_to_gcs >> compose_welcome_non_ios_non_clickers >> export_welcome_non_ios_non_clickers_to_ftp
