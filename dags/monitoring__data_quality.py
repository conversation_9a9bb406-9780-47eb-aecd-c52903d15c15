r"""
**PURPOSE:**

This Dag is used for monitoring data_quality

**COMMAND TO TEST:** 
    gsutil -m cp -R dags/monitoring__data_quality.py**gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/
    gsutil -m cp -R data/sql/monitoring__data_quality/*  gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/monitoring__data_quality/
"""

import os
from datetime import timedelta, datetime
from google.cloud import bigquery
from airflow.models import Variable
from airflow.datasets import Dataset
from airflow.utils.trigger_rule import TriggerRule
import logging
from data_quality_plugin.operators.monitoring_data_quality_operator import GetDataQualityMetadataOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.operators.python import BranchPythonOperator
from airflow.operators.empty import EmptyOperator
from airflow.providers.common.sql.sensors.sql import SqlSensor
from collapsible_doc_dag import CDocDAG

# ----------------- CONFIG ----------------------
dag_name = 'monitoring__data_quality'

env = os.environ.get("ENV")
bq_project = 'pm-{}-matrix'.format(env)
email_on_failure = True

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
monitoring__data_quality__var = Variable.get("monitoring__data_quality", deserialize_json=True)
matrix_bucket = buckets['matrix']
mozart_bucket = buckets['mozart']

client = bigquery.Client(project=bq_project)

if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    # force preprod on other env
    bq_project = 'pm-preprod-matrix'

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2023, 5, 13, 2, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': False,
    'provide_context': True,
    'execution_timeout': timedelta(hours=8),
    'sla': None,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=20),
    'depends_on_past': False,

    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_APPEND',
    # GCP CONNECTION
    'gcp_conn_id': 'gcs_matrix',
    # PSQL Connection
    'postgres_conn_id': 'psql_matrix_email_app',

    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',  # possibles values: INTERACTIVE and BATCH.
}

def check_time():
    """"
        Check if execution time is 08h00.
    """
    current_timestamp = datetime.now()
    logging.info(f"Execution datetime is: {current_timestamp}!")
    if current_timestamp.hour == 8:
        return "execute_all"
    else:
        return "prisma_branch"

# ---  DAG ----
with CDocDAG(
        dag_name,
        description='Generated valuable data for email',
        tags=["monitoring"],
        doc_md=__doc__,
        schedule=[Dataset('bigquery://matrix/refined_data/prisma_click/'), Dataset('bigquery://matrix/refined_data/prisma_open/')],
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=10),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/monitoring__data_quality/'],
        concurrency=10
) as dag:

    task_doc_md = "Execute all tasks"
    execute_all = EmptyOperator(task_id="execute_all")
    execute_all.doc_md = task_doc_md

    task_doc_md = "Execute Prisma branch only"
    prisma_branch = EmptyOperator(task_id="prisma_branch")
    prisma_branch.doc_md = task_doc_md

    task_doc_md = """Define the branch to be executed based on the execution time"""
    check_branch = BranchPythonOperator(
        task_id="check_branch",
        python_callable=check_time,
    )
    check_branch.doc_md = task_doc_md

    task_doc_md = """
        We need to wait for refined open event to generate simulated open events
        """
    wait_for_matrix_refined_dags = SqlSensor(
        task_id='wait_for_matrix_refined_dags',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
            SELECT CASE WHEN state = 'success' THEN TRUE ELSE FALSE END
            FROM task_instance
            WHERE dag_id LIKE 'refined_data__%{str_env}'
                AND DATE(start_date) = CURRENT_DATE
        """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_matrix_refined_dags.doc_mc = task_doc_md

    task_doc_md = """
        Compute metadata KPI for `pm-prod-matrix.refined_data.*` 
    """
    monitoring_prod_matrix_refined_data = GetDataQualityMetadataOperator(
        task_id='monitoring_{bq_project}.{bq_dataset}'.format(
            bq_project='pm-prod-matrix',
            bq_dataset=monitoring__data_quality__var['pm-prod-matrix']['refined_data']['bq_dataset']
        ),
        execution_timeout=timedelta(hours=4),
        bq_project=bq_project,
        bq_dataset=monitoring__data_quality__var['pm-prod-matrix']['refined_data']['bq_dataset'],
        mode_full=monitoring__data_quality__var['pm-prod-matrix']['refined_data']['mode']['full'],
        mode_partial=monitoring__data_quality__var['pm-prod-matrix']['refined_data']['mode']['partial'],
        default_start_date=monitoring__data_quality__var['pm-prod-matrix']['refined_data']['full']["default_start_date"],
        quartile_interval=monitoring__data_quality__var['pm-prod-matrix']['refined_data']['quartile_interval'],
        partial_start_date=monitoring__data_quality__var['pm-prod-matrix']['refined_data']['partial']['partial_start_date'],
        partial_end_date=monitoring__data_quality__var['pm-prod-matrix']['refined_data']['partial']['partial_end_date']
    )
    monitoring_prod_matrix_refined_data.doc_md = task_doc_md

    task_doc_md = """
        Compute Prisma reactivity (open, click) KPIs from refined data for monitoring purposes.
    """
    compute__reactivity = BigQueryExecuteQueryOperator(
        task_id="compute_reactivity",
        sql="email_reactivity/prisma_reactivity.sql",
        params={
            "bq_project": bq_project,
            "is_full": monitoring__data_quality__var.get("prisma_reactivity").get("is_full"),
            "start_date": monitoring__data_quality__var.get("prisma_reactivity").get("start_date"),
            "end_date": monitoring__data_quality__var.get("prisma_reactivity").get("end_date")
        },
        trigger_rule=TriggerRule.NONE_FAILED
    )
    compute__reactivity.doc_md = task_doc_md

    check_branch >> [execute_all, prisma_branch]

    prisma_branch >> compute__reactivity

    execute_all >> [compute__reactivity, wait_for_matrix_refined_dags]
    wait_for_matrix_refined_dags >> monitoring_prod_matrix_refined_data
