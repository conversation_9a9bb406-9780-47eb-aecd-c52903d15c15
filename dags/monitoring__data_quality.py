r"""
**PURPOSE:**

This Dag is used for monitoring data_quality

Included processes:
- Prisma Reactivity: every 8h, computes clicks/opens in the last 8h
- BQ Metadata: daily task to compute BQ metadata. Used in Dashboard Governance and BQ Metadata DB
- CRM Tag: daily calculation of the volume of profiles in each CRM Tag state and anomalies (profiles with multiple states or no state at all)
- PEC vs PLC: daily calculation of unsync volume between the two bases (at global, consent and universe level)

**COMMAND TO TEST:**
    gsutil -m cp -R dags/monitoring__data_quality.py gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/
    gsutil -m cp -R data/sql/monitoring__data_quality/*  gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/monitoring__data_quality/
"""

import os
import json
from datetime import timedelta, datetime
from airflow.utils.task_group import TaskGroup
from airflow.models import Variable
from airflow.datasets import Dataset
from airflow.utils.trigger_rule import TriggerRule
from jinja2 import Template
import logging
from google.cloud import secretmanager_v1 as secretmanager
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.operators.python import BranchPythonOperator
from airflow.operators.empty import EmptyOperator
from collapsible_doc_dag import CDocDAG


# ============== USEFUL FUNCTIONS ==================

def check_time():
    """"
    Check if execution time is 08h00.
    """
    current_timestamp = datetime.now()
    logging.info(f"Execution datetime is: {current_timestamp}!")
    if current_timestamp.hour == 8:
        return "execute_all"
    else:
        return "prisma_branch"


def read_sql(sql_file_path):
    """
    Reads a SQL file.
    Args:
        sql_file_path: SQL file path
    Returns:
        A SQL template
    """
    try:
        with open(f"/home/<USER>/gcs/data/sql/monitoring__data_quality/bq/{sql_file_path}", mode="r",
                  encoding="utf-8-sig") as _f:
            sql_template = _f.read()
            return sql_template
    except Exception as _e:
        logging.error(f"Error reading file {sql_file_path}. Error: {str(_e)}")
        raise


def render_sql(sql_file_path, **kwargs):
    """
    Renders a SQL template using provided parameters.
    Args:
        sql_file_path: SQL file path
        kwargs: "params", the parameters to render
    Returns:
        A rendered SQL template
    """
    try:
        sql_template = read_sql(sql_file_path)
        logging.info(f"Rendered query:\n\n {Template(sql_template).render(**kwargs)}")  # Log query for debugging
        return Template(sql_template).render(**kwargs)
    except Exception as _e:
        logging.error(f"Error rendering SQL file {sql_file_path}: {str(_e)}")
        raise


def get_secret_value(project_id, secret_id):
    """
    Gets secret value from Google Secret Manager
    Args:
        project_id: Secret Manager's project id
        secret_id: secret's id
    Returns:
        Secret in JSON format
    """
    # Create the Secret Manager client
    secret_client = secretmanager.SecretManagerServiceClient()

    # Get latest version of the secret
    secret_name = f"projects/{project_id}/secrets/{secret_id}/versions/latest"

    # Access the secret version
    response = secret_client.access_secret_version(name=secret_name)
    json_str = response.payload.data.decode("UTF-8")
    return json.loads(json_str.replace("\n", ""))


# ============== CONFIGS ==================
dag_name = 'monitoring__data_quality'

env = os.environ.get("ENV")
bq_project = 'pm-{}-matrix'.format(env)
secret_project_id = "386359793924"
secret_id = "airflow-variables-monitoring__data_quality"
email_on_failure = True

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']
mozart_bucket = buckets['mozart']

if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    secret_project_id = "83423634369"

data_quality_var = get_secret_value(secret_project_id, secret_id)
bq_monitoring_var = data_quality_var.get("bq_metadata_monitoring", {})
prisma_reactivity_var = data_quality_var.get("prisma_reactivity", {})
pec_plc_unsync_var = data_quality_var.get("pec_plc_unsync", {})
crm_tag_var = data_quality_var.get("crm_tag", {})

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2023, 5, 13, 2, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': False,
    'provide_context': True,
    'execution_timeout': timedelta(hours=8),
    'sla': None,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=20),
    'depends_on_past': False,

    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_APPEND',
    # GCP CONNECTION
    'gcp_conn_id': 'gcs_matrix',
    # PSQL Connection
    'postgres_conn_id': 'psql_matrix_email_app',

    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',  # possibles values: INTERACTIVE and BATCH.
}


# ============== DAG ==================
with CDocDAG(
        dag_name,
        description='Generated valuable data for email',
        tags=["monitoring"],
        doc_md=__doc__,
        schedule=[Dataset('bigquery://matrix/refined_data/prisma_click/'), Dataset('bigquery://matrix/refined_data/prisma_open/')],
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=10),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/monitoring__data_quality/'],
        concurrency=10
) as dag:

    # EMPTY TASKS
    execute_all = EmptyOperator(task_id="execute_all")
    execute_all.doc_md = "Empty task to begin the Execute All branch"

    prisma_branch = EmptyOperator(task_id="prisma_branch")
    prisma_branch.doc_md = "Empty task to begin the Execute Prisma branch only"

    # FUNCTIONAL TASKS
    # Branch task to decide which branch to execute based on the execution time
    check_branch = BranchPythonOperator(
        task_id="check_branch",
        python_callable=check_time,
    )
    check_branch.doc_md = "Define the branch to be executed based on the execution time"

    # BQ Metadata Monitoring
    with TaskGroup("bq_metadata_monitoring", tooltip="BQ Metadata Monitoring tasks") as bq_metadata_monitoring:
        store__metadata = BigQueryExecuteQueryOperator(
            task_id="store__metadata",
            sql="bq/store__metadata.sql",
            params={
                "bq_project": bq_project,
                "metadata_config": bq_monitoring_var,
                "env": env
            },
            trigger_rule=TriggerRule.ALL_SUCCESS,
            outlets=[Dataset("bigquery://matrix/generated_data/store__bq_metadata/")]

        )
        store__metadata.doc_md = "Store BQ metadata"

        with TaskGroup("extract__metadata", tooltip="Extract BQ metadata") as extract__metadata:
            # Loop through each project in the variable
            for project in bq_monitoring_var:
                # Loop through the tuples (project, datasets)
                for project_name, datasets in project.items():

                    # Create a group for extracting metadata for all datasets inside a project
                    with TaskGroup(f"{project_name}", tooltip=f"Metadata extraction tasks for {project_name}"):

                        # Create a group for each dataset for better readability
                        for dataset in datasets:
                            get_dataset_info = BigQueryExecuteQueryOperator(
                                task_id=f"{dataset}",
                                sql="bq/extract__metadata.sql",
                                params={
                                    "bq_project": bq_project,
                                    "project_name": project_name,
                                    "dataset": dataset,
                                    "env": env
                                }
                            )
                            get_dataset_info.doc_md = f"Extract BQ metadata for {project_name} and {dataset} dataset"

        extract__metadata >> store__metadata

    # Prisma Reactivity
    prisma_reactivity = BigQueryExecuteQueryOperator(
        task_id="prisma_reactivity",
        sql="email_reactivity/prisma_reactivity.sql",
        params={
            "bq_project": bq_project,
            "is_full": prisma_reactivity_var.get("is_full"),
            "start_date": prisma_reactivity_var.get("start_date"),
            "end_date": prisma_reactivity_var.get("end_date")
        },
        trigger_rule=TriggerRule.NONE_FAILED
    )
    prisma_reactivity.doc_md = "Compute Prisma reactivity (open, click) KPIs from refined data for monitoring purposes."

    with TaskGroup("pec_plc_unsync", tooltip="Monitors the unsync between PEC and PLC in different levels") as pec_plc_unsync:

        task_doc_md = """
        Compute daily unsync between PEC and Profile lifecycle (new) by consent
        """
        unsync_by_consent = BigQueryExecuteQueryOperator(
            task_id='unsync_by_consent',
            sql='unsync_pec_plc/consent.sql',
            params={
                "bq_project": pec_plc_unsync_var.get("bq_project")
            }
        )
        unsync_by_consent.doc_md = task_doc_md

        task_doc_md = """
        Compute daily unsync between PEC and Profile lifecycle (new) global
        """
        unsync_global = BigQueryExecuteQueryOperator(
            task_id='unsync_global',
            sql='unsync_pec_plc/global.sql',
            params={
                "bq_project": pec_plc_unsync_var.get("bq_project")
            }
        )
        unsync_global.doc_md = task_doc_md

        task_doc_md = """
        Compute daily unsync between PEC and Profile lifecycle (new) by universe
        """
        unsync_by_universe = BigQueryExecuteQueryOperator(
            task_id='unsync_by_universe',
            sql='unsync_pec_plc/universe.sql',
            params={
                "bq_project": pec_plc_unsync_var.get("bq_project")
            }
        )
        unsync_by_universe.doc_md = task_doc_md

    task_doc_md = """
    Compute daily volume of profiles by CRM Tag state and identifies anomalies (multiples states or no state for a given granularity level and granularity name)
    """
    crm_tag = BigQueryExecuteQueryOperator(
        task_id='crm_tag',
        sql='crm_tag/crm_tag_monitoring.sql',
        params={
            "bq_project": crm_tag_var.get("bq_project", bq_project),
            "is_full": crm_tag_var.get("is_full", False)
        }
    )
    crm_tag.doc_md = task_doc_md

    # DEPENDENCIES
    check_branch >> [execute_all, prisma_branch]
    prisma_branch >> prisma_reactivity

    execute_all >> [prisma_reactivity, bq_metadata_monitoring, pec_plc_unsync, crm_tag]
