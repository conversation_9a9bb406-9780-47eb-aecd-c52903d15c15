r"""
**PURPOSE:**

This DAG is used to import postmaster data into bq.

#**from developer doc:**
**https:**//developers.google.com/gmail/postmaster/quickstart/python?hl=fr&authuser=1

#**steps:**# 1/ create a quickstart.py script in your dev environnement (local)
# 2/**oauth to https:**//postmaster.google.com/ with <EMAIL>
# 3/ Verify that OAuth consent screen in SAFE project should be set with User type = External, Publishing status= In production
# 3/**generate oauth key in SAFE projet with type:**Desktop app / Ordinateur de bureau then download json file in the same folder with quickstart.py
# 4/ execute command `python3 quickstart.py` <NAME_EMAIL> to validate oauth process to get a file [token.pickle]
# 5/ update data/resources/postmaster_token files

#**DS:**
**https:**//lookerstudio.google.com/u/0/reporting/55c3d9de-ec1a-445e-ba87-0ad554f39ce1/page/p_45u9c81ptc
"""
import itdata_plugin

# --------------
"""

gsutil cp dags/postmaster_import.py gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/
gsutil cp data/scripts/postmaster_api_call.py gs://europe-west1-preprod-mozart-2fa49086-bucket/data/scripts/
gsutil cp data/scripts/script_import_postmaster_to_gcs.sh gs://europe-west1-preprod-mozart-2fa49086-bucket/data/scripts/
gsutil cp data/resources/postmaster_token/* gs://europe-west1-preprod-mozart-2fa49086-bucket/data/resources/postmaster_token/
gsutil cp -r data/sql/postmaster_import/* gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/postmaster_import/

"""
# --------------

import ast
import json
import os
from datetime import timedelta, datetime

import pendulum
from airflow.models import Variable
from airflow.operators.bash import BashOperator
from airflow.operators.email import EmailOperator
from airflow.operators.python import PythonOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryValueCheckOperator
from airflow.providers.google.cloud.transfers.gcs_to_bigquery import GCSToBigQueryOperator
from collapsible_doc_dag import CDocDAG

# ----------------- CONFIG ----------------------
dag_name = 'postmaster_import'
email_on_failure = False
email_on_retry = True

env = os.environ.get("ENV")

# BQ specific config
bq_project = 'pm-{}-matrix'.format(env)

# GCS specific config
buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
gcs_bucket = buckets['matrix']

bq_dataset_import = 'import'
bq_dataset_store = 'store'
postmaster_import = 'postmaster_import'
postmaster_store = 'postmaster_store'

schemas_names = ['postmaster_schemas_import.json']
searchpath = '/home/<USER>/gcs/data/sql/postmaster_import/'

if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    email_on_retry = False
# -----------------------------------------------

# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2021, 8, 5, tzinfo=pendulum.timezone("Europe/Paris")),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=50),
    'sla': None,
    'email_on_retry': email_on_retry,
    'retries': 1,
    'retry_delay': timedelta(minutes=3),
    'depends_on_past': False,
    'bigquery_conn_id': 'bq_matrix',
    'gcp_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
}

with CDocDAG(
        dag_name,
        description='Automatisation of the sample checking from mics',
        tags=["mics"],
        doc_md=__doc__,
        schedule_interval='0 12 * * *',  # once a day, at 12AM CET
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=15),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/postmaster_import/', '/home/<USER>/gcs/data/scripts/']
) as dag:
    # Loading JSON schemas
    def set_schemas():
        """
        Parses JSON schema and setting Airflow variables
        :return:
        """
        for schema in schemas_names:
            basename = schema.rsplit('.', 1)[0]
            load_schema = json.load(open('{}schemas/{}'.format(searchpath, schema), 'r'))
            var = itdata_plugin.set_variable_in_secret_manager('schema_{}'.format(basename), load_schema)


    ds_nodash = '{{ ds_nodash }}'

    task_doc_md = """
    bash operator to refactor the file before import him into BQ<br />
    <br />
    """

    import_to_gcs = BashOperator(
        task_id='import_to_gcs',
        bash_command='script_import_postmaster_to_gcs.sh',
        params={
            'bucket': gcs_bucket,
            'python_file': 'postmaster_api_call.py',
            'postmaster_dir': 'postmaster',
            'import_data': 'import_data',
            'import_filename': 'import_postmaster_tools_info.csv'
        }
    )
    import_to_gcs.doc_md = task_doc_md

    # importing schema to airflow variable

    task_doc_md = """
    Setting JSON schemas in Airflow variables 
    """
    set_schemas = PythonOperator(
        task_id='set_schemas',
        python_callable=set_schemas
    )
    set_schemas.doc_md = task_doc_md

    task_doc_md = """
    import the postmaster infos to BQ<br />
    <br />
    """

    # import to BQ [GCS TO BQ]
    # Va aller dans le import
    import_postmaster_to_bq = GCSToBigQueryOperator(
        task_id='import_postmaster_to_bq',
        source_objects=['postmaster/{{ ds_nodash }}/import_data/{{ ds_nodash }}_import_postmaster_tools_info.csv'],
        destination_project_dataset_table=bq_project + ':' + bq_dataset_import + '.' + postmaster_import + '_{}'.format(
            ds_nodash),
        source_format='CSV',
        autodetect=False,
        schema_fields=ast.literal_eval(Variable.get('schema_postmaster_schemas_import')),
        write_disposition='WRITE_TRUNCATE',
        bucket=gcs_bucket
    )

    import_postmaster_to_bq.doc_md = task_doc_md

    # check table volume
    check_import_table_data = BigQueryValueCheckOperator(
        task_id='check_import_table_data',
        sql='0_check_import_table.sql',
        pass_value=True,
        params={
            'import_table': bq_dataset_import + '.' + postmaster_import
        }
    )
    check_import_table_data.doc_md = 'check import table data before merge to store'

    notif_body = (
            "Bonjour,"
            "<br> Aucune donnée n'a été importée depuis PostMaster Api <br>"
            "Vérifier le dags: postmaster_import.<br>"
            "<br> Cordialement,"
            "<br> Mozart (env= " + env + ") <br>"
    )

    # Send email confirmation
    send_email_notif = EmailOperator(
        task_id='send_email_notif',
        to='<EMAIL>' if env.lower() == 'prod' else "<EMAIL>",
        subject='[Dags: postmaster_import] Alerte PostMaster Api - Table vide',
        cc=None,
        trigger_rule='one_failed',
        html_content=notif_body,
        conn_id='sendgrid_default'
    )
    send_email_notif.doc_md = 'send an alert to the technical team'

    task_doc_md = """
    Used to store information into the store<br />
    We use a merge to concatenate informations<br />
    """

    store_postmaster = BigQueryExecuteQueryOperator(
        task_id='store_postmaster',
        sql='store_postmaster.sql',
        use_legacy_sql=False,
        trigger_rule='all_success',
        params={
            'bq_project': bq_project,
            'bq_dataset_import': bq_dataset_import,
            'bq_dataset_store': bq_dataset_store,
            'postmaster_store': postmaster_store,
            'postmaster_import': postmaster_import
        }
    )
    store_postmaster.doc_md = task_doc_md

    import_to_gcs >> set_schemas >> import_postmaster_to_bq >> check_import_table_data >> \
        [send_email_notif, store_postmaster]

