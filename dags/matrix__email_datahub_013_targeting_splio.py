r"""
**PURPOSE:**
**This dag is used to:**- Synchroniser le refus de ciblage pub d’un profil PMC vers les univers NL.

**METHODOLOGY:**

- Depuis la table `pm-prod-userhub.refined_data.pmc_profile`:
- Synchroniser le refus de ciblage pub (pmc_personalized_ads = False) d’un profil PMC vers les univers NL.

**COMMAND TO TEST:** 

- gsutil cp dags/matrix__email_datahub_013_targeting_splio.py gs://europe-west1-mozart-cluster-2e910a39-bucket/dags/ 
- gsutil -m cp -R data/sql/matrix_email/11_export_splio_datahub_013.sql**gs://europe-west1-mozart-cluster-2e910a39-bucket/data/sql/matrix_email/
"""

import os
from datetime import timedelta, datetime

from airflow.models import Variable
from export_plugin.operators.export_splio_bigquery_operator import ExportSplioBigQueryOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from collapsible_doc_dag import CDocDAG

dag_name = 'matrix__email_datahub_013_targeting_splio'

env = os.environ.get("ENV")
bq_project = 'pm-{}-userhub'.format(env)
gcs_matrix_uri = 'gs://it-data-prod-matrix-pipeline'
email_on_failure = True
# ------------- FOR PREPROD ENV -----------------
env = os.environ.get("ENV")
if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    gcs_matrix_uri = 'gs://it-data-preprod-matrix-preprod-pipeline'
# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2022, 8, 15, 00, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=50),
    'sla': None,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'depends_on_past': False,

    'gcp_conn_id': 'gcs_matrix',
    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_TRUNCATE',
    'allow_large_results': True,
    'flatten_results': False,
}

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']
mozart_bucket = buckets['mozart']

datahub_contact_013 = Variable.get("datahub_contact_013", deserialize_json=True)
splio_universes_sftp = Variable.get("splio_universes_sftp", deserialize_json=True)
splio_universes_all = Variable.get("splio_universes_all", deserialize_json=True)
splio_universes = {u: splio_universes_all[u] for u in datahub_contact_013['universes'] if u in splio_universes_all}

with CDocDAG(
        dag_name,
        description='Synchronize the refusal of advertising targeting from a PMC profile to the NL universes',
        doc_md=__doc__,
        schedule_interval="45 4 * * *",
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=1),
        catchup=False,
        tags=["datahub"],
        template_searchpath=['/home/<USER>/gcs/data/sql/matrix_email/']) as dag:
    task_doc_md = """
        Update store pmc ads targeting table & compute diff datahub_013 for export
    """
    prepare_datahub_013 = BigQueryExecuteQueryOperator(
        task_id="prepare_datahub_013",
        sql="10_prepare_datahub_013_pmc_ads.sql",
        params={
            'env': env,
            'full_export': datahub_contact_013["full_export"]
        }
    )
    prepare_datahub_013.doc_md = task_doc_md

    # export needed data for datahub013
    export_splio_datahub_013 = ExportSplioBigQueryOperator(
        task_id='export_splio_datahub_013',
        sql='11_export_splio_datahub_013.sql',
        params={
            'interval': datahub_contact_013["interval"],
            'full_export': datahub_contact_013["full_export"],
            'universe_id': '{universe_id}',
            'env': env
        },
        destination_dataset_table_temporary='temp.{universe}_contacts_013_{splio_sequence}',
        universes=splio_universes,
        universes_sftp=splio_universes_sftp,
        datahub_file_template='{universe}_contacts_013_{splio_sequence}*.csv',
        splio_sequence='{{ next_execution_date.strftime("%Y%m%d_%H%M") }}',
        bucket=matrix_bucket,
        bucket_path='export_splio/datahub_013/{{ next_execution_date.strftime("%Y%m%d_%H%M") }}/',
        destination_cloud_storage_path_uris=gcs_matrix_uri,
        splio_remote_dir='imports/',
        flux_ref=13,
        bq_project=bq_project,
    )
    export_splio_datahub_013.doc_md = 'export data to Splio Nl universes'

    task_doc_md = """
        Update store datahub 013 pmc ads targeting
    """
    refresh_store_datahub_013 = BigQueryExecuteQueryOperator(
        task_id="refresh_store_datahub_013",
        sql="12_update_store_datahub_013.sql",
    )

    refresh_store_datahub_013.doc_md = task_doc_md

    prepare_datahub_013 >> export_splio_datahub_013 >> refresh_store_datahub_013
