r"""
_PICASSO_ import segment data
"""

import ast
import json
import os
from datetime import timedelta, datetime

from airflow.models import Variable
from airflow.operators.python import PythonOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.google.cloud.transfers.gcs_to_bigquery import GCSToBigQueryOperator
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from bq_plugin.operators.bigquery_to_psql_operator import BigQueryToPostgresOperator
from collapsible_doc_dag import CDocDAG
from segmentmanager_plugin.operators.get_segments_operator import GetSegmentsOperator
from segmentmanager_plugin.operators.get_third_party_segments_operator import GetThirdPartySegmentsOperator
import itdata_plugin

buckets = Variable.get('gcs_bucket_names', deserialize_json=True)
matrix_bucket = buckets['matrix']

dag_name = 'segment_manager_import'
bq_project = 'pm-prod-matrix'
email_on_failure = True
# ------------- FOR PREPROD ENV -----------------
env = os.environ.get('ENV')
if env != 'prod':
    dag_name = '{}_{}'.format(dag_name, env)
    email_on_failure = False
    bq_project = 'pm-preprod-matrix'
# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2021, 10, 27, 4, 00, 00),
    'email': [eval(Variable.get('airflow_email_alertes'))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=360),
    'sla': None,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'depends_on_past': False,
    'bucket': matrix_bucket,
    'gcs_bucket': matrix_bucket,
    # GCP CONNECTION
    'gcp_conn_id': 'gcs_matrix',
    'gcs_conn_id': 'gcs_matrix',
    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_TRUNCATE',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',  # possibles values: INTERACTIVE and BATCH.
    # bq import csv options
    'source_format': 'CSV',
    'skip_leading_rows': 0,  # skip header
    'autodetect': True,
    'max_bad_records': 0,
    # pgsql
    'postgres_conn_id': 'psql_picasso_app',
    'temp_bucket': matrix_bucket
}

day = '{{ next_ds_nodash }}'
gcs_path = 'segment_manager/{}/'.format(day)
searchpath = '/home/<USER>/gcs/data/sql/segment_manager/'
schemas_names = ['dmp_segment.json', 'dmp_segment_query.json', 'dfp_thirdparty_segment.json']
table = 'picasso.segment'
columns = 'id, ref, data, source, type, vertical, name, source_new, type_new, cpm, name_new, short_description, cpc, emails, userpoints, cookies, first_ids, utiq_ids, ad_seg_id, ad_seg_name, ad_seg_description, ad_seg_size, ad_seg_mobile_web_size, ad_seg_idfa, ad_seg_adid, ad_seg_ppid, combined_first_ids, combined_emails, combined_userpoints, combined_gam_ids, combined_utiq_ids'
def_table = 'picasso_import.dmp_segment_definition'
def_columns = 'short_description, definition'

with CDocDAG(
        dag_name,
        description='Segment Manager Import',
        tags=["segment_manager"],
        doc_md=__doc__,
        schedule_interval='1 7 * * *',
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=6),
        catchup=False,
        template_searchpath=[searchpath]
) as dag:
    # Loading JSON schemas
    def set_schemas():
        """
        Parses JSON schema and setting Airflow variables
        :return:
        """
        for schema in schemas_names:
            basename = schema.rsplit('.', 1)[0]
            load_schema = json.load(open('{}schemas/{}'.format(searchpath, schema), 'r'))
            var = itdata_plugin.set_variable_in_secret_manager('schema_{}'.format(basename), load_schema)


    task_doc_md = """
    Setting JSON schemas in Airflow variables 
    """
    set_schemas = PythonOperator(
        task_id='set_schemas',
        python_callable=set_schemas,
    )
    set_schemas.doc_md = task_doc_md

    task_doc_md = """
    Querying DMP and DFP to get all segments metadata in CSV file
    """
    get_segments_file = GetSegmentsOperator(
        task_id='get_segments_file',
        mics_api_key=Variable.get('mics_apikey'),
        gcs_object_path=gcs_path,
        dfp_key_file='/home/<USER>/gcs/data/resources/json_keyfiles/dfp_keyfile.json',
        execution_timeout=timedelta(hours=4),
    )
    get_segments_file.doc_md = task_doc_md

    task_doc_md = """
    Importing in BQ import table
    """
    import_segments = GCSToBigQueryOperator(
        task_id='import_segments',
        source_objects=['{}segments.csv'.format(gcs_path)],
        destination_project_dataset_table='{}:import.picasso_segment_{}'.format(bq_project, day),
        schema_fields=ast.literal_eval(Variable.get('schema_dmp_segment')),
        field_delimiter=';',
        quote_character='',
        allow_quoted_newlines=True,
        skip_leading_rows=1
    )
    import_segments.doc_md = task_doc_md

    task_doc_md = """
    Truncate and replace segment data
    """
    insert_segments = BigQueryExecuteQueryOperator(
        task_id='insert_segments',
        sql='insert_segments.sql',
        params={
            'bq_project': bq_project
        }
    )
    insert_segments.doc_md = task_doc_md

    task_doc_md = """
    Importing segment data in PostgreSQL
    """
    pg_import = BigQueryToPostgresOperator(
        task_id='pg_import',
        source_export_dataset_table='{}:temp.export_segments_{}'.format(bq_project, day),
        destination_table='picasso.segment',
        truncate_destination_table=True,
        bucket=matrix_bucket,
        destination_cloud_storage_object_path='picasso/{{ next_ds_nodash }}/picasso_segment_{{ next_ds_nodash }}.csv',
        source_query='export_segments.sql',
        source_query_destination_table='{}:temp.export_segments_{}'.format(bq_project, day),
        params={
            'bq_project': bq_project
        },
        copy_expert_query="COPY {} ({}) FROM STDIN WITH CSV DELIMITER AS '\t' NULL AS '\\N'".format(table, columns),
        prepare_query=False,
    )
    pg_import.doc_md = task_doc_md

    task_doc_md = """
    Setting is_main field in picasso.segment table
    """
    main_segments = SQLExecuteQueryOperator(
        task_id='main_segments',
        sql='main_segments.sql',
        conn_id='psql_picasso_app',
    )
    main_segments.doc_md = task_doc_md

    task_doc_md = """
    Filling PGSQL display table with new entries and cleaning deleted entries
    """
    pg_display = SQLExecuteQueryOperator(
        task_id='pg_display',
        sql='display_table.sql',
        conn_id='psql_picasso_app',
    )
    pg_display.doc_md = task_doc_md

    gcp_project_id = os.environ.get("GCP_PROJECT_ID")

    task_doc_md = """
    Copy definitions in a temp table
    """
    definition_copy = BigQueryExecuteQueryOperator(
        task_id='definition_copy',
        sql='definition_copy.sql',
        impersonation_chain= f'composer-node@{gcp_project_id}.iam.gserviceaccount.com',
        impersonation_scopes=[
                'https://www.googleapis.com/auth/bigquery',
                'https://www.googleapis.com/auth/cloud-platform',
                'https://www.googleapis.com/auth/drive'
        ],
        location='EU',
        params={
            'bq_project': bq_project
        }
    )

    task_doc_md = """
    Export DMP segment definition data into PGSQL
    """
    definition_import = BigQueryToPostgresOperator(
        task_id='definition_import',
        source_export_dataset_table='{}:temp.export_definitions_{}'.format(bq_project, day),
        destination_table='picasso_import.dmp_segment_definition',
        truncate_destination_table=True,
        bucket=matrix_bucket,
        destination_cloud_storage_object_path='picasso/{{ next_ds_nodash }}/picasso_segment_definition.csv',
        source_query='export_definitions.sql',
        source_query_destination_table='{}:temp.export_definitions_{}'.format(bq_project, day),
        params={
            'bq_project': bq_project
        },
        copy_expert_query="COPY {} ({}) FROM STDIN WITH CSV DELIMITER AS '\t' NULL AS '\\N'".format(def_table, def_columns),
        prepare_query=False,
        use_impersonation_chain=True,
    )
    definition_import.doc_md = task_doc_md

    task_doc_md = """
    Insert definition in DMP segment table
    """
    insert_definition = SQLExecuteQueryOperator(
        task_id='insert_definition',
        sql='insert_definition.sql',
        conn_id='psql_picasso_app',
    )
    insert_definition.doc_md = task_doc_md

    task_doc_md = """
    Querying DFP to get all 3rd party segments metadata in CSV file
    """
    get_thirdparty_segments_file = GetThirdPartySegmentsOperator(
        task_id='get_thirdparty_segments_file',
        gcs_object_path=gcs_path,
        dfp_key_file='/home/<USER>/gcs/data/resources/json_keyfiles/dfp_keyfile.json',
        execution_timeout=timedelta(hours=4),
    )
    get_thirdparty_segments_file.doc_md = task_doc_md

    task_doc_md = """
    Importing 3rd party segments in BQ import table
    """
    import_thirdparty_segments = GCSToBigQueryOperator(
        task_id='import_thirdparty_segments',
        source_objects=['{}thirdparty_segments.csv'.format(gcs_path)],
        destination_project_dataset_table='{}:import.picasso_thirdparty_segment_{}'.format(bq_project, day),
        schema_fields=ast.literal_eval(Variable.get('schema_dfp_thirdparty_segment')),
        field_delimiter=';',
        quote_character='',
        allow_quoted_newlines=True,
        skip_leading_rows=1
    )
    import_thirdparty_segments.doc_md = task_doc_md

    task_doc_md = """
    Truncate and replace 3rd party segment data
    """
    insert_thirdparty_segments = BigQueryExecuteQueryOperator(
        task_id='insert_thirdparty_segments',
        sql='insert_thirdparty_segments.sql',
        params={
            'bq_project': bq_project
        }
    )
    insert_thirdparty_segments.doc_md = task_doc_md

    task_doc_md = """
    Importing third party segment data in PostgreSQL
    """
    pg_thirdparty_import = BigQueryToPostgresOperator(
        task_id='pg_thirdparty_import',
        source_export_dataset_table='{}:temp.export_thirdparty_segments_{}'.format(bq_project, day),
        destination_table='picasso.thirdparty_segment',
        truncate_destination_table=True,
        bucket=matrix_bucket,
        destination_cloud_storage_object_path='picasso/{{ next_ds_nodash }}/picasso_thirdparty_segment_{{ next_ds_nodash }}.csv',
        source_query='export_thirdparty_segments.sql',
        source_query_destination_table='{}:temp.export_thirdparty_segments_{}'.format(bq_project, day),
        params={
            'bq_project': bq_project
        },
        copy_expert_query="COPY {} (id, ref, name, source, vertical, type, description, size, "
                          "mobile_web_size, idfa_size, adid_size, ppid_size, cost) FROM STDIN "
                          "WITH CSV DELIMITER AS '\t' NULL AS '\\N'".format(
            'picasso.thirdparty_segment'),
        prepare_query=False,
    )
    pg_thirdparty_import.doc_md = task_doc_md

    task_doc_md = """
    Importing segmentation tags data in PostgreSQL
    """
    segmentation_tags_import = BigQueryToPostgresOperator(
        task_id='segmentation_tags_import',
        source_export_dataset_table='{}:temp.export_segmentation_tags_{}'.format(bq_project, day),
        destination_table='picasso.segmentation_tags',
        truncate_destination_table=True,
        bucket=matrix_bucket,
        destination_cloud_storage_object_path='picasso/{{ next_ds_nodash }}/segmentation_tags_{{ next_ds_nodash }}.csv',
        source_query='export_segmentation_tags.sql',
        source_query_destination_table='{}:temp.export_segmentation_tags_{}'.format(bq_project, day),
        params={
            'bq_project': bq_project
        },
        copy_expert_query="COPY {} (segment_name, create_date, update_date, destination, volume) "
                          "FROM STDIN WITH CSV DELIMITER AS '\t' NULL AS '\\N'".format(
            'picasso.segmentation_tags'),
        prepare_query=False,
    )
    segmentation_tags_import.doc_md = task_doc_md

    task_doc_md = """
    Importing contextual segments data in PostgreSQL
    """
    contextual_segments_import = BigQueryToPostgresOperator(
        task_id='contextual_segments_import',
        source_export_dataset_table='{}:temp.export_contextual_segments_{}'.format(bq_project, day),
        destination_table='picasso.contextual_segment',
        truncate_destination_table=True,
        bucket=matrix_bucket,
        destination_cloud_storage_object_path='picasso/{{ next_ds_nodash }}/contextual_segments_{{ next_ds_nodash }}.csv',
        source_query='export_contextual_segments.sql',
        source_query_destination_table='{}:temp.export_contextual_segments_{}'.format(bq_project, day),
        params={
            'bq_project': bq_project
        },
        copy_expert_query="COPY {} (id, name, category, theme, type, tags, active, nb_urls, nb_visitors_3m, create_date, update_date) "
                          "FROM STDIN WITH CSV DELIMITER AS '\t' NULL AS '\\N'".format(
            'picasso.contextual_segment'),
        prepare_query=False,
    )
    contextual_segments_import.doc_md = task_doc_md

    task_doc_md = """
    Importing affinity tags data in PostgreSQL
    """
    affinity_tags_import = BigQueryToPostgresOperator(
        task_id='affinity_tags_import',
        source_export_dataset_table='{}:temp.export_affinity_tags_{}'.format(bq_project, day),
        destination_table='picasso.affinity_tags',
        truncate_destination_table=True,
        bucket=matrix_bucket,
        destination_cloud_storage_object_path='picasso/{{ next_ds_nodash }}/affinity_tags_{{ next_ds_nodash }}.csv',
        source_query='export_affinity_tags.sql',
        source_query_destination_table='{}:temp.export_affinity_tags_{}'.format(bq_project, day),
        params={
            'bq_project': bq_project
        },
        copy_expert_query="COPY {} (name, destination, volume, create_date, update_date) "
                          "FROM STDIN WITH CSV DELIMITER AS '\t' NULL AS '\\N'".format(
            'picasso.affinity_tags'),
        prepare_query=False,
    )
    affinity_tags_import.doc_md = task_doc_md

    task_doc_md = """
    Merge & update Picasso segmentation_tags_info
    """
    update_segmentation_tags_info = BigQueryExecuteQueryOperator(
        task_id='update_segmentation_tags_info',
        sql='update_segmentation_tags_info.sql',
        params={
            'bq_project': bq_project
        }
    )
    update_segmentation_tags_info.doc_md = task_doc_md

    set_schemas >> [get_segments_file, get_thirdparty_segments_file]
    get_segments_file >> import_segments >> insert_segments >> pg_import >> main_segments >> [pg_display, definition_copy]
    definition_copy >> definition_import >> insert_definition
    get_thirdparty_segments_file >> import_thirdparty_segments >> insert_thirdparty_segments >> pg_thirdparty_import
    update_segmentation_tags_info >> segmentation_tags_import
