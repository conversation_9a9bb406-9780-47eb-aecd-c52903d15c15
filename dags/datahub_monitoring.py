# --------------------------------
# --- Commands for preprod ENV
# gsutil cp dags/datahub_monitoring.py gs://europe-west1-mozart-cluster-2e910a39-bucket/dags/

# --- Transfers sql to gcs (preprod) using:
# gsutil -m cp -R data/sql/datahub_monitoring/*  gs://europe-west1-mozart-cluster-2e910a39-bucket/data/sql/datahub_monitoring/

# --- Transfers schemas to gcs (preprod) using:
# gsutil -m cp -R data/sql/datahub_monitoring/schemas/* gs://it-data-preprod-matrix-preprod-pipeline/datahub_monitoring/schemas/


# --------------------------------

import os
from datetime import timedelta, datetime

from airflow import DAG
from airflow.models import Variable
from airflow.operators.python import PythonOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.google.cloud.transfers.gcs_to_bigquery import GCSToBigQueryOperator
from airflow.utils.email import send_email
from dev_plugin.operators.psql_export_query_to_gcs_operator import PostgresQueryExportToGoogleCloudStorageOperator
from psql_plugin.operators.psql_get_value_operator import PostgresGetValueOperator

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']

dag_name = 'datahub_monitoring'
email_on_failure = True
bq_project = 'pm-prod-matrix'
# ------------- FOR PREPROD ENV -----------------
env = os.environ.get("ENV")
if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    bq_project = 'pm-preprod-matrix'
# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2021, 6, 4, 14, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=50),
    'sla': None,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=1),
    'depends_on_past': False,

    'bucket': matrix_bucket,

    # PSQL Connection
    'conn_id': 'psql_matrix_pmc_app',
    'postgres_conn_id': 'psql_matrix_pmc_app',
    'database': 'matrix',
    'schema': 'matrix__pmc',

    # GCP CONNECTION
    'gcp_conn_id': 'gcs_matrix',
    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_TRUNCATE',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',
}

datahub_monitoring_params = Variable.get("datahub_monitoring_params", deserialize_json=True)
interval_backup = datahub_monitoring_params['interval_backup']

monitoring_datahub_schemas = [{"mode": "REQUIRED", "name": "universe_name", "type": "STRING"},
                              {"mode": "REQUIRED", "name": "universe_type", "type": "STRING"},
                              {"mode": "REQUIRED", "name": "flux_ref", "type": "INTEGER"},
                              {"mode": "REQUIRED", "name": "splio_sequence", "type": "NUMERIC"},
                              {"mode": "REQUIRED", "name": "sent_date", "type": "TIMESTAMP"},
                              {"mode": "NULLABLE", "name": "volume", "type": "INTEGER"}]
with DAG(
        dag_name,
        schedule_interval="35 * * * *",
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=1),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/datahub_monitoring/']
) as dag:
    # get last datahub files
    export_datahub_monitoring_snapshot = PostgresQueryExportToGoogleCloudStorageOperator(
        task_id='export_datahub_monitoring_snapshot',
        sql='01_export_monitoring_datahub.sql',
        params={
            'interval': interval_backup
        },
        filename='datahub_monitoring/datahub_monitoring_{{next_execution_date.strftime("%Y_%m_%d_%H%M")}}.csv',
    )

    # GCS TO BQ
    bq_import_datahub_monitoring = GCSToBigQueryOperator(
        task_id='bq_import_datahub_monitoring',
        source_objects=['datahub_monitoring/datahub_monitoring_{{next_execution_date.strftime("%Y_%m_%d_%H%M")}}.csv'],
        destination_project_dataset_table=bq_project + ':import.datahub_monitoring_{{next_execution_date.strftime("%Y_%m_%d_%H%M")}}',
        source_format='CSV',
        field_delimiter=';',
        schema_fields=monitoring_datahub_schemas,
        skip_leading_rows=1,
        autodetect=False,
        max_bad_records=0,
        bucket=matrix_bucket,
    )

    # prepare imported data before store
    prepare_datahub_monitoring = BigQueryExecuteQueryOperator(
        task_id='prepare_datahub_monitoring',
        sql='02_prepare_monitoring_datahub.sql',
        params={
            'import_table': bq_project + '.import.datahub_monitoring',
        },
        destination_dataset_table=bq_project + ':prepare.monitoring_datahub_monitoring_{{next_execution_date.strftime("%Y_%m_%d_%H%M")}}',
    )

    # store imported data
    store_datahub_monitoring = BigQueryExecuteQueryOperator(
        task_id='store_datahub_monitoring',
        sql='03_store_monitoring_datahub.sql',
        params={
            'prepare_table': bq_project + '.prepare.monitoring_datahub_monitoring',
            'store_table': bq_project + '.store_monitoring.datahub_monitoring',
        },
        # destination_dataset_table= bq_project + ':store_monitoring.datahub_monitoring',
    )

    export_datahub_monitoring_snapshot >> bq_import_datahub_monitoring >> \
        prepare_datahub_monitoring >> store_datahub_monitoring


# ------------------------------------------------------------------------------------
# ------------------------------------------------------------------------------------

# Exemple {
# "alert_to":"<EMAIL>",
# "alert_cc":"<EMAIL>",
# "flux": {
#     "0": " 2 days ",
#     "1": " 2 days "
# }
# }

def check_datahub(**context):
    check_datahub_params = Variable.get("datahub_check_flux_params", deserialize_json=True)

    flux_str = ''
    for flux_ref in check_datahub_params['flux'].keys():
        interval = check_datahub_params['flux'][flux_ref]
        print('flux_ref ' + flux_ref)
        print('interval ' + interval)
        query = '''
            SELECT 
                count(*) 
            FROM matrix__email_splio.datahub_monitoring
            WHERE sent_date > NOW() - INTERVAL  '{interval}' 
            AND flux_ref = {flux_ref}
        '''.format(flux_ref=flux_ref, interval=interval)

        get_flux_refs_detected = PostgresGetValueOperator(
            task_id='get_flux_refs_detected_{flux_ref}'.format(flux_ref=flux_ref),
            sql=query,
            database='matrix',
            params={
                'flux_ref': int(flux_ref),
                'interval': interval
            },
            postgres_conn_id='psql_matrix_email_app',
        )

        res = get_flux_refs_detected.execute(context)
        print('count result: ' + str(res))
        if not res:
            print('no data for flux_ref =  : {} !! '.format(flux_ref))
            flux_str += "<br> - Flux ref : {flux_ref}, Intervalle: {interval} ".format(
                flux_ref=flux_ref,
                interval=interval.replace('days', 'jours')
            )
        else:
            print('found data for flux_ref =  : {}'.format(flux_ref))

    # send alert email in case we found flux which does not respect the interval
    if flux_str != '':
        # prepare email template:
        title = "[Datahub Exports Alerte] - Aucun export détecté pour certains flux datahub !! "

        body = ("Bonjour,"
                "<br> Nous avons détecté qu\'aucun export n'a été réalisé vers Splio pour les datahubs suivant: <br>"
                + str(flux_str) +
                "<br><br><i> Remarque: vérifier la configuration des intervalles de chaque flux depuis le variable "
                " airflow \"datahub_check_flux_params\". <br> Seuls les flux déclarés sont vérifiés. </i>"
                "<br>"
                "<br> Cordialement,"
                "<br> Mozart (env= " + env + ") <br>")

        # Send email
        alert_cc = check_datahub_params['alert_cc']
        alert_to = check_datahub_params['alert_to']
        send_email(to=alert_to, subject=title, html_content=body, cc=alert_cc, mime_charset='utf-8', conn_id='sendgrid_default')


with DAG(
        'datahub_flux_check',
        schedule_interval="35 7 * * *",
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=1),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/datahub_monitoring/']
) as dag_check:
    PythonOperator(
        task_id='check_flux',
        python_callable=check_datahub,
    )
