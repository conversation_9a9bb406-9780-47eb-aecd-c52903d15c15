r"""
**PURPOSE:**

This dag is used to compute kpis based on edito contents (bookmarks and alerts).
Data comes from bookmarks, alerts and persons tables stored into pm-prod-mirror.refined_data dataset.
The main goal of this DAG is to facilitate the access to new fields as kpis from bookmark, alerts and person contents.

**METHODOLOGY:**

First, we will compute kpis at star (person) scale monthly, focusing only on bookmarks with type='**follow:**people'.
To do this,
**we use data from BigQuery:**
**Edito data:**- `pm-prod-mirror.refined_data.bookmark`
- `pm-prod-mirror.refined_data.person`
- `pm-prod-mirror.refined_data.alert`**Splio tracking:**- `pm-prod-matrix.refined_data.splio_report`**Tmail:**- `pm-prod-matrix.refined_data.tmail_alert`

After that, we compute kpis at brand scale monthly, also focusing only on bookmarks with type='**follow:**people'.

**COMMAND TO TEST:** 

    - gsutil -m cp -R dags/business_data__edito.py gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/
    - gsutil -m cp -R data/sql/business_data/edito/*  gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/business_data/edito/

**NB:** This DAG will be affected by refined_data.splio_report/tmail_alert and refined_data.bookmark/person/alert
"""

import os
from datetime import timedelta, datetime
from airflow.providers.common.sql.sensors.sql import SqlSensor
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.models import Variable
from collapsible_doc_dag import CDocDAG

# ----------------- CONFIG ----------------------
# Setup variables
env = os.environ.get("ENV")
bq_project_mirror = 'pm-{}-mirror'.format(env)
bq_project_matrix = 'pm-{}-matrix'.format(env)

# DAG configs
dag_name = 'business_data__edito'
email_on_failure = True
if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False

# ------------- INIT DAG ITEMS -----------------
default_args = {
    'owner': 'airflow',
    'start_date': datetime(2024, 2, 15, 3, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=3),
    'sla': None,
    'email_on_retry': False,
    'retries': 0,
    'retry_delay': timedelta(minutes=1),
    'depends_on_past': False,
    # BQ CONNECTION
    'gcp_conn_id': 'bq_matrix',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',
    'use_legacy_sql': False
}

with CDocDAG(
    dag_name,
    description="Generate kpis to track stars following evolution monthly per brand",
    tags=["business", "edito"],
    doc_md=__doc__,
    schedule_interval="50 8 1 * *",      # monthly at 08h50 UTC
    default_args=default_args,
    max_active_runs=1,
    dagrun_timeout=timedelta(minutes=40),
    catchup=False,
    template_searchpath=['/home/<USER>/gcs/data/sql/business_data/edito/']
) as dag:

    task_doc_md = """
    We need to wait for generated active bookmark by profile.
    """
    wait_for_generated_bookmark_by_profile = SqlSensor(
        task_id='wait_for_generated_bookmark_by_profile',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                SELECT CASE WHEN state = 'success' THEN TRUE ELSE FALSE END
                FROM task_instance
                WHERE dag_id = 'generated_data__edito{str_env}'
                  AND task_id = 'active_bookmark_by_profile'
                  AND DATE(start_date) = CURRENT_DATE
            """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_generated_bookmark_by_profile.doc_mc = task_doc_md

    # generate follow stars kpis by brand
    task_doc_md = """
    compute follow stars kpis by brand
    """
    follow_stars_brand_kpis = BigQueryExecuteQueryOperator(
        task_id='follow_stars_brand_kpis',
        sql='follow_star_kpi_brand_monthly.sql',
        params={
            'bq_project': bq_project_mirror
        }
    )
    follow_stars_brand_kpis.doc_md = task_doc_md

    # generate follow stars kpis by star
    task_doc_md = """
    compute follow stars kpis by star
    """
    follow_stars_kpis = BigQueryExecuteQueryOperator(
        task_id='follow_stars_kpis',
        sql='follow_star_kpi_monthly.sql',
        params={
            'bq_project': {'mirror': bq_project_mirror, 'matrix': bq_project_matrix}
        }
    )
    follow_stars_kpis.doc_md = task_doc_md

    wait_for_generated_bookmark_by_profile >> follow_stars_kpis >> follow_stars_brand_kpis
