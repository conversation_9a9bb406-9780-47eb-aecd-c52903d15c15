r"""
**PURPOSE:**

This DAG is used to retrieve email with cookies from liveramp via mics s3 bucket
"""

# --------------
# gsutil cp dags/partners_emb_import_liveramp.py gs://europe-west1-mozart-cluster-2e910a39-bucket/dags/
# --------------

import os
from datetime import timedelta, datetime

import pendulum
from airflow.models import Variable
from airflow.operators.python import PythonOperator
from airflow.providers.amazon.aws.sensors.s3 import S3KeySensor
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.google.cloud.transfers.gcs_to_bigquery import GCSToBigQueryOperator
from airflow.providers.google.cloud.transfers.s3_to_gcs import S3ToGCSOperator
from airflow.utils.email import send_email
from collapsible_doc_dag import CDocDAG

# ----------------- CONFIG ----------------------
dag_name = 'partners_emb_import_liveramp'
email_on_failure = False
email_on_retry = True

env = os.environ.get("ENV")

# BQ specific config
bq_project = 'pm-{}-matrix'.format(env)
bq_dataset = 'mics'
# lr stand for liveramp (mics partner used to )
# table_import_lr = 'import_emb_lr'
# table_history = 'recette_mics_history'
# mics_sample = 'mics_sample_{{ ds_nodash }}'
# mics_sample_prefix = 'mics_sample'
# mics_dir = 'mediarithmics'
cookies_dir = 'cookies_import'
# sample_source = 'sample_source'
# sample_target = 'sample_target'
# table_sample = 'sample_to_display'

# GCS specific config
buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
gcs_bucket = buckets['matrix']

if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    email_on_retry = False
    bq_project = 'pm-{}-matrix'.format(env)



# -----------------------------------------------------------------------------------------------------------


def notify_mics_email(**kwargs):
    """
    Returns a function intended to be called from a PythonOperator
    and we send a mail depending of the entry :
     - could be a mail fi the sample is not present
     - could be a mail if a desyncrho is spotted

    Parameters :
        -- is_file_present (bool)
        -- check_for_desynchro (bool)
        -- date_today (date with dash)

    Results:
        -- send email

    """
    global dag_name

    is_file_present = kwargs['is_file_present']

    additional_recipients = ["<EMAIL>"]

    recipient = eval(Variable.get("airflow_email_alertes"))
    cc = ", ".join(additional_recipients)

    if is_file_present == False:
        # if we want to notify that the sample file is not here

        title = "Fichier emb cookies manquant"
        body = ("Bonjour, <br> "
                "<br><br> Si vous recevez cet email, cela signifie que le fichier venant de liveramp avec les emails cookifiés"
                "<br>n'est pas dans le bucket aws_s3_mediarithmics.<br>"
                "<br>L'import des emails cookifiés est donc impossible.<br>"
                "<br><br>Cordialement, <br> "
                "L'équipe technique IT-Data"
                )

        send_email(to=recipient, subject=title, html_content=body, cc=cc, mime_charset='utf-8', conn_id='sendgrid_default')
        print('email html + ' + body)

    return True


# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2021, 8, 5, tzinfo=pendulum.timezone("Europe/Paris")),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=50),
    'sla': None,
    'email_on_retry': email_on_retry,
    'retries': 2,
    'retry_delay': timedelta(minutes=3),
    'depends_on_past': False,
    'bigquery_conn_id': 'bq_matrix',
    'gcp_conn_id': 'bq_matrix',
}

with CDocDAG(
        dag_name,
        description='Import email EMB with cookies into the base',
        tags=["partner"],
        doc_md=__doc__,
        schedule_interval='30 11 * * *',  # once a day, at 11:30 CET
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=5),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/partners/cookies_import/']
) as dag:
    ds_nodash = '{{ ds_nodash }}'

    date = 20230114

    task_doc_md = """
    Sensor to check if the file is here or not <br />
    If the file is not here, we notify by email <br />
    """

    check_file_existence = S3KeySensor(
        task_id='check_file_existence',
        bucket_name='1165-prisma-media',
        bucket_key='export_emails_emb/*_{}.csv'.format(date),
        wildcard_match=True,
        aws_conn_id='aws_s3_mediarithmics',
        verify=None,
        retries=0,
        poke_interval=0,
        retry_delay=timedelta(minutes=5),
        timeout=5,
        mode='reschedule'
    )

    check_file_existence.doc_md = task_doc_md

    task_doc_md = """
    If the LR file is not here, we notify with an email<br />
    """
    notify_file_absent = PythonOperator(
        task_id='notify_file_absent',
        python_callable=notify_mics_email,
        trigger_rule='all_failed',
        op_kwargs={'is_file_present': False}
    )
    notify_file_absent.doc_md = task_doc_md

    task_doc_md = """
    Daily retrieve sample from mics S3 to our gcs <br />
    path example : gs://prod/partners/cookies_import/ <br />
    """

    retrieve_file_to_gcs = S3ToGCSOperator(
        task_id='retrieve_file_to_gcs',
        bucket='1165-prisma-media',
        aws_conn_id='aws_s3_mediarithmics',
        prefix='export_emails_emb/Export_Emails_EMB_MR_{}'.format(date),  # it's a prefix path care
        gcp_conn_id='gcs_matrix',
        dest_gcs="gs://{}/{}/{}/".format(gcs_bucket, "partners", "cookies_import"),
        replace=False,
    )

    retrieve_file_to_gcs.doc_md = task_doc_md

    task_doc_md = """
    import the table created to bq<br />
    <br />
    """

    # import to BQ [GCS TO BQ]
    import_emb_cookies_to_bq = GCSToBigQueryOperator(
        task_id='import_emb_cookies_to_bq',
        source_objects=['partners/cookies_import/export_emails_emb/Export_Emails_EMB_MR_20230114.csv'],
        destination_project_dataset_table=bq_project + ':import.import_emb_lr',
        source_format='CSV',
        field_delimiter=';',
        autodetect=True,
        bucket=gcs_bucket
    )

    import_emb_cookies_to_bq.doc_md = task_doc_md

    task_doc_md = """
    This query is used to create a table where we store info about emb cookies from liveramp<br />
    """

    store_table_emb_cookies = BigQueryExecuteQueryOperator(
        task_id='store_table_emb_cookies',
        sql='emb_cookies.sql',
        use_legacy_sql=False,
        params={
            'bq_project': bq_project,
            'table_import': 'import_emb_lr'
        }
    )

    store_table_emb_cookies.doc_md = task_doc_md

    check_file_existence >> retrieve_file_to_gcs >> import_emb_cookies_to_bq >> store_table_emb_cookies
    check_file_existence >> notify_file_absent
