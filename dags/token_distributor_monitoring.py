r"""
**PURPOSE:**
**This dag is used to:**- send alert emails concerning token distributor API

**COMMAND TO TEST:** 

- gsutil cp dags/token_distributor_monitoring.py gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/
"""

import os
from datetime import timed<PERSON><PERSON>, datetime

from airflow.models import Variable
from airflow.operators.python import PythonOperator
from airflow.utils.email import send_email
from collapsible_doc_dag import CDocDAG
from psql_plugin.operators.psql_get_value_operator import PostgresGetValueOperator

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']

dag_name = 'token_distributor_monitoring'
email_on_failure = True
bq_project = 'pm-prod-matrix'
# ------------- FOR PREPROD ENV -----------------
env = os.environ.get("ENV")
if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    bq_project = 'pm-preprod-matrix'
# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2023, 1, 23, 7, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=50),
    'sla': None,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=1),
    'depends_on_past': False,

    'bucket': matrix_bucket,

    # PSQL Connection
    'conn_id': 'psql_matrix_pmc_app',
    'postgres_conn_id': 'psql_matrix_pmc_app',
    'database': 'matrix',
    'schema': 'matrix__pmc',

    # GCP CONNECTION
    'gcp_conn_id': 'gcs_matrix',
    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_TRUNCATE',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',
}

datahub_monitoring_params = Variable.get("datahub_monitoring_params", deserialize_json=True)
interval_backup = datahub_monitoring_params['interval_backup']


# ------------------------------------------------------------------------------------
# ------------------------------------------------------------------------------------

# Exemple
# {
#   "alert_cc": "<EMAIL>",
#   "alert_to": "<EMAIL>",
#   "containers": {
#     "sopost_channel": "esampling.token_distrib_container_sopost_channel"
#   }
# }

def check_token_percentage(**context):
    token_distributor_config = Variable.get("distributor_monitoring_config", deserialize_json=True)

    for name, table in token_distributor_config['containers'].items():
        send_alert_email = False

        query = '''
            SELECT 
                round(100 * count(*) FILTER (WHERE email_sha256 IS NULL) / count(*)) AS available_tokens_percentage
            FROM {table}
        '''.format(table=table)
        available_token_percentage = PostgresGetValueOperator(
            task_id='get_available_token_percentage_for_{table}'.format(table=table),
            sql=query,
            database='matrix',
            params={
                'table': table,
            },
            postgres_conn_id='psql_esampling_app',
        )

        percentage = available_token_percentage.execute(context)
        print('count result: ' + str(percentage))

        if percentage and int(percentage) <= 20:
            send_alert_email = int(percentage) == 20 or int(percentage) == 10 or int(percentage) == 5
            print('do send email: ' + str(send_alert_email))

        # send alert email in case we found flux which does not respect the interval
        if send_alert_email:
            sql = '''
                SELECT 
                    count(*) AS available_tokens
                FROM {table}
                WHERE email_sha256 IS NULL
            '''.format(table=table)
            available_tokens = PostgresGetValueOperator(
                task_id='get_available_tokens_for_{table}'.format(table=table),
                sql=sql,
                database='matrix',
                params={
                    'table': table,
                },
                postgres_conn_id='psql_esampling_app',
            )
            total_tokens = available_tokens.execute(context)

            # prepare email template:
            title = "[Token Distributor Availablity Alerte]! "

            body = ("Bonjour,"
                    "<br> Pour la campagne de e-sampling " + str(name) + ", il ne reste plus que " + str(percentage) + "% soit " + str(total_tokens) + " tokens."
                    "<br>"
                    "<br> Cordialement,"
                    "<br> Mozart (env= " + env + ") <br>")

            # Send email
            alert_cc = token_distributor_config['alert_cc']
            alert_to = token_distributor_config['alert_to']
            send_email(to=alert_to, subject=title, html_content=body, cc=alert_cc, mime_charset='utf-8', conn_id='sendgrid_default')


with CDocDAG(
        dag_name,
        description='Token Distributor Monitoring',
        tags=["monitoring"],
        doc_md=__doc__,
        schedule_interval="1 7 * * *",
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=1),
        catchup=False,
) as dag:
    task_doc_md = """
    Check available token percentage for each provided container 
    """
    check_available_token_percentage = PythonOperator(
        task_id='check_available_token_percentage',
        python_callable=check_token_percentage,
    )
    check_available_token_percentage.doc_md = task_doc_md
