r"""
# Picasso Segmentation Tags Import DAG

## Purpose
This DAG is part of a segmentation tag generation pipeline for <PERSON>, tasked with preparing and importing segmentation tag definitions from a PostgreSQL database to BigQuery. It serves as the initial step, setting the stage for subsequent processing and exportation of segmentation tags to various destination platforms.

## Execution Flow
1. **Start**: Initializes the DAG execution.
2. **Prepare Segment Definition**: Executes a PostgreSQL query to prepare the segmentation tag definitions for export.
3. **Import Segment Definitions**: Transfers the prepared segmentation tag definitions from PostgreSQL to BigQuery.
4. **Trigger Creation DAG**: Optionally triggers a downstream DAG for further processing upon successful import.

## Configuration
- **Environment (ENV)**: Determines the operational environment (`dev` or `prod`). Affects naming conventions and operational parameters.
- **BigQuery and PostgreSQL**: Utilizes Google Cloud's BigQuery for data storage and PostgreSQL for source data, requiring appropriate configurations and permissions for both services.
- **Airflow Variables**: Utilizes Airflow variables (`airflow_email_alertes`, `gcs_bucket_names`) for dynamic email alerting and bucket management.

## Key Features
- **Dynamic DAG Naming**: Supports environment-specific DAG names to facilitate parallel development and production workflows.
- **Schema Mapping**: Employs a schema mapping for the PostgreSQL to BigQuery data transfer, ensuring data integrity and structure are maintained.
- **Downstream Triggering**: Capable of triggering downstream processing DAGs, enabling modular, multi-stage data processing pipelines.

## Usage
- **Schedule**: Configured to run daily. Manual triggers are also supported for ad-hoc execution requirements.
- **Parameters**:
  - `env`: Set via an environment variable to specify the execution environment.
  - `email_alerts`: Airflow variable for dynamic email notification configuration.

## Dependencies
- A configured PostgreSQL connection (`psql_picasso_app`).
- Access to a Google Cloud BigQuery project and dataset.
- SQL templates located in `/home/<USER>/gcs/data/sql/segment_manager/segmentation_tags/` for defining and importing segmentation tags.

## Note
Ensure that all prerequisites are met, including proper configuration of Airflow connections, variables, and access permissions for both PostgreSQL and BigQuery services, to avoid execution failures.
"""

import os
from datetime import datetime, timedelta
from airflow.providers.google.cloud.hooks.bigquery import BigQueryHook
from airflow.models import Variable
from collapsible_doc_dag import CDocDAG
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from bq_plugin.operators.psql_to_bigquery_operator import PostgresToBigQueryOperator
from airflow.providers.google.cloud.operators.bigquery import (
    BigQueryExecuteQueryOperator,
)
from airflow.operators.trigger_dagrun import TriggerDagRunOperator

# Constants
ENV = os.environ.get("ENV", "dev")
IS_PROD = ENV == "prod"

# BigQuery configuration
BQ_PROJECT = f"pm-{ENV}-matrix"
STORE_SEGMENT_DATASET = "store_email_segment"

# PostgreSQL configuration
POSTGRES_CONN_ID = "psql_matrix_email_app"

# Airflow configuration
EMAIL_ALERTS = Variable.get("airflow_email_alertes", default_var="[]")
TEMPLATE_PATH = "/home/<USER>/gcs/data/sql/segment_manager/segmentation_tags/"
MATRIX_BUCKET = Variable.get("gcs_bucket_names", deserialize_json=True)["matrix"]
SNAPSHOT_FOLDER = "picasso/backup_matrix/"
CURRENT_DATEHOUR = '{{next_execution_date.strftime("%Y-%m-%d-%H")}}'

default_args = {
    "owner": "airflow",
    "start_date": datetime(2024, 3, 1),
    "email": eval(EMAIL_ALERTS),
    "email_on_failure": IS_PROD,
    "retries": 2,
    "execution_timeout": timedelta(minutes=40),
    "retry_delay": timedelta(minutes=20),
}

## hooks
bq_hook = BigQueryHook(gcp_conn_id="bq_matrix", use_legacy_sql=False)

with CDocDAG(
    (
        f"picasso_segmentation_tags_1_import_{ENV}"
        if not IS_PROD
        else "picasso_segmentation_tags_1_import"
    ),
    tags=["segment_manager"],
    default_args=default_args,
    doc_md=__doc__,
    description="Generate segments from segment definitions defined in postgres picasso.segment_definitions.",
    schedule_interval="20 0 * * *",
    catchup=False,
    template_searchpath=[TEMPLATE_PATH],
) as dag:

    create_destination_tables = BigQueryExecuteQueryOperator(
        task_id="create_destination_tables",
        sql="00-bq_prepare_destination_tables.sql",
        gcp_conn_id="bq_matrix",
        use_legacy_sql=False,
        location="EU",
        params={"bq_project": BQ_PROJECT},
    )

    task_doc_md = """
    Create a postgres export table for Segmentation Tags definitions
    """
    prepare_segment_definition = SQLExecuteQueryOperator(
        task_id="prepare_segment_definition",
        sql="01-pg_export_segmentation_tag_definition_table.sql",
        conn_id=POSTGRES_CONN_ID,
    )
    prepare_segment_definition.doc_md = task_doc_md

    schema_fields = [
        {"name": "id", "type": "INTEGER", "mode": "REQUIRED"},
        {"name": "sql_definition", "type": "STRING", "mode": "REQUIRED"},
        {"name": "type", "type": "STRING", "mode": "REQUIRED"},
        {"name": "universe", "type": "STRING", "mode": "REQUIRED"},
        {"name": "name", "type": "STRING", "mode": "REQUIRED"},
        {"name": "description", "type": "STRING", "mode": "REQUIRED"},
        {"name": "frequency", "type": "STRING", "mode": "NULLABLE"},
        {"name": "status", "type": "STRING", "mode": "REQUIRED"},
        {"name": "created_by", "type": "STRING", "mode": "REQUIRED"},
        {"name": "destination_platforms", "type": "STRING", "mode": "REQUIRED"},
    ]

    task_doc_md = """
    Import Segment Definition from postgres to bigquery
    """
    import_segment_definitions = PostgresToBigQueryOperator(
        task_id="import_segment_definitions",
        database="matrix",
        table="matrix__email_tmp.export_segmentation_tag_definition",
        bucket=MATRIX_BUCKET,
        source_object=SNAPSHOT_FOLDER
        + "segmentation_tag_definitions_"
        + CURRENT_DATEHOUR
        + ".csv",
        destination_project_dataset_table=f"{BQ_PROJECT}.store_picasso.segmentation_tag_definition_to_create",
        schema_fields=schema_fields,
        write_disposition="WRITE_TRUNCATE",
        postgres_conn_id=POSTGRES_CONN_ID,
        max_bad_records=1,
        allow_quoted_newlines=True,
        field_delimiter='\t',
        quote_character=''
    )
    import_segment_definitions.doc_md = task_doc_md

    creation_dag_name = (
        f"picasso_segmentation_tags_2_create_{ENV}"
        if not IS_PROD
        else "picasso_segmentation_tags_2_create"
    )
    trigger_creation_dag = TriggerDagRunOperator(
        task_id=f"trigger_{creation_dag_name}",
        trigger_dag_id=creation_dag_name,
        wait_for_completion=True,
        poke_interval=10,
        dag=dag,
    )

    prepare_segment_definition >> import_segment_definitions >> trigger_creation_dag
    create_destination_tables >> trigger_creation_dag
