r"""
# Personalized NL Send Newsletter using Rogue-One

**PURPOSE:**
Send the newsletter email for each profile/campaign.

**METHODOLOGY:**
- Call Rogue-One API to bulk send emails
- If we are handling the last chunk, then campaign workflow dag will be triggered to update the campaign status.
"""

import os
import logging
from datetime import timedelta
from airflow.models import Variable
from airflow.decorators import task
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
import requests as req
from collapsible_doc_dag import CDoc<PERSON><PERSON>
from datetime import datetime
import json

# ----------------- CONFIG ----------------------
ENV = os.environ.get("ENV")
API_TOKEN = Variable.get('personalized_nl_r1_api_key')
API_HEADERS = {
    'Content-Type': 'application/json',
    'X-Auth-Identity': 'mozart',
    'X-Auth-Token': API_TOKEN
}

dag_name = 'personalized_nl__send_nl'
campaign_workflow_dag_name = 'personalized_nl__campaign_workflow'
email_on_failure = True
config = Variable.get("personalized_nl", deserialize_json=True)
rogue_one_send_nl_url = 'https://rogue-one.prismadata.fr/api/personalized-nl'
if ENV != 'prod':
    dag_name = dag_name + '_' + ENV
    campaign_workflow_dag_name = campaign_workflow_dag_name + '_' + ENV
    email_on_failure = False
    rogue_one_send_nl_url = 'https://rogue-one.preprod.prismadata.fr/api/personalized-nl'


default_args = {
    'owner': 'airflow',
    'start_date': datetime(2024, 5, 1, 2, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'retries': 3,
    'retry_delay': timedelta(minutes=2),
    'execution_timeout': timedelta(minutes=2),
    'email_on_retry': False,
    'depends_on_past': False,
    'wait_for_downstream': False,
}

with CDocDAG(
        dag_name,
        description='Send personalized NL Per profile',
        tags=["nl", "personalized-nl"],
        doc_md=__doc__,
        schedule_interval=None,  # Only manual trigger
        default_args=default_args,
        max_active_runs=40,
        dagrun_timeout=timedelta(minutes=10),
        catchup=False,
) as dag:
    @task
    def send_nl(**kwargs):
        """
        Call Rogue-One Send NL API <br />
        """
        email_campaign_id = kwargs['dag_run'].conf['email_campaign_id']
        start_profile_master_id = kwargs['dag_run'].conf['start_profile_master_id']
        end_profile_master_id = kwargs['dag_run'].conf['end_profile_master_id']
        bulk_id = f'{email_campaign_id}_{start_profile_master_id}_{end_profile_master_id}'
        logging.info(f"\n Calling bulk API for {bulk_id}")
        response = req.request("POST", rogue_one_send_nl_url, headers=API_HEADERS,
                               data=json.dumps({"cid": email_campaign_id,
                                                "start_pmi": start_profile_master_id,
                                                "end_pmi": end_profile_master_id}))
        if response.status_code != 200:
            logging.info(f"\n could not send nl for {bulk_id}")
            logging.info(f"\n Response error code: {response.status_code}")
            raise Exception()

        json_response = json.loads(response.content)
        if json_response["status"] != 200:
            logging.info(f"\n could not send nl for {bulk_id}")
            logging.info(f"\n API error code: {json_response['status']}, message: {json_response['detail']}")
            raise Exception()


    trigger_campaign_workflow_task = TriggerDagRunOperator(
        task_id=f'trigger_{campaign_workflow_dag_name}',
        trigger_dag_id=campaign_workflow_dag_name,
        conf={
            'email_campaign_id': "{{ dag_run.conf['email_campaign_id'] }}",
        },
        wait_for_completion=False
    )

    send_nl() >> trigger_campaign_workflow_task
