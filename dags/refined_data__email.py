r"""
**PURPOSE:**
**This DAG compute daily refined email data at 00:**00 (all tasks), 08h00 and 16h00 (only for Prisma click and open) in incremental way.
Data comes from emails events as pandora, sub/unsub, open, click and sent.
The main goal of this DAG is to enrich our refined base with normalized tables for each email event.
Add stats of new subscriptions for each pandora event.

We also refined profile data to create the profile_business_360.
We refine tmail alert table by keeping useful information. This step is done by the task refine_tmail_email.
We refine also splio report that list tracking stats as opens, clicks, etc...

**METHODOLOGY:**

First, we will compute refined sub/unsub events.
To do this,
**we use data from BigQuery:**
**Email data:**- `pm-prod-matrix.store_matrix_email.profile_master_id`
- `pm-prod-matrix.store_matrix_email.email_event`
- `pm-prod-matrix.store_matrix_email.simulated_sub_unsub_event`
- `pm-prod-matrix.refined_data.email_base`**Pandora events:**- `pm-prod-matrix.store_pandora.event`**PrismaConnect data:**- `store_matrix_pmc.profile_master_id`

Second, we will compute refined pandora events.
To do this,
**we use data from BigQuery:**
**Pandora events:**- `pm-prod-matrix.store_pandora.event`
- `pm-prod-matrix.store_pandora.pmc_event`
- `pm-prod-matrix.store_pandora.partner`
- `pm-prod-matrix.store_pandora.context`**PrismaConnect data:**- `store_matrix_pmc.profile_email`**Email data:**- `pm-prod-matrix.store_matrix_email.profile_master_id`
- `pm-prod-matrix.refined_data.email_event_sub_unsub`
- `pm-prod-matrix.refined_data.email_event_sub_unsub_new`
- `pm-prod-matrix.refined_data.email_base`

In parallel, we refine tracking data for open, click and sent events.
We add some extra information as rogue_one id and universe name for each event depending on its types (open, click or sent).
To do this,
**we use data from BigQuery:**
**Email data:**- `pm-prod-matrix.store_matrix_email.profile_master_id`
- `pm-prod-matrix.store_matrix_email.rogue_one_email_consent`
- `pm-prod-matrix.refined_data.email_base`**To handle with duplicates between splio data and prisma tracking we have applied the strategy described in https:**//docs.google.com/spreadsheets/d/1V-erCGZNp0I-e_Hrzydak9Q8A2aqC5dBoYqKIDZM2GA/edit?usp=sharing .

Tracking data (Prisma and partners):

- `pm-prod-matrix.store_tracking.prisma_full_data`
- `pm-prod-matrix.store_partner.splio_event_open`
- `pm-prod-matrix.store_partner.splio_event_click`
- `pm-prod-matrix.store_tracking.splio_report_data`
- `pm-prod-matrix.store_tracking.splio_report`
- `pm-prod-matrix.store_tracking.riviera_full_data`
- `pm-prod-matrix.store_tracking.webrivage_full_data`

For the profile business 360,
**we create 3 tables:**- `pm-prod-matrix.refined_data.profile_email`
- `pm-prod-matrix.refined_data.profile_pmc`
- `pm-prod-matrix.refined_data.profile_client_abo`

To create `pm-prod-matrix.business_data.profile_business_360`

To refine tmail alert table,
**we based on:**- `pm-prod-matrix.store_tmail.alert`

To refine splio report into one table on refined_data dataset,
**we use these BQ tables:**
**Splio data:**- `pm-prod-matrix.store_tracking.splio_report_data`
- `pm-prod-matrix.store_tracking.splio_report`
- `pm-prod-matrix.refined_data.email_base`**Consent data:**- `pm-prod-matrix.store_karinto.universe`
- `pm-prod-matrix.`refined_data.email_base`
- `pm-prod-matrix.store_matrix_email.rogue_one_email_consent`

We refine also coaching data. 

To create `pm-prod-matrix.refined_data.coaching_data`:**Coaching data:**- `pm-prod-matrix.store_coaching.grossesse_data`**Refined data:**- `pm-prod-matrix.refined_data.profile_digital_360`

We refine also batch userbase, events and campaigns.**Batch data:**- `pm-prod-matrix.store_batch.userbase__`
- `pm-prod-matrix.store_batch.event__`
- `pm-prod-matrix.store_batch.campaign__`

We refine also Qualifio campaigns.**Qualifio data:**- `pm-prod-matrix.store_qualifio.campaign`

**HOW_TO:**
**EXECUTE A full export:**- Go to Admin -> Variables
    - Change the variable email**refined_data:***_full_export to True**EXECUTE AN incremental export:**- Go to Admin -> Variables
    - Change the variable email**refined_data:***_interval to desired one

**COMMAND TO TEST:** 

    gsutil -m cp -R dags/refined_data__email.py**gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/
    gsutil -m cp -R data/sql/refined_data/*  gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/refined_data/
    gsutil -m cp -R data/json/preprod/*  gs://europe-west1-preprod-mozart-2fa49086-bucket/data/json/preprod/

**CAREFUL:**

If there's any problem in `pm-prod-matrix.refined_data.email_event_sub_unsub` or `pm-prod-matrix.refined_data.email_base` or `pm-prod-matrix.refined_data.profile_digital_360` creation, the DAG will be affected.
"""


import os
from airflow.decorators import task
from datetime import timedelta, datetime
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.operators.empty import EmptyOperator
from airflow.utils.task_group import TaskGroup
from airflow.operators.python import BranchPythonOperator
from airflow.utils.trigger_rule import TriggerRule
import logging
from airflow.models import Variable
from airflow.datasets import Dataset
from collapsible_doc_dag import CDocDAG
from airflow.providers.common.sql.sensors.sql import SqlSensor
import json
from env_plugin.EnvironmentVariableManager import EnvironmentVariableManager
import itdata_plugin

# read config from json file
def read_json_config(json_path):
    """
    Reads a JSON config file and return a dict variable
    Args:
        json_path: full path of the JSON file

    Returns:
        configs in dict format
    """
    f = open(json_path)
    json_config = json.load(f)
    f.close()
    return json_config


def check_time():
    """"
        Check if execution time is 00h00.
    """
    current_timestamp = datetime.now()
    logging.info(f"Execution datetime is: {current_timestamp}!")
    if current_timestamp.hour == 00:
        return "execute_all"
    else:
        return "prisma_branch"


# Update Airflow config
@task
def variable_set(key, value):
    """
    This task reads the configs inside a config variable (from a json file)
    and updates Airflow variable
    """
    itdata_plugin.set_variable_in_secret_manager(secret_name=key, secret_value=value, serialize_json=True)


env = os.environ.get("ENV")
refined_data_path = f'/home/<USER>/gcs/data/json/{env}/refined_data__email.json'
dag_name = 'refined_data__email'
email_on_failure = True
bq_project = {'mirror': f'pm-{env}-mirror',
              'matrix': f'pm-{env}-matrix',
              'userhub': f'pm-{env}-userhub'}

# ------------- FOR PREPROD ENV -----------------
if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    "start_date": datetime(2021, 9, 6, 00, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=3),
    'sla': None,
    'email_on_retry': False,
    'retries': 5,
    'retry_delay': timedelta(minutes=2),
    'depends_on_past': False,
    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'gcp_conn_id': 'bq_matrix',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',
    'use_legacy_sql': False,
    'dataset_suffix': EnvironmentVariableManager.get_clone_config(dag_name)
}

# get full_export and interval values
refined_data__email = read_json_config(refined_data_path)

with CDocDAG(
        dag_name,
        description="""
            Normalize and refine raw data from store datasets.

            Data is:
                * pandora events
                * sub/unsub/sent/open/click email events
                * tmail alert
                * coaching profile lifecycle and theirs statistics
                * profiles information email/pmc/abo
                * partners:
                    - splio report campaign
                    - batch event, userbase and campaign
                    - qualifio campaign
        """,
        doc_md=__doc__,
        tags=["refined", "email"],
        schedule_interval="15 0,8,16 * * *",
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(minutes=120),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/refined_data/', '/home/<USER>/gcs/data/sql/']
) as dag:
    # refine email event error
    task_doc_md = """
    Refine errors comes from email event queue
    """
    refine_email_event_error = BigQueryExecuteQueryOperator(
        task_id='refine_email_event_error',
        sql='emails/email_event_error.sql',
        params={
            "bq_project": bq_project.get("matrix")
        }
    )
    refine_email_event_error.doc_md = task_doc_md

    # refine sub_unsub events
    task_doc_md = """
    Normalize sub/unsub email events by executing `refine_data/emails/refine_email_events_sub_unsub.sql` query.<br />
    This task create a partitioned refined table (by day) stored in `pm-prod-matrix.refined_data.email_event_sub_unsub` table.<br />
    """
    refine_sub_unsub_events = BigQueryExecuteQueryOperator(
        task_id='refine_sub_unsub_events',
        sql='emails/email_events_sub_unsub.sql',
        params={
            "is_full": refined_data__email.get("sub_unsub").get("is_full"),
            "bq_project": bq_project.get("matrix"),
            "interval": refined_data__email.get("sub_unsub").get("interval")
        }
    )
    refine_sub_unsub_events.doc_md = task_doc_md

    # new refine sub_unsub events
    # This task was created to generate a new refine_sub_unsub_events, which will generate a new PLC table
    task_doc_md = """
    Normalize sub/unsub email events by executing `refine_data/emails/refine_email_events_sub_unsub.sql` query.<br />
    This task create a partitioned refined table (by day) stored in `pm-prod-matrix.refined_data.email_event_sub_unsub_new` table.<br />
    """
    refine_sub_unsub_events_new = BigQueryExecuteQueryOperator(
        task_id='refine_sub_unsub_events_new',
        sql='emails/email_events_sub_unsub_new.sql',
        params={
            "is_full": refined_data__email.get("sub_unsub_new").get("is_full"),
            "bq_project": bq_project.get("matrix"),
            "interval": refined_data__email.get("sub_unsub_new").get("interval")
        }
    )
    refine_sub_unsub_events_new.doc_md = task_doc_md

    # sensor on store_pandora
    task_doc_md = """
            We need to wait for store_pandora.event to update refined_data.pandora_events
            """
    # @Doc --> https://airflow.readthedocs.io/en/1.9.0/_modules/airflow/operators/sensors.html
    wait_for_store_pandora_event = SqlSensor(
        task_id='wait_for_store_pandora_event',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                SELECT
                    CASE WHEN count(*) > 0 THEN TRUE ELSE FALSE END
                FROM dag_run
                WHERE dag_id = 'matrix__backup_pandora{str_env}'
                    AND state = 'success'
                    AND DATE(start_date) = CURRENT_DATE
            """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_store_pandora_event.doc_mc = task_doc_md

    # refine pandora events
    task_doc_md = """
    Normalize pandora events by executing `refine_data/pandora/refine_pandora_events.sql` query.<br />
    This task create a partitioned refined table(by day) stored in `pm-prod-matrix.refined_data.pandora_events` table.<br />
    """
    refine_pandora_events = BigQueryExecuteQueryOperator(
        task_id='refine_pandora_events',
        sql='pandora/pandora_events.sql',
        params={
            "pandora_full_export": refined_data__email.get("pandora").get("full_export"),
            "pandora_interval": refined_data__email.get("pandora").get("interval"),
            "bq_project": bq_project.get("matrix")
        }
    )
    refine_pandora_events.doc_md = task_doc_md

    task_doc_md = """
        We need to wait for splio report to finish to refine it < /br>.
        """
    # @Doc --> https://airflow.readthedocs.io/en/1.9.0/_modules/airflow/operators/sensors.html
    wait_for_splio_report = SqlSensor(
        task_id='wait_for_splio_report',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                    SELECT CASE WHEN (task_id = 'mep_bq_data' AND state = 'success') THEN TRUE ELSE FALSE END
                    FROM task_instance
                    WHERE dag_id = 'splio_report_campaign'
                      AND task_id = 'mep_bq_data'
                      AND DATE(start_date) = CURRENT_DATE
                """
    )
    wait_for_splio_report.doc_mc = task_doc_md

    task_doc_md = """
        We need to wait for splio score import to finish importing FAI (signalspams) data into the store to refine it < /br>.
        """
    wait_for_score_complaints_fai = SqlSensor(
        task_id='wait_for_score_complaints_fai',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                    SELECT CASE WHEN (task_id = 'signalspams_import_to_store' AND state = 'success') THEN TRUE ELSE FALSE END
                    FROM task_instance
                    WHERE dag_id = 'splio_complaints_import'
                      AND task_id = 'signalspams_import_to_store'
                      AND DATE(start_date) = CURRENT_DATE
                """
    )
    wait_for_score_complaints_fai.doc_mc = task_doc_md

    task_doc_md = """
        We need to wait for splio score import to finish importing mail (scores) data into the store to refine it < /br>.
        """
    wait_for_score_complaints_mail = SqlSensor(
        task_id='wait_for_score_complaints_mail',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                    SELECT CASE WHEN (task_id = 'scores_import_to_store' AND state = 'success') THEN TRUE ELSE FALSE END
                    FROM task_instance
                    WHERE dag_id = 'splio_complaints_import'
                      AND task_id = 'scores_import_to_store'
                      AND DATE(start_date) = CURRENT_DATE
                """
    )
    wait_for_score_complaints_mail.doc_mc = task_doc_md

    # refine splio report link
    task_doc_md = """
    refine splio report link.
    """
    refine_splio_report_link = BigQueryExecuteQueryOperator(
        task_id='refine_splio_report_link',
        sql='emails/splio_report_link.sql',
        params={
            "bq_project": bq_project.get("matrix")
        }
    )
    refine_splio_report_link.doc_md = task_doc_md

    # start refine open
    start_refine_open = EmptyOperator(task_id="start_refine_open")
    task_doc_md = """start refining open events."""
    start_refine_open.doc_md = task_doc_md

    task_doc_md = """Execute all"""
    execute_all = EmptyOperator(task_id="execute_all")
    execute_all.doc_md = task_doc_md

    task_doc_md = """Execute Prisma Open and Click only"""
    prisma_branch = EmptyOperator(task_id="prisma_branch")
    prisma_branch.doc_md = task_doc_md

    task_doc_md = """Define the branch to be executed based on the execution time"""
    check_branch = BranchPythonOperator(
        task_id="check_branch",
        python_callable=check_time,
    )
    check_branch.doc_md = task_doc_md

    with TaskGroup("refine_open", tooltip="Tasks for refine open") as refine_open:
        # refine prisma open events
        task_doc_md = """
        Refine prisma open events. In this task, we do: <br />
            - Remove duplicates from store. <br />
            - Match open to Splio universe. <br />
            - Match open to email consent public ref / email theme. <br />
            - Add open hardware / software information's extracted from 'user-agent'. <br />
        """
        refine_prisma_open = BigQueryExecuteQueryOperator(
            task_id='refine_prisma_open',
            sql='emails/prisma_open.sql',
            params={
                "bq_project": bq_project.get("matrix"),
                "is_full": refined_data__email.get("prisma").get("open").get("full_export"),
                "interval": refined_data__email.get("prisma").get("open").get("interval"),
                "start_date": refined_data__email.get("prisma").get("open").get("start_date"),
                "end_date": refined_data__email.get("prisma").get("open").get("end_date")
            },
            trigger_rule=TriggerRule.NONE_FAILED,
            outlets=[Dataset("bigquery://matrix/refined_data/prisma_open/")]
        )
        refine_prisma_open.doc_md = task_doc_md

        # refine splio open events
        task_doc_md = """
        Refine splio open events. In this task, we do: <br />
            - Remove duplicates from store. <br />
            - Match open to Splio universe. <br />
            - Match open to email consent public ref / email theme. <br />
            - Add open hardware / software information's extracted from 'user-agent'. <br />
        """
        refine_splio_open = BigQueryExecuteQueryOperator(
            task_id='refine_splio_open',
            sql='emails/splio_open.sql',
            params={
                "bq_project": bq_project.get("matrix"),
                "is_full": refined_data__email.get("splio").get("open").get("full_export"),
                "interval": refined_data__email.get("splio").get("open").get("interval"),
                "start_date": refined_data__email.get("splio").get("open").get("start_date"),
                "end_date": refined_data__email.get("splio").get("open").get("end_date")
            }
        )
        refine_splio_open.doc_md = task_doc_md

        # refine riviera open events
        task_doc_md = """
        Refine riviera open events. In this task, we do: <br />
            - Remove duplicates from store. <br />
            - Match open to email consent public ref / email theme. <br />
        """
        refine_riviera_open = BigQueryExecuteQueryOperator(
            task_id='refine_riviera_open',
            sql='emails/riviera_open.sql',
            params={
                "bq_project": bq_project.get("matrix"),
                "is_full": refined_data__email.get("riviera").get("open").get("full_export"),
                "interval": refined_data__email.get("riviera").get("open").get("interval"),
                "start_date": refined_data__email.get("riviera").get("open").get("start_date"),
                "end_date": refined_data__email.get("riviera").get("open").get("end_date")
            }
        )
        refine_riviera_open.doc_md = task_doc_md

        # refine webrivage open events
        task_doc_md = """
        Refine webrivage open events. In this task, we do: <br />
            - Remove duplicates from store. <br />
            - Match open to email consent public ref / email theme. <br />
        """
        refine_webrivage_open = BigQueryExecuteQueryOperator(
            task_id='refine_webrivage_open',
            sql='emails/webrivage_open.sql',
            params={
                "bq_project": bq_project.get("matrix"),
                "is_full": refined_data__email.get("webrivage").get("open").get("full_export"),
                "start_date": refined_data__email.get("webrivage").get("open").get("start_date"),
                "end_date": refined_data__email.get("webrivage").get("open").get("end_date"),
                "interval": refined_data__email.get("webrivage").get("open").get("interval")
            }
        )
        refine_webrivage_open.doc_md = task_doc_md

        # refine partners open events
        task_doc_md = """
                Refine partners open events. In this task, we do: <br />
                    - Remove duplicates from store. <br />
                    - Match open to email consent public ref / email theme. <br />
                """
        refine_partners_open = BigQueryExecuteQueryOperator(
            task_id='refine_partners_open',
            sql='emails/partners_open.sql',
            params={
                "bq_project": bq_project.get("matrix"),
                "is_full": refined_data__email.get("partners").get("open").get("full_export"),
                "interval": refined_data__email.get("partners").get("open").get("interval"),
                "start_date": refined_data__email.get("partners").get("open").get("start_date"),
                "end_date": refined_data__email.get("partners").get("open").get("end_date")
            }
        )
        refine_partners_open.doc_md = task_doc_md

        # refine open events
        task_doc_md = """
        Merge all open events tracking(Prisma and partners) into one table by executing `refine_data/emails/email_event_open.sql` query.<br />
        This task create a partitioned refined table(by day) stored in `pm-prod-matrix.refined_data.email_event_open` table.<br />
        """
        merge_refined_open = BigQueryExecuteQueryOperator(
            task_id='merge_refined_open',
            sql='emails/email_event_open.sql',
            params={
                "bq_project": bq_project.get("matrix"),
                "is_full": refined_data__email.get("global").get("open").get("full_export"),
                "interval": refined_data__email.get("global").get("open").get("interval"),
                "start_date": refined_data__email.get("global").get("open").get("start_date"),
                "end_date": refined_data__email.get("global").get("open").get("end_date")
            }
        )
        merge_refined_open.doc_md = task_doc_md

        [refine_prisma_open, refine_splio_open, refine_riviera_open,
         refine_webrivage_open, refine_partners_open] >> merge_refined_open

    # finish refine open
    end_refine_open = EmptyOperator(task_id="end_refine_open")
    task_doc_md = """finish refining open events."""
    end_refine_open.doc_md = task_doc_md

    # start refine click
    start_refine_click = EmptyOperator(task_id="start_refine_click")
    task_doc_md = """start refining click events."""
    start_refine_click.doc_md = task_doc_md

    with TaskGroup("refine_click", tooltip="Tasks for refine click") as refine_click:
        # refine prisma click events
        task_doc_md = """
        Refine prisma click events. In this task, we do: <br />
            - Remove duplicates from store. <br />
            - Match click to Splio universe. <br />
            - Match click to email consent public ref / email theme. <br />
            - Add click hardware / software information's extracted from 'user-agent'. <br />
        """
        refine_prisma_click = BigQueryExecuteQueryOperator(
            task_id='refine_prisma_click',
            sql='emails/prisma_click.sql',
            params={
                "bq_project": bq_project,
                "is_full": refined_data__email.get("prisma").get("click").get("full_export"),
                "interval": refined_data__email.get("prisma").get("click").get("interval"),
                "start_date": refined_data__email.get("prisma").get("click").get("start_date"),
                "end_date": refined_data__email.get("prisma").get("click").get("end_date")
            },
            trigger_rule=TriggerRule.NONE_FAILED,
            outlets=[Dataset("bigquery://matrix/refined_data/prisma_click/")]
        )
        refine_prisma_click.doc_md = task_doc_md

        # refine splio click events
        task_doc_md = """
        Refine splio click events. In this task, we do: <br />
            - Remove duplicates from store. <br />
            - Match click to Splio universe. <br />
            - Match click to email consent public ref / email theme. <br />
            - Add click hardware / software information's extracted from 'user-agent'. <br />
        """
        refine_splio_click = BigQueryExecuteQueryOperator(
            task_id='refine_splio_click',
            sql='emails/splio_click.sql',
            params={
                "bq_project": bq_project,
                "is_full": refined_data__email.get("splio").get("click").get("full_export"),
                "interval": refined_data__email.get("splio").get("click").get("interval"),
                "start_date": refined_data__email.get("splio").get("click").get("start_date"),
                "end_date": refined_data__email.get("splio").get("click").get("end_date")
            }
        )
        refine_splio_click.doc_md = task_doc_md

        # refine riviera click events
        task_doc_md = """
        Refine riviera click events. In this task, we do: <br />
            - Remove duplicates from store. <br />
            - Match click to email consent public ref / email theme. <br />
        """
        refine_riviera_click = BigQueryExecuteQueryOperator(
            task_id='refine_riviera_click',
            sql='emails/riviera_click.sql',
            params={
                "bq_project": bq_project.get("matrix"),
                "is_full": refined_data__email.get("riviera").get("click").get("full_export"),
                "interval": refined_data__email.get("riviera").get("click").get("interval"),
                "start_date": refined_data__email.get("riviera").get("click").get("start_date"),
                "end_date": refined_data__email.get("riviera").get("click").get("end_date")
            }
        )
        refine_riviera_click.doc_md = task_doc_md

        # refine webrivage click events
        task_doc_md = """
        Refine webrivage click events. In this task, we do: <br />
            - Remove duplicates from store. <br />
            - Match click to email consent public ref / email theme. <br />
        """
        refine_webrivage_click = BigQueryExecuteQueryOperator(
            task_id='refine_webrivage_click',
            sql='emails/webrivage_click.sql',
            params={
                "bq_project": bq_project.get("matrix"),
                "is_full": refined_data__email.get("webrivage").get("click").get("full_export"),
                "start_date": refined_data__email.get("webrivage").get("click").get("start_date"),
                "end_date": refined_data__email.get("webrivage").get("click").get("end_date"),
                "interval": refined_data__email.get("webrivage").get("click").get("interval")
            }
        )
        refine_webrivage_click.doc_md = task_doc_md

        # refine partners click events
        task_doc_md = """
                Refine partners click events. In this task, we do: <br />
                    - Remove duplicates from store. <br />
                    - Match click to email consent public ref / email theme. <br />
                """
        refine_partners_click = BigQueryExecuteQueryOperator(
            task_id='refine_partners_click',
            sql='emails/partners_click.sql',
            params={
                "bq_project": bq_project.get("matrix"),
                "is_full": refined_data__email.get("partners").get("click").get("full_export"),
                "start_date": refined_data__email.get("partners").get("click").get("start_date"),
                "end_date": refined_data__email.get("partners").get("click").get("end_date"),
                "interval": refined_data__email.get("partners").get("click").get("interval")
            }
        )
        refine_partners_click.doc_md = task_doc_md

        # refine click events
        task_doc_md = """
        Merge all click events tracking (Prisma and partners) into one table by executing `refine_data/emails/email_event_click.sql` query.<br />
        This task create a partitioned refined table(by day) stored in `pm-prod-matrix.refined_data.email_event_click` table.<br />
        """
        merge_refined_click = BigQueryExecuteQueryOperator(
            task_id='merge_refined_click',
            sql='emails/email_event_click.sql',
            params={
                "bq_project": bq_project,
                "is_full": refined_data__email.get("global").get("click").get("full_export"),
                "interval": refined_data__email.get("global").get("click").get("interval"),
                "start_date": refined_data__email.get("global").get("click").get("start_date"),
                "end_date": refined_data__email.get("global").get("click").get("end_date")
            }
        )
        merge_refined_click.doc_md = task_doc_md

        [refine_prisma_click, refine_splio_click, refine_riviera_click, refine_partners_click,
         refine_webrivage_click] >> merge_refined_click

    # finish refine click
    end_refine_click = EmptyOperator(task_id="end_refine_click")
    task_doc_md = """finish refining click events."""
    end_refine_click.doc_md = task_doc_md

    # Generated device information with woothee
    task_doc_md = """
    Categorize device with user agent according to woothee lib and complete a table by executing refine_data/emails/user_agent.sql` query.<br />
    This task create a generated table stored in `pm-prod-matrix.generated_data.user_agent_woothee` table.<br />
    """
    generate_user_agent_woothee = BigQueryExecuteQueryOperator(
        task_id='generate_user_agent_woothee',
        sql='generated_data/email/user_agent__woothee.sql',
        params={
            "gspath_to_parser_lib": "gs://{gcs_bucket}/data/packages/woothee-js/release/woothee.js".format(
                gcs_bucket=os.environ["GCS_BUCKET"]),
            "bq_dataset_generated": "generated_data",
            "generated_table": "user_agent_woothee",
            "bq_dataset_store_tracking": "store_tracking",
            "prisma_full_data": "prisma_full_data",
            "bq_project": bq_project.get("matrix")
        }
    )
    generate_user_agent_woothee.doc_md = task_doc_md

    # Generated device information
    task_doc_md = """
    Categorize device with user agent according to UA-Parser lib and complete a table by executing refine_data/emails/user_agent.sql` query.<br />
    This task create a refined table stored in `pm-prod-matrix.refined_data.user_agent_ua-parser` table.<br />
    """
    generate_user_agent_ua_parser = BigQueryExecuteQueryOperator(
        task_id='generate_user_agent_ua_parser',
        sql='generated_data/email/user_agent__ua_parser.sql',
        params={
            "gspath_to_parser_lib": "gs://{gcs_bucket}/data/packages/ua-parser-js-master/src/ua-parser.js".format(
                gcs_bucket=os.environ.get("GCS_BUCKET")),
            "bq_dataset_generated": "generated_data",
            "generated_table": "user_agent_ua-parser",
            "bq_dataset_store_tracking": "store_tracking",
            "prisma_full_data": "prisma_full_data",
            "bq_project": bq_project.get("matrix")
        }
    )
    generate_user_agent_ua_parser.doc_md = task_doc_md

    # Refined device information
    task_doc_md = """
    Combine device with user agent and complete a table by executing refine_data/emails/ser_agent.sql` query.<br />
    This task create a generated table stored in `pm-prod-matrix.generated_data.user_agent` table.<br />
    """
    refine_user_agent = BigQueryExecuteQueryOperator(
        task_id='refine_user_agent',
        sql='emails/user_agent.sql',
        params={
            "bq_dataset_generated": "generated_data",
            'bq_dataset_refined': "refined_data",
            "refined_table": "user_agent",
            "user_agent_ua-parser": "user_agent_ua-parser",
            "user_agent_woothee": "user_agent_woothee",
            "bq_project": bq_project.get("matrix")
        }
    )
    refine_user_agent.doc_md = task_doc_md

    # refine sent events
    task_doc_md = """
    Refine sent events tracking from splio into one table by executing `refine_data/emails/email_events_sent.sql` query.<br />
    This task create a partitioned refined table (by day) stored in `pm-prod-matrix.refined_data.email_event_sent` table.<br />
    """
    refine_sent_events = BigQueryExecuteQueryOperator(
        task_id='refine_sent_events',
        sql='emails/email_events_sent.sql',
        params={
            "sent_full_export": refined_data__email.get("sent").get("full_export"),
            "sent_interval": refined_data__email.get("sent").get("interval"),
            "bq_project": bq_project.get("matrix")
        }
    )
    refine_sent_events.doc_md = task_doc_md

    # change the view profil_digital_360_enriched into a table
    task_doc_md = """
    Create a table inside store to put the table from profil_digital_360_enriched view inside.
    """
    enriched_vue_to_table = BigQueryExecuteQueryOperator(
        task_id='enriched_vue_to_table',
        sql='profile/profile_enriched.sql',
        params={
            "bq_project": bq_project.get("matrix"),
            "bq_dataset_store": 'store',
            "table_enriched": 'profile_digital_360_enriched'
        }
    )
    enriched_vue_to_table.doc_md = task_doc_md

    # improve country code when misspelled by user
    task_doc_md = """
    Compute a matching table from refine_email (yesterday) to <br />
    match data for the new profile_email
    """
    improve_country_code = BigQueryExecuteQueryOperator(
        task_id='improve_country_code',
        sql='profile/improve_country_code.sql',
    )
    improve_country_code.doc_md = task_doc_md

    # refine profile email
    task_doc_md = """
    Compute the refine profile email from store_matrix_email into refined_data.profile_email
    """
    refine_profile_email = BigQueryExecuteQueryOperator(
        task_id='refine_profile_email',
        sql='profile/profile_email.sql',
        params={
            "bq_project": bq_project.get("matrix"),
            "bq_dataset": 'refined_data',
            "table": 'profile_email'
        },
        outlets=[Dataset("bigquery://matrix/refined_data/profile_email/")]
    )
    refine_profile_email.doc_md = task_doc_md

    # refine profile email brand
    task_doc_md = """
    Compute the refine profile email by brand data from store_matrix_email into refined_data.profile_email_brand
    """
    refine_profile_email_brand = BigQueryExecuteQueryOperator(
        task_id='refine_pprofile_email_brand',
        sql='profile/profile_email_brand.sql',
        params={
            "bq_project": bq_project.get("matrix"),
            "bq_dataset": 'refined_data',
            "table": 'profile_email_brand'
        }
    )
    refine_profile_email.doc_md = task_doc_md

    # refine profile client_abo
    task_doc_md = """
    Compute the refine profile mag from export_it_data into refined_data.profile_client_abo
    """
    refine_profile_client_abo = BigQueryExecuteQueryOperator(
        task_id='refine_profile_client_abo',
        sql='profile/profile_client_abo.sql',
        params={
            "bq_project": bq_project.get("matrix"),
            "bq_dataset": 'refined_data',
            "table": 'profile_client_abo'
        },
        outlets=[Dataset("bigquery://matrix/refined_data/profile_client_abo/")]
    )
    refine_profile_client_abo.doc_md = task_doc_md

    # refine profile client_abo
    task_doc_md = """
    Compute the refine profile mag from export_it_data into refined_data.profile_client_abo
    """
    refine_profile_client_abo_with_postal_base = BigQueryExecuteQueryOperator(
        task_id='refine_profile_client_abo_with_postal_base',
        sql='profile/profile_client_abo_with_postal_base.sql',
        params={
            "bq_project": bq_project.get("matrix"),
            "bq_dataset": 'refined_data',
            "table": 'profile_client_abo_with_postal_base'
        }
    )
    refine_profile_client_abo_with_postal_base.doc_md = task_doc_md

    # refine profile PMC
    task_doc_md = """
    Compute the refine profile pmc from store_matrix_pmc into refined_data.profile_pmc
    """
    refine_profile_pmc = BigQueryExecuteQueryOperator(
        task_id='refine_profile_pmc',
        sql='profile/profile_pmc.sql',
        params={
            "bq_userhub_project": bq_project.get("userhub")
        },
        outlets=[Dataset("bigquery://matrix/refined_data/profile_pmc/")]
    )
    refine_profile_pmc.doc_md = task_doc_md

    ################################################ SOCIO DEMO ################################################

    # refine profile email socio demo
    task_doc_md = """
    Compute the refined profile email socio demo data into refined_data.profile_email_socio_demo
    """
    refine_profile_email_socio_demo = BigQueryExecuteQueryOperator(
        task_id='refine_profile_email_socio_demo',
        sql='profile/socio_demo/profile_email_socio_demo.sql',
        params={
            "bq_project": bq_project.get("matrix")
        }
    )
    refine_profile_email_socio_demo.doc_md = task_doc_md


    # refine profile pmc socio demo
    task_doc_md = """
    Compute the refined profile pmc socio demo data into refined_data.profile_pmc_socio_demo
    """
    refine_profile_pmc_socio_demo = BigQueryExecuteQueryOperator(
        task_id='refine_profile_pmc_socio_demo',
        sql='profile/socio_demo/profile_pmc_socio_demo.sql',
        params={
            "bq_project": bq_project.get("matrix")
        }
    )
    refine_profile_pmc_socio_demo.doc_md = task_doc_md


    # refine profile client abo socio demo
    task_doc_md = """
    Compute the refined profile client abo socio demo data into refined_data.profile_client_abo_socio_demo
    """
    refine_profile_client_abo_socio_demo = BigQueryExecuteQueryOperator(
        task_id='refine_profile_client_abo_socio_demo',
        sql='profile/socio_demo/profile_client_abo_socio_demo.sql',
        params={
            "bq_project": bq_project.get("matrix")
        }
    )
    refine_profile_client_abo_socio_demo.doc_md = task_doc_md


    # refine profile IA socio demo
    task_doc_md = """
    Compute the refined profile IA socio demo data into refined_data.profile_client_abo_socio_demo
    """
    refine_profile_ia_socio_demo = BigQueryExecuteQueryOperator(
        task_id='refine_profile_ia_socio_demo',
        sql='profile/socio_demo/profile_ia_socio_demo.sql',
        params={
            "bq_project": bq_project.get("matrix")
        }
    )
    refine_profile_ia_socio_demo.doc_md = task_doc_md


    # refine profile batch socio demo
    task_doc_md = """
    Compute the refined profile batch socio demo data into refined_data.profile_batch_socio_demo
    """
    refine_profile_batch_socio_demo = BigQueryExecuteQueryOperator(
        task_id='refine_profile_batch_socio_demo',
        sql='profile/socio_demo/profile_batch_socio_demo.sql',
        params={
            "bq_project": bq_project.get("matrix")
        }
    )
    refine_profile_batch_socio_demo.doc_md = task_doc_md


    # refine profile EMB socio demo
    task_doc_md = """
    Compute the refined profile EMB socio demo data into refined_data.profile_emb_socio_demo
    """
    refine_profile_emb_socio_demo = BigQueryExecuteQueryOperator(
        task_id='refine_profile_emb_socio_demo',
        sql='profile/socio_demo/profile_emb_socio_demo.sql',
        params={
            "bq_project": bq_project.get("matrix")
        },
        outlets=[Dataset("bigquery://matrix/refined_data/profile_emb_socio_demo/")]
    )
    refine_profile_emb_socio_demo.doc_md = task_doc_md

    # refine profile 360
    task_doc_md = """
    Based on the 3 previous task, it computes the profile 360 <br />
    into refined_data.profile_digital_360 and business_data.profile_digital_360.<br />
    It merges all information from refined_data email, client_abo, pmc.<br />
    """
    refine_profile_360 = BigQueryExecuteQueryOperator(
        task_id='refine_profile_360',
        sql='profile/profile_360.sql',
        params={
            "bq_userhub_project": bq_project.get("userhub")
        }
    )
    refine_profile_360.doc_md = task_doc_md

    # overview profile 360
    task_doc_md = """
    Based on refined digital profile 360, it computes some KPIs as fill ratio of each column, volume of email, pmc and magazine univers, etc..<br />
    """
    overview_profile_360 = BigQueryExecuteQueryOperator(
        task_id='overview_profile_360',
        sql='profile/overview_profile_360.sql',
        params={
            "bq_project": bq_project.get("matrix"),
            "bq_dataset": 'datastudio',
            "destination_table": 'overview_digital_profile_360'
        }
    )
    overview_profile_360.doc_md = task_doc_md

    # refine tmail alert
    task_doc_md = """
    refine alert table in store_tmail dataset
    """
    refine_tmail_alert = BigQueryExecuteQueryOperator(
        task_id='refine_tmail_alert',
        sql='emails/tmail_alert.sql',
        params={
            "full_export": refined_data__email.get("alert").get("full_export"),
            "interval": refined_data__email.get("alert").get("interval"),
            "bq_project": bq_project.get("matrix")
        }
    )
    refine_tmail_alert.doc_md = task_doc_md

    # refine tmail email
    task_doc_md = """
    refine email table in store_tmail dataset
    """
    refine_tmail_email = BigQueryExecuteQueryOperator(
        task_id='refine_tmail_email',
        sql='emails/tmail_email.sql',
        params={
            "full_export": refined_data__email.get("email").get("full_export"),
            "interval": refined_data__email.get("email").get("interval"),
            "bq_project": bq_project.get("matrix")
        }
    )
    refine_tmail_email.doc_md = task_doc_md

    # refine splio report
    task_doc_md = """
    refine splio report tables by normalizing it.
    """
    refine_splio_report = BigQueryExecuteQueryOperator(
        task_id='refine_splio_report',
        sql='emails/splio_report.sql',
        params={
            "bq_project": bq_project.get("matrix")
        }
    )
    refine_splio_report.doc_md = task_doc_md

    # get coaching splio stats
    task_doc_md = """
    get coaching splio stats.
    """
    coaching_campaign_splio_stats = BigQueryExecuteQueryOperator(
        task_id='coaching_campaign_splio_stats',
        sql='coaching/coaching_campaign_splio_stats.sql',
        params={
            "bq_project": bq_project.get("matrix"),
            "splio_full_export": refined_data__email.get("splio").get("campaign_coaching").get("full_export"),
            "splio_interval": refined_data__email.get("splio").get("campaign_coaching").get("interval")
        }
    )
    coaching_campaign_splio_stats.doc_md = task_doc_md

    # refine coaching information
    task_doc_md = """
    refine coaching data.
    """
    refine_coaching = BigQueryExecuteQueryOperator(
        task_id='refine_coaching',
        sql='coaching/coaching_data.sql',
        params={
            "bq_project": bq_project.get("matrix"),
            "bq_userhub": 'pm-{}-userhub'.format(env),
            "coaching_full_export": refined_data__email.get("coaching").get("full_export"),
            "coaching_interval": refined_data__email.get("coaching").get("interval")
        }
    )
    refine_coaching.doc_md = task_doc_md

    # coaching profile lifecycle
    task_doc_md = """
    build coaching profile lifecycle by adding state to each step by date, coaching and profile.
    """
    coaching_profile_lifecycle = BigQueryExecuteQueryOperator(
        task_id='coaching_profile_lifecycle',
        sql='coaching/coaching_profile_lifecycle.sql',
        params={
            "bq_project": bq_project.get("matrix"),
            "is_full": refined_data__email.get("coaching_profile_lifecycle").get("is_full"),
            "interval": refined_data__email.get("coaching_profile_lifecycle").get("interval")
        }
    )
    coaching_profile_lifecycle.doc_md = task_doc_md

    # coaching profile lifecycle stats by date, coaching and step
    task_doc_md = """
    compute stats per date, coaching and step based on states.
    """
    coaching_profile_lifecycle_stats = BigQueryExecuteQueryOperator(
        task_id='coaching_profile_lifecycle_stats',
        sql='coaching/coaching_profile_lifecycle_stats.sql',
        params={
            "bq_project": bq_project.get("matrix"),
            "is_full": refined_data__email.get("coaching_profile_lifecycle").get("is_full"),
            "interval": refined_data__email.get("coaching_profile_lifecycle").get("interval")
        }
    )
    coaching_profile_lifecycle_stats.doc_md = task_doc_md

    # coaching profile lifecycle
    task_doc_md = """
    build coaching profile lifecycle for 'Grossesse' coaching by adding state to each step by date, coaching and profile.
    """
    coaching_grossesse_profile_lifecycle = BigQueryExecuteQueryOperator(
        task_id='coaching_grossesse_profile_lifecycle',
        sql='coaching/coaching_grossesse_profile_lifecycle.sql',
        params={
            "bq_project": bq_project.get("matrix"),
            "is_full": refined_data__email.get("coaching_profile_lifecycle").get("is_full"),
            "interval": refined_data__email.get("coaching_profile_lifecycle").get("interval")
        }
    )
    coaching_grossesse_profile_lifecycle.doc_md = task_doc_md

    # coaching profile lifecycle stats by date, coaching and step
    task_doc_md = """
    compute stats per date and step based on states for coaching 'Grossesse'.
    """
    coaching_grossesse_profile_lifecycle_stats = BigQueryExecuteQueryOperator(
        task_id='coaching_grossesse_profile_lifecycle_stats',
        sql='coaching/coaching_grossesse_profile_lifecycle_stats.sql',
        params={
            "bq_project": bq_project.get("matrix"),
            "is_full": refined_data__email.get("coaching_profile_lifecycle").get("is_full"),
            "interval": refined_data__email.get("coaching_profile_lifecycle").get("interval")
        }
    )
    coaching_grossesse_profile_lifecycle_stats.doc_md = task_doc_md

    # refine Batch userbase
    task_doc_md = """
    refine Batch userbase.
    """
    refine_batch_userbase = BigQueryExecuteQueryOperator(
        task_id='refine_batch_userbase',
        sql='batch/userbase.sql',
        params={
            "bq_project": bq_project.get("matrix"),
            "brand_list": refined_data__email.get("batch").get("userbase").get("brand_list"),
            "batch_full_export": refined_data__email.get("batch").get("userbase").get("full_export"),
            "batch_interval": refined_data__email.get("batch").get("userbase").get("interval")
        },
        outlets=[Dataset("bigquery://matrix/refined_data/batch_userbase/")]
    )
    refine_batch_userbase.doc_md = task_doc_md

    # refine Batch userbase optin
    task_doc_md = """
    create refined Batch userbase optin.
    """
    refine_batch_userbase_optin = BigQueryExecuteQueryOperator(
        task_id='refine_batch_userbase_optin',
        sql='batch/userbase_optin.sql',
        params={
            "bq_project": bq_project.get("matrix"),
        }
    )
    refine_batch_userbase_optin.doc_md = task_doc_md

    # refine Batch event
    task_doc_md = """
    refine Batch event.
    """
    refine_batch_event = BigQueryExecuteQueryOperator(
        task_id='refine_batch_event',
        sql='batch/event.sql',
        params={
            "bq_project": bq_project.get("matrix"),
            "brand_list": refined_data__email.get("batch").get("event").get("brand_list"),
            "batch_full_export": refined_data__email.get("batch").get("event").get("full_export"),
            "batch_interval": refined_data__email.get("batch").get("event").get("interval")
        }
    )
    refine_batch_event.doc_md = task_doc_md

    # refine Batch campaign
    task_doc_md = """
    refine Batch campaign.
    """
    refine_batch_campaign = BigQueryExecuteQueryOperator(
        task_id='refine_batch_campaign',
        sql='batch/campaign.sql',
        params={
            "bq_project": bq_project,
            "brand_list": refined_data__email.get("batch").get("campaign").get("brand_list"),
            "batch_full_export": refined_data__email.get("batch").get("campaign").get("full_export"),
            "batch_interval": refined_data__email.get("batch").get("campaign").get("interval")
        }
    )
    refine_batch_campaign.doc_md = task_doc_md

    # Create the prisma_snapshot
    task_doc_md = """
    This tasks is used to create 3 prisma snapshot J (latest), J-1 and J-2 (previous)
    """
    prepare_prisma_snapshot = BigQueryExecuteQueryOperator(
        task_id='prepare_prisma_snapshot',
        sql='snapshot/prisma_snapshot.sql',
        params={
            "bq_project": bq_project.get("matrix")
        },
        outlets=[Dataset("bigquery://matrix/autocorrect/prisma_snapshot_available/")]
    )
    prepare_prisma_snapshot.doc_md = task_doc_md

    # refine Qualifio campaign
    task_doc_md = """
    refine Qualifio campaign.
    """
    refine_qualifio_campaign = BigQueryExecuteQueryOperator(
        task_id='refine_qualifio_campaign',
        sql='qualifio/campaign.sql',
        params={
            "bq_project": bq_project.get("matrix"),
            "is_full": refined_data__email.get("qualifio").get("is_full"),
            "interval": refined_data__email.get("qualifio").get("interval")
        }
    )
    refine_qualifio_campaign.doc_md = task_doc_md

    # refineEverest Validity Rollup data
    task_doc_md = """
    refine Everest Validity rollup data for IP reputation.
    """
    refine_everest_validity_rollup = BigQueryExecuteQueryOperator(
        task_id='refine_everest_validity_rollup',
        sql='emails/everest_validity_rollup.sql',
        params={
            "bq_project": bq_project.get("matrix"),
            "full_export": refined_data__email.get("everest_validity").get("rollup").get("full_export"),
            "interval": refined_data__email.get("everest_validity").get("rollup").get("interval")
        }
    )
    refine_everest_validity_rollup.doc_md = task_doc_md

    # refineEverest Validity Daily data
    task_doc_md = """
    refine Everest Validity daily data for IP reputation.
    """
    refine_everest_validity_daily = BigQueryExecuteQueryOperator(
        task_id='refine_everest_validity_daily',
        sql='emails/everest_validity_daily.sql',
        params={
            "bq_project": bq_project.get("matrix"),
            "full_export": refined_data__email.get("everest_validity").get("daily").get("full_export"),
            "interval": refined_data__email.get("everest_validity").get("daily").get("interval")
        }
    )
    refine_everest_validity_daily.doc_md = task_doc_md

    refine_splio_campaign_stat = BigQueryExecuteQueryOperator(
        task_id='refine_splio_campaign_stat',
        sql='emails/splio_campaign_stat.sql',
        params={
            "bq_project": bq_project.get("matrix"),
            "full_export": refined_data__email.get('splio_history').get("full_export"),
            "interval": refined_data__email.get('splio_history').get("interval")
        }
    )
    refine_splio_campaign_stat.doc_md = task_doc_md

    refine_organic_acquisition_old = BigQueryExecuteQueryOperator(
        task_id='refine_organic_acquisition_old',
        sql='pmc/organic_acquisition_old.sql',
        params={
            "bq_project_matrix": bq_project.get("matrix"),
            "bq_project_userhub": bq_project.get("userhub"),
            "is_full": refined_data__email.get('organic_acquisition').get("is_full"),
            "start_date": refined_data__email.get('organic_acquisition').get("start_date"),
            "end_date": refined_data__email.get('organic_acquisition').get("end_date"),
            "time_interval": refined_data__email.get('organic_acquisition').get("time_interval")
        }
    )
    refine_organic_acquisition_old.doc_md = task_doc_md

    refine_organic_acquisition = BigQueryExecuteQueryOperator(
        task_id='refine_organic_acquisition',
        sql='pmc/organic_acquisition.sql',
        params={
            "bq_project_matrix": bq_project.get("matrix"),
            "bq_project_userhub": bq_project.get("userhub"),
            "is_full": refined_data__email.get('organic_acquisition').get("is_full"),
            "start_date": refined_data__email.get('organic_acquisition').get("start_date"),
            "end_date": refined_data__email.get('organic_acquisition').get("end_date"),
            "time_interval": refined_data__email.get('organic_acquisition').get("time_interval")
        }
    )
    refine_organic_acquisition.doc_md = task_doc_md

    task_doc_md = """
    Build daily FAI complaints stats to refined data upload
    """

    # Daily Email Subject Stats
    splio_fai_complaints_stats_daily = BigQueryExecuteQueryOperator(
        task_id='splio_fai_complaints_stats_daily',
        sql='emails/splio_fai_complaints_stats_daily.sql',
        params={
            "bq_project": bq_project,
            "partial": refined_data__email.get("fai_complaints_stats").get("partial"),
            "start_date": refined_data__email.get("fai_complaints_stats").get("start_date"),
            "end_date": refined_data__email.get("fai_complaints_stats").get("end_date"),
            "time_interval": refined_data__email.get("fai_complaints_stats").get("time_interval")
        }
    )

    splio_fai_complaints_stats_daily.doc_md = task_doc_md

    task_doc_md = """
    Build daily email complaints stats to refined data upload
    """

    # Daily Email Complaints stats
    splio_mail_complaints_stats_daily = BigQueryExecuteQueryOperator(
        task_id='splio_mail_complaints_stats_daily',
        sql='emails/splio_mail_complaints_stats_daily.sql',
        params={
            "bq_project": bq_project,
            "partial": refined_data__email.get("mail_complaints_stats").get("partial"),
            "start_date": refined_data__email.get("mail_complaints_stats").get("start_date"),
            "end_date": refined_data__email.get("mail_complaints_stats").get("end_date"),
            "time_interval": refined_data__email.get("mail_complaints_stats").get("time_interval")
        }
    )

    splio_mail_complaints_stats_daily.doc_md = task_doc_md

    task_doc_md = """
        Refine persona affinity data
        """

    refine_persona_affinity = BigQueryExecuteQueryOperator(
        task_id='refine_persona_affinity',
        sql='profile/persona_segment_affinity.sql',
        params={"bq_project": bq_project.get("matrix")}
    )
    refine_persona_affinity.doc_md = task_doc_md

    task_doc_md = """
        Refine Personalized NL email event sent.
    """

    # Daily Email Subject Stats
    refine_personalized_nl_email_event_sent = BigQueryExecuteQueryOperator(
        task_id='refine_personalized_nl_email_event_sent',
        sql='emails/personalized_nl_email_event_sent.sql',
        params={
            "bq_project": bq_project.get("matrix"),
            "is_full": refined_data__email.get("personalized_nl_email_event_sent").get("is_full"),
            "time_interval": refined_data__email.get("personalized_nl_email_event_sent").get("time_interval")
        }
    )

    refine_personalized_nl_email_event_sent.doc_md = task_doc_md

    # parallel tasks
    check_branch >> [execute_all, prisma_branch]

    prisma_branch >> [refine_prisma_open, refine_prisma_click]

    execute_all >> [
        refine_email_event_error,
        refine_persona_affinity,
        refine_sub_unsub_events_new,  # new branch
        refine_sub_unsub_events,      # old branch for old processes
        wait_for_splio_report,
        refine_sent_events,
        refine_tmail_alert,
        refine_tmail_email,
        improve_country_code,
        refine_splio_campaign_stat,
        refine_coaching,
        refine_batch_userbase,
        refine_batch_event,
        refine_batch_campaign,
        prepare_prisma_snapshot,
        refine_qualifio_campaign,
        generate_user_agent_woothee,
        generate_user_agent_ua_parser,
        refine_profile_email_brand,
        refine_profile_client_abo,
        refine_profile_client_abo_with_postal_base,
        refine_profile_pmc,
        refine_profile_email_socio_demo,
        refine_profile_pmc_socio_demo,
        refine_profile_client_abo_socio_demo,
        refine_profile_ia_socio_demo,
        refine_profile_batch_socio_demo,
        refine_profile_emb_socio_demo,
        enriched_vue_to_table,
        refine_everest_validity_rollup,
        refine_everest_validity_daily,
        wait_for_score_complaints_fai,
        wait_for_score_complaints_mail,
        refine_personalized_nl_email_event_sent
    ]

    refine_sub_unsub_events >> wait_for_store_pandora_event >> refine_pandora_events
    refine_sub_unsub_events >> [coaching_profile_lifecycle, coaching_grossesse_profile_lifecycle]

    coaching_profile_lifecycle >> coaching_profile_lifecycle_stats
    coaching_grossesse_profile_lifecycle >> coaching_grossesse_profile_lifecycle_stats

    wait_for_splio_report >> refine_splio_report_link
    refine_splio_report_link >> start_refine_open >> refine_open >> end_refine_open
    refine_splio_report_link >> start_refine_click >> refine_click >> end_refine_click
    improve_country_code >> refine_profile_email
    [refine_profile_email, refine_profile_email_brand, refine_profile_client_abo, refine_profile_client_abo_with_postal_base, refine_profile_pmc,
     refine_profile_email_socio_demo, refine_profile_pmc_socio_demo, refine_profile_client_abo_socio_demo,
     refine_profile_ia_socio_demo, refine_profile_batch_socio_demo, refine_profile_emb_socio_demo,
     enriched_vue_to_table] >> refine_profile_360 >> overview_profile_360 >> [refine_organic_acquisition, refine_organic_acquisition_old]
    refine_splio_report_link >> refine_splio_report >> [coaching_campaign_splio_stats,
                                                        coaching_profile_lifecycle,
                                                        coaching_grossesse_profile_lifecycle]
    refine_batch_userbase >> refine_batch_userbase_optin
    [generate_user_agent_woothee, generate_user_agent_ua_parser] >> refine_user_agent

    #splio complaints stats
    wait_for_score_complaints_fai >> splio_fai_complaints_stats_daily
    wait_for_score_complaints_mail >> splio_mail_complaints_stats_daily

    # update Airflow variable
    variable_set("refined_data__email", refined_data__email)
