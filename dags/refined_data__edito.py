r"""
**PURPOSE:**
**This DAG compute daily refined edito data at 00:**00 UTC+2 in incremental way.
Data comes from bookmark events stored into {{params.bq_project.mirror}}.store_bookmark dataset.
The main goal of this DAG is to enrich our refined base with normalized tables for edito data.
We refine also article dataset store_one_article.

**METHODOLOGY:**

First, we will compute refined bookmark contents.
To do this,
**we use data from BigQuery:**
**Edito data:**- `pm-prod-mirror.store_bookmark.bookmark_content`
- `pm-prod-mirror.store_bookmark.bookmark`**Email data:**- `{{params.bq_project.matrix}}.store_matrix_email.profile_master_id`**PrismaConnect data:**- `{{params.bq_project.matrix}}.store_matrix_pmc.profile_master_id`

Second, we will compute refined alerts events.
To do this,
**we use data from BigQuery:**
**Alert data:**- `pm-prod-mirror.store_bookmark.alert_resource_log`
- `pm-prod-mirror.store_bookmark.alert_resource_send_log`
- `pm-prod-mirror.store_bookmark.alert_content_log`

We will compute also refined person content from pm-prod-mirror.store_one_person dataset.
To do this,
**we use data from BigQuery:**
**Edito data:**- `pm-prod-mirror.store_one_person.person`
- `pm-prod-mirror.store_one_person.award`
- `pm-prod-mirror.store_one_person.personNomination`
- `pm-prod-mirror.store_one_person.job`
- `pm-prod-mirror.store_one_person.personJob`
- `pm-prod-mirror.store_one_person.degree`
- `pm-prod-mirror.store_one_person.personDegree`
- `pm-prod-mirror.store_one_person.school`
- `pm-prod-mirror.store_one_person.personStudy`
- `pm-prod-mirror.store_one_person.country`
- `pm-prod-mirror.store_one_person.personCountry`
- `pm-prod-mirror.store_one_person.title`
- `pm-prod-mirror.store_one_person.personTitle`
- `pm-prod-mirror.store_one_person.passion`
- `pm-prod-mirror.store_one_person.personPassion`

We compute also refined article content from {{params.bq_project.mirror}}.store_one_article dataset.
To do this,
**we use data from BigQuery:**
**Edito data:**- `pm-prod-mirror.store_one_article.article`
- `pm-prod-mirror.store_one_article.articleUrl`
- `pm-prod-mirror.store_one_article.url`
- `pm-prod-mirror.store_one_article.articleTag`
- `pm-prod-mirror.store_one_article.tag`
- `pm-prod-mirror.store_one_article.category`
- `pm-prod-mirror.store_one_article.articleCategory`
- `pm-prod-mirror.store_one_article.articleChannel`
- `pm-prod-mirror.store_one_article.push`
- `pm-prod-mirror.store_one_article.pushSegment`
- `pm-prod-mirror.store_one_article.segment`

-**Preprod:**gsutil -m cp -R data/sql/refined_data/edito/*  gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/refined_data/edito/
gsutil -m cp -R dags/refined_data__edito.py gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/**Prod:**gsutil -m cp -R data/sql/refined_data/edito/*  gs://europe-west1-prod-mozart-co-2ef7e904-bucket/data/sql/refined_data/edito/
gsutil -m cp -R dags/refined_data__edito.py gs://europe-west1-prod-mozart-co-2ef7e904-bucket/dags/
"""

import os
from datetime import timedelta, datetime
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.models import Variable
from collapsible_doc_dag import CDocDAG

dag_name = 'refined_data__edito'
email_on_failure = True
bq_project = {'mirror': 'pm-prod-mirror', 'matrix': 'pm-prod-matrix'}

#------------- FOR PREPROD ENV -----------------
env = os.environ.get("ENV")
if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    bq_project = {'mirror': 'pm-preprod-mirror', 'matrix': 'pm-preprod-matrix'}
#-----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2021, 9, 14, 00, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=3),
    'sla': None,
    'email_on_retry': False,
    'retries': 0,
    'retry_delay': timedelta(minutes=1),
    'depends_on_past': False,
    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'gcp_conn_id': 'bq_matrix',
    'write_disposition': 'WRITE_APPEND',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',
    'use_legacy_sql': False
}

with CDocDAG(
    dag_name,
    description="Normalize edito data",
    tags=["refined", "edito"],
    doc_md=__doc__,
    schedule_interval="30 8 * * *",
    default_args=default_args,
    max_active_runs=1,
    dagrun_timeout=timedelta(minutes=40),
    catchup=False,
    template_searchpath=['/home/<USER>/gcs/data/sql/refined_data/']
) as dag:
    #refine bookmark contents
    task_doc_md = """
    Normalize bookmark events and contents by executing refine_data/edito/bookmark_contents.sql query.<br />
    This task create a partionned refined table(by day) stored in `pm-prod-mirror.refined_data.bookmark` table.<br />
    """
    refine_bookmark_contents = BigQueryExecuteQueryOperator(
        task_id='refine_bookmark_contents',
        sql='edito/bookmark_contents.sql',
        params={
            'bq_project': bq_project
        }
    )
    refine_bookmark_contents.doc_md = task_doc_md

    #refine alert contents
    task_doc_md = """
    Normalize alert by executing refine_data/edito/alert_contents.sql query.<br />
    This task create a partionned refined table(by day) stored in `pm-prod-mirror.refined_data.alert` table.<br />
    """
    refine_alert_contents = BigQueryExecuteQueryOperator(
        task_id='refine_alert_contents',
        sql='edito/alert_contents.sql',
        params={
            'bq_project': bq_project
        }
    )
    refine_alert_contents.doc_md = task_doc_md

    #refine person contents
    task_doc_md = """
    Normalize person by executing refine_data/edito/person.sql query.<br />
    This task create a partionned refined table(by day) stored in `pm-prod-mirror.refined_data.person` table.<br />
    """
    refine_person_contents = BigQueryExecuteQueryOperator(
        task_id='refine_person_contents',
        sql='edito/person.sql',
        params={
            'bq_project': bq_project
        }
    )
    refine_person_contents.doc_md = task_doc_md

    #refine article contents
    task_doc_md = """
    Refine article content and storing it into `{{ bq_project.mirror }}.refined_data.article`<br />
    """
    refine_article_contents = BigQueryExecuteQueryOperator(
        task_id='refine_article_contents',
        sql='edito/article.sql',
        params={
            'bq_project': bq_project
        }
    )
    refine_article_contents.doc_md = task_doc_md

    # refine program contents
    task_doc_md = """
        Refine program content and storing it into `{{ bq_project.mirror }}.refined_data.program`<br />
        """
    refine_program_contents = BigQueryExecuteQueryOperator(
        task_id='refine_program_contents',
        sql='edito/program.sql',
        params={
            'bq_project': bq_project
        }
    )
    refine_program_contents.doc_md = task_doc_md

    # refine channel contents
    task_doc_md = """
            Refine channel content and storing it into `{{ bq_project.mirror }}.refined_data.channel`<br />
            """
    refine_channel_contents = BigQueryExecuteQueryOperator(
        task_id='refine_channel_contents',
        sql='edito/channel.sql',
        params={
            'bq_project': bq_project
        }
    )
    refine_channel_contents.doc_md = task_doc_md

    # refine prismia contents
    task_doc_md = f"""
            Refine PrimsIA content and store it into `pm-{env}-mirror.refined_data.prismia_stat`<br />}}
            """
    refine_prismia_contents = BigQueryExecuteQueryOperator(
        task_id='refine_prismia_contents',
        sql='edito/prismia_stat.sql',
        params={
            'bq_project': bq_project
        }
    )
    refine_prismia_contents.doc_md = task_doc_md

    refine_bookmark_contents
    refine_alert_contents
    refine_person_contents
    refine_article_contents
    refine_program_contents
    refine_channel_contents
    refine_prismia_contents
