r"""
**PURPOSE:**
This DAG is used to retrieve the indicators via the mics s3 bucket and historize them

**MODES:**
**The DAG can run in two modes:**1.**Normal mode:**Processes files for specified date range
2.**Gap filling mode:**Identifies and fills missing dates in our history

To enable gap filling mode, set the 'gap_filling_mode' parameter to true in the 'mediarithmics_metrics_new_instance'**variable:**{
    "dashboards": true,
    "segments": true,
    "gap_filling_mode": true
}
"""

# --------------
# gsutil cp dags/mediarithmics_kpis_new_instance.py gs://europe-west1-mozart-cluster-2e910a39-bucket/dags/


##gsutil cp dags/mediarithmics_kpis_new_instance.py gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/
##gsutil cp dags/mediarithmics_kpis_new_instance.py gs://europe-west1-prod-mozart-co-2ef7e904-bucket/dags/
# --------------

import logging
import os
import re
from datetime import timedelta, datetime
from airflow.operators.python import BranchPythonOperator
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.models import Variable
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.operators.python import ShortCircuitOperator
from airflow.providers.google.cloud.hooks.bigquery import BigQueryHook
from airflow.providers.google.cloud.transfers.gcs_to_bigquery import GCSToBigQueryOperator
from airflow.providers.google.cloud.transfers.s3_to_gcs import S3ToGCSOperator
from airflow.utils.email import send_email
from collapsible_doc_dag import CDocDAG
from flux_plugin.operators.metrics_alerting import MetricsAlertingOperator

# ----------------- CONFIG ----------------------
dag_name = 'mediarithmics_kpis_new_instance'
email_on_failure = True
email_on_retry = False

env = os.environ.get("ENV")

# BQ specific config
bq_project = 'pm-{}-matrix'.format(env)
bq_dataset = 'store_partner'

dag_vars = Variable.get("mediarithmics_metrics_new_instance", deserialize_json=True, default_var={})
import_dashboards = dag_vars.get('dashboards', True)
import_segments = dag_vars.get('segments', True)
gap_filling_mode = dag_vars.get('gap_filling_mode', False)


param_dates = dag_vars.get('param_dates', True)
start_date, end_date = "{{ next_ds_nodash }}", "{{ next_ds_nodash }}"
if param_dates:
    # If we're loading a big time perimeter, it's preferable to launch the segment and dashboard KPIs separately
    # to avoid a 'too many simultaneous queries' error
    start_date, end_date = str(dag_vars.get('start_date', True)), str(dag_vars.get('end_date', True))

# GCS specific config
buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
gcs_bucket = buckets['matrix']
gcs_mics_dir = 'mediarithmics/{{ next_ds_nodash }}/kpis_new_instance_source'
gcs_mics_dir_target = 'mediarithmics/{{ next_ds_nodash }}/kpis_new_instance_target'
mics_dir = 'mediarithmics'
source = 'source'
target = 'target'

subfolder_schema_kpis = [
    {"name": "dashboard_name", "mode": "NULLABLE", "type": "STRING", "description": "", "fields": []},
    {"name": "value", "mode": "NULLABLE", "type": "INTEGER", "description": "", "fields": []}]
subfolder_schema_segments = [
    {"name": "segment_name", "mode": "NULLABLE", "type": "STRING", "description": "", "fields": []},
    {"name": "value", "mode": "NULLABLE", "type": "INTEGER", "description": "", "fields": []}]

# &nbsp;&nbsp;&nbsp;&nbsp for tablutation, <br> for linebreaks
body = """
    Bonjour, <br> 
    <br> 
    Vous recevez ce message car des métriques observées enregistrent des écarts de volumes importants comparé à J-7.<br> 
    Veuillez retrouver la liste des métriques en question dans le Gsheet : <br> 
    https://docs.google.com/spreadsheets/d/1DWfIgbzCCIILb0dnTEgaKdtvbqHMwUMFqtcj4I8DHSI/edit?usp=sharing<br> 
    <br> 
    Rappel des règles d’alerting sur les métriques suivantes : <br>
        - Global : un drop positif ou négatif de 30% minimum comparé à J-7 <br>
        - Segments : <br>
        &nbsp;&nbsp;&nbsp;&nbsp;- Segments d'audience (comportant un trigramme dans la nomenclature du segment) : un drop négatif de 30% minimum comparé à J-7 <br>
        &nbsp;&nbsp;&nbsp;&nbsp;- Segments 2nd party : un drop négatif de 30% minimum comparé à J-7 <br>
        - Environnement : un drop négatif de 30% minimum comparé à J-7<br>
    <br> 
    Cordialement,<br> 
    <br> 
    L'équipe IT-Data
"""


def dashboards_dry_run():
    """
    Return false if we are in dry run mode.
    Used for the ShortCircuitOperator below.
    """
    return import_dashboards


def segments_dry_run():
    """
    Return false if we are in dry run mode.
    Used for the ShortCircuitOperator below.
    """
    return import_segments


def get_date(file_path):
    """
    Return the date of the file's data based on its title
    Useful to check if a file is within our search perimeter,
    and to create a dated import table for each file
    """
    match = re.search(r"export_(?:dashboard|segment)_metrics_(\d{8})", file_path)
    if match:
        return match.group(1)
    return None


def get_file_name(file_path):
    """
    Return the filename, removing the folder and extension information
    """
    match = re.search(r'([^\/]+)(?=\.\w+$)', file_path)
    if match:
        return match.group(1)  # Returns the filename
    return None  # Returns None if no match was found


def get_missing_dates(metric_type, **kwargs):
    """
    Identifies dates where we're missing data in our historization table.

    Args:
        metric_type (str): Either 'dashboard' or 'segment'

    Returns:
        list: List of dates in YYYYMMDD format where data is missing
    """

    query = """
    WITH date_range AS (
        SELECT date
        FROM UNNEST(GENERATE_DATE_ARRAY(
            (SELECT MIN(observation_date) 
             FROM `{bq_project}.{bq_dataset}.mediarithmics_metrics_new_instance`
             WHERE type = '{metric_type}'),
            CURRENT_DATE()
        )) as date
    )
    SELECT FORMAT_DATE('%Y%m%d', dr.date) as missing_date
    FROM date_range dr
    LEFT JOIN (
        SELECT DISTINCT observation_date 
        FROM `{bq_project}.{bq_dataset}.mediarithmics_metrics_new_instance`
        WHERE type = '{metric_type}'
    ) hist ON dr.date = hist.observation_date
    WHERE hist.observation_date IS NULL
    ORDER BY dr.date
    """.format(bq_project=bq_project, bq_dataset=bq_dataset, metric_type=metric_type)

    hook = BigQueryHook(gcp_conn_id=default_args['bigquery_conn_id'],
                        delegate_to=None, use_legacy_sql=False)
    conn = hook.get_conn()
    cursor = conn.cursor()
    cursor.execute(query)
    missing_dates = [row[0] for row in cursor.fetchall()]

    return missing_dates


def list_blobs(start_date, end_date, **kwargs):
    """
    Return the list of available files in the S3 bucket corresponding to our date filters
    In gap filling mode, only returns files for dates where we're missing data
    """
    s3_hook = S3Hook(aws_conn_id='aws_s3_mediarithmics')
    files = s3_hook.list_keys(bucket_name='1695-prisma-media', prefix='export_dashboard_metrics/export')

    if not gap_filling_mode:
        # Original logic for normal mode
        start_date_obj = datetime.strptime(start_date, '%Y%m%d').date()
        end_date_obj = datetime.strptime(end_date, '%Y%m%d').date()

        logging.info(f"Start date : {start_date_obj}, end date : {end_date_obj}")
        files_with_dates = [file for file in files if get_date(file)]
        logging.info(f"Files before filtering : {files_with_dates}")
        filtered_files = [
            file for file in files_with_dates
            if start_date_obj <= datetime.strptime(get_date(file), '%Y%m%d').date() <= end_date_obj
        ]
        logging.info(f"Files after filtering : {filtered_files}")
    else:
        # Gap filling mode logic

        missing_dashboard_dates = get_missing_dates('dashboard') if import_dashboards else []
        #missing_dashboard_dates.sort(reverse=True)

        missing_segment_dates = get_missing_dates('segment') if import_segments else []
        #missing_segment_dates.sort(reverse=True)

        logging.info(f"Missing dashboard dates : {missing_dashboard_dates}")
        logging.info(f"Missing segment dates : {missing_segment_dates}")
        files_with_dates = [file for file in files if get_date(file)]
        #files_with_dates.sort(key=get_date, reverse=True)

        logging.info(f"Files before filtering : {files_with_dates}")
        filtered_files = [
            file for file in files_with_dates
            if ("_dashboard_metrics_" in file and get_date(file) in missing_dashboard_dates) or ("_segment_metrics_" in file and get_date(file) in missing_segment_dates)
        ]
        #filtered_files.sort(key=get_date, reverse=True)

        logging.info(f"Files after filtering : {filtered_files}")

    logging.info(f"Found {len(filtered_files)} files to process")
    return filtered_files


def file_reception_check(**kwargs):
    """
    Checks if the file listing function found any files given our search perimeter
    Returns either 'OK', or what we're missing
    """
    task_instance = kwargs['task_instance']
    files_to_load = task_instance.xcom_pull(task_ids=["get_file_list"], key="return_value")[0]
    logging.info(f"Files to load : {files_to_load}")

    num_segments = len([x for x in files_to_load if 'segment' in x])
    num_dashboards = len([x for x in files_to_load if 'dashboard' in x])
    logging.info(f"Segments : {num_segments}, dahsboards : {num_dashboards}")

    if (num_dashboards == 0) & (num_segments == 0):
        return 'dashboard et segment'
    if (num_dashboards == 0) & (num_segments > 0):
        return 'dashboard'
    if (num_dashboards > 0) & (num_segments == 0):
        return 'segment'
    if (num_dashboards > 0) & (num_segments > 0):
        return 'ok'


def files_existence_branch(**kwargs):
    """
    Branches the DAG based on file reception check results.
    Returns list of task_ids based on which files exist.
    """
    task_instance = kwargs['task_instance']
    file_check_result = task_instance.xcom_pull(task_ids=["file_reception_check"], key="return_value")[0]

    branches = []
    if file_check_result == 'ok':
        branches = ['upload_dashboard_files', 'upload_segment_files']
    elif file_check_result == 'dashboard':
        branches = ['upload_segment_files']
    elif file_check_result == 'segment':
        branches = ['upload_dashboard_files']
    return branches


def notify_mics_email(**kwargs):
    """
    Returns a function intended to be called from a PythonOperator
    and we send a mail if we find no segment file or no dashboard file
    """
    task_instance = kwargs['task_instance']
    type = task_instance.xcom_pull(task_ids=["file_reception_check"], key="return_value")[0]
    if type != "ok" and not gap_filling_mode :
        # @fixme :
        #  discuss whether we should put the recipient list in a variable,
        #  since on one hand we won't have to push a change each time,
        #  but on the other hand we're trying to transition away from Airflow variables for version control
        to =  ", ".join(["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"])
        # cc = ", ".join(cc)

        # if we want to notify that the sample file is not here

        title = "Export pour Dashboard Mics - Fichier non reçu : " + type
        body = ("Bonjour, <br> "
                "<br><br> Si vous recevez cet email, cela signifie que l'on n'a pas reçu"
                "<br>les données dans le bucket aws_s3_mediarithmics (1695).<br>"
                "<br>S'il n'est pas généré, les données du jour seront perdues.<br>"
                "<br>Veuillez nous prévenir une fois le fichier généré afin qu'on puisse l'ingérer.<br>"
                "<br><br>Cordialement, <br> "
                "L'équipe technique IT-Data"
                )

        if env == 'prod':
            send_email(to=to, subject=title, html_content=body, mime_charset='utf-8', conn_id='sendgrid_default')
            logging.info(f"Mail sent to recipients : {to}")
        print('email html + ' + body)


def import_kpi_files(exec_date, **kwargs):
    """
    Uploads and merges all the dashboard files we've found
    """
    gcs_folder_kpis = "gs://{}/{}/{}/kpi_new_instance_source/".format(gcs_bucket, mics_dir, exec_date)
    logging.info('Start !')
    # Get the file list from the previous task
    task_instance = kwargs['task_instance']
    files_to_load = task_instance.xcom_pull(task_ids=["get_file_list"], key="return_value")[0]
    kpi_files = [file for file in files_to_load if "dashboard_metrics_" in file]
    logging.info('Files to load : ' + str(files_to_load))

    # Treating KPI files
    for file in kpi_files:
        logging.info('Current loop : ' + str(file))
        file_name = get_file_name(file)
        file_date = get_date(file)
        logging.info('File name : ' + file_name + ", date : " + file_date)

        retrieve_dashboard_kpis = S3ToGCSOperator(
            task_id='retrieve_dashboard_kpis',
            bucket='1695-prisma-media',
            aws_conn_id='aws_s3_mediarithmics',
            prefix='export_dashboard_metrics/' + file_name,
            gcp_conn_id='gcs_matrix',
            dest_gcs=gcs_folder_kpis,
            replace=True,
        )
        retrieve_dashboard_kpis.execute(kwargs)
        # Load each file into a dedicated table that'll be merged into the target table
        bq_load_kpis = GCSToBigQueryOperator(
            task_id='bq_load_kpis',
            source_objects=[
                mics_dir + "/" + exec_date + "/kpi_new_instance_source/export_dashboard_metrics/export_dashboard_metrics_" + str(
                    file_date) + ".csv"],
            destination_project_dataset_table=bq_project + '.import.mics_kpis_new_instance_' + str(file_date),
            source_format='CSV',
            field_delimiter=',',
            autodetect=True,
            bucket=gcs_bucket,
            schema_fields=subfolder_schema_kpis,
            skip_leading_rows=1,
            write_disposition='WRITE_TRUNCATE',
            gcp_conn_id='bq_matrix'
        )
        bq_load_kpis.execute(kwargs)

    # Merge each file into the target table, including the observation date to historize the data

    merge_query = """
        CREATE TABLE IF NOT EXISTS `{bq_project}.{bq_dataset}.mediarithmics_metrics_new_instance` (
            observation_date DATE OPTIONS(description="The date where we received the file."),
            type STRING OPTIONS(description="The type of the indicator we're tracking (dashboard or segment)."),
            indicator_name STRING OPTIONS(description="The name of the indicator we're tracking."),
            value INT64 OPTIONS(description="The value on the given date.")
        ) OPTIONS(
            expiration_timestamp=NULL,
            description="Table where all Mics dashboards are stored and historized"
        );

        MERGE `{bq_project}.{bq_dataset}.mediarithmics_metrics_new_instance` as A
        USING (
            SELECT 
                dashboard_name,
                _TABLE_SUFFIX as file_date,
                MAX(value) as value -- To avoid KOs due to duplicates in source file
            FROM `{bq_project}.import.mics_kpis_new_instance_*`
            GROUP BY ALL
        ) as B
        ON DATE(A.observation_date) = DATE(PARSE_DATE('%Y%m%d', B.file_date))
            AND A.indicator_name = B.dashboard_name
            AND A.type = "dashboard"
        WHEN MATCHED THEN
            UPDATE SET A.value = B.value
        WHEN NOT MATCHED THEN
            INSERT VALUES(
                DATE(PARSE_DATE('%Y%m%d', B.file_date)),
                "dashboard",
                B.dashboard_name,
                B.value
            );
    """.format(bq_project=bq_project, bq_dataset=bq_dataset)

    logging.info(merge_query)
    logging.info('BQ conn id : ' + str(default_args['bigquery_conn_id']))
    hook = BigQueryHook(gcp_conn_id=default_args['bigquery_conn_id'],
                        delegate_to=None, use_legacy_sql=False)
    conn = hook.get_conn()
    cursor = conn.cursor()
    cursor.execute(merge_query)


def import_segment_files(exec_date, **kwargs):
    """
    Uploads and merges all the segment files we've found
    """
    gcs_folder_segments = "gs://{}/{}/{}/segment_new_instance_source/".format(gcs_bucket, mics_dir, exec_date)
    logging.info('Start !')
    # Get the file list from the previous task
    task_instance = kwargs['task_instance']
    files_to_load = task_instance.xcom_pull(task_ids=["get_file_list"], key="return_value")[0]
    segment_files = [file for file in files_to_load if "segment_metrics_" in file]
    logging.info('Files to load : ' + str(segment_files))

    # Treating segment files
    for file in segment_files:
        logging.info('Current loop : ' + str(file))
        file_name = get_file_name(file)
        file_date = get_date(file)
        logging.info('File name : ' + file_name + ", date : " + file_date)

        retrieve_dashboard_kpis = S3ToGCSOperator(
            task_id='retrieve_segment_kpis',
            bucket='1695-prisma-media',
            aws_conn_id='aws_s3_mediarithmics',
            prefix='export_dashboard_metrics/' + file_name,
            gcp_conn_id='gcs_matrix',
            dest_gcs=gcs_folder_segments,
            replace=True,
        )
        retrieve_dashboard_kpis.execute(kwargs)

        # Load each file into a dedicated table that'll be merged into the target table
        # The tables always have to match the date of the file and not the exec date, otherwise the full will not work
        bq_load_kpis = GCSToBigQueryOperator(
            task_id='bq_load_segmentss',
            # Must make sure that Mics don't change the folder and file names
            # Must nominally load each file and not a * wildcard, otherwise if we load multiple files,
            # each file will keep being loaded in the following loop iterations since it'll stay in the folder
            source_objects=[
                mics_dir + "/" + exec_date + "/segment_new_instance_source/export_dashboard_metrics/export_segment_metrics_" + str(
                    file_date) + ".csv"],
            destination_project_dataset_table=bq_project + '.import.mics_segments_new_instance_' + str(file_date),
            source_format='CSV',
            field_delimiter=',',
            autodetect=True,
            bucket=gcs_bucket,
            schema_fields=subfolder_schema_kpis,
            skip_leading_rows=1,
            write_disposition='WRITE_TRUNCATE',
            gcp_conn_id='bq_matrix'
        )
        bq_load_kpis.execute(kwargs)

    # Merge each file into the target table, including the observation date to historize the data
    merge_query = """
        CREATE TABLE IF NOT EXISTS `{bq_project}.{bq_dataset}.mediarithmics_metrics_new_instance` (
            observation_date DATE OPTIONS(description="The date where we received the file."),
            type STRING OPTIONS(description="The type of the indicator we're tracking (dashboard or segment)."),
            indicator_name STRING OPTIONS(description="The name of the indicator we're tracking."),
            value INT64 OPTIONS(description="The value on the given date.")
        ) OPTIONS(
            expiration_timestamp=NULL,
            description="Table where all Mics dashboards are stored and historized"
        );

        MERGE `{bq_project}.{bq_dataset}.mediarithmics_metrics_new_instance` as A
        USING (
            SELECT 
                dashboard_name,
                _TABLE_SUFFIX as file_date,
                MAX(value) AS value -- To avoid KOs due to duplicates in source file
            FROM `{bq_project}.import.mics_segments_new_instance_*`
            GROUP BY ALL 
        ) as B
        ON DATE(A.observation_date) = DATE(PARSE_DATE('%Y%m%d', B.file_date))
            AND A.indicator_name = B.dashboard_name
            AND A.type = "segment"
        WHEN MATCHED THEN
            UPDATE SET A.value = B.value
        WHEN NOT MATCHED THEN
            INSERT VALUES(
                DATE(PARSE_DATE('%Y%m%d', B.file_date)),
                "segment",
                B.dashboard_name,
                B.value
            );
    """.format(bq_project=bq_project, bq_dataset=bq_dataset)

    logging.info(merge_query)
    logging.info('BQ conn id : ' + str(default_args['bigquery_conn_id']))
    hook = BigQueryHook(gcp_conn_id=default_args['bigquery_conn_id'],
                        delegate_to=None, use_legacy_sql=False)
    conn = hook.get_conn()
    cursor = conn.cursor()
    cursor.execute(merge_query)

    return True


if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    email_on_retry = False
    bq_project = 'pm-{}-matrix'.format(env)

# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2021, 8, 5, 00, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=5),
    'sla': None,
    'email_on_retry': email_on_retry,
    'retries': 2,
    'retry_delay': timedelta(hours=3),
    'depends_on_past': False,
    'bigquery_conn_id': 'bq_matrix',
    'gcp_conn_id': 'bq_matrix',
}

with CDocDAG(
        dag_name,
        description='Importing Mics KPIs for historization',
        tags=["mediarithmics"],
        doc_md=__doc__,
        schedule_interval='0 9 * * *',  # Moved forward to run after the file generation and ingest data the same day
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=15),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/mediarithmics/', '/home/<USER>/gcs/data/scripts/']
) as dag:
    # -----------------Filter the files to keep only those within the date range-----------------#

    task_doc_md = """
        If we find no file, we send a mail to mics to notify them% <br />
        """
    get_file_list = PythonOperator(
        task_id='get_file_list',
        python_callable=list_blobs,
        op_kwargs={
            'start_date': start_date,
            'end_date': end_date
        },
        provide_context=True

    )
    get_file_list.doc_md = task_doc_md

    task_doc_md = """
        If we find no file, we send a mail to mics to notify them% <br />
        """
    file_reception_check = PythonOperator(
        task_id='file_reception_check',
        python_callable=file_reception_check
    )
    file_reception_check.doc_md = task_doc_md

    # Split the upload_files dummy operator into two
    task_doc_md = """
        Handles dashboard file uploads to the processing pipeline.
        """
    upload_dashboard_files = EmptyOperator(
        task_id='upload_dashboard_files',
    )
    upload_dashboard_files.doc_md = task_doc_md

    task_doc_md = """
        Handles segment file uploads to the processing pipeline.
        """
    upload_segment_files = EmptyOperator(
        task_id='upload_segment_files',
    )
    upload_segment_files.doc_md = task_doc_md

    task_doc_md = """
        Branches the workflow based on file existence check results.
        """
    branch_operator = BranchPythonOperator(
        task_id='files_existence_branch',
        python_callable=files_existence_branch,
        provide_context=True
    )
    branch_operator.doc_md = task_doc_md

    task_doc_md = """
        If we find no file, we send a mail to mics to notify them% <br />
        """
    notify_if_file_absent = PythonOperator(
        task_id='notify_if_file_absent',
        python_callable=notify_mics_email
    )
    notify_if_file_absent.doc_md = task_doc_md

    # -----------------Dry run-----------------#
    task_doc_md = """
        Short circuit to avoid importing a type of file if it's not available so that it doesn't break the DAG<br />
        If Airflow variable json: mediarithmics_metrics['dashboard'] is set to:<br />
        false, then dry run stop the dag otherwize, if it's true, we continue the dag.<br />
        We used a function and it must return False to shortcut downstream tasks.
        """
    dashboards_short_circuit = ShortCircuitOperator(
        task_id="dashboards_short_circuit",
        python_callable=dashboards_dry_run
    )
    dashboards_short_circuit.doc_md = task_doc_md

    task_doc_md = """
        Short circuit to avoid importing a type of file if it's not available so that it doesn't break the DAG<br />
        If Airflow variable json: mediarithmics_metrics['segment'] is set to:<br />
        false, then dry run stop the dag otherwize, if it's true, we continue the dag.<br />
        We used a function and it must return False to shortcut downstream tasks.
        """
    segments_short_circuit = ShortCircuitOperator(
        task_id="segments_short_circuit",
        python_callable=segments_dry_run
    )
    segments_short_circuit.doc_md = task_doc_md

    # -----------------Import the KPI files-----------------#

    task_doc_md = """
        Import all of the dashboard files we've found
        """
    import_kpi_files = PythonOperator(
        task_id='import_kpi_files',
        python_callable=import_kpi_files,
        op_kwargs={
            'exec_date': "{{ next_ds_nodash }}"
        },
        provide_context=True
    )
    import_kpi_files.doc_md = task_doc_md

    # -----------------Import the segment files-----------------#

    task_doc_md = """
        Import all of the segment files we've found
        """
    import_segment_files = PythonOperator(
        task_id='import_segment_files',
        python_callable=import_segment_files,
        op_kwargs={
            'exec_date': "{{ next_ds_nodash }}"
        },
        provide_context=True
    )
    import_segment_files.doc_md = task_doc_md

    # -----------------Send alerting email-----------------#

    task_doc_md = """
        Send an email if some metrics have passed a rise/drop threshold
        """
    metrics_alerting = MetricsAlertingOperator(
        task_id='metrics_alerting',
        dag_name=dag_name,
        # recipients = ['<EMAIL>','<EMAIL>','<EMAIL>'],
        recipients=['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'] if env == 'prod' else [],
        title="[Dashboard Mediarithmics] - Alerte drops volumes métriques (nouvelle instance)",
        body=body,
        path="/home/<USER>/gcs/data/sql/mediarithmics/alerting",
        sql_file="ko_metrics_count_new_instance.sql",  # @fixme not used for now
        bq_project=bq_project,
        table="store_monitoring.mediarithmics_metrics_alerting_new_instance",
        env=env
    )
    metrics_alerting.doc_md = task_doc_md

    get_file_list >> file_reception_check >> [branch_operator, notify_if_file_absent]
    branch_operator >> [upload_dashboard_files, upload_segment_files]
    upload_dashboard_files >> dashboards_short_circuit >> import_kpi_files
    upload_segment_files >> segments_short_circuit >> import_segment_files
    [import_kpi_files, import_segment_files] >> metrics_alerting