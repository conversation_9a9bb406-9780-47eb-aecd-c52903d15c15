r"""
**PURPOSE**:
This DAG is used to prepare tables before sending them GCS and finally to notify.

**METHODOLOGY:**

List of generated_data tables:

**- `export_notify.partner_batch_reactivity___by_platform_

To compute this,
**we use:**
**Refined data:**- `pm-prod-matrix.refined_data.batch_userbase`**Generated data:**- `pm-prod-matrix.generated_data.last_event_by_platform`**Business data:**- `pm-prod-matrix.business_data.profile_digital_360` 

This DAG is used also to generate historical push event (optin, sent, optout, delete).

**GOOD TO KNOW: **

This dag must be executed at 3h UTC. It depends on `generated_data__batch`, which is executed at 5h UTC.
Hence, this dag has been delayed by 24h regarding the `generated_data_batch` dag.
In other words, data from this dag are 3 days late from today's date.
"""

import logging
import os
import re
from datetime import datetime, timedelta

from airflow.models import Variable
from airflow.operators.bash import BashOperator
from airflow.operators.python import <PERSON><PERSON>perator, ShortCircuitOperator
from airflow.providers.common.sql.sensors.sql import SqlSensor
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.google.cloud.transfers.bigquery_to_gcs import BigQueryToGCSOperator
from collapsible_doc_dag import CDocDAG
from sftp_plugin.operators.gcs_to_sftp_glob_operator import \
    GoogleCloudStorageToSftpGlobOperator as GcsToSftpGlobOperator

# ----------------- CONFIG ----------------------
dag_name = 'export_notify'

env = os.environ.get("ENV")
bq_project = 'pm-prod-matrix'
gcs_uri = 'gs://it-data-prod-matrix-pipeline/'
email_on_failure = True
email_on_retry = False
suffix = ''

current_datetime = '{{ next_execution_date.strftime("%Y%m%d_%H%M%S") }}'
current_date = '{{ next_execution_date.strftime("%Y%m%d") }}'
current_date_formatted = '{{ next_execution_date.strftime("%Y-%m-%d") }}'
bq_dataset = 'export_notify'
flow_name = 'batch_reactivity'

export_notify_variable = Variable.get("export_notify", deserialize_json=True).get("batch_reactivity")
bucket_name = export_notify_variable['bucket']
bucket_name_backup = export_notify_variable['bucket_backup']
start_date = export_notify_variable.get('start_date', current_date_formatted)
end_date = export_notify_variable.get('end_date', current_date_formatted)
prefix = export_notify_variable['prefix']
export_data_bool = bool(export_notify_variable['export_data_bool'])

sftp_conn_id = 'sftp_notify'
gcs_dirname = 'partners/export_notify/batch_reactivity/'
sftp_dirname = '/data/ciblage/'
gcs_conn_id = 'gcs_matrix'

# ------------- FOR PREPROD ENV -----------------
if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    email_on_retry = False
    bq_project = 'pm-preprod-matrix'
    gcs_uri = 'gs://it-data-preprod-matrix-preprod-pipeline/'
    suffix = '_test'
# -----------------------------------------------

temp_bucket_path = f"{gcs_uri}data/tmp/"
final_bucket_path = f"{gcs_uri}partners/{bq_dataset}/{flow_name}/{current_date}/"

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'start_date': datetime(2023, 10, 25, 15, 00, 00),
    'retries': 3,
    'retry_delay': timedelta(minutes=5),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'email_on_retry': email_on_retry,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=8),
    'sla': None,
    'gcp_conn_id': 'gcs_matrix',

    # export format & destination
    'export_format': 'csv',
    'field_delimiter': ';',
    'gzip': False,
    'parameters': None,
    'bucket': bucket_name,

    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_TRUNCATE',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',
    'source_format': 'CSV',
    'autodetect': False,
    'max_bad_records': "0",
    'allow_jagged_rows': True,
    'ignore_unknown_values': True,
    'quote_character': '',

    # google cloud storage for export
    'gcs_conn_id': 'gcs_matrix',
    'gcs_bucket': bucket_name,
}

# Use backup bucket for historical data
if start_date != current_date_formatted:
    bucket_name = bucket_name_backup

with CDocDAG(dag_name,
             description="Prepare, aggregate and export batch reactivity data to GCS and then to Notify's SFTP",
             doc_md=__doc__,
             schedule_interval="0 3 * * *",  # Notify requirement
             catchup=False,
             max_active_runs=1,
             dagrun_timeout=timedelta(hours=15),
             template_searchpath=['/home/<USER>/gcs/data/sql/notify'],
             default_args=default_args) as dag:
    # ------------------------------------------------
    #       Functions to import and compose data
    # ------------------------------------------------

    def export_dry_run():
        """
        Return false if we are in dry run mode.
        Used for the ShortCircuitOperator below.
        """
        return export_data_bool


    def _filter_files(gcs_conn_id, bucket_name, prefix, current_date_formatted, start_date=current_date_formatted,
                      end_date=current_date_formatted, **kwargs):
        """
        Filter out files from GCS bucket based on a start_date and an end_date.
        Arguments:
            gcs_conn_id:            (str)  connection id to gcs
            bucket_name:            (str)  name of the bucket where the files are
            prefix:                 (str)  path to the folder where the files are
            current_date_formatted: (str)  dag execution date as yyyy-mm-dd
            start_date:             (str)  start date used to filter files
            end_date:               (str)  end date used to filter files
        Returns:
            List of filtered files.
        """

        logging.info(f'Filtering files from {bucket_name}')

        # Lists all the blobs in the bucket with a given prefix
        hook = GCSHook(gcp_conn_id=gcs_conn_id)
        files = hook.list(bucket_name=bucket_name, prefix=prefix)

        logging.info(f"Exporting files from {start_date} to {end_date}")

        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

        logging.info('Files before filter apply \n\n' + str(files))

        # Filter elements based on date and exclude specific trigrams using regular expressions
        filtered_files = [
            file for file in files
            if not file.endswith('/')  # Exclude directories
               and start_date <= datetime.strptime(re.search(r"(\d{8})", file).group(1), "%Y%m%d").date() <= end_date
        ]

        logging.info("Filtered files: " + str(filtered_files))
        return filtered_files


    def _bq_to_gcs(current_datetime, export_notify_variable, temp_bucket_path, flow_name, final_bucket_path, suffix,
                   **kwargs):
        """
        Exports data from BQ to GCS
        """

        for brand in export_notify_variable['brands']:
            for support in brand['support']:
                temp_gcs_file_path = f"{temp_bucket_path}tmp_composed_{brand['brand_trigram']}_{support.upper()}_*.csv"
                bq_table = f"partner_{flow_name}_{brand['brand_trigram']}_{support.upper()}_by_platform_{current_datetime}"
                final_gcs_file_path = f"{final_bucket_path}notify_audit_in_{brand['brand_trigram']}_{support.upper()}_{current_datetime}{suffix}.csv"

                # Create bq_to_gcs tasks dynamically
                bq_to_gcs_task_name = f"store_table_{brand['brand_trigram']}_{support}"
                bq_to_gcs = BigQueryToGCSOperator(task_id=bq_to_gcs_task_name,
                                                  destination_cloud_storage_uris=[temp_gcs_file_path],
                                                  source_project_dataset_table=f"{bq_project}.{bq_dataset}.{bq_table}",
                                                  print_header=False,
                                                  force_rerun=True,
                                                  field_delimiter=';',
                                                  export_format='CSV',
                                                  gcp_conn_id='bq_matrix'
                                                  )
                bq_to_gcs.execute(kwargs)

                logging.info(f'Data exported to GCS. Starting compose...')

                # When we export data from BQ to GCS, it might be split into many chunks.
                # We need hence to compose files once they are stored in GCS.
                compose_files_task_name = f"compose_files_{brand['brand_trigram']}_{support}"
                compose_files_task = BashOperator(task_id=compose_files_task_name,
                                                  bash_command=f'cd /home/<USER>/gcs/data/scripts/ && ./gcs_compose_modified.sh {temp_bucket_path} {temp_gcs_file_path} {final_gcs_file_path} {flow_name} {bq_project} {bq_table} {bq_dataset}'
                                                  )
                compose_files_task.execute(kwargs)

                logging.info(f'Files composed!')


    def _export_data(sftp_conn_id, gcs_dirname, sftp_dirname, bucket_name, **kwargs):
        """
        Export data to Notify's SFTP
        """
        task_instance = kwargs['ti']
        list_files = task_instance.xcom_pull(task_ids=['filter_files'], key='return_value')[0]

        logging.info(f'Files to be exported:\n {list_files}')

        logging.info(f'Exporting data from {bucket_name}...')

        export_data = GcsToSftpGlobOperator(
            task_id='export_notify',
            gcs_bucket=bucket_name,
            sftp_conn_id=sftp_conn_id,
            file_pattern=list_files,
            file_exclude_pattern=[],
            gcs_dirname=gcs_dirname,
            sftp_dirname=sftp_dirname,
            gzip=False
        )
        export_data.execute(kwargs)

        logging.info('Data exported!')


    # ------------------------------------------------
    #                   Sensor
    # ------------------------------------------------

    task_doc_md = """
        We need to wait the generated_data__batch dag to complete before creating partner_batch_reactivity table.
        """
    wait_for_batch_last_event_by_platform_event = SqlSensor(
        task_id='wait_for_batch_last_event_by_platform_event',
        conn_id='airflow_db',
        timeout=60 * 60 * 4,  # 4 hours
        retries=24,
        poke_interval=60 * 10,  # 10 min
        mode="reschedule",
        sql="""
                SELECT CASE WHEN state = 'success' THEN TRUE ELSE FALSE END
                FROM task_instance
                WHERE dag_id = 'generated_data__batch{str_env}'
                    AND task_id = 'batch_last_event_by_platform'
                    AND DATE(start_date) = current_date - 3
            """.format(str_env='' if env == 'prod' else '_' + env)
    )
    wait_for_batch_last_event_by_platform_event.doc_md = task_doc_md

    # ------------------------------------------------
    #           Generate tables for notify
    # ------------------------------------------------

    task_doc_md = """
        Prepare tables with reactivity data from batch to be exported to GCS.
        """
    partner_batch_reactivity_brand = BigQueryExecuteQueryOperator(
        task_id="partner_batch_reactivity_brand",
        sql='batch_reactivity_by_platform.sql',
        params={
            'bq_project': bq_project,
            'dict_brands': export_notify_variable['brands'],
        }
    )
    partner_batch_reactivity_brand.doc_md = task_doc_md

    # ------------------------------------------------
    #         Export to GCS and compose data
    # ------------------------------------------------

    task_doc_md = """
        Export data from BQ to GCS and merge files at the end
        """
    bq_to_gcs = PythonOperator(
        task_id="bq_to_gcs",
        python_callable=_bq_to_gcs,
        op_args=[current_datetime, export_notify_variable, temp_bucket_path, flow_name, final_bucket_path, suffix]
    )
    bq_to_gcs.doc_md = task_doc_md

    task_doc_md = """
        Filter files before sending to Notify's SFTP
        """
    filter_files = PythonOperator(
        task_id="filter_files",
        python_callable=_filter_files,
        op_args=[gcs_conn_id, bucket_name, prefix, current_date_formatted, start_date, end_date]
    )
    filter_files.doc_md = task_doc_md

    task_doc_md = """
        Short circuit to avoid exporting files during test<br />
        If Airflow variable json: export_notify_variable['batch_reactivity']['export_data_bool'] is set to:<br />
        false, then dry run stop the dag otherwise, if it's true, we continue the dag.<br />
        We used a function and it must return False to shortcut downstream tasks.
        """
    export_short_circuit = ShortCircuitOperator(
        task_id="export_short_circuit",
        python_callable=export_dry_run
    )
    export_short_circuit.doc_md = task_doc_md

    task_doc_md = """
        Export files to Notify's SFTP
        """
    export_files = PythonOperator(
        task_id="export_files",
        python_callable=_export_data,
        op_args=[sftp_conn_id, gcs_dirname, sftp_dirname, bucket_name]
    )
    export_files.doc_md = task_doc_md

    wait_for_batch_last_event_by_platform_event >> partner_batch_reactivity_brand >> bq_to_gcs >> filter_files >> export_short_circuit
    export_short_circuit >> export_files
