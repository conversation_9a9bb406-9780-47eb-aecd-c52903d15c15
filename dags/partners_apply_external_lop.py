r"""
**PURPOSE:**

This dag is used to catch up lop from webrivage and riviera.

First, we create both tables for email_event to send to psql.
Then we merge both tables and avoid duplicate email consent unsub.
Finally, send this table to psql.
"""

import os
from datetime import timed<PERSON><PERSON>, datetime

import pendulum
from airflow.models import Variable
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from bq_plugin.operators.bigquery_to_psql_operator import BigQueryToPostgresOperator
from collapsible_doc_dag import CDocDAG
from matrix_plugin.operators.bigquery_to_email_event import BigQueryToEmailEventOperator

# ----------------- CONFIG ----------------------
dag_name = 'partners_apply_external_lop'
email_on_failure = True
email_on_retry = False

env = os.environ.get("ENV")

# BQ specific config
bq_project = 'pm-{}-matrix'.format(env)

# GCS specific config
buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']
path_global_lop_tech = 'partners/technical_lop/{{ next_ds }}/global/'

if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    email_on_retry = False

# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2021, 8, 5, tzinfo=pendulum.timezone("Europe/Paris")),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=50),
    'sla': None,
    'email_on_retry': email_on_retry,
    'retries': 2,
    'retry_delay': timedelta(minutes=1),
    'depends_on_past': False,
    'bigquery_conn_id': 'bq_matrix',
    'gcp_conn_id': 'bq_matrix',
    'temp_bucket': matrix_bucket,
    'postgres_conn_id': 'psql_matrix_email_app',
    'use_legacy_sql': False,
}

with CDocDAG(
        dag_name,
        description='lop Webrivage and Riviera',
        tags=['lop', 'partner'],
        doc_md=__doc__,
        schedule_interval='0 6 * * *',  # once a day, at 6AM CET
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=5),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/partners/technical_lop/']
) as dag:
    create_table_lop_webrivage = BigQueryExecuteQueryOperator(
        task_id='create_table_lop_webrivage',
        sql='webrivage_lop.sql',
    )
    create_table_lop_webrivage.doc_md = """
        Create a table for webrivage lop<br />
        It can handle bounced, purge and opposition
    """

    create_table_lop_riviera = BigQueryExecuteQueryOperator(
        task_id='create_table_lop_riviera',
        sql='riviera_lop.sql',
    )
    create_table_lop_riviera.doc_md = 'Create a table for riviera lop'

    merge_partners_lop_event = BigQueryExecuteQueryOperator(
        task_id='merge_partners_lop_event',
        sql='merge_partners_lop_event.sql',
    )
    merge_partners_lop_event.doc_md = 'Merge both table before push it to psql event queue'

    export_events_to_queue = BigQueryToEmailEventOperator(
        task_id='export_events_to_queue',
        postgres_conn_id='psql_matrix_email_app',
        database='matrix',
        bigquery_conn_id='bq_matrix',
        destination_cloud_storage_object_path=path_global_lop_tech +
                                              'lop_partners_event_{{ next_execution_date.strftime("%Y%m%d") }}.csv',
        import_table='matrix__email_tmp.bq_to_psql_lop_partners',
        source_project_dataset_table='export_matrix_email.lop_partners_event',
        bucket=matrix_bucket,
    )
    export_events_to_queue.doc_md = 'Send email event to PostgreSQL email_event queue (matrix__email_queue.email_event)'

    [create_table_lop_webrivage, create_table_lop_riviera] >> merge_partners_lop_event >> export_events_to_queue

    # -- Compute technical_blacklist from partners LOP data
    refine_technical_blacklist = BigQueryExecuteQueryOperator(
        task_id='refine_technical_blacklist',
        sql='refine_technical_blacklist.sql',
    )
    refine_technical_blacklist.doc_md = """
    Daily compute technical POL from partners lop data: <br/>
    
    - Splio<br/>
    - Riviera<br/>
    - Webrivage<br/>
    """

    export_technical_blacklist_to_psql = BigQueryToPostgresOperator(
        task_id='export_technical_blacklist_to_psql',
        source_export_dataset_table=bq_project + ':temp.technical_blacklist_{{ next_execution_date.strftime("%Y%m%d") }}',
        destination_table='matrix__email_import.technical_blacklist',
        truncate_destination_table=True,
        bucket=matrix_bucket,
        destination_cloud_storage_object_path=path_global_lop_tech + 'technical_blacklist_{{ next_execution_date.strftime("%Y%m%d") }}.csv'
    )
    export_technical_blacklist_to_psql.doc_md = 'Export refined technical_blacklist to PSQL tmp table'

    import_technical_blacklist_from_bq = SQLExecuteQueryOperator(
        task_id='import_technical_blacklist_from_bq',
        sql='import_technical_blacklist_from_bq.sql',
        autocommit=True,
        conn_id='psql_matrix_email_app',
    )
    import_technical_blacklist_from_bq.doc_md = 'Update technical blacklists in final PGSQL table'

    refine_technical_blacklist >> export_technical_blacklist_to_psql >> import_technical_blacklist_from_bq
