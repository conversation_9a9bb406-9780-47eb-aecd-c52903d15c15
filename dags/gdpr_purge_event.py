r"""
**PURPOSE:**

This DAG purges EmailEvent and Pandora related data to comply with GDPR
We only keep the latest 6 months events from those tables
"""

import os
from datetime import timedelta, datetime

from airflow.models import Variable
from airflow.providers.common.sql.operators.sql import SQLExecuteQueryOperator
from collapsible_doc_dag import CDocDAG

dag_name = 'gdpr_events_purge'
email_on_failure = True
# ------------- FOR PREPROD ENV -----------------
env = os.environ.get("ENV")
if env != 'prod':
    dag_name = '{}_{}'.format(dag_name, env)
    email_on_failure = False
# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2023, 4, 21, 4, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=60),
    'sla': None,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'depends_on_past': False,
    # PSQL Connection
    'dbname': 'matrix',
}

with CDocDAG(
        dag_name,
        description='GDPR Events Purge process',
        tags=["pandora"],
        doc_md=__doc__,
        schedule_interval="5 4 * * *",
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=2),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/purge/']
) as dag:
    task_doc_md = """
    Purging data from Last Seen Email Hash
    """
    pandora_purge_seen_email_hash = SQLExecuteQueryOperator(
        task_id='pandora_purge_seen_email_hash',
        sql='pandora_purge_seen_email_hash.sql',
        conn_id='psql_matrix_email_app'
    )
    pandora_purge_seen_email_hash.doc_md = task_doc_md

    task_doc_md = """
    Purging data from Kickbox event
    """
    pandora_purge_kickbox_event = SQLExecuteQueryOperator(
        task_id='pandora_purge_kickbox_event',
        sql='pandora_purge_kickbox_event.sql',
        conn_id='psql_pandora_app'
    )
    pandora_purge_kickbox_event.doc_md = task_doc_md

    pandora_purge_cancel_event = SQLExecuteQueryOperator(
        task_id='pandora_purge_cancel_event',
        sql='pandora_purge_cancel_event.sql',
        conn_id='psql_pandora_app'
    )
    pandora_purge_cancel_event.doc_md = task_doc_md

    task_doc_md = """
    Purging data from Pandora event
    """
    pandora_purge_event = SQLExecuteQueryOperator(
        task_id='pandora_purge_event',
        sql='pandora_purge_event.sql',
        conn_id='psql_pandora_app'
    )
    pandora_purge_event.doc_md = task_doc_md

    task_doc_md = """
    Purging data from Email event
    """
    email_event_purge = SQLExecuteQueryOperator(
        task_id='email_event_purge',
        sql='email_event_purge.sql',
        conn_id='psql_matrix_email_app'
    )
    email_event_purge.doc_md = task_doc_md

    task_doc_md = """
    Purging data from Tmail Email Content and Email
    """
    tmail_email_purge = SQLExecuteQueryOperator(
        task_id='tmail_email_purge',
        sql='tmail_email_purge.sql',
        conn_id='psql_tmail_app'
    )
    tmail_email_purge.doc_md = task_doc_md

    pandora_purge_cancel_event >> [pandora_purge_event, email_event_purge]
