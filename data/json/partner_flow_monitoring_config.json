{"flows": [{"pattern_name": "partner_splio_event_clicks", "pattern_format": "pattern_name_scope_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "import", "flow_type": "events", "scopes": ["cac_alert", "caminteresse", "caminteresse_alert", "caminteresse_loy", "capital", "capital_alert", "capital_loy", "capital_paid", "cuisineactuelle", "cuisineactuelle_crm", "cuisineactuelle_loy", "femme_actuelle", "femme_actuelle_loy", "femmeactuelle_alert", "femmeactuelle_crm", "gala", "gala_alert", "gala_loy", "gentside_alert", "gentside_fr", "gentside_fr_loy", "geo", "geo_alert", "geo_loy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harpers<PERSON><PERSON><PERSON>_<PERSON>y", "harvard_business_loy", "harvard_business_rev", "neon", "neon_alert", "neon_loy", "nl_shopping", "ohmymag_alert", "ohmymag_fr", "ohmymag_fr_loy", "pm_b2b_prisme", "prisma_connect", "prisma_connect_test", "programme_tv", "programme_tv_loy", "t2s_alert", "tele_loisirs", "tele_loisirs_loy", "teleloisirs_alert", "voici", "voici_alert", "voici_crm", "voici_loy"], "is_active": true, "flow_direction": "import", "flow_name": "splio_activity", "partner_name": "splio"}, {"pattern_name": "partner_splio_event_sent", "pattern_format": "pattern_name_scope_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "import", "flow_type": "events", "scopes": ["cac_alert", "caminteresse", "caminteresse_alert", "caminteresse_loy", "capital", "capital_alert", "capital_loy", "capital_paid", "cuisineactuelle", "cuisineactuelle_crm", "cuisineactuelle_loy", "femme_actuelle", "femme_actuelle_loy", "femmeactuelle_alert", "femmeactuelle_crm", "gala", "gala_alert", "gala_loy", "gentside_alert", "gentside_fr", "gentside_fr_loy", "geo", "geo_alert", "geo_loy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harpers<PERSON><PERSON><PERSON>_<PERSON>y", "harvard_business_loy", "harvard_business_rev", "neon", "neon_alert", "neon_loy", "nl_shopping", "ohmymag_alert", "ohmymag_fr", "ohmymag_fr_loy", "pm_b2b_prisme", "prisma_connect", "prisma_connect_test", "programme_tv", "programme_tv_loy", "t2s_alert", "tele_loisirs", "tele_loisirs_loy", "teleloisirs_alert", "voici", "voici_alert", "voici_crm", "voici_loy"], "is_active": true, "flow_direction": "import", "flow_name": "splio_activity", "partner_name": "splio"}, {"pattern_name": "partner_splio_event_open", "pattern_format": "pattern_name_scope_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "import", "flow_type": "events", "scopes": ["cac_alert", "caminteresse", "caminteresse_alert", "caminteresse_loy", "capital", "capital_alert", "capital_loy", "capital_paid", "cuisineactuelle", "cuisineactuelle_crm", "cuisineactuelle_loy", "femme_actuelle", "femme_actuelle_loy", "femmeactuelle_alert", "femmeactuelle_crm", "gala", "gala_alert", "gala_loy", "gentside_alert", "gentside_fr", "gentside_fr_loy", "geo", "geo_alert", "geo_loy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harpers<PERSON><PERSON><PERSON>_<PERSON>y", "harvard_business_loy", "harvard_business_rev", "neon", "neon_alert", "neon_loy", "nl_shopping", "ohmymag_alert", "ohmymag_fr", "ohmymag_fr_loy", "pm_b2b_prisme", "prisma_connect", "prisma_connect_test", "programme_tv", "programme_tv_loy", "t2s_alert", "tele_loisirs", "tele_loisirs_loy", "teleloisirs_alert", "voici", "voici_alert", "voici_crm", "voici_loy"], "is_active": true, "flow_direction": "import", "flow_name": "splio_activity", "partner_name": "splio"}, {"pattern_name": "splio_campaign_stat_staging", "pattern_format": "pattern_name_scope_campaign_report_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "import", "flow_type": "events", "scopes": ["cac_alert", "caminteresse", "caminteresse_alert", "caminteresse_loy", "capital", "capital_alert", "capital_loy", "capital_paid", "cuisineactuelle", "cuisineactuelle_crm", "cuisineactuelle_loy", "femme_actuelle", "femme_actuelle_loy", "femmeactuelle_alert", "femmeactuelle_crm", "gala", "gala_alert", "gala_loy", "gentside_alert", "gentside_fr", "gentside_fr_loy", "geo", "geo_alert", "geo_loy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harpers<PERSON><PERSON><PERSON>_<PERSON>y", "harvard_business_loy", "harvard_business_rev", "neon", "neon_alert", "neon_loy", "nl_shopping", "ohmymag_alert", "ohmymag_fr", "ohmymag_fr_loy", "pm_b2b_prisme", "prisma_connect", "prisma_connect_test", "programme_tv", "programme_tv_loy", "t2s_alert", "tele_loisirs", "tele_loisirs_loy", "teleloisirs_alert", "voici_alert", "voici_crm", "voici_loy"], "is_active": true, "flow_direction": "import", "flow_name": "splio_campaign_stat_history", "partner_name": "splio"}, {"pattern_name": "partner_snapshot", "pattern_format": "pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "import", "flow_type": "events", "scopes": ["not_set"], "is_active": true, "flow_name": "splio_snapshot_import", "flow_direction": "import", "partner_name": "splio"}, {"pattern_name": "tracking_cible", "pattern_format": "pattern_name_scope_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "import", "flow_type": "events", "scopes": ["cac_alert", "caminteresse", "caminteresse_alert", "capital", "capital_alert", "capital_loy", "capital_paid", "cuisineactuelle", "cuisineactuelle_crm", "femme_actuelle", "femmeactuelle_alert", "femmeactuelle_crm", "gala", "gentside_alert", "gentside_fr", "geo", "geo_alert", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harvard_business_rev", "nl_shopping", "ohmymag_alert", "ohmymag_fr", "prisma_connect", "programme_tv", "programme_tv_loy", "tele_loisirs", "tele_loisirs_loy", "teleloisirs_alert", "voici", "voici_alert", "voici_loy"], "is_active": true, "flow_name": "splio_target_campaign", "flow_direction": "import", "partner_name": "splio"}, {"pattern_name": "partner_snapshot_splio_lop", "pattern_format": "pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "import", "flow_type": "events", "scopes": ["not_set"], "is_active": true, "flow_direction": "import", "flow_name": "splio_lop_import", "partner_name": "splio"}, {"pattern_name": "rogue_one_theme", "pattern_format": "pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "import", "flow_type": "events", "scopes": ["not_set"], "is_active": false, "flow_direction": "import", "flow_name": "splio_report_campaign", "partner_name": "splio"}, {"pattern_name": "rogue_one_email_consent", "pattern_format": "pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "import", "flow_type": "events", "scopes": ["not_set"], "is_active": false, "flow_direction": "import", "flow_name": "splio_report_campaign", "partner_name": "splio"}, {"pattern_name": "tracking_splio_report", "pattern_format": "pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "import", "flow_type": "events", "scopes": ["not_set"], "is_active": true, "flow_direction": "import", "flow_name": "splio_report_campaign", "partner_name": "splio"}, {"pattern_name": "tracking_splio_report_data", "pattern_format": "pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "import", "flow_type": "events", "scopes": ["not_set"], "is_active": true, "flow_direction": "import", "flow_name": "splio_report_campaign", "partner_name": "splio"}, {"pattern_name": "tracking_splio_report_link", "pattern_format": "pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "import", "flow_type": "events", "scopes": ["not_set"], "is_active": true, "flow_direction": "import", "flow_name": "splio_report_campaign", "partner_name": "splio"}, {"pattern_name": "contacts_099", "pattern_format": "scope_pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "temp", "flow_type": "profiles", "scopes": ["cac_alert", "caminteresse_alert", "caminteresse", "caminteresse_loy", "capital_alert", "capital", "capital_loy", "capital_paid", "cuisineactuelle", "cuisineactuelle_crm", "cuisineactuelle_loy", "femme_actuelle", "femme_actuelle_loy", "femmeactuelle_alert", "femmeactuelle_crm", "gala_alert", "gala", "gala_loy", "gentside_alert", "gentside_fr", "gentside_fr_loy", "geo_alert", "geo", "geo_loy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harpers<PERSON><PERSON><PERSON>_<PERSON>y", "harvard_business_loy", "harvard_business_rev", "neon_alert", "neon", "neon_loy", "nl_shopping", "ohmymag_alert", "ohmymag_fr", "ohmymag_fr_loy", "prisma_connect", "prisma_connect_test", "programme_tv", "programme_tv_loy", "t2s_alert", "tele_loisirs", "tele_loisirs_loy", "teleloisirs_alert", "voici_alert", "voici", "voici_crm", "voici_loy"], "is_active": true, "flow_direction": "export", "flow_name": "datahub_099", "partner_name": "splio"}, {"pattern_name": "contacts_090", "pattern_format": "scope_pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "temp", "flow_type": "profiles", "scopes": ["cac_alert", "caminteresse", "caminteresse_alert", "caminteresse_loy", "capital", "capital_alert", "capital_loy", "capital_paid", "cuisineactuelle", "cuisineactuelle_crm", "cuisineactuelle_loy", "femme_actuelle", "femme_actuelle_loy", "femmeactuelle_alert", "femmeactuelle_crm", "gala", "gala_alert", "gala_loy", "gentside_alert", "gentside_fr", "gentside_fr_loy", "geo", "geo_alert", "geo_loy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harpers<PERSON><PERSON><PERSON>_<PERSON>y", "harvard_business_loy", "harvard_business_rev", "neon", "neon_alert", "neon_loy", "nl_shopping", "ohmymag_alert", "ohmymag_fr", "ohmymag_fr_loy", "prisma_connect", "prisma_connect_test", "programme_tv", "programme_tv_loy", "t2s_alert", "tele_loisirs", "tele_loisirs_loy", "teleloisirs_alert", "voici", "voici_alert", "voici_crm", "voici_loy"], "is_active": true, "flow_direction": "export", "flow_name": "datahub_090", "partner_name": "splio"}, {"pattern_name": "monitoring_splio_lop_monitoring", "pattern_format": "pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "prepare", "flow_type": "profiles", "scopes": ["not_set"], "is_active": true, "flow_direction": "export", "flow_name": "lop_blacklist", "partner_name": "splio"}, {"pattern_name": "contacts_016", "pattern_format": "scope_pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "temp", "flow_type": "profiles", "scopes": ["cac_alert", "caminteresse", "caminteresse_alert", "caminteresse_loy", "capital", "capital_alert", "capital_loy", "cuisineactuelle", "cuisineactuelle_crm", "cuisineactuelle_loy", "femme_actuelle", "femme_actuelle_loy", "femmeactuelle_alert", "femmeactuelle_crm", "gala", "gala_alert", "gala_loy", "gentside_alert", "gentside_fr", "gentside_fr_loy", "geo", "geo_alert", "geo_loy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harpers<PERSON><PERSON><PERSON>_<PERSON>y", "harvard_business_loy", "harvard_business_rev", "neon", "neon_alert", "neon_loy", "nl_shopping", "ohmymag_alert", "ohmymag_fr", "ohmymag_fr_loy", "pm_b2b_prisme", "prisma_connect", "prisma_connect_test", "programme_tv", "programme_tv_loy", "t2s_alert", "tele_loisirs", "tele_loisirs_loy", "teleloisirs_alert", "voici", "voici_alert", "voici_crm", "voici_loy"], "is_active": true, "flow_direction": "export", "flow_name": "datahub_016", "partner_name": "splio"}, {"pattern_name": "contacts_015", "pattern_format": "scope_pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "temp", "flow_type": "profiles", "scopes": ["cac_alert", "caminteresse", "caminteresse_alert", "caminteresse_loy", "capital", "capital_alert", "capital_loy", "capital_paid", "cuisineactuelle", "cuisineactuelle_crm", "cuisineactuelle_loy", "femme_actuelle", "femme_actuelle_loy", "femmeactuelle_alert", "femmeactuelle_crm", "gala", "gala_alert", "gala_loy", "gentside_alert", "gentside_fr", "gentside_fr_loy", "geo", "geo_alert", "geo_loy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harpers<PERSON><PERSON><PERSON>_<PERSON>y", "harvard_business_loy", "harvard_business_rev", "neon", "neon_alert", "neon_loy", "nl_shopping", "ohmymag_alert", "ohmymag_fr", "ohmymag_fr_loy", "pm_b2b_prisme", "prisma_connect", "prisma_connect_test", "programme_tv", "programme_tv_loy", "t2s_alert", "tele_loisirs", "tele_loisirs_loy", "teleloisirs_alert", "voici", "voici_alert", "voici_crm", "voici_loy"], "is_active": true, "flow_direction": "export", "flow_name": "datahub_015", "partner_name": "splio"}, {"pattern_name": "contacts_014", "pattern_format": "scope_pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "temp", "flow_type": "profiles", "scopes": ["caminteresse", "capital", "capital_paid", "cuisineactuelle", "femme_actuelle", "gala", "gentside_fr", "geo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harvard_business_rev", "neon", "ohmymag_fr", "programme_tv", "tele_loisirs", "voici"], "is_active": true, "flow_direction": "export", "flow_name": "datahub_014", "partner_name": "splio"}, {"pattern_name": "contacts_013", "pattern_format": "scope_pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-userhub", "prod": "pm-prod-userhub"}, "bq_dataset": "temp", "flow_type": "profiles", "scopes": ["caminteresse", "capital", "cuisineactuelle", "femme_actuelle", "gala", "gentside_fr", "geo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harvard_business_rev", "neon", "ohmymag_fr", "pm_b2b_prisme", "prisma_connect", "programme_tv", "tele_loisirs", "voici"], "is_active": true, "flow_direction": "export", "flow_name": "datahub_013", "partner_name": "splio"}, {"pattern_name": "contacts_012", "pattern_format": "scope_pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "temp", "flow_type": "profiles", "scopes": ["capital_paid"], "is_active": true, "flow_direction": "export", "flow_name": "datahub_012", "partner_name": "splio"}, {"pattern_name": "contacts_011", "pattern_format": "scope_pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "temp", "flow_type": "profiles", "scopes": ["caminteresse_loy", "capital_loy", "cuisineactuelle_loy", "femme_actuelle_loy", "gala_loy", "gentside_fr_loy", "geo_loy", "harpers<PERSON><PERSON><PERSON>_<PERSON>y", "harvard_business_loy", "neon_loy", "ohmymag_fr_loy", "programme_tv_loy", "tele_loisirs_loy", "voici_loy"], "is_active": true, "flow_direction": "export", "flow_name": "datahub_011", "partner_name": "splio"}, {"pattern_name": "contacts_010", "pattern_format": "scope_pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "temp", "flow_type": "profiles", "scopes": ["pm_b2b_prisme"], "is_active": true, "flow_direction": "export", "flow_name": "datahub_010", "partner_name": "splio"}, {"pattern_name": "contacts_009", "pattern_format": "scope_pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "temp", "flow_type": "profiles", "scopes": ["grossesse_femmeactuelle_crm", "maison_femmeactuelle_crm", "patisserie_cuisineactuelle_crm"], "is_active": true, "flow_direction": "export", "flow_name": "datahub_009", "partner_name": "splio"}, {"pattern_name": "contacts_008", "pattern_format": "scope_pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "temp", "flow_type": "profiles", "scopes": ["nl_shopping"], "is_active": true, "flow_direction": "export", "flow_name": "datahub_008", "partner_name": "splio"}, {"pattern_name": "events_108", "pattern_format": "scope_pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "temp", "flow_type": "events", "scopes": ["nl_shopping"], "is_active": true, "flow_direction": "export", "flow_name": "datahub_008", "partner_name": "splio"}, {"pattern_name": "contacts_007", "pattern_format": "scope_pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "temp", "flow_type": "profiles", "scopes": ["cac_alert", "caminteresse", "caminteresse_alert", "caminteresse_loy", "capital", "capital_alert", "capital_loy", "capital_paid", "cuisineactuelle", "cuisineactuelle_crm", "cuisineactuelle_loy", "femme_actuelle", "femme_actuelle_loy", "femmeactuelle_alert", "femmeactuelle_crm", "gala", "gala_alert", "gala_loy", "gentside_alert", "gentside_fr", "gentside_fr_loy", "geo", "geo_alert", "geo_loy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harpers<PERSON><PERSON><PERSON>_<PERSON>y", "harvard_business_loy", "harvard_business_rev", "neon", "neon_alert", "neon_loy", "nl_shopping", "ohmymag_alert", "ohmymag_fr", "ohmymag_fr_loy", "pm_b2b_prisme", "prisma_connect", "prisma_connect_test", "programme_tv", "programme_tv_loy", "t2s_alert", "tele_loisirs", "tele_loisirs_loy", "teleloisirs_alert", "voici", "voici_alert", "voici_crm", "voici_loy"], "is_active": true, "flow_direction": "export", "flow_name": "datahub_007", "partner_name": "splio"}, {"pattern_name": "contacts_003", "pattern_format": "scope_pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "temp", "flow_type": "profiles", "scopes": ["prisma_connect"], "is_active": true, "flow_direction": "export", "flow_name": "datahub_003", "partner_name": "splio"}, {"pattern_name": "contacts_002", "pattern_format": "scope_pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "temp", "flow_type": "profiles", "scopes": ["caminteresse", "capital", "cuisineactuelle", "cuisineactuelle_crm", "femme_actuelle", "femmeactuelle_crm", "gala", "gentside_fr", "geo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harvard_business_rev", "neon", "ohmymag_fr", "programme_tv", "tele_loisirs", "voici", "voici_crm"], "is_active": true, "flow_direction": "export", "flow_name": "datahub_002", "partner_name": "splio"}, {"pattern_name": "contacts_001", "pattern_format": "scope_pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "temp", "flow_type": "profiles", "scopes": ["cac_alert", "caminteresse_alert", "capital_alert", "femmeactuelle_alert", "gala_alert", "gentside_alert", "geo_alert", "neon_alert", "ohmymag_alert", "t2s_alert", "teleloisirs_alert", "voici_alert"], "is_active": true, "flow_direction": "export", "flow_name": "datahub_001", "partner_name": "splio"}, {"pattern_name": "partner_riviera", "pattern_format": "pattern_name_scope-202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "prepare", "flow_type": "profiles", "scopes": ["ca_minteresse_part", "capital_part", "cuisine_actuelle_part", "femme_actuelle_part", "gentside_part", "geo_part", "neon_part", "ohmymag_part", "serengo_part", "tele_2_semaines_part", "tele_loisirs_part", "voici_part"], "is_active": true, "flow_direction": "import", "flow_name": "import_snapshot_riviera", "partner_name": "riviera"}, {"pattern_name": "tracking_riviera_click", "pattern_format": "pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "import", "flow_type": "events", "scopes": ["not_set"], "is_active": true, "flow_direction": "import", "flow_name": "riviera_import_reactivity", "partner_name": "riviera"}, {"pattern_name": "tracking_riviera_open", "pattern_format": "pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "import", "flow_type": "events", "scopes": ["not_set"], "is_active": true, "flow_direction": "import", "flow_name": "riviera_import_reactivity", "partner_name": "riviera"}, {"pattern_name": "generic_incremental_unsub", "pattern_format": "pattern_name_scope_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "import", "flow_type": "profiles", "scopes": ["ca_minteresse_part", "capital_part", "cuisine_actuelle_part", "femme_actuelle_part", "gala_part", "gentside_part", "geo_part", "neon_part", "ohmymag_part", "serengo_part", "tele_2_semaines_part", "tele_loisirs_part", "voici_part"], "is_active": true, "flow_direction": "export", "flow_name": "partners_export", "partner_name": "riviera"}, {"pattern_name": "generic_incremental_riviera_sub", "pattern_format": "pattern_name_scope_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "import", "flow_type": "profiles", "scopes": ["ca_minteresse_part", "capital_part", "cuisine_actuelle_part", "femme_actuelle_part", "gentside_part", "geo_part", "neon_part", "ohmymag_part", "serengo_part", "tele_loisirs_part", "tele_2_semaines_part", "voici_part"], "is_active": true, "flow_direction": "export", "flow_name": "partners_export", "partner_name": "riviera"}, {"pattern_name": "generic_incremental_unsub", "pattern_format": "pattern_name_scope_resync_riviera_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "import", "flow_type": "profiles", "scopes": ["b2b_prisme_crm", "b2b_prisme_kpi_crm", "ca_minteresse_culture_g_nl", "ca_minteresse_loy", "ca_minteresse_nature_nl", "ca_minteresse_nl", "ca_minteresse_part", "ca_minteresse_sante_nl", "caminteresse_alert", "capital_alert", "capital_automobile_nl", "capital_carriere_nl", "capital_classe_eco_affaires_nl", "capital_event_nl", "capital_flash_nl", "capital_hebdo_eco_nl", "capital_loy", "capital_mag_release_alert", "capital_matinal_bourse_quotidienne_nl", "capital_part", "capital_paywall_declaration_revenus_nl", "capital_paywall_nl", "capital_paywall_retraite_nl", "capital_prospects_paid_nl", "capital_quotidienne_nl", "capital_quotidienne_soir_nl", "capital_retraite_nl", "capital_service_nl", "capital_vins_spiritueux_nl", "capital_votre_argent_nl", "capital_wallet_nl", "crm_prisma", "crm_prismashop", "cuisine_actuelle_actu_chaude_nl", "cuisine_actuelle_alert", "cuisine_actuelle_best_of_nl", "cuisine_actuelle_coaching_antigaspi_nl", "cuisine_actuelle_coaching_maison_nl", "cuisine_actuelle_coaching_patisserie_nl", "cuisine_actuelle_cool_nl", "cuisine_actuelle_diner_nl", "cuisine_actuelle_event_nl", "cuisine_actuelle_flash_nl", "cuisine_actuelle_hebdo_nl", "cuisine_actuelle_loy", "cuisine_actuelle_menu_semaine_nl", "cuisine_actuelle_part", "cuisine_actuelle_passion_apero_nl", "cuisine_actuelle_quotidienne_nl", "cuisine_actuelle_trop_trop_bon_nl", "femme_actuelle_alert", "femme_actuelle_amour_sexo_nl", "femme_actuelle_astro_nl", "femme_actuelle_beaute_addict_nl", "femme_actuelle_beaute_nl", "femme_actuelle_coaching_grossesse_nl", "femme_actuelle_coaching_maison_nl", "femme_actuelle_coaching_minceur_nl", "femme_actuelle_coaching_sante_nl", "femme_actuelle_cuisine_nl", "femme_actuelle_diapo_nl", "femme_actuelle_escapades_nl", "femme_actuelle_event_nl", "femme_actuelle_flash_nl", "femme_actuelle_hebdo_nl", "femme_actuelle_jeux_nl", "femme_actuelle_loy", "femme_actuelle_ma_vie_facile_nl", "femme_actuelle_minceur_nl", "femme_actuelle_mode_nl", "femme_actuelle_part", "femme_actuelle_pause_simone_nl", "femme_actuelle_people_nl", "femme_actuelle_quotidienne_nl", "femme_actuelle_sante_nl", "femme_actuelle_simone_nl", "femme_actuelle_video_hebdo_nl", "gala_alert", "gala_beaute_sans_filtre_nl", "gala_beauty_nl", "gala_bestof_nl", "gala_break_nl", "gala_carve_out_nl", "gala_event_nl", "gala_fashion_nl", "gala_lifestyle_nl", "gala_loy", "gala_mood_nl", "gala_politique_nl", "gala_quotidienne_nl", "gala_quotidienne_soir_nl", "gala_royaute_nl", "gala_video_nl", "gentside_9m15_nl", "gentside_alert", "gentside_buzz_nl", "gentside_combat_nl", "gentside_culture_pop_nl", "gentside_lifestyle_nl", "gentside_loy", "gentside_maxisciences_nl", "gentside_news_nl", "gentside_part", "gentside_sexy_nl", "gentside_voyage_nl", "geo_alert", "geo_communaute_photo_nl", "geo_dimanche_nl", "geo_environnement_nl", "geo_histoire_nl", "geo_loy", "geo_part", "geo_photo_du_jour_nl", "geo_politique_nl", "geo_quotidienne_nl", "harpers_bazaar_evenement_nl", "harpers_bazaar_loy", "harpers_bazaar_news_nl", "harpers_bazaar_society_nl", "harvard_business_review_evenement_nl", "harvard_business_review_les_plus_lus_nl", "harvard_business_review_loy", "hbr_nl", "neon_alert", "neon_loy", "neon_nl", "neon_part", "neon_pause_q_nl", "ohmymag_alert", "ohmymag_animaux_nl", "ohmymag_buzz_nl", "ohmymag_loy", "ohmymag_news_nl", "ohmymag_oh_my_food_nl", "ohmymag_oh_my_home_nl", "ohmymag_part", "ohmymag_people_nl", "prisma_connect_crm", "prisma_connect_crm_test", "prisma_connect_produit_nl", "prisma_connect_produit_nl_test", "prismashop_offres_prisma", "serengo_nl", "serengo_part", "tele_2_semaines_actu_nl", "tele_2_semaines_alert", "tele_2_se<PERSON><PERSON>_loy", "tele_2_semaines_part", "tele_2_semaines_quotidienne_nl", "tele_loisirs_alert", "tele_loisirs_buzz_nl", "tele_loisirs_cinema_nl", "tele_loisirs_fun_nl", "tele_loisirs_loy", "tele_loisirs_netflix_nl", "tele_loisirs_part", "tele_loisirs_quotidienne_nl", "tele_loisirs_selection_nl", "tele_loisirs_sport_nl", "tele_loisirs_tele_realite_nl", "tele_loisirs_video_nl", "voici_alert", "voici_event_nl", "voici_flash_nl", "voici_loy", "voici_part", "voici_pause_nl", "voici_quotidienne_nl", "voici_quotidienne_soir_nl", "voici_royaute_nl", "voici_stars_nl", "voici_style_nl", "voici_tele_realite_nl", "voici_video_nl"], "is_active": true, "flow_direction": "export", "flow_name": "partners_autocorrect", "partner_name": "riviera"}, {"pattern_name": "generic_incremental_sub", "pattern_format": "pattern_name_scope_resync_riviera_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "import", "flow_type": "profiles", "scopes": ["b2b_prisme_crm", "b2b_prisme_kpi_crm", "ca_minteresse_culture_g_nl", "ca_minteresse_loy", "ca_minteresse_nature_nl", "ca_minteresse_nl", "ca_minteresse_part", "ca_minteresse_sante_nl", "caminteresse_alert", "capital_alert", "capital_automobile_nl", "capital_carriere_nl", "capital_classe_eco_affaires_nl", "capital_event_nl", "capital_flash_nl", "capital_hebdo_eco_nl", "capital_loy", "capital_mag_release_alert", "capital_matinal_bourse_quotidienne_nl", "capital_part", "capital_paywall_declaration_revenus_nl", "capital_paywall_nl", "capital_paywall_retraite_nl", "capital_prospects_paid_nl", "capital_quotidienne_nl", "capital_quotidienne_soir_nl", "capital_retraite_nl", "capital_service_nl", "capital_vins_spiritueux_nl", "capital_votre_argent_nl", "capital_wallet_nl", "crm_prisma", "crm_prismashop", "cuisine_actuelle_actu_chaude_nl", "cuisine_actuelle_alert", "cuisine_actuelle_best_of_nl", "cuisine_actuelle_coaching_antigaspi_nl", "cuisine_actuelle_coaching_maison_nl", "cuisine_actuelle_coaching_patisserie_nl", "cuisine_actuelle_cool_nl", "cuisine_actuelle_diner_nl", "cuisine_actuelle_event_nl", "cuisine_actuelle_flash_nl", "cuisine_actuelle_hebdo_nl", "cuisine_actuelle_loy", "cuisine_actuelle_menu_semaine_nl", "cuisine_actuelle_part", "cuisine_actuelle_passion_apero_nl", "cuisine_actuelle_quotidienne_nl", "cuisine_actuelle_trop_trop_bon_nl", "femme_actuelle_alert", "femme_actuelle_amour_sexo_nl", "femme_actuelle_astro_nl", "femme_actuelle_beaute_addict_nl", "femme_actuelle_beaute_nl", "femme_actuelle_coaching_grossesse_nl", "femme_actuelle_coaching_maison_nl", "femme_actuelle_coaching_minceur_nl", "femme_actuelle_coaching_sante_nl", "femme_actuelle_cuisine_nl", "femme_actuelle_diapo_nl", "femme_actuelle_escapades_nl", "femme_actuelle_event_nl", "femme_actuelle_flash_nl", "femme_actuelle_hebdo_nl", "femme_actuelle_jeux_nl", "femme_actuelle_loy", "femme_actuelle_ma_vie_facile_nl", "femme_actuelle_minceur_nl", "femme_actuelle_mode_nl", "femme_actuelle_part", "femme_actuelle_pause_simone_nl", "femme_actuelle_people_nl", "femme_actuelle_quotidienne_nl", "femme_actuelle_sante_nl", "femme_actuelle_simone_nl", "femme_actuelle_video_hebdo_nl", "gala_alert", "gala_beaute_sans_filtre_nl", "gala_beauty_nl", "gala_bestof_nl", "gala_break_nl", "gala_carve_out_nl", "gala_event_nl", "gala_fashion_nl", "gala_lifestyle_nl", "gala_loy", "gala_mood_nl", "gala_politique_nl", "gala_quotidienne_nl", "gala_quotidienne_soir_nl", "gala_royaute_nl", "gala_video_nl", "gentside_9m15_nl", "gentside_alert", "gentside_buzz_nl", "gentside_combat_nl", "gentside_culture_pop_nl", "gentside_lifestyle_nl", "gentside_loy", "gentside_maxisciences_nl", "gentside_news_nl", "gentside_part", "gentside_sexy_nl", "gentside_voyage_nl", "geo_alert", "geo_communaute_photo_nl", "geo_dimanche_nl", "geo_environnement_nl", "geo_histoire_nl", "geo_loy", "geo_part", "geo_photo_du_jour_nl", "geo_politique_nl", "geo_quotidienne_nl", "harpers_bazaar_evenement_nl", "harpers_bazaar_loy", "harpers_bazaar_news_nl", "harpers_bazaar_society_nl", "harvard_business_review_evenement_nl", "harvard_business_review_les_plus_lus_nl", "harvard_business_review_loy", "hbr_nl", "neon_alert", "neon_loy", "neon_nl", "neon_part", "neon_pause_q_nl", "ohmymag_alert", "ohmymag_animaux_nl", "ohmymag_buzz_nl", "ohmymag_loy", "ohmymag_news_nl", "ohmymag_oh_my_food_nl", "ohmymag_oh_my_home_nl", "ohmymag_part", "ohmymag_people_nl", "prisma_connect_crm", "prisma_connect_crm_test", "prisma_connect_produit_nl", "prisma_connect_produit_nl_test", "prismashop_offres_prisma", "serengo_nl", "serengo_part", "tele_2_semaines_actu_nl", "tele_2_semaines_alert", "tele_2_se<PERSON><PERSON>_loy", "tele_2_semaines_part", "tele_2_semaines_quotidienne_nl", "tele_loisirs_alert", "tele_loisirs_buzz_nl", "tele_loisirs_cinema_nl", "tele_loisirs_fun_nl", "tele_loisirs_loy", "tele_loisirs_netflix_nl", "tele_loisirs_part", "tele_loisirs_quotidienne_nl", "tele_loisirs_selection_nl", "tele_loisirs_sport_nl", "tele_loisirs_tele_realite_nl", "tele_loisirs_video_nl", "voici_alert", "voici_event_nl", "voici_flash_nl", "voici_loy", "voici_part", "voici_pause_nl", "voici_quotidienne_nl", "voici_quotidienne_soir_nl", "voici_royaute_nl", "voici_stars_nl", "voici_style_nl", "voici_tele_realite_nl", "voici_video_nl"], "is_active": true, "flow_direction": "export", "flow_name": "partners_autocorrect", "partner_name": "riviera"}, {"pattern_name": "gdpr_purge_riviera", "pattern_format": "pattern_name", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "export_partner", "flow_type": "profiles", "scopes": ["not_set"], "is_active": true, "flow_direction": "export", "flow_name": "partner_riviera_export_purge", "partner_name": "riviera"}, {"pattern_name": "partner_webrivage", "pattern_format": "pattern_name_scope_sub-202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "prepare", "flow_type": "profiles", "scopes": ["capital_sub", "cuisine_actuelle_sub", "femme_actuelle_sub", "gentside_sub", "geo_sub", "ohmymag_sub", "serengo_sub", "tele_2_semaines_sub", "tele_loisirs_sub", "voici_sub"], "is_active": true, "flow_direction": "import", "flow_name": "import_snapshot_webrivage_sub", "partner_name": "webrivage"}, {"pattern_name": "partner_webrivage", "pattern_format": "pattern_name_scope_unsub-202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "prepare", "flow_type": "profiles", "scopes": ["capital_unsub", "cuisine_actuelle_unsub", "femme_actuelle_unsub", "gentside_unsub", "geo_sub", "ohmymag_unsub", "serengo_unsub", "tele_2_semaines_unsub", "tele_loisirs_unsub", "voici_unsub"], "is_active": true, "flow_direction": "import", "flow_name": "import_snapshot_webrivage_unsub", "partner_name": "webrivage"}, {"pattern_name": "partner_log_webrivage_sub", "pattern_format": "pattern_name_scope-202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "import", "flow_type": "profiles", "scopes": ["capital", "cuisine_actuelle", "femme_actuelle", "gentside", "geo", "ohmymag", "serengo", "tele_2_semaines", "tele_loisirs", "voici"], "is_active": true, "flow_direction": "import", "flow_name": "import_snapshot_log_webrivage", "partner_name": "webrivage"}, {"pattern_name": "partner_log_rattrapage_webrivage_sub", "pattern_format": "pattern_name_scope-202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "import", "flow_type": "profiles", "scopes": ["capital", "cuisine_actuelle", "femme_actuelle", "gentside", "geo", "ohmymag", "serengo", "tele_2_semaines", "tele_loisirs", "voici"], "is_active": true, "flow_direction": "import", "flow_name": "import_snapshot_log_rattrapage_webrivage", "partner_name": "webrivage"}, {"pattern_name": "tracking_webrivage", "pattern_format": "pattern_name_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "import", "flow_type": "events", "scopes": ["not_set"], "is_active": true, "flow_direction": "import", "flow_name": "partner_webrivage_import_reactivity", "partner_name": "webrivage"}, {"pattern_name": "generic_incremental_unsub", "pattern_format": "pattern_name_scope_resync_webrivage_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "import", "flow_type": "profiles", "scopes": ["b2b_prisme_crm", "b2b_prisme_kpi_crm", "ca_minteresse_culture_g_nl", "ca_minteresse_loy", "ca_minteresse_nature_nl", "ca_minteresse_nl", "ca_minteresse_part", "ca_minteresse_sante_nl", "caminteresse_alert", "capital_alert", "capital_automobile_nl", "capital_carriere_nl", "capital_classe_eco_affaires_nl", "capital_event_nl", "capital_flash_nl", "capital_hebdo_eco_nl", "capital_loy", "capital_mag_release_alert", "capital_matinal_bourse_quotidienne_nl", "capital_part", "capital_paywall_declaration_revenus_nl", "capital_paywall_nl", "capital_paywall_retraite_nl", "capital_prospects_paid_nl", "capital_quotidienne_nl", "capital_quotidienne_soir_nl", "capital_retraite_nl", "capital_service_nl", "capital_vins_spiritueux_nl", "capital_votre_argent_nl", "capital_wallet_nl", "crm_prisma", "crm_prismashop", "cuisine_actuelle_actu_chaude_nl", "cuisine_actuelle_alert", "cuisine_actuelle_best_of_nl", "cuisine_actuelle_coaching_antigaspi_nl", "cuisine_actuelle_coaching_maison_nl", "cuisine_actuelle_coaching_patisserie_nl", "cuisine_actuelle_cool_nl", "cuisine_actuelle_diner_nl", "cuisine_actuelle_event_nl", "cuisine_actuelle_flash_nl", "cuisine_actuelle_hebdo_nl", "cuisine_actuelle_loy", "cuisine_actuelle_menu_semaine_nl", "cuisine_actuelle_part", "cuisine_actuelle_passion_apero_nl", "cuisine_actuelle_quotidienne_nl", "cuisine_actuelle_trop_trop_bon_nl", "femme_actuelle_alert", "femme_actuelle_amour_sexo_nl", "femme_actuelle_astro_nl", "femme_actuelle_beaute_addict_nl", "femme_actuelle_beaute_nl", "femme_actuelle_coaching_grossesse_nl", "femme_actuelle_coaching_maison_nl", "femme_actuelle_coaching_minceur_nl", "femme_actuelle_coaching_sante_nl", "femme_actuelle_cuisine_nl", "femme_actuelle_diapo_nl", "femme_actuelle_escapades_nl", "femme_actuelle_event_nl", "femme_actuelle_flash_nl", "femme_actuelle_hebdo_nl", "femme_actuelle_jeux_nl", "femme_actuelle_loy", "femme_actuelle_ma_vie_facile_nl", "femme_actuelle_minceur_nl", "femme_actuelle_mode_nl", "femme_actuelle_part", "femme_actuelle_pause_simone_nl", "femme_actuelle_people_nl", "femme_actuelle_quotidienne_nl", "femme_actuelle_sante_nl", "femme_actuelle_simone_nl", "femme_actuelle_video_hebdo_nl", "gala_alert", "gala_beaute_sans_filtre_nl", "gala_beauty_nl", "gala_bestof_nl", "gala_break_nl", "gala_carve_out_nl", "gala_event_nl", "gala_fashion_nl", "gala_lifestyle_nl", "gala_loy", "gala_mood_nl", "gala_politique_nl", "gala_quotidienne_nl", "gala_quotidienne_soir_nl", "gala_royaute_nl", "gala_video_nl", "gentside_9m15_nl", "gentside_alert", "gentside_buzz_nl", "gentside_combat_nl", "gentside_culture_pop_nl", "gentside_lifestyle_nl", "gentside_loy", "gentside_maxisciences_nl", "gentside_news_nl", "gentside_part", "gentside_sexy_nl", "gentside_voyage_nl", "geo_alert", "geo_communaute_photo_nl", "geo_dimanche_nl", "geo_environnement_nl", "geo_histoire_nl", "geo_loy", "geo_part", "geo_photo_du_jour_nl", "geo_politique_nl", "geo_quotidienne_nl", "harpers_bazaar_evenement_nl", "harpers_bazaar_loy", "harpers_bazaar_news_nl", "harpers_bazaar_society_nl", "harvard_business_review_evenement_nl", "harvard_business_review_les_plus_lus_nl", "harvard_business_review_loy", "hbr_nl", "neon_alert", "neon_loy", "neon_nl", "neon_part", "neon_pause_q_nl", "ohmymag_alert", "ohmymag_animaux_nl", "ohmymag_buzz_nl", "ohmymag_loy", "ohmymag_news_nl", "ohmymag_oh_my_food_nl", "ohmymag_oh_my_home_nl", "ohmymag_part", "ohmymag_people_nl", "prisma_connect_crm", "prisma_connect_crm_test", "prisma_connect_produit_nl", "prisma_connect_produit_nl_test", "prismashop_offres_prisma", "serengo_nl", "serengo_part", "tele_2_semaines_actu_nl", "tele_2_semaines_alert", "tele_2_se<PERSON><PERSON>_loy", "tele_2_semaines_part", "tele_2_semaines_quotidienne_nl", "tele_loisirs_alert", "tele_loisirs_buzz_nl", "tele_loisirs_cinema_nl", "tele_loisirs_fun_nl", "tele_loisirs_loy", "tele_loisirs_netflix_nl", "tele_loisirs_part", "tele_loisirs_quotidienne_nl", "tele_loisirs_selection_nl", "tele_loisirs_sport_nl", "tele_loisirs_tele_realite_nl", "tele_loisirs_video_nl", "voici_alert", "voici_event_nl", "voici_flash_nl", "voici_loy", "voici_part", "voici_pause_nl", "voici_quotidienne_nl", "voici_quotidienne_soir_nl", "voici_royaute_nl", "voici_stars_nl", "voici_style_nl", "voici_tele_realite_nl", "voici_video_nl"], "is_active": true, "flow_direction": "export", "flow_name": "export_consents_unsub_webrivage", "partner_name": "webrivage"}, {"pattern_name": "generic_incremental_sub", "pattern_format": "pattern_name_scope_resync_webrivage_202", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "import", "flow_type": "profiles", "scopes": ["b2b_prisme_crm", "b2b_prisme_kpi_crm", "ca_minteresse_culture_g_nl", "ca_minteresse_loy", "ca_minteresse_nature_nl", "ca_minteresse_nl", "ca_minteresse_part", "ca_minteresse_sante_nl", "caminteresse_alert", "capital_alert", "capital_automobile_nl", "capital_carriere_nl", "capital_classe_eco_affaires_nl", "capital_event_nl", "capital_flash_nl", "capital_hebdo_eco_nl", "capital_loy", "capital_mag_release_alert", "capital_matinal_bourse_quotidienne_nl", "capital_part", "capital_paywall_declaration_revenus_nl", "capital_paywall_nl", "capital_paywall_retraite_nl", "capital_prospects_paid_nl", "capital_quotidienne_nl", "capital_quotidienne_soir_nl", "capital_retraite_nl", "capital_service_nl", "capital_vins_spiritueux_nl", "capital_votre_argent_nl", "capital_wallet_nl", "crm_prisma", "crm_prismashop", "cuisine_actuelle_actu_chaude_nl", "cuisine_actuelle_alert", "cuisine_actuelle_best_of_nl", "cuisine_actuelle_coaching_antigaspi_nl", "cuisine_actuelle_coaching_maison_nl", "cuisine_actuelle_coaching_patisserie_nl", "cuisine_actuelle_cool_nl", "cuisine_actuelle_diner_nl", "cuisine_actuelle_event_nl", "cuisine_actuelle_flash_nl", "cuisine_actuelle_hebdo_nl", "cuisine_actuelle_loy", "cuisine_actuelle_menu_semaine_nl", "cuisine_actuelle_part", "cuisine_actuelle_passion_apero_nl", "cuisine_actuelle_quotidienne_nl", "cuisine_actuelle_trop_trop_bon_nl", "femme_actuelle_alert", "femme_actuelle_amour_sexo_nl", "femme_actuelle_astro_nl", "femme_actuelle_beaute_addict_nl", "femme_actuelle_beaute_nl", "femme_actuelle_coaching_grossesse_nl", "femme_actuelle_coaching_maison_nl", "femme_actuelle_coaching_minceur_nl", "femme_actuelle_coaching_sante_nl", "femme_actuelle_cuisine_nl", "femme_actuelle_diapo_nl", "femme_actuelle_escapades_nl", "femme_actuelle_event_nl", "femme_actuelle_flash_nl", "femme_actuelle_hebdo_nl", "femme_actuelle_jeux_nl", "femme_actuelle_loy", "femme_actuelle_ma_vie_facile_nl", "femme_actuelle_minceur_nl", "femme_actuelle_mode_nl", "femme_actuelle_part", "femme_actuelle_pause_simone_nl", "femme_actuelle_people_nl", "femme_actuelle_quotidienne_nl", "femme_actuelle_sante_nl", "femme_actuelle_simone_nl", "femme_actuelle_video_hebdo_nl", "gala_alert", "gala_beaute_sans_filtre_nl", "gala_beauty_nl", "gala_bestof_nl", "gala_break_nl", "gala_carve_out_nl", "gala_event_nl", "gala_fashion_nl", "gala_lifestyle_nl", "gala_loy", "gala_mood_nl", "gala_politique_nl", "gala_quotidienne_nl", "gala_quotidienne_soir_nl", "gala_royaute_nl", "gala_video_nl", "gentside_9m15_nl", "gentside_alert", "gentside_buzz_nl", "gentside_combat_nl", "gentside_culture_pop_nl", "gentside_lifestyle_nl", "gentside_loy", "gentside_maxisciences_nl", "gentside_news_nl", "gentside_part", "gentside_sexy_nl", "gentside_voyage_nl", "geo_alert", "geo_communaute_photo_nl", "geo_dimanche_nl", "geo_environnement_nl", "geo_histoire_nl", "geo_loy", "geo_part", "geo_photo_du_jour_nl", "geo_politique_nl", "geo_quotidienne_nl", "harpers_bazaar_evenement_nl", "harpers_bazaar_loy", "harpers_bazaar_news_nl", "harpers_bazaar_society_nl", "harvard_business_review_evenement_nl", "harvard_business_review_les_plus_lus_nl", "harvard_business_review_loy", "hbr_nl", "neon_alert", "neon_loy", "neon_nl", "neon_part", "neon_pause_q_nl", "ohmymag_alert", "ohmymag_animaux_nl", "ohmymag_buzz_nl", "ohmymag_loy", "ohmymag_news_nl", "ohmymag_oh_my_food_nl", "ohmymag_oh_my_home_nl", "ohmymag_part", "ohmymag_people_nl", "prisma_connect_crm", "prisma_connect_crm_test", "prisma_connect_produit_nl", "prisma_connect_produit_nl_test", "prismashop_offres_prisma", "serengo_nl", "serengo_part", "tele_2_semaines_actu_nl", "tele_2_semaines_alert", "tele_2_se<PERSON><PERSON>_loy", "tele_2_semaines_part", "tele_2_semaines_quotidienne_nl", "tele_loisirs_alert", "tele_loisirs_buzz_nl", "tele_loisirs_cinema_nl", "tele_loisirs_fun_nl", "tele_loisirs_loy", "tele_loisirs_netflix_nl", "tele_loisirs_part", "tele_loisirs_quotidienne_nl", "tele_loisirs_selection_nl", "tele_loisirs_sport_nl", "tele_loisirs_tele_realite_nl", "tele_loisirs_video_nl", "voici_alert", "voici_event_nl", "voici_flash_nl", "voici_loy", "voici_part", "voici_pause_nl", "voici_quotidienne_nl", "voici_quotidienne_soir_nl", "voici_royaute_nl", "voici_stars_nl", "voici_style_nl", "voici_tele_realite_nl", "voici_video_nl"], "is_active": true, "flow_direction": "export", "flow_name": "export_consents_sub_webrivage", "partner_name": "webrivage"}, {"pattern_name": "gdpr_purge_webrivage", "pattern_format": "pattern_name", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "export_partner", "flow_type": "profiles", "scopes": ["not_set"], "is_active": true, "flow_direction": "export", "flow_name": "export_purge_webrivage", "partner_name": "webrivage"}, {"pattern_name": "export_welcome", "pattern_format": "pattern_name_scope", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "export_partner", "flow_type": "profiles", "scopes": ["CAC", "CAP", "CMI", "FAC", "GAL", "GEO", "GNT", "NEO", "OMM", "T2S", "TVL", "VCI"], "is_active": true, "flow_direction": "export", "flow_name": "export_welcome_clickers", "partner_name": "welcome_media"}, {"pattern_name": "export_welcome_non_clickers", "pattern_format": "pattern_name_scope", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "export_partner", "flow_type": "profiles", "scopes": ["CAC", "CAP", "CMI", "FAC", "GAL", "GEO", "GNT", "NEO", "OMM", "T2S", "TVL", "VCI"], "is_active": true, "flow_direction": "export", "flow_name": "export_welcome_non_clickers", "partner_name": "welcome_media"}, {"pattern_name": "export_welcome_non_ios_non_clickers", "pattern_format": "pattern_name_scope", "bq_project_search": {"preprod": "pm-preprod-matrix", "prod": "pm-prod-matrix"}, "bq_dataset": "export_partner", "flow_type": "profiles", "scopes": ["CAC", "CAP", "CMI", "FAC", "GAL", "GEO", "GNT", "NEO", "OMM", "T2S", "TVL", "VCI"], "is_active": true, "flow_direction": "export", "flow_name": "export_welcome_non_ios_non_clickers", "partner_name": "welcome_media"}], "interval": "7 DAY", "price_per_terra": 7.81}