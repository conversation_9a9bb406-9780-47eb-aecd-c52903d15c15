{"global": {"is_first_run": true, "expiration_interval": 7}, "domains": [{"email": {"is_active": true, "socio_demo": {"is_available": true, "age": {"is_available": true, "column_name": "info.birthdate"}, "gender": {"is_available": true, "column_name": "info.gender"}, "zipcode": {"is_available": true, "column_name": "info.zipcode"}}, "profile_master_id": {"name": "email_profile_master_id", "type": "INTEGER"}, "profile_secondary_id": [{"name": "email_sha256", "alias": "email_sha256"}], "profile_table_schema": "pm-prod-matrix.refined_data.profile_email", "domain_order": 0, "linked_domain": {"pmc": {"profile_link_id": "email_sha256"}, "business_abonnement": {"profile_link_id": "email_sha256"}, "emb": {"profile_link_id": "email_sha256"}}}, "pmc": {"is_active": true, "socio_demo": {"is_available": true, "age": {"is_available": true, "column_name": "info.birthdate"}, "gender": {"is_available": true, "column_name": "info.gender"}, "zipcode": {"is_available": true, "column_name": "info.zipcode"}}, "profile_master_id": {"name": "pmc_profile_master_id", "type": "INTEGER"}, "profile_secondary_id": [{"name": "email_sha256", "alias": "email_sha256"}, {"name": "uuid", "alias": "pmc_uuid"}], "profile_table_schema": "pm-prod-matrix.refined_data.profile_pmc", "domain_order": 1, "linked_domain": {"batch": {"profile_link_id": "pmc_uuid"}, "google_analytics": {"profile_link_id": "pmc_profile_master_id"}}}, "batch": {"is_active": true, "socio_demo": {"is_available": false, "age": {"is_available": false, "column_name": null}, "gender": {"is_available": false, "column_name": null}, "zipcode": {"is_available": false, "column_name": null}}, "profile_master_id": {"name": "install_id", "type": "STRING"}, "profile_secondary_id": [{"name": "custom_user_id", "alias": "pmc_uuid"}], "profile_table_schema": "pm-prod-matrix.refined_data.batch_userbase", "domain_order": 5, "linked_domain": {}}, "business_abonnement": {"is_active": true, "socio_demo": {"is_available": true, "age": {"is_available": true, "column_name": "info.birthdate"}, "gender": {"is_available": true, "column_name": "info.gender"}, "zipcode": {"is_available": true, "column_name": "info.zipcode"}}, "profile_master_id": {"name": "customer_id", "type": "STRING"}, "profile_secondary_id": [{"name": "email_sha256", "alias": "email_sha256"}], "profile_table_schema": "pm-prod-matrix.refined_data.profile_client_abo", "domain_order": 2, "linked_domain": {}}, "google_analytics": {"is_active": true, "socio_demo": {"is_available": false, "age": {"is_available": false, "column_name": null}, "gender": {"is_available": false, "column_name": null}, "zipcode": {"is_available": false, "column_name": null}}, "profile_master_id": {"name": "user_pseudo_id", "type": "STRING"}, "profile_secondary_id": [{"name": "pmc_profile_master_id", "alias": "pmc_profile_master_id"}], "profile_table_schema": "pm-prod-ga4.refined_data.google_analytics_profile", "domain_order": 3, "linked_domain": {}}, "emb": {"is_active": true, "socio_demo": {"is_available": true, "age": {"is_available": true, "column_name": "info.birthdate"}, "gender": {"is_available": true, "column_name": "info.gender"}, "zipcode": {"is_available": false, "column_name": null}}, "profile_master_id": {"name": "email_sha256", "type": "STRING"}, "profile_secondary_id": [{"name": "email_sha256", "alias": "email_sha256_sec"}], "profile_table_schema": "pm-prod-matrix.refined_data.profile_emb_socio_demo", "domain_order": 4, "linked_domain": {}}}]}