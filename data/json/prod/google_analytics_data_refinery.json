[{"is_full": false, "layer_name": "refined_data", "process_name": "refine__ga4_events", "long_session_duration": 21600, "start_date": "2024-06-01", "end_date": "2024-06-01", "users": {"is_full": true, "interval": "3 DAY", "start_date": "2024-06-01", "end_date": "2025-12-31"}, "ga_property": [{"property_id": 151726054, "brand_trigram": "TEL", "country": "ALL", "platform": "APP", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 153776196, "brand_trigram": "CAC", "country": "ALL", "platform": "APP", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 152813298, "brand_trigram": "CAP", "country": "ALL", "platform": "APP", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 297528309, "brand_trigram": "GEO", "country": "ALL", "platform": "APP", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 361641579, "brand_trigram": "HBZ", "country": "ALL", "platform": "APP", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 416914473, "brand_trigram": "FAC", "country": "ALL", "platform": "APP", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 416980564, "brand_trigram": "CTV", "country": "ALL", "platform": "APP", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 417001117, "brand_trigram": "T2S", "country": "ALL", "platform": "APP", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 214721287, "brand_trigram": "PPR", "country": "ALL", "platform": "APP", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 152913709, "brand_trigram": "CAM", "country": "ALL", "platform": "APP", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 434522896, "brand_trigram": "VOI", "country": "ALL", "platform": "APP", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 256577148, "brand_trigram": "CTV", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 257568745, "brand_trigram": "CAC", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 257618002, "brand_trigram": "TEL", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 257686835, "brand_trigram": "T2S", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 257700560, "brand_trigram": "FAC", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 257703265, "brand_trigram": "VOI", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 258022174, "brand_trigram": "CAP", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 369362384, "brand_trigram": "CAM", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 369380948, "brand_trigram": "GEO", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 369390726, "brand_trigram": "HBR", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 369408023, "brand_trigram": "NEO", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 417407679, "brand_trigram": "HBZ", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 418425576, "brand_trigram": "GEN", "country": "UK", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 418430213, "brand_trigram": "GEN", "country": "DE", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 418435567, "brand_trigram": "OMM", "country": "FR", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 418447767, "brand_trigram": "OMM", "country": "DE", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 418448675, "brand_trigram": "GEN", "country": "FR", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 418454933, "brand_trigram": "GEN", "country": "ALL", "platform": "WEB", "section": "MAXISCIENCE", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 418457467, "brand_trigram": "OMM", "country": "UK", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 418466550, "brand_trigram": "GEN", "country": "ALL", "platform": "WEB", "section": "TRIP", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 427728204, "brand_trigram": "PPR", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 317217396, "brand_trigram": "PRS", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 449595091, "brand_trigram": "GMI", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 449588124, "brand_trigram": "MMA", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2024-11-01", "2024-12-18"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 307737207, "brand_trigram": "CAZ", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2025-01-01", "2025-02-11"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 323529514, "brand_trigram": "PAS", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2025-01-01", "2025-02-11"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 314063157, "brand_trigram": "FBI", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2025-01-01", "2025-02-11"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 321828612, "brand_trigram": "DEC", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2025-01-01", "2025-02-11"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 447543626, "brand_trigram": "TUR", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2025-01-13", "2025-02-14"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 321847000, "brand_trigram": "MET", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2025-01-01", "2025-02-11"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 333678862, "brand_trigram": "CTM", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2025-06-10", "2025-06-20"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 460690643, "brand_trigram": "TGL", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2025-06-10", "2025-06-20"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 460689135, "brand_trigram": "IDE", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2025-06-10", "2025-06-20"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}, {"property_id": 467509456, "brand_trigram": "MAG", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2025-01-01", "2025-02-11"]}, "incremental_mode": {"is_on": true, "day_interval": 3}, "table_prefix": ["events_", "events_intraday_"]}]}, {"layer_name": "shared_data", "process_name": "share__ga4_live_events", "teams": [{"team_name": "itbi", "tasks": [{"airflow_task_id": "start__share", "airflow_task_description": "Start share process !", "airflow_task_operator": "EmptyOperator", "airflow_task_trigger_rule": "all_success", "airflow_task_dependencies": []}, {"airflow_task_id": "is_it_22h_UTC", "airflow_task_description": "Check if time condition is met or not ? ", "airflow_task_operator": "BranchPythonOperator", "airflow_task_trigger_rule": "all_success", "python_callable": "check_time", "true_task_id": "truncate__table", "false_task_id": "skip__truncate_table", "airflow_task_dependencies": ["start__share"]}, {"airflow_task_id": "truncate__table", "airflow_task_description": "Trun<PERSON> shared to table at day beginning.", "airflow_task_operator": "BigQueryOperator", "airflow_task_trigger_rule": "all_success", "source_bq_project": {"preprod": {"ga4": "pm-preprod-ga4"}, "prod": {"ga4": "pm-prod-ga4"}}, "destination_bq_dataset": "share_itbi", "sql_file_path": "itbi/truncate__table.sql", "airflow_task_dependencies": ["is_it_22h_UTC"]}, {"airflow_task_id": "skip__truncate_table", "airflow_task_description": "<PERSON>p truncate table during the day.", "airflow_task_operator": "EmptyOperator", "airflow_task_trigger_rule": "all_success", "airflow_task_dependencies": ["is_it_22h_UTC"]}, {"airflow_task_id": "create__intermediate_table", "airflow_task_description": "Create intermediate tables per property to contain live GA4 events generated on the last 30 minutes.", "airflow_task_operator": "BigQueryOperator", "airflow_task_trigger_rule": "one_success", "source_bq_project": {"preprod": {"ga4": "pm-preprod-ga4"}, "prod": {"ga4": "pm-prod-ga4"}}, "sql_file_path": "itbi/create__intermediate_table.sql", "airflow_task_dependencies": ["skip__truncate_table", "truncate__table"]}, {"airflow_task_id": "share__ga4_events_live", "airflow_task_description": "Share GA4 events to IT-BI Team.", "airflow_task_operator": "BigQueryOperator", "airflow_task_trigger_rule": "all_success", "source_bq_project": {"preprod": {"ga4": "pm-preprod-ga4"}, "prod": {"ga4": "pm-prod-ga4"}}, "destination_bq_dataset": "share_itbi", "sql_file_path": "itbi/share__ga4_events_live_to_itbi.sql", "airflow_task_dependencies": ["create__intermediate_table"]}, {"airflow_task_id": "end__share", "airflow_task_description": "End share process !", "airflow_task_operator": "EmptyOperator", "airflow_task_trigger_rule": "all_success", "airflow_task_dependencies": ["share__ga4_events_live"]}]}], "ga_property": [{"property_id": 151726054, "brand_trigram": "TEL", "country": "ALL", "platform": "APP", "section": "ALL"}, {"property_id": 153776196, "brand_trigram": "CAC", "country": "ALL", "platform": "APP", "section": "ALL"}, {"property_id": 152813298, "brand_trigram": "CAP", "country": "ALL", "platform": "APP", "section": "ALL"}, {"property_id": 297528309, "brand_trigram": "GEO", "country": "ALL", "platform": "APP", "section": "ALL"}, {"property_id": 361641579, "brand_trigram": "HBZ", "country": "ALL", "platform": "APP", "section": "ALL"}, {"property_id": 416914473, "brand_trigram": "FAC", "country": "ALL", "platform": "APP", "section": "ALL"}, {"property_id": 416980564, "brand_trigram": "CTV", "country": "ALL", "platform": "APP", "section": "ALL"}, {"property_id": 417001117, "brand_trigram": "T2S", "country": "ALL", "platform": "APP", "section": "ALL"}, {"property_id": 214721287, "brand_trigram": "PPR", "country": "ALL", "platform": "APP", "section": "ALL"}, {"property_id": 152913709, "brand_trigram": "CAM", "country": "ALL", "platform": "APP", "section": "ALL"}, {"property_id": 434522896, "brand_trigram": "VOI", "country": "ALL", "platform": "APP", "section": "ALL"}, {"property_id": 256577148, "brand_trigram": "CTV", "country": "ALL", "platform": "WEB", "section": "ALL"}, {"property_id": 257568745, "brand_trigram": "CAC", "country": "ALL", "platform": "WEB", "section": "ALL"}, {"property_id": 257618002, "brand_trigram": "TEL", "country": "ALL", "platform": "WEB", "section": "ALL"}, {"property_id": 257686835, "brand_trigram": "T2S", "country": "ALL", "platform": "WEB", "section": "ALL"}, {"property_id": 257700560, "brand_trigram": "FAC", "country": "ALL", "platform": "WEB", "section": "ALL"}, {"property_id": 257703265, "brand_trigram": "VOI", "country": "ALL", "platform": "WEB", "section": "ALL"}, {"property_id": 258022174, "brand_trigram": "CAP", "country": "ALL", "platform": "WEB", "section": "ALL"}, {"property_id": 369362384, "brand_trigram": "CAM", "country": "ALL", "platform": "WEB", "section": "ALL"}, {"property_id": 369380948, "brand_trigram": "GEO", "country": "ALL", "platform": "WEB", "section": "ALL"}, {"property_id": 369390726, "brand_trigram": "HBR", "country": "ALL", "platform": "WEB", "section": "ALL"}, {"property_id": 369408023, "brand_trigram": "NEO", "country": "ALL", "platform": "WEB", "section": "ALL"}, {"property_id": 417407679, "brand_trigram": "HBZ", "country": "ALL", "platform": "WEB", "section": "ALL"}, {"property_id": 418425576, "brand_trigram": "GEN", "country": "UK", "platform": "WEB", "section": "ALL"}, {"property_id": 418430213, "brand_trigram": "GEN", "country": "DE", "platform": "WEB", "section": "ALL"}, {"property_id": 418435567, "brand_trigram": "OMM", "country": "FR", "platform": "WEB", "section": "ALL"}, {"property_id": 418447767, "brand_trigram": "OMM", "country": "DE", "platform": "WEB", "section": "ALL"}, {"property_id": 418448675, "brand_trigram": "GEN", "country": "FR", "platform": "WEB", "section": "ALL"}, {"property_id": 418454933, "brand_trigram": "GEN", "country": "ALL", "platform": "WEB", "section": "MAXISCIENCE"}, {"property_id": 418457467, "brand_trigram": "OMM", "country": "UK", "platform": "WEB", "section": "ALL"}, {"property_id": 418466550, "brand_trigram": "GEN", "country": "ALL", "platform": "WEB", "section": "TRIP"}, {"property_id": 427728204, "brand_trigram": "PPR", "country": "ALL", "platform": "WEB", "section": "ALL"}, {"property_id": 317217396, "brand_trigram": "PRS", "country": "ALL", "platform": "WEB", "section": "ALL"}, {"property_id": 449595091, "brand_trigram": "GMI", "country": "ALL", "platform": "WEB", "section": "ALL"}, {"property_id": 333678862, "brand_trigram": "CTM", "country": "ALL", "platform": "WEB", "section": "ALL"}, {"property_id": 460690643, "brand_trigram": "TGL", "country": "ALL", "platform": "WEB", "section": "ALL"}, {"property_id": 460689135, "brand_trigram": "IDE", "country": "ALL", "platform": "WEB", "section": "ALL"}, {"property_id": 449588124, "brand_trigram": "MMA", "country": "ALL", "platform": "WEB", "section": "ALL"}]}, {"layer_name": "shared_data", "process_name": "share__ga4_events", "teams": [{"team_name": "orion", "tasks": [{"airflow_task_id": "start__share", "airflow_task_description": "Start share process !", "airflow_task_operator": "EmptyOperator", "airflow_task_trigger_rule": "all_success", "airflow_task_dependencies": []}, {"airflow_task_id": "wait_for__refined_ga4_events", "airflow_task_description": "Wait for refined GA4 data !", "airflow_task_operator": "SqlSensor", "airflow_task_trigger_rule": "all_success", "airflow_task_dependencies": ["start__share"], "airflow_task_conn_id": "airflow_db", "airflow_task_timeout": 60, "airflow_task_retries": 1, "airflow_task_poke_interval": 60, "airflow_task_mode": "poke", "airflow_task_soft_fail": true, "sql_query": "SELECT CASE WHEN state = 'success' THEN TRUE ELSE FALSE END FROM task_instance WHERE dag_id = 'refined_data__google_analytics_4' AND task_id = 'end_refine' AND DATE(start_date) = CURRENT_DATE"}, {"airflow_task_id": "is_share_task_already_run", "airflow_task_description": "Check if share task already run on the current day !", "airflow_task_operator": "SqlSensor", "airflow_task_trigger_rule": "all_success", "airflow_task_dependencies": ["wait_for__refined_ga4_events"], "airflow_task_conn_id": "airflow_db", "airflow_task_timeout": 10, "airflow_task_retries": 1, "airflow_task_poke_interval": 60, "airflow_task_mode": "poke", "airflow_task_soft_fail": true, "sql_query": "SELECT CASE WHEN COUNT(*) = 0 THEN TRUE ELSE FALSE END FROM task_instance WHERE dag_id = 'shared_data__google_analytics_4' AND task_id = 'share__ga4_events_to__orion.share__ga4_events' AND DATE(start_date) = CURRENT_DATE AND state = 'success'"}, {"airflow_task_id": "share__ga4_events", "airflow_task_description": "Share GA4 events to ORION Team.", "airflow_task_operator": "BigQueryOperator", "airflow_task_trigger_rule": "all_success", "source_bq_project": {"preprod": {"ga4": "pm-preprod-ga4"}, "prod": {"ga4": "pm-prod-ga4"}}, "is_full_load": {"WEB": false, "APP": false}, "start_date": {"WEB": "2024-09-04", "APP": "2024-06-01"}, "end_date": {"WEB": "2024-09-15", "APP": "2024-06-25"}, "destination_bq_dataset": "share_orion", "source_bq_table": {"WEB": "ga_events_WEB_*", "APP": "ga_events_APP_*"}, "destination_bq_table": "ga4_events", "sql_file_path": "orion/share__ga4_events_to_orion.sql", "airflow_task_dependencies": ["is_share_task_already_run"]}, {"airflow_task_id": "end__share", "airflow_task_description": "End share process !", "airflow_task_operator": "EmptyOperator", "airflow_task_trigger_rule": "none_failed", "airflow_task_dependencies": ["share__ga4_events"]}]}]}, {"layer_name": "shared_data", "process_name": "share__ga_kpi", "teams": [{"team_name": "marketing", "tasks": [{"airflow_task_id": "start__share", "airflow_task_description": "Start share process !", "airflow_task_operator": "EmptyOperator", "airflow_task_trigger_rule": "all_success", "airflow_task_dependencies": []}, {"airflow_task_id": "wait_for__refined_ga4_events", "airflow_task_description": "Wait for refined GA4 data !", "airflow_task_operator": "SqlSensor", "airflow_task_trigger_rule": "all_success", "airflow_task_dependencies": ["start__share"], "airflow_task_conn_id": "airflow_db", "airflow_task_timeout": 60, "airflow_task_retries": 1, "airflow_task_poke_interval": 60, "airflow_task_mode": "poke", "airflow_task_soft_fail": true, "sql_query": "SELECT CASE WHEN state = 'success' THEN TRUE ELSE FALSE END FROM task_instance WHERE dag_id = 'refined_data__google_analytics_4' AND task_id = 'end_refine' AND DATE(start_date) = CURRENT_DATE"}, {"airflow_task_id": "wait_for__refined_ga3_events", "airflow_task_description": "Wait for refined GA3 data !", "airflow_task_operator": "SqlSensor", "airflow_task_trigger_rule": "all_success", "airflow_task_dependencies": ["start__share"], "airflow_task_conn_id": "airflow_db", "airflow_task_timeout": 60, "airflow_task_retries": 1, "airflow_task_poke_interval": 60, "airflow_task_mode": "poke", "airflow_task_soft_fail": true, "sql_query": "SELECT CASE WHEN state = 'success' THEN TRUE ELSE FALSE END FROM task_instance WHERE dag_id = 'pmc_gan_navigation' AND task_id = 'refined_gan_navigation_all_visits' AND DATE(start_date) = CURRENT_DATE"}, {"airflow_task_id": "is_share_task_already_run", "airflow_task_description": "Check if share task already run on the current day !", "airflow_task_operator": "SqlSensor", "airflow_task_trigger_rule": "all_success", "airflow_task_dependencies": ["wait_for__refined_ga4_events", "wait_for__refined_ga3_events"], "airflow_task_conn_id": "airflow_db", "airflow_task_timeout": 10, "airflow_task_retries": 1, "airflow_task_poke_interval": 60, "airflow_task_mode": "poke", "airflow_task_soft_fail": true, "sql_query": "SELECT CASE WHEN COUNT(*) = 0 THEN TRUE ELSE FALSE END FROM task_instance WHERE dag_id = 'shared_data__google_analytics_4' AND task_id = 'share__ga_kpi_to__marketing.share__ga_kpi_per_channel_page_monthly' AND DATE(start_date) = CURRENT_DATE AND state = 'success'"}, {"airflow_task_id": "share__ga_kpi_per_channel_page_monthly", "airflow_task_description": "Share GA4 events to Marketing Team.", "airflow_task_operator": "BigQueryOperator", "airflow_task_trigger_rule": "all_success", "source_bq_project": {"preprod": {"4": "pm-preprod-ga4", "3": "pm-preprod-matrix"}, "prod": {"4": "pm-prod-ga4", "3": "pm-prod-matrix"}}, "source_bq_table": {"version": {"3": "gan_navigation_all_visits_*", "4": "ga_events_WEB_*"}}, "is_full_load": {"version": {"3": false, "4": false}}, "start_date": {"version": {"3": "2021-10-01", "4": "2024-06-03"}}, "end_date": {"version": {"3": "2024-06-02", "4": "2024-10-01"}}, "destination_bq_project": {"preprod": "pm-preprod-ga4", "prod": "pm-prod-ga4"}, "destination_bq_dataset": "share_marketing", "destination_bq_table": "ga_kpi_per_channel_page_monthly", "sql_file_path": "marketing/share__ga_kpi_per_channel_page_monthly.sql", "airflow_task_dependencies": ["is_share_task_already_run"]}, {"airflow_task_id": "end__share", "airflow_task_description": "End share process !", "airflow_task_operator": "EmptyOperator", "airflow_task_trigger_rule": "none_failed", "airflow_task_dependencies": ["share__ga_kpi_per_channel_page_monthly"]}]}, {"team_name": "itbi", "tasks": [{"airflow_task_id": "start__share", "airflow_task_description": "Start share process !", "airflow_task_operator": "EmptyOperator", "airflow_task_trigger_rule": "all_success", "airflow_task_dependencies": []}, {"airflow_task_id": "wait_for__refined_ga4_events", "airflow_task_description": "Wait for refined GA4 data !", "airflow_task_operator": "SqlSensor", "airflow_task_trigger_rule": "all_success", "airflow_task_dependencies": ["start__share"], "airflow_task_conn_id": "airflow_db", "airflow_task_timeout": 60, "airflow_task_retries": 1, "airflow_task_poke_interval": 60, "airflow_task_mode": "poke", "airflow_task_soft_fail": true, "sql_query": "SELECT CASE WHEN state = 'success' THEN TRUE ELSE FALSE END FROM task_instance WHERE dag_id = 'refined_data__google_analytics_4' AND task_id = 'end_refine' AND DATE(start_date) = CURRENT_DATE"}, {"airflow_task_id": "is_share_task_already_run", "airflow_task_description": "Check if share task already run on the current day !", "airflow_task_operator": "SqlSensor", "airflow_task_trigger_rule": "all_success", "airflow_task_dependencies": ["wait_for__refined_ga4_events"], "airflow_task_conn_id": "airflow_db", "airflow_task_timeout": 10, "airflow_task_retries": 1, "airflow_task_poke_interval": 60, "airflow_task_mode": "poke", "airflow_task_soft_fail": true, "sql_query": "SELECT CASE WHEN COUNT(*) = 0 THEN TRUE ELSE FALSE END FROM task_instance WHERE dag_id = 'shared_data__google_analytics_4' AND task_id = 'share__ga_kpi_to__itbi.share__ga4_prismashop_kpis_to_itbi' AND DATE(start_date) = CURRENT_DATE AND state = 'success'"}, {"airflow_task_id": "share__ga4_prismashop_kpis_to_itbi", "airflow_task_description": "Share GA4 prismashop Kpis to ITBI Team.", "airflow_task_operator": "BigQueryOperator", "airflow_task_trigger_rule": "all_success", "source_bq_project": {"preprod": {"4": "pm-preprod-ga4", "3": "pm-preprod-matrix"}, "prod": {"4": "pm-prod-ga4", "3": "pm-prod-matrix"}}, "source_bq_table": {"version": {"4": "ga_events_WEB_PRS_ALL_ALL"}}, "is_full_load": {"version": {"4": false}}, "start_date": {"version": {"4": "2024-06-03"}}, "end_date": {"version": {"4": "2024-07-20"}}, "destination_bq_project": {"preprod": "pm-preprod-ga4", "prod": "pm-prod-ga4"}, "destination_bq_dataset": "share_itbi", "destination_bq_table": "share_kpis_ga4_prismashop", "sql_file_path": "itbi/share__ga4_prismashop_kpis_to_itbi.sql", "airflow_task_dependencies": ["is_share_task_already_run"]}, {"airflow_task_id": "end__share", "airflow_task_description": "End share process !", "airflow_task_operator": "EmptyOperator", "airflow_task_trigger_rule": "none_failed", "airflow_task_dependencies": ["share__ga4_prismashop_kpis_to_itbi"]}]}]}]