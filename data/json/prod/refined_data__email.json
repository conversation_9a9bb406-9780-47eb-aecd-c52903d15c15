{"alert": {"full_export": "False", "interval": "7 DAY"}, "batch": {"campaign": {"brand_list": ["cac", "cam", "cap", "ctv", "fac", "gende", "geo", "gmi", "hbr", "hbz", "mas", "mma", "neo", "ommde", "ommfr", "ommuk", "tel", "voi", "t2s"], "full_export": "False", "interval": "4 DAY"}, "event": {"brand_list": ["cac", "cam", "cap", "ctv", "fac", "gende", "geo", "gmi", "hbr", "hbz", "mas", "mma", "neo", "ommde", "ommfr", "ommuk", "tel", "voi", "t2s"], "full_export": "False", "interval": "4 DAY"}, "userbase": {"brand_list": ["cac", "cam", "cap", "ctv", "fac", "gende", "geo", "gmi", "hbr", "hbz", "mas", "mma", "neo", "ommde", "ommfr", "ommuk", "tel", "voi", "t2s"], "full_export": "False", "interval": "4 DAY"}}, "click": {"full_export": false}, "coaching": {"full_export": "False", "interval": "2 DAY"}, "coaching_profile_lifecycle": {"interval": "2 DAY", "is_full": false}, "email": {"full_export": "False", "interval": "7 DAY"}, "everest_validity": {"daily": {"full_export": true, "interval": "1 DAY"}, "rollup": {"full_export": true, "interval": "1 DAY"}}, "fai_complaints_stats": {"end_date": "2024-04-11", "partial": false, "start_date": "2023-01-01", "time_interval": "2 DAY"}, "global": {"click": {"full_export": false, "interval": "7 DAY", "start_date": "2016-01-01", "end_date": "2025-12-31"}, "open": {"full_export": false, "interval": "7 DAY", "start_date": "2016-01-01", "end_date": "2025-12-31"}}, "mail_complaints_stats": {"end_date": "2024-04-11", "partial": false, "start_date": "2023-01-01", "time_interval": "2 DAY"}, "open": {"full_export": false}, "organic_acquisition": {"end_date": "2025-12-31", "is_full": false, "start_date": "2022-01-01", "time_interval": "2 DAY", "truncate_table": false}, "pandora": {"full_export": "False", "interval": "7 DAY"}, "partners": {"click": {"full_export": false, "interval": "7 DAY", "start_date": "2016-01-01", "end_date": "2024-11-30"}, "open": {"full_export": false, "interval": "7 DAY", "start_date": "2016-01-01", "end_date": "2024-11-30"}}, "prisma": {"click": {"full_export": false, "interval": "3 DAY", "start_date": "2016-01-01", "end_date": "2025-11-30"}, "open": {"full_export": false, "interval": "3 DAY", "start_date": "2016-01-01", "end_date": "2024-11-30"}}, "qualifio": {"interval": "2 DAY", "is_full": false}, "riviera": {"click": {"full_export": false, "interval": "7 DAY", "start_date": "2016-01-01", "end_date": "2024-11-30"}, "open": {"full_export": false, "interval": "7 DAY", "start_date": "2016-01-01", "end_date": "2024-11-30"}}, "sent": {"full_export": false, "interval": "7 DAY"}, "splio": {"campaign_coaching": {"full_export": "False", "interval": "2 DAY"}, "click": {"full_export": false, "interval": "7 DAY", "start_date": "2016-01-01", "end_date": "2025-12-31"}, "open": {"full_export": false, "interval": "7 DAY", "start_date": "2016-01-01", "end_date": "2025-12-31"}}, "splio_history": {"full_export": true, "interval": "90 DAY"}, "sub_unsub": {"is_full": false, "interval": "30 DAY"}, "sub_unsub_new": {"is_full": false, "interval": "30 DAY"}, "webrivage": {"click": {"full_export": false, "interval": "7 DAY", "start_date": "2016-01-01", "end_date": "2024-11-30"}, "open": {"full_export": false, "interval": "7 DAY", "start_date": "2016-01-01", "end_date": "2024-11-30"}}, "personalized_nl_email_event_sent": {"is_full": true, "time_interval": "2 DAY"}}