[{"is_full": false, "layer_name": "refined_data", "process_name": "refine__ga4_events", "long_session_duration": 21600, "start_date": "2024-06-01", "end_date": "2024-06-01", "users": {"is_full": true, "interval": "3 DAY", "start_date": "2024-06-01", "end_date": "2025-12-31"}, "ga_property": [{"property_id": 151726054, "brand_trigram": "TEL", "country": "ALL", "platform": "APP", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2023-01-01", "2023-01-02"]}, "incremental_mode": {"is_on": true, "day_interval": 2}, "table_prefix": "events_intraday_"}, {"property_id": 257568745, "brand_trigram": "CAC", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": false, "date_range": ["2023-01-01", "2023-01-02"]}, "incremental_mode": {"is_on": true, "day_interval": 2}, "table_prefix": "events_"}, {"property_id": 257618002, "brand_trigram": "TEL", "country": "ALL", "platform": "WEB", "section": "ALL", "partial_mode": {"is_on": true, "date_range": ["2023-12-14", "2023-12-16"]}, "incremental_mode": {"is_on": false, "day_interval": 2}, "table_prefix": "events_intraday_"}]}, {"layer_name": "shared_data", "process_name": "share__ga_kpi", "teams": [{"team_name": "itbi", "tasks": [{"airflow_task_id": "start__share", "airflow_task_description": "Start share process !", "airflow_task_operator": "EmptyOperator", "airflow_task_trigger_rule": "all_success", "airflow_task_dependencies": []}, {"airflow_task_id": "wait_for__refined_ga4_events", "airflow_task_description": "Wait for refined GA4 data !", "airflow_task_operator": "SqlSensor", "airflow_task_trigger_rule": "all_success", "airflow_task_dependencies": ["start__share"], "airflow_task_conn_id": "airflow_db", "airflow_task_timeout": 60, "airflow_task_retries": 1, "airflow_task_poke_interval": 60, "airflow_task_mode": "poke", "airflow_task_soft_fail": true, "sql_query": "SELECT CASE WHEN state = 'success' THEN TRUE ELSE FALSE END FROM task_instance WHERE dag_id = 'refined_data__google_analytics_4' AND task_id = 'end_refine' AND DATE(start_date) = CURRENT_DATE"}, {"airflow_task_id": "is_share_task_already_run", "airflow_task_description": "Check if share task already run on the current day !", "airflow_task_operator": "SqlSensor", "airflow_task_trigger_rule": "all_success", "airflow_task_dependencies": ["wait_for__refined_ga4_events"], "airflow_task_conn_id": "airflow_db", "airflow_task_timeout": 10, "airflow_task_retries": 1, "airflow_task_poke_interval": 60, "airflow_task_mode": "poke", "airflow_task_soft_fail": true, "sql_query": "SELECT CASE WHEN COUNT(*) = 0 THEN TRUE ELSE FALSE END FROM task_instance WHERE dag_id = 'shared_data__google_analytics_4' AND task_id = 'share__ga_kpi_to__itbi.share__ga4_prismashop_kpis_to_itbi' AND DATE(start_date) = CURRENT_DATE AND state = 'success'"}, {"airflow_task_id": "share__ga4_prismashop_kpis_to_itbi", "airflow_task_description": "Share GA4 prismashop Kpis to ITBI Team.", "airflow_task_operator": "BigQueryOperator", "airflow_task_trigger_rule": "all_success", "source_bq_project": {"preprod": {"4": "pm-preprod-ga4", "3": "pm-preprod-matrix"}, "prod": {"4": "pm-prod-ga4", "3": "pm-prod-matrix"}}, "source_bq_table": {"version": {"4": "ga_events_WEB_PRS_ALL_ALL"}}, "is_full_load": {"version": {"4": false}}, "start_date": {"version": {"4": "2024-06-03"}}, "end_date": {"version": {"4": "2024-07-20"}}, "destination_bq_project": {"preprod": "pm-preprod-ga4", "prod": "pm-prod-ga4"}, "destination_bq_dataset": "share_itbi", "destination_bq_table": "share_kpis_ga4_prismashop", "sql_file_path": "itbi/share__ga4_prismashop_kpis_to_itbi.sql", "airflow_task_dependencies": ["is_share_task_already_run"]}, {"airflow_task_id": "end__share", "airflow_task_description": "End share process !", "airflow_task_operator": "EmptyOperator", "airflow_task_trigger_rule": "none_failed", "airflow_task_dependencies": ["share__ga4_prismashop_kpis_to_itbi"]}]}]}]