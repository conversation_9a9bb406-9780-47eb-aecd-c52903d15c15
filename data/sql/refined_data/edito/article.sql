-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
DECLARE LOWER_BOUND DEFAULT  0;
DECLARE UPPER_BOUND DEFAULT  UNIX_MICROS("9999-12-31 23:59:59.999999+00");

DROP TABLE IF  EXISTS `{{ params.bq_project.mirror }}.refined_data.article`;
CREATE TABLE IF NOT EXISTS `{{ params.bq_project.mirror }}.refined_data.article`(
    article_id          STRING NOT NULL OPTIONS(description="article uuid. ref: {{ params.bq_project.mirror }}.store_one_article.article.id"),
    create_date         TIMESTAMP       OPTIONS(description="article creation date. ref: {{ params.bq_project.mirror }}.store_one_article.article.createdAt"),
    update_date         TIMESTAMP       OPTIONS(description="article last update date. ref: {{ params.bq_project.mirror }}.store_one_article.article.updatedAt"),
    publication_date    TIMESTAMP       OPTIONS(description="article publication date. ref: {{ params.bq_project.mirror }}.store_one_article.article.publishedAt"),
    edition_date        TIMESTAMP       OPTIONS(description="article edition date. ref: {{ params.bq_project.mirror }}.store_one_article.article.editedAt"),
    status              STRING          OPTIONS(description="article status as enum= frozen, draft, scheduled, published, deleted, ready. ref: {{ params.bq_project.mirror }}.store_one_article.article.status"),
    format              STRING          OPTIONS(description="article format as enum= RICH, SLIDESHOW, VIDEO, RECIPE. ref: {{ params.bq_project.mirror }}.store_one_article.article.format"),
    brand_trigram       STRING          OPTIONS(description="article brand trigram. ref: {{ params.bq_project.mirror }}.store_one_article.article.brandKeys"),
    article_author      ARRAY<STRING>   OPTIONS(description="article author and 1 article can be written by multiple author's. ref: {{ params.bq_project.mirror }}.store_one_article.author.name/.recipe.author"),
    source    STRUCT <
        id                 STRING    OPTIONS(description="id of source. ref : {{ params.bq_project.mirror }}.store_one_article.source.uuid "),
        title              STRING    OPTIONS(description="title of source. ref : {{ params.bq_project.mirror }}.store_one_article.source.title"),
        slug               STRING    OPTIONS(description="slug of source. ref : {{ params.bq_project.mirror }}.store_one_article.source.slug")
        >    OPTIONS(description="article source. ref : {{ params.bq_project.mirror }}.store_one_article.source"),
    is_pushed           BOOLEAN         OPTIONS(description="true/false. true=send notifications to users. ref: {{ params.bq_project.mirror }}.store_one_article.article.isPushed"),
    content             STRUCT<
        title               STRING          OPTIONS(description="artilce title content. ref: {{ params.bq_project.mirror }}.store_one_article.article.title"),
        head                STRING          OPTIONS(description="article chapô content. ref: {{ params.bq_project.mirror }}.store_one_article.article.lead#blocks#text"),
        body                STRING          OPTIONS(description="article body content. ref: {{ params.bq_project.mirror }}.store_one_article.article.body#blocks#text"),
        recipe              STRUCT<
            cook_time        INTEGER        OPTIONS(description="cook time in seconds. "),
            preparation_time INTEGER        OPTIONS(description="preparation time in seconds. "),
            waiting_time     INTEGER         OPTIONS(description="waiting time in seconds. "),
            total_time       INTEGER         OPTIONS(description="total time in seconds. "),
            instructions     STRING          OPTIONS(description="recipe instructions. "),
            advice           STRING          OPTIONS(description="some advices to do well the recipe. "),
            cost             STRING          OPTIONS(description="cost as enum = ['Bon marché', 'Moyen', 'Assez cher', 'Cher', 'Non spécifié', 'Abordable', 'Ne sais pas', 'null', '']"),
            difficulty       STRING          OPTIONS(description="recipe difficulty degree as enum = ['null', 'Très facile', 'Moyen', 'Facile', 'Difficile', 'Non spécifié', ' Confirmé']"),
            yield            INTEGER         OPTIONS(description="number of persons that can be served by the recipe. "),
            ingredients      STRING          OPTIONS(description="recipe ingredients. "),
            calorie_level    STRING          OPTIONS(description="recipe calorie level as enum = ['null', 'Elevé', 'Moyen', 'Faible', 'Non spécifié', 'Ne sais pas', 'Très élevé', '']. ")
        >                                   OPTIONS(description="recipe information when is_recipe=True"),
         url                 STRUCT<
            one                 STRING          OPTIONS(description="ONE URL as https://diapo.bone.prismamedia.com/article/list#/<brandkey>/article/edit/<article ID>/search. "||
                                                                    "ref: {{ params.bq_project.mirror }}.store_one_article.article"),
            `order`             ARRAY<INTEGER>  OPTIONS(description="URL order. ref: {{ params.bq_project.mirror }}.store_one_article.url.domain&path"),
            public              ARRAY<STRING>   OPTIONS(description="list of article public URL. ref: {{ params.bq_project.mirror }}.store_one_article.url.domain&path"),
            main_public         STRING          OPTIONS(description="main canonical URL. It corresponds to URL on the first position. ref: {{ params.bq_project.mirror }}.store_one_article.articleUrl#order/position ")
        >                                   OPTIONS(description="URL information"),
        ecommerce            ARRAY<STRUCT<
            id                  STRING      OPTIONS(description="ecommerce tag's id. ref: {{ params.bq_project.mirror }}.store_one_article.body.data.tagId"),
            url                 STRING      OPTIONS(description="url to which the ecommerce tag redirects. ref: {{ params.bq_project.mirror }}.store_one_article.body.data.url"),
            label               STRING      OPTIONS(description="text displayed in the ecommerce tag. ref: {{ params.bq_project.mirror }}.store_one_article.body.data.buttonLabel"),
            type                STRING      OPTIONS(description="ecommerce tag CTA type. ref: {{ params.bq_project.mirror }}.store_one_article.body.data.type")
            >
        >                               OPTIONS(description='list of ecommerce tag information'),
        tag                 ARRAY<STRUCT<
            id                  STRING      OPTIONS(description="id of source. ref : {{ params.bq_project.mirror }}.store_one_article.tag.uuid "),
            title               STRING      OPTIONS(description="article tags titles. ref: {{ params.bq_project.mirror }}.store_one_article.tag.title"),
            type                STRING      OPTIONS(description="article tags types as enum= [tag, people, ....]. ref: {{ params.bq_project.mirror }}.store_one_article.tag.type"),
            language            STRING      OPTIONS(description="article tags languages as enum= [de, fr, ....]. ref: {{ params.bq_project.mirror }}.store_one_article.tag.lang"),
            slug                STRING      OPTIONS(description="slug of source. ref : {{ params.bq_project.mirror }}.store_one_article.tag.slug")
            >
        >                               OPTIONS(description="list of tag information"),
        category            ARRAY<STRUCT<
            cat_1               STRING      OPTIONS(description="article first category title. ref: {{ params.bq_project.mirror }}.store_one_article.categroy.title"),
            id_1                STRING      OPTIONS(description="ID for article first category"),
            slug_1              STRING      OPTIONS(description="Slug for article first category"),
            cat_2               STRING      OPTIONS(description="article second category title. ref: {{ params.bq_project.mirror }}.store_one_article.categroy.title"),
            id_2                STRING      OPTIONS(description="ID for article second category"),
            slug_2              STRING      OPTIONS(description="Slug for article second category"),
            cat_3               STRING      OPTIONS(description="article third category title. ref: {{ params.bq_project.mirror }}.store_one_article.categroy.title"),
            id_3                STRING      OPTIONS(description="ID for article third category"),
            slug_3              STRING      OPTIONS(description="Slug for article third category"),
            cat_4               STRING      OPTIONS(description="article fourth category title. ref: {{ params.bq_project.mirror }}.store_one_article.categroy.title"),
            id_4                STRING      OPTIONS(description="ID for article fourth category"),
            slug_4              STRING      OPTIONS(description="Slug for article fourth category"),
            cat_5               STRING      OPTIONS(description="article fifth category title. ref: {{ params.bq_project.mirror }}.store_one_article.categroy.title"),
            id_5                STRING      OPTIONS(description="ID for article fifth category"),
            slug_5              STRING      OPTIONS(description="Slug for article fifth category")
            >
        >                               OPTIONS(description="list of category information"),
        qualifiers     ARRAY<STRUCT <
                                    id                 STRING    OPTIONS(description="id of qualifier. ref: {{ params.bq_project.mirror }}.store_one_article.qualifier.id "),
                                    title              STRING    OPTIONS(description="title of qualifier. ref: {{ params.bq_project.mirror }}.store_one_article.qualifier.title"),
                                    slug               STRING    OPTIONS(description="slug of qualifier. ref: {{ params.bq_project.mirror }}.store_one_article.qualifier.slug")
                                    >
            >   OPTIONS(description="list of article qualifiers : title, id and slug"),
        embed               ARRAY<STRUCT<
            type                STRING      OPTIONS(description="embed type as enum = ['article', 'image', 'video']. ref: {{ params.bq_project.mirror }}.store_one_article.article.medias.#iframely#meta#extension/type"),
            position            STRING      OPTIONS(description="enum = ['lead', 'body']"),
            url                 STRING      OPTIONS(description="embed content canonic URL. ref: {{ params.bq_project.mirror }}.store_one_article.article.medias.#iframely#meta#canonical"),
            source              STRING      OPTIONS(description="partner embed source as image/video sources. ref: {{ params.bq_project.mirror }}.store_one_article.article.medias.#source"),
            technical_source    STRING      OPTIONS(description="prisma internal source. ref: {{ params.bq_project.mirror }}.store_one_article.article.medias.#iframely#meta#site"),
            credit              STRING      OPTIONS(description="embed content credit. ref: {{ params.bq_project.mirror }}.store_one_article.article.medias.#iframely#meta#credit"),
            tag                 STRING      OPTIONS(description="embed content tags for videos. ref: {{ params.bq_project.mirror }}.store_one_article.article.medias.#iframely#meta#tags")
            >
        >                               OPTIONS(description="list of embed content information"),
        push                ARRAY<STRUCT<
            create_date         TIMESTAMP   OPTIONS(description="notification creation datetime. ref: {{ params.bq_project.mirror }}.store_one_article.push.createdAt"),
            segment             STRING      OPTIONS(description="notification segment title. ref: {{ params.bq_project.mirror }}.store_one_article.segment.title"),
            device              STRING      OPTIONS(description="notification device. ref: {{ params.bq_project.mirror }}.store_one_article.push.targetDevice")
            >
        >                               OPTIONS(description="list of push information")
    >                                   OPTIONS(description="article content information"),
    is_live                 BOOLEAN              OPTIONS(description="true/false. 1=is it article related to live post?"),
    post                    ARRAY<STRUCT<
        create_date             TIMESTAMP   OPTIONS(description="post creation datetime. ref: {{ params.bq_project.mirror }}.store_one_article.livePost.createdAt"),
        publication_date        TIMESTAMP   OPTIONS(description="post publication datetime. ref: {{ params.bq_project.mirror }}.store_one_article.livePost.publishedAt"),
        title                   STRING      OPTIONS(description="post titles. ref: {{ params.bq_project.mirror }}.store_one_article.livePost.title"),
        body                    STRING      OPTIONS(description="post content body. ref: {{ params.bq_project.mirror }}.store_one_article.livePost.title")
        >
    >                                  OPTIONS(description="list of post information"),
    review              ARRAY<STRUCT<
            average             FLOAT64     OPTIONS(description="article average review"),
            count               INTEGER     OPTIONS(description="reviews count")
        >
    >                               OPTIONS(description="article reviews"),
    ad_info              ARRAY<STRUCT<
            product_id          STRING     OPTIONS(description="Ad product Id. ref: {{ params.bq_project.mirror }}.store_one_product.product#id.")
        >
    >                               OPTIONS(description="Article ads information.")

)
PARTITION BY TIMESTAMP_TRUNC(create_date, YEAR)
OPTIONS(description="Refine article content by normalizing different store_one_article dataset tables. \n"||
                    "Add all article information. \n"||
                    "Table partitioned by create_date with day scale \n"||
                    "DAG: {{ dag.dag_id }}. \n"||
                    "Sync: daily");
-- useful function to extract article body content : extract json from array
CREATE TEMPORARY FUNCTION CUSTOM_JSON_EXTRACT_BODY(json_arr ARRAY<STRING>)
RETURNS string
LANGUAGE js AS """
    try {
        var result = " ";
        var arrayLength = json_arr.length;
        for (var i = 0; i < arrayLength; i++) {
            var parsed = JSON.parse(json_arr[i]);
            result += JSON.stringify(parsed['text'])
        }
        return result;
    } catch (e) {}
    return null;
""";

-- useful function to extract article embed content : extract json from array
CREATE TEMPORARY FUNCTION CUSTOM_JSON_EXTRACT_BODY_IFRAMELY(json_arr ARRAY<STRING>)
RETURNS ARRAY<string>
LANGUAGE js AS """
    try {
        var result = [];
        var arrayLength = json_arr.length;
        for (var i = 0; i < arrayLength; i++) {
            var parsed = JSON.parse(json_arr[i]);
            if(parsed['data'].hasOwnProperty('iframely')){
               result.push(JSON.stringify(parsed['data']['iframely']))
            }
        }
        return result;
    } catch (e) {}
    return null;
""";

-- dedup array of struct !
CREATE TEMP FUNCTION dedup(val ANY TYPE) AS ((
  SELECT ARRAY_AGG(t)
  FROM (SELECT DISTINCT * FROM UNNEST(val) v) t
));


-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
INSERT INTO `{{ params.bq_project.mirror }}.refined_data.article`
WITH article_information AS(
    -- select raw article columns
    SELECT
        a.id                                                                        AS article_id,
        a.createdAt                                                                 AS create_date,
        a.updatedAt                                                                 AS update_date,
        TIMESTAMP_MICROS(a.publishedAt)                                             AS publication_date,
        a.editedAt                                                                  AS edition_date,
        a.status,
        IF(format="RICH", "ARTICLE", format)                                        AS format,
        a.brandKey                                                                  AS brand_trigram,
        a.title,
        JSON_VALUE(JSON_EXTRACT_ARRAY(lead, '$.blocks')[ORDINAL(1)], '$.text')      AS head,
        CUSTOM_JSON_EXTRACT_BODY(JSON_EXTRACT_ARRAY(body, '$.blocks'))              AS body,
        CAST(isPushed AS BOOLEAN)                                                   AS is_pushed,
        NULL                                                                        AS cook_time,
        NULL                                                                        AS preparation_time,
        NULL                                                                        AS waiting_time,
        NULL                                                                        AS total_time,
        NULL                                                                        AS instructions,
        NULL                                                                        AS advice,
        NULL                                                                        AS cost,
        NULL                                                                        AS difficulty,
        NULL                                                                        AS yield,
        NULL                                                                        AS ingredients,
        NULL                                                                        AS calorie_level,
        STRUCT(
        s.id     AS id,
        s.title  AS title,
        s.slug   AS slug
        )                                                                           AS source
    FROM `{{ params.bq_project.mirror }}.store_one_article.article` AS a
    LEFT JOIN `{{ params.bq_project.mirror }}.store_one_article.source` AS s ON a.sourceIntId = s.intId
    WHERE a.publishedAt BETWEEN LOWER_BOUND AND UPPER_BOUND
    UNION ALL
    -- recipe article
    SELECT
        id                                                                      AS article_id,
        createdAt                                                               AS create_date,
        updatedAt                                                               AS update_date,
        publishedAt                                                             AS publication_date,
        editedAt                                                                AS edition_date,
        status,
        'RECIPE'                                                                AS format,
        brandKey                                                                AS brand_trigram,
        title,
        NULL                                                                    AS head,
        NULL                                                                    AS body,
        False                                                                   AS is_pushed,
        cookTime                                                                AS cook_time,
        prepTime                                                                AS preparation_time,
        waitTime                                                                AS waiting_time,
        totalTime                                                               AS total_time,
        CUSTOM_JSON_EXTRACT_BODY(JSON_EXTRACT_ARRAY(instructions, '$.blocks'))  AS instructions,
        IF(JSON_VALUE(JSON_EXTRACT_ARRAY(advice, '$.blocks')[ORDINAL(1)], '$.text') IS NULL,
            advice,
            JSON_VALUE(JSON_EXTRACT_ARRAY(advice, '$.blocks')[ORDINAL(1)], '$.text')
        ) AS advice,
        estimatedCost AS cost,
        difficulty,
        yield,
        CUSTOM_JSON_EXTRACT_BODY(JSON_EXTRACT_ARRAY(rawIngredients, '$.blocks'))     AS ingredients,
        calorieLevel AS calorie_level,
        STRUCT(
        '(not set)'  AS id,
         source      AS title,
        '(not set)'  AS slug
        ) AS source
    FROM `{{ params.bq_project.mirror }}.store_one_recipe.recipe`
    -- live : intId =36809946
    -- video : intId= 31665966
), article_author AS (
    -- get article author's
    -- article
    SELECT
        a.id AS article_id,
        ARRAY_AGG(ath.name IGNORE NULLS) AS author_name,
    FROM `{{ params.bq_project.mirror }}.store_one_article.article` AS a
    LEFT JOIN `{{ params.bq_project.mirror }}.store_one_article.articleAuthor`  AS aath ON a.intId = aath.articleIntId
    LEFT JOIN `{{ params.bq_project.mirror }}.store_one_article.author`         AS ath ON aath.authorIntId = ath.intId
    GROUP BY 1
    UNION ALL
    -- recipe
    SELECT
        r.id AS article_id,
        ARRAY_AGG(a.name IGNORE NULLS) AS author_name
    FROM `{{ params.bq_project.mirror }}.store_one_recipe.recipe`   AS r
    JOIN `{{ params.bq_project.mirror }}.store_one_article.author`  AS a ON a.id = r.author
    GROUP BY 1
), domain_hard_mapping AS (
    -- MAP (brand, format) to domain
    SELECT
          brandKey AS brand_trigram,
          format,
          origin AS domain
    FROM `{{ params.bq_project.mirror }}.store_one_article.brandedOrigin`
    GROUP BY ALL
) , article_url AS(
    -- select all articles urls
    -- article
    SELECT
        a.id AS article_id,
        a.brandKey,
        CONCAT("https://one-web.prismamedia.com/#/", a.brandKey, "/article/edit/", a.id) AS one,
        ARRAY_AGG(CONCAT(RTRIM(dhm.domain, "/"), au.path) IGNORE NULLS ORDER BY au.order) AS public,
        ARRAY_AGG(au.order ORDER BY au.order) AS `order`,
        ARRAY_AGG(CONCAT(RTRIM(dhm.domain, "/"), au.path) IGNORE NULLS ORDER BY au.order ASC)[SAFE_ORDINAL(1)] AS main_public
    FROM `{{ params.bq_project.mirror }}.store_one_article.article`     AS a
    JOIN `{{ params.bq_project.mirror }}.store_one_article.articleUrl`  AS au   ON au.articleIntId = a.intId
    JOIN domain_hard_mapping AS dhm ON dhm.brand_trigram = a.brandKey
                                    AND dhm.format = a.format
    GROUP BY ALL
    UNION ALL
    -- recipe
    SELECT
        r.id AS article_id,
        r.brandKey,
        CONCAT("https://one-recipe.prismamedia.com/", r.brandKey, "/edit/", r.id) AS one,
        CASE WHEN r.brandKey = "FAC" THEN ARRAY_AGG(CONCAT("www.femmeactuelle.fr", u.path) IGNORE NULLS ORDER BY ru.position)
        WHEN r.brandKey = "CAC" THEN ARRAY_AGG(CONCAT("www.cuisineactuelle.fr", u.path) IGNORE NULLS ORDER BY ru.position)
        END AS public,
        ARRAY_AGG(ru.position ORDER BY ru.position) AS `order`,
        ARRAY_AGG(CONCAT(IF(r.brandKey = "FAC", "www.femmeactuelle.fr", "www.cuisineactuelle.fr"), u.path) IGNORE NULLS ORDER BY ru.position ASC)[SAFE_ORDINAL(1)] AS main_public
    FROM `{{ params.bq_project.mirror }}.store_one_recipe.recipe`     AS r
    JOIN `{{ params.bq_project.mirror }}.store_one_recipe.recipe_url` AS ru  ON ru.recipeIntId = r.intId
    JOIN `{{ params.bq_project.mirror }}.store_one_recipe.url`        AS u    ON u.intId = ru.urlIntId
    GROUP BY ALL
), extract_ecommerce_tag AS (
    -- ecommerce tag info
    SELECT
        id AS article_id,
        ARRAY_AGG(
            STRUCT(
                JSON_EXTRACT_SCALAR(block, '$.data.tagId') AS id,
                JSON_EXTRACT_SCALAR(block, '$.data.url') AS url,
                JSON_EXTRACT_SCALAR(block, '$.data.buttonLabel') AS label,
                JSON_EXTRACT_SCALAR(block, '$.data.type') AS type
                )
            IGNORE NULLS
            -- order to make sure items are aligned
            ORDER BY JSON_EXTRACT_SCALAR(block, '$.data.tagId'), JSON_EXTRACT_SCALAR(block, '$.data.url')
        ) AS ecommerce
    FROM `{{ params.bq_project.mirror }}.store_one_article.article`, UNNEST(JSON_EXTRACT_ARRAY(body, '$.blocks')) AS block
    -- filter tagIds = ""
    WHERE JSON_EXTRACT_SCALAR(block, '$.data.tagId') != ''
    GROUP BY ALL
), article_tag AS (
    -- select all articles tags
    SELECT
        a.id AS article_id,
        ARRAY_AGG(
            STRUCT(t.id AS id,
            t.title AS title ,
            t.type AS type ,
            t.lang AS language,
            t.slug AS slug)
        IGNORE NULLS) AS tag
    FROM `{{ params.bq_project.mirror }}.store_one_article.article`    AS a
    JOIN `{{ params.bq_project.mirror }}.store_one_article.articleTag` AS `at` ON `at`.articleIntId = a.intId
    JOIN `{{ params.bq_project.mirror }}.store_one_article.tag`        AS t    ON t.intId = `at`.tagIntId
    GROUP BY 1
    UNION ALL
    -- recipe article
    SELECT
        r.id AS article_id,
        ARRAY_AGG(
            STRUCT(t.id AS id,
            t.title AS title ,
            '' AS type ,
            '' AS language,
            t.slug AS slug)
        IGNORE NULLS) AS tag
    FROM `{{ params.bq_project.mirror }}.store_one_recipe.recipe`     AS r
    JOIN `{{ params.bq_project.mirror }}.store_one_recipe.recipe_tag` AS rt ON rt.recipeIntId = r.intId
    JOIN `{{ params.bq_project.mirror }}.store_one_recipe.tag`        AS t    ON t.intId = rt.tagIntId
    GROUP BY 1
), article_category_tree AS(
    -- rebuild category tree for articles
    SELECT
        c1.parentintId AS cat_1_id, c1.title AS cat_1_title, c1.level AS c1_level, c1.slug AS slug_1, c1.id AS id_1,
        c2.intId       AS cat_2_id, c2.title AS cat_2_title, c2.level AS c2_level, c2.slug AS slug_2, c2.id AS id_2,
        c3.intId       AS cat_3_id, c3.title AS cat_3_title, c3.level AS c3_level, c3.slug AS slug_3, c3.id AS id_3,
        c4.intId       AS cat_4_id, c4.title AS cat_4_title, c4.level AS c4_level, c4.slug AS slug_4, c4.id AS id_4,
        c5.intId       AS cat_5_id, c5.title AS cat_5_title, c5.level AS c5_level, c5.slug AS slug_5, c5.id AS id_5
    FROM      `{{ params.bq_project.mirror }}.store_one_article.category` AS c1
    LEFT JOIN `{{ params.bq_project.mirror }}.store_one_article.category` AS c2 ON c2.parentintId = c1.intId AND c2.level = 2
    LEFT JOIN `{{ params.bq_project.mirror }}.store_one_article.category` AS c3 ON c3.parentintId = c2.intId AND c3.level = 3
    LEFT JOIN `{{ params.bq_project.mirror }}.store_one_article.category` AS c4 ON c4.parentintId = c3.intId AND c4.level = 4
    LEFT JOIN `{{ params.bq_project.mirror }}.store_one_article.category` AS c5 ON c5.parentintId = c4.intId AND c5.level = 5
    WHERE c1.level = 1
), recipe_category_tree AS(
    -- rebuild category tree for recipes
    SELECT
        c1.parentintId AS cat_1_id, c1.title AS cat_1_title, c1.level AS c1_level, c1.slug AS slug_1, c1.id AS id_1,
        c2.intId       AS cat_2_id, c2.title AS cat_2_title, c2.level AS c2_level, c2.slug AS slug_2, c2.id AS id_2,
        c3.intId       AS cat_3_id, c3.title AS cat_3_title, c3.level AS c3_level, c3.slug AS slug_3, c3.id AS id_3,
        c4.intId       AS cat_4_id, c4.title AS cat_4_title, c4.level AS c4_level, c4.slug AS slug_4, c4.id AS id_4,
        c5.intId       AS cat_5_id, c5.title AS cat_5_title, c5.level AS c5_level, c5.slug AS slug_5, c5.id AS id_5
    FROM      `{{ params.bq_project.mirror }}.store_one_recipe.category` AS c1
    LEFT JOIN `{{ params.bq_project.mirror }}.store_one_recipe.category` AS c2 ON c2.parentintId = c1.intId AND c2.level = 2
    LEFT JOIN `{{ params.bq_project.mirror }}.store_one_recipe.category` AS c3 ON c3.parentintId = c2.intId AND c3.level = 3
    LEFT JOIN `{{ params.bq_project.mirror }}.store_one_recipe.category` AS c4 ON c4.parentintId = c3.intId AND c4.level = 4
    LEFT JOIN `{{ params.bq_project.mirror }}.store_one_recipe.category` AS c5 ON c5.parentintId = c4.intId AND c5.level = 5
    WHERE c1.level = 1
), distinct_article_categories AS(
    -- select distinct categories/sub_categories for articles
    SELECT DISTINCT
        a.id AS article_id,
        IF(COALESCE(ct_1.c1_level, ct_2.c1_level,ct_3.c1_level, ct_4.c1_level, ct_5.c1_level) <= c.level,
        COALESCE(ct_1.cat_1_title, ct_2.cat_1_title,ct_3.cat_1_title, ct_4.cat_1_title, ct_5.cat_1_title),
        NULL) AS cat_1,
        IF(COALESCE(ct_2.c2_level, ct_2.c2_level,ct_3.c2_level, ct_4.c2_level, ct_5.c2_level) <= c.level,
        COALESCE(ct_2.cat_2_title, ct_2.cat_2_title,ct_3.cat_2_title, ct_4.cat_2_title, ct_5.cat_2_title),
        NULL) AS cat_2,
        IF(COALESCE(ct_3.c3_level, ct_3.c3_level,ct_3.c3_level, ct_4.c3_level, ct_5.c3_level) <= c.level,
        COALESCE(ct_3.cat_3_title, ct_3.cat_3_title,ct_3.cat_3_title, ct_4.cat_3_title, ct_5.cat_3_title),
        NULL) AS cat_3,
        IF(COALESCE(ct_4.c4_level, ct_4.c4_level,ct_4.c4_level, ct_4.c4_level, ct_5.c4_level) <= c.level,
        COALESCE(ct_4.cat_4_title, ct_4.cat_4_title,ct_4.cat_4_title, ct_4.cat_4_title, ct_5.cat_4_title),
        NULL) AS cat_4,
        IF(COALESCE(ct_5.c5_level, ct_5.c5_level,ct_5.c5_level, ct_5.c5_level, ct_5.c5_level) <= c.level,
        COALESCE(ct_5.cat_5_title, ct_5.cat_5_title,ct_5.cat_5_title, ct_5.cat_5_title, ct_5.cat_5_title),
        NULL) AS cat_5,
        ct_1.id_1,
        ct_2.id_2,
        ct_3.id_3,
        ct_4.id_4,
        ct_5.id_5,
        ct_1.slug_1,
        ct_2.slug_2,
        ct_3.slug_3,
        ct_4.slug_4,
        ct_5.slug_5
    FROM `{{ params.bq_project.mirror }}.store_one_article.article` AS a
    LEFT JOIN `{{ params.bq_project.mirror }}.store_one_article.articleCategory` AS ac   ON ac.articleIntId = a.intId
    LEFT JOIN `{{ params.bq_project.mirror }}.store_one_article.category` AS c ON ac.categoryIntId = c.intId
    LEFT JOIN article_category_tree AS ct_1 ON ct_1.cat_1_id = ac.categoryIntId
    LEFT JOIN article_category_tree AS ct_2 ON ct_2.cat_2_id = ac.categoryIntId
    LEFT JOIN article_category_tree AS ct_3 ON ct_3.cat_3_id = ac.categoryIntId
    LEFT JOIN article_category_tree AS ct_4 ON ct_4.cat_4_id = ac.categoryIntId
    LEFT JOIN article_category_tree AS ct_5 ON ct_5.cat_5_id = ac.categoryIntId
), distinct_recipe_categories AS(
    -- select distinct categories/sub_categories for recipes
    SELECT DISTINCT
        r.id AS article_id,
        IF(COALESCE(ct_1.c1_level, ct_2.c1_level,ct_3.c1_level, ct_4.c1_level, ct_5.c1_level) <= c.level,
        COALESCE(ct_1.cat_1_title, ct_2.cat_1_title,ct_3.cat_1_title, ct_4.cat_1_title, ct_5.cat_1_title),
        NULL) AS cat_1,
        IF(COALESCE(ct_2.c2_level, ct_2.c2_level,ct_3.c2_level, ct_4.c2_level, ct_5.c2_level) <= c.level,
        COALESCE(ct_2.cat_2_title, ct_2.cat_2_title,ct_3.cat_2_title, ct_4.cat_2_title, ct_5.cat_2_title),
        NULL) AS cat_2,
        IF(COALESCE(ct_3.c3_level, ct_3.c3_level,ct_3.c3_level, ct_4.c3_level, ct_5.c3_level) <= c.level,
        COALESCE(ct_3.cat_3_title, ct_3.cat_3_title,ct_3.cat_3_title, ct_4.cat_3_title, ct_5.cat_3_title),
        NULL) AS cat_3,
        IF(COALESCE(ct_4.c4_level, ct_4.c4_level,ct_4.c4_level, ct_4.c4_level, ct_5.c4_level) <= c.level,
        COALESCE(ct_4.cat_4_title, ct_4.cat_4_title,ct_4.cat_4_title, ct_4.cat_4_title, ct_5.cat_4_title),
        NULL) AS cat_4,
        IF(COALESCE(ct_5.c5_level, ct_5.c5_level,ct_5.c5_level, ct_5.c5_level, ct_5.c5_level) <= c.level,
        COALESCE(ct_5.cat_5_title, ct_5.cat_5_title,ct_5.cat_5_title, ct_5.cat_5_title, ct_5.cat_5_title),
        NULL) AS cat_5,
        ct_1.id_1,
        ct_2.id_2,
        ct_3.id_3,
        ct_4.id_4,
        ct_5.id_5,
        ct_1.slug_1,
        ct_2.slug_2,
        ct_3.slug_3,
        ct_4.slug_4,
        ct_5.slug_5
    FROM  `{{ params.bq_project.mirror }}.store_one_recipe.recipe` AS r
    LEFT JOIN `{{ params.bq_project.mirror }}.store_one_recipe.recipe_category` AS rc   ON rc.recipeIntId = r.intId
    LEFT JOIN `{{ params.bq_project.mirror }}.store_one_recipe.category` AS c ON rc.categoryIntId = c.intId
    LEFT JOIN recipe_category_tree AS ct_1 ON ct_1.cat_1_id = rc.categoryIntId
    LEFT JOIN recipe_category_tree AS ct_2 ON ct_2.cat_2_id = rc.categoryIntId
    LEFT JOIN recipe_category_tree AS ct_3 ON ct_3.cat_3_id = rc.categoryIntId
    LEFT JOIN recipe_category_tree AS ct_4 ON ct_4.cat_4_id = rc.categoryIntId
    LEFT JOIN recipe_category_tree AS ct_5 ON ct_5.cat_5_id = rc.categoryIntId
),  distinct_video_categories AS(
    -- select distinct categories/sub_categories for videos
    SELECT DISTINCT
        a.id AS article_id,
        IF(COALESCE(ct_1.c1_level, ct_2.c1_level,ct_3.c1_level, ct_4.c1_level, ct_5.c1_level) <= c.level,
        COALESCE(ct_1.cat_1_title, ct_2.cat_1_title,ct_3.cat_1_title, ct_4.cat_1_title, ct_5.cat_1_title),
        NULL) AS cat_1,
        IF(COALESCE(ct_2.c2_level, ct_2.c2_level,ct_3.c2_level, ct_4.c2_level, ct_5.c2_level) <= c.level,
        COALESCE(ct_2.cat_2_title, ct_2.cat_2_title,ct_3.cat_2_title, ct_4.cat_2_title, ct_5.cat_2_title),
        NULL) AS cat_2,
        IF(COALESCE(ct_3.c3_level, ct_3.c3_level,ct_3.c3_level, ct_4.c3_level, ct_5.c3_level) <= c.level,
        COALESCE(ct_3.cat_3_title, ct_3.cat_3_title,ct_3.cat_3_title, ct_4.cat_3_title, ct_5.cat_3_title),
        NULL) AS cat_3,
        IF(COALESCE(ct_4.c4_level, ct_4.c4_level,ct_4.c4_level, ct_4.c4_level, ct_5.c4_level) <= c.level,
        COALESCE(ct_4.cat_4_title, ct_4.cat_4_title,ct_4.cat_4_title, ct_4.cat_4_title, ct_5.cat_4_title),
        NULL) AS cat_4,
        IF(COALESCE(ct_5.c5_level, ct_5.c5_level,ct_5.c5_level, ct_5.c5_level, ct_5.c5_level) <= c.level,
        COALESCE(ct_5.cat_5_title, ct_5.cat_5_title,ct_5.cat_5_title, ct_5.cat_5_title, ct_5.cat_5_title),
        NULL) AS cat_5,
        ct_1.id_1,
        ct_2.id_2,
        ct_3.id_3,
        ct_4.id_4,
        ct_5.id_5,
        ct_1.slug_1,
        ct_2.slug_2,
        ct_3.slug_3,
        ct_4.slug_4,
        ct_5.slug_5
    FROM `{{ params.bq_project.mirror }}.store_one_article.article` AS a
    LEFT JOIN `{{ params.bq_project.mirror }}.store_one_article.articleChannel` AS ach  ON ach.articleIntId = a.intId
    LEFT JOIN `{{ params.bq_project.mirror }}.store_one_article.category` AS c ON ach.categoryIntId = c.intId
    LEFT JOIN article_category_tree AS ct_1 ON ct_1.cat_1_id = ach.categoryIntId
    LEFT JOIN article_category_tree AS ct_2 ON ct_2.cat_2_id = ach.categoryIntId
    LEFT JOIN article_category_tree AS ct_3 ON ct_3.cat_3_id = ach.categoryIntId
    LEFT JOIN article_category_tree AS ct_4 ON ct_4.cat_4_id = ach.categoryIntId
    LEFT JOIN article_category_tree AS ct_5 ON ct_5.cat_5_id = ach.categoryIntId
), article_categories AS(
    -- add article categories except video format
    SELECT
        article_id,
        ARRAY_AGG(
            STRUCT(
                cat_1,
                id_1,
                slug_1,
                cat_2,
                id_2,
                slug_2,
                cat_3,
                id_3,
                slug_3,
                cat_4,
                id_4,
                slug_4,
                cat_5,
                id_5,
                slug_5
            )
        IGNORE NULLS) AS category
    FROM distinct_article_categories
    GROUP BY 1
), recipe_categories AS(
    SELECT
        article_id,
        ARRAY_AGG(
            STRUCT(
                cat_1,
                id_1,
                slug_1,
                cat_2,
                id_2,
                slug_2,
                cat_3,
                id_3,
                slug_3,
                cat_4,
                id_4,
                slug_4,
                cat_5,
                id_5,
                slug_5
            )
        IGNORE NULLS) AS category
    FROM  distinct_recipe_categories
    GROUP BY 1
), videos_categories AS(
     -- add videos categories
    SELECT
        article_id,
        ARRAY_AGG(
            STRUCT(
                cat_1,
                id_1,
                slug_1,
                cat_2,
                id_2,
                slug_2,
                cat_3,
                id_3,
                slug_3,
                cat_4,
                id_4,
                slug_4,
                cat_5,
                id_5,
                slug_5
            )
        IGNORE NULLS) AS category
    FROM distinct_video_categories
    GROUP BY 1
), article_qualifier AS(
    -- add article qualifier
    SELECT
        a.id AS article_id,
        ARRAY_AGG(
            STRUCT(
                q.id, q.title, q.slug
            ) IGNORE NULLS) AS qualifiers
    FROM `{{ params.bq_project.mirror }}.store_one_article.article` AS a
    LEFT JOIN `{{ params.bq_project.mirror }}.store_one_article.articleQualifier`   AS aq ON aq.articleIntId = a.intId
    LEFT JOIN `{{ params.bq_project.mirror }}.store_one_article.qualifier`          AS q ON q.intId = aq.qualifierIntId
    GROUP BY 1
), lead_embed AS(
    -- get lead embeds: video, image or slideshow
    -- article
    SELECT
        a.id AS article_id,
        CASE
            WHEN ( COALESCE(JSON_VALUE(unested_json,  '$.iframely.meta.media'),JSON_VALUE(unested_json,  '$.iframely.meta.extension')) = "player"
                 OR STARTS_WITH(JSON_VALUE(unested_json, '$.iframely.meta.canonical'), 'https://www.dailymotion.com') )
            THEN 'video'
            ELSE 'image'
        END AS type,
        "lead" AS position,
        JSON_VALUE(unested_json, '$.iframely.meta.canonical')   AS url,
        JSON_VALUE(unested_json, '$.source')                    AS source,
        JSON_VALUE(unested_json,'$.iframely.meta.site')         AS technical_source,
        JSON_VALUE(unested_json, '$.iframely.meta.credit')      AS credit,
        JSON_VALUE(unested_json, '$.iframely.meta.tags')        AS tag
    FROM `{{ params.bq_project.mirror }}.store_one_article.article` AS a, UNNEST(JSON_EXTRACT_ARRAY(medias, '$.')) AS unested_json
    UNION ALL
    -- recipe
    SELECT
        r.id AS article_id,
        CASE
            WHEN ( COALESCE(JSON_VALUE(unested_json,  '$.iframely.meta.media'),JSON_VALUE(unested_json,  '$.iframely.meta.extension')) = "player"
                 OR STARTS_WITH(JSON_VALUE(unested_json, '$.iframely.meta.canonical'), 'https://www.dailymotion.com') )
            THEN 'video'
            ELSE 'image'
        END AS type,
        "lead" AS position,
        JSON_VALUE(unested_json, '$.iframely.meta.canonical')   AS url,
        JSON_VALUE(unested_json, '$.source')                    AS source,
        JSON_VALUE(unested_json,'$.iframely.meta.site')         AS technical_source,
        JSON_VALUE(unested_json, '$.iframely.meta.credit')      AS credit,
        JSON_VALUE(unested_json, '$.iframely.meta.tags')        AS tag
    FROM `{{ params.bq_project.mirror }}.store_one_recipe.recipe` AS r, UNNEST(JSON_EXTRACT_ARRAY(medias, '$.')) AS unested_json
), body_embed AS(
    -- get body embeds: image, article
    -- article
    SELECT
        -- article as embed
        a.id AS article_id,
        CASE
            WHEN LOWER(JSON_VALUE(iframely, "$.meta.site")) LIKE "%article" THEN "article"
            -- embed in body article: we consider it as image (To be checked later with ONE Squad)
            WHEN JSON_VALUE(iframely, "$.meta.canonical")  IS NOT NULL      THEN "image"
            ELSE NULL
        END AS type,
        "body" AS position,
        JSON_VALUE(iframely, "$.meta.canonical")        AS url,
        JSON_VALUE(iframely, "$.source")                AS source,
        JSON_VALUE(iframely, "$.meta.site")             AS technical_source,
        JSON_VALUE(iframely, '$.iframely.meta.credit')  AS credit,
        JSON_VALUE(iframely, '$.iframely.meta.tags')    AS tag
    FROM   `{{ params.bq_project.mirror }}.store_one_article.article` AS a, UNNEST(CUSTOM_JSON_EXTRACT_BODY_IFRAMELY(JSON_EXTRACT_ARRAY(body, '$.blocks'))) AS iframely
    UNION DISTINCT
    SELECT
        -- image as embed
        a.id AS article_id,
        CASE
            WHEN JSON_VALUE(iframely, "$.meta.image") LIKE "%.jpg"      THEN "image"
            -- embed in body article: we consider it as image (To be checked later with ONE Squad)
            WHEN JSON_VALUE(iframely, "$.meta.canonical")  IS NOT NULL  THEN "image"
            ELSE NULL
        END AS type,
        "body" AS position,
        COALESCE(JSON_VALUE(iframely, "$.meta.image"),  JSON_VALUE(iframely, "$.meta.canonical")) AS url,
        JSON_VALUE(iframely, "$.source")                AS source,
        JSON_VALUE(iframely, "$.meta.site")             AS technical_source,
        JSON_VALUE(iframely, '$.iframely.meta.credit')  AS credit,
        JSON_VALUE(iframely, '$.iframely.meta.tags')    AS tag
    FROM   `{{ params.bq_project.mirror }}.store_one_article.article` AS a, UNNEST(CUSTOM_JSON_EXTRACT_BODY_IFRAMELY(JSON_EXTRACT_ARRAY(body, '$.blocks'))) AS iframely
    UNION DISTINCT
    -- recipe
    SELECT
        -- article as embed
        r.id AS article_id,
        CASE
            WHEN LOWER(JSON_VALUE(iframely, "$.meta.site")) LIKE "%article" THEN "article"
            -- embed in body article: we consider it as image (To be checked later with ONE Squad)
            WHEN JSON_VALUE(iframely, "$.meta.canonical")  IS NOT NULL      THEN "image"
            ELSE NULL
        END AS type,
        "body" AS position,
        JSON_VALUE(iframely, "$.meta.canonical")        AS url,
        JSON_VALUE(iframely, "$.source")                AS source,
        JSON_VALUE(iframely, "$.meta.site")             AS technical_source,
        JSON_VALUE(iframely, '$.iframely.meta.credit')  AS credit,
        JSON_VALUE(iframely, '$.iframely.meta.tags')    AS tag
    FROM   `{{ params.bq_project.mirror }}.store_one_recipe.recipe` AS r, UNNEST(CUSTOM_JSON_EXTRACT_BODY_IFRAMELY(JSON_EXTRACT_ARRAY(instructions, '$.blocks'))) AS iframely
    UNION DISTINCT
    SELECT
        -- image as embed
        r.id AS article_id,
        CASE
            WHEN JSON_VALUE(iframely, "$.meta.image") LIKE "%.jpg" THEN "image"
            -- embed in body article: we consider it as image (To be checked later with ONE Squad)
            WHEN JSON_VALUE(iframely, "$.meta.canonical")  IS NOT NULL  THEN "image"
            ELSE NULL
        END AS type,
        "body" AS position,
        COALESCE(JSON_VALUE(iframely, "$.meta.image"),  JSON_VALUE(iframely, "$.meta.canonical")) AS url,
        JSON_VALUE(iframely, "$.source")                AS source,
        JSON_VALUE(iframely, "$.meta.site")             AS technical_source,
        JSON_VALUE(iframely, '$.iframely.meta.credit')  AS credit,
        JSON_VALUE(iframely, '$.iframely.meta.tags')    AS tag
    FROM   `{{ params.bq_project.mirror }}.store_one_recipe.recipe` AS r, UNNEST(CUSTOM_JSON_EXTRACT_BODY_IFRAMELY(JSON_EXTRACT_ARRAY(instructions, '$.blocks'))) AS iframely
), lead_body_embed AS(
    -- union of lead and body embed's
    SELECT * FROM lead_embed
    UNION DISTINCT
    SELECT * FROM body_embed
), article_embed AS(
     -- set embed information into array of structure
    SELECT
        article_id,
        ARRAY_AGG(
            STRUCT(
                type,
                position,
                url,
                source,
                technical_source,
                credit,
                tag
            )
        IGNORE NULLS) AS embed
    FROM lead_body_embed
    GROUP BY 1
), article_push AS(
   -- select push and segment information
    SELECT
        a.id AS article_id,
        ARRAY_AGG(
            STRUCT(
                p.createdAt AS create_date,
                s.title AS segment,
                p.targetDevice AS device
            )
        IGNORE NULLS) AS push
    FROM `{{ params.bq_project.mirror }}.store_one_article.article`     AS a
    JOIN `{{ params.bq_project.mirror }}.store_one_article.push`        AS p    ON a.intId = p.intId
    JOIN `{{ params.bq_project.mirror }}.store_one_article.pushSegment` AS ps   ON ps.pushintId = p.intId
    JOIN `{{ params.bq_project.mirror }}.store_one_article.segment`     AS s    ON s.intId = ps.segmentIntId
    WHERE a.isPushed = 1
    GROUP BY 1
), live_post AS(
    -- select live post content
    SELECT
        a.id                                                                                    AS article_id,
        true                                                                                    AS is_live,
        lp.liveIntId                                                                            AS live_int_id,
        ARRAY_AGG(
            STRUCT(
                lp.createdAt                                                    AS create_date,
                lp.publishedAt                                                  AS publication_date,
                lp.title                                                        AS title,
                CUSTOM_JSON_EXTRACT_BODY(JSON_EXTRACT_ARRAY(lp.body, '$.blocks'))    AS body
            )
        IGNORE NULLS) AS post
    FROM `{{ params.bq_project.mirror }}.store_one_article.livePost`    AS lp
    JOIN `{{ params.bq_project.mirror }}.store_one_article.live`        AS l ON lp.liveIntId = l.intId
    JOIN `{{ params.bq_project.mirror }}.store_one_article.article`     AS a ON a.intId = l.articleIntId
    GROUP BY 1, 2, 3
), article_reviews AS(
    -- get articles reviews
    SELECT
        SPLIT(ca.content_id, ":")[SAFE_OFFSET(1)] AS article_id,
        ARRAY_AGG(
            STRUCT(
                CAST(average as FLOAT64) AS average,
                reviews_count AS count
            )
        IGNORE NULLS) AS review

    FROM `{{ params.bq_project.mirror }}.store_user_review.content_averages` AS ca
    WHERE
        LOWER(ca.content_id) LIKE 'article:%'
        OR
        LOWER(ca.content_id) LIKE 'recipe:%'
    GROUP BY 1
), ads_information AS(
    -- extract ads into articles
    SELECT
        a.id AS article_id,
        dedup(ARRAY_AGG(
            IF(SPLIT(REGEXP_EXTRACT(un_bl, "data-product-id=(.*)"), '\\"')[SAFE_ORDINAL(2)] IS NOT NULL,
            STRUCT(
              SPLIT(REGEXP_EXTRACT(un_bl, "data-product-id=(.*)"), '\\"')[SAFE_ORDINAL(2)] AS product_id
            )
            , NULL)
          IGNORE NULLS
        )) AS ad_info
    FROM `{{ params.bq_project.mirror }}.store_one_article.article` AS a, UNNEST(JSON_EXTRACT_ARRAY(body, '$.blocks')) AS un_bl
    GROUP BY 1
    UNION ALL
    -- @Now, recipes doesen't have ads, but later they should be !
    SELECT
        a.id AS article_id,
        dedup(ARRAY_AGG(
            IF(SPLIT(REGEXP_EXTRACT(un_bl, "data-product-id=(.*)"), '\\"')[SAFE_ORDINAL(2)] IS NOT NULL,
            STRUCT(
              SPLIT(REGEXP_EXTRACT(un_bl, "data-product-id=(.*)"), '\\"')[SAFE_ORDINAL(2)] AS product_id
            )
            , NULL)
          IGNORE NULLS
        )) AS ad_info
    FROM `{{ params.bq_project.mirror }}.store_one_recipe.recipe` AS a, UNNEST(JSON_EXTRACT_ARRAY(instructions, '$.blocks')) AS un_bl
    GROUP BY 1
)
-- join all together and organize data into structure
SELECT
    a.article_id,
    a.create_date,
    a.update_date,
    a.publication_date,
    a.edition_date,
    a.status,
    a.format,
    a.brand_trigram,
    aa.author_name,
    a.source,
    a.is_pushed,
    STRUCT(
        a.title,
        a.head,
        a.body,
        STRUCT(a.cook_time, a.preparation_time, a.waiting_time,
                a.total_time, a.instructions, a.advice,
                a.cost, a.difficulty, a.yield,
                a.ingredients, a.calorie_level
        ) AS recipe,
        STRUCT(au.one, au.order, au.public, au.main_public) AS url,
        eet.ecommerce,
        `at`.tag,
        CASE WHEN a.format = "VIDEO"  THEN vc.category
        WHEN a.format = "RECIPE" THEN rc.category
        ELSE ac.category
        END AS category,
        aq.qualifiers,
        ae.embed,
        ap.push
    ) AS content,
    IFNULL(lp.is_live, false) AS is_live,
    lp.post,
    ar.review,
    adin.ad_info
FROM article_information         AS a
LEFT JOIN article_author         AS aa USING(article_id)
LEFT JOIN article_url            AS au USING(article_id)
LEFT JOIN extract_ecommerce_tag  AS eet USING(article_id)
LEFT JOIN article_tag            AS `at` USING(article_id)
LEFT JOIN article_categories     AS ac USING(article_id)
LEFT JOIN recipe_categories      AS rc USING(article_id)
LEFT JOIN videos_categories      AS vc USING(article_id)
LEFT JOIN article_qualifier      AS aq USING(article_id)
LEFT JOIN article_embed          AS ae USING(article_id)
LEFT JOIN article_push           AS ap USING(article_id)
LEFT JOIN live_post              AS lp USING(article_id)
LEFT JOIN article_reviews        AS ar USING(article_id)
LEFT JOIN ads_information        AS adin USING(article_id);