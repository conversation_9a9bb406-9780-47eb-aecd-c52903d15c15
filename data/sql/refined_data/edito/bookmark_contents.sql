-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project.mirror }}.refined_data.bookmark`(
    content_id              STRING      NOT NULL    OPTIONS(description="content id and each content depend on brand --> 1 content = 1 to N brands."||
                                                                        "if bookmark_type=readitlater id=url publicId as string. ref: {{ params.bq_project.mirror }}.store_one_article.url.publicId"||
                                                                        "if bookmark_type=follow:people id=person uuid. ref: {{ params.bq_project.mirror }}.store_one_person.person.id"||
                                                                        "if bookmark_type=follow. ref: ??"||
                                                                        "if bookmark_type=follow:broadcast. ref: ?? "||
                                                                        "if bookmark_type=bookmark id=Recipe/article:publicId. ref: {{ params.bq_project.mirror }}.store_one_recipe.url.publicId/{{ params.bq_project.mirror }}.store_one_article.url.publicId"||
                                                                        "if bookmark_type=follow:tag id=tag uuid. ref: {{ params.bq_project.mirror }}.store_one_article.tag.id"),
    update_date             TIMESTAMP               OPTIONS(description="content last update datetime. ref: {{ params.bq_project.mirror }}.store_bookmark.bookmark_content.lastUpdate"),
    brand_trigram           STRING                  OPTIONS(description="brand trigram. ref: {{ params.bq_project.mirror }}.store_bookmark.bookmark_content.brand"),
    shoot_id                INTEGER                 OPTIONS(description="partner id to shoot the alert by email. ref: {{ params.bq_project.mirror }}.store_bookmark.bookmark_content.id"),
    content_path            STRING                  OPTIONS(description="ref: {{ params.bq_project.mirror }}.store_bookmark.bookmark_content.path"),
    content_title           STRING                  OPTIONS(description="ref: {{ params.bq_project.mirror }}.store_bookmark.bookmark_content.title"),
    content_category        STRING                  OPTIONS(description="content theme. ref: {{ params.bq_project.mirror }}.store_bookmark.bookmark_content.category"),

    bookmark                ARRAY<STRUCT<
        id                      INTEGER          OPTIONS(description="bookmarks ids. ref: {{ params.bq_project.mirror }}.store_bookmark.bookmark.id"),
        create_date             TIMESTAMP        OPTIONS(description="bookmark creation datetime. ref: {{ params.bq_project.mirror }}.store_bookmark.bookmark.created_at"),
        update_date             TIMESTAMP        OPTIONS(description="bookmark last update datetime. ref: {{ params.bq_project.mirror }}.store_bookmark.bookmark.updated_at"),
        type                    STRING           OPTIONS(description="bookmark type. enum = readitlater, bookmark, follow, follow:tag, follow:people, follow:broadcast. ref:{{ params.bq_project.mirror }}.store_bookmark.bookmark.type"),
        is_deleted              INTEGER          OPTIONS(description="deleted status. is deleted ? False = 0, True = unsubscription from bookmark."),
        user                 STRUCT<
            pmc_uuid                STRING           OPTIONS(description="user uuid. ref: {{ params.bq_project.mirror }}.store_bookmark.bookmark.user_id"),
            pmc_profile_master_id   INTEGER          OPTIONS(description="user pmc pmi. ref: {{ params.bq_project.matrix }}.store_matrix_pmc.profile_master_id_v2.id"),
            email_profile_master_id INTEGER          OPTIONS(description="user email pmi. ref: {{ params.bq_project.matrix }}.store_matrix_email.profile_master_id.id")
        >                                                   OPTIONS(description="pmc and email information")
    >>                                               OPTIONS(description="Bookmarks information")
)
PARTITION BY TIMESTAMP_TRUNC(update_date, DAY)
OPTIONS(
    description="List all bookmarked contents.\n"
              ||"Normalize bookmark and content and user information.\n"
              ||"Aggregate bookmarks by content and brand.\n"
              ||"Table partitioned by bookmark.create_date with day scale.\n\n"
              ||"DAG: {{ dag.dag_id }}.\n\n"
              ||"Sync: daily"
);

TRUNCATE TABLE `{{ params.bq_project.mirror }}.refined_data.bookmark`;

-- insert into table with full process
INSERT INTO `{{ params.bq_project.mirror }}.refined_data.bookmark`
-- WITH sub-queries:
-- * content: extract contents information that are bookmarked
-- * bookmark: extract bookmarks information
-- * email_pmc_profile: add pmc and email profile master id based on user uuid
-- lastly, select all needed information as a struct
WITH content AS(
    --  extract contents information that are bookmarked
    SELECT
        content_id,
        UPPER(brand) AS brand_trigram,
        lastUpdate   AS update_date,
        id           AS shoot_id,
        content_path,
        title        AS content_title,
        category     AS content_category
    FROM `{{ params.bq_project.mirror }}.store_bookmark.bookmark_content`
),

bookmark AS (
    -- extract bookmarks information
    SELECT
        content_id,
        UPPER(brand)     AS brand_trigram,
        id               AS id,
        created_at       AS create_date,
        updated_at       AS update_date,
        bookmark_type    AS type,
        deleted          AS is_deleted,
        user_id          AS pmc_uuid
    FROM `{{ params.bq_project.mirror }}.store_bookmark.bookmark`
),

email_pmc_profile AS(
    -- add pmc and email profile master id based on user uuid
    SELECT DISTINCT
        b.user_id,
        pd.id.pmc_profile_master_id,
        pd.id.email_profile_master_id
    FROM `{{ params.bq_project.mirror }}.store_bookmark.bookmark` AS b
    JOIN `{{ params.bq_project.matrix }}.business_data.profile_digital_360` AS pd ON pd.id.pmc_uuid = b.user_id
)

SELECT 
    c.content_id,
    c.update_date,
    c.brand_trigram,
    c.shoot_id,
    c.content_path,
    c.content_title, 
    c.content_category,
    ARRAY_AGG(
        STRUCT(
            b.id            AS id,
            b.create_date   AS create_date,
            b.update_date   AS update_date,
            b.type          AS type,
            b.is_deleted    AS is_deleted,
            STRUCT(
                b.pmc_uuid AS pmc_uuid,
                eppmi.pmc_profile_master_id AS pmc_profile_master_id,
                eppmi.email_profile_master_id AS email_profile_master_id
            ) AS user
        )
    ) AS bookmark
FROM content AS c
JOIN bookmark AS b
    ON b.content_id = c.content_id
    AND b.brand_trigram = c.brand_trigram
JOIN email_pmc_profile AS eppmi ON eppmi.user_id = b.pmc_uuid
GROUP BY ALL;
