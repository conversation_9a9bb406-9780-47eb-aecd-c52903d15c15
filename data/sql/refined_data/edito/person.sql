-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{params.bq_project.mirror}}.refined_data.person`(
    person_int_id           INTEGER    NOT NULL     OPTIONS(description="person technical id. ref: {{params.bq_project.mirror}}.store_one_person.person.intId"),
    person_uuid             STRING     NOT NULL     OPTIONS(description="person uuid. ref: {{params.bq_project.mirror}}.store_one_person.person.id"),
    create_date             TIMESTAMP               OPTIONS(description="person creation datetime. ref:{{params.bq_project.mirror}}.store_one_person.person.createdAt"),
    update_date             TIMESTAMP               OPTIONS(description="person last update datetime. ref:{{params.bq_project.mirror}}.store_one_person.person.updatedAt"),
    personal_information    STRUCT<
        full_name               STRING                  OPTIONS(description="ref:{{params.bq_project.mirror}}.store_one_person.person.firstName"),
        title                   ARRAY<STRING>           OPTIONS(description="list[] of person title, for example: Princesse consort du <PERSON>. ref:{{params.bq_project.mirror}}.store_one_person.title.title"),
        birth_date              DATE                    OPTIONS(description="ref:{{params.bq_project.mirror}}.store_one_person.person.birth"),
        death_date              DATE                    OPTIONS(description="ref:{{params.bq_project.mirror}}.store_one_person.person.death"),
        sex                     STRING                  OPTIONS(description="enum = femme, homme, transgenre, null. ref:{{params.bq_project.mirror}}.store_one_person.person.sex"),
        hoobies                 ARRAY<STRING>           OPTIONS(description="list[] of person hobbies. ref:{{params.bq_project.mirror}}.store_one_person.passion.title")
    >                                               OPTIONS(description="person personal information"),
    biographie_per_brand    STRUCT<
        brand_trigram           ARRAY<STRING>           OPTIONS(description="list[] of brand trigram. ref:{{params.bq_project.mirror}}.store_one_person.person.bioPerBrand#brand_tri"),
        status                  ARRAY<STRING>           OPTIONS(description="list[] of biography status as enum =draft, published, deleted. ref:{{params.bq_project.mirror}}.store_one_person.person.bioPerBrand#brand_tri#biographie"),
        description             ARRAY<STRING>           OPTIONS(description="list[] of person description. ref:{{params.bq_project.mirror}}.store_one_person.person.bioPerBrand#brand_tri#biographie"),
        image_link              ARRAY<STRING>           OPTIONS(description="list[] of person image link. ref:{{params.bq_project.mirror}}.store_one_person.person.bioPerBrand#brand_tri#media#iframely#meta#canonical")
    >                                               OPTIONS(description="person biography per brand"),
    postal_information      STRUCT<
        address                 STRING                  OPTIONS(description="person address. ref:{{params.bq_project.mirror}}.store_one_person.person.residence"),
        country                 ARRAY<STRING>           OPTIONS(description="list[] of person country. ref:{{params.bq_project.mirror}}.store_one_person.country.title")
    >                                               OPTIONS(description="person postal information"),
    academic_information    STRUCT<
        school                  ARRAY<STRING>           OPTIONS(description="list[] of person school. ref:{{params.bq_project.mirror}}.store_one_person.school.title"),
        degree                  ARRAY<STRING>           OPTIONS(description="list[] of person degree. ref:{{params.bq_project.mirror}}.store_one_person.degree.title")
    >                                               OPTIONS(description="person academic information"),
    professional_information    STRUCT<
        job                     ARRAY<STRING>           OPTIONS(description="list[] of person job. ref:{{params.bq_project.mirror}}.store_one_person.job.title"),
        award                   ARRAY<STRING>           OPTIONS(description="list[] of person award. ref:{{params.bq_project.mirror}}.store_one_person.award.title")
    >                                               OPTIONS(description="person professional information"),
)
PARTITION BY TIMESTAMP_TRUNC(create_date, DAY)
OPTIONS(description="List all persons information."||
                    "Normalize persons tables."||
                    "Table partitioned by create_date with day scale."||
                    "DAG: {{ dag.dag_id }}."||
                    "Sync: daily at 00:00AM UTC+2");

-- ----------------- useful functions ----------------------------------------
-- extract first keys of json into an ARRAY<STRING>
CREATE TEMPORARY FUNCTION jsonObjectKeys(input STRING)
RETURNS Array<String>
LANGUAGE js AS """
    try {
        return Object.keys(JSON.parse(input));
    } catch (e) {}
    return null
""";
-- extract json corresponding to key into a string
CREATE TEMPORARY FUNCTION CUSTOM_JSON_EXTRACT(json STRING, key STRING)
RETURNS STRING
LANGUAGE js AS """
    try {
        var parsed = JSON.parse(json);
        return JSON.stringify(parsed[key]);
    } catch (e) {}
    return null;
""";
-- ---------------------------------------------------------------------------
TRUNCATE TABLE `{{params.bq_project.mirror}}.refined_data.person`;

-- insert into table
INSERT INTO `{{params.bq_project.mirror}}.refined_data.person`
-- WITH sub-queries :
-- * person_hobbies : extract person hobbies
-- * person_title : extract person title
-- * get_brands : extract brands from bioByBrand column
-- * get_json_per_brand : extract bio json per brand per person
-- * biographie_per_brand : extract person biography information from bioByBrand column
-- * postal_information : extract postal information
-- * school_information : extract school information
-- * degree_information : extract degree information
-- * job_information : extract job information
-- * award_information : extract award information
-- lastly, join all CTE together into STRUCT
WITH person_hoobies AS (
    -- extract person hobbies
    SELECT
        pp.personIntId,
        ARRAY_AGG(p.title IGNORE NULLS) AS title
    FROM `{{params.bq_project.mirror}}.store_one_person.passion`AS p
    JOIN `{{params.bq_project.mirror}}.store_one_person.personPassion` AS pp ON pp.passionIntId = p.intId
    GROUP BY 1
), person_title AS (
    -- extract person title
    SELECT
        pt.personIntId,
        ARRAY_AGG(t.title IGNORE NULLS) AS title
    FROM `{{params.bq_project.mirror}}.store_one_person.title`        AS t
    JOIN `{{params.bq_project.mirror}}.store_one_person.personTitle`  AS pt ON pt.titleIntId = t.intId
    GROUP BY 1
), get_brands AS (
    -- extract brands from bioByBrand column
    SELECT
        intId,
        bioByBrand,
        jsonObjectKeys(bioByBrand) AS brand_trigram
    FROM
        `{{params.bq_project.mirror}}.store_one_person.person`
    WHERE bioByBrand IS NOT NULL
), get_json_per_brand AS(
    -- extract bio json per brand per person
    SELECT
        intId,
        brand_trigram,
        -- extract biography per brand as json
        CUSTOM_JSON_EXTRACT(JSON_EXTRACT(bioByBrand, "$."), brand_trigram) AS json_bio,
    FROM get_brands AS gb, UNNEST(gb.brand_trigram) as brand_trigram
), biographie_per_brand AS(
    -- extract person biography information from bioByBrand column
    SELECT
        intId AS personIntId,
        ARRAY_AGG(brand_trigram)                                                            AS brand_trigram,
        ARRAY_AGG(JSON_VALUE(json_bio, "$.status") IGNORE NULLS)                            AS status,
        ARRAY_AGG(JSON_VALUE(json_bio, "$.biographie") IGNORE NULLS)                        AS description,
        ARRAY_AGG(JSON_VALUE(json_bio, "$.media.iframely.meta.canonical") IGNORE NULLS)     AS image_link
    FROM get_json_per_brand
    GROUP BY 1
), postal_information AS (
    -- extract postal information
    SELECT
        pc.personIntId,
        ARRAY_AGG(c.title IGNORE NULLS) AS title
    FROM `{{params.bq_project.mirror}}.store_one_person.country`        AS c
    JOIN `{{params.bq_project.mirror}}.store_one_person.personCountry`    AS pc ON pc.countryIntId = c.intId
    GROUP BY 1
), school_information AS (
    -- extract school information
    SELECT
        ps.personIntId,
        ARRAY_AGG(s.title IGNORE NULLS) AS school
    FROM `{{params.bq_project.mirror}}.store_one_person.school`         AS s
    JOIN `{{params.bq_project.mirror}}.store_one_person.personStudy`    AS ps ON ps.schoolIntId = s.intId
    GROUP BY 1
), degree_information AS (
    -- extract degree information
    SELECT
        pd.personIntId,
        ARRAY_AGG(d.title IGNORE NULLS) AS degree
    FROM `{{params.bq_project.mirror}}.store_one_person.degree`         AS d
    JOIN `{{params.bq_project.mirror}}.store_one_person.personDegree`   AS pd ON pd.degreeIntId = d.intId
    GROUP BY 1
), job_information AS (
    -- extract job information
    SELECT
        pj.personIntId,
        ARRAY_AGG(j.title IGNORE NULLS) AS job
    FROM `{{params.bq_project.mirror}}.store_one_person.job`         AS j
    JOIN `{{params.bq_project.mirror}}.store_one_person.personJob`   AS pj ON pj.jobIntId = j.intId
    GROUP BY 1
), award_information AS (
    -- extract award information
    SELECT
        pn.personIntId,
        ARRAY_AGG(a.title IGNORE NULLS) AS award
    FROM `{{params.bq_project.mirror}}.store_one_person.award`              AS a
    JOIN `{{params.bq_project.mirror}}.store_one_person.personNomination`   AS pn ON pn.awardIntId = a.intId
    GROUP BY 1
)
SELECT
    p.intId     AS person_int_id,
    p.id        AS person_uuid,
    p.createdAt AS create_date,
    p.updatedAt AS update_date,
    STRUCT(
        p.fullName AS full_name,
        pt.title    AS title,
        PARSE_DATE('%Y-%m-%d',  p.birth) AS birth_date,
        PARSE_DATE('%Y-%m-%d',  p.death) AS death_date,
        p.sex,
        ph.title AS hoobies
    ) AS personal_information,
    STRUCT(
        bb.brand_trigram,
        bb.status,
        bb.description,
        bb.image_link
    ) AS biographie_per_brand,
    STRUCT(
        p.residence AS address,
        po.title    AS country
    ) AS postal_information,
    STRUCT(
        s.school,
        d.degree
    ) AS academic_information,
    STRUCT(
        j.job,
        a.award
    ) AS professional_information
FROM `{{params.bq_project.mirror}}.store_one_person.person` AS p
LEFT JOIN person_title          AS pt   ON pt.personIntId = p.intId
LEFT JOIN person_hoobies        AS ph   ON ph.personIntId = p.intId
LEFT JOIN biographie_per_brand  AS bb   ON bb.personIntId = p.intId
LEFT JOIN postal_information    AS po   ON po.personIntId = p.intId
LEFT JOIN school_information    AS s    ON s.personIntId  = p.intId
LEFT JOIN degree_information    AS d    ON d.personIntId  = p.intId
LEFT JOIN job_information       AS j    ON J.personIntId  = p.intId
LEFT JOIN award_information     AS a    ON a.personIntId  = p.intId
