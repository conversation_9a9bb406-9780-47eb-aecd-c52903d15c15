-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project.mirror }}.refined_data.program`(
    program_id        STRING NOT NULL OPTIONS(description="Program ID from the source. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.program.id"),
    info              STRUCT<
        title         STRING OPTIONS(description="Program title. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.program.title"),
        country       STRING OPTIONS(description="Country of the program. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.program.country"),
        releasedYear  INT64  OPTIONS(description="Year of release for the program. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.program.releasedYear"),
        synopsis      STRING OPTIONS(description="Synopsis of the program. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.program.synopsis")
    > OPTIONS(description="Program's general information."),
    genre             STRUCT<
        category      STRING OPTIONS(description="Program's genre category. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.genre.shortTitle"),
        genre         STRING OPTIONS(description="Program's genre name. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.genre.name")
    > OPTIONS(description="Genre information associated with the program."),
    url_list          ARRAY<STRUCT<
        url           STRING OPTIONS(description="Program's URL. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.urlProgram.url"),
        brand         STRING OPTIONS(description="Brand associated with the program URL. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.urlProgram.brand"),
        createdAt     TIMESTAMP OPTIONS(description="Timestamp when the URL was created. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.urlProgram.createdAt"),
        updatedAt     TIMESTAMP OPTIONS(description="Timestamp when the URL was last updated. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.urlProgram.updatedAt")
    >> OPTIONS(description="List of URLs associated with the program."),
    collection_list   ARRAY<STRUCT<
        type          STRING OPTIONS(description="Collection type. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.collection.type"),
        title         STRING OPTIONS(description="Collection title. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.collection.title"),
        country       STRING OPTIONS(description="Collection country. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.collection.country"),
        releasedYear  INT64  OPTIONS(description="Year of release for the collection. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.collection.releasedYear"),
        originalTitle STRING OPTIONS(description="Original title of the collection. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.collection.originalTitle"),
        category      STRING OPTIONS(description="Category of the collection. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.genre.shortTitle"),
        genre         STRING OPTIONS(description="Genre of the collection. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.genre.name")
    >> OPTIONS(description="Hierarchy of collections associated with the program."),
    people_list       ARRAY<STRING> OPTIONS(description="List of people associated with the program. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.person.searchField")
)
OPTIONS(description="Refined program data normalized from different datasets within {{ params.bq_project.mirror }}.store_tel_epg_aurora.\n"||
                    "Includes program details, genres, URLs, collections, and associated people.\n"||
                    "DAG: {{ dag.dag_id }}.\n"||
                    "Sync: daily.");

TRUNCATE TABLE `{{ params.bq_project.mirror }}.refined_data.program`;

INSERT INTO `{{ params.bq_project.mirror }}.refined_data.program`
-- List of collections (seasons, shows, etc)
WITH RECURSIVE collection_hierarchy AS (
  -- Anchor member: Start with the given collection(s)
  SELECT
    c.id,
    c.type,
    c.title,
    c.country,
    c.releasedYear,
    c.originalTitle,
    c.parentCollectionId,
    ARRAY<STRUCT<
      type STRING,
      title STRING,
      country STRING,
      releasedYear INT64,
      originalTitle STRING,
      category STRING,
      genre STRING
    >>[STRUCT(
      c.type,
      c.title,
      c.country,
      c.releasedYear,
      c.originalTitle,
      g.shortTitle,
      g.name
    )] AS hierarchy
  FROM `{{ params.bq_project.mirror }}.store_tel_epg_aurora.collection` c
  LEFT JOIN `{{ params.bq_project.mirror }}.store_tel_epg_aurora.genre` g
    ON c.formatGenreId = g.id

  UNION ALL

  -- Recursive member: Join the table to itself to find parents and build the hierarchy
  SELECT
    c.id,
    c.type,
    c.title,
    c.country,
    c.releasedYear,
    c.originalTitle,
    c.parentCollectionId,
    ARRAY_CONCAT(
      ch.hierarchy,
      [STRUCT(
        c.type,
        c.title,
        c.country,
        c.releasedYear,
        c.originalTitle,
        g.shortTitle,
        g.name
      )]
    ) AS hierarchy
  FROM `{{ params.bq_project.mirror }}.store_tel_epg_aurora.collection` c
  JOIN collection_hierarchy ch
    ON c.id = ch.parentCollectionId
  LEFT JOIN `{{ params.bq_project.mirror }}.store_tel_epg_aurora.genre` g
    ON c.formatGenreId = g.id
),

-- List of people (actors, TV presenters, singers, etc)
people AS (
  SELECT
    pp.programId,
    ARRAY_AGG(p.searchField) AS people
  FROM `{{ params.bq_project.mirror }}.store_tel_epg_aurora.programPerson` pp
  LEFT JOIN `{{ params.bq_project.mirror }}.store_tel_epg_aurora.person` p
    ON pp.personId = p.id
  GROUP BY pp.programId
),

-- List of genres and categories
genre AS (
  SELECT
    id,
    shortTitle AS category,
    name AS genre
  FROM `{{ params.bq_project.mirror }}.store_tel_epg_aurora.genre`
),

-- List of URLs related to programs
urls AS (
  SELECT
    programId,
    ARRAY_AGG(STRUCT(url, brand, createdAt, updatedAt)) AS url_list
  FROM `{{ params.bq_project.mirror }}.store_tel_epg_aurora.urlProgram`
  GROUP BY programId
)

SELECT
  pa.boneUUID AS program_id,
  STRUCT(
    pa.title,
    pa.country,
    pa.releasedYear,
    pa.synopsis
  ) AS info,
  STRUCT(
    g.category,
    g.genre
  ) AS genre,
  url.url_list,
  co.hierarchy AS collection_list,
  p.people AS people_list
FROM `{{ params.bq_project.mirror }}.store_tel_epg_aurora.program` pa
LEFT JOIN urls url
  ON url.programId = pa.id
LEFT JOIN genre g
  ON pa.formatGenreId = g.id
LEFT JOIN collection_hierarchy co
  ON pa.collectionId = co.id
LEFT JOIN people p
  ON pa.id = p.programId;
