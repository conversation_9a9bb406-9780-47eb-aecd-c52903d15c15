-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project.mirror }}.refined_data.channel`(
    channel_id        STRING NOT NULL OPTIONS(description="Channel ID from the source. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.channel.id"),
    info              STRUCT<
        title         STRING OPTIONS(description="Channel title. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.channel.title"),
        isActive      INT64 OPTIONS(description="Indicates whether the channel is active. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.channel.isActive"),
        hasReplayPage INT64 OPTIONS(description="Indicates whether the channel has a replay page. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.channel.hasReplayPage"),
        category      STRING OPTIONS(description="Category of the channel. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.channelCategory.name")
    > OPTIONS(description="Channel's general information."),
    bouquets_list     ARRAY<STRUCT<
        bouquet       STRING OPTIONS(description="Name of the bouquet. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.bouquet.name"),
        provider      STRING OPTIONS(description="Provider of the bouquet. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.bouquet.provider"),
        `group`       STRING OPTIONS(description="Group associated with the bouquet. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.channelBouquet.group"),
        `order`       INT64 OPTIONS(description="Order of the bouquet. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.channelBouquet.order"),
        isFree        INT64 OPTIONS(description="Indicates whether the bouquet is free. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.channelBouquet.isFree")
    >> OPTIONS(description="List of bouquets associated with the channel."),
    url_list          ARRAY<STRUCT<
        url           STRING OPTIONS(description="Channel's URL. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.urlChannel.url"),
        brand         STRING OPTIONS(description="Brand associated with the channel URL. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.urlChannel.brand"),
        createdAt     TIMESTAMP OPTIONS(description="Timestamp when the URL was created. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.urlChannel.createdAt"),
        updatedAt     TIMESTAMP OPTIONS(description="Timestamp when the URL was last updated. Ref: {{ params.bq_project.mirror }}.store_tel_epg_aurora.urlChannel.updatedAt")
    >> OPTIONS(description="List of URLs associated with the channel.")
)
OPTIONS(description="Refined channel data normalized from different datasets within {{ params.bq_project.mirror }}.store_tel_epg_aurora.\n"||
                    "Includes channel details, bouquets, URLs, and associated categories.\n"||
                    "Table is partitioned by createdAt timestamp with yearly granularity.\n"||
                    "DAG: {{ dag.dag_id }}.\n"||
                    "Sync: daily.");

TRUNCATE TABLE `{{ params.bq_project.mirror }}.refined_data.channel`;

INSERT INTO `{{ params.bq_project.mirror }}.refined_data.channel`
WITH urls AS (
  SELECT
    channelId,
    ARRAY_AGG(
      STRUCT(
        url,
        brand,
        createdAt,
        updatedAt
      )
    ) AS url_list
  FROM `{{ params.bq_project.mirror }}.store_tel_epg_aurora.urlChannel`
  GROUP BY channelId
),

bouquets AS (
  SELECT
    cb.channelId,
    ARRAY_AGG(
      STRUCT(
        b.name AS bouquet,
        b.provider,
        cb.group,
        cb.order,
        cb.isFree
      )
    ) AS bouquets_list
  FROM `{{ params.bq_project.mirror }}.store_tel_epg_aurora.bouquet` b
  LEFT JOIN `{{ params.bq_project.mirror }}.store_tel_epg_aurora.channelBouquet` cb
    ON b.id = cb.bouquetId
  GROUP BY cb.channelId
),

category AS (
  SELECT
    id,
    name AS category
  FROM `{{ params.bq_project.mirror }}.store_tel_epg_aurora.channelCategory`
)

SELECT
  c.boneUUID,
  STRUCT(
    c.title,
    c.isActive,
    c.hasReplayPage,
    cat.category
  ) AS info,
  b.bouquets_list,
  url.url_list
FROM `{{ params.bq_project.mirror }}.store_tel_epg_aurora.channel` c
LEFT JOIN urls url
  ON url.channelId = c.id
LEFT JOIN bouquets b
  ON c.id = b.channelId
LEFT JOIN category cat
  ON c.channelCategoryId = cat.id;
