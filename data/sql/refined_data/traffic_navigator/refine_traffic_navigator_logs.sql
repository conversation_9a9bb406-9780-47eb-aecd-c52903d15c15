-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `refined_data.traffic_navigator`
(
    project_id          STRING               OPTIONS(description="The Google Cloud project ID where the request originated."),
    service_id          STRING               OPTIONS(description="The ID of the service handling the request (e.g., pxl, selecta)."),
    request_method      STRING               OPTIONS(description="The HTTP request method (e.g., GET, POST)."),
    request_url         STRING               OPTIONS(description="The full request URL."),
    request_origin      STRING               OPTIONS(description="The protocol and domain of the request URL (e.g., https://www.example.com)."),
    request_path_full   STRING               OPTIONS(description="The full path of the request URL after the domain, including query parameters and fragments (e.g., /users/123?p=1)."),
    request_path_base   STRING               OPTIONS(description="The base path of the request URL, excluding query parameters and fragments (e.g., /api/v1)."),
    request_subpath     STRING               OPTIONS(description="The path after the base path, excluding query parameters and fragments (e.g., /users/123)."),
    status              INT64               OPTIONS(description="The HTTP status code of the response (e.g., 200, 404, 500)."),
    remote_ip           STRING               OPTIONS(description="The remote IP address of the client making the request."),
    user_agent          STRING               OPTIONS(description="The User-Agent string of the client making the request."),
    user_agent_category STRING               OPTIONS(description="The category of the user agent (e.g., Browser, Mobile, Bot)."),
    latency             FLOAT64              OPTIONS(description="The latency of the request, representing the time taken to process the request."),
    latency_ms          INT64                OPTIONS(description="The latency of the request in milliseconds."),
    timestamp           TIMESTAMP            OPTIONS(description="The timestamp of the request.")
)
PARTITION BY TIMESTAMP_TRUNC(timestamp, DAY)
OPTIONS(
    description="This table contains traffic data from Cloud Run and App Engine logs.\n\n"
              ||"DAG: {{ dag.dag_id }}.\n\n"
              ||"Sync: daily."
);

-- create tmp table for recent logs
CREATE TABLE IF NOT EXISTS `temp.traffic_navigator_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}`
LIKE `refined_data.traffic_navigator`;

TRUNCATE TABLE `temp.traffic_navigator_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}`;

INSERT INTO `temp.traffic_navigator_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}`
(project_id, service_id,
 request_method, request_url, request_origin, request_path_full, request_path_base, request_subpath,
 status, remote_ip, user_agent, user_agent_category, latency, latency_ms, timestamp)
WITH cloud_run_logs AS (
    SELECT
        resource.labels.project_id AS project_id,
        labels.cloud_run_service_id AS service_id,
        httpRequest.requestMethod AS request_method,
        httpRequest.requestUrl AS request_url,
        REGEXP_EXTRACT(httpRequest.requestUrl, r'^(https?://[^/]+)') AS request_origin,
        REGEXP_EXTRACT(httpRequest.requestUrl, r'^https?://[^/]+(.*)') AS request_path_full,
        REGEXP_EXTRACT(httpRequest.requestUrl, r'^https?://[^/]+/([^/?#]+)') AS request_path_base,
        REGEXP_EXTRACT(httpRequest.requestUrl, r'^https?://[^/]+/[^/]+(.*)') AS request_subpath,
        httpRequest.status,
        httpRequest.remoteIp AS remote_ip,
        httpRequest.userAgent AS user_agent,
        CASE
            WHEN LOWER(httpRequest.userAgent) LIKE '%bot%' OR LOWER(httpRequest.userAgent) LIKE '%crawler%' THEN 'Bot'
            WHEN LOWER(httpRequest.userAgent) LIKE '%mobile%' THEN 'Mobile'
            ELSE 'Browser'
        END AS user_agent_category,
        httpRequest.latency,
        CAST(CAST(REGEXP_EXTRACT(CAST(httpRequest.latency AS STRING), r'(\d+\.?\d*)') AS BIGNUMERIC) * 1000 AS INT64) AS latency_ms,
        timestamp

    FROM `store_logs.run_googleapis_com_requests`
    {% if params.is_full != true %}
        WHERE DATE(timestamp) > DATE_SUB(CURRENT_DATE(), INTERVAL {{params.interval}})
    {% endif %}

), app_engine_logs AS (
    SELECT
        resource.labels.project_id AS project_id,
        CASE
          WHEN resource.labels.project_id like '%partners-pixels' AND resource.labels.module_id = 'default' THEN 'pxl'
          WHEN resource.labels.project_id like '%pref-center' AND resource.labels.module_id = 'default' THEN 'selecta'
          ELSE resource.labels.module_id
        END AS service_id,
        protoPayload.method  AS request_method,
        CONCAT('https://', protoPayload.host, protoPayload.resource) AS request_url,
        protoPayload.host AS request_origin,
        REGEXP_EXTRACT(protoPayload.resource, r'^(.*)') AS request_path_full,
        REGEXP_EXTRACT(protoPayload.resource, r'^/([^/?#]+)') AS request_path_base,
        REGEXP_EXTRACT(protoPayload.resource, r'^/[^/]+/?([^?#]*)') AS request_subpath,
        protoPayload.status,
        protoPayload.ip AS remote_ip,
        protoPayload.userAgent AS user_agent,
        CASE
            WHEN LOWER(protoPayload.userAgent) LIKE '%bot%' OR LOWER(protoPayload.userAgent) LIKE '%crawler%' THEN 'Bot'
            WHEN LOWER(protoPayload.userAgent) LIKE '%mobile%' THEN 'Mobile'
            ELSE 'Browser'
        END AS user_agent_category,
        protoPayload.latency,
        CAST(CAST(REGEXP_EXTRACT(CAST(protoPayload.latency AS STRING), r'(\d+\.?\d*)') AS BIGNUMERIC) * 1000 AS INT64) AS latency_ms,
        timestamp
    FROM `store_logs.appengine_googleapis_com_request_log`
    {% if params.is_full != true %}
        WHERE DATE(timestamp) > DATE_SUB(CURRENT_DATE(), INTERVAL {{params.interval}})
    {% endif %}
), union_logs AS (
  SELECT
    project_id, service_id,
    request_method, request_url, request_origin, request_path_full, request_path_base, request_subpath,
    status, remote_ip, user_agent, user_agent_category, latency, latency_ms, timestamp
  FROM cloud_run_logs
  UNION ALL
  SELECT
      project_id, service_id,
      request_method, request_url, request_origin, request_path_full, request_path_base, request_subpath,
      status, remote_ip, user_agent, user_agent_category, latency, latency_ms, timestamp
  FROM app_engine_logs
)
SELECT
    project_id, service_id,
    request_method, request_url, request_origin, request_path_full, request_path_base, request_subpath,
    status, remote_ip, user_agent, user_agent_category, latency, latency_ms, timestamp
FROM union_logs
GROUP BY ALL
;


{% if params.is_full %}
    TRUNCATE TABLE `refined_data.traffic_navigator`;
{% endif %}

MERGE `refined_data.traffic_navigator` dest
USING `temp.traffic_navigator_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}` source
ON dest.project_id = source.project_id
    AND dest.service_id = source.service_id
    AND dest.request_method = source.request_method
    AND dest.request_url = source.request_url
    AND dest.timestamp = source.timestamp
    AND dest.remote_ip = source.remote_ip
    # enough ??
WHEN MATCHED THEN
UPDATE
    SET request_origin = source.request_origin,
        request_path_full = source.request_path_full,
        request_path_base = source.request_path_base,
        request_subpath = source.request_subpath,
        status = source.status,
        user_agent = source.user_agent,
        user_agent_category = source.user_agent_category,
        latency = source.latency,
        latency_ms = source.latency_ms
WHEN NOT MATCHED THEN
  INSERT (project_id, service_id,
          request_method, request_url, request_origin, request_path_full, request_path_base, request_subpath,
          status, remote_ip, user_agent, user_agent_category, latency, latency_ms, timestamp)
  VALUES (source.project_id, source.service_id,
          source.request_method, source.request_url, source.request_origin, source.request_path_full, source.request_path_base, source.request_subpath,
          source.status, source.remote_ip, source.user_agent, source.user_agent_category, source.latency, source.latency_ms, source.timestamp)
;