-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- Create the persona_segment_affinity table if it doesn't exist
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.refined_data.persona_segment_affinity` (
    email_profile_master_id      INT64    NOT NULL       OPTIONS(description="Profile email master id : unique identifier from profile digitial 360"),
    segment_names                ARRAY<STRING>           OPTIONS(description="affinity tags as list"),
    gender            STRING                OPTIONS(description="Gender as enum=['F','M']"),
    age_bin           STRING                OPTIONS(description="Age bin as enum=['0-17','18-24','25-34','35-44','45-54','55-64','65+']"),
    region            STRING                OPTIONS(description="Region of residence"),
    brand_trigram     ARRAY<STRING>         OPTIONS(description="List of brands for which the profile has at least one active consent for this brand"),
    is_optin_nl       BOOLEAN   NOT NULL    OPTIONS(description="True if profile has at least one active nl consent"),
    is_optin_part     BOOLEAN   NOT NULL    OPTIONS(description="True if profile has at least one active part consent"),
    last_open_date    DATE                  OPTIONS(description="Last open made by the profile - all brands included"),
    last_click_date   DATE                  OPTIONS(description="Last click made by the profile - all brands included"),
    is_active         BOOLEAN   NOT NULL    OPTIONS(description="TRUE if last click was within 180 days or open within 90 days, FALSE otherwise"),
     PRIMARY KEY (email_profile_master_id) NOT ENFORCED
)
    PARTITION BY last_open_date
    OPTIONS(description="This tables contains socio-demo and brand data for affinity profiles\n" ||
                                "DAG: {{ dag.dag_id }}" ||
                                "Sync: Daily");

-- Delete existing records
TRUNCATE TABLE `{{ params.bq_project }}.refined_data.persona_segment_affinity`;

-- Insert new affinity data into persona_segment_affinity
INSERT INTO `{{ params.bq_project }}.refined_data.persona_segment_affinity`
WITH profiles_info AS (
    SELECT DISTINCT
        s.email_profile_master_id,
        SAFE_CAST(info.birthdate AS DATE) AS birthdate,
        info.gender AS gender,
        info.zipcode AS zipcode,
        consent AS consent_public_ref,
        segment_names,
        MAX(activities.email.last_open_date) AS last_open_date,
        MAX(activities.email.last_click_date) AS last_click_date
    FROM `{{ params.bq_project }}.generated_data.affinity_tag` AS s
    LEFT JOIN `{{ params.bq_project }}.business_data.profile_digital_360` AS pd360 ON (email_profile_master_id = id.email_profile_master_id)
    LEFT JOIN UNNEST(service.email.active.consents) AS consent
    GROUP BY ALL
) , profiles_info_enriched AS (
    SELECT
        pi.email_profile_master_id,
        pi.segment_names,
        CASE pi.gender WHEN 'F' THEN 'Femme' WHEN 'M' THEN 'Homme' ELSE '-' END AS gender,
        CASE
            WHEN FLOOR(DATE_DIFF(CURRENT_DATE(), pi.birthdate, DAY) / 365.25) BETWEEN 0 AND 17 THEN '0-17'
            WHEN FLOOR(DATE_DIFF(CURRENT_DATE(), pi.birthdate, DAY) / 365.25) BETWEEN 18 AND 24 THEN '18-24'
            WHEN FLOOR(DATE_DIFF(CURRENT_DATE(), pi.birthdate, DAY) / 365.25) BETWEEN 25 AND 34 THEN '25-34'
            WHEN FLOOR(DATE_DIFF(CURRENT_DATE(), pi.birthdate, DAY) / 365.25) BETWEEN 35 AND 44 THEN '35-44'
            WHEN FLOOR(DATE_DIFF(CURRENT_DATE(), pi.birthdate, DAY) / 365.25) BETWEEN 45 AND 54 THEN '45-54'
            WHEN FLOOR(DATE_DIFF(CURRENT_DATE(), pi.birthdate, DAY) / 365.25) BETWEEN 55 AND 64 THEN '55-64'
            WHEN FLOOR(DATE_DIFF(CURRENT_DATE(), pi.birthdate, DAY) / 365.25) >= 65 THEN '65+'
            ELSE '-'
        END AS age_bin,
        COALESCE(reg.name, '-') AS region,
        ARRAY_AGG(DISTINCT b.trigram) AS brand_trigram,
        MAX(IF(ec.type = 'nl', TRUE, FALSE)) as is_optin_nl,
        MAX(IF(ec.type = 'part', TRUE, FALSE)) AS is_optin_part,
        MAX(pi.last_open_date) AS last_open_date,
        MAX(pi.last_click_date) AS last_click_date
    FROM profiles_info AS pi
    LEFT JOIN `{{ params.bq_project }}.open_data.fr_departments` AS dep
    ON dep.code = SUBSTRING(pi.zipcode, 1, 2)
    LEFT JOIN `{{ params.bq_project }}.open_data.before_after_2016_fr_departments` AS bad
    ON bad.department_code = dep.code
    LEFT JOIN `{{ params.bq_project }}.open_data.fr_regions` AS reg
    ON reg.code = bad.new_region_code
    LEFT JOIN `{{ params.bq_project }}.store_karinto.email_consent` AS ec
    ON ec.public_ref = pi.consent_public_ref
    LEFT JOIN `{{ params.bq_project }}.store_karinto.brand` AS b
    ON b.id = ec.brand_id
    WHERE ec.type IN ('nl', 'part') AND b.trigram != 'GAL'
    GROUP BY ALL )
SELECT
    email_profile_master_id,
    segment_names,
    gender,
    age_bin,
    region,
    brand_trigram,
    is_optin_nl,
    is_optin_part,
    last_click_date,
    last_open_date,
    COALESCE((DATE_DIFF(CURRENT_DATE(), last_click_date, DAY) <= 180 OR DATE_DIFF(CURRENT_DATE(), last_open_date, DAY) <= 90), FALSE) AS is_active
FROM profiles_info_enriched
   GROUP BY ALL;
