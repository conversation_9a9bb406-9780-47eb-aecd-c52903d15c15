-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
-- Purpose of this script :
-- 1. Drop previous table
-- 2. Insert into the blank table information about profile email

-- Liste of information :
--  email, email_md5, id_client, firstname, lastname, gender, birthdate,
--  address, address_sup, postal_code, city


DECLARE current_run_timestamp STRING;

-- We retrieve the date at which the ''last snapshot'' table was populated.
SET current_run_timestamp = (
    SELECT FORMAT_TIMESTAMP("%c", CURRENT_TIMESTAMP(), "CET")
);

DROP TABLE IF EXISTS `{{ params.bq_project }}.{{ params.bq_dataset }}.{{ params.table }}`;
CREATE TABLE `{{ params.bq_project }}.{{ params.bq_dataset }}.{{ params.table }}` (
    email_md5                   STRING          OPTIONS(description="email_md5 from export_it_data"),
    email_sha256                STRING          OPTIONS(description="email_sha256 from export_it_data"),
    customer_id                 STRING          OPTIONS(description="id_client from export_it_data"),
    active_customer_id          ARRAY<STRING>   OPTIONS(description="array with all active customer_id for this email_sh256"),
    inactive_customer_id        ARRAY<STRING>   OPTIONS(description="array with all inactive customer_id for this email_sh256"),
    master_id_client            ARRAY<STRING>   OPTIONS(description="array with all master customer_id for this email_sh256, we should have only one"),
    is_active                   INT64           OPTIONS(description="Boolean to know if the master profile is active (not necessarily the profile)"),
    update_date                 TIMESTAMP       OPTIONS(description="Update date from export_it_data"),
    info STRUCT <
                email           STRING      OPTIONS(description="export_it_data.email"),
                firstname       STRING      OPTIONS(description="export_it_data.prenom"),
                lastname        STRING      OPTIONS(description="export_it_data.nom"),
                birthdate       STRING      OPTIONS(description="export_it_data.date_de_naissance"),
                gender          STRING      OPTIONS(description="export_it_data.genre"),
                address         STRING      OPTIONS(description="export_it_data.address"),
                address_sup     STRING      OPTIONS(description="export_it_data.address_supplementary"),
                zipcode         STRING      OPTIONS(description="export_it_data.zip_code"),
                city            STRING      OPTIONS(description="export_it_data.city"),
                rnvp_2          STRING      OPTIONS(description="Address information from customer_data"),
                rnvp_3          STRING      OPTIONS(description="Additional address information from customer_data"),
                rnvp_4          STRING      OPTIONS(description="Additional address information from customer_data"),
                rnvp_5          STRING      OPTIONS(description="Additional address information from customer_data"),
                rnvp_6          STRING      OPTIONS(description="Additional address information from customer_data"),
                address_is_routed BOOLEAN   OPTIONS(description="Flag indicating if the address is routed from customer_data"),
                country         STRING      OPTIONS(description="export_it_data.country"),
                cellular_phone  STRING      OPTIONS(description="Cellular phone from customer_data"),
                home_phone      STRING      OPTIONS(description="Home phone from customer_data"),
                is_mailing_optout BOOLEAN   OPTIONS(description="Flag indicating if the customer has opted out of marketing communication from customer_data"),
                is_company      BOOLEAN     OPTIONS(description="Flag indicating if the customer is a company from customer_data")
                >                           OPTIONS(description="personal information"),
    service STRUCT <
                first_sub_date         TIMESTAMP       OPTIONS(description="Date of first sub"),
                last_unsub_date        TIMESTAMP       OPTIONS(description="Date of last unsub"),
                providers              ARRAY<STRING>   OPTIONS(description="list of providers"),
                active      ARRAY<STRUCT<magazine_title STRING, magazine_ref INTEGER, brand_name STRING, subscribe_type STRING, is_groupeur BOOLEAN, subscription_is_routed BOOLEAN, thematic STRING, start_date DATE, end_date DATE>> OPTIONS(description="Array of active magazine subscriptions from customer_data"),
                inactive    ARRAY<STRUCT<magazine_title STRING, magazine_ref INTEGER, brand_name STRING, subscribe_type STRING, is_groupeur BOOLEAN, subscription_is_routed BOOLEAN, thematic STRING, start_date DATE, end_date DATE>> OPTIONS(description="Array of inactive magazine subscriptions from customer_data")
                >                                              OPTIONS(description="info about the service"),
    enrichment_data STRUCT <
                social_pro_category STRING      OPTIONS(description="Social professional category from customer_data"),
                marital_status      STRING      OPTIONS(description="Marital status from customer_data"),
                income              STRING      OPTIONS(description="Income from customer_data"),
                home_status         STRING      OPTIONS(description="Home status from customer_data"),
                study_level         STRING      OPTIONS(description="Study level from customer_data"),
                children            STRING      OPTIONS(description="Number of children from customer_data"),
                town_size           STRING      OPTIONS(description="Size of the town from customer_data"),
                home_owner          STRING      OPTIONS(description="Flag indicating if the customer is a home owner from customer_data")
                >                                              OPTIONS(description="Additional customer enrichment data")
) OPTIONS(
  expiration_timestamp=NULL,
  description="Table where all profile client abo (mag) info are stored.\n"
              || "We refined profile data from the pm-prod-business-abonnement.export_it_data dataset (while we're working on importing those tables to store_matrix_email)"
              || "\n\n"
              || "Daily updates thru the Airflow DAG {{ dag.dag_id }}. Content dated " || current_run_timestamp || " CET."
);



INSERT INTO `{{ params.bq_project }}.{{ params.bq_dataset }}.{{ params.table }}`
--create table if not exists `{{ params.bq_project }}.workspace.pca_new` as
WITH
jointure_titre_trigram AS (
    -- We use this CTE to join titre/marques with the trigram
    WITH jointure_all_titre_trigram AS (
        SELECT
            DISTINCT rt.TITRE       AS titre,
            CASE
                WHEN REGEXP_CONTAINS(regexp_replace(normalize(lower(rt.TITRE), NFD), r"\pM", ''),
                                        regexp_replace(normalize(lower(eb.brand_name), NFD), r"\pM", '')) THEN eb.brand_trigram
                WHEN REGEXP_CONTAINS(regexp_replace(normalize(lower(rt.MARQUE), NFD), r"\pM", ''),
                                        regexp_replace(normalize(lower(eb.brand_name), NFD), r"\pM", '')) THEN eb.brand_trigram
                WHEN lower(rt.TITRE) LIKE "%télé-loisirs%" THEN "TEL"
                WHEN lower(rt.TITRE) LIKE "%télé 2 semaine%" THEN "T2S"
            END                     AS brand_trigram
        FROM `pm-prod-business-abonnement.export_it_data.referentiel_titre` AS rt
        CROSS JOIN `refined_data.email_base` AS eb
        )

    SELECT *
    FROM jointure_all_titre_trigram
    WHERE brand_trigram IS NOT NULL
),

mag_subs AS (
    -- select all mags subscriptions
    SELECT
        e.EMAIL                                         AS email,
        TO_HEX(MD5(e.EMAIL))                            AS email_md5,
        TO_HEX(SHA256(e.EMAIL))                         AS email_sha256,
        a.CANAL_SOUSCRIPTION AS provider,

        -- We split if the id_client is an active or an inactive one
        IF(a.IS_ACTIVE = 1, COALESCE(e.customer_ref, p.customer_ref),NULL)  AS active_id_client,
        CASE
            WHEN a.IS_ACTIVE = 0 THEN COALESCE(e.customer_ref, p.customer_ref)
            -- In case an id is not present inside abonnement, we need to keep track of it AS inactive
            WHEN COALESCE(e.customer_ref, p.customer_ref) IS NOT NULL AND a.ID_CLIENT IS NULL THEN COALESCE(e.customer_ref, p.customer_ref)
        END  AS inactive_id_client,
        COALESCE(e.customer_ref, p.customer_ref)                                     AS id_client,
        a.IS_ACTIVE                                     AS IS_ACTIVE,

        IF(COALESCE(cast(e.IS_MASTER as integer),0) = 1, COALESCE(e.customer_ref, p.customer_ref), NULL)  AS master_id_client,

        -- We are splitting magazines and brand if the profile is active or not
        IF(a.IS_ACTIVE = 1, rt.TITRE, NULL)               AS active_magazines,
        IF(a.IS_ACTIVE = 0, rt.TITRE, NULL)               AS inactive_magazines,
        IF(a.IS_ACTIVE = 1, jtt.brand_trigram, NULL)      AS active_trigram,
        IF(a.IS_ACTIVE = 0, jtt.brand_trigram, NULL)      AS inactive_trigram,

        -- We only keep date for active services
        IF(a.IS_ACTIVE = 1, DATE_DEBUT_ABO, NULL) AS first_sub_date,
        -- DATE_FIN_ABO can be in the future ; ignore those cases
        IF(a.IS_ACTIVE = 1, IF(DATE_FIN_ABO < CURRENT_DATE(), DATE_FIN_ABO, NULL), NULL) AS last_unsub_date,
    FROM `{{ params.bq_project }}.store_matrix_postal.email` AS e
    FULL OUTER JOIN `{{ params.bq_project }}.store_matrix_postal.profile` p on e.customer_ref = p.customer_ref
    LEFT JOIN `pm-prod-business-abonnement.export_it_data.abonnement` AS a ON a.ID_CLIENT = COALESCE(e.customer_ref, p.customer_ref)
    LEFT JOIN `pm-prod-business-abonnement.export_it_data.referentiel_titre` AS rt ON rt.ID_TITRE = a.ID_TITRE
    LEFT JOIN jointure_titre_trigram AS jtt ON jtt.titre = rt.TITRE
),

mag_only_active_agg as (
    -- We aggregate all magazines and brand active or not with the emails info
    SELECT id_client, email,   email_md5,  email_sha256, -- emails info
        ARRAY_AGG(DISTINCT active_magazines IGNORE NULLS ORDER BY active_magazines)     AS active_magazines,
    FROM mag_subs
    GROUP BY 1,2,3,4
),

mag_sub_agg AS (
    -- We aggregate all magazines and active brands or not with the emails info
   SELECT
        -- emails info
        ms.id_client,
        ms.email,
        ms.email_md5,
        ms.email_sha256,
        ARRAY_AGG(DISTINCT ms.id_client  IGNORE NULLS)                                  AS array_id_client,
        ARRAY_AGG(DISTINCT active_id_client  IGNORE NULLS)                              AS active_array_id_client,
        ARRAY_AGG(DISTINCT inactive_id_client  IGNORE NULLS)                            AS inactive_array_id_client,
        ARRAY_AGG(DISTINCT master_id_client  IGNORE NULLS)                              AS array_master_id_client,
        ARRAY_AGG(DISTINCT provider  IGNORE NULLS)                                      AS providers,
        --ARRAY_AGG(DISTINCT active_magazines IGNORE NULLS ORDER BY active_magazines)     AS active_magazines,
        ARRAY_AGG(DISTINCT IF(inactive_magazines NOT IN UNNEST(mog.active_magazines),
                              inactive_magazines, NULL)  IGNORE NULLS )                 AS inactive_magazines,
        ARRAY_AGG(DISTINCT active_trigram IGNORE NULLS ORDER BY active_trigram)         AS active_trigram,
        ARRAY_AGG(DISTINCT inactive_trigram IGNORE NULLS ORDER BY inactive_trigram)     AS inactive_trigram,
        MIN(first_sub_date)                                                             AS first_sub_date,
        MAX(last_unsub_date)                                                            AS last_unsub_date
    FROM mag_subs AS ms
    JOIN mag_only_active_agg AS mog ON mog.id_client = ms.id_client
    GROUP BY 1,2,3,4

),

mag_subs_with_status AS (
  -- Here, we format date and compute magazine status, depending on last_unsub_date
  SELECT
    msa.id_client,
    msa.email,
    msa.email_md5,
    msa.email_sha256,
    array_id_client,
    active_array_id_client,inactive_array_id_client,array_master_id_client,
    providers,
    active_magazines, inactive_magazines, active_trigram, inactive_trigram,
    --Use timestamp for first_sub_date and last_unsub_date cf mag_profile
    (FORMAT_DATE("%Y-%m-%d", first_sub_date)) AS first_sub_date,
    (FORMAT_DATE("%Y-%m-%d", last_unsub_date)) AS last_unsub_date,
    IF(last_unsub_date IS NULL, 'sub', 'unsub')  AS mag_status
  FROM mag_sub_agg AS msa
  JOIN mag_only_active_agg AS moaa ON moaa.id_client = msa.id_client

),

mag_profile AS (
    -- get profiles information
    SELECT
        m.email,
        m.email_md5,
        m.email_sha256,
        m.active_array_id_client,
        m.inactive_array_id_client,
        id_client,
        m.array_master_id_client,
        p.DATE_DERNIERE_MAJ                             AS update_date,

        -- Personal informationd about the person, by email_sha and id_client
        p.PRENOM                                        AS firstname,
        p.NOM                                           AS lastname,
        -- we clean birthdate is not good format if before 1925 or after current year
        IF(SAFE_CAST(FORMAT_DATE("%Y-%m-%d", p.DATE_NAISSANCE) AS DATE) IS NOT NULL
                    AND EXTRACT(YEAR FROM DATE(FORMAT_DATE("%Y-%m-%d", p.DATE_NAISSANCE)))>1925
                    AND EXTRACT(YEAR FROM DATE(FORMAT_DATE("%Y-%m-%d", p.DATE_NAISSANCE)))<EXTRACT(YEAR FROM CURRENT_DATE()),
            FORMAT_DATE("%Y-%m-%d", p.DATE_NAISSANCE), NULL) AS birthdate,
        --- to be deleted FORMAT_DATE("%Y-%m-%d", p.DATE_NAISSANCE)       AS birthdate,
        -- normalize gender data
        CASE p.GENRE
            WHEN 'Homme' THEN 'M'
            WHEN 'Femme' THEN 'F'
            ELSE NULL
        END                                             AS gender,
        a.RUE                                           AS address,
        a.INFO_SUPP_RUE                                 AS address_sup,

        -- if 5 digits, we keep it
        -- if 4 digit but CH or BE we keep it
        -- else we don't keep it
        CASE
            WHEN
                    CHAR_LENGTH(a.CODE_POSTAL) = 5
                AND SAFE_CAST(a.CODE_POSTAL AS INT64) IS NOT NULL
                AND a.CODE_POSTAL != "00000"
                THEN a.CODE_POSTAL
            WHEN
                    a.CODE_PAYS IN ("CH","BE")
                AND CHAR_LENGTH(a.CODE_POSTAL) = 4
                AND SAFE_CAST(a.CODE_POSTAL AS INT64) IS NOT NULL
                AND a.CODE_POSTAL != "0000"
                THEN a.CODE_POSTAL
            ELSE NULL
        END AS zipcode,

        -- old
        --a.CODE_POSTAL                                   AS zipcode,
        a.VILLE                                         AS city,
        a.CODE_PAYS                                     AS country,

        -- info about providers
        providers,
        -- Informations about magazines and brand
        m.active_magazines,
        m.inactive_magazines,
        m.active_trigram,
        m.inactive_trigram,

        --Use timestamp for first_sub_date and last_unsub_date
        TIMESTAMP(DATE (m.first_sub_date))              AS first_sub_date,
        TIMESTAMP(DATE (m.last_unsub_date))             AS last_unsub_date,

        -- whole database is considered prisma
        ['prisma']                                      AS owner,

    FROM mag_subs_with_status AS m, unnest(m.array_id_client)                AS id_client
    LEFT JOIN `pm-prod-business-abonnement.export_it_data.profil`   AS p ON p.ID_CLIENT = id_client
    LEFT JOIN `pm-prod-business-abonnement.export_it_data.adresse`  AS a ON a.ID_CLIENT = id_client

),

grouped_firstname AS (
    -- This cte is used to group the email_sha256 with a valid firstname if exist
    -- it's used in the next CTE to linked a client_id with the firstname
    SELECT id_client,
    MAX(firstname) AS firstname,
    FROM mag_profile
    GROUP BY 1

) ,

grouped_cte AS (
    -- This cte is used to retrieve information from a profile with the client_id and the firstname
    -- (we now aggregate over client_id so this is obsolete AS we already ensured the unicity)
    SELECT
        mp.id_client,
        IF(mp.firstname IS NULL, grouped.firstname,mp.firstname) AS firstname ,
        MAX(lastname)               AS lastname,
        MAX(birthdate)              AS birthdate,
        MAX(gender)                 AS gender,
        MAX(address)                AS address,
        MAX(address_sup)            AS address_sup,
        MAX(zipcode)                AS zipcode,
        MAX(city)                   AS city,
        MAX(country)                AS country,
    FROM mag_profile                 AS mp
    LEFT JOIN grouped_firstname     AS grouped ON grouped.id_client = mp.id_client
    GROUP BY 1,2

),

only_active AS (
    -- This CTE is used to keep only an active client_id and the last updated one
    SELECT * EXCEPT(rank)
    FROM(
        SELECT
            -- info on profiles
            mp.email, mp.email_md5, mp.email_sha256,
            -- info on active and inactive client_id
            mp.active_array_id_client, mp.inactive_array_id_client, mp.id_client,
            mp.array_master_id_client AS master_id_client,
            mp.update_date,
            gc.* except(id_client),

            -- In case there are multiple profiles for a master, we take the last modified profile
            ROW_NUMBER() OVER (PARTITION BY mp.id_client
                              ORDER BY mp.update_date DESC) AS rank,

            -- info on mag and brands and providers
            mp.providers,
            mp.active_magazines, mp.inactive_magazines, mp.active_trigram, mp.inactive_trigram,
            mp.first_sub_date,mp.last_unsub_date, mp.owner,
            IF(mp.id_client IN UNNEST(active_array_id_client),1,0) AS is_active
        FROM mag_profile AS mp
        JOIN grouped_cte AS gc ON mp.id_client  = gc.id_client
        -- If it's a postal base profile, the condition doesn't apply
        WHERE  mp.id_client IN UNNEST(IF(email IS NULL, [mp.id_client], array_master_id_client))
        )
    WHERE  rank = 1
), customer_data AS (
    SELECT
        p.customer_ref,
        p.cellular_phone,
        p.home_phone,
        p.is_mailing_optout,
        p.is_company,
        a.rnvp_2,
        a.rnvp_3,
        a.rnvp_4,
        a.rnvp_5,
        a.rnvp_6,
        a.is_routed AS address_is_routed,
        ARRAY_AGG(IF(ms.is_active IS TRUE,STRUCT(ms.magazine_title, ms.magazine_ref, ms.brand_name, ms.subscribe_type, ms.is_groupeur, ms.is_routed AS subscription_is_routed, mt.thematic, ms.start_date, ms.end_date),NULL) IGNORE NULLS) AS active_subscriptions,
        ARRAY_AGG(IF(ms.is_active IS FALSE,STRUCT(ms.magazine_title, ms.magazine_ref, ms.brand_name, ms.subscribe_type, ms.is_groupeur, ms.is_routed AS subscription_is_routed, mt.thematic, ms.start_date, ms.end_date),NULL) IGNORE NULLS) AS inactive_subscriptions,
        ANY_VALUE(
            STRUCT(
                e.social_pro_category,
                e.marital_status,
                e.income,
                e.home_status,
                e.study_level,
                e.children,
                e.town_size,
                e.home_owner
            )
        ) AS enrichment_data
    FROM `{{ params.bq_project }}.store_matrix_postal.profile` p
    LEFT JOIN `{{ params.bq_project }}.store_matrix_postal.address` a ON p.customer_ref = a.customer_ref
    LEFT JOIN `{{ params.bq_project }}.store_matrix_postal.magazine_subscription` ms ON p.customer_ref = ms.customer_ref
    LEFT JOIN `{{ params.bq_project }}.store_matrix_postal.magazine_theme` mt ON ms.magazine_title = mt.magazine_title
    LEFT JOIN `{{ params.bq_project }}.store_matrix_postal.enrichment` e ON p.customer_ref = e.customer_ref
    GROUP BY ALL
)

SELECT
    oa.email_md5,
    oa.email_sha256,
    oa.id_client AS customer_id,
    oa.active_array_id_client AS active_customer_id,
    oa.inactive_array_id_client AS inactive_customer_id,
    oa.master_id_client,
    oa.is_active,
    oa.update_date,
    STRUCT(
        oa.email,
        oa.firstname,
        oa.lastname,
        oa.birthdate,
        oa.gender,
        oa.address,
        oa.address_sup,
        oa.zipcode,
        oa.city,
        cd.rnvp_2,
        cd.rnvp_3,
        cd.rnvp_4,
        cd.rnvp_5,
        cd.rnvp_6,
        cd.address_is_routed,
        oa.country,
        cd.cellular_phone,
        cd.home_phone,
        cd.is_mailing_optout,
        cd.is_company
    ) AS info,
    STRUCT(
        oa.first_sub_date,
        oa.last_unsub_date,
        oa.providers,
        cd.active_subscriptions AS active,
        cd.inactive_subscriptions AS inactive
    ) AS service,
    cd.enrichment_data
FROM only_active oa
LEFT JOIN customer_data cd ON oa.id_client = cd.customer_ref;

