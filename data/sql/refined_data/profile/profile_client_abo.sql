-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
-- Purpose of this script :
-- 1. Drop previous table
-- 2. Insert into the blank table information about profile email 

-- Liste of information :
--  email, email_md5, id_client, firstname, lastname, gender, birthdate,
--  address, address_sup, postal_code, city


DECLARE current_run_timestamp STRING;

-- We retrieve the date at which the ''last snapshot'' table was populated.
SET current_run_timestamp = (
    SELECT FORMAT_TIMESTAMP("%c", CURRENT_TIMESTAMP(), "CET")
);

DROP TABLE IF EXISTS `{{ params.bq_project }}.{{ params.bq_dataset }}.{{ params.table }}`;
CREATE TABLE `{{ params.bq_project }}.{{ params.bq_dataset }}.{{ params.table }}` (
    email_md5                   STRING          OPTIONS(description="email_md5 from export_it_data"),
    email_sha256                STRING          OPTIONS(description="email_sha256 from export_it_data"),
    customer_id                 STRING          OPTIONS(description="id_client from export_it_data"),
    active_customer_id          ARRAY<STRING>   OPTIONS(description="array with all active customer_id for this email_sh256"),
    inactive_customer_id        ARRAY<STRING>   OPTIONS(description="array with all inactive customer_id for this email_sh256"),
    master_id_client            ARRAY<STRING>   OPTIONS(description="array with all master customer_id for this email_sh256, we should have only one"),
    is_active                   INT64           OPTIONS(description="Boolean to know if the profil the master profil is active (not necessary the profil)"),
    update_date                 TIMESTAMP       OPTIONS(description="date de maj from export_it_data"),
    info STRUCT <
                email           STRING      OPTIONS(description="email from export_it_data"),
                firstname       STRING      OPTIONS(description="prenom from export_it_data"),
                lastname        STRING      OPTIONS(description="nom from export_it_data"),
                birthdate       STRING      OPTIONS(description="date de naissance from export_it_data"),
                gender          STRING      OPTIONS(description="genre from export_it_data"),
                address         STRING      OPTIONS(description="rue from export_it_data"),
                address_sup     STRING      OPTIONS(description="rue_supp from export_it_data"),
                zipcode         STRING      OPTIONS(description="postal code from export_it_data"),
                city            STRING      OPTIONS(description="ville from export_it_data"),
                country         STRING      OPTIONS(description="code pays from export_it_data")
                >                           OPTIONS(description="personnal information"),

    service STRUCT <
                first_sub_date         TIMESTAMP       OPTIONS(description="premiere date abonnement"),
                last_unsub_date        TIMESTAMP       OPTIONS(description="derniere date desabonnement"),
                providers              ARRAY<STRING>   OPTIONS(description="list of providers"),
                active      STRUCT <
                                    magazines  ARRAY<STRING>   OPTIONS(description="titres des magazines actifs"),
                                    marques    ARRAY<STRING>   OPTIONS(description="noms des marques associées"),
                                    owner      ARRAY<STRING>   OPTIONS(description="source owner")
                                    >                          OPTIONS(description="info about active profile"),
                inactive    STRUCT <
                                    magazines  ARRAY<STRING>   OPTIONS(description="titre des magazines inactifs"),
                                    marques    ARRAY<STRING>   OPTIONS(description="noms des marques associées")
                                    >                          OPTIONS(description="info about inactive profile")
                >                                              OPTIONS(description="info about the service mag actif")   
) OPTIONS(
  expiration_timestamp=NULL,
  description="Table where all profilee client abo (mag) info are stored.\n"
              || "We refined profile data from the pm-prod-business-abonnement.export_it_data dataset"
              || "\n\n"
              || "Daily updates thru the Airflow DAG {{ dag.dag_id }}. Content dated " || current_run_timestamp || " CET."
);


INSERT INTO `{{ params.bq_project }}.{{ params.bq_dataset }}.{{ params.table }}`
WITH jointure_titre_trigram AS (
    -- We use this CTE to join titre/marques with the trigram
    WITH jointure_all_titre_trigram AS (
        SELECT
            DISTINCT rt.TITRE       AS titre,
            CASE
                WHEN REGEXP_CONTAINS(regexp_replace(normalize(lower(rt.TITRE), NFD), r"\pM", ''),
                                        regexp_replace(normalize(lower(eb.brand_name), NFD), r"\pM", '')) THEN eb.brand_trigram
                WHEN REGEXP_CONTAINS(regexp_replace(normalize(lower(rt.MARQUE), NFD), r"\pM", ''),
                                        regexp_replace(normalize(lower(eb.brand_name), NFD), r"\pM", '')) THEN eb.brand_trigram
                WHEN lower(rt.TITRE) LIKE "%télé-loisirs%" THEN "TEL"
                WHEN lower(rt.TITRE) LIKE "%télé 2 semaine%" THEN "T2S"
            END                     AS brand_trigram
        FROM `pm-prod-business-abonnement.export_it_data.referentiel_titre` AS rt
        CROSS JOIN `refined_data.email_base` AS eb
        )

    SELECT *
    FROM jointure_all_titre_trigram
    WHERE brand_trigram is not null
), mag_subs AS (
    -- select all mags subscriptions
    SELECT
        e.EMAIL                                         AS email,
        TO_HEX(MD5(e.EMAIL))                            AS email_md5,
        TO_HEX(SHA256(e.EMAIL))                         AS email_sha256,
        a.CANAL_SOUSCRIPTION as provider,

        -- We split if the id_client is an active or an inactive one
        CASE WHEN a.IS_ACTIVE = 1 THEN e.ID_CLIENT END  AS active_id_client,
        CASE
            WHEN a.IS_ACTIVE = 0 THEN e.ID_CLIENT
            -- In case an id is not present inside abonnement, we need to keep track of it as inactive
            WHEN e.ID_CLIENT IS NOT NULL AND a.ID_CLIENT IS NULL THEN e.ID_CLIENT
        END  AS inactive_id_client,
        e.ID_CLIENT                                     AS id_client,
        a.IS_ACTIVE                                     AS IS_ACTIVE,

        CASE WHEN e.IS_MASTER = 1 THEN e.ID_CLIENT END  AS master_id_client,


        -- We are splitting magazines and brand if the profile is active or not
        CASE WHEN a.IS_ACTIVE = 1 THEN rt.TITRE  END            AS active_magazines,
        CASE WHEN a.IS_ACTIVE = 0 THEN rt.TITRE  END            AS inactive_magazines,
        CASE WHEN a.IS_ACTIVE = 1 THEN jtt.brand_trigram END    AS active_trigram,
        CASE WHEN a.IS_ACTIVE = 0 THEN jtt.brand_trigram END    AS inactive_trigram,

        -- We only keep date for active services
        CASE WHEN a.IS_ACTIVE = 1 THEN DATE_DEBUT_ABO END AS first_sub_date,
        -- DATE_FIN_ABO can be in the future ; ignore those cases
        CASE WHEN a.IS_ACTIVE = 1 THEN IF(DATE_FIN_ABO < CURRENT_DATE(), DATE_FIN_ABO, NULL) END AS last_unsub_date,
    FROM `pm-prod-business-abonnement.export_it_data.email` AS e
    LEFT JOIN `pm-prod-business-abonnement.export_it_data.abonnement` AS a ON a.ID_CLIENT = e.ID_CLIENT
    LEFT JOIN `pm-prod-business-abonnement.export_it_data.referentiel_titre` AS rt ON rt.ID_TITRE = a.ID_TITRE
    LEFT JOIN jointure_titre_trigram as jtt ON jtt.titre = rt.TITRE
),mag_only_active_agg as (
    -- We aggregate all magazines and brand active or not with the emails info
    SELECT email,   email_md5,  email_sha256, -- emails info
        ARRAY_AGG(DISTINCT active_magazines IGNORE NULLS ORDER BY active_magazines)     AS active_magazines,
    FROM mag_subs
    GROUP BY 1,2,3

), mag_sub_agg as (
    -- We aggregate all magazines and brand active or not with the emails info
    SELECT ms.email,   ms.email_md5,  ms.email_sha256, -- emails info
        ARRAY_AGG(DISTINCT id_client  IGNORE NULLS)                                     AS array_id_client,
        ARRAY_AGG(DISTINCT active_id_client  IGNORE NULLS)                              AS active_array_id_client,
        ARRAY_AGG(DISTINCT inactive_id_client  IGNORE NULLS)                            AS inactive_array_id_client,
        ARRAY_AGG(DISTINCT master_id_client  IGNORE NULLS)                              AS array_master_id_client,
        ARRAY_AGG(DISTINCT provider  IGNORE NULLS)                                     AS providers,
        --ARRAY_AGG(DISTINCT active_magazines IGNORE NULLS ORDER BY active_magazines)     AS active_magazines,
        ARRAY_AGG(DISTINCT IF (inactive_magazines not in unnest(mog.active_magazines),inactive_magazines, null)  IGNORE NULLS ) AS inactive_magazines,
        ARRAY_AGG(DISTINCT active_trigram IGNORE NULLS ORDER BY active_trigram)         AS active_trigram,
        ARRAY_AGG(DISTINCT inactive_trigram IGNORE NULLS ORDER BY inactive_trigram)     AS inactive_trigram,
        MIN(first_sub_date)                                                             AS first_sub_date,
        MAX(last_unsub_date)                                                            AS last_unsub_date
    FROM mag_subs as ms
    JOIN mag_only_active_agg as mog ON mog.email= ms.email AND ms.email_md5 =mog.email_md5 AND ms.email_sha256 =mog.email_sha256
    GROUP BY 1,2,3
), mag_subs_with_status AS (
  -- Here, we format date and compute magazine status, depending on last_unsub_date
  SELECT
    msa.email,
    msa.email_md5,
    msa.email_sha256,
    array_id_client,
    active_array_id_client,inactive_array_id_client,array_master_id_client,
    providers,
    active_magazines, inactive_magazines, active_trigram, inactive_trigram,
    --Use timestamp for first_sub_date and last_unsub_date cf mag_profile
    (FORMAT_DATE("%Y-%m-%d", first_sub_date)) AS first_sub_date,
    (FORMAT_DATE("%Y-%m-%d", last_unsub_date)) AS last_unsub_date,
    IF(last_unsub_date IS NULL, 'sub', 'unsub')  AS mag_status
  FROM mag_sub_agg as msa
  JOIN mag_only_active_agg as moaa ON moaa.email_sha256 = msa.email_sha256

), mag_profile AS (
    -- get profile information
    SELECT
        m.email,
        m.email_md5,
        m.email_sha256,
        m.active_array_id_client,
        m.inactive_array_id_client,
        id_client,
        m.array_master_id_client,
        p.DATE_DERNIERE_MAJ                             AS update_date,

        -- Personal information about the person, by email_sha and id_client
        p.PRENOM                                        AS firstname,
        p.NOM                                           AS lastname,
        -- we clean birthdate is not good format if before 1925 or after current year
        IF(SAFE_CAST(FORMAT_DATE("%Y-%m-%d", p.DATE_NAISSANCE) AS DATE) IS NOT NULL
                    AND EXTRACT(YEAR FROM DATE(FORMAT_DATE("%Y-%m-%d", p.DATE_NAISSANCE)))>1925
                    AND EXTRACT(YEAR FROM DATE(FORMAT_DATE("%Y-%m-%d", p.DATE_NAISSANCE)))<EXTRACT(YEAR FROM CURRENT_DATE()),
            FORMAT_DATE("%Y-%m-%d", p.DATE_NAISSANCE), null) AS birthdate,
        --- to be deleted FORMAT_DATE("%Y-%m-%d", p.DATE_NAISSANCE)       AS birthdate,
        -- normalize gender data
        CASE p.GENRE
            WHEN 'Homme' THEN 'M'
            WHEN 'Femme' THEN 'F'
            ELSE null
        END                                             AS gender,
        a.RUE                                           AS address,
        a.INFO_SUPP_RUE                                 AS address_sup,

        -- if 5 digits, we keep it
        -- if 4 digit but CH or BE we keep it
        -- else we don't keep it
        CASE
            WHEN
                    CHAR_LENGTH(a.CODE_POSTAL) = 5
                AND SAFE_CAST(a.CODE_POSTAL AS INT64) IS NOT NULL
                AND a.CODE_POSTAL != "00000"
                THEN a.CODE_POSTAL
            WHEN
                    a.CODE_PAYS IN ("CH","BE")
                AND CHAR_LENGTH(a.CODE_POSTAL) = 4
                AND SAFE_CAST(a.CODE_POSTAL AS INT64) IS NOT NULL
                AND a.CODE_POSTAL != "0000"
                THEN a.CODE_POSTAL
            ELSE Null
        END AS zipcode,

        -- old
        --a.CODE_POSTAL                                   AS zipcode,
        a.VILLE                                         AS city,
        a.CODE_PAYS                                     AS country,

        -- info about providers
        providers,
        -- Informations about magazines and brand
        m.active_magazines,
        m.inactive_magazines,
        m.active_trigram,
        m.inactive_trigram,

        --Use timestamp for first_sub_date and last_unsub_date
        TIMESTAMP(DATE (m.first_sub_date))              AS first_sub_date,
        TIMESTAMP(DATE (m.last_unsub_date))             AS last_unsub_date,

        -- whole database is considered prisma
        ['prisma']                                      AS owner,

    FROM mag_subs_with_status AS m, unnest(m.array_id_client)                AS id_client
    LEFT JOIN `pm-prod-business-abonnement.export_it_data.profil`   AS p ON p.ID_CLIENT = id_client
    LEFT JOIN `pm-prod-business-abonnement.export_it_data.adresse`  AS a ON a.ID_CLIENT = id_client

), grouped_firstname as (
    -- This cte is used to group the email_sha256 with a valid firstname if exist
    -- it's used in the next CTE to link an email_sha256 with the firstname and not the client_id
    SELECT email_sha256,
    MAX(firstname) AS firstname,
    FROM mag_profile
    GROUP BY 1

) , grouped_cte AS (
    -- This cte is used to retrieve information from a profile with the email_sha and the firstname
    SELECT
        mp.email_sha256,
        CASE WHEN mp.firstname IS NULL THEN grouped.firstname
        ELSE mp.firstname END       AS firstname ,
        MAX(lastname)               AS lastname,
        MAX(birthdate)              AS birthdate,
        MAX(gender)                 AS gender,
        MAX(address)                AS address,
        MAX(address_sup)            AS address_sup,
        MAX(zipcode)                AS zipcode,
        MAX(city)                   AS city,
        MAX(country)                AS country,
    FROM mag_profile                 AS mp
    LEFT JOIN grouped_firstname     AS grouped ON grouped.email_sha256 = mp.email_sha256
    GROUP BY 1,2

), only_active as (
    -- This CTE is used to keep only an active client_id and the last updated one
    SELECT * EXCEPT(rank)
    FROM(
        SELECT
            -- info on profiles
            mp.email, mp.email_md5, mp.email_sha256,
            -- info on client_id active and inactive
            mp.active_array_id_client, mp.inactive_array_id_client, mp.id_client,
            mp.array_master_id_client AS master_id_client,
            mp.update_date,
            gc.* except(email_sha256),

            -- In case they are multiple profil for a master, we take the last modified profile
            ROW_NUMBER() OVER (PARTITION BY mp.email_sha256
                              ORDER BY mp.update_date, mp.firstname, mp.lastname, mp.gender, mp.birthdate  DESC) AS rank,

            -- info on mag and brands and providers
            mp.providers,
            mp.active_magazines, mp.inactive_magazines, mp.active_trigram, mp.inactive_trigram,
            mp.first_sub_date,mp.last_unsub_date, mp.owner,
            CASE
                WHEN mp.id_client in unnest(active_array_id_client) then 1 ELSE 0
            END AS is_active
        FROM mag_profile AS mp
        JOIN grouped_cte AS gc ON mp.email_sha256  = gc.email_sha256
        WHERE  mp.id_client in unnest(array_master_id_client)
        )
    WHERE  rank = 1
)

SELECT
    email_md5,
    email_sha256,
    id_client as customer_id,
    active_array_id_client as active_customer_id,
    inactive_array_id_client as inactive_customer_id,
    master_id_client,
    is_active,
    update_date,
    STRUCT(
        email,
        firstname,
        lastname,
        birthdate,
        gender,
        address,
        address_sup,
        zipcode,
        city,
        country
    ) AS info,
    STRUCT(
        first_sub_date,
        last_unsub_date,
        providers,
        STRUCT(
            active_magazines as magazines,
            active_trigram as marques,
            owner
        ) AS active,
        STRUCT(
            inactive_magazines as magazines,
            inactive_trigram as marques
        ) AS inactive
    ) AS service
FROM only_active
;