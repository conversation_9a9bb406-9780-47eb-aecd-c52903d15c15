-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- we use this sql to create a table from the view, if the table is not create or into a runtime error,
-- then the pd360 will not be impacted

DECLARE current_run_timestamp STRING;

-- We retrieve the date at which the ''last snapshot'' table was populated.
SET current_run_timestamp = (
    SELECT FORMAT_TIMESTAMP("%c", CURRENT_TIMESTAMP(), "CET")
);


-- create table with enriched information about profiles into the pd360
DROP TABLE IF EXISTS `{{ params.bq_project }}.{{ params.bq_dataset_store }}.{{ params.table_enriched }}`;
CREATE TABLE `{{ params.bq_project }}.{{ params.bq_dataset_store }}.{{ params.table_enriched }}` (


	email_sha256				STRING						OPTIONS(description="Clé du profil PMC"), 
	GENRE								STRING						OPTIONS(description="Genre déduit du prénom (NULL si le prénom n'est pas renseigné) et des données de naissances"), 
	CLASSE_AGE_PREDITE	STRING						OPTIONS(description="Classe d'âge prédite par le modèle développé pour le business abonnement, basé entre autres sur le prénom (NULL si le prénom n'est pas renseigné)"), 	
	CLASSE_AGE_VILLE		STRING						OPTIONS(description="Classe d'âge prédominante au niveau de la commune (NULL si le code postal n'est pas renseigné), déduite des données IRIS"), 
	NIVEAU_ETUDES				STRING						OPTIONS(description="Niveau d'études prédominant au niveau de la commune (NULL si le code postal n'est pas renseigné), déduit des données IRIS"), 	
	LIB_CSP							STRING						OPTIONS(description="Catégorie socio-professionnelle prédominante au niveau de la commune (NULL si le code postal n'est pas renseigné), déduite des données IRIS"), 	
	ENFANTS							STRING						OPTIONS(description="Nombre d'enfants le plus fréquent au niveau de la commune (NULL si le code postal n'est pas renseigné), déduit des données IRIS"), 	
	SITUATION_FAMILIALE	STRING						OPTIONS(description="Situation familiale prédominante au niveau de la commune (NULL si le code postal n'est pas renseigné), déduite des données IRIS"), 	
	TYPE_LOGEMENT				STRING						OPTIONS(description="Type de logement prédominant au niveau de la commune (NULL si le code postal n'est pas renseigné), déduit des données IRIS"), 	
	STATUT_HABITATION		STRING						OPTIONS(description="Statut d'habitation (maison ou appartement) prédominant au niveau de la commune (NULL si le code postal n'est pas renseigné), déduit des données IRIS"), 	
	N_PIECES						STRING						OPTIONS(description="Nombre de pièces par logement le plus courant au niveau de la commune (NULL si le code postal n'est pas renseigné), déduit des données IRIS"), 	
	TAILLE_LOGEMENT			STRING						OPTIONS(description="Taille de logement la plus courante au niveau de la commune (NULL si le code postal n'est pas renseigné), déduite des données IRIS"), 	
	VOLATILITE_MENAGES	STRING						OPTIONS(description="Nombre d'années depuis l'installation de la personne dans la commune le plus fréquent (NULL si le code postal n'est pas renseigné), déduit des données IRIS")

) OPTIONS(
  expiration_timestamp=NULL,
  description="Table where all enriched information about profiles into the pd360 are stored.\n"
              || "We store this inside bataset store"
              || "\n\n"
              || "Daily updates thru the Airflow DAG {{ dag.dag_id }}. Content dated " --|| current_run_timestamp || " CET."
);

INSERT INTO `{{ params.bq_project }}.{{ params.bq_dataset_store }}.{{ params.table_enriched }}`

SELECT *
FROM `pm-prod-business-abonnement.export_it_data.profil_digital_360_enriched`
-- to avoid duplicate (when predict is in error in the it bi team)
GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13
;