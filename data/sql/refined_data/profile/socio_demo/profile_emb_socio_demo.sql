-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- EMB
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.refined_data.profile_emb_socio_demo`(
    email_sha256        STRING      NOT NULL  OPTIONS(description="SHA-256 hashed email address of the user."),
    info                STRUCT<
        create_date     DATE        OPTIONS(description="Row creation date"),
        update_date     DATE        OPTIONS(description="Row last update date"),
        gender          STRING      OPTIONS(description="User's gender"),
        birthdate       DATE        OPTIONS(description="User's birthdate.")
    >                               OPTIONS(description="Socio-demographic data of the user."),
    PRIMARY KEY(email_sha256) NOT ENFORCED
)
OPTIONS(description="This table stores enriched socio-demographic data for users based on EMB enrichment data.\n"||
                    "DAG: {{ dag.dag_id }}. \n"||
                    "Sync: daily");

MERGE `{{ params.bq_project }}.refined_data.profile_emb_socio_demo` AS dst
USING(
  SELECT
    email_sha256,
    CURRENT_DATE() AS create_date,
    update_date,
    CASE
      WHEN civilite = 'M.' THEN 'M'
      WHEN civilite = 'Mme' THEN 'F'
      ELSE "(not set)"
    END AS gender,
    PARSE_DATE("%Y-%m-%d", birthdate) AS birthdate
  FROM `{{ params.bq_project }}.store_partner.emb_full_enrichment`
  GROUP BY email_sha256, update_date, birthdate, civilite
) AS ref
ON
  dst.email_sha256 = ref.email_sha256
WHEN MATCHED THEN
  UPDATE SET
    dst.info = STRUCT(
      ref.create_date AS create_date,
      ref.update_date AS update_date,
      ref.gender AS gender,
      ref.birthdate AS birthdate
    )
WHEN NOT MATCHED BY TARGET THEN
  INSERT(email_sha256, info)
  VALUES(ref.email_sha256, STRUCT(ref.create_date, ref.update_date, ref.gender, ref.birthdate));
