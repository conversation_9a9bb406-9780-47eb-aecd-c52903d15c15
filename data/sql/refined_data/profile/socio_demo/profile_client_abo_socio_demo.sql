-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- MAG
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.refined_data.profile_client_abo_socio_demo`(
    email_sha256          STRING      NOT NULL OPTIONS(description="SHA-256 hashed email address of the user."),
    info                  STRUCT<
        email             STRING      OPTIONS(description="Email from the user's profile."),
        firstname         STRING      OPTIONS(description="First name from the user's profile."),
        lastname          STRING      OPTIONS(description="Last name from the user's profile."),
        birthdate         STRING      OPTIONS(description="Birthdate from the user's profile."),
        gender            STRING      OPTIONS(description="Gender from the user's profile."),
        address           STRING      OPTIONS(description="Primary address from the user's profile."),
        address_sup       STRING      OPTIONS(description="Additional address information from the user's profile."),
        zipcode           STRING      OPTIONS(description="Postal code from the user's profile."),
        city              STRING      OPTIONS(description="City from the user's profile."),
        country           STRING      OPTIONS(description="Country code from the user's profile.")
    >                               OPTIONS(description="Personal information from the user's profile, stored as a structured record."),
    PRIMARY KEY(email_sha256) NOT ENFORCED
)
OPTIONS(description="This table stores enriched socio-demographic data for users based on their physical magazine subscriptions. \n"||
                    "DAG: {{ dag.dag_id }}. \n"||
                    "Sync: daily");

MERGE `{{ params.bq_project }}.refined_data.profile_client_abo_socio_demo` AS dst
USING (
SELECT 
    email_sha256,
    STRUCT(
        info.email AS email,
        info.firstname AS firstname,
        info.lastname AS lastname,
        info.birthdate AS birthdate,
        info.gender AS gender,
        info.address AS address,
        info.address_sup AS address_sup,
        info.zipcode AS zipcode,
        info.city AS city,
        info.country AS country
    ) AS info
FROM 
    `{{ params.bq_project }}.refined_data.profile_client_abo`) AS ref
ON 
  dst.email_sha256 = ref.email_sha256
WHEN MATCHED THEN 
  UPDATE SET 
    dst.info = ref.info
WHEN NOT MATCHED BY TARGET THEN 
  INSERT(email_sha256, info)
  VALUES(ref.email_sha256, ref.info);

