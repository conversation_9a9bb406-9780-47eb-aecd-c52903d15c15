-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DROP TABLE IF EXISTS `{{params.bq_project}}.{{params.bq_dataset}}.{{params.destination_table}}`;
CREATE TABLE IF NOT EXISTS `{{params.bq_project}}.{{params.bq_dataset}}.{{params.destination_table}}`
(
    email_sha256                STRING  OPTIONS(description="ref: store_matrix_email.profile_master_id.email_sha256"),
    email_profile_master_id     INTEGER OPTIONS(description="ref: store_matrix_email.profile_master_id.id"),
    customer_id                 STRING  OPTIONS(description="customer id as business abo client"),  
    pmc_email_profile_id        INTEGER OPTIONS(description="ref: store_matrix_pmc.profile_master_id_v2.id"),
    source                      STRING  OPTIONS(description="enum as = ['email', 'pmc', 'magazine', 'email & pmc', 'email & magazine', 'pmc & magazine'' ]"),  
    has_firstname               BOOLEAN OPTIONS(description="0/1. 1 if profile has a firstname"),  
    has_lastname                BOOLEAN OPTIONS(description="0/1. 1 if profile has a lastname"),      
    has_gender                  BOOLEAN OPTIONS(description="0/1. 1 if profile has a gender"),  
    gender                      STRING  OPTIONS(description="gender as enum = ['M', 'F', NULL]"),  
    gender_source               STRING  OPTIONS(description="enum as = []"),  
    has_birthdate               BOOLEAN OPTIONS(description="0/1. 1 if profile has a filled birthdate"),  
    age                         INTEGER OPTIONS(description="computed age from birthdate"),  
    age_source                  STRING  OPTIONS(description="enum = []"),  
    has_address                 BOOLEAN OPTIONS(description="0/1. 1 if profile has a filled address"),  
    address                     STRING  OPTIONS(description="profile address"),  
    has_zipcode                 BOOLEAN OPTIONS(description="0/1. 1 if profile has a filled zipcode"),  
    zipcode                     STRING  OPTIONS(description="profile zipcode"),  
    has_city                    BOOLEAN OPTIONS(description="0/1. 1 if profile has a filled city"),  
    city                        STRING  OPTIONS(description="profile city"),  
    has_study_level             BOOLEAN OPTIONS(description="0/1. 1 if profile has a filled study level"),  
    study_level                 STRING  OPTIONS(description="profile study level as enum = []"),  
    has_profession              BOOLEAN OPTIONS(description="0/1. 1 if profile has a filled profession"),  
    profession                  STRING  OPTIONS(description="profile profession as enum = []"),  
    has_is_single               BOOLEAN OPTIONS(description="0/1. 1 if profile has a filled is single"),  
    is_single                   BOOLEAN OPTIONS(description="0/1. 1 if profile is single"),  
    has_children                BOOLEAN OPTIONS(description="0/1. 1 if profile has children"),  
    has_accommodation_type      BOOLEAN OPTIONS(description="0/1. 1 if profile has a filled accommodation type"),  
    accommodation_type          STRING  OPTIONS(description="accommodation type as enum = []"),  
    has_housing_status          BOOLEAN OPTIONS(description="0/1. 1 if profile has a filled housing status"),  
    housing_status              STRING  OPTIONS(description="housing status as enum = []"),  
    has_how_many_rooms          BOOLEAN OPTIONS(description="0/1. 1 if profile has a filled how many rooms"),  
    how_many_rooms              STRING  OPTIONS(description="how many rooms profile has ?"),  
    has_area                    BOOLEAN OPTIONS(description="0/1. 1 if profile has a filled area"),  
    area                        STRING  OPTIONS(description="profile house area"),  
    has_is_stay_less_10_years   BOOLEAN OPTIONS(description="0/1. 1 if profile has a filled is stay less than 10 years"),  
    is_stay_less_10_years       BOOLEAN OPTIONS(description="0/1. 1 if profile is stay less than 10 years at the same address"),  
    has_valid_cgu               BOOLEAN OPTIONS(description="0/1. 1 if profile has a valid CGU"),  
    pmc_brand_trigram           STRING  OPTIONS(description="brand trigram"),  
    is_active_email             BOOLEAN OPTIONS(description="0/1. 1 if profile is sub at least to one email consent"),  
    is_active_magazine          BOOLEAN OPTIONS(description="0/1. 1 if profile is sub at least to one magazine"),  
    nb_active_consent           INTEGER OPTIONS(description="number of active consent"),  
    nb_active_brand             INTEGER OPTIONS(description="number of active brand"),  
    nb_active_magazine          INTEGER OPTIONS(description="number of active magazine"),  
    nb_active_mag_brand         INTEGER OPTIONS(description="number of active magazine brand"),  
    final_source                STRING  OPTIONS(description="source as enum = ['email', 'pmc', 'magazine']"),  
    region_name                 STRING  OPTIONS(description="region name before 2016"),  
    active_consent              STRING  OPTIONS(description="email consent public ref"),  
    consent_type                STRING  OPTIONS(description="enum = ['loy', 'nl', 'alert', 'part', 'crm', 'prosp']"),  
    active_brand                STRING  OPTIONS(description="consent brand trigram"),  
    active_magazine             STRING  OPTIONS(description="active magazine name") ,
    active_magazine_brand       STRING  OPTIONS(description="magazine brand trigram")
)
OPTIONS(description="This table contains an overview of digital profile 360."||
                    "DAG: {{ dag.dag_id }}. "||
                    "Sync: daily");


INSERT INTO `{{params.bq_project}}.{{params.bq_dataset}}.{{params.destination_table}}`
WITH profile_information AS(
    SELECT 
        id.email_sha256, 
        id.email_profile_master_id AS email_profile_master_id,
        id.mag_customer_id AS customer_id,
        id.pmc_profile_master_id AS pmc_email_profile_id,
        CASE WHEN id.email_profile_master_id IS NULL AND id.mag_customer_id IS NULL AND id.pmc_profile_master_id IS NULL THEN 'non connue'
        WHEN id.email_profile_master_id IS NOT NULL AND id.mag_customer_id IS NULL AND id.pmc_profile_master_id IS NULL THEN 'email'
        WHEN id.email_profile_master_id IS NULL AND id.mag_customer_id IS NOT NULL AND id.pmc_profile_master_id IS NULL THEN 'magazine'
        WHEN id.email_profile_master_id IS NULL AND id.mag_customer_id IS NULL AND id.pmc_profile_master_id IS NOT NULL THEN 'pmc'
        WHEN id.email_profile_master_id IS NOT NULL AND id.mag_customer_id IS NOT NULL AND id.pmc_profile_master_id IS NULL THEN 'email & magazine'
        WHEN id.email_profile_master_id IS NOT NULL AND id.mag_customer_id IS NULL AND id.pmc_profile_master_id IS NOT NULL THEN 'email & pmc'
        WHEN id.email_profile_master_id IS NULL AND id.mag_customer_id IS NOT NULL AND id.pmc_profile_master_id IS NOT NULL THEN 'magazine & pmc'
        WHEN id.email_profile_master_id IS NOT NULL AND id.mag_customer_id IS NOT NULL AND id.pmc_profile_master_id IS NOT NULL THEN 'magazine & pmc & email'
        ELSE '' 
        END AS source, 
        info.firstname!="undefined" OR info.firstname IS NOT NULL AS has_firstname,
        info.lastname!="undefined" OR info.lastname IS NOT NULL AS has_lastname,
        info.gender IS NOT NULL AND info.gender IN ('M', 'F') AS has_gender, 
        info.gender,
        info.relevance.gender AS gender_source,
        info.birthdate IS NOT NULL AS has_birthdate,
        DATE_DIFF(CURRENT_DATE(), PARSE_DATE('%Y-%m-%d',  info.birthdate), YEAR) AS age, 
        info.relevance.birthdate AS age_source, 
        info.adress IS NOT NULL AS has_address,
        LOWER(info.adress) AS address, 
        info.zipcode IS NOT NULL AS has_zipcode,
        info.zipcode  AS zipcode, 
        info.city IS NOT NULL AS has_city,
        UPPER(info.city) AS city, 
        info.enriched.study_level IS NOT NULL has_study_level,
        info.enriched.study_level AS study_level, 
        info.enriched.profession IS NOT NULL AS has_profession,
        info.enriched.profession AS profession,
        info.enriched.is_single IS NOT NULL AS has_is_single,
        info.enriched.is_single AS is_single,
        info.enriched.has_children AS has_children, 
        info.enriched.accommodation_type IS NOT NULL AS has_accommodation_type,
        info.enriched.accommodation_type AS accommodation_type,
        info.enriched.housing_status IS NOT NULL AS has_housing_status, 
        info.enriched.housing_status AS housing_status, 
        info.enriched.hm_rooms IS NOT NULL AS has_how_many_rooms,
        info.enriched.hm_rooms AS how_many_rooms, 
        info.enriched.surface IS NOT NULL AS has_area,
        info.enriched.surface AS area, 
        info.enriched.stayed_less_than_10_years IS NOT NULL AS has_is_stay_less_10_years,
        info.enriched.stayed_less_than_10_years AS is_stay_less_10_years, 
        service.pmc.is_valid_pmc AS has_valid_cgu,
        service.pmc.source_brand_trigram AS pmc_brand_trigram,
        ARRAY_LENGTH(service.email.active.consents)!=0 AS is_active_email, 
        ARRAY_LENGTH(service.mag.active.magazines)!=0 AS is_active_magazine, 
        ARRAY_LENGTH(service.email.active.consents) AS nb_active_consent, 
        ARRAY_LENGTH(service.email.active.brands) AS nb_active_brand,
        ARRAY_LENGTH(service.mag.active.magazines) AS nb_active_magazine, 
        ARRAY_LENGTH(service.mag.active.marques) AS nb_active_mag_brand
    FROM `{{params.bq_project}}.business_data.profile_digital_360`
), active_consent AS(
    -- get (consent, brand)
    SELECT 
        id.email_profile_master_id AS email_profile_master_id,
        active_consent,
        eb.brand_trigram AS active_brand,
        eb.consent_type AS consent_type
    FROM `{{params.bq_project}}.business_data.profile_digital_360` AS p, UNNEST(service.email.active.consents) AS active_consent
    LEFT JOIN `{{params.bq_project}}.refined_data.email_base` AS eb ON eb.consent_public_ref = active_consent
), active_magazine AS(
    -- get (magazine, brand)
    SELECT 
        --id.customer_id AS customer_id,
        id.email_sha256,
        active_magazine,
        mb.brand_trigram AS active_magazine_brand
    FROM `{{params.bq_project}}.business_data.profile_digital_360` AS p, UNNEST(service.mag.active.magazines) AS active_magazine
    LEFT JOIN `{{params.bq_project}}.refined_data.magazine_brand`AS mb ON mb.magazine_title = active_magazine
), add_final_source AS (
    SELECT 
        p.*, 
        CASE WHEN email_profile_master_id IS NOT NULL THEN 'email' ELSE NULL END AS final_source, 
    FROM profile_information AS p 
    UNION DISTINCT 
    SELECT 
        p.*, 
        CASE WHEN pmc_email_profile_id IS NOT NULL THEN 'pmc' ELSE NULL END AS final_source, 
    FROM profile_information AS p 
    UNION DISTINCT 
    SELECT 
        p.*, 
        CASE WHEN customer_id IS NOT NULL THEN 'magazine' ELSE NULL END AS final_source, 
    FROM profile_information AS p 
)
SELECT DISTINCT 
    pi.*,
    bar.former_name AS region_name,
    ac.active_consent,
    ac.consent_type,
    ac.active_brand, 
    am.active_magazine,
    am.active_magazine_brand
FROM add_final_source AS pi
LEFT JOIN active_consent AS ac ON pi.email_profile_master_id = ac.email_profile_master_id 
LEFT JOIN active_magazine AS am ON pi.email_sha256 = am.email_sha256
LEFT JOIN `{{params.bq_project}}.open_data.fr_departments` AS drf ON drf.code = SUBSTR(pi.zipcode,  1, 2)
LEFT JOIN `{{params.bq_project}}.open_data.fr_regions` AS rfr ON rfr.code = drf.region_code
LEFT JOIN `{{params.bq_project}}.open_data.before_after_2016_fr_regions` AS bar ON bar.new_code = rfr.code
-- to avoid duplication
WHERE pi.final_source IS NOT NULL;

