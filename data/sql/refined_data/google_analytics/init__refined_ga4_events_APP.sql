-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE OR REPLACE TABLE `{{ params.bq_project }}.refined_data.ga_events_APP_TEMPLATE`
(
    visit_date      DATE    NOT NULL    OPTIONS(description="Date of visit extracted from event date."),
    user_pseudo_id  STRING  NOT NULL    OPTIONS(description=
                                                "Google Analytics pseudo Id generated by GA Tracker. \n"||
                                                "It depends on device according to Google Documentation. \n"||
                                                "For more details 👉🏻 https://www.optizent.com/blog/what-is-user_pseudo_id-in-ga4-bigquery-export/"
                                               ),
    property_data   STRUCT<
        property_id     INT64   NOT NULL    OPTIONS(description="Google Analytics property Id."),
        brand_trigram   STRING  NOT NULL    OPTIONS(description="Website / Application brand trigram."),
        country         STRING  NOT NULL    OPTIONS(description="Property country. enum = ('ALL', 'UK', 'DE', ...). ‘ALL' is a default value."),
        platform        STRING  NOT NULL    OPTIONS(description="Property platform. enum = ('WEB', 'IOS', 'ANDROID')."),
        section         STRING  NOT NULL    OPTIONS(description="Property section (it's exclusive for Gentside websites). enum = ('ALL', 'SPORT', 'TRIP', ...)."),
        app_info STRUCT<
            id              STRING OPTIONS(description="Application Id"),
            version         STRING OPTIONS(description="Version number"),
            install_store   STRING OPTIONS(description="Install store"),
            firbase_app_id  STRING OPTIONS(description="Firebase App Id"),
            install_source  STRING OPTIONS(description="Install source")
        > OPTIONS(description="Application Informations")
    >   OPTIONS(description="Google Analytics property information."),
    user_data STRUCT<
        pmc_uuid                    ARRAY<STRING>   OPTIONS(description=
                                                         "PMC uuid as custom dimension. \n"||
                                                         "GA dimension: 'web_pmc_id'  \n" ||
                                                         "Is it native or custom dimension ? : custom"
                                                        ),
        is_connected                BOOLEAN         OPTIONS(description=
                                                         "Is profile connected ?. \n"||
                                                         "GA dimension: 'connected' \n"||
                                                         "Is it native or custom dimension ? : native"
                                                        ),
        is_consent_enabled          BOOLEAN         OPTIONS(description=
                                                         "Is profile consent ? It's linked with Google Consent Mode which interact with Prisma Media CMP. \n"||
                                                         "Enum = 'granted', 'not_answered', 'declined' \n"||
                                                         "GA dimension: 'consents' \n"||
                                                         "Is it native or custom dimension ? : native \n"||
                                                         "For more details 👉🏻 https://support.google.com/analytics/answer/9976101?sjid=12483505493093531101-EU#tag_behavior"
                                                        ),
        is_accessibility_enabled    BOOLEAN         OPTIONS(description=
                                                         "(??) "||
                                                         "GA dimension: 'accessibility_enabled' \n"||
                                                         "Is it native or custom dimension ? : (??)"
                                                        ),
        is_location_enabled         BOOLEAN         OPTIONS(description=
                                                         "Is profile location enabled ? "||
                                                         "GA dimension: 'location_enabled' \n"||
                                                         "Is it native or custom dimension ? : (??)"
                                                        ),
        is_push_enabled             BOOLEAN         OPTIONS(description=
                                                         "Is profile push enabled ? "||
                                                         "GA dimension: 'push_enabled' \n"||
                                                         "Is it native or custom dimension ? : (??)"
                                                        ),
        is_paywall                  BOOLEAN         OPTIONS(description=
                                                         "Is profile subscribed to paywall offer ? \n"||
                                                         "GA dimension: 'paywall_subscriber' \n"||
                                                         "Is it native or custom dimension ? : custom"
                                                        ),
        premium_state               STRING          OPTIONS(description=
                                                         "(??)"||
                                                         "GA dimension: 'premium_level' \n"||
                                                         "Is it native or custom dimension ? : (??)"
                                                        ),
        first_open_time             TIMESTAMP       OPTIONS(description=
                                                        "The first open time \n"||
                                                        "GA dimension: 'first_open_time' \n"||
                                                        "Is it native or custom dimension ? : native"
                                                        ),
        previous_first_open_count   INT64           OPTIONS(description=
                                                         "Number of first open after uninstall / install. \n"||
                                                         "GA dimension: 'previous_first_open_count' \n"||
                                                         "Is it native or custom dimension ? : native"
                                                        ),
        keywords                    ARRAY<STRING>   OPTIONS(description=
                                                            "User keywords as array. \n"||
                                                            "GA dimension: 'keywords'  \n" ||
                                                            "Is it native or custom dimension ? : (??)"
                                                            ),
        tracking_info               STRUCT<
            source                      STRING  OPTIONS(description="Source of the first user acquisition \n"),
            medium                      STRING  OPTIONS(description="Medium of the first user acquisition \n"),
            campaign                    STRING  OPTIONS(description="Campaign of the first user acquisition \n"),
            default_channel_group       STRING  OPTIONS(description="Default channel group as defined by Google at user scale. \n"||
                                                        "It's the first channel group from where user has been arrived. \n"||
                                                        "For more details 👉🏻 https://support.google.com/analytics/answer/9756891?hl=en"
                                                        ),
            custom_channel_group        STRING  OPTIONS(description="Custom channel group as defined by Prisma Media at user scale. \n"||
                                                        "It's the first channel group from where user has been arrived. \n"||
                                                        "For more details 👉🏻 https://docs.google.com/spreadsheets/d/15IJMid4OrJKMythVk9udgdZfdKvnLYPuN-YXfRYpcRM/edit?usp=sharing"
                                                        )
        > OPTIONS(description="User first acquisition tracking information.")

    >   OPTIONS(description="User data."),
    session_data ARRAY<
        STRUCT<
            session_id                  STRING      NOT NULL    OPTIONS(description=
                                                                        "Session Id to flag unique session. \n"||
                                                                        "It's a concatenation of 'ga_session_id' and 'user_pseudo_id' \n"||
                                                                        "GA dimension: 'ga_session_id' \n"||
                                                                        "'ga_session_id': It's a native GA dimension and it correspond to stat session timestamp. \n"||
                                                                        "Is it native or custom dimension ? : native \n"||
                                                                        "For more details 👉🏻 https://support.google.com/analytics/answer/9191807"
                                                                       ),
            session_number              INTEGER     NOT NULL    OPTIONS(description=
                                                                        "Number of session that user has started up to the current session. \n"||
                                                                        "GA dimension: 'ga_session_number' \n"||
                                                                        "Is it native or custom dimension ? : native \n"||
                                                                        "For more details 👉🏻 https://support.google.com/analytics/answer/9191807"
                                                                       ),
            session_start_datetime      TIMESTAMP   NOT NULL    OPTIONS(description="First event timestamp's during the session."),
            session_end_datetime        TIMESTAMP   NOT NULL    OPTIONS(description="Last event timestamp's during the session"),
            is_engaged_session          BOOLEAN     NOT NULL    OPTIONS(description=
                                                                        "Is it session concedered as engaged by GA tracker ? \n"||
                                                                        "An engaged session is a session that lasts longer than 10 seconds, has a conversion event, or has at least 2 screenviews or screenviews. \n"||
                                                                        "GA dimension: 'session_engaged' \n"||
                                                                        "Is it native or custom dimension ? : native \n"||
                                                                        "For more details 👉🏻 https://support.google.com/analytics/answer/12195621"
                                                                       ),
            is_logged_session           BOOLEAN     NOT NULL    OPTIONS(description=
                                                                            "Is it a logged session ? \n"||
                                                                            "A logged session is a session that contains a 'pmc_session_start' custom event or a filled PMC Web Id. \n"||
                                                                            "GA dimension: 'web_pmc_id' \n"||
                                                                            "Is it native or custom dimension ? : custom \n"
                                                                           ),
            landing_screen                STRING                  OPTIONS(description=
                                                                            "The first screen on the session. \n"||
                                                                            "GA dimension: 'entrances' & 'screen_location' \n"||
                                                                            "Is it native or custom dimension ? : native \n"
                                                                           ),
            exit_screen                   STRING                  OPTIONS(description=
                                                                        "The last screen on the session. \n"||
                                                                        "GA dimension: 'screen_location' \n"||
                                                                        "Is it native or custom dimension ? : native \n"
                                                                       ),
            tracking_info               STRUCT<
                source                      STRING  OPTIONS(description=
                                                            "Session UTM source. \n"||
                                                            "This UTM source has been redressed (for non-direct) and it's attributed to the last non-direct source by user through the last 30 days. \n"||
                                                            "GA dimension: 'source' \n"||
                                                            "Is it native or custom dimension ? : native \n"||
                                                            "For more details 👉🏻 https://support.google.com/analytics/answer/9191807?hl=en#zippy=%2Cadjust-session-timeout:~:text=How%20session%20attribution%20works,also%20attributed%20to%20%E2%80%9Cgoogle%20/%20organic%E2%80%9D"
                                                            ),
                medium                      STRING  OPTIONS(description=
                                                            "Session UTM medium. \n"||
                                                            "This UTM medium has been redressed (for non-direct) and it's attributed to the last non-direct source by user through the last 30 days. \n"||
                                                            "GA dimension: 'medium' \n"||
                                                            "Is it native or custom dimension ? : native \n"||
                                                            "For more details 👉🏻 https://support.google.com/analytics/answer/9191807?hl=en#zippy=%2Cadjust-session-timeout:~:text=How%20session%20attribution%20works,also%20attributed%20to%20%E2%80%9Cgoogle%20/%20organic%E2%80%9D"
                                                            ),
                campaign                    STRING  OPTIONS(description=
                                                            "Session UTM campaign. \n"||
                                                            "This UTM campaign has been redressed (for non-direct) and it's attributed to the last non-direct source by user through the last 30 days. \n"||
                                                            "GA dimension: 'campaign' \n"||
                                                            "Is it native or custom dimension ? : native \n"||
                                                            "For more details 👉🏻 https://support.google.com/analytics/answer/9191807?hl=en#zippy=%2Cadjust-session-timeout:~:text=How%20session%20attribution%20works,also%20attributed%20to%20%E2%80%9Cgoogle%20/%20organic%E2%80%9D"
                                                            ),
                default_channel_group       STRING  OPTIONS(description="Default channel group as defined by Google at session scale. \n"||
                                                            "For more details 👉🏻 https://support.google.com/analytics/answer/9756891?hl=en"
                                                            ),
                custom_channel_group        STRING  OPTIONS(description="Custom channel group as defined by Prisma Media at session scale. \n"||
                                                            "For more details 👉🏻 https://docs.google.com/spreadsheets/d/15IJMid4OrJKMythVk9udgdZfdKvnLYPuN-YXfRYpcRM/edit?usp=sharing"
                                                            )
            >   OPTIONS(description="Session Tracking information."),
            device_info STRUCT<
                category                    STRING  OPTIONS(description="The device category (mobile, tablet, desktop)."),
                operating_system            STRING  OPTIONS(description="The operating system of the device."),
                operating_system_version    STRING  OPTIONS(description="The OS version."),
                brand_name                  STRING  OPTIONS(description="The device brand name."),
                model_name                  STRING  OPTIONS(description="The device model name."),
                marketing_name              STRING  OPTIONS(description="The device marketing name."),
                os_harware_model            STRING  OPTIONS(description="The device model information retrieved directly from the operating system.")
            >   OPTIONS(description="Device information"),
            geo_info    STRUCT<
                continent       STRING  OPTIONS(description="The continent from which events were reported, based on IP address."),
                sub_continent   STRING  OPTIONS(description="The subcontinent from which events were reported, based on IP address."),
                region          STRING  OPTIONS(description="The region from which events were reported, based on IP address."),
                country         STRING  OPTIONS(description="The country from which events were reported, based on IP address."),
                city            STRING  OPTIONS(description="The city from which events were reported, based on IP address."),
                metro           STRING  OPTIONS(description="The metro from which events were reported, based on IP address.")
            >   OPTIONS(description=
                        "Geographic information. \n"||
                        "For more details 👉🏻 https://support.google.com/analytics/answer/7029846/"
                       ),
            screen_data ARRAY<
                STRUCT<
                    screen_hit_id   STRING                  OPTIONS(description=
                                                                    "Screen Id within a hit and it start by <brand_trigram>_id. It serve to the mapping with G-Ad Manager Data. \n"||
                                                                    "GA dimension: 'page_hit_id' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                   ),
                    global_info     STRUCT<
                        screen_id           INT64          OPTIONS(description=
                                                                    "Screen Id. \n"||
                                                                    "GA dimension: 'firebase_screen_id' \n"||
                                                                    "Is it native or custom dimension ? : native"
                                                                    ),
                        screen_path         STRING         OPTIONS(description=
                                                                    "Screen title as path. \n"||
                                                                    "GA dimension: 'firebase_screen' \n"||
                                                                    "Is it native or custom dimension ? : native"
                                                                    ),
                        is_landing_screen   BOOLEAN        OPTIONS(description=
                                                                    "Is it landing screen ? \n"||
                                                                    "GA dimension: 'entrances' \n"||
                                                                    "Is it native or custom dimension ? : native"
                                                                   ),
                        is_exit_screen      BOOLEAN         OPTIONS(description="Is it exit screen ? It corresponds to the last viewed screen in the session."),
                        screen_level_1      STRING          OPTIONS(description="Screen path first level extracted from screen location."),
                        screen_level_2      STRING          OPTIONS(description="Screen path second level extracted from screen location."),
                        screen_level_3      STRING          OPTIONS(description="Screen path third level extracted from screen location."),
                        screen_level_4      STRING          OPTIONS(description="Screen path fourth level extracted from screen location."),
                        screen_class        STRING          OPTIONS(description=
                                                                    "Screen type. \n"||
                                                                    "GA dimension: 'firebase_screen_class' \n"||
                                                                    "Is it native or custom dimension ? : native"
                                                                    ),
                        screen_type         STRING          OPTIONS(description=
                                                                "Screen type. \n"||
                                                                "GA dimension: 'firebase_type' \n"||
                                                                "Is it native or custom dimension ? : native"
                                                                ),
                        screen_keywords     ARRAY<STRING>   OPTIONS(description=
                                                                    "List of screen keywords. \n"||
                                                                    "GA dimension: 'keywords' \n"||
                                                                    "Is it native or custom dimension ? : native"
                                                                   )
                    >   OPTIONS(description="Screen global information."),
                    content_info        STRUCT<
                        content_title            STRING          OPTIONS(description=
                                                                        "Content title. \n"||
                                                                        "GA dimension: 'content_title' \n"||
                                                                        "Is it native or custom dimension ? : custom"
                                                                        ),
                        content_type            ARRAY<STRING>   OPTIONS(description=
                                                                        "List of content type. \n"||
                                                                        "GA dimension: 'content_type' \n"||
                                                                        "Is it native or custom dimension ? : custom"
                                                                        ),
                        content_channel         STRING          OPTIONS(description=
                                                                        "Content channel. \n"||
                                                                        "GA dimension: 'content_channel' \n"||
                                                                        "Is it native or custom dimension ? : custom"
                                                                        )
                    >   OPTIONS(description="Content information."),
                    event_info ARRAY<
                        STRUCT <
                            event_name                  STRING      NOT NULL    OPTIONS(description=
                                                                                        "Event name. \n"||
                                                                                        "Event can be 'native' / 'custom' like ('start_session', 'screen_view', 'user_engagement', ...) . \n"||
                                                                                        "For more details 👉🏻 https://docs.google.com/spreadsheets/d/1m8_4tFyYqDxutbZ2aGBc7syDjX8E6qAvw9d4tTeHcNI/edit?usp=sharing"
                                                                                        ),
                            event_datetime              TIMESTAMP   NOT NULL    OPTIONS(description="Event datetime."),
                            event_platform              STRING      NOT NULL    OPTIONS(description="Event platform as ('WEB', 'IOS', 'ANDROID') <--> Hit datasource in GA Universal."),
                            engagement_time_in_seconds  FLOAT64                 OPTIONS(description=
                                                                                        "Engagement time in seconds estimated by GA tracker at each event. \n"||
                                                                                        "It's the amount of time by event and added to next one."||
                                                                                        "GA dimension: 'engagement_time_msec' \n"||
                                                                                        "Is it native or custom dimension ? : native \n"||
                                                                                        "For more details 👉🏻 https://support.google.com/analytics/answer/11109416"
                                                                                       ),
                            bookmark_info STRUCT<
                                bookmark_consent    STRING  OPTIONS(description="(??) : I need some explanation. "),
                                bookmark_label      STRING  OPTIONS(description="(??) : I need some explanation. "),
                                bookmark_type       STRING  OPTIONS(description=
                                                                    "Bookmark type as = (''follow:people'', ''follow:tag'', ...) \n."||
                                                                    "GA dimension: 'bookmark_type' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                  ),
                                bookmark_content    STRING  OPTIONS(description=
                                                                    "content id associated to bookmark. \n"||
                                                                    "GA dimension: 'bookmark_content' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                   )
                            > OPTIONS(description="Bookmark information."),
                             review_info STRUCT<
                                user_input_format   STRING  OPTIONS(description=
                                                                    "Input format as : ('text','photo'). \n"||
                                                                    "GA dimension: 'user_input_format' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                   ),
                                user_input_type     STRING  OPTIONS(description=
                                                                    "Input type as : ('comment','score'). \n"||
                                                                    "GA dimension: 'user_input_type' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                   ),
                                user_input_label    STRING  OPTIONS(description=
                                                                    "Input label to flag the content and placement. \n"||
                                                                    "GA dimension: 'user_input_label' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                   )
                            >   OPTIONS(description="User input information (comment/review).It's available only for TEL website."),
                            video_info STRUCT<
                                video_url           STRING  OPTIONS(description=
                                                                    "Video URL. \n"||
                                                                    "GA dimension: 'video_dm_url' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                  ),
                                video_launch_mode   STRING  OPTIONS(description=
                                                                    "Video launch mode as : ('autoplay', 'click'). \n"||
                                                                    "GA dimension: 'video_dm_launchmode' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                  ),
                                video_player        STRING  OPTIONS(description="??")
                            >   OPTIONS(description="Video information."),
                             magazine_info STRUCT<
                                 magazine_title     STRING  OPTIONS(description=
                                                                    "Magazine title. \n"||
                                                                    "GA dimension: 'magazine_title' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                  )
                            >   OPTIONS(description="Magazine information."),
                            purchase_info STRUCT<
                                 product_id         STRING      OPTIONS(description=
                                                                        "Product Id as 'sub_adfree_cmp', 'cmp_tel_03', ... . \n"||
                                                                        "GA dimension: 'product_id' \n"||
                                                                        "Is it native or custom dimension ? : native"
                                                                      ),
                                 product_name       STRING      OPTIONS(description=
                                                                        "Product name. \n"||
                                                                        "GA dimension: 'product_name' \n"||
                                                                        "Is it native or custom dimension ? : native"
                                                                      ),
                                price_per_unit      INT64       OPTIONS(description=
                                                                        "Price per unit. \n"||
                                                                        "GA dimension: 'price' \n"||
                                                                        "Is it native or custom dimension ? : native"
                                                                      ),
                                currency            STRING      OPTIONS(description=
                                                                        "Currency.\n"||
                                                                        "GA dimension: 'currency' \n"||
                                                                        "Is it native or custom dimension ? : native"
                                                                      ),
                                quantity            INT64       OPTIONS(description=
                                                                        "Quantity.\n"||
                                                                        "GA dimension: 'quantity' \n"||
                                                                        "Is it native or custom dimension ? : native"
                                                                      ),
                                is_validated        BOOLEAN     OPTIONS(description=
                                                                        "Is purchase validated ? .\n"||
                                                                        "GA dimension: 'validated' \n"||
                                                                        "Is it native or custom dimension ? : native"
                                                                      ),
                                is_subscribed        BOOLEAN     OPTIONS(description=
                                                                        "(??) .\n"||
                                                                        "GA dimension: 'subscription' \n"||
                                                                        "Is it native or custom dimension ? : native"
                                                                      )
                            >   OPTIONS(description="Purchase information."),
                             star_info STRUCT<
                                 star_name     STRING   OPTIONS(description=
                                                                    "Star name. \n"||
                                                                    "GA dimension: 'star_name' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                )
                            >   OPTIONS(description="Star information."),
                            user_action_info STRUCT<
                                action_name         STRING  OPTIONS(description=
                                                                    "Action name. \n"||
                                                                    "GA dimension: 'action' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                  ),
                                action_category     STRING  OPTIONS(description=
                                                                    "Action category. \n"||
                                                                    "GA dimension: 'categroy' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                  ),
                                action_label        STRING  OPTIONS(description=
                                                                    "Action label. \n"||
                                                                    "GA dimension: 'categroy' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                  ),
                                clicked_button_name STRING  OPTIONS(description=
                                                                    "Clicked button name. \n"||
                                                                    "GA dimension: 'button_name' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                  )
                            >   OPTIONS(description="User action information."),
                            login_info STRUCT<
                                login_role      STRING  OPTIONS(description=
                                                                "Login role as : ('adminlogin', 'adminrole', 'autologin', 'none', NULL). It's linked to login and signup events. \n"||
                                                                "GA dimension: 'login_role' \n"||
                                                                "Is it native or custom dimension ? : custom"
                                                               ),
                                login_method    STRING  OPTIONS(description=
                                                                "Login method as : ('apple', 'beloud', 'email', 'facebook', 'x', 'pinterest', 'magic link', 'mdp'). It''s linked to login and signup events.\n"||
                                                                "GA dimension: 'method' \n"||
                                                                "Is it native or custom dimension ? : custom"
                                                               )
                                > OPTIONS(description="Login information."),
                            signup_info STRUCT<
                                signup_method   STRING  OPTIONS(description=
                                                                "Signup method as : ('apple', 'beloud', 'email', 'facebook', 'x', 'pinterest', 'magic link', 'mdp'). It''s linked to login and signup events.\n"||
                                                                "GA dimension: 'method' \n"||
                                                                "Is it native or custom dimension ? : custom"
                                                                )
                            > OPTIONS(description="Login information."),
                            previous_screen_info STRUCT<
                                 previous_screen_id     INT64   OPTIONS(description=
                                                                        "Previous screen Id. It's related to `screen_view` event and it's used to link navigation throgh screens.\n"||
                                                                        "GA dimension: 'firebase_previous_id' \n"||
                                                                        "Is it native or custom dimension ? : native"
                                                                        ),
                                previous_screen_path    STRING  OPTIONS(description=
                                                                        "Previous screen Path. It's related to `screen_view` event and it's used to link navigation throgh screens.\n"||
                                                                        "GA dimension: 'firebase_previous_screen' \n"||
                                                                        "Is it native or custom dimension ? : native"
                                                                        ),
                                previous_screen_class   STRING  OPTIONS(description=
                                                                        "Previous screen Class. It's related to `screen_view` event and it's used to link navigation throgh screens.\n"||
                                                                        "GA dimension: 'firebase_previous_class' \n"||
                                                                        "Is it native or custom dimension ? : native"
                                                                        )
                            >   OPTIONS(description="Previous screen information."),
                            tracking_info STRUCT<
                                source      STRING  OPTIONS(description=
                                                            "Event UTM source. \n"||
                                                            "GA dimension: 'source' \n"||
                                                            "Is it native or custom dimension ? : native \n"
                                                            ),
                                medium      STRING  OPTIONS(description=
                                                            "Event UTM medium. \n"||
                                                            "GA dimension: 'medium' \n"||
                                                            "Is it native or custom dimension ? : native \n"
                                                            ),
                                campaign    STRING  OPTIONS(description=
                                                            "Event UTM campaign. \n"||
                                                            "GA dimension: 'campaign' \n"||
                                                            "Is it native or custom dimension ? : native \n"
                                                            )
                            >   OPTIONS(description="Event Tracking information."),
                            ecommerce_info STRUCT<
                                transaction STRUCT<
                                    transaction_id      STRING  OPTIONS(description=
                                                                        "The transaction ID collected with the event. \n"||
                                                                        "GA dimension: 'transaction_id' \n"||
                                                                        "Is it native or custom dimension? : native \n"),
                                    total_item_quantity INT64   OPTIONS(description=
                                                                        "The number of items collected with the event. \n"||
                                                                        "GA dimension: 'total_item_quantity' \n"||
                                                                        "Is it native or custom dimension? : native \n"),
                                    unique_items        INT64   OPTIONS(description=
                                                                        "The number of unique items collected with the event. \n"||
                                                                        "GA dimension: 'unique_items' \n"||
                                                                        "Is it native or custom dimension? : native \n"),
                                    purchase_revenue    FLOAT64 OPTIONS(description=
                                                                        "Purchase revenue collected with the event (only for purchase events). \n"||
                                                                        "GA dimension: 'purchase_revenue' \n"||
                                                                        "Is it native or custom dimension? : native \n")
                                                    > OPTIONS(description=  "E-commerce transaction details."),
                                items ARRAY<STRUCT<
                                    item_name     STRING  OPTIONS(description=
                                                                    "The name of the item collected with the event. \n"||
                                                                    "GA dimension: 'item_name' \n"||
                                                                    "Is it native or custom dimension? : native \n"),
                                    item_category STRING  OPTIONS(description=
                                                                    "The category of the item collected with the event. \n"||
                                                                    "GA dimension: 'item_category' \n"||
                                                                    "Is it native or custom dimension? : native \n"),
                                    quantity      INT64   OPTIONS(description=
                                                                    "The quantity of the item collected with the event. \n"||
                                                                    "GA dimension: 'quantity' \n"||
                                                                    "Is it native or custom dimension? : native \n"),
                                    price         FLOAT64 OPTIONS(description=
                                                                    "The price of the item collected with the event. \n"||
                                                                    "GA dimension: 'price' \n"||
                                                                    "Is it native or custom dimension? : native \n")
                                                >> OPTIONS(description= "Items purchased in the event.")
                            > OPTIONS(description=  "E-commerce event data including transaction and items details.")
                        >
                    >   OPTIONS(description="Event information.")
                >
            >   OPTIONS(description="Screen data.")
        >
    >   OPTIONS(description="Session data."),
    PRIMARY KEY(visit_date, user_pseudo_id) NOT ENFORCED
)
PARTITION BY visit_date
OPTIONS(description="It contains refined GA4 events APP. \n"||
        "Table architecture: \n"||
        """visit date:
            |
            user
              |
              [sessions]
                    |
                    [screens]
                        |
                        [events]"""||
        "\n\n"||
        "Sync: Daily \n"||
        "DAG: {{ dag.dag_id }}"
        );