-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- declare incremental condition
DECLARE start_date DATE DEFAULT DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL {{ params.interval }});
DECLARE end_date DATE DEFAULT DATE("{{ data_interval_end }}");

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.refined_data.google_analytics_profile`(
    user_pseudo_id          STRING      NOT NULL        OPTIONS(description="Google Analytics pseudo Id generated by GA Tracker.\n"
                                                                      ||"It depends on device according to Google Documentation.\n"
                                                                      ||"For more details 👉🏻 https://www.optizent.com/blog/what-is-user_pseudo_id-in-ga4-bigquery-export/"),
    pmc_profile_master_id   INT64                       OPTIONS(description="PMC profile master ID")
)
OPTIONS(
    description="This table lists GA4 users and their PMC ID (if any).\n"
              ||"It is used in the Root ID process as a base source for GA4 profiles\n\n"
              ||"DAG: {{ dag.dag_id }}.\n\n"
              ||"Sync: daily."
);

{% if params.is_full %}
    SET start_date = DATE("{{ params.start_date }}");
    SET end_date = DATE("{{ params.end_date }}");
{% endif %}

-- SNAPSHOT documentation: https://cloud.google.com/bigquery/docs/table-snapshots-intro
DROP TABLE IF EXISTS `{{ params.bq_project }}.workspace.google_analytics_profile_snapshot`;

{% if not params.is_full %}
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.workspace.google_analytics_profile_snapshot`
COPY `{{ params.bq_project }}.refined_data.google_analytics_profile`;
{% endif %}

TRUNCATE TABLE `{{ params.bq_project }}.refined_data.google_analytics_profile`;

INSERT INTO `{{ params.bq_project }}.refined_data.google_analytics_profile`
WITH retrieve_data AS (
    -- APP
    SELECT
        ga4.user_pseudo_id,
        p360.id.pmc_profile_master_id AS pmc_profile_master_id
    FROM `{{ params.bq_project }}.refined_data.ga_events_APP_*` AS ga4
    LEFT JOIN UNNEST(user_data.pmc_uuid) AS pmc_uuid
    LEFT JOIN `{{ params.bq_project_matrix }}.business_data.profile_digital_360` AS p360 ON pmc_uuid = p360.id.pmc_uuid
    WHERE
        _TABLE_SUFFIX NOT LIKE "%TEMPLATE"
        AND _TABLE_SUFFIX NOT LIKE "%live%"
        {% if not params.is_full %}
        AND visit_date BETWEEN start_date AND end_date
        {% endif %}

    UNION ALL

    -- WEB
    SELECT
        ga4.user_pseudo_id,
        p360.id.pmc_profile_master_id AS pmc_profile_master_id
    FROM `{{ params.bq_project }}.refined_data.ga_events_WEB_*` AS ga4
    LEFT JOIN UNNEST(user_data.pmc_web_id) AS pmc_web_id
    LEFT JOIN `{{ params.bq_project_matrix }}.business_data.profile_digital_360` AS p360 ON pmc_web_id = p360.id.pmc_web_id
    WHERE
        _TABLE_SUFFIX NOT LIKE "%TEMPLATE"
        AND _TABLE_SUFFIX NOT LIKE "%live%"
        {% if not params.is_full %}
        AND visit_date BETWEEN start_date AND end_date
        {% endif %}


    {% if not params.is_full %}
    UNION ALL

    SELECT
        user_pseudo_id,
        pmc_profile_master_id
    FROM `{{ params.bq_project }}.workspace.google_analytics_profile_snapshot`
    {% endif %}
),

aggregate_pmc_web_id AS (
    SELECT
        user_pseudo_id,
        ARRAY_AGG(DISTINCT IFNULL(SAFE_CAST(pmc_profile_master_id AS STRING), "TECHNICAL_NULL")) AS pmc_profile_master_ids                   -- Flag NULLs to handle them safely later
    FROM retrieve_data
    GROUP BY ALL
)

SELECT
    user_pseudo_id,
    SAFE_CAST(NULLIF(pmc_profile_master_id, "TECHNICAL_NULL") AS INT64) AS pmc_profile_master_id                                            -- remove flag and set back to INT
FROM aggregate_pmc_web_id, UNNEST(pmc_profile_master_ids) AS pmc_profile_master_id
WHERE
    ARRAY_LENGTH(pmc_profile_master_ids) = 1                                                                            -- only one pmc_web_id = we include it (NULL or NOT NULL)
    OR (ARRAY_LENGTH(pmc_profile_master_ids) > 1 AND pmc_profile_master_id != "TECHNICAL_NULL");                        -- more than one pmc_web_id = we ignore the NULLs and include the others
