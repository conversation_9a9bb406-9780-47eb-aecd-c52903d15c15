-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- This view will be used by data/scripts/refine_ga4_events_live.ipynb
CREATE OR REPLACE VIEW `{{ bq_project }}.refined_data.ga_events_live_APP_{{ brand_trigram }}_{{ country }}_{{ section }}`
AS(

  WITH get_gan_events AS (
    -- Extract the most useful columns as: visit_date, user_pseudo_id, session_id & events information's (event_name, TIMESTAMP_MICROS(event_timestamp))
    SELECT
      PARSE_DATE("%Y%m%d", event_date) AS visit_date,
      user_pseudo_id,
      user_properties,
      CONCAT(user_pseudo_id, `{{ bq_project }}.refined_data.GetParamValue`(event_params, "ga_session_id").int_value) AS session_id,
      IFNULL(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "ga_session_number").int_value, -1)         AS session_number,
      LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "page_hit_id").string_value)                 AS screen_hit_id,
      event_name,
      event_params,
      -- Always, session start event and succeeding event are both at the same timestamp
      IF(
          event_name = "session_start",
          TIMESTAMP_SUB(TIMESTAMP_MICROS(event_timestamp), INTERVAL 1 SECOND),
          TIMESTAMP_MICROS(event_timestamp)
      ) AS event_datetime,
      e.platform AS event_platform,
      device,
      geo,
      traffic_source,
      app_info,
      STRUCT (
        STRUCT (
            ecommerce.transaction_id AS transaction_id,
            ecommerce.total_item_quantity AS total_item_quantity,
            ecommerce.unique_items AS unique_items,
            ecommerce.purchase_revenue AS purchase_revenue
            ) AS transaction,
        ARRAY(
                SELECT AS STRUCT
                item.item_name,
                item.item_category,
                item.quantity,
                item.price
                FROM UNNEST(items) AS item
        ) AS items
      ) AS ecommerce
    FROM `{{ bq_project }}.analytics_{{ property_id }}.events_intraday_*` AS e
    WHERE _TABLE_SUFFIX = FORMAT_DATE('%Y%m%d', PARSE_DATE('%Y-%m-%d', STRING(CURRENT_DATE())))
    AND user_pseudo_id IS NOT NULL
  ), get_user_data AS (
    -- 👨‍💼👨‍💼👨‍💼
    -- Extract user data: aggregate user ids into arrays to deal with user_pseudo_id with multiple (auth_id, batch_user_id, client_id)
    -- PK : (visit_date, user_pseudo_id)
    SELECT
      visit_date,
      user_pseudo_id,
      STRUCT(
        ARRAY_AGG(DISTINCT
          `{{ bq_project }}.refined_data.GetParamValue`(event_params, "web_pmc_id").string_value
          IGNORE NULLS
        ) AS pmc_uuid,
        IFNULL(
          LOGICAL_OR(
          (LOWER(`{{ bq_project }}.refined_data.GetParamValue`(user_properties, "connected").string_value) = "true")),
          FALSE
        ) AS is_connected,
        LOGICAL_OR(
          IFNULL((LOWER(`{{ bq_project }}.refined_data.GetParamValue`(user_properties, "consents").string_value) = "granted"), FALSE)
        ) AS is_consent_enabled,
        IFNULL(
          LOGICAL_OR(
          (LOWER(`{{ bq_project }}.refined_data.GetParamValue`(user_properties, "accessibility_enabled").string_value) = "true")),
          FALSE
        ) AS is_accessibility_enabled,
        IFNULL(
          LOGICAL_OR(
          (LOWER(`{{ bq_project }}.refined_data.GetParamValue`(user_properties, "location_enabled").string_value) = "true")),
          FALSE
        ) AS is_location_enabled,
        IFNULL(
          LOGICAL_OR(
          (LOWER(`{{ bq_project }}.refined_data.GetParamValue`(user_properties, "push_enabled").string_value) = "true")),
          FALSE
        ) AS is_push_enabled,
        IFNULL(
            LOGICAL_OR(SAFE_CAST(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "paywall_subscriber").string_value AS BOOL)),
            FALSE
        ) AS is_paywall,
        ANY_VALUE(
          LOWER(`{{ bq_project }}.refined_data.GetParamValue`(user_properties, "premium_level").string_value)
        ) AS premium_state,
        MIN(TIMESTAMP_MILLIS(`{{ bq_project }}.refined_data.GetParamValue`(user_properties, "first_open_time").int_value)) AS first_open_time,
        MIN(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "previous_first_open_count").int_value)            AS previous_first_open_count,
        SPLIT(STRING_AGG(DISTINCT `{{ bq_project }}.refined_data.GetParamValue`(user_properties, "keywords").string_value, ","), ",") AS keywords,
        -- get first tracking information by user
        STRUCT(
          ARRAY_AGG(traffic_source.source IGNORE NULLS) [SAFE_ORDINAL(1)] AS source,
          ARRAY_AGG(traffic_source.medium IGNORE NULLS)[SAFE_ORDINAL(1)]  AS medium,
          ARRAY_AGG(traffic_source.name   IGNORE NULLS)[SAFE_ORDINAL(1)]  AS campaign,
          `pm-prod-ga4.refined_data.BuildDefaultChannel`(
              ARRAY_AGG(traffic_source.source IGNORE NULLS)[SAFE_ORDINAL(1)],
              ARRAY_AGG(traffic_source.medium IGNORE NULLS)[SAFE_ORDINAL(1)],
              ARRAY_AGG(traffic_source.name   IGNORE NULLS)[SAFE_ORDINAL(1)]
          ) AS default_channel_group,
          `pm-prod-ga4.refined_data.BuildCustomChannel`(
              ARRAY_AGG(traffic_source.source IGNORE NULLS)[SAFE_ORDINAL(1)],
              ARRAY_AGG(traffic_source.medium IGNORE NULLS)[SAFE_ORDINAL(1)],
              ARRAY_AGG(traffic_source.name   IGNORE NULLS)[SAFE_ORDINAL(1)]
          ) AS custom_channel_group
        ) AS tracking_info
        ) AS user_data,
        STRUCT(
            ANY_VALUE(app_info.id)                  AS id,
            ANY_VALUE(app_info.version)             AS version,
            ANY_VALUE(app_info.install_store)       AS install_store,
            ANY_VALUE(app_info.firebase_app_id)     AS firebase_app_id,
            ANY_VALUE(app_info.install_source)      AS install_source
        ) AS app_info
    FROM get_gan_events               AS e
    LEFT JOIN UNNEST(user_properties) AS u
    GROUP BY ALL
  ), get_session_data AS (
      -- 👨‍💻👨‍💻👨‍💻
      -- Get session information's per user
      -- PK : (visit_date, user_pseudo_id, session_id)
      SELECT
          visit_date,
          user_pseudo_id,
          session_id,
          session_number,
          LOGICAL_OR(
              IFNULL(
                  COALESCE(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "session_engaged").int_value,
                  SAFE_CAST(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "session_engaged").string_value AS INT64)
                  ) = 1,
                  FALSE
              )
          )  AS is_engaged_session,
          LOGICAL_OR(
                  (event_name="pmc_session_start" OR `{{ bq_project }}.refined_data.GetParamValue`(e.event_params, "web_pmc_id").string_value IS NOT NULL)
          )  AS is_logged_session,
          MIN(event_datetime)  AS session_start_datetime,
          MAX(event_datetime)  AS session_end_datetime,
          ARRAY_AGG(
              IF(
                  event_name = "screen_view" AND `{{ bq_project }}.refined_data.GetParamValue`(event_params, "entrances").int_value = 1,
                  `{{ bq_project }}.refined_data.GetParamValue`(event_params, "firebase_screen").string_value,
                  NULL
              )
              IGNORE NULLS ORDER BY event_datetime ASC
          )[SAFE_ORDINAL(1)] AS landing_screen,
          ARRAY_AGG(
              IF(
                  event_name = "screen_view",
                  `{{ bq_project }}.refined_data.GetParamValue`(event_params, "firebase_screen").string_value,
                  NULL
              )
              IGNORE NULLS ORDER BY event_datetime DESC
          )[SAFE_ORDINAL(1)] AS exit_screen,
          -- Tracking info
          -- @@ IF UTM source / medium is NULL for session Lambda replace it by the first non-direct source/medium by user across the last 30 days 👉🏻 https://tanelytics.com/ga4-bigquery-session-traffic_source/#:~:text=Query%20the%20Last%20Non%2Ddirect%20Traffic%20Source
          STRUCT(
              ARRAY_AGG(LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "source").string_value) IGNORE NULLS ORDER BY event_datetime ASC)[SAFE_ORDINAL(1)] AS source,
              ARRAY_AGG(LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "medium").string_value) IGNORE NULLS ORDER BY event_datetime ASC)[SAFE_ORDINAL(1)] AS medium,
              ARRAY_AGG(LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "campaign").string_value) IGNORE NULLS ORDER BY event_datetime ASC)[SAFE_ORDINAL(1)] AS campaign,
              `{{ bq_project }}.refined_data.BuildDefaultChannel`(
                ARRAY_AGG(LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "source").string_value) IGNORE NULLS ORDER BY event_datetime ASC)[SAFE_ORDINAL(1)],
                ARRAY_AGG(LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "medium").string_value) IGNORE NULLS ORDER BY event_datetime ASC)[SAFE_ORDINAL(1)],
                ARRAY_AGG(LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "campaign").string_value) IGNORE NULLS ORDER BY event_datetime ASC)[SAFE_ORDINAL(1)]
              ) AS default_channel_group,
              `{{ bq_project }}.refined_data.BuildCustomChannel`(
                ARRAY_AGG(LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "source").string_value) IGNORE NULLS ORDER BY event_datetime ASC)[SAFE_ORDINAL(1)],
                ARRAY_AGG(LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "medium").string_value) IGNORE NULLS ORDER BY event_datetime ASC)[SAFE_ORDINAL(1)],
                ARRAY_AGG(LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "campaign").string_value) IGNORE NULLS ORDER BY event_datetime ASC)[SAFE_ORDINAL(1)]
              ) AS custom_channel_group
            ) AS tracking_info,
          -- device
          STRUCT(
            -- (mobile, desktop)
            ANY_VALUE(LOWER(device.category))                  AS category,
            ANY_VALUE(LOWER(device.operating_system))          AS operating_system,
            ANY_VALUE(LOWER(device.operating_system_version))  AS operating_system_version,
            ANY_VALUE(LOWER(device.mobile_brand_name))         AS brand_name,
            ANY_VALUE(LOWER(device.mobile_model_name))         AS model_name,
            ANY_VALUE(LOWER(device.mobile_marketing_name))     AS marketing_name,
            ANY_VALUE(LOWER(device.mobile_os_hardware_model))  AS os_harware_model
          ) AS device_info,
          -- geographic
          STRUCT(
            ANY_VALUE(LOWER(geo.continent))      AS continent,
            ANY_VALUE(LOWER(geo.sub_continent))  AS sub_continent,
            ANY_VALUE(LOWER(geo.region))         AS region,
            ANY_VALUE(LOWER(geo.country))        AS country,
            ANY_VALUE(LOWER(geo.city))           AS city,
            ANY_VALUE(LOWER(geo.metro))          AS metro
          ) AS geo_info
        FROM get_gan_events   AS e
        GROUP BY ALL
    ), get_screen_data AS (
      -- 📄📄📄
      -- Get information by screen / hit
      -- PK : (visit_date, user_pseudo_id, session_id, screen_hit_id)
      SELECT
        ge.visit_date,
        ge.user_pseudo_id,
        ge.session_id,
        IFNULL(ge.screen_hit_id, "(not set)") AS screen_hit_id,
        ---- screen global information's ----
        `{{ bq_project }}.refined_data.GetParamValue`(event_params, "firebase_screen_id").int_value        AS screen_id,
        `{{ bq_project }}.refined_data.GetParamValue`(event_params, "firebase_screen").string_value        AS screen_path,
        -- is it landing screen ?
        IFNULL(
            (`{{ bq_project }}.refined_data.GetParamValue`(event_params, "entrances").int_value = 1 AND event_name = "screen_view"),
            FALSE
        ) AS is_landing_screen,
        -- is it exit screen ?
        IFNULL(
          (
            `{{ bq_project }}.refined_data.GetParamValue`(event_params, "firebase_screen").string_value = sd.exit_screen
            AND event_name = "screen_view"
          ),
          FALSE
        ) AS is_exit_screen,
        -- screen first fourth level's
        CASE
          WHEN SPLIT(SPLIT(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "firebase_screen").string_value, '/')[SAFE_ORDINAL(4)], '?')[SAFE_ORDINAL(1)] = "" THEN NULL
          ELSE CONCAT('/', SPLIT(SPLIT(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "firebase_screen").string_value,'/')[SAFE_ORDINAL(4)], '?')[SAFE_ORDINAL(1)])
        END AS screen_level_1,
        CASE
          WHEN SPLIT(SPLIT(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "firebase_screen").string_value,'/')[SAFE_ORDINAL(5)], '?')[SAFE_ORDINAL(1)] = "" THEN NULL
          ELSE CONCAT('/', SPLIT(SPLIT(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "firebase_screen").string_value,'/')[SAFE_ORDINAL(5)], '?')[SAFE_ORDINAL(1)])
        END AS screen_level_2,
        CASE
          WHEN SPLIT(SPLIT(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "firebase_screen").string_value,'/')[SAFE_ORDINAL(6)], '?')[SAFE_ORDINAL(1)] = "" THEN NULL
          ELSE CONCAT('/', SPLIT(SPLIT(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "firebase_screen").string_value,'/')[SAFE_ORDINAL(6)], '?')[SAFE_ORDINAL(1)])
        END AS screen_level_3,
        CASE
          WHEN SPLIT(SPLIT(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "firebase_screen").string_value,'/')[SAFE_ORDINAL(7)], '?')[SAFE_ORDINAL(1)] = "" THEN NULL
          ELSE CONCAT('/', SPLIT(SPLIT(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "firebase_screen").string_value,'/')[SAFE_ORDINAL(7)], '?')[SAFE_ORDINAL(1)])
        END AS screen_level_4,
        -- screen classification
        LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "firebase_screen_class").string_value)  AS screen_class,
        LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "screen_type").string_value)            AS screen_type,
        SPLIT(
          LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "keywords").string_value),
          ","
        ) AS screen_keywords,

        ----------------------------------------------------------------------------------------------------------------
        ---- screen content information's ----

        LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "content_title").string_value)   AS content_title,
        SPLIT(
          LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "content_type").string_value),
          ",")
        AS content_type,
        LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "content_channel").string_value) AS content_channel,
        ----------------------------------------------------------------------------------------------------------------
        ---- screen event's ----
            ---- Global information's ----
        ge.event_name,
        ge.event_datetime,
        ge.event_platform,
        -- Engagement time per second per event --> It's meaning the spent time to achieve the action like scrolling / screen view / ....
        -- There's an example to explain how engagement is calculated 👉🏻  https://support.google.com/analytics/answer/11109416
        IFNULL(
            `{{ bq_project }}.refined_data.GetParamValue`(event_params, "engagement_time_msec").int_value / 1000,
            0
        ) AS engagement_time_in_seconds,
          -- Login information's ----
        IF(event_name="login", LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "login_role").string_value), NULL) AS login_role,
        IF(event_name="login", LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "method").string_value), NULL)     AS login_method,
          -- Signup information's ----
        IF(event_name="sign_up", LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "method").string_value), NULL) AS signup_method,
          -- Previous screen information's ----
        `{{ bq_project }}.refined_data.GetParamValue`(event_params, "firebase_previous_id").int_value        AS previous_screen_id,
        `{{ bq_project }}.refined_data.GetParamValue`(event_params, "firebase_previous_screen").string_value AS previous_screen_path,
        `{{ bq_project }}.refined_data.GetParamValue`(event_params, "firebase_previous_class").string_value  AS previous_screen_class,
            ---- Bookmark information's ----
        -- bookmark consent ?
        LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "bookmark_consent").string_value)  AS bookmark_consent,
        -- bookmark label
        LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "bookmark_label").string_value)    AS bookmark_label,
        -- bookmark type : alert_page, follow::people , .. ?
        LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "bookmark_type").string_value)     AS bookmark_type,
        -- bookmark content like : RECIPE:<>, ARTICLE:<> ,....
        LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "bookmark_content").string_value)  AS bookmark_content,
            ---- Review / comment information's ----
        LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "user_input_format").string_value)    AS user_input_format,
        LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "user_input_type").string_value)      AS user_input_type,
        LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "user_input_label").string_value)     AS user_input_label,
            ---- Video information's ----
        `{{ bq_project }}.refined_data.GetParamValue`(event_params, "video_dm_url").string_value                 AS video_url,
        LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "video_dm_launchmode").string_value)   AS video_launch_mode,
        LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "video_player").string_value)          AS video_player,
           --- Magazine information's
        LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "magazine_title").string_value)        AS magazine_title,
          -- Purchase information's
        `{{ bq_project }}.refined_data.GetParamValue`(event_params, "product_id").string_value             AS product_id,
        LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "product_name").string_value)    AS product_name,
        `{{ bq_project }}.refined_data.GetParamValue`(event_params, "price").int_value                     AS price_per_unit,
        LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "currency").string_value)        AS currency,
        `{{ bq_project }}.refined_data.GetParamValue`(event_params, "quantity").int_value                  AS quantity,
        (`{{ bq_project }}.refined_data.GetParamValue`(event_params, "validated").int_value = 1)           AS is_validated,
        (`{{ bq_project }}.refined_data.GetParamValue`(event_params, "subscription").int_value = 1)        AS is_subscribed,
          -- Star information's
        LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "star_name").string_value)    AS star_name,
          -- User action information's
        LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "action").string_value)      AS action_name,
        LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "categroy").string_value)    AS action_category,
        LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "label").string_value)       AS action_label,
        LOWER(`{{ bq_project }}.refined_data.GetParamValue`(event_params, "button_name").string_value) AS clicked_button_name,
          -- Tracking information's
        `{{ bq_project }}.refined_data.GetParamValue`(event_params, "source").string_value     AS source,
        `{{ bq_project }}.refined_data.GetParamValue`(event_params, "medium").string_value     AS medium,
        `{{ bq_project }}.refined_data.GetParamValue`(event_params, "campaign").string_value   AS campaign,
          -- ecommerce information's
        ge.ecommerce,
        -- Rank screen hit's
        ROW_NUMBER() OVER screen_WINDOW AS screen_rank
        FROM get_gan_events   AS ge
        JOIN get_session_data AS sd USING(visit_date, user_pseudo_id, session_id)
        WINDOW SCREEN_WINDOW  AS (PARTITION BY  visit_date, user_pseudo_id, session_id ORDER BY event_datetime ASC)
    ), nest_screen_data AS (
      -- Nest screen information's by screen hit
      SELECT
        visit_date,
        user_pseudo_id,
        session_id,
        screen_hit_id,
        ANY_VALUE(screen_rank) AS screen_rank,
        STRUCT(
            ANY_VALUE(screen_id)              AS screen_id,
            ANY_VALUE(screen_path)            AS screen_path,
            LOGICAL_OR(is_landing_screen)     AS is_landing_screen,
            LOGICAL_OR(is_exit_screen)        AS is_exit_screen,
            ANY_VALUE(screen_level_1)         AS screen_level_1,
            ANY_VALUE(screen_level_2)         AS screen_level_2,
            ANY_VALUE(screen_level_3)         AS screen_level_3,
            ANY_VALUE(screen_level_4)         AS screen_level_4,
            ANY_VALUE(screen_class)           AS screen_class,
            ANY_VALUE(screen_type)            AS screen_type,
            -- Get unique keywords into an array
            ANY_VALUE(`{{ bq_project }}.refined_data.dedup_array`(screen_keywords)) AS screen_keywords
        ) AS global_info,
        STRUCT(
            ANY_VALUE(content_title)    AS content_title,
            ANY_VALUE(content_type)     AS content_type,
            ANY_VALUE(content_channel)  AS content_channel
        ) AS content_info,
        ARRAY_AGG(
          STRUCT(
            event_name,
            event_datetime,
            event_platform,
            engagement_time_in_seconds,
            STRUCT(
              bookmark_consent,
              bookmark_label,
              bookmark_type,
              bookmark_content
            ) AS bookmark_info,
            STRUCT(
              user_input_format,
              user_input_type,
              user_input_label
            ) AS review_info,
            STRUCT(
              video_url,
              video_launch_mode,
              video_player
            ) AS video_info,
            STRUCT(
              magazine_title
            ) AS magazine_info,
            STRUCT(
                product_id,
                product_name,
                price_per_unit,
                currency,
                quantity,
                is_validated,
                is_subscribed
            ) AS purchase_info,
            STRUCT(
              star_name
            ) AS star_info,
            STRUCT(
              action_name,
              action_category,
              action_label,
              clicked_button_name
            ) AS user_action_info ,
            STRUCT(
              login_role,
              login_method
            ) AS login_info,
            STRUCT(
              signup_method
            ) AS signup_info,
            STRUCT(
              previous_screen_id,
              previous_screen_path,
              previous_screen_class
            ) AS previous_screen_info,
            STRUCT(
              source,
              medium,
              campaign
            ) AS tracking_info,
            ecommerce as ecommerce_info
          )
        ORDER BY event_datetime ASC
        ) AS event_info,
      FROM get_screen_data
      -- Group by user and hit
      GROUP BY ALL
    ), nest_screen_session_data AS (
      -- Nest screen data by user and session
      SELECT
        visit_date,
        user_pseudo_id,
        session_id,
        ARRAY_AGG(
          STRUCT(
            screen_hit_id,
            global_info,
            content_info,
            event_info
          )
          ORDER BY screen_rank ASC
        ) AS screen_data
      FROM nest_screen_data AS pd
      GROUP BY ALL
    ), nest_session_data AS (
      -- Nest session data by user
      SELECT
        sd.visit_date,
        sd.user_pseudo_id,
        ARRAY_AGG(
          STRUCT(
            sd.session_id,
            sd.session_number,
            sd.session_start_datetime,
            sd.session_end_datetime,
            sd.is_engaged_session,
            sd.is_logged_session,
            sd.landing_screen,
            sd.exit_screen,
            sd.tracking_info,
            sd.device_info,
            sd.geo_info,
            pd.screen_data
          ) ORDER BY sd.session_start_datetime ASC
        ) AS session_data
      FROM get_session_data           AS sd
      JOIN nest_screen_session_data   AS pd USING(visit_date, user_pseudo_id, session_id)
      GROUP BY ALL
    )
    -- Merge all nested data : (user, session, screen)
    SELECT
      ud.visit_date,
      ud.user_pseudo_id,
      STRUCT(
          {{ property_id }}              AS property_id,
          UPPER("{{ brand_trigram }}")   AS brand_trigram,
          UPPER("{{ country }}")         AS country,
          UPPER("{{ platform }}")        AS platform,
          UPPER("{{ section }}")         AS section,
          ud.app_info
      ) AS property_data,
      ud.* EXCEPT(visit_date, user_pseudo_id, app_info),
      sd.* EXCEPT(visit_date, user_pseudo_id)
    FROM get_user_data      AS ud
    JOIN nest_session_data  AS sd USING(visit_date, user_pseudo_id)
);

ALTER VIEW `{{ bq_project }}.refined_data.ga_events_live_WEB_{{ brand_trigram }}_{{ country }}_{{ section }}`
SET OPTIONS(description="This view contain intraday GA4 events refined for APP {{ brand_trigram }} {{ counrty }} {{ section }}");
