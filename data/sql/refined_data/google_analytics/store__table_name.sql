-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.refined_data.metadata_ga_events_refined_data`
(
    execution_date  DATE            NOT NULL    OPTIONS(description="The execution date."),
    property_id     INT64           NOT NULL    OPTIONS(description="The Property Id."),
    table_names     ARRAY<STRING>               OPTIONS(description="List of table name's to be treated."),
    PRIMARY KEY (execution_date, property_id) NOT ENFORCED
)
PARTITION BY execution_date
CLUSTER BY property_id
OPTIONS(description="This table contains metadata about refined data process by execution_date. \n"
       ||"DAG: {{ dag.dag_id }} \n"
       ||"SYNC: Daily \n"
);

MERGE INTO `{{ params.bq_project }}.refined_data.metadata_ga_events_refined_data` AS dst
USING (
    WITH get_table_ids AS (
        -- Get 'events_*' and 'events_intraday_*' table name's
        SELECT
            DATE("{{ next_ds }}")        AS execution_date,
            {{ params.property_id }}     AS property_id,
            table_id AS table_name,
            -- Extract the suffix as DATE
            PARSE_DATE("%Y%m%d", array_reverse(split(table_id , "_"))[offset(0)]) AS suffix_date,
        FROM `{{ params.bq_project }}.analytics_{{ params.property_id }}.__TABLES__`
        WHERE
            project_id = '{{ params.bq_project }}'
            AND
            dataset_id = 'analytics_{{ params.property_id }}'
            AND
            (
                {% for suffix in params.table_suffix %}
                    ENDS_WITH(table_id, "{{ suffix }}")
                {% if not loop.last %} OR {% endif %}
                {% endfor %}
            )
            AND
            -- Keep only "events_*" OR "events_intraday_*" patterns
            NOT (
              table_id LIKE "%users_%"
              OR
              table_id LIKE "%_fresh_%"
            )
    ), get_distinct_tables AS(
        -- Keep only one table per date between 'events_*' and 'events_intraday_*'
        SELECT
            execution_date,
            property_id,
            table_name,
            ROW_NUMBER() OVER(PARTITION BY suffix_date ORDER BY table_name ASC) AS row_num
        FROM get_table_ids
        QUALIFY row_num = 1
    )
    SELECT
        execution_date,
        property_id,
        ARRAY_AGG(DISTINCT table_name) AS table_names
    FROM get_distinct_tables
    GROUP BY ALL
) AS ref
ON
    ref.execution_date = dst.execution_date
    AND  ref.property_id = dst.property_id
WHEN MATCHED THEN
    UPDATE
         SET dst.table_names = ref.table_names
WHEN NOT MATCHED BY TARGET THEN
    INSERT(execution_date, property_id, table_names)
    VALUES(ref.execution_date, ref.property_id, ref.table_names);