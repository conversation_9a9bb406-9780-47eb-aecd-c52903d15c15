-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
CREATE OR REPLACE TABLE `{{ params.bq_project }}.temp.table_name_{{ params.property_id }}_{{ next_ds_nodash }}`
(
    table_name     STRING               OPTIONS(description="List of table name's to be treated.")
)
OPTIONS(description="It's a temporary table per property contains list of tables to be ingested in refined data layer. \n"
       ||"DAG: {{ dag.dag_id }} \n"
       ||"SYNC: Daily \n"
)
AS
 -- Get table name to upload into refined data
SELECT table_name
FROM `{{ params.bq_project }}.refined_data.metadata_ga_events_refined_data`, UNNEST(table_names) AS table_name
WHERE
    execution_date = DATE("{{ next_ds }}")
    AND
    property_id = {{ params.property_id }};

