-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}


-- compute coaching stats by step and date
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.datastudio.coaching_profile_lifecycle_stats_step`(
    observation_date        DATE    NOT NULL    OPTIONS(description="observation date"),
    coaching_group          STRING  NOT NULL    OPTIONS(description="enum = ['Minceur','Santé','<PERSON>ess<PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON> gaspi','Je fais tout maison'],"),
    step_name               STRING              OPTIONS(description="step name"),
    broke_number            INTEGER             OPTIONS(description="number of broken coaching per date by step"),
    inprogress_number       INTEGER             OPTIONS(description="number of coaching in progress per date by step")
)
PARTITION BY observation_date
CLUSTER BY coaching_group
OPTIONS(description="It contains number of profiles in each step by coaching and date. "||
                    "It's used by this dashboard : https://datastudio.google.com/reporting/a8a6e60a-738d-4f17-b263-00afc8383fc5 ."||
                    "DAG: {{ dag.dag_id }}. "||
                    "Sync: daily");

{% if params.is_full %}
    TRUNCATE TABLE `{{ params.bq_project }}.datastudio.coaching_profile_lifecycle_stats_step`;
{% else %}
    DELETE FROM `{{ params.bq_project }}.datastudio.coaching_profile_lifecycle_stats_step`
    WHERE observation_date >= DATE_SUB(DATE('{{ next_ds }}') , INTERVAL {{ params.interval }});
{% endif %}

INSERT INTO `{{ params.bq_project }}.datastudio.coaching_profile_lifecycle_stats_step`
-- compute KPI by day and step
SELECT
    observation_date,
    coaching_group,
    step_name,
    COUNT(DISTINCT IF(state = "broke", email_profile_master_id, NULL))          AS broke_number,
    COUNT(DISTINCT IF(state = "in progress", email_profile_master_id, NULL))    AS inprogress_number
FROM `{{ params.bq_project }}.datastudio.coaching_profile_lifecycle`
{% if not params.is_full %}
    -- Incremental
    WHERE observation_date >= DATE_SUB(DATE('{{ next_ds }}') , INTERVAL {{ params.interval }})
{% endif %}
GROUP BY 1,2,3;


-- compute global coaching stats by date
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.datastudio.coaching_profile_lifecycle_stats`(
    observation_date        DATE    NOT NULL    OPTIONS(description="date of step end date or unsub date"),
    coaching_group          STRING  NOT NULL    OPTIONS(description="enum = ['Minceur','Santé','Grossesse','Pâtisserie','Anti gaspi','Je fais tout maison'],"),
    sub_number              INTEGER             OPTIONS(description="number of subscription to coaching per date"),
    finished_number         INTEGER             OPTIONS(description="number of finished coaching per date"),
    broke_number            INTEGER             OPTIONS(description="number of broken coaching per date"),
    inprogress_number       INTEGER             OPTIONS(description="number of coaching in progress per date")
)
PARTITION BY observation_date
CLUSTER BY coaching_group
OPTIONS(description="It contains number of profiles per coaching by date. "||
                    "It's used by this dashboard : https://datastudio.google.com/reporting/a8a6e60a-738d-4f17-b263-00afc8383fc5 ."||
                    "DAG: {{ dag.dag_id }}. "||
                    "Sync: daily");

{% if params.is_full %}
    TRUNCATE TABLE `{{ params.bq_project }}.datastudio.coaching_profile_lifecycle_stats`;
{% else %}
    DELETE FROM `{{ params.bq_project }}.datastudio.coaching_profile_lifecycle_stats`
    WHERE observation_date >= DATE_SUB(DATE('{{ next_ds }}') , INTERVAL {{ params.interval }});
{% endif %}

INSERT INTO `{{ params.bq_project }}.datastudio.coaching_profile_lifecycle_stats`
 -- compute global coaching KPI by day
SELECT
    observation_date,
    coaching_group,
    CASE WHEN coaching_group NOT IN ("Santé", "Maison au naturel") THEN
        COUNT(DISTINCT IF(step_order=0, email_profile_master_id, NULL))
    ELSE
        COUNT(DISTINCT IF(step_order=1, email_profile_master_id, NULL))
    END AS sub_number,
    COUNT(DISTINCT IF(state = "done" AND  (REGEXP_CONTAINS(e.step_name, r'goodbye') OR REGEXP_CONTAINS(e.step_name, r'good-bye')), email_profile_master_id, NULL))     AS finished_number,
    COUNT(DISTINCT IF(state = "broke" or state = "not done", email_profile_master_id, NULL))                            AS broke_number,
    COUNT(DISTINCT IF(state = "in progress", email_profile_master_id, NULL))                                            AS inprogress_number
FROM `{{ params.bq_project }}.datastudio.coaching_profile_lifecycle` AS e
{% if not params.is_full %}
    -- Incremental
    WHERE observation_date >= DATE_SUB(DATE('{{ next_ds }}') , INTERVAL {{ params.interval }})
{% endif %}
GROUP BY 1,2;


