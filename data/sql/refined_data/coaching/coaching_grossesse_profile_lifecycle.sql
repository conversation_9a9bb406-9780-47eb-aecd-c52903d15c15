-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- Coaching "Grossesse"
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.datastudio.coaching_grossesse_profile_lifecycle`(
    observation_date        DATE    NOT NULL    OPTIONS(description="observation date"),
    email_profile_master_id INTEGER             OPTIONS(description="email profile master id. ref: {{ params.bq_project }}.store_matrix_email.profile_matser_id.id"),
    step_order              INTEGER             OPTIONS(description="step order"),
    step_name               STRING              OPTIONS(description="step name"),
    state                   STRING              OPTIONS(description="enum = ['broke', 'not done', 'done', 'in progress', 'not yet']. 'in progress' --> 'not yet' and 'broke'('unsub') --> 'not done")
)
PARTITION BY observation_date
OPTIONS(description="It contains state for each step by profile by end step date for 'Grossesse' coaching."
                  ||"\n\n"
                  ||"DAG: {{ dag.dag_id }}."
                  ||"\n\n"
                  ||"Sync: daily");

{% if params.is_full %}
    TRUNCATE TABLE `{{ params.bq_project }}.datastudio.coaching_grossesse_profile_lifecycle`;
{% else %}
    DELETE FROM `{{ params.bq_project }}.datastudio.coaching_grossesse_profile_lifecycle`
    WHERE observation_date >= DATE_SUB(DATE('{{ next_ds }}') , INTERVAL {{ params.interval }});
{% endif %}

INSERT INTO `{{ params.bq_project }}.datastudio.coaching_grossesse_profile_lifecycle`
WITH generate_date AS (
    -- generate date
    -- '2019-01-01' first coaching date
    {% if params.is_full %}
        -- Full
        SELECT
            observation_date
        FROM UNNEST(GENERATE_DATE_ARRAY(CAST('2019-01-01' AS DATE), DATE('{{ next_ds }}'), INTERVAL 1 DAY)) AS observation_date
    {% else %}
        -- Incremental
        SELECT
            observation_date
        FROM UNNEST(GENERATE_DATE_ARRAY(DATE_SUB(DATE('{{ next_ds }}'), INTERVAL {{ params.interval }}), DATE('{{ next_ds }}'), INTERVAL 1 DAY)) AS observation_date
    {% endif %}
), email_event AS(
    -- select first sub event and last unsub per consent and profile
    SELECT DISTINCT
        eesu.email_profile_master_id,
        eesu.event_type,
        rc.create_date,
        rc.start_date,
        rc.end_date,
        DATE_DIFF(DATE(rc.create_date), rc.start_date, MONTH)                   AS start_step,
        IF(eesu.event_type='sub', DATE(rc.create_date), MAX(DATE(event_date)))  AS event_date
    FROM `{{ params.bq_project }}.refined_data.email_event_sub_unsub_*` AS eesu, UNNEST(email_consent_public_ref) AS email_consent_public_ref
    JOIN `{{ params.bq_project }}.refined_data.coaching_data` AS rc
        ON rc.email_profile_master_id = eesu.email_profile_master_id
    -- get only 'Grossesse' coaching
    WHERE email_consent_public_ref = 'femme_actuelle_coaching_grossesse_nl'
    GROUP BY 1, 2, 3, 4, 5
    ORDER BY 1, 2
), ranked_event AS(
    -- rank "sub/unsub" events by date partitioned by coaching group, pmi and "sub/unsub"
    SELECT
        e.*,
        ROW_NUMBER() OVER(PARTITION BY email_profile_master_id ORDER BY event_date ASC) AS event_rank
    FROM email_event AS e
), valid_pmi AS(
     -- select only pmi that have as event 'sub' --> 'unsub' or only 'sub'
    SELECT
        email_profile_master_id
    FROM ranked_event
), valid_event AS(
     -- get only valid email event
    SELECT DISTINCT
        e.email_profile_master_id,
        e.event_type,
        DATE(e.event_date) AS event_date,
        start_step,
        cs.nl_name AS step_name,
        cs.step AS step_order,
        DENSE_RANK() OVER(PARTITION BY e.email_profile_master_id, e.event_type ORDER BY cs.step) AS step_rank,
    FROM email_event AS e
    JOIN valid_pmi AS v USING(email_profile_master_id)
    JOIN `{{ params.bq_project }}.datastudio.coaching_step_repository` AS cs On cs.coaching_group = "Grossesse" AND cs.step >= e.start_step
), step_range AS(
    -- add start/end date for each step only for 'sub' event
    SELECT
        e.email_profile_master_id,
        e.event_date,
        step_name,
        step_order,
        step_rank,
       -- step start date
        CASE
            -- First step
            WHEN step_rank = 1 THEN event_date
            -- If user start by welcome step
            WHEN step_order - 1 = 0 THEN DATE_ADD(e.event_date, INTERVAL ((step_rank - 1)) DAY)
            ELSE DATE_ADD(e.event_date, INTERVAL ((step_rank - 1) * 30) DAY)
        END AS step_start_date,
        -- add 1 day after welcome step
        CASE
            WHEN CONTAINS_SUBSTR(step_name, 'welcome') THEN DATE_ADD(e.event_date, INTERVAL 1 DAY)
        ELSE
            DATE_ADD(e.event_date, INTERVAL (step_rank * 30) DAY)
        END AS step_end_date,
    FROM valid_event AS e
    WHERE e.event_type = 'sub'
), unsub_step AS (
    -- get unsub step
    SELECT DISTINCT
            sed.email_profile_master_id,
            ue.event_date AS unsub_date,
            step_start_date,
            step_end_date,
            IF(IF(ue.event_date >= step_start_date AND ue.event_date < step_end_date, ue.step_order,  NULL) IS NOT NULL, sed.step_order, NULL) AS unsub_step_order,
    FROM step_range AS sed
    LEFT JOIN valid_event AS ue
        ON ue.email_profile_master_id = sed.email_profile_master_id
    WHERE ue.event_type = "unsub"
        AND IF(IF(ue.event_date >= step_start_date AND ue.event_date < step_end_date, ue.step_order, NULL) IS NOT NULL, sed.step_order, NULL) IS NOT NULL
), final_state AS(
    -- add state = ['in progress', 'done', 'not yet']
    SELECT DISTINCT
        d.observation_date,
        sed.*,
        us.unsub_date,
        us.unsub_step_order,
        -- add states based on observation/sub/unsub date
        CASE
            WHEN us.unsub_date IS NOT NULL AND us.unsub_date >= sed.step_start_date AND us.unsub_date < sed.step_end_date AND d.observation_date >= us.unsub_date                  THEN 'broke'
            WHEN d.observation_date >= sed.step_start_date AND d.observation_date < sed.step_end_date AND IF(us.unsub_date IS NOT NULL, d.observation_date < us.unsub_date, TRUE)   THEN 'in progress'
            WHEN sed.step_order > us.unsub_step_order AND d.observation_date >= us.unsub_date                                                                                       THEN 'not done'
            WHEN d.observation_date >= sed.step_end_date                                                                                                                            THEN 'done'
            WHEN sed.step_start_date > d.observation_date AND IF(us.unsub_date IS NOT NULL, d.observation_date < us.unsub_date, TRUE)                                               THEN 'not yet'
        ELSE NULL
        END AS state
    FROM step_range AS sed, generate_date AS d
    LEFT JOIN unsub_step AS us  ON us.email_profile_master_id = sed.email_profile_master_id
    WHERE observation_date >= sed.event_date
)

-- get only useful columns
SELECT
    observation_date,
    email_profile_master_id,
    step_order,
    step_name,
    state
FROM final_state;
