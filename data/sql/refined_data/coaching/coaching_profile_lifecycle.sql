-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- All coachings are concerned by this query except "Grossesse"
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.datastudio.coaching_profile_lifecycle`(
    observation_date        DATE    NOT NULL    OPTIONS(description="observation date"),
    coaching_group          STRING              OPTIONS(description="enum = ['Minceur','Santé','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON> gaspi','Je fais tout maison'],"),
    email_profile_master_id INTEGER             OPTIONS(description="email profile master id. ref: {{ params.bq_project }}.store_matrix_email.profile_matser_id.id"),     
    step_order              INTEGER             OPTIONS(description="step order"),     
    step_name               STRING              OPTIONS(description="step name"),      
    state                   STRING              OPTIONS(description="enum = ['broke', 'not done', 'done', 'in progress', 'not yet']. 'in progress' --> 'not yet' and 'broke'('unsub') --> 'not done")  
)
PARTITION BY observation_date
CLUSTER BY coaching_group
OPTIONS(description="It contains state for each step by profile by end step date for each coaching."
                  ||"\n\n"
                  ||"DAG: {{ dag.dag_id }}."
                  ||"\n\n"
                  ||"Sync: daily");

{% if params.is_full %}
    TRUNCATE TABLE `{{ params.bq_project }}.datastudio.coaching_profile_lifecycle`;
{% else %}
    DELETE FROM `{{ params.bq_project }}.datastudio.coaching_profile_lifecycle`
    WHERE observation_date >= DATE_SUB(DATE('{{ next_ds }}') , INTERVAL {{ params.interval }});
{% endif %}
-- to be confirmed 
-- coaching rules : 
    -- accepted events: 
        -- * 'sub'
        -- * 'sub' --> 'unsub'
    -- not accepted events:
        -- * 'unsub'
        -- * 'unsub' --> 'sub'
        -- * 'sub' --> 'unsub' --> 'sub'(exemple : 11954, 'Santé') : it means that this profile does only once the coaching
INSERT INTO `{{ params.bq_project }}.datastudio.coaching_profile_lifecycle`
WITH generate_date AS (
    -- generate date
    -- '2019-01-01' first coaching date
    {% if params.is_full %}
        -- Full
        SELECT
            observation_date
        FROM UNNEST(GENERATE_DATE_ARRAY(CAST('2019-01-01' AS DATE), DATE('{{ next_ds }}'), INTERVAL 1 DAY)) AS observation_date
    {% else %}
        -- Incremental
        SELECT
            observation_date
        FROM UNNEST(GENERATE_DATE_ARRAY(DATE_SUB(DATE('{{ next_ds }}'), INTERVAL {{ params.interval }}), DATE('{{ next_ds }}'), INTERVAL 1 DAY)) AS observation_date
    {% endif %}
), email_event AS(
   -- select first sub event and last unsub per consent and profile
    SELECT DISTINCT
        email_profile_master_id,
        event_type,
        email_consent_public_ref,
        -- get first sub and last unsub
        IF(event_type='sub', MIN(event_date), MAX(event_date)) AS event_date,
        CASE
            WHEN email_consent_public_ref = 'femme_actuelle_coaching_minceur_nl'        THEN 'Minceur'
            WHEN email_consent_public_ref = 'femme_actuelle_coaching_sante_nl'          THEN 'Santé'
            WHEN email_consent_public_ref = 'cuisine_actuelle_coaching_patisserie_nl'   THEN 'Pâtisserie'
            WHEN email_consent_public_ref = 'cuisine_actuelle_coaching_maison_nl'       THEN 'Je fais tout maison'
            WHEN email_consent_public_ref = 'cuisine_actuelle_coaching_antigaspi_nl'    THEN 'Anti gaspi'
            WHEN email_consent_public_ref = 'femme_actuelle_coaching_maison_nl'         THEN 'Maison au naturel'
        ELSE NULL END AS coaching_group
    FROM `{{ params.bq_project }}.refined_data.email_event_sub_unsub_*`, UNNEST(email_consent_public_ref) AS email_consent_public_ref
    -- desired coaching
    WHERE email_consent_public_ref IN ('femme_actuelle_coaching_minceur_nl',
                                        'femme_actuelle_coaching_sante_nl',
                                        'cuisine_actuelle_coaching_patisserie_nl',
                                        'cuisine_actuelle_coaching_maison_nl',
                                        'cuisine_actuelle_coaching_antigaspi_nl',
                                        'femme_actuelle_coaching_maison_nl')
    AND email_profile_master_id IS NOT NULL
    GROUP BY 1, 2, 3
), ranked_event AS(
    -- rank "sub/unsub" events by date partitioned by coaching group, pmi and "sub/unsub"
    SELECT
        e.*,
        ROW_NUMBER() OVER(PARTITION BY email_profile_master_id, coaching_group ORDER BY event_date ASC) AS event_rank
    FROM email_event AS e
), valid_pmi AS(
     -- select only pmi that have as event 'sub' --> 'unsub' or only 'sub'
    SELECT
        coaching_group,
        email_profile_master_id,
    FROM ranked_event
    WHERE (event_rank = 1 AND event_type = 'sub')
), valid_event AS(
     -- get only valid email event
    SELECT
        e.email_profile_master_id,
        e.coaching_group,
        e.event_type,
        DATE(e.event_date) AS event_date,
        cs.nl_name  AS step_name,
        cs.step AS step_order
    FROM email_event AS e
    JOIN valid_pmi AS v USING(coaching_group, email_profile_master_id)
    JOIN `{{ params.bq_project }}.datastudio.coaching_step_repository` AS cs USING(coaching_group)
), step_range AS(
    -- add start/end date for each step only for 'sub' event
    SELECT
        e.coaching_group,
        e.email_profile_master_id,
        step_name,
        step_order,
        e.event_date,
        -- step start date
        CASE
            WHEN coaching_group NOT IN ("Santé", "Maison au naturel") THEN
            CASE
                WHEN CONTAINS_SUBSTR(step_name, 'welcome') THEN event_date
                WHEN CONTAINS_SUBSTR(step_name, '1') THEN
                DATE_ADD(event_date, INTERVAL 1 DAY)
            ELSE
                DATE_ADD(event_date, INTERVAL ((step_order - 1)*7) + 1 DAY)
            END
        ELSE
            CASE
                WHEN CONTAINS_SUBSTR(step_name, '1') THEN event_date
            ELSE
                DATE_ADD(event_date, INTERVAL ((step_order - 1) * 7) + 1 DAY)
            END
        END AS step_start_date,
        -- add 1 day after welcome step
        CASE
            WHEN CONTAINS_SUBSTR(step_name, 'welcome') THEN
            DATE_ADD(event_date, INTERVAL 1 DAY)
        ELSE
            DATE_ADD(event_date, INTERVAL (step_order * 7) +1 DAY)
        END AS step_end_date,
    FROM valid_event AS e
    WHERE e.event_type = 'sub'
), unsub_step AS (
    -- get unsub step
    SELECT DISTINCT
            sed.email_profile_master_id,
            sed.coaching_group,
            ue.event_date AS unsub_date,
            IF(IF(ue.event_date >= step_start_date AND ue.event_date < step_end_date, ue.step_order,  NULL) IS NOT NULL, sed.step_order, NULL) AS unsub_step_order,
    FROM step_range AS sed
    LEFT JOIN valid_event AS ue
        ON ue.coaching_group = sed.coaching_group
        AND ue.email_profile_master_id = sed.email_profile_master_id
    WHERE ue.event_type = "unsub"
        AND IF(IF(ue.event_date >= step_start_date AND ue.event_date < step_end_date, ue.step_order,  NULL) IS NOT NULL, sed.step_order, NULL) IS NOT NULL
), final_state AS(
    -- add state = ['in progress', 'done', 'not yet']
    SELECT DISTINCT
        d.observation_date,
        sed.*,
        us.unsub_date,
        us.unsub_step_order,
        -- add states based on observation/sub/unsub date
        CASE
            WHEN us.unsub_date IS NOT NULL AND us.unsub_date >= step_start_date AND us.unsub_date < step_end_date  AND d.observation_date >= us.unsub_date                  THEN 'broke'
            WHEN d.observation_date >= step_start_date AND d.observation_date < step_end_date AND IF(us.unsub_date IS NOT NULL, d.observation_date < us.unsub_date, true)   THEN 'in progress'
            WHEN sed.step_order > us.unsub_step_order AND d.observation_date >= us.unsub_date                                                                               THEN 'not done'
            WHEN d.observation_date >= step_end_date                                                                                                                        THEN 'done'
            WHEN step_start_date > d.observation_date AND IF(us.unsub_date IS NOT NULL, d.observation_date < us.unsub_date, true)                                           THEN 'not yet'
            ELSE NULL
        END AS state
    FROM step_range AS sed, generate_date AS d
    LEFT JOIN unsub_step AS us
        ON us.coaching_group = sed.coaching_group
        AND us.email_profile_master_id = sed.email_profile_master_id
    WHERE observation_date >= sed.event_date
)
-- get only useful columns
SELECT
    observation_date,
    coaching_group,
    email_profile_master_id,
    step_order,
    step_name,
    state
FROM final_state;
