-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- for incremental --> It's cheaper than to put directly in the query as usual
DECLARE time_to_select DATE;
SET time_to_select = DATE(DATE("{{ next_ds }}") - INTERVAL {{params.batch_interval}});

CREATE TABLE IF NOT EXISTS `{{params.bq_project}}.refined_data.batch_event`(
    
    event_id                    STRING     NOT NULL OPTIONS(description="event uuid"),
    create_date                 TIMESTAMP           OPTIONS(description="event creation datetime"),
    brand_trigram               STRING              OPTIONS(description="brand trigram"),
    support                     STRING              OPTIONS(description="enums as =['android', 'ios', 'web']"),
    custom_user_id              STRING              OPTIONS(description="user uuid"),
    install_id                  STRING              OPTIONS(description="install id"),
    event_type                  STRING              OPTIONS(description="event type as enum = ['push_optin', 'push_optout', 'push_sent', 'push_error', 'token_deleted']"),
    push_type                   STRING              OPTIONS(description="available only for event type sent, opened and error else is null. push type as enum = ['campaign', 'transactional']"),
    campaign_token              STRING              OPTIONS(description="campaign/transactional token available only for event type : push_sent, push_opened, push_error else is NULL"),
    group_id                    STRING              OPTIONS(description="group id available only for event type : push_sent, push_opened, push_error for transactional campaign else is NULL"),
    mobile                      STRUCT<
        notif_state                 STRING              OPTIONS(description="enums = ['on', 'off']"),
        advertising_id              STRING              OPTIONS(description="advertising uuid"),
        position_clicked_cta        INTEGER             OPTIONS(description="click position"),
        cta_action                  STRING             OPTIONS(description="cta action")
    >                                               OPTIONS(description="columns available only for 'android' & 'ios'")
)
PARTITION BY TIMESTAMP_TRUNC(create_date, DAY)
OPTIONS(description="This table contains refined data of all event tables across all brands and supports('android', 'ios', 'web'). "||
                    "Table partitioned by create_date at day scale. "||
                    "DAG: {{ dag.dag_id }}. "||
                    "Sync: daily");

-- to avoid duplicate
{% if params.batch_full_export|lower != 'true'%}
-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
    DELETE FROM `{{params.bq_project}}.refined_data.batch_event`
    WHERE DATE(create_date) >= time_to_select;
{% else %}
    TRUNCATE TABLE `{{params.bq_project}}.refined_data.batch_event`;
{% endif %}
{% for brand in params.brand_list %}
    -- insert into table with incremental process
-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
    INSERT INTO `{{params.bq_project}}.refined_data.batch_event`
    {% if brand|lower not in ["gende", "gmi", "mas", "mma", "ommde", "ommfr", "ommuk"] %}
        WITH event_data AS(
            -- IOS
            SELECT
                event_id,
                create_date,
                "{{brand|upper}}" AS brand_trigram,
                "ios" AS support,
                custom_user_id,
                install_id,
                event_type,
                push_type,
                campaign_token,
                group_id,
                notif_state,
                advertising_id,
                position_clicked_cta,
                cta_action
            FROM `{{params.bq_project}}.store_batch.event_{{brand|lower}}_ios`
            {% if params.batch_full_export|lower != 'true'%}
                WHERE DATE(create_date) >= time_to_select
            {% endif %}
            UNION ALL
            -- Android
            SELECT
                event_id,
                create_date,
                "{{brand|upper}}" AS brand_trigram,
                "android" AS support,
                custom_user_id,
                install_id,
                event_type,
                push_type,
                campaign_token,
                group_id,
                notif_state,
                advertising_id,
                position_clicked_cta,
                cta_action
            FROM `{{params.bq_project}}.store_batch.event_{{brand|lower}}_android`
            {% if params.batch_full_export|lower != 'true'%}
                WHERE DATE(create_date) >= time_to_select
            {% endif %}
            UNION ALL
            -- Web
            SELECT
                event_id,
                create_date,
                "{{brand|upper}}" AS brand_trigram,
                "web" AS support,
                custom_user_id,
                install_id,
                event_type,
                push_type,
                campaign_token,
                group_id,
                NULL AS notif_state,
                NULL AS advertising_id,
                NULL AS position_clicked_cta,
                NULL AS cta_action
            FROM `{{params.bq_project}}.store_batch.event_{{brand|lower}}_web`
            {% if params.batch_full_export|lower != 'true'%}
                WHERE DATE(create_date) >= time_to_select
            {% endif %}
        ), ranked_events AS(
            SELECT
                event_id,
                create_date,
                brand_trigram,
                ROW_NUMBER() OVER(PARTITION BY event_id, brand_trigram, support ORDER BY create_date DESC) AS event_rank,
                support,
                custom_user_id,
                install_id,
                event_type,
                push_type,
                campaign_token,
                group_id,
                STRUCT(
                    notif_state,
                    advertising_id,
                    position_clicked_cta,
                    cta_action
                ) AS mobile
            FROM event_data
        )
        SELECT * EXCEPT(event_rank) FROM ranked_events WHERE event_rank = 1;
    {% else %}
    WITH ranked_events AS(
        -- Web
        SELECT
            event_id,
            create_date,
            "{{brand|upper}}" AS brand_trigram,
            ROW_NUMBER() OVER(PARTITION BY event_id ORDER BY create_date DESC) AS event_rank,
            "web" AS support,
            custom_user_id,
            install_id,
            event_type,
            push_type,
            campaign_token,
            group_id,
            STRUCT(
                CAST(NULL AS STRING) AS notif_state,
                CAST(NULL AS STRING) AS advertising_id,
                CAST(NULL AS INTEGER) AS position_clicked_cta,
                CAST(NULL AS STRING) AS cta_action
            ) AS mobile
        FROM `{{params.bq_project}}.store_batch.event_{{brand|lower}}_web`
        {% if params.batch_full_export|lower != 'true'%}
                WHERE DATE(create_date) >= time_to_select
        {% endif %}
    )
    SELECT * EXCEPT(event_rank) FROM ranked_events WHERE event_rank = 1;
    {% endif %}
{% endfor %}
