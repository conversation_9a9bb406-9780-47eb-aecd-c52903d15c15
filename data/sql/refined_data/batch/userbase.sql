-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE default_datetime TIMESTAMP;
SET default_datetime = (SELECT TIMESTAMP("1900-01-01 00:00:00"));       -- used to replace null install_date / last_visit_date

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.refined_data.batch_userbase`(
    install_id                  STRING     NOT NULL OPTIONS(description="install id"),
    install_date                TIMESTAMP           OPTIONS(description="install datetime; least value among last_visit_date, install_date and last_push_date. If all NULL, return default_datetime"),
    brand_trigram               STRING              OPTIONS(description="brand trigram"),
    support                     STRING              OPTIONS(description="enums as =['android', 'ios', 'web']"),
    custom_user_id              STRING              OPTIONS(description="user uuid"),
    last_visit_date             TIMESTAMP           OPTIONS(description="last visit datetime; least value among last_visit_date, install_date and last_push_date. If all NULL, return default_datetime"),
    last_push_date              TIMESTAMP           OPTIONS(description="last notification push datetime"),
    city                        STRING              OPTIONS(description="user city"),
    region                      STRING              OPTIONS(description="user region"),
    smart_segment               STRING              OPTIONS(description="segment created by batch based on advanced machine-learning"),
    mobile                      STRUCT<
        advertising_id              STRING              OPTIONS(description="advertising uuid"),
        device_type                 STRING              OPTIONS(description="device type extracted by batch. Not available for web"),
        os_version                  STRING              OPTIONS(description="os version extracted by batch. Not available for web"),
        app_version                 STRING              OPTIONS(description="application version. Not available for web"),
        is_optin                    BOOLEAN             OPTIONS(description="0/1. 1=user give her consent to receive notification")
    >                                               OPTIONS(description="columns available only for 'android' & 'ios'"),
    web                         STRUCT<
        browser                     STRING              OPTIONS(description="web browser as 'Chrome', 'Firefox', ...")
    >                                               OPTIONS(description="columns available only for 'web'")

)
PARTITION BY TIMESTAMP_TRUNC(install_date, DAY)
OPTIONS(description="This table contains refined data of all userbase tables across all brands and supports('android', 'ios', 'web')."||
                    "Table partitioned by install_date at day scale."||
                    "DAG: {{ dag.dag_id }}."||
                    "Sync: daily");

-- to avoid duplicate
TRUNCATE TABLE `{{ params.bq_project }}.refined_data.batch_userbase`;

-- put all userbase information into one table as refined data
{% for brand in params.brand_list %}
    -- insert into table with incremental process
    INSERT INTO `{{ params.bq_project }}.refined_data.batch_userbase`
    {% if brand|lower not in ["gende", "gmi", "mas", "mma", "ommde", "ommfr", "ommuk"] %}
        WITH userbase_data AS (
            -- Ios
            SELECT
                install_id,
                install_date,
                "{{ brand|upper }}" AS brand_trigram,
                "ios" AS support,
                custom_user_id,
                last_visit_date,
                last_push_date,
                city,
                region,
                REPLACE(LOWER(smart_segment), '_', ' ') AS smart_segment,
                advertising_id,
                device_type,
                os_version,
                app_version,
                is_optin,
                NULL AS browser
            FROM `{{ params.bq_project }}.store_batch.userbase_{{ brand|lower }}_ios`
            UNION ALL
            -- Android
            SELECT
                install_id,
                install_date,
                "{{ brand|upper }}" AS brand_trigram,
                "android" AS support,
                custom_user_id,
                last_visit_date,
                last_push_date,
                city,
                region,
                REPLACE(LOWER(smart_segment), '_', ' ') AS smart_segment,
                advertising_id,
                device_type,
                os_version,
                app_version,
                is_optin,
                NULL AS browser
            FROM `{{ params.bq_project }}.store_batch.userbase_{{ brand|lower }}_android`
            UNION ALL
            -- Web
            SELECT
                install_id,
                install_date,
                "{{ brand|upper }}" AS brand_trigram,
                "web" AS support,
                custom_user_id,
                last_visit_date,
                last_push_date,
                city,
                region,
                REPLACE(LOWER(smart_segment), '_', ' ') AS smart_segment,
                NULL AS advertising_id,
                NULL AS device_type,
                NULL AS os_version,
                NULL AS app_version,
                NULL AS is_optin,
                browser
            FROM `{{ params.bq_project }}.store_batch.userbase_{{ brand|lower }}_web`
        ), get_store_data AS (
            SELECT
                install_id,
                install_date,
                brand_trigram,
                support,
                custom_user_id,
                last_visit_date,
                last_push_date,
                city,
                region,
                smart_segment,
                STRUCT(
                    advertising_id,
                    device_type,
                    os_version,
                    app_version,
                    is_optin
                ) AS mobile,
                STRUCT(
                    browser
                ) AS web
            FROM userbase_data
        ),
    {% else %}
        WITH get_store_data AS (
            -- Web
            SELECT
                install_id,
                install_date,
                "{{ brand|upper }}" AS brand_trigram,
                "web" AS support,
                custom_user_id,
                last_visit_date,
                last_push_date,
                city,
                region,
                REPLACE(LOWER(smart_segment), '_', ' ') AS smart_segment,
                STRUCT(
                    CAST(NULL AS String) AS advertising_id,
                    CAST(NULL AS String) AS device_type,
                    CAST(NULL AS String) AS os_version,
                    CAST(NULL AS STRING) AS app_version,
                    CAST(NULL AS BOOLEAN) AS is_optin
                ) AS mobile,
                STRUCT(
                    browser
                ) AS web
            FROM `{{ params.bq_project }}.store_batch.userbase_{{ brand|lower }}_web`
        ),
    {%- endif -%}
    list_install_id AS (
        -- Get PMC uuid for each install ID (cf. https://pmdtech.atlassian.net/browse/ITDATA-4647)
        -- This CTE links one install id to one custom user id
        SELECT
            install_id,
            ARRAY_AGG(custom_user_id ORDER BY IF(custom_user_id IS NULL, 0, 1) DESC, install_date DESC, last_visit_date DESC)[ORDINAL(1)] AS custom_user_id
        FROM get_store_data
        GROUP BY ALL
    ), get_unique_install_id AS (
        -- Returns one line by (install_id, brand_trigram, support), with the most recent associated info
        -- Avoids having multiple lines for the same (install_id, brand_trigram, support), with different information (city, region, smart_segment...)
        SELECT
            install_id,
            ARRAY_AGG(install_date ORDER BY IF(install_date IS NULL, 0, 1) DESC, last_visit_date DESC)[ORDINAL(1)] AS install_date,
            brand_trigram,
            support,
            MAX(last_visit_date) AS last_visit_date,
            MAX(last_push_date) AS last_push_date,
            ARRAY_AGG(city ORDER BY last_visit_date DESC)[ORDINAL(1)] AS city,
            ARRAY_AGG(region ORDER BY last_visit_date DESC)[ORDINAL(1)] AS region,
            ARRAY_AGG(smart_segment ORDER BY last_visit_date DESC)[ORDINAL(1)] AS smart_segment,
            ARRAY_AGG(mobile ORDER BY last_visit_date DESC)[ORDINAL(1)] AS mobile,
            ARRAY_AGG(web ORDER BY last_visit_date DESC)[ORDINAL(1)] AS web
        FROM get_store_data
        GROUP BY ALL
    )

    SELECT
        gup.install_id,
        IFNULL(gup.install_date, LEAST(IFNULL(gup.last_visit_date, default_datetime), IFNULL(gup.last_push_date, default_datetime))) AS install_date,
        gup.brand_trigram,
        gup.support,
        lid.custom_user_id,
        IFNULL(gup.last_visit_date, LEAST(IFNULL(gup.install_date, default_datetime), IFNULL(gup.last_push_date, default_datetime))) AS last_visit_date,
        gup.last_push_date,
        gup.city,
        gup.region,
        gup.smart_segment,
        gup.mobile,
        gup.web
    FROM get_unique_install_id AS gup
    LEFT JOIN list_install_id AS lid USING(install_id);
{% endfor %}
