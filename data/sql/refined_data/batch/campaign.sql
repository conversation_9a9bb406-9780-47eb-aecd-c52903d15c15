-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- for incremental --> It's cheaper than to put directly in the query as usual
DECLARE time_to_select DATE;
SET time_to_select = DATE(DATE("{{ next_ds }}") - INTERVAL {{params.batch_interval}});
CREATE TABLE IF NOT EXISTS `{{ params.bq_project.matrix }}.refined_data.batch_campaign`(
    
    campaign_token              STRING     NOT NULL OPTIONS(description="campaign uuid. ref: pm-prod-matrix.store_batch.campaign_*_*.payload#campaign_token"),
    create_date                 TIMESTAMP           OPTIONS(description="campaign creation datetime. ref: pm-prod-matrix.store_batch.campaign_*_*.payload#created_date"),
    campaign_name               STRING              OPTIONS(description="campaign name. ref: pm-prod-matrix.store_batch.campaign_*_*.payload#name"),
    brand_trigram               STRING              OPTIONS(description="brand trigram"),
    support                     STRING              OPTIONS(description="enums as =['android', 'ios', 'web']"),
    push_date                   TIMESTAMP           OPTIONS(description="notification push time. ref: pm-prod-matrix.store_batch.campaign_*_*.payload#push_time/local_push_time"),
    deep_link                   STRING              OPTIONS(description="deep link extracted from payload. ref: pm-prod-matrix.store_batch.campaign_*_*.payload#deeplink"),
    source                      STRING              OPTIONS(description="source extracted from deep link column"),
    medium                      STRING              OPTIONS(description="medium extracted from deep link column"),
    article                     STRUCT<
        id                      STRING                  OPTIONS(description="article id. ref: {{ params.bq_project.mirror }}.store_one_article.article#id"),
        publication_date        TIMESTAMP               OPTIONS(description="article publication datetime"),
        public_url              STRING                  OPTIONS(description="article public url"),
        format                  STRING                  OPTIONS(description="enum = ['ARTICLE', 'VIDEO', 'SLIDESHOW', 'RECIPE']"),
        title                   STRING                  OPTIONS(description="article title"),
        tag                     STRUCT<
            title                   STRING                  OPTIONS(description="article tag"),
            type                    STRING                  OPTIONS(description="enum = ['person', 'politic', ...]")
        >                                               OPTIONS(description="article tag information"),
        category                STRUCT<
            first                   STRING                  OPTIONS(description="first category"),
            second                  STRING                  OPTIONS(description="second category"),
            third                   STRING                  OPTIONS(description="third category"),
            fourth                  STRING                  OPTIONS(description="fourth category"),
            fifth                   STRING                  OPTIONS(description="fifth category")
        >                                               OPTIONS(description="article category information")

    >                                               OPTIONS(description="article information")
)
PARTITION BY TIMESTAMP_TRUNC(create_date, DAY)
OPTIONS(description="This table contains refined data of all campaigns tables across all brands and supports('android', 'ios', 'web'). \n\n"||
                    "We add also some article information based on campaign deep link"||
                    "Table partitioned by create_date at day scale. \n\n"||
                    "DAG: {{ dag.dag_id }}. \n\n"||
                    "Sync: daily");

-- to avoid duplicate
{% if params.batch_full_export|lower != 'true'%}
-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
    DELETE FROM `{{ params.bq_project.matrix }}.refined_data.batch_campaign`
    WHERE DATE(create_date) >= time_to_select;
{% else %}
    TRUNCATE TABLE `{{ params.bq_project.matrix }}.refined_data.batch_campaign`;
{% endif %}
{% for brand in params.brand_list %}
    -- insert into table with incremental process
-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
    INSERT INTO `{{ params.bq_project.matrix }}.refined_data.batch_campaign`
    {% if brand|lower not in ["gende", "gmi", "mas", "mma", "ommde", "ommfr", "ommuk"] %}
        WITH campaign_data AS(
            -- Ios
            SELECT
                campaign_id AS campaign_token,
                create_date,
                campaign_name,
                "{{brand|upper}}" AS brand_trigram,
                "ios" AS support,
                push_time AS push_date,
                deep_link,
                REPLACE(
                    REPLACE(
                        REGEXP_REPLACE(
                            REGEXP_EXTRACT(deep_link, '^([^/?#]+(?:/[^?#]*)?)(?:[?][^#]+)?(?:#.*)?$'), r'\/amp\/','/')
                        ,  'https://', '')
                    , 'http://', ''
                ) AS public_url,
                REGEXP_EXTRACT(deep_link, '[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}') AS article_uuid,
                REGEXP_EXTRACT(deep_link, ".*utm_source=([^&]*)") AS source,
                REGEXP_EXTRACT(deep_link, ".*utm_medium=([^&]*)") AS medium
            FROM `{{ params.bq_project.matrix }}.store_batch.campaign_{{brand|lower}}_ios`
            {% if params.batch_full_export|lower != 'true'%}
                WHERE DATE(create_date) >= time_to_select
            {% endif %}
            UNION ALL
            -- Android
            SELECT
                campaign_id AS campaign_token,
                create_date,
                campaign_name,
                "{{brand|upper}}" AS brand_trigram,
                "android" AS support,
                push_time AS push_date,
                deep_link,
                REPLACE(
                    REPLACE(
                        REGEXP_REPLACE(
                            REGEXP_EXTRACT(deep_link, '^([^/?#]+(?:/[^?#]*)?)(?:[?][^#]+)?(?:#.*)?$'), r'\/amp\/','/')
                        ,  'https://', '')
                    , 'http://', ''
                ) AS public_url,
                REGEXP_EXTRACT(deep_link, '[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}') AS article_uuid,
                REGEXP_EXTRACT(deep_link, ".*utm_source=([^&]*)") AS source,
                REGEXP_EXTRACT(deep_link, ".*utm_medium=([^&]*)") AS medium
            FROM `{{ params.bq_project.matrix }}.store_batch.campaign_{{brand|lower}}_android`
            {% if params.batch_full_export|lower != 'true'%}
                WHERE DATE(create_date) >= time_to_select
            {% endif %}
            UNION ALL
            -- Web
            SELECT
                campaign_id AS campaign_token,
                create_date,
                campaign_name,
                "{{brand|upper}}" AS brand_trigram,
                "web" AS support,
                push_time AS push_date,
                deep_link,
                REPLACE(
                    REPLACE(
                        REGEXP_REPLACE(
                            REGEXP_EXTRACT(deep_link, '^([^/?#]+(?:/[^?#]*)?)(?:[?][^#]+)?(?:#.*)?$'), r'\/amp\/','/')
                        ,  'https://', '')
                    , 'http://', ''
                ) AS public_url,
                REGEXP_EXTRACT(deep_link, '[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}') AS article_uuid,
                REGEXP_EXTRACT(deep_link, ".*utm_source=([^&]*)") AS source,
                REGEXP_EXTRACT(deep_link, ".*utm_medium=([^&]*)") AS medium
            FROM `{{ params.bq_project.matrix }}.store_batch.campaign_{{brand|lower}}_web`
            {% if params.batch_full_export|lower != 'true'%}
                WHERE DATE(create_date) >= time_to_select
            {% endif %}
        ), article_information AS (
            -- article basic information
            SELECT
                article_id,
                publication_date,
                content.title AS article_title,
                format AS article_format
            FROM `{{ params.bq_project.mirror }}.refined_data.article`
            WHERE
                status = "published"
                AND
                publication_date IS NOT NULL
        ), unnest_urls AS (
            -- unnest article public urls
            SELECT
                article_id,
                public_url AS article_url
            FROM `{{ params.bq_project.mirror }}.refined_data.article` AS a, UNNEST(content.url.public) AS public_url
        ), unnest_tags AS (
            -- unnest article tags
            SELECT
                article_id,
                tag.title   AS tag_title,
                tag.type    AS tag_type
            FROM `{{ params.bq_project.mirror }}.refined_data.article` AS a, UNNEST(content.tag) AS tag
        ), unnest_categories AS (
            -- unnest article categories
            SELECT
                article_id,
                category.cat_1 AS first_category,
                category.cat_2 AS second_category,
                category.cat_3 AS third_category,
                category.cat_4 AS fourth_category,
                category.cat_5 AS fifth_category
            FROM `{{ params.bq_project.mirror }}.refined_data.article`AS a , UNNEST(content.category) AS category
        ), article_data AS(
            -- join together all article information
            SELECT DISTINCT
                a.* ,
                u.article_url AS public_url,
                t.* EXCEPT(article_id),
                cat.* EXCEPT(article_id)
            FROM article_information    AS a
            LEFT JOIN unnest_urls       AS u    ON a.article_id = u.article_id
            LEFT JOIN unnest_tags       AS t    ON a.article_id = t.article_id
            LEFT JOIN unnest_categories AS cat  ON a.article_id = cat.article_id
        )
        -- add article information to push campaign's
        SELECT
            campaign_token,
            create_date,
            campaign_name,
            brand_trigram,
            support,
            push_date,
            deep_link,
            source,
            medium,
            STRUCT(
                COALESCE(a.article_id, b.article_id)        AS id,
                COALESCE(a.publication_date, b.publication_date),
                COALESCE(a.public_url, b.public_url),
                COALESCE(a.article_format, b.article_format)   AS format,
                COALESCE(a.article_title, b.article_title)    AS title,
                STRUCT(
                    COALESCE(a.tag_title, b.tag_title) AS title,
                    COALESCE(a.tag_type, b.tag_type)  AS type
                ) AS tag,
                STRUCT(
                    COALESCE(a.first_category, b.first_category)    AS first,
                    COALESCE(a.second_category, b.second_category)   AS second,
                    COALESCE(a.third_category, b.third_category)    AS third,
                    COALESCE(a.fourth_category, b.fourth_category)   AS fourth,
                    COALESCE(a.fifth_category, b.fifth_category)    AS fifth
                ) AS category
            ) AS article
        FROM campaign_data      AS c
        LEFT JOIN article_data  AS a USING (public_url)
        LEFT JOIN article_data  AS b ON c.article_uuid = b.article_id;
    {% else %}
        -- Web
        WITH campaign_data AS(
            SELECT
                campaign_id AS campaign_token,
                create_date,
                campaign_name,
                "{{brand|upper}}" AS brand_trigram,
                "web" AS support,
                push_time AS push_date,
                deep_link,
                REPLACE(
                        REPLACE(
                            REGEXP_REPLACE(
                                REGEXP_EXTRACT(deep_link, '^([^/?#]+(?:/[^?#]*)?)(?:[?][^#]+)?(?:#.*)?$'), r'\/amp\/','/')
                            ,  'https://', '')
                        , 'http://', ''
                ) AS public_url,
                REGEXP_EXTRACT(deep_link, '[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}') AS article_uuid,
                REGEXP_EXTRACT(deep_link, ".*utm_source=([^&]*)") AS source,
                REGEXP_EXTRACT(deep_link, ".*utm_medium=([^&]*)") AS medium
            FROM `{{ params.bq_project.matrix }}.store_batch.campaign_{{brand|lower}}_web`
        ), article_information AS (
            -- article basic information
            SELECT
                article_id,
                publication_date,
                content.title AS article_title,
                format AS article_format
            FROM `{{ params.bq_project.mirror }}.refined_data.article`
            WHERE
                status = "published"
                AND
                publication_date IS NOT NULL
        ), unnest_urls AS (
            -- unnest article public urls
            SELECT
                article_id,
                public_url AS article_url
            FROM `{{ params.bq_project.mirror }}.refined_data.article` AS a, UNNEST(content.url.public) AS public_url
        ), unnest_tags AS (
            -- unnest article tags
            SELECT
                article_id,
                tag.title   AS tag_title,
                tag.type    AS tag_type
            FROM `{{ params.bq_project.mirror }}.refined_data.article` AS a, UNNEST(content.tag) AS tag
        ), unnest_categories AS (
            -- unnest article categories
            SELECT
                article_id,
                category.cat_1 AS first_category,
                category.cat_2 AS second_category,
                category.cat_3 AS third_category,
                category.cat_4 AS fourth_category,
                category.cat_5 AS fifth_category
            FROM `{{ params.bq_project.mirror }}.refined_data.article`AS a , UNNEST(content.category) AS category
        ), article_data AS(
            -- join together all article information
            SELECT DISTINCT
                a.* ,
                u.article_url AS public_url,
                t.* EXCEPT(article_id),
                cat.* EXCEPT(article_id)
            FROM article_information    AS a
            LEFT JOIN unnest_urls       AS u    ON a.article_id = u.article_id
            LEFT JOIN unnest_tags       AS t    ON a.article_id = t.article_id
            LEFT JOIN unnest_categories AS cat  ON a.article_id = cat.article_id
        )
        SELECT
            campaign_token,
            create_date,
            campaign_name,
            brand_trigram,
            support,
            push_date,
            deep_link,
            source,
            medium,
            STRUCT(
                COALESCE(a.article_id, b.article_id)        AS id,
                COALESCE(a.publication_date, b.publication_date),
                COALESCE(a.public_url, b.public_url),
                COALESCE(a.article_format, b.article_format)   AS format,
                COALESCE(a.article_title, b.article_title)    AS title,
                STRUCT(
                    COALESCE(a.tag_title, b.tag_title) AS title,
                    COALESCE(a.tag_type, b.tag_type)  AS type
                ) AS tag,
                STRUCT(
                    COALESCE(a.first_category, b.first_category)    AS first,
                    COALESCE(a.second_category, b.second_category)   AS second,
                    COALESCE(a.third_category, b.third_category)    AS third,
                    COALESCE(a.fourth_category, b.fourth_category)   AS fourth,
                    COALESCE(a.fifth_category, b.fifth_category)    AS fifth
                ) AS category
            ) AS article
        FROM campaign_data AS c
        LEFT JOIN article_data AS a USING (public_url)
        LEFT JOIN article_data AS b ON c.article_uuid = b.article_id;
    {% endif %}
{% endfor %}