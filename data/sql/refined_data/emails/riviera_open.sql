-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- declare incremental condition
DECLARE start_date DATE DEFAULT DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL {{ params.interval }});
DECLARE end_date DATE DEFAULT DATE("{{ data_interval_end }}");

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.refined_data.riviera_open`(
    email_profile_master_id     INTEGER     NOT NULL OPTIONS(description="Email profile master id. ref: {{ params.bq_project }}.store_matrix_email.profile_master_id#id."),
    open_date                   TIMESTAMP   NOT NULL OPTIONS(description="Open datetime."),
    email_consent_id            INTEGER     NOT NULL OPTIONS(description="Email consent public Id. ref: {{ params.bq_project }}.store_karinto.email_consent#public_id."),
    rogue_one_email_id          INTEGER     NOT NULL OPTIONS(description="Rogue-one email Id."),
    email_consent_public_ref    STRING               OPTIONS(description="Email consent public ref. ref: {{ params.bq_project }}.store_karinto.email_consent#consent_public_ref."),
    theme                       STRING               OPTIONS(description="Email theme. ref: {{ params.bq_project }}.refined_data.splio_report_link#theme."),
    PRIMARY KEY(email_profile_master_id, open_date, email_consent_id, rogue_one_email_id) NOT ENFORCED
)
PARTITION BY TIMESTAMP_TRUNC(open_date, DAY)
CLUSTER BY email_profile_master_id, email_consent_public_ref,  email_consent_id, rogue_one_email_id
OPTIONS(
    description="Refined open events from riviera open events:\n"
              ||"- Remove duplicates from store.\n"
              ||"- Match open to email consent public ref / email theme.\n\n"
              ||"DAG: {{ dag.dag_id }}.\n\n"
              ||"Sync: daily."
);

{% if params.is_full %}
    TRUNCATE TABLE `{{ params.bq_project }}.refined_data.riviera_open`;
    SET start_date = DATE("{{ params.start_date }}");
    SET end_date = DATE("{{ params.end_date }}");
{% else %}
    DELETE FROM `{{ params.bq_project }}.refined_data.riviera_open`
    WHERE DATE(open_date) BETWEEN start_date AND end_date;
{% endif %}

INSERT INTO `{{ params.bq_project }}.refined_data.riviera_open`
-- get open riviera tracking
SELECT
    profile_master_id                           AS email_profile_master_id,
    event_date                                  AS open_date,
    public_id                                   AS email_consent_id,
    IFNULL(SAFE_CAST(idCampaign AS INT64), 0)   AS rogue_one_email_id,
    eb.consent_public_ref                       AS email_consent_public_ref,
    rgt.theme
FROM `{{ params.bq_project }}.store_tracking.riviera_full_data`         AS ro
LEFT JOIN `{{ params.bq_project }}.refined_data.email_base`             AS eb ON eb.consent_public_id = public_id
LEFT JOIN `{{ params.bq_project }}.store_matrix_email.rogue_one_theme`  AS rgt ON rgt.rogue_one_email_id = IFNULL(SAFE_CAST(idCampaign AS INT64), 0)
WHERE
    DATE(event_date) BETWEEN start_date AND end_date
    AND event_type = 'open';
