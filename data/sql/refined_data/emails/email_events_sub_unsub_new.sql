-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE start_date DATE DEFAULT DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL {{ params.interval }});
DECLARE end_date DATE DEFAULT DATE("{{ data_interval_end }}");

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.workspace.refined_template__email_event_sub_unsub_YEAR` (
    event_id                 INTEGER     NOT NULL OPTIONS(description="ref: store_matrix_email.email_event.id"),
    event_date               TIMESTAMP            OPTIONS(description="ref: store_matrix_email.email_event.create_date"),
    event_type               STRING               OPTIONS(description="enum: sub, unsub"),
    email_profile_master_id  INTEGER              OPTIONS(description="ref: store_matrix_email.profile_master_id.id"),
    pmc_profile_master_id    INTEGER              OPTIONS(description="ref: store_matrix_pmc.profile_master_id_v2.id"),
    event_source             STRUCT<
        source                  STRING               OPTIONS(description="email event source"),
        medium                  STRING               OPTIONS(description="email event medium")
    >                                             OPTIONS(description="event raw source/medium"),
    source                   STRUCT<
        solution                STRING               OPTIONS(description="enum: pandora, preference-center, other"),
        entity                  STRING               OPTIONS(description="enum: prisma, riviera, webrivage, ownpage, other"),
        type                    STRING               OPTIONS(description="enum: preference-center, data-direct api, unsub theme, unsub tunnel, direct-access"),
        newsletter_email_id     INTEGER              OPTIONS(description="rogue-one email id extracted from the event(no ref FTM)")
    >                                             OPTIONS(description="event aggregated source"),
    email_consent_public_ref ARRAY<STRING>        OPTIONS(description="list of email consent public ref the event applied to. ref: pm-prod-matrix.store_karinto.email_consent.public_ref")
)
PARTITION BY TIMESTAMP_TRUNC(event_date, DAY)
OPTIONS(description="Template table for refined_data.email_event_sub_unsub_YEAR."
                  ||"\n\n"
                  ||"DAG: {{ dag.dag_id }}"
                  ||"\n\n"
                  ||"Sync: daily"
);

-- set variable
{% if params.is_full %}
    -- create a list with all years since 2008 up to the current year
    {%- set years = range(2008, macros.ds_format(next_ds, "%Y-%m-%d", "%Y") | int + 1) -%}
    -- years = [2008, 2009, 2010, ..., 2024]
{%- else -%}
    -- create a list with the years of the current and previous days and return the unique values
    -- ex: on Jan 1st 2024, returns [2024, 2023]; on Jan 2nd 2024, returns [2024]
    {%- set years = [ macros.ds_format(next_ds, "%Y-%m-%d", "%Y"), macros.ds_format(macros.ds_add(next_ds, -1), "%Y-%m-%d", "%Y") ] | map("int") | unique | list -%}
{%- endif %}


{% for year in years %}
    -- Create sub/unsub table yearly as template table "_YEAR"
    CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.refined_data.email_event_sub_unsub_new_{{ year }}0101`
    LIKE `{{ params.bq_project }}.workspace.refined_template__email_event_sub_unsub_YEAR`
    OPTIONS(description="Contains all refined email events sub/unsub."
                      ||"\n"
                      ||"Simulated events:"
                      ||"\n"
                      ||"Blacklist --> unsub and Change email --> unsub then sub."
                      ||"\n\n"
                      ||"DAG: {{ dag.dag_id }}"
                      ||"\n\n"
                      ||"Sync: daily");

    {% if params.is_full %}
        -- Truncate table
        TRUNCATE TABLE `{{ params.bq_project }}.refined_data.email_event_sub_unsub_new_{{ year }}0101`;
    {% endif %}

    -- Merge new events with store into refined data table
    MERGE `{{ params.bq_project }}.refined_data.email_event_sub_unsub_new_{{ year }}0101` AS dst
    USING (
        WITH union_simulated_sub_events AS (
            -- union of sub/unsub events and blacklist/change_email events
            SELECT ee.*
            FROM `{{ params.bq_project }}.store_matrix_email.email_event` ee
            WHERE
                type IN ('sub', 'unsub')
                AND TIMESTAMP_TRUNC(ee.create_date, YEAR) = TIMESTAMP("{{ year }}-01-01")
                -- make sure we do not lose any late events
                {% if not params.is_full  %}
                AND DATE(create_date) BETWEEN start_date AND end_date
                {% endif %}
            UNION DISTINCT
            SELECT ssue.*
            FROM `{{ params.bq_project }}.store_matrix_email.simulated_sub_unsub_event` ssue
            WHERE
                TIMESTAMP_TRUNC(ssue.create_date, YEAR) = TIMESTAMP("{{ year }}-01-01")
                -- make sure we do not lose any late events
                {% if not params.is_full  %}
                AND DATE(create_date) BETWEEN start_date AND end_date
                {% endif %}
        ), sub_unsub_events AS (
            -- get basic event information
            SELECT
                e.id                AS event_id,
                e.create_date       AS event_date,
                type                AS event_type,
                profile_master_id   AS email_profile_master_id,
                payload             AS payload,
                LOWER(IF(
                    CAST(JSON_EXTRACT_SCALAR(payload, '$.ev') AS INT64) < 99,
                    JSON_EXTRACT_SCALAR(payload, '$.da.c.us'),
                    JSON_EXTRACT_SCALAR(payload,'$.source'))
                ) AS source,
                LOWER(IF(
                    CAST(JSON_EXTRACT_SCALAR(payload, '$.ev') AS INT64) < 99,
                    JSON_EXTRACT_SCALAR(payload,'$.da.c.um'),
                    JSON_EXTRACT_SCALAR(payload,'$.medium'))
                ) AS medium,
                IF(
                    CAST(JSON_EXTRACT_SCALAR(payload, '$.ev') AS INT64) < 99,
                    COALESCE(JSON_EXTRACT_STRING_ARRAY(payload,'$.da.c.f'), JSON_EXTRACT_ARRAY(payload,'$.consent_ids')),
                    JSON_EXTRACT_ARRAY(payload,'$.consent_ids')
                ) AS consent_array
            FROM union_simulated_sub_events AS e
            -- keep only valid email profile master id
            JOIN `{{ params.bq_project }}.store_matrix_email.profile_master_id` AS p ON p.id = e.profile_master_id
        ), event_consents AS (
            -- compute and aggregate email base public_ref for each event
            SELECT
                e.event_id,
                e.event_date,
                e.email_profile_master_id,
                ARRAY_AGG(DISTINCT COALESCE(old_eb.consent_public_ref, new_eb.consent_public_ref)
                        IGNORE NULLS
                        ORDER BY COALESCE(old_eb.consent_public_ref, new_eb.consent_public_ref)
                ) AS email_consent_public_ref
            FROM sub_unsub_events AS e, UNNEST(consent_array) AS consent
            LEFT JOIN `{{ params.bq_project }}.refined_data.email_base` AS new_eb ON new_eb.consent_id = SAFE_CAST(consent AS INT64)
            LEFT JOIN `{{ params.bq_project }}.refined_data.email_base` AS old_eb ON old_eb.consent_public_ref = consent
            LEFT JOIN `{{ params.bq_project }}.refined_data.email_event_error`  AS eee
                ON eee.email_event_id = e.event_id
                AND
                    CASE
                        WHEN eee.scope = 'profile' THEN 1 = 1                                                           -- error applied to all consents (cross brand)
                        WHEN eee.scope = 'brand' THEN eee.brand_trigram = new_eb.brand_trigram                          -- error applied to all consents from the brand
                        WHEN eee.scope = 'consent' AND eee.email_consent_public_ref IS NOT NULL THEN eee.email_consent_public_ref = COALESCE(old_eb.consent_public_ref, new_eb.consent_public_ref)   -- error applied only to the consent
                        WHEN eee.scope = 'consent' AND eee.email_consent_public_ref IS NULL THEN 1 = 1                  -- error applied only to the consent (error codes = 444, 445, 446). Only one consent by error event in this case
                    END
            -- keep only valid events !
            WHERE eee.event_id IS NULL
            GROUP BY ALL
        ), event_source AS (
            -- compute event source related information
            SELECT
                e.event_id,
                e.event_date,
                e.email_profile_master_id,
                -- Aggregate source and medium to 'pandora', 'preference-center', 'other' : source_solution
                CASE
                    WHEN e.medium = "pf-center"        THEN "preference-center"
                    WHEN e.medium = "data-direct"      THEN "preference-center"
                    WHEN pandora_event.id IS NOT NULL  THEN "pandora"
                    ELSE "other"
                END AS solution,
                -- Aggregate source and medium to 'prisma', 'riviera', 'webrivage', 'ownpage' and 'other': source_entity
                CASE
                    WHEN STARTS_WITH(e.source, "unsub link")    THEN "prisma"
                    WHEN CONTAINS_SUBSTR(e.source, "riviera")   THEN "riviera"
                    WHEN CONTAINS_SUBSTR(e.source, "webrivage") THEN "webrivage"
                    WHEN CONTAINS_SUBSTR(e.source, "ownpage")   THEN "ownpage"
                    ELSE "other"
                END AS entity,
                -- Aggregate source and medium to preference-center', 'data-direct api', 'theme unsub', 'tunnel unsub', 'direct-access'and 'other': source_type
                CASE
                    WHEN CONTAINS_SUBSTR(e.source, 'theme unsub')   THEN 'unsub theme'
                    WHEN CONTAINS_SUBSTR(e.source, 'tunnel unsub')  THEN 'unsub tunnel'
                    WHEN e.medium = 'pf-center'                     THEN 'preference-center'
                    WHEN e.medium = 'data-direct'                   THEN 'data-direct api'
                    WHEN CONTAINS_SUBSTR(e.source, 'direct access') THEN 'direct-access'
                    ELSE 'other'
                END AS type,
                e.source AS source,
                e.medium AS medium,
                SAFE_CAST(
                    COALESCE(
                        IF(STARTS_WITH(e.source, 'unsub link'),
                        REGEXP_REPLACE(e.source, r"^unsub link - ([0-9]*)$", r"\1"),
                        NULL),
                        IF(STARTS_WITH(e.medium, 'unsub link'),
                        REGEXP_REPLACE(e.medium, r"^unsub link - ([0-9]*)$", r"\1"),
                        NULL)
                    )
                AS INT64) AS newsletter_email_id
            FROM sub_unsub_events AS e
            LEFT JOIN `{{ params.bq_project }}.store_pandora.event` AS pandora_event ON pandora_event.email_event_id = e.event_id
        ), pmc_profile AS (
            -- extract pmc_profile_master_id
            SELECT
                e.event_id,
                e.event_date,
                pd.id.email_profile_master_id AS email_profile_master_id,
                pd.id.pmc_profile_master_id AS pmc_profile_master_id
            FROM sub_unsub_events AS e
            JOIN `{{ params.bq_project }}.business_data.profile_digital_360` AS pd ON pd.id.email_profile_master_id = e.email_profile_master_id
        )
        SELECT
            e.event_id,
            e.event_date,
            e.event_type,
            e.email_profile_master_id,
            pmc.pmc_profile_master_id,
            STRUCT(
                s.source,
                s.medium
            ) AS event_source,
            STRUCT(
                s.solution,
                s.entity,
                s.type,
                s.newsletter_email_id
            ) AS source,
            c.email_consent_public_ref
        FROM sub_unsub_events AS e
        JOIN event_consents AS c
            ON e.event_id = c.event_id
            AND e.event_date = c.event_date
            AND e.email_profile_master_id = c.email_profile_master_id
        JOIN event_source AS s
            ON e.event_id = s.event_id
            AND e.event_date = s.event_date
            AND e.email_profile_master_id = s.email_profile_master_id
        LEFT JOIN pmc_profile AS pmc
            ON e.event_id = pmc.event_id
            AND e.event_date = pmc.event_date
            AND e.email_profile_master_id = pmc.email_profile_master_id
        ) AS ref
            ON dst.event_id = ref.event_id
            AND dst.event_date = ref.event_date
            AND dst.email_profile_master_id = ref.email_profile_master_id
        WHEN MATCHED THEN
            UPDATE SET
                dst.event_type = ref.event_type,
                dst.pmc_profile_master_id = ref.pmc_profile_master_id,
                dst.event_source = ref.event_source,
                dst.source = ref.source,
                dst.email_consent_public_ref = ref.email_consent_public_ref
        WHEN NOT MATCHED BY TARGET THEN
            INSERT(event_id, event_date, event_type, email_profile_master_id, pmc_profile_master_id, event_source, source, email_consent_public_ref)
            VALUES(ref.event_id, ref.event_date, ref.event_type, ref.email_profile_master_id, ref.pmc_profile_master_id, ref.event_source, ref.source, ref.email_consent_public_ref)
        ;

{%- endfor %}
