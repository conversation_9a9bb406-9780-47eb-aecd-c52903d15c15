-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- declare incremental condition
DECLARE start_date DATE DEFAULT DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL {{ params.interval }});
DECLARE end_date DATE DEFAULT DATE("{{ data_interval_end }}");

--------- USEFUL FUNCTIONS -----------------------------------------------------------

-- function to decode URL
CREATE TEMP FUNCTION URLDECODE(url STRING) AS ((
  SELECT SAFE_CONVERT_BYTES_TO_STRING(
    ARRAY_TO_STRING(ARRAY_AGG(
        IF(STARTS_WITH(y, '%'), FROM_HEX(SUBSTR(y, 2)), CAST(y AS BYTES)) ORDER BY i
      ), b''))
  FROM UNNEST(REGEXP_EXTRACT_ALL(url, r"%[0-9a-fA-F]{2}|[^%]+")) AS y WITH OFFSET AS i
));

--------------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `{{ params.bq_project.matrix }}.refined_data.prisma_click`(
    email_profile_master_id     INTEGER     NOT NULL OPTIONS(description="Email profile master id. ref: store_matrix_email.profile_master_id#id."),
    click_date                  TIMESTAMP   NOT NULL OPTIONS(description="Click datetime."),
    universe_name               STRING      NOT NULL OPTIONS(description="Splio universe name based on:\n"
                                                                       ||"- Consent Id for 'nl'/'loy'.\n"
                                                                       ||"- Consent public Id for 'alert'."
                                                                       ||"- Rogue-one Id for activation / nl-shopping / consent theme."),
    email_consent_id            INTEGER     NOT NULL OPTIONS(description="Email consent public id. ref: {{ params.bq_project.matrix }}.store_tracking.prisma_full_data#public_id."),
    rogue_one_email_id          INTEGER     NOT NULL OPTIONS(description="Rogue-one email Id. ref: {{ params.bq_project.matrix }}.store_tracking.prisma_full_data#message_id"),
    email_consent_public_ref    STRING               OPTIONS(description="Email consent public ref based on:\n"
                                                                       ||"- Consent public Id for 'nl'/'loy'.\n"
                                                                       ||"- Consent Id for 'alert'."
                                                                       ||"- Rogue-one Id for activation / nl-shopping / consent theme."),
    theme                       STRING               OPTIONS(description="Email theme. ref: {{ params.bq_project.matrix }}.store_matrix_email.rogue_one_theme.theme"),
    email_type                  STRING      NOT NULL OPTIONS(description="Email type based on universe, as 'static', 'nl', 'loy' ..."),
    clicked_url                 STRING               OPTIONS(description="Canonical clicked url on NL email."),
    url_type                    STRING               OPTIONS(description="Enum = [ads, system, content, NULL]\n"
                                                                       ||"content: article.\n"
                                                                       ||"system: auto-login, mirror url, unsub, ...\n"
                                                                       ||"ads: created ads by Regie, partner ads (ividence,  powerspace), prismashop."),
    url_target                  STRING               OPTIONS(description="Enum = [mirror, powerspace, ividence, prismashop, crea_ads, site_<brand>, desabo, social, legal, auto-login, NULL]."),
    url_position                INTEGER              OPTIONS(description="Url position in NL code."),
    user_agent_hash             STRING               OPTIONS(description="User agent hash which corresponds to the user agent that performed the clicking. ref: {{ params.bq_project.matrix }}.store_tracking.prisma_full_data#http_user_agent."),
    user_agent                  STRING               OPTIONS(description="User agent which corresponds to the user agent that performed the clicking. ref: {{ params.bq_project.matrix }}.store_tracking.prisma_full_data#http_user_agent."),
    article_id                  ARRAY<STRING>        OPTIONS(description="Article Id from ONE base. ref: {{ params.bq_project.mirror }}.store_one_article.article.id for articles, {{ params.bq_project.mirror }}.store_tel_epg_aurora.program.boneUUID for programs, {{ params.bq_project.mirror }}.store_tel_epg_aurora.channel.boneUUID for channels."),
    PRIMARY KEY(email_profile_master_id, click_date, universe_name, email_consent_id, rogue_one_email_id, clicked_url, url_position) NOT ENFORCED
)
PARTITION BY TIMESTAMP_TRUNC(click_date, DAY)
CLUSTER BY email_profile_master_id, universe_name,  email_consent_id, rogue_one_email_id
OPTIONS(
    description="Refined click events from prisma full data tracking table:\n"
              ||"- Remove duplicates from store.\n"
              ||"- Match click to Splio universe.\n"
              ||"- Match click to email consent public ref / email theme.\n"
              ||"- Add click hardware / software information's extracted from 'user-agent'.\n"
              ||"- Add article information from ONE base.\n\n"
              ||"DAG: {{ dag.dag_id }}.\n\n"
              ||"Sync: daily."
);

{% if params.is_full %}
    TRUNCATE TABLE `{{ params.bq_project.matrix }}.refined_data.prisma_click`;
    SET start_date = DATE("{{ params.start_date }}");
    SET end_date = DATE("{{ params.end_date }}");
{% else %}
    DELETE FROM `{{ params.bq_project.matrix }}.refined_data.prisma_click`
    WHERE DATE(click_date) BETWEEN start_date AND end_date;
{% endif %}

INSERT INTO `{{ params.bq_project.matrix }}.refined_data.prisma_click`
-- ⚠️ We refine click events in 3 steps: 
    -- 1️⃣ First: events related to 'nl', 'loy' based on consent public Ids. 
    -- 2️⃣ Second: events related to 'alert' based on consent Ids.
    -- 3️⃣ third: events related to theme like 'activation', 'nl-shopping' and 'consent theme' based on:
                -- http host for nl-shopping.
                -- rogue-one email Ids for activation and consent theme.

-- Why 👆🏻?
    -- 1️⃣: For 'nl', 'loy' public id column is filled by public consent Id.
    -- 2️⃣: For 'alert' public id column is filled by consent Id.
    -- 3️⃣: For 'nl-shopping' public id is set to 0 and there isn't a consent, ie. for 'activation', 'nl-shopping'.

-- We add also:
    -- Article Id from ONE base.
    -- Categorize clicked URL's based on url type and / or clicked URL host.
WITH articles AS (
    SELECT DISTINCT
        article_id,
        REGEXP_EXTRACT(article_url, r"^[^/]+(/.*)") AS url_ending
    FROM `{{ params.bq_project.mirror }}.refined_data.article`,
    UNNEST(content.url.public) AS article_url WITH OFFSET OFF
    )
, programs AS (
    SELECT DISTINCT
        program_id,
        urls.url
    FROM `{{ params.bq_project.mirror }}.refined_data.program`,
    UNNEST(url_list) urls
    )
, channels AS (
    SELECT DISTINCT
        channel_id,
        urls.url
    FROM `{{ params.bq_project.mirror }}.refined_data.channel`,
    UNNEST(url_list) urls
    )
, click_info AS (
    -- extract click info as clicked URL
    SELECT DISTINCT
        profile_master_id   AS email_profile_master_id,
        datetime            AS click_date,
        IF(rose.rogue_one_email_id IS NOT NULL, TRUE, FALSE) AS is_static_email,
        COALESCE(uebc.universe_type, ueba.universe_type, uebt.universe_type) AS universe_type,
        COALESCE(eba.consent_type, ebc.consent_type) AS consent_type,
        CASE
        -- nl-shopping 👇🏻
            WHEN pfd.http_host LIKE '%la-selection-privee.fr' THEN "nl_shopping"
            ELSE COALESCE(ut.universe_name, uebc.universe_name, ueba.universe_name)
        END                             AS universe_name,
        COALESCE(uebc.brand_trigram, ueba.brand_trigram, ut.brand_trigram) AS brand_trigram,
        public_id                       AS email_consent_id,
        SAFE_CAST(message_id AS INT64)  AS rogue_one_email_id,
        CASE
            -- nl-shopping 👇🏻
            WHEN pfd.http_host LIKE '%la-selection-privee.fr' THEN NULL
            ELSE COALESCE(uebc.email_consent_public_ref, ueba.email_consent_public_ref)
        END                             AS email_consent_public_ref,
        ut.theme                        AS theme,
        REPLACE(REPLACE(REGEXP_REPLACE(COALESCE(REGEXP_EXTRACT(URLDECODE(url_redirect), r'^([^/?#]+(?:/[^?#]*)?)(?:[?][^#]+)?(?:#.*)?$'), URLDECODE(url_redirect)), r'\?.*$', ''), 'https://', ''), 'http://', '' ) AS clicked_url,
        link_type,
        SAFE_CAST(link_number AS INT64) AS url_position,
        TO_HEX(SHA256(http_user_agent)) AS user_agent_hash,
        http_user_agent                 AS user_agent
    FROM `{{ params.bq_project.matrix }}.store_tracking.prisma_full_data`                 AS pfd
    LEFT JOIN `{{ params.bq_project.matrix }}.store_matrix_email.rogue_one_static_email`         AS rose ON SAFE_CAST(pfd.message_id AS INT64) = rose.rogue_one_email_id
    -- nl & loy
    LEFT JOIN `{{ params.bq_project.matrix }}.refined_data.email_base`                    AS ebc  ON pfd.public_id = ebc.consent_public_id
    LEFT JOIN `{{ params.bq_project.matrix }}.refined_data.universe_email_base`           AS uebc ON ebc.consent_public_ref = uebc.email_consent_public_ref
    -- alert
    LEFT JOIN `{{ params.bq_project.matrix }}.refined_data.email_base`                    AS eba  ON pfd.public_id = eba.consent_id
    LEFT JOIN `{{ params.bq_project.matrix }}.refined_data.universe_email_base`           AS ueba ON eba.consent_public_ref = ueba.email_consent_public_ref
    -- theme : activation / nl-shopping / consent theme
    LEFT JOIN `{{ params.bq_project.matrix }}.store_matrix_email.rogue_one_theme`         AS rgt ON rgt.rogue_one_email_id = SAFE_CAST(pfd.message_id AS INT64)
    LEFT JOIN `{{ params.bq_project.matrix }}.refined_data.universe_theme`                AS ut  ON rgt.theme = ut.theme
    LEFT JOIN `{{ params.bq_project.matrix }}.refined_data.universe_email_base`           AS uebt ON ut.universe_name = uebt.universe_name
    WHERE
        DATE(datetime) BETWEEN start_date AND end_date
        AND type = "click"
        -- keep only active consents or nl-shopping.
        -- nl shopping does not have a consent linked, so must be handled differently
        AND IF(
            pfd.http_host LIKE '%la-selection-privee.fr' OR ut.theme IS NOT NULL,                                       -- not null themes and NL shopping are included
            TRUE,
            ebc.consent_is_active OR eba.consent_is_active                                                              -- for other cases, we include only active consents
        )
)
-- add article Id based on clicked URL.
-- categorize clicked URL's.
SELECT
    email_profile_master_id,
    click_date,
    universe_name,
    email_consent_id,
    IFNULL(rogue_one_email_id, 0) AS rogue_one_email_id,
    email_consent_public_ref,
    theme,
    CASE
        WHEN ci.is_static_email THEN "static"
        WHEN ci.email_consent_public_ref IS NULL AND ci.theme LIKE "activation-%" THEN "activation"
        WHEN ci.email_consent_public_ref IS NULL AND ci.theme LIKE "nl-shopping-%" THEN "nl"
        ELSE COALESCE(ci.universe_type, ci.consent_type, "not_set")
    END AS email_type,
    clicked_url,
    -- url type.
    CASE
        -- ads
        WHEN clicked_url LIKE "ipe.%"           THEN "ads"
        WHEN clicked_url LIKE "nla.%"           THEN "ads"
        WHEN clicked_url LIKE "a.pwspace%"      THEN "ads"
        WHEN clicked_url LIKE "%prismashop.%"   THEN "ads"
        WHEN LOWER(link_type) = "a"             THEN "ads"
        -- system
        WHEN clicked_url LIKE "dm%"                     THEN "system"
        WHEN clicked_url LIKE "connect.%"               THEN "system"
        WHEN clicked_url LIKE "www.prismaconnect.fr%"   THEN "system"
        WHEN clicked_url LIKE "%$miroirUrl$%"           THEN "system"
        WHEN clicked_url LIKE "rogue-one.%"             THEN "system"
        WHEN clicked_url LIKE "%main_consent_unsub_domain%" THEN "system"
        WHEN clicked_url LIKE "%api.prismaconnect.fr/prd/legal-validate%" THEN "system"
        -- content
        WHEN LOWER(link_type) = "o"     THEN "content"
        WHEN article_id IS NOT NULL     THEN "content"
        WHEN clicked_url LIKE "%.html"  THEN "content"
        WHEN clicked_url LIKE "actu.%"  THEN "content"
        -- programme-tv
        WHEN clicked_url LIKE "%programme-tv%"   THEN "programme-tv"
        WHEN clicked_url LIKE "%programme.tv%"   THEN "programme-tv"
        -- mobile store
        WHEN clicked_url LIKE "%apple.com%app%"     THEN "mobile-store"
        WHEN clicked_url LIKE "%onelink%"           THEN "mobile-store"
        WHEN clicked_url LIKE "play.google.%"       THEN "mobile-store"
        -- others
        ELSE NULL
    END AS url_type,
    -- url target.
    CASE
        -- ads partner
        WHEN clicked_url LIKE "ipe.%"           THEN "ividence"
        WHEN clicked_url LIKE "nla.%"           THEN "powerspace"
        WHEN clicked_url LIKE "a.pwspace%"      THEN "powerspace"
        WHEN clicked_url LIKE "%prismashop.%"   THEN "prismashop"
        -- created ads
        WHEN LOWER(link_type) = "a" THEN "crea_ads"
        -- unsub
        WHEN clicked_url LIKE "dm%" THEN "unsub"
        WHEN clicked_url LIKE "%main_consent_unsub_domain%" THEN "unsub"
        WHEN clicked_url LIKE "%api.prismaconnect.fr/prd/legal-validate%" THEN "unsub"
        -- login
        WHEN clicked_url LIKE "connect.%"               THEN "auto-login"
        WHEN clicked_url LIKE "www.prismaconnect.fr%"   THEN "auto-login"
        -- mirror link
        WHEN clicked_url LIKE "%$miroirUrl$%"           THEN "mirror"
        -- social
        WHEN  clicked_url LIKE "%facebook.com%" THEN "social"
        WHEN  clicked_url LIKE "%twitter.com%"  THEN "social"
        WHEN  clicked_url LIKE "%linkedin.com%" THEN "social"
        WHEN  clicked_url LIKE "%tiktok.com%"   THEN "social"
        WHEN  clicked_url LIKE "%instagram.com%"THEN "social"
        WHEN  clicked_url LIKE "%pinterest.com%"THEN "social"
        -- legal
        WHEN  clicked_url  LIKE "%www.prismamedia.com/charte-pour-la-protection-des-donnees/%" THEN "legal"
        WHEN  clicked_url  LIKE "%www.prismamedia.com/espace-confidentialite/%" THEN "legal"
        WHEN  clicked_url  LIKE "%api.prismaconnect.fr/prd/legal-validate%" THEN "legal"
        -- site brand for content
        WHEN LOWER(link_type) = "o"     THEN COALESCE(CONCAT(LOWER(brand_trigram), "_site"),"site")
        WHEN COALESCE(a.article_id, programs.program_id, channels.channel_id) IS NOT NULL     THEN COALESCE(CONCAT(LOWER(brand_trigram), "_site"),"site")
        WHEN clicked_url LIKE "%.html"  THEN COALESCE(CONCAT(LOWER(brand_trigram), "_site"),"site")
        WHEN clicked_url LIKE "actu.%"  THEN COALESCE(CONCAT(LOWER(brand_trigram), "_site"),"site")
        -- programme-tv
        WHEN clicked_url LIKE "%programme-tv%"   THEN "programme-tv"
        WHEN clicked_url LIKE "%programme.tv%"   THEN "programme-tv"
        -- jeux
        WHEN clicked_url LIKE "%/page/jeu%" THEN "jeux"
        WHEN clicked_url LIKE "%/jeux/%"    THEN "jeux"
        WHEN clicked_url LIKE "%/jeu/%"     THEN "jeux"
        WHEN clicked_url LIKE "quiz.%"      THEN "jeux"
        -- mobile store
        WHEN clicked_url LIKE "%apple.com%app%"     THEN "mobile-store"
        WHEN clicked_url LIKE "%onelink%"           THEN "mobile-store"
        WHEN clicked_url LIKE "play.google.%"       THEN "mobile-store"

        ELSE NULL
    END AS url_target,
    url_position,
    user_agent_hash,
    user_agent,
    ARRAY_AGG(COALESCE(a.article_id, programs.program_id, channels.channel_id) IGNORE NULLS) AS article_id
FROM click_info AS ci
-- get article information 👇🏻.
LEFT JOIN articles AS a
    ON REGEXP_EXTRACT(ci.clicked_url, r"^[^/]+(/.*)") = a.url_ending
LEFT JOIN programs
    ON REGEXP_EXTRACT(ci.clicked_url, r"^[^/]+(/.*)") = programs.url
LEFT JOIN channels
    ON REGEXP_EXTRACT(ci.clicked_url, r"^[^/]+(/.*)") = channels.url
WHERE
    -- keep only tracked universes.
    universe_name IS NOT NULL
    -- keep only tracked email base (nl shopping is included in the "theme IS NOT NULL" filter)
    AND (email_consent_public_ref IS NOT NULL OR theme IS NOT NULL)
GROUP BY ALL;
