-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.refined_data.splio_report_link`(
    report_id                 INTEGER NOT NULL    OPTIONS(description="Report id. ref: {{ params.bq_project }}.store_tracking.splio_report_link#id."),
    universe_name             STRING  NOT NULL    OPTIONS(description="Splio universe name. ref: {{ params.bq_project }}.store_karinto.universe#name."),
    campaign_ref              STRING  NOT NULL    OPTIONS(description="Campaign Id. ref: {{ params.bq_project }}.store_tracking.splio_report#campaign_ref."),
    message_ref               STRING  NOT NULL    OPTIONS(description="Campaign message Id. ref: {{ params.bq_project }}.store_tracking.splio_report#message_ref."),
    sendout_ref               INTEGER NOT NULL    OPTIONS(description="Campaign send Id. ref: {{ params.bq_project }}.store_tracking.splio_report#message_ref."),
    rogue_one_email_id        INTEGER             OPTIONS(description="Campaign Rogue-one Id extracted from redirect links. ref: {{ params.bq_project }}.store_tracking.splio_report_link#url"),
    email_consent_public_ref  STRING              OPTIONS(description="Email consent public ref fetched from redirect URL's / (universe, consent) mapping. ref: {{ params.bq_project }}.store_tracking.splio_report_link#url / {{ params.bq_project }}.refined_data.universe_email_base."),
    theme                     STRING              OPTIONS(description="Nl-shopping / activation / consent theme fetched from (rogue-one , theme) mapping. ref: {{ params.bq_project }}.store_matrix_email.rogue_one_theme#theme"),
    main_utm_source           STRING              OPTIONS(description="The most occurring  utm source from destination URL's. ref: {{ params.bq_project }}.store_tracking.splio_report_link#url."),
    utm_source_list           ARRAY<STRING>       OPTIONS(description="List of non null utm source's from destination URL's. ref: {{ params.bq_project }}.store_tracking.splio_report_link#url."),
    link                      ARRAY<
        STRUCT<
            position              INTEGER NOT NULL    OPTIONS(description="Url position in shot email code's."),
            destination_url       STRING              OPTIONS(description="Destination url."),
            info                  STRUCT <
                nb_click              INTEGER             OPTIONS(description="Click count on destination URL's."),
                type                  STRING              OPTIONS(description="['ad', 'unsub','social-media', 'auto-login', 'legal', 'mirror']."),
                target                STRING              OPTIONS(description="['powerspace', 'ividence', 'crea_ads', 'prismashop','site_<trigram>', ...]"),
                is_redirect           BOOLEAN             OPTIONS(description="True=redirect link based on URL host.")
            >                                         OPTIONS(description="Link information."),
            tracking              STRUCT<
                utm_source            STRING              OPTIONS(description="[utm source] extracted from URL's."),
                utm_content           STRING              OPTIONS(description="[utm content] extracted from URL's.")
            >                                       OPTIONS(description="Tracking URL's information.")
        >
    >                                             OPTIONS(description="URLs extra-information."),
    email_type                STRING  NOT NULL    OPTIONS(description="Email type based on universe, as 'static', 'nl', 'loy' ..."),
    PRIMARY KEY(report_id, universe_name, campaign_ref, message_ref, sendout_ref) NOT ENFORCED
)
OPTIONS(
    description="⚠️ We keep only campaigns fetched to email base information (email consent, theme). ⚠️\n"
              ||"Refined splio report link's from store: \n"
              ||"- Fetch campaign with email base information (universe, rogue-one Id, consent, theme). \n"
              ||"- Extract destination URL's from Splio links. \n"
              ||"- Extract the most occurring utm source from destination URL's. \n"
              ||"- Extract the most occurring rogue-one Id from destination URL's. \n"
              ||"- Extract first consent Id's != 0. \n"
              ||"- Categorize destination URL's type = ['ad', 'unsub','social-media', 'auto-login', 'legal', 'mirror'].\n"
              ||"- Categorize destination URL's target = ['powerspace', 'ividence', 'crea_ads', 'prismashop','site_<trigram>', ...].\n"
              ||"- Extract tracking information like utm_content & medium from destination URL's. \n"
              ||"\n\n"
              ||"DAG: {{ dag.dag_id }}. \n"
              ||"Sync: Daily.");

TRUNCATE TABLE `{{ params.bq_project }}.refined_data.splio_report_link`;

INSERT INTO `{{ params.bq_project }}.refined_data.splio_report_link`
WITH links AS (
  -- decode splio report links.
  -- add Splio universe name.
  SELECT DISTINCT
    report_id,
    u.name            AS universe_name,
    u.type            AS universe_type,
    sr.campaign_ref,
    sr.message_ref,
    SAFE_CAST(sr.sendout_ref AS INT64) AS sendout_ref,
    link_id,
    NET.HOST(url)     AS url_host,
    url,
    srl.clicks        AS nb_click
  FROM `{{ params.bq_project }}.store_tracking.splio_report_link`  AS srl
  JOIN `{{ params.bq_project }}.store_tracking.splio_report`       AS sr ON sr.id = srl.report_id
  JOIN `{{ params.bq_project }}.store_karinto.universe`            AS u  ON sr.universe_id = u.id
), links_with_destination AS (
    -- compute url destination. In case redirect is used, extract the destination url.
    SELECT
      report_id,
      universe_name,
      universe_type,
      campaign_ref,
      message_ref,
      sendout_ref,
      link_id,
      nb_click,
      url,
      (url_host LIKE "redirect.%") AS is_redirect,
      CASE
        WHEN url_host LIKE "redirect.%" THEN `{{ params.bq_project }}.store_tracking.url_decode`(REGEXP_EXTRACT(url, ".*&u=(.*)$"))
        ELSE url
      END AS destination_url,
      SAFE_CAST(REGEXP_EXTRACT(url, "/P-([0-9]+)-") AS INT64)     AS rogue_one_email_id,
      SAFE_CAST(REGEXP_EXTRACT(url, "[0-9]-([0-9]+)/") AS INT64)  AS email_consent_public_id
    FROM links
), links_with_info AS(
    -- extract information from the link.
    SELECT
        report_id,
        universe_name,
        universe_type,
        campaign_ref,
        message_ref,
        sendout_ref,
        link_id,
        destination_url,
        `{{ params.bq_project }}.store_tracking.website_url_to_brand`(destination_url) AS destination_url_website_brand,
        rogue_one_email_id,
        email_consent_public_id,
        is_redirect,
        nb_click,
        -- GAN info
        REGEXP_EXTRACT(destination_url, ".*&utm_source=([^&]*)")              AS utm_source,
        REGEXP_EXTRACT(destination_url, ".*&utm_content=([^&]*)")             AS utm_content,
        -- add url type
        CASE
            -- native-ads partner
            WHEN destination_url LIKE "%ipe.%"                                             THEN "ad"
            WHEN destination_url LIKE "%nla.%"                                             THEN "ad"
            WHEN destination_url LIKE "%prismashop.fr%"                                    THEN "ad"
            -- sub
            WHEN REGEXP_CONTAINS(destination_url, 'http(s)?://dm([al]?|[0-9]*)\\..*')
                 AND destination_url LIKE '%/sub/%'                                        THEN "sub"
            WHEN REGEXP_CONTAINS(destination_url, "(https?://)?prismashop\\.commander.*")  THEN "sub"
            -- unsub
            WHEN REGEXP_CONTAINS(destination_url, 'http(s)?://dm([al]?|[0-9]*)\\..*')      THEN "unsub"
            -- social media
            WHEN REGEXP_CONTAINS(destination_url, ".*facebook\\.(com|fr).*")               THEN "social-media"
            ----- @fixme: if twitter URL change to X as x.com or another URL, this regexp must be changed
            WHEN REGEXP_CONTAINS(destination_url, ".*twitter\\.(com|fr).*")                THEN "social-media"
            WHEN REGEXP_CONTAINS(destination_url, ".*linkedin\\.(com|fr).*")               THEN "social-media"
            WHEN REGEXP_CONTAINS(destination_url, ".*tiktok\\.(com|fr).*")                 THEN "social-media"
            WHEN REGEXP_CONTAINS(destination_url, ".*instagram\\.(com|fr).*")              THEN "social-media"
            WHEN REGEXP_CONTAINS(destination_url, ".*pinterest\\.(com|fr).*")              THEN "social-media"
            -- auto-login
            WHEN REGEXP_CONTAINS(destination_url, "http(s)?://connect.%")                  THEN "auto-login"
            WHEN destination_url LIKE "%www.prismaconnect.fr%"                             THEN "auto-login"
            -- crea_ad
            WHEN destination_url LIKE "%connect.%"                                         THEN "crea_ad"
            WHEN REGEXP_REPLACE(REGEXP_EXTRACT(destination_url,"&l=[a-z]"),"&l=","") = "a" THEN "crea_ad"
            WHEN REGEXP_EXTRACT(destination_url, ".*&utm_content=([^&]*)") = 'crea_a'      THEN "crea_ad"
            -- legal
            WHEN destination_url LIKE "%www.prismamedia.com/charte%"                       THEN "legal"
            -- mirror
            WHEN destination_url LIKE "%$miroirUrl$%"                                      THEN "mirror"
            -- other
            ELSE "other"
        END AS type,
        CASE
            WHEN destination_url LIKE "%ipe.%"                                             THEN "ividence"
            WHEN destination_url LIKE "%nla.%"                                             THEN "powerspace"
            WHEN destination_url LIKE "%prismashop.fr%"                                    THEN "prismashop"
            -- created ads
            WHEN REGEXP_REPLACE(REGEXP_EXTRACT(destination_url,"&l=[a-z]"),"&l=","") = "a" THEN "crea_ad"
            -- unsub
            WHEN REGEXP_CONTAINS(destination_url, 'http(s)?://dm([al]?|[0-9]*)\\..*')      THEN "unsub"
            -- login
            WHEN REGEXP_CONTAINS(destination_url, "http(s)?://connect.%")                  THEN "auto-login"
            WHEN destination_url LIKE "%www.prismaconnect.fr%"                             THEN "auto-login"
            -- mirror link
            WHEN LOWER(destination_url) LIKE "%$miroirurl$%"                               THEN "mirror"
            WHEN LOWER(destination_url) LIKE "%$mirrorurl$%"                               THEN "mirror"
            -- social
            WHEN REGEXP_CONTAINS(destination_url, ".*facebook\\.(com|fr).*")               THEN "social"
            ----- @fixme: if twitter URL change to X as x.com or another URL, this regexp must be changed
            WHEN REGEXP_CONTAINS(destination_url, ".*twitter\\.(com|fr).*")                THEN "social"
            WHEN REGEXP_CONTAINS(destination_url, ".*linkedin\\.(com|fr).*")               THEN "social"
            WHEN REGEXP_CONTAINS(destination_url, ".*tiktok\\.(com|fr).*")                 THEN "social"
            WHEN REGEXP_CONTAINS(destination_url, ".*instagram\\.(com|fr).*")              THEN "social"
            WHEN REGEXP_CONTAINS(destination_url, ".*pinterest\\.(com|fr).*")              THEN "social"
            -- legal
            WHEN destination_url  LIKE "%www.prismamedia.com/charte%"                      THEN "legal"
            -- site brand for content
            WHEN REGEXP_REPLACE(REGEXP_EXTRACT(destination_url,"&l=[a-z]"),"&l=","") = "o" THEN CONCAT("site")
            WHEN destination_url LIKE "%.html"                                             THEN CONCAT("site")
            -- sub
            WHEN REGEXP_CONTAINS(destination_url, "(https?://)?prismashop\\.commander.*")  THEN "sub"
            -- other
            ELSE "other"
        END AS target
    FROM links_with_destination
), report_utm_source_occurrence AS (
    -- count utm source occurrence by report.
    SELECT
        report_id,
        universe_name,
        universe_type,
        campaign_ref,
        message_ref,
        sendout_ref,
        utm_source,
        rogue_one_email_id,
        email_consent_public_id,
        COUNT(link_id) AS nb_occurrence
    FROM links_with_info
    GROUP BY ALL
) , report_most_info AS (
    -- for each report:
      -- get the most occurred utm_source in destination URL's as main utm source.
      -- get the most occurred rogue_one_email_id in destination URL's as main rogue_one_email_id.
      -- get the first email consent id != 0.
    SELECT DISTINCT
        report_id,
        ARRAY_AGG(utm_source IGNORE NULLS ORDER BY nb_occurrence DESC)[SAFE_ORDINAL(1)]                          AS report_main_utm_source,
        ARRAY_AGG(rogue_one_email_id IGNORE NULLS ORDER BY nb_occurrence DESC)[SAFE_ORDINAL(1)]                  AS report_rogue_one_email_id,
        ARRAY_AGG(email_consent_public_id IGNORE NULLS ORDER BY email_consent_public_id DESC)[SAFE_ORDINAL(1)]  AS email_consent_id
    FROM report_utm_source_occurrence
    GROUP BY 1
), consent_universe_count AS (
    -- count consent number by universe.
    SELECT
        universe_name,
        email_consent_public_ref,
        COUNT(DISTINCT email_consent_public_ref) OVER(PARTITION BY universe_name) AS nb_consent
    FROM `{{ params.bq_project }}.refined_data.universe_email_base`
), mono_consent_universe AS (
    -- get mono consent universes.
    SELECT * EXCEPT(nb_consent)
    FROM consent_universe_count
    WHERE nb_consent = 1
), report_link_information AS (
  -- consolidate all report link information.
    SELECT
        report_id,
        li.universe_name,
        campaign_ref,
        message_ref,
        sendout_ref,
        IFNULL(report_rogue_one_email_id, 0) AS rogue_one_email_id,
        IF(rose.rogue_one_email_id IS NOT NULL, TRUE, FALSE) AS is_static_email,
        COALESCE(eb.consent_type, ebc.consent_type, eba.consent_type) AS consent_type,
        CASE
            WHEN rose.rogue_one_email_id IS NOT NULL THEN rose.last_used_consent_public_ref
            -- IF rogue-one ID exist --> public consent id 👇🏻
            -- IF universe isn't mono-consent --> get email consent public ref based on consent public ref.
            WHEN IFNULL(report_rogue_one_email_id, 0) > 0 AND mcu.universe_name IS NULL     THEN COALESCE(eb.consent_public_ref, ebc.consent_public_ref, rose.last_used_consent_public_ref)
            -- IF universe isn't mono-consent --> get email consent public ref based universe name.
            WHEN IFNULL(report_rogue_one_email_id, 0) > 0 AND mcu.universe_name IS NOT NULL THEN mcu.email_consent_public_ref
            -- IF rogue-one ID doesn't exist  -->  consent id 👇🏻
            -- IF universe isn't mono-consent --> get email consent public ref based on consent public ref.
            WHEN IFNULL(report_rogue_one_email_id, 0) = 0 AND mcu.universe_name IS NULL     THEN eba.consent_public_ref
            -- IF universe isn't mono-consent --> get email consent public ref based universe name.
            WHEN IFNULL(report_rogue_one_email_id, 0) = 0 AND mcu.universe_name IS NOT NULL THEN mcu.email_consent_public_ref
            ELSE NULL
        END email_consent_public_ref,
        rgt.theme,
        report_main_utm_source                                  AS main_utm_source,
        ARRAY_AGG(DISTINCT utm_source IGNORE NULLS)             AS utm_source_list,
        ARRAY_AGG(
            STRUCT(
                link_id,
                destination_url,
                STRUCT(
                    nb_click,
                    IF(
                        li.type = "other" AND destination_url_website_brand IS NOT NULL,
                        "content_" || destination_url_website_brand,
                        li.type
                    ) AS type,
                    IF(
                        li.target = "other" AND destination_url_website_brand IS NOT NULL,
                        "site_" || destination_url_website_brand,
                        li.target
                    ) AS target,
                    is_redirect
                ) AS info,
                STRUCT(
                    utm_source,
                    utm_content
                ) AS tracking
            )
            ORDER BY link_id ASC
        ) AS link
    FROM links_with_info        AS li
    LEFT JOIN report_most_info  AS rs USING(report_id)
    -- JOIN ON consent public ID 👇🏻
    LEFT JOIN `{{ params.bq_project }}.refined_data.email_base`                    AS eb ON eb.consent_public_id = IF(rs.email_consent_id=4, 2288, rs.email_consent_id) --@Fix me: To deal with tele_2_semaines_part on camintersse universe
    -- if consent public id isn't indicated in redirect links, join on rogue-one id's 👇🏻
    LEFT JOIN `{{ params.bq_project }}.store_matrix_email.rogue_one_email_consent` AS rec ON rec.rogue_one_email_id = rs.report_rogue_one_email_id
    LEFT JOIN `{{ params.bq_project }}.refined_data.email_base`                    AS ebc ON ebc.consent_id  = rec.email_consent_id
    -- JOIN ON consent ID👇🏻
    LEFT JOIN `{{ params.bq_project }}.refined_data.email_base`                    AS eba ON eba.consent_id = rs.email_consent_id
    -- theme as nl-shopping and activation
    LEFT JOIN `{{ params.bq_project }}.store_matrix_email.rogue_one_theme`         AS rgt ON rgt.rogue_one_email_id = rs.report_rogue_one_email_id
    -- other cases for alert and crm, join on universe name👇🏻
    LEFT JOIN mono_consent_universe                                                AS mcu ON mcu.universe_name = li.universe_name
    LEFT JOIN `{{ params.bq_project }}.store_matrix_email.rogue_one_static_email`  AS rose ON rs.report_rogue_one_email_id = rose.rogue_one_email_id

    GROUP BY ALL
), tracked_campaigns AS(
    -- keep only campaigns with email base information (email consent, theme)  ! 👇🏻
    SELECT
        rli.*,
        (ueb.universe_name IS NOT NULL OR uet.universe_name IS NOT NULL OR (rose.rogue_one_email_id IS NOT NULL AND rli.email_consent_public_ref IS NOT NULL)) AS is_valid_row,
        CASE
            WHEN rli.is_static_email THEN "static"
            WHEN rli.email_consent_public_ref IS NULL AND rli.theme LIKE "activation-%" THEN "activation"
            WHEN rli.email_consent_public_ref IS NULL AND rli.theme LIKE "nl-shopping-%" THEN "nl"
            ELSE COALESCE(ueb.universe_type, uebt.universe_type, rli.consent_type, "not_set")
        END AS email_type
    FROM report_link_information AS rli
    -- Some report id doesn't has the right composition (universe, consent/theme) 👇🏻
    LEFT JOIN `{{ params.bq_project }}.refined_data.universe_email_base` AS ueb
        ON CONCAT(rli.universe_name, rli.email_consent_public_ref) = CONCAT(ueb.universe_name, ueb.email_consent_public_ref)
    LEFT JOIN `{{ params.bq_project }}.refined_data.universe_theme` AS uet ON CONCAT(rli.universe_name, rli.theme) = CONCAT(uet.universe_name, uet.theme)
    LEFT JOIN `{{ params.bq_project }}.refined_data.universe_email_base` AS uebt ON uebt.universe_name = uet.universe_name
    LEFT JOIN `{{ params.bq_project }}.store_matrix_email.rogue_one_static_email` AS rose ON rose.rogue_one_email_id = rli.rogue_one_email_id
    WHERE
        -- keep campaigns with email base information (email consent, theme)  ! 👇🏻
        rli.email_consent_public_ref IS NOT NULL
        OR rli.theme IS NOT NULL
    GROUP BY ALL
)

-- keep only valid campaigns : The right (universe, consent).
SELECT * EXCEPT(is_valid_row, is_static_email, consent_type)
FROM tracked_campaigns
WHERE is_valid_row;
