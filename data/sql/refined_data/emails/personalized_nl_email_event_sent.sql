-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE start_date DATE DEFAULT DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL {{ params.time_interval }});
DECLARE end_date DATE DEFAULT DATE("{{ data_interval_end }}");

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.refined_data.personalized_nl_email_event_sent`
(
  shoot_date                TIMESTAMP NOT NULL  OPTIONS(description="Shoot Date. ref: {{ params.bq_project }}.store_stream.personalized_nl_email_campaign#shoot_date."),
  email_profile_master_id   INT64     NOT NULL  OPTIONS(description="Email profile master Id. ref: {{ params.bq_project }}.store_matrix_email.profile_master_id."),
  universe_name             STRING    NOT NULL  OPTIONS(description="Universe name. ref: {{ params.bq_project }}.store_karinto.universe#name."),
  campaign_id               STRING    NOT NULL  OPTIONS(description="Campaign Id as concatenation of (shoot_date, '_', rogue_one_email_id)."),
  rogue_one_email_id        INT64     NOT NULL  OPTIONS(description="Rogue-one email ID. ref: {{ params.bq_project }}.store_matrix_email.rogue_one_email_consent."),
  email_consent_public_ref  STRING    NOT NULL  OPTIONS(description="Email consent public ref and '(not set)' for NL-Shopping. ref: {{ params.bq_project }}.store_karinto.email_consent."),
  nl_name                   STRING    NOT NULL  OPTIONS(description="Theme that reflects the Personalized NL name. ref: {{ params.bq_project }}.store_matrix_email.rogue_one_theme."),
  create_date               TIMESTAMP NOT NULL  OPTIONS(description="Row creation datetime"),
  update_date               TIMESTAMP NOT NULL  OPTIONS(description="Row last update datetime"),
  email_subject             STRING    NOT NULL OPTIONS(description="Email subject. ref: {{ params.bq_project }}.store_stream.personalized_nl_email#subject."),
  email_preheader           STRING    NOT NULL OPTIONS(description="Email subject. ref: {{ params.bq_project }}.store_stream.personalized_nl_email#preheader."),
  ab_test_variation         STRING    NOT NULL OPTIONS(description="A/B test variation. Actually, we have two : 'IA subject', 'classic subject'. ref: {{ params.bq_project }}.store_stream.personalized_nl_email#personalized_subject_variation."),
  content ARRAY<
    STRUCT<
      article_order     INT64   NOT NULL OPTIONS(description="Article order in the email body."),
      article_id        STRING  NOT NULL OPTIONS(description="Article uuid."),
      article_url       STRING  NOT NULL OPTIONS(description="Article URL."),
      article_image_url STRING  NOT NULL OPTIONS(description="Article image URL."),
      article_title     STRING  NOT NULL OPTIONS(description="Article title."),
      article_source    STRING  NOT NULL OPTIONS(description="Article source as enum ['top_read', 'recommend']. \n"
                                                        ||"- 'top_read' = article is chosen from 'TOP ARTICLE'.\n "
                                                        ||"- 'recommend' = article is recommended."
      )
    >
  > OPTIONS(description="Shot content information extracted from {{ params.bq_project }}.store_stream.personalized_nl_email#recommended_content."),
  PRIMARY KEY (shoot_date, email_profile_master_id, universe_name, campaign_id, rogue_one_email_id) NOT ENFORCED
)
PARTITION BY TIMESTAMP_TRUNC(shoot_date, DAY)
CLUSTER BY universe_name, email_consent_public_ref, email_profile_master_id
OPTIONS(description="This table contains personalized NL sent email's.\n"
                    ||"\n\n"
                    ||"Sync: Daily. \n"
                    ||"DAG: {{ dag.dag_id }}"
);


MERGE  `{{ params.bq_project }}.refined_data.personalized_nl_email_event_sent` AS dst
USING(
  WITH get_array_data AS (
    -- Extract JSON content as ARRAY
    SELECT *,  JSON_EXTRACT_ARRAY(recommended_content) AS json_content_array
    FROM `{{ params.bq_project }}.store_stream.personalized_nl_email`
    {% if not params.is_full %}
        WHERE DATE(create_date) BETWEEN start_date AND end_date
    {% endif %}
  )
  SELECT
    pnec.shoot_date   AS shoot_date,
    profile_master_id AS email_profile_master_id,
    JSON_VALUE(pnecd.config, '$.smtp-universe') AS universe_name,
    CONCAT(
      FORMAT_DATE("%Y%m%d", DATE(pnec.shoot_date)),
      "_",
      pnec.rogue_one_email_id)        AS campaign_id,
    pnec.rogue_one_email_id           AS rogue_one_email_id,
    IFNULL(eb.consent_public_ref, "(not set)") AS email_consent_public_ref,
    rgt.theme                         AS nl_name,
    CURRENT_TIMESTAMP()   AS create_date,
    CURRENT_TIMESTAMP()   AS update_date,
    pne.subject   AS email_subject,
    pne.preheader AS email_preheader,
    IF(personalized_subject_variation = "set", "IA subject", "classic subject") AS ab_test_variation,
    ARRAY_AGG(
      STRUCT(
        SAFE_CAST(
          JSON_VALUE(json_content, "$.order") AS INT64
        ) AS article_order,
        IFNULL(JSON_VALUE(json_content, "$.idone"), "(not set)")      AS article_id,
        IFNULL(JSON_VALUE(json_content, "$.url"), "(not set)")        AS article_url,
        IFNULL(JSON_VALUE(json_content, "$.image_uri"), "(not set)")  AS article_image_url,
        IFNULL(JSON_VALUE(json_content, "$.title"), "(not set)")      AS article_title,
        IFNULL(JSON_VALUE(json_content, "$.from"), "(not set)")       AS article_source
      )
    ORDER BY SAFE_CAST(JSON_VALUE(json_content, "$.order") AS INT64)
    ) AS content
  FROM get_array_data             AS pne
  JOIN UNNEST(json_content_array) AS json_content
  JOIN `{{ params.bq_project }}.store_stream.personalized_nl_email_campaign`                AS pnec     ON pnec.id = pne.email_campaign_id
  JOIN `{{ params.bq_project }}.store_stream.personalized_nl_email_campaign_definition`     AS pnecd    ON pnecd.id = pnec.email_campaign_definition_id
  LEFT JOIN `{{ params.bq_project }}.store_matrix_email.rogue_one_email_consent`            AS rgec     ON rgec.rogue_one_email_id = pnec.rogue_one_email_id
  LEFT JOIN `{{ params.bq_project }}.refined_data.email_base`                               AS eb       ON eb.consent_id = rgec.email_consent_id
  LEFT JOIN `{{ params.bq_project }}.store_matrix_email.rogue_one_theme`                    AS rgt      ON rgt.rogue_one_email_id = pnec.rogue_one_email_id
  WHERE (pne.status = "SENT" AND pnec.status = "SENT")
  GROUP BY ALL
) AS ref
ON
  dst.shoot_date = ref.shoot_date
  AND dst.email_profile_master_id = ref.email_profile_master_id
  AND dst.universe_name = ref.universe_name
  AND dst.campaign_id = ref.campaign_id
  AND dst.rogue_one_email_id = ref.rogue_one_email_id
WHEN MATCHED THEN
  UPDATE SET
    dst.update_date = CURRENT_TIMESTAMP(),
    dst.email_consent_public_ref = ref.email_consent_public_ref,
    dst.nl_name = ref.nl_name,
    dst.email_subject = ref.email_subject,
    dst.email_preheader = ref.email_preheader,
    dst.ab_test_variation = ref.ab_test_variation,
    dst.content = ref.content

WHEN NOT MATCHED BY TARGET THEN
  INSERT(shoot_date, email_profile_master_id, universe_name, campaign_id, rogue_one_email_id, email_consent_public_ref, nl_name, create_date, update_date, email_subject, email_preheader, ab_test_variation, content)
  VALUES(ref.shoot_date, ref.email_profile_master_id, ref.universe_name, ref.campaign_id, ref.rogue_one_email_id, ref.email_consent_public_ref, ref.nl_name, ref.create_date, ref.update_date, ref.email_subject, ref.email_preheader, ref.ab_test_variation, ref.content);

