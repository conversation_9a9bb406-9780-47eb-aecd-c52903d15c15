-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- declare incremental condition
DECLARE start_date DATE DEFAULT DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL {{ params.interval }});
DECLARE end_date DATE DEFAULT DATE("{{ data_interval_end }}");

--------- USEFUL FUNCTIONS -----------------------------------------------------------

-- function to decode URL
CREATE TEMP FUNCTION URLDECODE(url STRING) AS ((
  SELECT SAFE_CONVERT_BYTES_TO_STRING(
    ARRAY_TO_STRING(ARRAY_AGG(
        IF(STARTS_WITH(y, '%'), FROM_HEX(SUBSTR(y, 2)), CAST(y AS BYTES)) ORDER BY i
      ), b''))
  FROM UNNEST(REGEXP_EXTRACT_ALL(url, r"%[0-9a-fA-F]{2}|[^%]+")) AS y WITH OFFSET AS i
));

--------------------------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS `{{ params.bq_project.matrix }}.refined_data.splio_click`(
    email_profile_master_id     INTEGER            NOT NULL OPTIONS(description="Email profile master id. ref: {{ params.bq_project.matrix }}.store_matrix_email.profile_master_id#id."),
    click_date                  DATE               NOT NULL OPTIONS(description="Click date."),
    universe_name               STRING             NOT NULL OPTIONS(description="Splio universe name. ref: {{ params.bq_project.matrix }}.store_partner.splio_event_click#universe_name."),
    rogue_one_email_id          INTEGER            NOT NULL OPTIONS(description="Rogue-one email Id. ref: {{ params.bq_project.matrix }}.refined_data.splio_report_link#rogue_one_email_id."),
    email_consent_public_ref    STRING                      OPTIONS(description="Email consent public ref. ref: {{ params.bq_project.matrix }}.refined_data.splio_report_link#email_consent_public_ref."),
    theme                       STRING                      OPTIONS(description="Email theme. ref: {{ params.bq_project.matrix }}.refined_data.splio_report_link#theme."),
    email_type                  STRING             NOT NULL OPTIONS(description="Email type based on universe, as 'static', 'nl', 'loy' ..."),
    click_info                  ARRAY<
        STRUCT<
            user_agent_hash             STRING                  OPTIONS(description="User agent hash which corresponds to the user agent that performed the clicking. ref: {{ params.bq_project.matrix }}.store_partner.splio_event_click#user_agent."),
            user_agent                  STRING                  OPTIONS(description="User agent which corresponds to the user agent that performed the clicking. ref: {{ params.bq_project.matrix }}.store_partner.splio_event_click#user_agent."),
            click_datetime              TIMESTAMP               OPTIONS(description="Click datetime list per day and profile."),
            clicked_url                 STRING                  OPTIONS(description="Canonical clicked url on NL email from splio report link. ref: {{ params.bq_project.matrix }}.refined_data.splio_report_link#link#destination_url."),
            url_type                    STRING                  OPTIONS(description="Enum = [ads, system, content, NULL]\n"
                                                                                  ||"content: article.\n"
                                                                                  ||"system: auto-login, mirror url, unsub, ...\n"
                                                                                  ||"ads: created ads by Regie, partner ads( ividence,  powerspace), prismashop."),
            url_target                  STRING                  OPTIONS(description="Enum = [mirror, powerspace, ividence, prismashop, crea_ads, site_<brand>, desabo, social, legal, auto-login, NULL]."),
            url_position                INTEGER                 OPTIONS(description="Url position in NL code."),
            article_id                  STRING                  OPTIONS(description="Article Id from ONE base. ref: {{ params.bq_project.mirror }}.store_one_article.article.id for articles, {{ params.bq_project.mirror }}.store_tel_epg_aurora.program.boneUUID for programs, {{ params.bq_project.mirror }}.store_tel_epg_aurora.channel.boneUUID for channels.")
        >
    >                                                       OPTIONS(description="To keep uniqueness by profile and rogue-one."),
    PRIMARY KEY(email_profile_master_id, click_date, universe_name, rogue_one_email_id, email_consent_public_ref, theme) NOT ENFORCED
)
PARTITION BY click_date
CLUSTER BY email_profile_master_id, universe_name,  rogue_one_email_id
OPTIONS(
    description="⚠️ To refine click events, we use refined_data.splio_report_link table. ⚠️\n"
              ||"Refined click events from splio click events:\n"
              ||"- Remove duplicates from store.\n"
              ||"- Match click to Splio universe.\n"
              ||"- Match click to email consent public ref / email theme.\n"
              ||"- Add click hardware / software information's extracted from 'user-agent'.\n\n"
              ||"DAG: {{ dag.dag_id }}.\n\n"
              ||"Sync: daily."
);

{% if params.is_full %}
    TRUNCATE TABLE `{{ params.bq_project.matrix }}.refined_data.splio_click`;
    SET start_date = DATE("{{ params.start_date }}");
    SET end_date = DATE("{{ params.end_date }}");
{% else %}
    DELETE FROM `{{ params.bq_project.matrix }}.refined_data.splio_click`
    WHERE DATE(click_date) BETWEEN start_date AND end_date;
{% endif %}

INSERT INTO `{{ params.bq_project.matrix }}.refined_data.splio_click`
WITH aurl AS (
    SELECT DISTINCT
        article_id,
        content.url.main_public AS article_url,
        publication_date
    FROM `{{ params.bq_project.mirror }}.refined_data.article`
    )
, acurl AS (
    SELECT DISTINCT
        article_id,
        content.url.main_public AS article_url,
        publication_date
    FROM `{{ params.bq_project.mirror }}.refined_data.article`
    )
, apurl AS (
    SELECT DISTINCT
        article_id,
        article_url,
    FROM `{{ params.bq_project.mirror }}.refined_data.article`
    JOIN UNNEST(content.url.public) AS article_url
    )
, programs AS (
    SELECT DISTINCT
        program_id,
        urls.url
    FROM `{{ params.bq_project.mirror }}.refined_data.program`,
    UNNEST(url_list) urls
    )
, channels AS (
    SELECT DISTINCT
        channel_id,
        urls.url
    FROM `{{ params.bq_project.mirror }}.refined_data.channel`,
    UNNEST(url_list) urls
    )
, url_info AS (
    -- get URL information from splio report link
    SELECT DISTINCT
        message_ref,
        campaign_ref,
        rogue_one_email_id,
        COALESCE(eba.brand_trigram, ut.brand_trigram) AS brand_trigram,
        srl.email_consent_public_ref,
        srl.theme,
        COALESCE(
                REPLACE(REPLACE(REGEXP_REPLACE(
                                  REGEXP_EXTRACT(URLDECODE(SPLIT(link.destination_url, "&u=")[safe_offset(1)]),
                                                  '^([^/?#]+(?:/[^?#]*)?)(?:[?][^#]+)?(?:#.*)?$'),
                                  r'\/amp\/',
                                  '/'), 'https://', ''), 'http://', ''),
                REPLACE(REPLACE(REGEXP_REPLACE(
                                  REGEXP_EXTRACT(URLDECODE(link.destination_url),
                                                  '^([^/?#]+(?:/[^?#]*)?)(?:[?][^#]+)?(?:#.*)?$'),
                                  r'\/amp\/',
                                  '/'), 'https://', ''), 'http://', ''),
                link.destination_url
            )           AS clicked_url,
        link.position   AS url_position,
        srl.email_type
    FROM `{{ params.bq_project.matrix }}.refined_data.splio_report_link` AS srl, UNNEST(link) AS link
    LEFT JOIN `{{ params.bq_project.matrix }}.refined_data.email_base`             AS eba ON eba.consent_public_ref = srl.email_consent_public_ref
    LEFT JOIN `{{ params.bq_project.matrix }}.refined_data.universe_theme`         AS ut ON ut.theme = srl.theme
    -- keep only active consents (nl shopping is included in the "theme IS NOT NULL" filter)
    WHERE IF(
        srl.universe_name = "nl_shopping" OR srl.theme IS NOT NULL,                                                     -- not null themes and NL shopping are included
        TRUE,
        eba.consent_is_active                                                                                           -- for other cases, we include only active consents
    )
)

SELECT
    sec.profile_master_id   AS email_profile_master_id,
    DATE(sec.date_event)    AS click_date,
    sec.universe_name       AS universe_name,
    COALESCE(uic.rogue_one_email_id, uim.rogue_one_email_id) AS rogue_one_email_id,
    COALESCE(uic.email_consent_public_ref, uim.email_consent_public_ref) AS email_consent_public_ref,
    COALESCE(uic.theme, uim.theme) AS theme,
    COALESCE(uic.email_type, uim.email_type, "not_set") AS email_type,
    ARRAY_AGG(
        STRUCT(
            TO_HEX(SHA256(user_agent))  AS user_agent_hash,
            user_agent                  AS user_agent,
            sec.date_event              AS click_datetime,
            COALESCE(uic.clicked_url, uim.clicked_url) AS clicked_url,
            -- url type
            CASE
                -- ads
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "ipe.%"         THEN "ads"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "nla.%"         THEN "ads"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "a.pwspace%"    THEN "ads"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%prismashop.%" THEN "ads"
                -- system
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "dm%"                     THEN "system"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "connect.%"               THEN "system"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "www.prismaconnect.fr%"   THEN "system"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%$miroirUrl$%"           THEN "system"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "rogue-one.%"             THEN "system"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%main_consent_unsub_domain%" THEN "system"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%api.prismaconnect.fr/prd/legal-validate%" THEN "system"
                -- content
                WHEN COALESCE(aurl.article_id, acurl.article_id, apurl.article_id, programs.program_id, channels.channel_id) IS NOT NULL THEN "content"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%.html" THEN "content"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "actu.%" THEN "content"
                -- programme-tv
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%programme-tv%" THEN "programme-tv"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%programme.tv%" THEN "programme-tv"
                -- mobile store
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%apple.com%app%" THEN "mobile-store"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%onelink%"       THEN "mobile-store"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "play.google.%"   THEN "mobile-store"
                -- others
                ELSE NULL
            END AS url_type,
            -- url target
            CASE
                -- ads partner
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "ipe.%"         THEN "ividence"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "nla.%"         THEN "powerspace"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "a.pwspace%"    THEN "powerspace"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%prismashop.%" THEN "prismashop"
                -- unsub
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "dm%" THEN "unsub"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%main_consent_unsub_domain%" THEN "unsub"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%api.prismaconnect.fr/prd/legal-validate%" THEN "unsub"
                -- login
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "connect.%"             THEN "auto-login"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "www.prismaconnect.fr%" THEN "auto-login"
                -- mirror link
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%$miroirUrl$%" THEN "mirror"
                -- social
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%facebook.com%"  THEN "social"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%twitter.com%"   THEN "social"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%linkedin.com%"  THEN "social"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%tiktok.com%"    THEN "social"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%instagram.com%" THEN "social"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%pinterest.com%" THEN "social"
                -- legal
                WHEN COALESCE(uic.clicked_url, uim.clicked_url)  LIKE "%www.prismamedia.com/charte-pour-la-protection-des-donnees/%" THEN "legal"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url)  LIKE "%www.prismamedia.com/espace-confidentialite/%" THEN "legal"
                -- site brand for content
                WHEN COALESCE(aurl.article_id, acurl.article_id, apurl.article_id) IS NOT NULL THEN COALESCE(CONCAT(LOWER(COALESCE(uic.brand_trigram, uim.brand_trigram)), "_site"), "site")
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%.html" THEN COALESCE(CONCAT(LOWER(COALESCE(uic.brand_trigram, uim.brand_trigram)), "_site"), "site")
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "actu.%" THEN COALESCE(CONCAT(LOWER(COALESCE(uic.brand_trigram, uim.brand_trigram)), "_site"), "site")
                -- programme-tv
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%programme-tv%" THEN "programme-tv"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%programme.tv%" THEN "programme-tv"
                -- jeux
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%/page/jeu%" THEN "jeux"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%/jeux/%"    THEN "jeux"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%/jeu/%"     THEN "jeux"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "quiz.%"      THEN "jeux"
                -- mobile store
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%apple.com%app%" THEN "mobile-store"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "%onelink%"       THEN "mobile-store"
                WHEN COALESCE(uic.clicked_url, uim.clicked_url) LIKE "play.google.%"   THEN "mobile-store"
                ELSE NULL
            END AS url_target,
            COALESCE(uic.url_position, uim.url_position) AS url_position,
            COALESCE(aurl.article_id, acurl.article_id, apurl.article_id, programs.program_id, channels.channel_id) AS article_id
        )
    ORDER BY sec.date_event DESC) AS click_info
FROM `{{ params.bq_project.matrix }}.store_partner.splio_event_clicks` AS sec
LEFT JOIN url_info AS uic
    ON sec.link_id = uic.url_position
    AND sec.sendout_ref = uic.campaign_ref
LEFT JOIN url_info AS uim
    ON sec.link_id = uim.url_position
    AND sec.sendout_ref = uim.message_ref
-- get article information 👇🏻. For this case, we will look for an exact match
LEFT JOIN aurl
    ON COALESCE(uic.clicked_url, uim.clicked_url) = aurl.article_url
  -- get article information 👇🏻: For this case, we will look for an ending match
LEFT JOIN acurl
    ON REGEXP_EXTRACT(COALESCE(uic.clicked_url, uim.clicked_url), r"^[^/]+(/.*)") = REGEXP_EXTRACT(acurl.article_url, r"^[^/]+(/.*)")
-- get article information 👇🏻: For this case, we will look for an exact match among all the available URLs
LEFT JOIN apurl
    ON COALESCE(uic.clicked_url, uim.clicked_url) = apurl.article_url
-- get program information
LEFT JOIN programs
    ON REGEXP_EXTRACT(COALESCE(uic.clicked_url, uim.clicked_url), r"^[^/]+(/.*)") = programs.url
-- get channel information
LEFT JOIN channels
    ON REGEXP_EXTRACT(COALESCE(uic.clicked_url, uim.clicked_url), r"^[^/]+(/.*)") = channels.url
WHERE
    DATE(sec.date_event) BETWEEN start_date AND end_date
    AND sec.profile_master_id IS NOT NULL
    AND COALESCE(uic.rogue_one_email_id, uim.rogue_one_email_id) IS NOT NULL
GROUP BY ALL;
