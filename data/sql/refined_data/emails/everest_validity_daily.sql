-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE start_date DATE DEFAULT DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL {{ params.interval }});
DECLARE end_date DATE DEFAULT DATE("{{ data_interval_end }}");

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.refined_data.everest_validity_daily` (
  create_date     DATE     NOT NULL     OPTIONS(description="report creation date"),
  ip  STRUCT<
    address          STRING             OPTIONS(description='IP address'),
    brand_trigram    ARRAY<STRING>      OPTIONS(description='brand trigram as list where IP is used'),
    status           ARRAY<STRUCT<
      source         STRING             OPTIONS(description="source as enum=['microsoft','yahoo','global','safelist','aol']"),
      value          STRING             OPTIONS(description="status as enum=['enabled','suspended']")
      >
    > OPTIONS(description="IP Status"),
    validity_kpi STRUCT<
      placement    ARRAY<STRUCT<
          source        STRING     OPTIONS(description="source as enum=['microsoft_srd','microsoft_total','microsoft_inbox','yahoot_total','yahoo_inbox','comcast_total','comcast','unknown_users','aol_delivered']"),
          volume        INTEGER    OPTIONS(description="volume"),
          complaint  STRUCT <
            volume                 INTEGER    OPTIONS(description="complaint volume"),
            rate                   FLOAT64    OPTIONS(description="complaint rate")
          > OPTIONS(description="Complaint info")
        >
      >  OPTIONS(description="Placement info"),
      trap      ARRAY<STRUCT<
          source                   STRING     OPTIONS(description="trap source as enum=['rp_network1','cloudmark','critical','significant']"),
          volume                   INTEGER    OPTIONS(description="trap volume")
        >
      >     OPTIONS(description="Spamtrap volume")
    >     OPTIONS(description="Validity KPI for placements and traps")
  >    OPTIONS(description='IP address information')
)
PARTITION BY create_date
OPTIONS(description="This table contains refined data for IP reputation from Everest API.\n" ||
                    "\n" ||
                    "DAG: {{ dag.dag_id }}.\n" ||
                    "Sync: daily."
);



{% if not params.full_export %} -- incremental
DELETE FROM `{{ params.bq_project }}.refined_data.everest_validity_daily`
WHERE create_date BETWEEN start_date AND end_date;
{% else %} -- full export
TRUNCATE TABLE `{{ params.bq_project }}.refined_data.everest_validity_daily`;
{% endif %}

INSERT INTO `{{ params.bq_project }}.refined_data.everest_validity_daily` (
  WITH everest_validity_new AS (
    SELECT *
    FROM `{{ params.bq_project }}.store_email_deliverability.everest_daily`
    {% if not params.full_export %} -- incremental
    WHERE DATE(report_date) BETWEEN start_date AND end_date
    {% endif %}
  ), everest_validity_keys AS (
    SELECT DISTINCT
      report_date,
      ip_address
    FROM everest_validity_new
  ), ip_address_brand_def AS (
    SELECT 1 AS id, '*************' AS ip_address, 'GEO' AS brand_trigram
    UNION ALL
    SELECT 2 AS id, '*************' AS ip_address, 'CAM' AS brand_trigram
    UNION ALL
    SELECT 2 AS id, '*************' AS ip_address, 'NEO' AS brand_trigram
    UNION ALL
    SELECT 2 AS id, '*************' AS ip_address, 'HBR' AS brand_trigram
    UNION ALL
    SELECT 3 AS id, '*************' AS ip_address, 'TEL' AS brand_trigram
    UNION ALL
    SELECT 3 AS id, '*************' AS ip_address, 'T2S' AS brand_trigram
    UNION ALL
    SELECT 3 AS id, '*************' AS ip_address, 'TEL' AS brand_trigram
    UNION ALL
    SELECT 3 AS id, '*************' AS ip_address, 'T2S' AS brand_trigram
    UNION ALL
    SELECT 4 AS id, '*************' AS ip_address, 'CAP' AS brand_trigram
    UNION ALL
    SELECT 4 AS id, '*************' AS ip_address, 'CAP' AS brand_trigram
    UNION ALL
    SELECT 5 AS id, '*************' AS ip_address, 'GAL' AS brand_trigram
    UNION ALL
    SELECT 5 AS id, '*************' AS ip_address, 'GAL' AS brand_trigram
    UNION ALL
    SELECT 5 AS id, '**************' AS ip_address, 'GAL' AS brand_trigram
    UNION ALL
    SELECT 6 AS id, '**************' AS ip_address, 'CAC' AS brand_trigram
    UNION ALL
    SELECT 6 AS id, '**************' AS ip_address, 'CAC' AS brand_trigram
    UNION ALL
    SELECT 7 AS id, '**************' AS ip_address, 'FAC' AS brand_trigram
    UNION ALL
    SELECT 7 AS id, '**************' AS ip_address, 'FAC' AS brand_trigram
    UNION ALL
    SELECT 7 AS id, '**************' AS ip_address, 'FAC' AS brand_trigram
    UNION ALL
    SELECT 8 AS id, '**************' AS ip_address, 'VOI' AS brand_trigram
    UNION ALL
    SELECT 8 AS id, '**************' AS ip_address, 'VOI' AS brand_trigram
    UNION ALL
    SELECT 8 AS id, '**************' AS ip_address, 'VOI' AS brand_trigram
    UNION ALL
    SELECT 9 AS id, '**************' AS ip_address, 'GEN' AS brand_trigram
    UNION ALL
    SELECT 9 AS id, '**************' AS ip_address, 'GEN' AS brand_trigram
    UNION ALL
    SELECT 10 AS id, '**************' AS ip_address, 'OMM' AS brand_trigram
    UNION ALL
    SELECT 10 AS id, '**************' AS ip_address, 'OMM' AS brand_trigram
    UNION ALL
    SELECT 11 AS id, '**************' AS ip_address, 'FID' AS brand_trigram
  ), ip_address_brand AS (
    SELECT
      ip_address AS ip_address,
      ARRAY_AGG(brand_trigram) AS brand_trigram
    FROM ip_address_brand_def
    GROUP BY 1
  ), ip_status_microsoft AS (
    -- Status Microsoft
    SELECT
      report_date,
      ip_address,
      'microsoft'       AS source,
      status__microsoft AS value
    FROM everest_validity_new
  ), ip_status_yahoo AS (
    -- Status Yahoo
    SELECT
      report_date,
      ip_address,
      'yahoo'       AS source,
      status__yahoo AS value
    FROM everest_validity_new
  ), ip_status_global AS (
    -- Status Global
    SELECT
      report_date,
      ip_address,
      'global'       AS source,
      status__global AS value
    FROM everest_validity_new
  ), ip_status_safelist AS (
    -- Status safelist
    SELECT
      report_date,
      ip_address,
      'safelist'       AS source,
      status__safelist AS value
    FROM everest_validity_new
  ), ip_status_aol AS (
    -- Status AOL
    SELECT
      report_date,
      ip_address,
      'aol'       AS source,
      status__aol AS value
    FROM everest_validity_new
  ), ip_status_concat AS (
    SELECT * FROM ip_status_microsoft
    UNION ALL
    SELECT * FROM ip_status_yahoo
    UNION ALL
    SELECT * FROM ip_status_global
    UNION ALL
    SELECT * FROM ip_status_safelist
    UNION ALL
    SELECT * FROM ip_status_aol
  ), ip_status AS (
    SELECT
      report_date,
      ip_address,
      ARRAY_AGG(STRUCT(source, value) IGNORE NULLS) AS status
    FROM ip_status_concat
    GROUP BY 1,2
  ), ip_placement_microsoft_srd AS (
    -- Placement Microsoft SRD (sampled)
    SELECT
      report_date,
      ip_address,
      'microsoft_srd'                         AS source,
      microsoft_srd__volume                   AS volume,
      microsoft_srd__complaints               AS complaint__volume,
      microsoft_srd__complaint_rate           AS complaint__rate
    FROM everest_validity_new
  ), ip_placement_microsoft_total AS (
    -- Placement Microsoft total
    SELECT
      report_date,
      ip_address,
      'microsoft_total'                       AS source,
      microsoft_total__volume                 AS volume,
      microsoft_total__complaints             AS complaint__volume,
      microsoft_total__complaint_rate         AS complaint__rate
    FROM everest_validity_new
  ), ip_placement_microsoft_inbox AS (
    -- Placement Microsoft inbox
    SELECT
      report_date,
      ip_address,
      'microsoft_inbox'                       AS source,
      microsoft_inbox__volume                 AS volume,
      microsoft_inbox__complaints             AS complaint__volume,
      microsoft_inbox__complaint_rate         AS complaint__rate
    FROM everest_validity_new
  ), ip_placement_yahoo_total AS (
    -- Placement Yahoo total
    SELECT
      report_date,
      ip_address,
      'yahoo_total'                           AS source,
      yahoo_total__volume                     AS volume,
      yahoo_total__complaints                 AS complaint__volume,
      yahoo_total__complaint_rate             AS complaint__rate
    FROM everest_validity_new
  ), ip_placement_yahoo_inbox AS (
    -- Placement Yahoo inbox
    SELECT
      report_date,
      ip_address,
      'yahoo_inbox'                           AS source,
      yahoo_inbox__volume                     AS volume,
      yahoo_inbox__complaints                 AS complaint__volume,
      yahoo_inbox__complaint_rate             AS complaint__rate
    FROM everest_validity_new
  ), ip_placement_comcast_total AS (
    -- Placement Comcast total
    SELECT
      report_date,
      ip_address,
      'comcast_total'                         AS source,
      comcast_total__volume                   AS volume,
      NULL                                    AS complaint__volume,
      NULL                                    AS complaint__rate
    FROM everest_validity_new
  ), ip_placement_comcast AS (
    -- Placement Comcast
    SELECT
      report_date,
      ip_address,
      'comcast'                       AS source,
      comcast__volume                 AS volume,
      comcast__complaints             AS complaint__volume,
      comcast__complaint_rate         AS complaint__rate
    FROM everest_validity_new
  ), ip_placement_unknown_users AS (
    -- Placement Unknown Users
    SELECT
      report_date,
      ip_address,
      'unknown_users'                 AS source,
      unknown_users__volume           AS volume,
      unknown_users__complaints       AS complaint__volume,
      unknown_users__complaint_rate   AS complaint__rate
    FROM everest_validity_new
  ), ip_placement_aol_delivered AS (
    -- Placement AOL
    SELECT
      report_date,
      ip_address,
      'aol_delivered'                 AS source,
      aol_delivered__volume           AS volume,
      aol_delivered__complaints       AS complaint__volume,
      aol_delivered__complaint_rate   AS complaint__rate
    FROM everest_validity_new
  ), ip_placement_concat AS (
    SELECT * FROM ip_placement_microsoft_srd
    UNION ALL
    SELECT * FROM ip_placement_microsoft_total
    UNION ALL
    SELECT * FROM ip_placement_microsoft_inbox
    UNION ALL
    SELECT * FROM ip_placement_yahoo_total
    UNION ALL
    SELECT * FROM ip_placement_yahoo_inbox
    UNION ALL
    SELECT * FROM ip_placement_comcast_total
    UNION ALL
    SELECT * FROM ip_placement_comcast
    UNION ALL
    SELECT * FROM ip_placement_unknown_users
    UNION ALL
    SELECT * FROM ip_placement_aol_delivered
  ), ip_placement AS (
    SELECT
    report_date,
    ip_address,
    ARRAY_AGG(
      STRUCT(
        source,
        volume,
        STRUCT(
          complaint__volume      AS volume,
          complaint__rate        AS rate
        ) AS complaint
      )
      IGNORE NULLS
    ) AS placement
    FROM ip_placement_concat
    GROUP BY 1,2
  ), ip_trap_rp_network1 AS (
    SELECT
      report_date,
      ip_address,
      'rp_network1'                             AS source,
      rp_network1_traps__count                  AS volume
    FROM everest_validity_new
  ), ip_trap_cloudmark AS (
    SELECT
      report_date,
      ip_address,
      'cloudmark'                              AS source,
      cloudmark_traps__count                   AS volume
    FROM everest_validity_new
  ), ip_trap_critical AS (
    SELECT
      report_date,
      ip_address,
      'critical'                              AS source,
      critical_traps__count                   AS volume
    FROM everest_validity_new
  ), ip_trap_significant AS (
    SELECT
      report_date,
      ip_address,
      'significant'                              AS source,
      significant_traps__count                   AS volume
    FROM everest_validity_new
  ), ip_trap_concat AS (
    SELECT * FROM ip_trap_rp_network1
    UNION ALL
    SELECT * FROM ip_trap_cloudmark
    UNION ALL
    SELECT * FROM ip_trap_critical
    UNION ALL
    SELECT * FROM ip_trap_significant
  ), ip_trap AS (
    SELECT
      report_date,
      ip_address,
      ARRAY_AGG(
        STRUCT(
          source,
          volume
        )
        IGNORE NULLS
      ) AS trap
    FROM ip_trap_concat
    GROUP BY 1,2
  )

  SELECT
    ev.report_date AS create_date,
    STRUCT(
        ev.ip_address   AS address,
        b.brand_trigram AS brand_trigram,
        ips.status      AS status,
        STRUCT(
            placement,
            trap
        ) AS validity_kpi
    ) AS ip
  FROM everest_validity_keys AS ev
  JOIN ip_address_brand AS b ON b.ip_address = ev.ip_address
  JOIN ip_status    AS ips ON ips.report_date = ev.report_date AND ips.ip_address = ev.ip_address
  JOIN ip_trap      AS ipt ON ipt.report_date = ev.report_date AND ipt.ip_address = ev.ip_address
  JOIN ip_placement AS ipp ON ipp.report_date = ev.report_date AND ipp.ip_address = ev.ip_address
)