-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE start_date DATE DEFAULT DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL {{ params.interval }});
DECLARE end_date DATE DEFAULT DATE("{{ data_interval_end }}");

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.refined_data.splio_campaign_stat` (
    observation_date       DATE         NOT NULL    OPTIONS(description="updating date"),
    start_date             TIMESTAMP    NOT NULL    OPTIONS(description="campaign start date"),
    end_date               TIMESTAMP    NOT NULL    OPTIONS(description="campaign end date"),
    id STRUCT<
        message_ref            STRING                   OPTIONS(description="ref: store.splio_campaign_stat.message"),
        sendout_ref            STRING                   OPTIONS(description="ref: store.splio_campaign_stat.sendID")
    > OPTIONS(description=""),
    campaign STRUCT<
        universe_name            STRING                   OPTIONS(description="ref: store.splio_campaign_stat.univers"),
        name                     STRING                   OPTIONS(description="ref: store.splio_campaign_stat.nom"),
        category                 STRING                   OPTIONS(description="ref: store.splio_campaign_stat.category"),
        type                     STRING                   OPTIONS(description="ref: store.splio_campaign_stat.campaign_type"),
        status                   STRING                   OPTIONS(description="ref: store.splio_campaign_stat.status")
    > OPTIONS(description=""),
    rogue_one STRUCT<
        id                   INT64                     OPTIONS(description="ref: store.splio_campaign_stat.codeope"),
        shoot_date           DATE                      OPTIONS(description="ref: store_matrix_email.rogue_one_email_consent.rogue_one_shoot_date"),
        email_base STRUCT<
            owner_name           STRING                    OPTIONS(description="ref: refined_data.email_base.owner_name"),
            pole_name            STRING                    OPTIONS(description="ref: refined_data.email_base.pole_name"),
            brand_trigram        STRING                    OPTIONS(description="ref: refined_data.email_base.brand_trigram"),
            consent_public_ref   STRING                    OPTIONS(description="ref: refined_data.email_base.consent_public_ref")
        > OPTIONS(description="information from refined_data.email_base")
    > OPTIONS(description="rogue one information"),
    stat STRUCT<
        global STRUCT<
            target       INT64                             OPTIONS(description="ref: store.splio_campaign_stat.cnt_target"),
            selected     INT64                             OPTIONS(description="ref: store.splio_campaign_stat.cnt_todo"),
            sent         INT64                             OPTIONS(description="ref: store.splio_campaign_stat.cnt_tried"),
            delivered    INT64                             OPTIONS(description="ref: store.splio_campaign_stat.cnt_done"),
            soft_bounce  INT64                             OPTIONS(description="ref: store.splio_campaign_stat.cnt_soft"),
            hard_bounce  INT64                             OPTIONS(description="ref: store.splio_campaign_stat.cnt_hard"),
            open         INT64                             OPTIONS(description="ref: store.splio_campaign_stat.cnt_open"),
            opener       INT64                             OPTIONS(description="ref: store.splio_campaign_stat.cnt_openers"),
            click        INT64                             OPTIONS(description="ref: store.splio_campaign_stat.cnt_clic"),
            clicker      INT64                             OPTIONS(description="ref: store.splio_campaign_stat.cnt_clickers"),
            unsub        INT64                             OPTIONS(description="ref: store.splio_campaign_stat.cnt_unsub"),
            complaint    INT64                             OPTIONS(description="ref: store.splio_campaign_stat.cnt_cnt_plaintes")
        > OPTIONS(description="Global KPI"),
        support ARRAY<STRUCT<
            type         STRING                            OPTIONS(description="enum=['desktop','mobile','tablet']"),
            os           STRING                            OPTIONS(description="enum=['apple','windows','iphone','android','blackberry','ipad',...]"),
            open         INT64                             OPTIONS(description="opens volume"),
            click        INT64                             OPTIONS(description="clicks volume")
        >> OPTIONS(description="KPI per device"),
        service ARRAY<STRUCT<
            name         STRING                            OPTIONS(description="name of email service provider"),
            open         INT64                             OPTIONS(description="opens volume"),
            click        INT64                             OPTIONS(description="clicks volume")
        >> OPTIONS(description="KPI per service")
    > OPTIONS(description="KPI")
)
PARTITION BY observation_date
OPTIONS(description="This table contains Splio campaign stats by observation date.\n" ||
                    "DAG: refined_data__email.\n" ||
                    "Sync: Daily.");

{% if params.full_export %}
TRUNCATE TABLE `{{ params.bq_project }}.refined_data.splio_campaign_stat`;
{% endif %}

-- Upsert
MERGE `{{ params.bq_project }}.refined_data.splio_campaign_stat` AS ref
USING (
    WITH rogue_one_email_consent_dedup AS (
        SELECT DISTINCT
            rogue_one_email_id,
            email_consent_id,
            DATE(rogue_one_shoot_date) AS shoot_date
        FROM `{{ params.bq_project }}.store_matrix_email.rogue_one_email_consent`
    )
  -- Compute global campaign stats
    , global_stats AS (
    -- Compute global campaign stats
        SELECT
            update_date   AS update_date,
            sendID        AS sendout_ref,
            univers       AS universe_name,
            nom           AS campaign_name,
            category      AS campaign_category,
            campaign_type AS campaign_type,
            status        AS campaign_status,
            codeope       AS rogue_one_email_id,
            starttime     AS start_date,
            endtime       AS end_date,
            message       AS message_ref,
            todo          AS target,
            done          AS selected,
            cnt_tried     AS sent,
            cnt_done      AS delivered,
            cnt_soft      AS soft_bounce,
            cnt_hard      AS hard_bounce,
            cnt_open      AS open,
            cnt_openers   AS opener,
            cnt_clic      AS click,
            cnt_clickers  AS clicker,
            cnt_unsub     AS unsub,
            cnt_plaintes  AS complaint
        FROM `{{ params.bq_project }}.store.splio_campaign_stat`
{% if not params.full_export %}
        WHERE update_date BETWEEN start_date AND end_date
{% endif %}
),

  -- Compute stats per service
    service_stats AS (
        SELECT DISTINCT
            update_date   AS update_date,
            sendID        AS sendout_ref,
            univers       AS universe_name,
            nom           AS campaign_name,
            category      AS campaign_category,
            campaign_type AS campaign_type,
            status        AS campaign_status,
            codeope       AS rogue_one_email_id,
            starttime     AS start_date,
            endtime       AS end_date,
            message       AS message_ref,
            service_name  AS service_name,
            CASE
                WHEN service_name = 'hotmail'     THEN cnt_ouv_hotmail
                WHEN service_name = 'yahoo'       THEN cnt_ouv_yahoo
                WHEN service_name = 'gmail'       THEN cnt_ouv_gmail
                WHEN service_name = 'orange'      THEN cnt_ouv_orange
                WHEN service_name = 'sfr'         THEN cnt_ouv_sfr
                WHEN service_name = 'laposte'     THEN cnt_ouv_laposte
                WHEN service_name = 'free'        THEN cnt_ouv_free
                WHEN service_name = 'webmail'     THEN cnt_ouv_webmail
                WHEN service_name = 'outlook'     THEN cnt_ouv_outlook
                WHEN service_name = 'thunderbird' THEN cnt_ouv_thunderbird
                WHEN service_name = 'msoe'        THEN cnt_ouv_msoe
                WHEN service_name = 'lotus'       THEN cnt_ouv_lotus
                WHEN service_name = 'netease'     THEN cnt_ouv_netease
                WHEN service_name = 'qq'          THEN cnt_ouv_qq
                WHEN service_name = 'sina'        THEN cnt_ouv_sina
                WHEN service_name = 'sohu'        THEN cnt_ouv_sohu
                ELSE NULL
            END AS open,
            CASE
                WHEN service_name = 'hotmail'     THEN cnt_cli_hotmail
                WHEN service_name = 'yahoo'       THEN cnt_cli_yahoo
                WHEN service_name = 'gmail'       THEN cnt_cli_gmail
                WHEN service_name = 'orange'      THEN cnt_cli_orange
                WHEN service_name = 'sfr'         THEN cnt_cli_sfr
                WHEN service_name = 'laposte'     THEN cnt_cli_laposte
                WHEN service_name = 'free'        THEN cnt_cli_free
                WHEN service_name = 'webmail'     THEN cnt_cli_webmail
                WHEN service_name = 'outlook'     THEN cnt_cli_outlook
                WHEN service_name = 'thunderbird' THEN cnt_cli_thunderbird
                WHEN service_name = 'msoe'        THEN cnt_cli_msoe
                WHEN service_name = 'lotus'       THEN cnt_cli_lotus
                WHEN service_name = 'netease'     THEN cnt_cli_netease
                WHEN service_name = 'qq'          THEN cnt_cli_qq
                WHEN service_name = 'sina'        THEN cnt_cli_sina
                WHEN service_name = 'sohu'        THEN cnt_cli_sohu
                ELSE NULL
            END AS click
        FROM `{{ params.bq_project }}.store.splio_campaign_stat`,
          UNNEST(['outlook','gmail','yahoo','thunderbird','msoe','lotus',
                  'netease','qq','sina','sohu','orange','sfr','laposte',
                  'free','webmail','hotmail']) AS service_name
{% if not params.full_export %}
        WHERE update_date BETWEEN start_date AND end_date
{% endif %}
      ),

      -- Aggregate stats per service into array of struct
    structured_service_stats AS (
        SELECT
            update_date,
            sendout_ref,
            universe_name,
            campaign_name,
            campaign_category,
            campaign_type,
            campaign_status,
            rogue_one_email_id,
            start_date,
            end_date,
            message_ref,
            ARRAY_AGG(
                STRUCT(
                    service_name AS name,
                    open         AS open,
                    click        AS click
                )
            ) AS service
        FROM service_stats
        GROUP BY 1,2,3,4,5,6,7,8,9,10,11
    ),

    -- Compute stats for mobile
    device_mobile_stats AS (
        SELECT DISTINCT
            update_date          AS update_date,
            sendID               AS sendout_ref,
            univers              AS universe_name,
            nom                  AS campaign_name,
            category             AS campaign_category,
            campaign_type        AS campaign_type,
            status               AS campaign_status,
            codeope              AS rogue_one_email_id,
            starttime            AS start_date,
            endtime              AS end_date,
            message              AS message_ref,
            'mobile'             AS device_type,
            os                   AS device_os,
            CASE
                WHEN os = 'ios'      THEN cnt_ouv_mobile_iphone
                WHEN os = 'android'  THEN cnt_ouv_mobile_android
                WHEN os = 'ios_fake' THEN cnt_ouv_mobile_blackberry -- it's ios fake
                WHEN os = 'windows'  THEN cnt_ouv_mobile_windows
                WHEN os = 'ios'      THEN cnt_ouv_mobile_symbian
                ELSE NULL
            END AS open,
            CASE
                WHEN os = 'ios'      THEN cnt_cli_mobile_iphone
                WHEN os = 'android'  THEN cnt_cli_mobile_android
                WHEN os = 'ios_fake' THEN cnt_cli_mobile_blackberry -- it's ios fake
                WHEN os = 'windows'  THEN cnt_cli_mobile_windows
                WHEN os = 'ios'      THEN cnt_cli_mobile_symbian
                ELSE NULL
            END AS click
        FROM `{{ params.bq_project }}.store.splio_campaign_stat`,
          UNNEST(['ios','ios_fake','android','windows']) AS os
{% if not params.full_export %}
        WHERE update_date BETWEEN start_date AND end_date
{% endif %}
      ),

      -- Compute stats for desktop
    device_desktop_stats AS (
        SELECT DISTINCT
            update_date          AS update_date,
            sendID               AS sendout_ref,
            univers              AS universe_name,
            nom                  AS campaign_name,
            category             AS campaign_category,
            campaign_type        AS campaign_type,
            status               AS campaign_status,
            codeope              AS rogue_one_email_id,
            starttime            AS start_date,
            endtime              AS end_date,
            message              AS message_ref,
            'desktop'            AS device_type,
            os                   AS device_os,
            CASE
                WHEN os = 'macintosh' THEN cnt_ouv_apple
                WHEN os = 'windows'   THEN cnt_ouv_other
                ELSE NULL
            END AS open,
            CASE
                WHEN os = 'macintosh' THEN cnt_cli_apple
                WHEN os = 'windows'   THEN cnt_cli_other
                ELSE NULL
            END AS click
        FROM `{{ params.bq_project }}.store.splio_campaign_stat`,
          UNNEST(['macintosh','windows']) AS os
{% if not params.full_export %}
        WHERE update_date BETWEEN start_date AND end_date
{% endif %}
    ),

    -- Compute stats for tablet
    device_tablet_stats AS (
        SELECT DISTINCT
            update_date                   AS update_date,
            sendID                        AS sendout_ref,
            univers                       AS universe_name,
            nom                           AS campaign_name,
            category                      AS campaign_category,
            campaign_type                 AS campaign_type,
            status                        AS campaign_status,
            codeope                       AS rogue_one_email_id,
            starttime                     AS start_date,
            endtime                       AS end_date,
            message                       AS message_ref,
            'tablet'                      AS device_type,
            os                            AS device_os,
            CASE
                WHEN os = 'ipad'    THEN cnt_ouv_mobile_ipad
                WHEN os = 'android' THEN cnt_ouv_mobile_android_tablet
                ELSE NULL
            END AS open,
            CASE
                WHEN os = 'ipad'    THEN cnt_cli_mobile_ipad
                WHEN os = 'android' THEN cnt_cli_mobile_android_tablet
                ELSE NULL
            END AS click
        FROM `{{ params.bq_project }}.store.splio_campaign_stat`,
          UNNEST(['ipad','android']) AS os
{% if not params.full_export %}
        WHERE update_date BETWEEN start_date AND end_date
{% endif %}
    ),

      -- Concatenate mobile, desktop and tablet stats
    device_stats AS (
        SELECT DISTINCT * FROM device_mobile_stats
        UNION ALL
        SELECT DISTINCT * FROM device_desktop_stats
        UNION ALL
        SELECT DISTINCT * FROM device_tablet_stats
    ),

      -- Aggregate support stats as array or struct
    structured_device_stats AS (
        SELECT
            update_date,
            sendout_ref,
            universe_name,
            campaign_name,
            campaign_category,
            campaign_type,
            campaign_status,
            rogue_one_email_id,
            start_date,
            end_date,
            message_ref,
            ARRAY_AGG(
                STRUCT(
                    device_type AS type,
                    device_os   AS os,
                    open        AS open,
                    click       AS click
                )
            ) AS support
        FROM device_stats
        GROUP BY 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11
    )

      -- Build final datamart + add email_base info
    SELECT
        g.update_date AS observation_date,
        g.start_date,
        g.end_date,
        STRUCT(
            g.sendout_ref,
            g.message_ref
        ) AS id,
        STRUCT(
            g.universe_name     AS universe_name,
            g.campaign_name     AS name,
            g.campaign_category AS category,
            g.campaign_type     AS type,
            g.campaign_status   AS status
        ) AS campaign,
        STRUCT(
            g.rogue_one_email_id    AS id,
            ecd.shoot_date          AS shoot_date,
            STRUCT(
                eb.owner_name               AS owner_name,
                eb.pole_name                AS pole_name,
                eb.brand_trigram            AS brand_trigram,
                eb.consent_public_ref       AS consent_public_ref
            ) AS email_base
        ) AS rogue_one,
        STRUCT(
            STRUCT(
                g.target      AS target,
                g.selected    AS selected,
                g.sent        AS sent,
                g.delivered   AS delivered,
                g.soft_bounce AS soft_bounce,
                g.hard_bounce AS hard_bounce,
                g.open        AS open,
                g.opener      AS opener,
                g.click       AS click,
                g.clicker     AS clicker,
                g.unsub       AS unsub,
                g.complaint   AS complaint
            ) AS global,
            d.support AS support,
            s.service AS service
        ) AS stat
    FROM global_stats AS g
    JOIN structured_device_stats AS d
        USING(
            update_date,
            sendout_ref,
            universe_name,
            campaign_name,
            campaign_category,
            campaign_type,
            start_date,
            end_date,
            message_ref
        )
    JOIN structured_service_stats AS s
        USING(
            update_date,
            sendout_ref,
            universe_name,
            campaign_name,
            campaign_category,
            campaign_type,
            start_date,
            end_date,
            message_ref
        )
    LEFT JOIN rogue_one_email_consent_dedup AS ecd
        ON  ecd.rogue_one_email_id = g.rogue_one_email_id
        AND ecd.shoot_date BETWEEN DATE(g.start_date) AND DATE(g.end_date)
    LEFT JOIN `{{ params.bq_project }}.refined_data.email_base` AS eb
        ON ecd.email_consent_id = eb.consent_id
) AS dst

ON      dst.observation_date                        = ref.observation_date
    AND dst.start_date                              = ref.start_date
    AND dst.end_date                                = ref.end_date
    AND dst.id.sendout_ref                          = ref.id.sendout_ref
    AND dst.id.message_ref                          = ref.id.message_ref
    AND dst.campaign.universe_name                  = ref.campaign.universe_name
    AND dst.campaign.name                           = ref.campaign.name
    AND dst.campaign.category                       = ref.campaign.category
    AND dst.campaign.type                           = ref.campaign.type
    AND dst.campaign.status                         = ref.campaign.status
    AND dst.rogue_one.id                            = ref.rogue_one.id
    AND dst.rogue_one.shoot_date                    = ref.rogue_one.shoot_date
    AND dst.rogue_one.email_base.owner_name         = ref.rogue_one.email_base.owner_name
    AND dst.rogue_one.email_base.pole_name          = ref.rogue_one.email_base.pole_name
    AND dst.rogue_one.email_base.brand_trigram      = ref.rogue_one.email_base.brand_trigram
    AND dst.rogue_one.email_base.consent_public_ref = ref.rogue_one.email_base.consent_public_ref

-- Update existing campaign stats
WHEN MATCHED THEN
    UPDATE SET
        ref.stat = dst.stat

-- Insert new campaigns
WHEN NOT MATCHED THEN
    INSERT (
        observation_date,
        start_date,
        end_date,
        id,
        campaign,
        rogue_one,
        stat
    )
    VALUES (
        dst.observation_date,
        dst.start_date,
        dst.end_date,
        dst.id,
        dst.campaign,
        dst.rogue_one,
        dst.stat
    )