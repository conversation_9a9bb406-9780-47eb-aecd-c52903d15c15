-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.refined_data.splio_report`(
    start_date                          TIMESTAMP               OPTIONS(description="campaign creation datetime. ref: {{ params.bq_project }}.store_tracking.splio_report_data#stattime"),
    end_date                            TIMESTAMP               OPTIONS(description="report last update datetime. ref: {{ params.bq_project }}.store_tracking.splio_report_data#endtime"),
    id STRUCT <
        message_ref                         STRING                  OPTIONS(description="campaign message id. ref: {{ params.bq_project }}.store_tracking.splio_report_data#data#message#id"),
        campaign_ref                        STRING                  OPTIONS(description="ref: {{ params.bq_project }}.store_tracking.splio_report_data.data#campaign"),
        sendout_ref                         STRING                  OPTIONS(description="ref: {{ params.bq_project }}.store_tracking.splio_report_data.data#id"),
        report_id                           INTEGER                 OPTIONS(description="splio report id. ref: {{ params.bq_project }}.store_tracking.splio_report_data.report_id")
    >                                                           OPTIONS(description="IDs informations"),
    campaign STRUCT<
        mode                       STRING                           OPTIONS(description="enum = ['auto', 'alert', 'manual', 'other'] extracted from message_name"),
        universe_name              STRING                           OPTIONS(description="ref: {{ params.bq_project }}.store_tracking.store_karinto.universe.name"),
        universe_type              STRING                           OPTIONS(description="ref: {{ params.bq_project }}.store_tracking.store_karinto.universe.type"),
        segment                    STRING                           OPTIONS(description="campaign segement extracted from message name as enum=[A2, A0, X0, etc....]. ref: {{ params.bq_project }}.store_tracking.splio_report_data.data#message#name"),
        sender_email               STRING                           OPTIONS(description="campaign subject. ref: {{ params.bq_project }}.store_tracking.splio_report_data.data#sender_email"),
        sender_name                STRING                           OPTIONS(description="campaign subject. ref: {{ params.bq_project }}.store_tracking.splio_report_data.data#sender_name"),
        message_name               STRING                           OPTIONS(description="campaign message name. ref: {{ params.bq_project }}.store_tracking.splio_report_data#message#name"),
        message_subject            STRING                           OPTIONS(description="campaign message name. ref: {{ params.bq_project }}.store_tracking.splio_report_data#message#subject"),
        source                     STRING                           OPTIONS(description="utm source extracted from splio links. It contains NL name and it's the most frequent by report id")
    >                                                           OPTIONS(description="campaigns informations"),
    rogue_one STRUCT <
        id                              INTEGER                     OPTIONS(description="rogue-one id. ref: {{ params.bq_project }}.store_tracking.splio_report.rogue_one_email_id"),
        shoot_date                      TIMESTAMP                   OPTIONS(description="rogue-one shoot datetime. ref: {{ params.bq_project }}.store_tracking.splio_report.rogue_one_email_id"),
        email_base  STRUCT<
            owner_name                  STRING                          OPTIONS(description="owner name. ref: {{ params.bq_project }}.store_karinto.owner.name"),
            pole_name                   STRING                          OPTIONS(description="owner name. ref: {{ params.bq_project }}.store_karinto.pole.name"),
            brand_trigram               STRING                          OPTIONS(description="owner name. ref: {{ params.bq_project }}.store_karinto.brand.trigram"),
            email_consent_public_ref    STRING                          OPTIONS(description="email consent public ref. ref:  {{ params.bq_project }}.store_karinto.consent.public_ref")
        >                                                           OPTIONS(description="consent information based on rogue-one email id extarcted from links."),
        theme                           STRING                      OPTIONS(description="theme as 'NL shoopping', 'Geo destination', ...")
    >                                                           OPTIONS(description="rogue-one's informations based on consents IDs extracted from links"),
    stats    STRUCT<
        targets                 INTEGER                             OPTIONS(description="number of targets email. ref: {{ params.bq_project }}.store_tracking.splio_report_data.recipients#targets"),
        sent                    INTEGER                             OPTIONS(description="number of sent email. ref: {{ params.bq_project }}.store_tracking.splio_report_data.recipients#sent"),
        delivered               INTEGER                             OPTIONS(description="number of arrived email to recipients. ref: {{ params.bq_project }}.store_tracking.splio_report_data.recipients#delivered"),
        opens                   INTEGER                             OPTIONS(description="number of opened emails. ref: {{ params.bq_project }}.store_tracking.splio_report_data.reactions#opens"),
        openers                 INTEGER                             OPTIONS(description="number of distinct users that opens an email at least. ref: {{ params.bq_project }}.store_tracking.splio_report_data.recipients#openers"),
        clicks                  INTEGER                             OPTIONS(description="number of clicked emails. ref: {{ params.bq_project }}.store_tracking.splio_report_data.reactions#clicks"),
        clickers                INTEGER                             OPTIONS(description="number of distinct users that clicks into an email at least. ref: {{ params.bq_project }}.store_tracking.splio_report_data.recipients#clickers"),
        unsubscribes            INTEGER                             OPTIONS(description="number of unsubscribes. ref: {{ params.bq_project }}.store_tracking.splio_report_data.reactions#unsubscribes"),
        soft_bounces            INTEGER                             OPTIONS(description="number of soft bounced emails. ref: {{ params.bq_project }}.store_tracking.splio_report_data.recipients#soft_bounce"),
        hard_bounces            INTEGER                             OPTIONS(description="number of hard bounced email. ref: {{ params.bq_project }}.store_tracking.splio_report_data.recipients#hard_bounce"),
        spam_complaints         INTEGER                             OPTIONS(description="number of spam complaints. ref: {{ params.bq_project }}.store_tracking.splio_report_data.reactions#spam_complaints")
    >                                                           OPTIONS(description="campaigns stats")
)PARTITION BY TIMESTAMP_TRUNC(start_date, DAY)
OPTIONS(description="List splio campaigns and its statistics. "||
                    "DAG: {{ dag.dag_id }}. "||
                    "Sync: daily");

TRUNCATE TABLE `{{ params.bq_project }}.refined_data.splio_report`;

INSERT INTO `{{ params.bq_project }}.refined_data.splio_report`

WITH valid_json AS (
    -- to avoid non valid json : to be fixed later and JIRA ticket is created
    SELECT
        report_id,
        REPLACE(data, '\\\\"', '\\"') AS payload,
        universe_id
    FROM `{{ params.bq_project }}.store_tracking.splio_report_data` AS sd
    LEFT JOIN `{{ params.bq_project }}.store_tracking.splio_report` AS sr ON sr.id = sd.report_id
    WHERE starttime IS NOT NULL
), get_recent_report AS(
    -- select report id MAX per message ref
    SELECT
        JSON_VALUE(payload, '$.message.id')    AS message_ref,
        JSON_VALUE(payload, '$.message.name')  AS message_name,
        JSON_VALUE(payload, '$.id')            AS sendout_ref,
        MAX(report_id)                         AS report_id,
        MAX(universe_id)                       AS universe_id
    FROM valid_json
    GROUP BY 1,2,3
), splio_campaigns AS(
    -- get only the most recent report with their payload field
    SELECT
        ci.message_ref,
        ci.message_name,
        ci.sendout_ref,
        ci.report_id,
        ci.universe_id,
        vj.payload
    FROM valid_json  AS vj
    JOIN get_recent_report AS ci USING (report_id)
), prepare_campaigns AS(
    -- extract info from payload
    SELECT
        sc.message_ref,
        message_name,
        universe_id,
        JSON_VALUE(payload, '$.message.subject')                          AS message_subject,
        report_id,
        sc.sendout_ref,
        JSON_VALUE(payload, '$.campaign')                                 AS campaign_ref,
        SAFE_CAST(JSON_VALUE(payload, '$.starttime') AS TIMESTAMP)        AS campaign_start_date,
        SAFE_CAST(JSON_VALUE(payload, '$.endtime') AS TIMESTAMP)          AS campaign_end_date,
        CASE
            WHEN message_name LIKE '%[AUTO]%' THEN 'auto'
            WHEN message_name LIKE '%[API]%' THEN 'alert'
            WHEN message_name LIKE '%[TIRGGER]%' THEN 'manual'
            WHEN message_name LIKE '%[MANUAL]%'  THEN 'manual'
            WHEN message_name LIKE  '%[Manuelle]%' THEN 'manual'
            ELSE 'other'
        END     AS campaign_mode,
    CASE
      -- Primary pattern extraction
      WHEN REGEXP_CONTAINS(message_name, r'[]"-]\s*([A-Z][0-9]+(?:[.][0-9]+)?|Date Update)\s*[]"-]') THEN
        REGEXP_EXTRACT(message_name, r'[]"-]\s*([A-Z][0-9]+(?:[.][0-9]+)?|Date Update)\s*[]"-]')
      -- Secondary pattern extraction for NOTIFYSCORED
      WHEN REGEXP_CONTAINS(message_name, r'[]"-]\s*([A-Z][0-9]+)NOTIFYSCORED\s*[]"-]') THEN
        CONCAT(REGEXP_EXTRACT(message_name, r'[]"-]\s*([A-Z][0-9]+)NOTIFYSCORED\s*[]"-]'), '_SCORED')
      -- Secondary pattern extraction for NOTIFY
      WHEN REGEXP_CONTAINS(message_name, r'[]"-]\s*([A-Z][0-9]+)NOTIFY\s*[]"-]') THEN
        REGEXP_EXTRACT(message_name, r'[]"-]\s*([A-Z][0-9]+)NOTIFY\s*[]"-]')
      -- Final pattern :  Extraction for A0, A1, X0, X1, A2 preceded or followed by -
      WHEN REGEXP_CONTAINS(message_name, r'(\bA[0-2]|X[0-1])\s*-|\-\s*(\bA[0-2]|X[0-1])') THEN
        REGEXP_EXTRACT(message_name, r'(\bA[0-2]|X[0-1])')
      ELSE NULL  END AS campaign_segment,
        JSON_VALUE(payload, '$.message.sender_email')                                                           AS campaign_sender_email,
        JSON_VALUE(payload, '$.message.sender_name')                                                            AS campaign_sender_name,
        srl.rogue_one_email_id,
        REGEXP_REPLACE(REGEXP_REPLACE(REGEXP_REPLACE(srl.main_utm_source,r'#nlsha=.*$',''), r'#nlref=.*$', ''),r'%C3%A9',
                                       'é')                                                           AS main_utm_source,
        SAFE_CAST(JSON_VALUE(payload, '$.recipients.total')          AS INT64)                        AS targets,
        SAFE_CAST(JSON_VALUE(payload, '$.recipients.delivered')      AS INT64)                        AS delivered,
        SAFE_CAST(JSON_VALUE(payload, '$.recipients.sent')           AS INT64)                        AS sent,
        SAFE_CAST(JSON_VALUE(payload, '$.reactions.opens')           AS INT64)                        AS opens,
        SAFE_CAST(JSON_VALUE(payload, '$.recipients.openers')        AS INT64)                        AS openers,
        SAFE_CAST(JSON_VALUE(payload, '$.reactions.clicks')          AS INT64)                        AS clicks,
        SAFE_CAST(JSON_VALUE(payload, '$.recipients.clickers')       AS INT64)                        AS clickers,
        SAFE_CAST(JSON_VALUE(payload, '$.reactions.unsubscribes')    AS INT64)                        AS unsubscribes,
        SAFE_CAST(JSON_VALUE(payload, '$.recipients.soft bounces')   AS INT64)                        AS soft_bounces,
        SAFE_CAST(JSON_VALUE(payload, '$.recipients.hard bounces')   AS INT64)                        AS hard_bounces,
        SAFE_CAST(JSON_VALUE(payload, '$.reactions.spam complaints') AS INT64)                        AS spam_complaints,
    FROM  splio_campaigns AS sc
    LEFT JOIN `{{ params.bq_project }}.refined_data.splio_report_link`    AS srl USING(report_id)
), rank_shoot AS (
    -- rank shoot by date
    SELECT
        rogue_one_email_id,
        email_consent_id,
        rogue_one_shoot_date,
        ROW_NUMBER() OVER(PARTITION BY rogue_one_email_id ORDER BY rogue_one_shoot_date DESC) AS row_num
    FROM  `{{ params.bq_project }}.store_matrix_email.rogue_one_email_consent`
), recent_shoot AS (
    -- select the last shoot by rogue-one id
    SELECT *EXCEPT(row_num) FROM rank_shoot WHERE row_num = 1
), monoconsent_universes AS (
      -- Identify universes linked to only one consent reference
      SELECT
        universe_name,
        MAX(email_consent_public_ref) AS email_consent_public_ref
      FROM `{{ params.bq_project }}.refined_data.universe_email_base` AS ueb_mono
      GROUP BY universe_name
      HAVING COUNT(DISTINCT ueb_mono.email_consent_public_ref) = 1
    ),
    prepare_smtp_campaigns AS (
      -- Select data from the main store_tracking table
      SELECT
        *,
        -- Extract rogue_one_email_id from reference if pattern matches
        CASE
          WHEN REGEXP_CONTAINS(reference, r'^(\d{8})_(\d+)$')
          THEN SAFE_CAST(REGEXP_EXTRACT(reference, r'^\d{8}_(\d+)$') AS INT64)
          ELSE CAST(NULL AS INT64)
        END AS extracted_rogue_one_id,
        -- Clean universe name
        REPLACE(univers, "_fwd", "") AS cleaned_universe_name
      FROM `{{ params.bq_project }}.store_tracking.splio_report_smtp`
    )

-- 1. Select all data from the main splio_report table (regular campaigns)
SELECT DISTINCT
    pc.campaign_start_date AS start_date,
    pc.campaign_end_date AS end_date,
    STRUCT(
        pc.message_ref,
        pc.campaign_ref,
        pc.sendout_ref,
        pc.report_id
    ) AS id,
    STRUCT (
        pc.campaign_mode AS mode,
        u.name AS universe_name,
        u.type AS universe_type,
        pc.campaign_segment AS segment,
        pc.campaign_sender_email AS sender_email,
        pc.campaign_sender_name AS sender_name,
        pc.message_name,
        pc.message_subject,
        pc.main_utm_source
    ) AS campaign,
    STRUCT (
        pc.rogue_one_email_id AS id,
        COALESCE(ec.rogue_one_shoot_date, rgt.rogue_one_shoot_date) AS shoot_date,
        STRUCT(
            eb.owner_name                 AS owner_name,
            eb.pole_name                  AS pole_name,
            eb.brand_trigram              AS brand_trigram,
            srl.email_consent_public_ref  AS email_consent_public_ref
        ) AS email_base,
        srl.theme AS theme
    ) AS rogue_one,
    STRUCT(
        pc.targets,
        pc.sent,
        pc.delivered,
        pc.opens,
        pc.openers,
        pc.clicks,
        pc.clickers,
        pc.unsubscribes,
        pc.soft_bounces,
        pc.hard_bounces,
        pc.spam_complaints
    ) AS stats
FROM prepare_campaigns AS pc
LEFT JOIN `{{ params.bq_project }}.refined_data.splio_report_link`            AS srl  USING(report_id)
LEFT JOIN recent_shoot                                                        AS ec   ON ec.rogue_one_email_id = pc.rogue_one_email_id
LEFT JOIN `{{ params.bq_project }}.refined_data.email_base`                   AS eb   ON srl.email_consent_public_ref = eb.consent_public_ref
LEFT JOIN `{{ params.bq_project }}.store_matrix_email.rogue_one_theme`        AS rgt  ON rgt.rogue_one_email_id = srl.rogue_one_email_id
LEFT JOIN `{{ params.bq_project }}.store_karinto.universe`                    AS u    ON pc.universe_id = u.id


UNION ALL

-- 2. Select and transform data for SMTP campaigns
SELECT DISTINCT
    S.premier_mail AS start_date,
    S.dernier_mail AS end_date,
    STRUCT(
        S.reference AS message_ref,
        S.reference AS campaign_ref, -- Same value for all 3 in Splio Report
        S.reference AS sendout_ref, -- Same value for all 3 in Splio Report
        S.id AS report_id
    ) AS id,
    STRUCT(
        "smtp" AS mode,
        S.cleaned_universe_name AS universe_name,
        ueb.universe_type AS universe_type,
        CAST(NULL AS STRING) AS segment, -- Field remains NULL as per original logic
        CAST(NULL AS STRING) AS sender_email, -- Field remains NULL
        CAST(NULL AS STRING) AS sender_name, -- Field remains NULL
        S.sujet AS message_name, -- Field takes the subject value in order to pass filtering conditions
        S.sujet AS message_subject,
        CAST(NULL AS STRING) AS source -- Field remains NULL
    ) AS campaign,
    STRUCT(
        rot.rogue_one_email_id AS id,
        rot.rogue_one_shoot_date AS shoot_date,
        STRUCT(
            'prisma' AS owner_name, -- Hardcoded as per original logic
            CAST(NULL AS STRING) AS pole_name, -- Remains NULL
            COALESCE(eb.brand_trigram, ebmu.brand_trigram) AS brand_trigram,
            COALESCE(eb.consent_public_ref, ebmu.consent_public_ref) AS email_consent_public_ref
        ) AS email_base,
        rot.theme AS theme
    ) AS rogue_one,
    STRUCT(
        S.nb_total AS targets, -- Map nb_total to targets
        COALESCE(S.nb_total, 0) - COALESCE(S.nb_bloques, 0) AS sent, -- Calculate sent
        S.nb_livres AS delivered,
        S.nb_ouverts AS opens,
        S.nb_ouverts_unique AS openers,
        S.nb_clicks AS clicks,
        S.nb_clickers AS clickers,
        S.nb_desabo AS unsubscribes,
        S.nb_soft AS soft_bounces,
        S.nb_hard AS hard_bounces,
        S.nb_spam AS spam_complaints
    ) AS stats
FROM prepare_smtp_campaigns AS S
LEFT JOIN `{{ params.bq_project }}.store_matrix_email.rogue_one_theme` AS rot
    ON S.extracted_rogue_one_id = rot.rogue_one_email_id
LEFT JOIN `{{ params.bq_project }}.store_matrix_email.rogue_one_email_consent` AS roec
    ON S.extracted_rogue_one_id = roec.rogue_one_email_id
LEFT JOIN monoconsent_universes AS mu
    ON mu.universe_name = S.univers -- Use original univers for this join as per logic
LEFT JOIN `{{ params.bq_project }}.refined_data.email_base` AS eb
    ON roec.email_consent_id = eb.consent_id
LEFT JOIN `{{ params.bq_project }}.refined_data.email_base` AS ebmu
    ON mu.email_consent_public_ref = ebmu.consent_public_ref
LEFT JOIN `{{ params.bq_project }}.refined_data.universe_email_base` AS ueb
    ON COALESCE(eb.consent_public_ref, ebmu.consent_public_ref) = ueb.email_consent_public_ref
    AND S.cleaned_universe_name = ueb.universe_name
;

