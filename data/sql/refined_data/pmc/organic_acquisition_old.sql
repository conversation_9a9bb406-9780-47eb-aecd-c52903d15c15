-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE start_date DATE DEFAULT DATE_SUB(CURRENT_DATE(), INTERVAL {{ params.time_interval }});
DECLARE end_date DATE DEFAULT CURRENT_DATE();

CREATE TABLE IF NOT EXISTS `{{ params.bq_project_matrix }}.refined_data.organic_acquisition_old` (
    event_id                   INT64            NOT NULL    OPTIONS(description="Event id. ref:store_matrix_email.email_event.id"),
    event_date                 DATE             NOT NULL    OPTIONS(description="Event date. ref:store_matrix_email.email_event.create_date"),
    event_type                 STRING                       OPTIONS(description="Event_type. ref:store_matrix_email.email_event.type: sub/unsub"),
    event_medium               STRING                       OPTIONS(description="Event_type. ref:store_matrix_email.email_event.payload medium: email-base-new-profile/email-base-update/sub incitement"),
    event_source               STRING                       OPTIONS(description="Event source. ref:store_matrix_email.email_event.payload source: PMC/web-user"),
    id STRUCT<
        email_profile_master_id    INT64           NOT NULL     OPTIONS(description="Profile master id. ref:{{ params.bq_project_matrix }}.store_matrix_email.email_event.profile_master_id"),
        pmc_profile_master_id      INT64                        OPTIONS(description="Profile master id. ref:{{ params.bq_project_matrix }}.business_data.profile_digital_360.pmc_profile_master_id"),
        pmc_uuid                   STRING                       OPTIONS(description="PMC uuid. ref:{{ params.bq_project_userhub }}.refined_data.user_event.uuid")
        > OPTIONS(description="User ids"),
    signup STRUCT<
        brand_trigram              STRING                       OPTIONS(description="Brand trigram in signup service. 1st part"),
        service                    STRING                       OPTIONS(description="Service name in signup service. 2nd part"),
        interface                  STRING                       OPTIONS(description="Interface in signup service. 3rd part"),
        source                     STRING                       OPTIONS(description="Source/Environment in signup service. 5th part"),
        content                    STRING                       OPTIONS(description="Content in signup service. 4th part")
    > OPTIONS(description="Split signup service"),
    email_base   ARRAY<STRUCT<
        consent_public_ref         STRING                       OPTIONS(description="Consent public ref"),
        consent_type               STRING                       OPTIONS(description="Consent type as enum=['nl','loy','alert','part']")
    >> OPTIONS(description="Event information")
)
PARTITION BY event_date
OPTIONS(
    description="This table contains refined organic_acquisition.\n" ||
                "Source: store_matrix_email.email_event.\n" ||
                "Filters for organic acquisition: source='PMC' AND medium='email-base-new-profile'.\n" ||
                "Sync.: Daily." ||
                "DAG: {{ dag.dag_id }}."
);

{% if params.is_full %}
SET start_date = DATE("{{ params.start_date }}");
SET end_date = DATE("{{ params.end_date }}");
{% endif %}

DELETE FROM `{{ params.bq_project_matrix }}.refined_data.organic_acquisition_old`
WHERE event_date BETWEEN start_date AND end_date;

MERGE `{{ params.bq_project_matrix }}.refined_data.organic_acquisition_old` AS dst
USING(
    WITH organic_acquisition AS (
        SELECT DISTINCT
            DATE(ev.create_date) AS event_date,
            ev.id                AS event_id,
            ec.public_ref        AS consent_public_ref,
            profile_master_id    AS email_profile_master_id,
            ev.type              AS event_type,
            JSON_EXTRACT_SCALAR(ev.payload, "$.medium") AS event_medium,
            ec.type              AS consent_type,
            JSON_EXTRACT_SCALAR(ev.payload, "$.source") AS event_source
        FROM `{{ params.bq_project_matrix }}.store_matrix_email{{ dag.default_args.dataset_suffix }}.email_event` AS ev,
            UNNEST(JSON_EXTRACT_ARRAY(payload, "$.consent_ids")) AS consent_id
        JOIN `{{ params.bq_project_matrix }}.store_karinto{{ dag.default_args.dataset_suffix }}.email_consent` AS ec
            ON CAST(ec.id AS INT64) = CAST(consent_id AS INT64)
        WHERE
            DATE(ev.create_date) BETWEEN start_date AND end_date
            AND JSON_EXTRACT_SCALAR(ev.payload, "$.medium") IN ('email-base-new-profile', 'email-base-update', 'sub incitement')
            AND JSON_EXTRACT_SCALAR(ev.payload, "$.source") IN ('PMC', 'web-user')

    ), profile_ids AS (
        SELECT
            pd.id.email_profile_master_id AS email_profile_master_id ,
            pd.id.pmc_profile_master_id AS pmc_profile_master_id,
            pd.id.pmc_uuid AS pmc_uuid,
            pmc.system_data.signup_service AS signup_service
        FROM `{{ params.bq_project_matrix }}.business_data{{ dag.default_args.dataset_suffix }}.profile_digital_360` AS pd
        JOIN `{{ params.bq_project_userhub }}.refined_data{{ dag.default_args.dataset_suffix }}.pmc_profile` AS pmc
        ON pd.info.email = pmc.profile_data.email
        WHERE DATE(pmc.system_data.signup_date) BETWEEN start_date AND end_date
    ), merged_event_email_pmc AS (
        SELECT
            o.event_id,
            o.event_date,
            o.event_type,
            o.event_medium,
            o.event_source,
            o.email_profile_master_id,
            p.pmc_profile_master_id,
            p.pmc_uuid,
            p.signup_service,
            ARRAY_AGG(
                STRUCT(
                    o.consent_public_ref,
                    o.consent_type
                )
                ORDER BY o.event_date, o.event_id, o.consent_type, o.consent_public_ref
            ) AS email_base
        FROM organic_acquisition  AS o
        LEFT JOIN profile_ids     AS p USING(email_profile_master_id)
        GROUP BY ALL
    )

    SELECT
        event_id,
        event_type,
        event_date,
        event_medium,
        event_source,
        STRUCT(
            email_profile_master_id,
            pmc_profile_master_id,
            pmc_uuid
        ) AS id,
        STRUCT(
            SPLIT(signup_service, '_')[SAFE_OFFSET(0)] AS brand_trigram,
            SPLIT(signup_service, '_')[SAFE_OFFSET(1)] AS service,
            SPLIT(signup_service, '_')[SAFE_OFFSET(2)] AS interface,
            SPLIT(signup_service, '_')[SAFE_OFFSET(3)] AS source,
            SPLIT(signup_service, '_')[SAFE_OFFSET(4)] AS content
        ) AS signup,
        email_base
    FROM merged_event_email_pmc
) AS ref
    ON  ref.id.email_profile_master_id = dst.id.email_profile_master_id
    AND DATE(ref.event_date) = DATE(dst.event_date)
    AND ref.event_id = dst.event_id

WHEN MATCHED THEN
    UPDATE SET
        dst.id.pmc_profile_master_id    = ref.id.pmc_profile_master_id,
        dst.id.pmc_uuid              = ref.id.pmc_uuid,
        dst.signup                   = ref.signup,
        dst.email_base               = ref.email_base

WHEN NOT MATCHED THEN
    INSERT(
        event_id,
        event_type,
        event_date,
        event_medium,
        event_source,
        id,
        signup,
        email_base
    )
    VALUES(
        ref.event_id,
        ref.event_type,
        ref.event_date,
        ref.event_medium,
        ref.event_source,
        ref.id,
        ref.signup,
        ref.email_base
    );
