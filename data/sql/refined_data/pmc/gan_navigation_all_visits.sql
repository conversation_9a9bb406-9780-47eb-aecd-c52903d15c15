-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE start_date DEFAULT DATE("{{ params.start_date }}");
DECLARE end_date DEFAULT DATE("{{ params.end_date }}");


CREATE TABLE IF NOT EXISTS `{{ params.bq_project_matrix }}.workspace.refined_template__gan_navigation_all_visits_YEARMONTH` (
    visit_date          DATE      NOT NULL    OPTIONS(description="Navgation date"),
    session_id          STRING                OPTIONS(description="Session ID as concatened fullVisitorId and visitId"),
    user                STRUCT<
        full_visitor_id     STRING                OPTIONS(description="Visitor ID from Google Analytics: fullVisitorId"),
        web_id              STRING                OPTIONS(description="PMC Web ID when it's a logged visit, NULL otherwise"),
        client_id           STRING                OPTIONS(description="Client ID from GAN. ref: "),
        batch_install_id    STRING                OPTIONS(description="Batch Intall ID from GAN. ref: Custom Dimension 48 (Prisma) / 21 (Cerise)"),
        email_sha256        STRING                OPTIONS(description="Email sha256. ref: business_data.profile_digital_360.id.email_sha256")
    > OPTIONS(description="User informations"),
    property            STRUCT<
        owner_name          STRING                OPTIONS(description="Owner as enum=['prisma','cerise']"),
        brand_trigram       STRING                OPTIONS(description="Brand trigram"),
        website             STRING                OPTIONS(description="Website name"),
        platform            STRING                OPTIONS(description="Platform for navigation as enum=['web','mob']"),
        tag_id              STRING                OPTIONS(description="Tracking id for dataset in projects 'prisma-gan-bigquery-ojd-export' or 'cherry-userwaypoint'")
    > OPTIONS(description="Property informations (Owner, Brand, Website, Platform)"),
    visit               STRUCT<
        tracking            STRUCT<
            source              STRING                OPTIONS(description="Traffic source. ref: trafficSource.source"),
            medium              STRING                OPTIONS(description="Traffic medium. ref: trafficSource.medium"),
            channel             STRING                OPTIONS(description="Traffic channel as enum=['email','batch','google discover',...]")
        > OPTIONS(description="Tracking informations as source, medium, channel grouping"),
        is_logged           BOOL                  OPTIONS(description="TRUE if visit was made by a logged user (with a pmc web id). FALSE otherwise."),
        device_category     STRING                OPTIONS(description="Device category. ref: device.deviceCategory")
    > OPTIONS(description="Visit informations like source, medium, channel, browser, logged status, visited URL"),
    hits                ARRAY<STRUCT<
        number              INT64                 OPTIONS(description="Hit number as rank in list of ordered hits"),
        type                STRING                OPTIONS(description="Type of hit as enum=['PAGE','EVENT']"),
        page                STRUCT<
            hostname            STRING                OPTIONS(description="Hostname"),
            path                STRING                OPTIONS(description="Page path as URL"),
            title               STRING                OPTIONS(description="Page title"),
            canonical_url       STRING                OPTIONS(description="Simplified url visited for this session"),
            article_id          STRING                OPTIONS(description="If page contains an article then article_id (Can be merged with table `pm-prod-mirror.refined_data.article.id`). NULL otherwise.")
        > OPTIONS(description="Web page informations")
    >> OPTIONS(description="List of hits with informations"),
    PRIMARY KEY (visit_date, session_id) NOT ENFORCED
)
PARTITION BY visit_date
OPTIONS(
    description="This table contains all visits on Prisma & Cerise websites partitioned by day.\n"
              ||"You can find informations on sessions and page views per user, device categories, browsers, article and more in this refined data table.\n"
              ||"\n"
              ||"Sync.: Daily.\n"
              ||"DAG: {{ dag.dag_id }}"
);


{% if params.is_full != "True" %}
SET start_date  = DATE_SUB(DATE("{{ next_ds }}"), INTERVAL {{ params.time_interval }});
SET end_date  = CURRENT_DATE();
{% endif %}


{% for yyyymm in params.date_list_ym %}
    -- Create gan_navigation_all_visits table monthly as Template Table "_YEARMONTH"
    CREATE TABLE IF NOT EXISTS `{{ params.bq_project_matrix }}.refined_data.gan_navigation_all_visits_{{ yyyymm }}01`
    LIKE `{{ params.bq_project_matrix }}.workspace.refined_template__gan_navigation_all_visits_YEARMONTH`
    OPTIONS(
        description="This table contains all visits on Prisma & Cerise websites partitioned by day.\n"
                  ||"You can find informations on sessions and page views per user, device categories, browsers, article and more in this refined data table.\n"
                  ||"\n"
                  ||"Sync.: Daily.\n"
                  ||"DAG: {{ dag.dag_id }}"
    );

    -- For each website, we insert new data from GAN
    {% for i in range(params.list_len) %}

        -- Delete rows in order to not generate duplicates
        DELETE FROM `{{ params.bq_project_matrix }}.refined_data.gan_navigation_all_visits_{{ yyyymm }}01`
        WHERE DATE(visit_date) BETWEEN start_date AND end_date
            AND property.brand_trigram = "{{ params.trigram[i] }}"
            AND property.platform = SPLIT("{{ params.website[i] }}", "_")[SAFE_OFFSET(1)]
            AND property.website = "{{ params.label[i] }}";

--         -- Insert new GAN data
        INSERT INTO `{{ params.bq_project_matrix }}.refined_data.gan_navigation_all_visits_{{ yyyymm }}01`
        WITH gan_data AS (
            -- Extract data from GAN BQ project
            -- Each tag corresponds to a unique website on a unique platform.
            SELECT DISTINCT
                DATE(FORMAT_DATE('%Y-%m-%d',PARSE_DATE('%Y%m%d', date)))        AS visit_date,
                visitstarttime                                                  AS visit_start_time,
                visitId                                                         AS visit_id,
                ----------------------------------------- USER ------------------------------------------------
                fullVisitorId                                                   AS user_full_visitor_id,
                IF(
                    hitscustomDimensions.index = 25 AND hits.eventInfo.eventAction = "WebId",
                    hitscustomDimensions.value, NULL
                )                                                               AS user_web_id,
                clientId                                                        AS user_client_id,
                CASE
                    WHEN '{{ params.owner[i] }}' = 'prisma' AND hitscustomDimensions.index = 48 THEN hitscustomDimensions.value
                    WHEN '{{ params.owner[i] }}' = 'cerise' AND hitscustomDimensions.index = 21 THEN hitscustomDimensions.value
                    ELSE NULL
                END                                                             AS user_batch_install_id,
                --------------------------------------- PROPERTY ----------------------------------------------
                "{{ params.owner[i] }}"                                         AS owner_name,
                "{{ params.trigram[i] }}"                                       AS brand_trigram,
                "{{ params.label[i] }}"                                         AS website,
                SPLIT("{{ params.website[i] }}", "_")[SAFE_OFFSET(1)]           AS platform,
                "{{ params.tag[i] }}"                                           AS tag_id,
                ---------------------------------------- DEVICE -----------------------------------------------
                IFNULL(device.deviceCategory, '(not set)')                      AS device_category,
                ---------------------------------------- VISIT ------------------------------------------------
                IFNULL(trafficSource.source, '(not set)')                       AS visit_source,
                IFNULL(trafficSource.medium, '(not set)')                       AS visit_medium,
                -- Channel grouping with function defined in {{ params.bq_project_matrix }} project
                `{{ params.bq_project_matrix }}.workspace.get_gan_navigation_channel`(
                    trafficSource.source,
                    trafficSource.medium,
                    trafficSource.campaign
                )                                                               AS visit_channel,
                ((hitscustomDimensions.index = 25 AND hits.eventInfo.eventAction = "WebId") OR (hitscustomDimensions.index = 11 AND hitscustomDimensions.value='true' AND SPLIT("{{ params.website[i] }}", "_")[SAFE_OFFSET(1)] = 'mob')) AS visit_is_logged,

                ------------------------------------------ HITS ------------------------------------------------
                CASE
                    -- if it's an article on prisma website
                    WHEN LOWER("{{ params.owner[i] }}") = "prisma"
                        AND hitscustomDimensions.index = 30
                        AND REGEXP_CONTAINS(hitscustomDimensions.value,r'[Aa]rticle:')
                        THEN REGEXP_EXTRACT(hitscustomDimensions.value,r'^[Aa]rticle:([^\s]+)')
                    -- if it's a recipe on prisma website
                    WHEN LOWER("{{ params.owner[i] }}") = "prisma"
                        AND hitscustomDimensions.index = 30
                        AND REGEXP_CONTAINS(hitscustomDimensions.value,r'[Rr]ecipe:')
                        THEN REGEXP_EXTRACT(hitscustomDimensions.value,r'^[Rr]ecipe:([^\s]+)')
                    -- if it's cerise website
                    WHEN LOWER("{{ params.owner[i] }}") = "cerise"
                        AND hitscustomDimensions.index = 26
                        THEN hitscustomDimensions.value
                    ELSE NULL
                END                                                            AS article_id,
                REGEXP_REPLACE(
                    REGEXP_EXTRACT(
                        CONCAT(hits.page.hostname, hits.page.pagePath),
                        '^([^/?#]+(?:/[^?#]*)?)(?:[?][^#]+)?(?:#.*)?$'
                    ), r'\/amp\/', '/'
                )                                                              AS canonical_url,
                hits.hitNumber                                                 AS hit_number,
                hits.type                                                      AS hit_type,
                hits.page.hostname                                             AS hostname,
                hits.page.PagePath                                             AS page_path,
                hits.page.PageTitle                                            AS page_title
            FROM `{{ params.bq_project_gan[i] }}.{{ params.tag[i] }}.ga_sessions_*`
                LEFT JOIN UNNEST(hits) AS hits
                LEFT JOIN UNNEST(hits.customDimensions) AS hitscustomDimensions
            WHERE _TABLE_SUFFIX NOT LIKE '%intraday_%'
                AND _TABLE_SUFFIX BETWEEN "{{ yyyymm }}01" AND "{{ yyyymm }}31"
                AND _TABLE_SUFFIX >= REGEXP_REPLACE(CAST(start_date AS STRING), '-', '')
                AND _TABLE_SUFFIX <= REGEXP_REPLACE(CAST(end_date AS STRING), '-', '')
                AND totals.visits =1
        ), get_one_article AS (
          SELECT DISTINCT
              article_id,
              canonical_url
          FROM `{{ params.bq_project_mirror }}.refined_data.article` , UNNEST(content.url.public) AS canonical_url
        ), gan_data_ordered_hits AS (
            SELECT DISTINCT
                visit_date,
                visit_start_time,
                user_full_visitor_id,
                user_web_id,
                user_client_id,
                user_batch_install_id,
                visit_id,
                owner_name,
                brand_trigram,
                website,
                platform,
                tag_id,
                device_category,
                visit_source,
                visit_medium,
                visit_channel,
                visit_is_logged,
                hit_type,
                hit_number,
                hostname,
                canonical_url,
                page_path,
                page_title,
                COALESCE(gd.article_id, ar.article_id) AS article_id, -- OVER (PARTITION BY visit_date, visit_id, user_full_visitor_id, tag_id, device_category, visit_source, visit_medium, visit_is_logged ORDER BY article_id DESC),
                ROW_NUMBER() OVER (PARTITION BY visit_date, visit_id, user_full_visitor_id, tag_id, device_category, visit_source, visit_medium, visit_is_logged, hit_number ORDER BY COALESCE(gd.article_id, ar.article_id) DESC) AS row_id
            FROM gan_data AS gd
            LEFT JOIN get_one_article  AS ar USING(canonical_url)
        ), filled_and_structured_data AS (
            -- aggregate hit informations
            SELECT
                visit_date,
                user_full_visitor_id,
                FIRST_VALUE(user_web_id IGNORE NULLS) OVER (PARTITION BY visit_date, user_full_visitor_id, visit_id ORDER BY user_web_id DESC) AS user_web_id,
                FIRST_VALUE(user_client_id IGNORE NULLS) OVER (PARTITION BY visit_date, user_full_visitor_id, visit_id ORDER BY user_client_id DESC) AS user_client_id,
                FIRST_VALUE(user_batch_install_id IGNORE NULLS) OVER (PARTITION BY visit_date, user_full_visitor_id, visit_id ORDER BY user_batch_install_id DESC) AS user_batch_install_id,
                owner_name,
                brand_trigram,
                website,
                platform,
                tag_id,
                CONCAT(user_full_visitor_id,'_',visit_start_time) AS session_id,
                device_category,
                visit_source,
                visit_medium,
                visit_channel,
                MAX(visit_is_logged) OVER (PARTITION BY visit_date, user_full_visitor_id, visit_start_time) AS visit_is_logged,
                STRUCT(
                    hit_number AS number,
                    hit_type   AS type,
                    STRUCT(
                        hostname      AS hostname,
                        page_path     AS path,
                        page_title    AS title,
                        canonical_url AS canonical_url,
                        article_id    AS article_id
                    ) AS page
                ) AS hits
            FROM gan_data_ordered_hits
            WHERE row_id = 1
        ), aggregated_hits AS (
            SELECT
                visit_date,
                user_full_visitor_id,
                user_web_id,
                user_client_id,
                user_batch_install_id,
                owner_name,
                brand_trigram,
                website,
                platform,
                tag_id,
                session_id,
                device_category,
                visit_source,
                visit_medium,
                visit_channel,
                visit_is_logged,
                ARRAY_AGG(hits) AS hits
            FROM filled_and_structured_data
            GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16
        )

        SELECT
            visit_date,
            session_id,
            STRUCT(
                user_full_visitor_id    AS full_visitor_id,
                user_web_id             AS web_id,
                user_client_id          AS client_id,
                user_batch_install_id   AS batch_install_id,
                pd.id.email_sha256      AS email_sha256
            ) AS user,
            STRUCT(
                owner_name,
                brand_trigram,
                website,
                platform,
                tag_id
            ) AS property,
            STRUCT(
                STRUCT(
                    visit_source     AS source,
                    visit_medium     AS medium,
                    visit_channel    AS channel
                ) AS tracking,
                visit_is_logged      AS is_logged,
                device_category
            ) AS visit,
            hits
        FROM aggregated_hits AS ah
        LEFT JOIN `{{ params.bq_project_matrix }}.business_data.profile_digital_360` AS pd
            ON pd.id.pmc_web_id = ah.user_web_id;
    {% endfor %}

    CREATE OR REPLACE TEMP TABLE UserUnified AS
    SELECT
        session_id,
        STRUCT(
            MAX(user.full_visitor_id) AS full_visitor_id,
            MAX(user.web_id) AS web_id,
            MAX(user.client_id) AS client_id,
            MAX(user.batch_install_id) AS batch_install_id,
            MAX(user.email_sha256) AS email_sha256
        ) AS user,
        MAX(visit.is_logged) as is_logged
    FROM `{{ params.bq_project_matrix }}.refined_data.gan_navigation_all_visits_{{ yyyymm }}01`
    WHERE visit_date BETWEEN start_date and end_date
    GROUP BY session_id;
    -- Update the original table
    UPDATE `{{ params.bq_project_matrix }}.refined_data.gan_navigation_all_visits_{{ yyyymm }}01` t1
    SET
    -- Propagate the user info to have unified info across the session
    t1.user = t2.user,
    -- Propagate the login information so that it retroactively affects session events before the login
    t1.visit.is_logged = t2.is_logged
    FROM UserUnified t2
    WHERE t1.session_id = t2.session_id;



{% endfor %}
