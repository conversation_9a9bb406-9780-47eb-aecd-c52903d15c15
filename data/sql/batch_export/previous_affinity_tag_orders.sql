-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- PROCESS:
-- Archive add / remove orders sent to Batch
-- Recreate tags from `generated_data.affinity_tag` using historic data and orders sent to Batch

MERGE `{{ params.bq_project }}.export_batch.previous-affinity_tag_orders` AS dst
USING(
    WITH map_tag_platform AS (
        -- map tags and platforms (list of brand / support in which a tag is available)
        -- used to filter rows before later joins
        SELECT
            tag,
            CONCAT(LOWER(brand.brand_name), '_', LOWER(support)) AS platform
        FROM `{{ params.bq_project }}.store_affinity_segment.tag_platform`,
        UNNEST(brands) AS brand,
        UNNEST(brand.platforms) AS support
    ), get_previous_tags AS (
        -- get yesterday's tags by profile
        SELECT
            pd.id.pmc_uuid AS pmc_uuid,
            segment_names
        FROM `{{ params.bq_project }}.generated_data.affinity_tag_history` AS hist
        -- get pmc_uuid
        JOIN `{{ params.bq_project }}.business_data.profile_digital_360` AS pd ON pd.id.email_profile_master_id = hist.email_profile_master_id
        -- keep only profiles with add / remove orders for today --> reduce volume before next joins
        JOIN `{{ params.bq_project }}.export_batch.actual-affinity_tag_orders` AS orders ON pd.id.pmc_uuid = orders.pmc_uuid
        WHERE
            -- get yesterday's data
            hist.observation_date = DATE_SUB(DATE("{{ next_ds }}"), INTERVAL 1 DAY)
            AND (affinity_tag_to_add != '[]' OR affinity_tag_to_remove != '[]')
    ), unnest_previous_tags AS (
        -- unnest tags
        -- use tag mapping to filter tags by platform
        SELECT
            pmc_uuid,
            platform,
            tag
        FROM get_previous_tags, UNNEST(segment_names) AS tag
        JOIN map_tag_platform AS mtp USING(tag)
    ), get_actual_orders AS (
        -- get today's add / remove orders
        SELECT
            platform,
            pmc_uuid,
            tag,
            'add' AS action
        FROM `{{ params.bq_project }}.export_batch.actual-affinity_tag_orders`, UNNEST(JSON_EXTRACT_STRING_ARRAY(affinity_tag_to_add, "$")) AS tag
        -- exclude rows that do not change
        WHERE affinity_tag_to_add != '[]'
        UNION ALL
        SELECT
            platform,
            pmc_uuid,
            tag,
            'remove' AS action
        FROM `{{ params.bq_project }}.export_batch.actual-affinity_tag_orders`, UNNEST(JSON_EXTRACT_STRING_ARRAY(affinity_tag_to_remove, "$")) AS tag
        -- exclude rows that do not change
        WHERE affinity_tag_to_remove != '[]'
    ), rebuild_current_tags AS (
        -- rebuild data from `affinity_tag` by comparing orders sent and yesterday's tags by profile
        -- return: added or unchanged tags
        -- ignore: excluded tags
        SELECT
            pmc_uuid,
            platform,
            TO_JSON_STRING(ARRAY_AGG(DISTINCT
                (
                    CASE
                        WHEN gao.tag IS NULL THEN upt.tag                                   -- tag is still active and should be kept
                        WHEN gao.tag IS NOT NULL AND gao.action = 'add' THEN gao.tag        -- tag was added
                    ELSE NULL END                                                           -- ignore other cases
                ) IGNORE NULLS)                                                             -- remove them from array
            ) AS current_tags
        FROM get_actual_orders AS gao
        FULL OUTER JOIN unnest_previous_tags AS upt USING(pmc_uuid, tag, platform)
        GROUP BY ALL
    )

    SELECT
        platform,
        pmc_uuid,
        affinity_tag_to_add,
        affinity_tag_to_remove,
        current_tags
    FROM `{{ params.bq_project }}.export_batch.actual-affinity_tag_orders`
    LEFT JOIN rebuild_current_tags USING(pmc_uuid, platform)
) AS ref
    ON dst.platform = ref.platform
    AND dst.pmc_uuid = ref.pmc_uuid
WHEN MATCHED THEN
UPDATE SET
    dst.affinity_tag_to_add = ref.affinity_tag_to_add,
    dst.affinity_tag_to_remove = ref.affinity_tag_to_remove,
    dst.current_tags = ref.current_tags
WHEN NOT MATCHED BY TARGET THEN
INSERT(
    platform,
    pmc_uuid,
    affinity_tag_to_add,
    affinity_tag_to_remove,
    current_tags
    )
VALUES(
    ref.platform,
    ref.pmc_uuid,
    ref.affinity_tag_to_add,
    ref.affinity_tag_to_remove,
    ref.current_tags
    );
