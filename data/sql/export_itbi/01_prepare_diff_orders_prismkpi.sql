-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
SELECT
    TIMESTAMP(ref.event_date) AS event_date,
    ref.action AS event_type,
    ref.email,
    TO_HEX(SHA256(ref.email )) AS email_hash,
    JSON_EXTRACT(CONCAT(
        '{',
        '"profile":',
        '{',
            '"lastname":"', CAST(IFNULL(pmk_info.LASTNAME, '') AS string), '",',
            '"firstname":"', CAST(IFNULL(pmk_info.FIRSTNAME, '') AS string), '",',
        '},',
        '"ev": 100,',
        '"profile_master_id": "', IFNULL(CAST(pmi.id AS string), ''), '",',
        '"event_date":"', CAST(ref.event_date AS string), '",',
        '"medium": "synchronize share it-bi diff prismkpi orders",',
        '"source": "it-bi",',
        '"app": "mozart",',
        '"process": "{{ dag.dag_id }}",',
        '"consent_ids":[',CAST(kec.id AS string),']',
        '}'), '$'
    ) AS payload
FROM `pm-prod-matrix.share_itbi.pm_b2b_prisme_orders` AS ref
JOIN `pm-prod-matrix.store_karinto.email_consent` AS kec ON kec.public_ref = 'b2b_prisme_kpi_crm'
LEFT JOIN `pm-prod-vente-au-numero.export_it_data.email_newsletter` AS pmk_info ON pmk_info.email = ref.email
LEFT JOIN `pm-prod-matrix.store_matrix_email.profile_master_id` AS pmi ON pmi.email = ref.email
WHERE ref.target = 'matrix' AND ref.consent = "b2b_prisme_kpi_crm";
