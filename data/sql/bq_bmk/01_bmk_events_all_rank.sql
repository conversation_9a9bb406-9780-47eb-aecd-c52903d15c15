-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}


-- BEGIN


-- ################################# DEBUG BEGIN #############################
--DROP TABLE IF EXISTS `{{params.bq_dataset_name}}.{{params.bq_ranked_table_name}}`
--;
-- #################################  DEBUG END  #############################


-- WE RE-CREATE THE ranked_table TABLE
-- IF (FOR SOME REASON) IT IS MISSING
CREATE TABLE IF NOT EXISTS `{{params.bq_dataset_name}}.{{params.bq_ranked_table_name}}` (
    uuid            STRING	    NOT NULL    OPTIONS(
        description="UUID received from the PMC CSV input files. Stored here as STRING " ||
                    "to be consistent with the format of values returned " ||
                    "by the 'GENERATE_UUID' GCP fonction."
    ),
    brand         STRING	    NOT NULL    OPTIONS(
        description="brand trigram ; as provided in the PMC CSV input file."
    ),
    content_id    STRING                  OPTIONS(
        description="concatened string with '|' as a separator, of subscribed 'tag_id's, " ||
        "as provided in the PMC CSV input file."
    ),
    updated_at    TIMESTAMP	NOT NULL    OPTIONS(
        description="Date provided in the PMC CSV input files. " ||
                    "Date of (last) modification of the record."
    ),
    rank          INT64 	    NOT NULL    OPTIONS(
        description="if 'updated_at' is the first (the earliest) for a given 'uuid/brand' pair, " ||
                    "then 'rank=1'. If its the second, then 'rank=2', etc."
    ),
    csv_datetime  TIMESTAMP	NOT NULL    OPTIONS(
        description="timestamp of the PMC CSV input file in which this particular record " ||
                    "last appeared."
    )
)
CLUSTER BY
    uuid, brand
OPTIONS(
  expiration_timestamp=NULL,
  description="temporary table where all PMC 'bookmark' CSV input files (concatenated) are dumped. \n" ||
              "at this stage, we got rid of superseded records (and kept only last-transmitted rows " ||
              "for each 'uuid/brand/updated_at' tuple). \n\n" ||
              "REMARK 1 : for rows with 'rank=1' and 'content_id' empty, we don't do anything " ||
              "(PMC informs us as the first status update that all previous tag subscription(s) " ||
              "for that 'uuid/brand' pair are now unsubscribed). \n\n" ||
              "REMARK 2 : for rows with 'rank=1', generally speaking, we record them " ||
              "as being 'tag subscription' events (even if it is very possible that " ||
              "they actually are notifications of 'tag unsubscribed' events " ||
              "on tags that we had never heard of prior to any PMC 'bookmark' data imports). \n\n " ||
              "Daily updates (at 1am) thru the Airflow DAG 'bq__bmk_events_all'."
)
;


-- BUG FIX (expiration, to be set to "Never" (NULL), if that's what we want,
-- must be set AFTER the table creation otherwise is ignored
-- (and the default schema value is applied)
ALTER TABLE `{{params.bq_dataset_name}}.{{params.bq_ranked_table_name}}`
SET OPTIONS (expiration_timestamp=NULL)
;


-- WE FREE THE ranked_table TABLE FROM ANY RECORD
TRUNCATE TABLE `{{params.bq_dataset_name}}.{{params.bq_ranked_table_name}}`
;


-- ##########################################################################
-- ###################### RE-POPULATE THE ENTIRE TABLE ######################
-- ##########################################################################
-- We 'rank' records per 'updated_date' (from the latest to the newest state)
-- When there is a draw (different 'content_id' for the same 'updated_date'),
-- we keep the status that has been communicated last (highest 'csv_datetime')

INSERT INTO `{{params.bq_dataset_name}}.{{params.bq_ranked_table_name}}`
WITH ranked_records_TAB AS (
    SELECT
        updated_at, uuid, brand, content_id,
        -- we use 'ROW_NUMBER' for convenience ; in case a PMC CSV file has been imported more than once
        -- in the parent BQ table (so as to be able to keep one only in our result).
        -- REMARK : as we're here in a "WITH" clause which is called
        -- more than once in the following query,
        -- we ensure that the row numbering always follows the same rows-ordering (whichever it might be)
        ROW_NUMBER() over(order by updated_at, uuid, brand, content_id, csv_datetime) AS row_nb,
        DENSE_RANK() OVER (PARTITION BY uuid, brand ORDER BY updated_at ASC) AS rank,
        csv_datetime
    FROM
        `{{params.bq_dataset_name}}.{{params.bq_bulk_table_name}}`
)
SELECT
    uuid, brand, content_id, updated_at, rank, csv_datetime
    --, row_nb
FROM
    ranked_records_TAB AS outer_TAB
where
    (uuid, brand, rank, updated_at,
    csv_datetime,
    row_nb
    ) = (
        SELECT AS STRUCT
            last_csv_TAB.uuid,
            last_csv_TAB.brand,
            last_csv_TAB.rank,
            last_csv_TAB.updated_at,
            last_csv_TAB.csv_datetime,
            MIN(last_csv_TAB_II.row_nb) AS row_nb # we have to pick any one
        FROM
            (
                -- In case of conflicting records (different content_id for a given updated_at),
                -- keep the one from the latest PMC-issued CSV.
                SELECT
                    inner_TAB.uuid,
                    inner_TAB.brand,
                    inner_TAB.rank,
                    inner_TAB.updated_at,
                    MAX(inner_TAB.csv_datetime) AS csv_datetime
                FROM
                    ranked_records_TAB AS inner_TAB
                WHERE
                    outer_TAB.uuid = inner_TAB.uuid
                    AND outer_TAB.brand = inner_TAB.brand
                    AND outer_TAB.rank = inner_TAB.rank
                    AND outer_TAB.updated_at = inner_TAB.updated_at
                GROUP BY
                    inner_TAB.uuid,
                    inner_TAB.brand,
                    inner_TAB.rank,
                    inner_TAB.updated_at
            ) AS last_csv_TAB
            -- To retrieve associated row numbers (in case a given file
            -- has been uploaded several times in the parent table.)
            INNER JOIN ranked_records_TAB AS last_csv_TAB_II
              ON (
                  last_csv_TAB_II.uuid = last_csv_TAB.uuid
                  AND last_csv_TAB_II.brand = last_csv_TAB.brand
                  AND last_csv_TAB_II.rank = last_csv_TAB.rank
                  AND last_csv_TAB_II.updated_at = last_csv_TAB.updated_at
                  AND last_csv_TAB_II.csv_datetime = last_csv_TAB.csv_datetime
              )
        GROUP BY
            last_csv_TAB.uuid,
            last_csv_TAB.brand,
            last_csv_TAB.rank,
            last_csv_TAB.updated_at,
            last_csv_TAB.csv_datetime
    )
    --ORDER BY uuid, brand, rank
;

--  END