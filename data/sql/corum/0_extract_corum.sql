-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
SELECT CASE WHEN civility = 'Monsieur' THEN 'Mr'
            WHEN civility = 'Madame' THEN 'Mrs'
            ELSE civility
       END AS civility, 
       firstname, 
       lastname, 
       NULL AS "phone", 
       '+' || phone AS "Mobile Phone", 
       email,
       NULL AS "Address 1: Street 1",
       NULL AS "Address 1: ZIP/Postal Code",
       NULL AS "Address 1: City",
       NULL AS "Address1: Country",
       NULL AS "Nationality",
       NULL AS "Job Title",
       'Yes' AS "CORUM Group Opt-in",
       'online_partnership' AS "Media",
       'capital' AS "Partner",
       'fr_cap_lp_digital_2021' AS "Campaign",
       CAST(project_value AS VARCHAR) || '€' AS "Project description",
       'BTOC FRANCE' AS "Owner",
       'Lead Direct' AS "Relationship",
       NULL AS "Office Phone",
       NULL AS "Other Investment Objective"
FROM esampling.qualifio_to_corum
WHERE DATE(create_date) = current_date - 1 