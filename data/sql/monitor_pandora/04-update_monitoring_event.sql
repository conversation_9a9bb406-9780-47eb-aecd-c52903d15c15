-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{params.bq_project}}.datastudio.pandora_monitoring` ( -- pm-prod-matrix.datastudio.pandora_monitoring
    Partenaire                      STRING      NOT NULL    OPTIONS(description="partner's name." ||
                                                             "ref: store_pandora.partner.name"),
    Contexte                        STRING      NOT NULL    OPTIONS(description="context's name." ||
                                                             "ref: store_pandora.context.name"),
    Date                            TIMESTAMP   NOT NULL    OPTIONS(description="date when Pandora events occured"),
    Nouveaux                        INTEGER                 OPTIONS(description="SUM of events with (NEW response and not cancelled)"),
    Nouveaux_ann                    INTEGER                 OPTIONS(description="SUM of events with (NEW response and cancelled)"),
    Duplications                    INTEGER                 OPTIONS(description="SUM of events with (DPL response and not cancelled)"),
    Duplications_ann                INTEGER                 OPTIONS(description="SUM of events with (DPL response and cancelled)"),
    Reactivations                   INTEGER                 OPTIONS(description="SUM of events with (REA response and not cancelled)"),
    Reactivations_ann               INTEGER                 OPTIONS(description="SUM of events with (REA response and cancelled)"),
    Permanent_Oppostion_List        INTEGER                 OPTIONS(description="SUM of events with POL response"),
    Erreurs                         INTEGER                 OPTIONS(description="SUM of events with ERR response"),
    Exceptions                      INTEGER                 OPTIONS(description="SUM of events with EXC response"),
    Total_Acquisition               INTEGER                 OPTIONS(description="COUNT of all events that occured that Date"),
    Total_Acquisition_ann           INTEGER                 OPTIONS(description="COUNT of all cancelled events"),
    Nouveaux_PMC                    INTEGER                 OPTIONS(description="SUM of PMC events with NEW response"),
    Existe_PMC                      INTEGER                 OPTIONS(description="SUM of PMC events with EXI response." ||
                                                                                "EXI : Terms of use validation"),
    Duplications_PMC                INTEGER                 OPTIONS(description="SUM of PMC events with DPL response"),
    Erreurs_PMC                     INTEGER                 OPTIONS(description="SUM of PMC events with ERR response")
)
PARTITION BY TIMESTAMP_TRUNC(Date, DAY)
OPTIONS(description="Gathered indicators about Pandora acquisition events related to the response sent by the API."||
                    "KPIs include count of new, duplication, reactivation and errors by date"||
                    "Table partitionned by Date with day scale "||
                    "DAG: pandora_monitoring_process. "||
                    "DataStudios: https://datastudio.google.com/reporting/1-cQGem0O4d7QYMWrdzqmVqw7Par784Hi "||
                                 "https://datastudio.google.com/reporting/1jgIX4d9nLWVEzpexWeezhJVi2riPvwX5 "||
                                 "https://datastudio.google.com/reporting/1s08OCb13Q8WAz59SLYqFQv86EQCRnfhe "||
                    "Sync: Every minute 15 of each hour between 5:00AM AND 19:00PM UTC+2");

MERGE `{{params.bq_project}}.datastudio.pandora_monitoring` M -- pm-prod-matrix.datastudio.pandora_monitoring
USING (
      WITH event_data AS (
           SELECT ev.event_id                                                                                                       AS event_id,
                  ANY_VALUE(ev.partner_name)                                                                                        AS Partenaire,
                  ANY_VALUE(ev.context_name)                                                                                        AS Contexte,
                  ANY_VALUE(DATE(ev.event_date))                                                                                    AS Date,
                  ANY_VALUE(CASE WHEN ev.response ='NEW' AND ev_cancel.pandora_event_id IS NULL THEN 1 ELSE 0 END)                  AS Nouveaux,
                  ANY_VALUE(CASE WHEN ev.response ='NEW' AND ev_cancel.pandora_event_id IS NOT NULL THEN 1 ELSE 0 end)              AS Nouveaux_ann,
                  ANY_VALUE(CASE WHEN ev.response in ('DPL', 'DBL') AND ev_cancel.pandora_event_id IS NULL THEN 1 ELSE 0 end)       AS Duplications,
                  ANY_VALUE(CASE WHEN ev.response in ('DPL', 'DBL') AND ev_cancel.pandora_event_id IS NOT NULL  THEN 1 ELSE 0 end)  AS Duplications_ann,
                  ANY_VALUE(CASE WHEN ev.response in ('REO', 'REA') AND ev_cancel.pandora_event_id IS NULL THEN 1 ELSE 0 end)       AS Reactivations,
                  ANY_VALUE(CASE WHEN ev.response in ('REO', 'REA') AND ev_cancel.pandora_event_id IS NOT NULL  THEN 1 ELSE 0 end)  AS Reactivations_ann,
                  ANY_VALUE(CASE WHEN ev.response in ('BLK', 'POL') THEN 1 ELSE 0 end)                                              AS Permanent_Oppostion_List,
                  ANY_VALUE(CASE WHEN ev.response = 'ERR' THEN 1 ELSE 0 end)                                                        AS Erreurs,
                  ANY_VALUE(CASE WHEN ev.response ='EXC' THEN 1 ELSE 0 end)                                                         AS Exceptions,
                  ------------ CANCEL -----------------
                  ANY_VALUE(CASE ev_cancel.pandora_event_id IS NOT NULL WHEN true THEN 1 ELSE 0 end )                               AS Acquisition_ann,
                  ------------ PMC  -------------------
                  ANY_VALUE(CASE WHEN ev_pmc.response ='NEW' THEN 1 ELSE 0 end)                                                     AS Nouveaux_PMC,
                  ANY_VALUE(CASE WHEN ev_pmc.response = 'EXI' THEN 1 ELSE 0 end)                                                    AS Existe_PMC,
                  ANY_VALUE(CASE WHEN ev_pmc.response in ('DPL', 'DBL') THEN 1 ELSE 0 end)                                          AS Duplications_PMC,
                  ANY_VALUE(CASE WHEN ev_pmc.response = 'ERR' THEN 1 ELSE 0 end)                                                    AS Erreurs_PMC,
             FROM `{{params.bq_project}}.store_pandora.monitor_event`             AS ev        -- `pm-prod-matrix.store_pandora.monitor_event`
             LEFT JOIN `{{params.bq_project}}.store_pandora.pmc_event`            AS ev_pmc    -- `pm-prod-matrix.store_pandora.pmc_event`
               ON SAFE_CAST(ev.event_id AS INT64) = ev_pmc.event_id
             LEFT JOIN `{{params.bq_project}}.store_pandora.cancel_pandora_event` AS ev_cancel -- `pm-prod-matrix.store_pandora.cancel_pandora_event`
               ON SAFE_CAST(ev.event_id AS INT64) = ev_cancel.pandora_event_id
            GROUP BY ev.event_id
      )
      SELECT data.Partenaire                    AS Partenaire,
             data.Contexte                      AS Contexte,
             data.Date                          AS Date,
             SUM(data.Nouveaux)                 AS Nouveaux,
             SUM(data.Nouveaux_ann)             AS Nouveaux_ann,
             SUM(data.Duplications)             AS Duplications,
             SUM(data.Duplications_ann)         AS Duplications_ann,
             SUM(data.Reactivations)            AS Reactivations,
             SUM(data.Reactivations_ann)        AS Reactivations_ann,
             SUM(data.Permanent_Oppostion_List) AS Permanent_Oppostion_List,
             SUM(data.Erreurs)                  AS Erreurs,
             SUM(data.Exceptions)               AS Exceptions,
             COUNT(data.event_id)               AS Total_Acquisition,
             SUM(data.Acquisition_ann)          AS Total_Acquisition_ann,
             ------------ PMC  -------------------
             SUM(data.Nouveaux_PMC)             AS Nouveaux_PMC,
             SUM(data.Existe_PMC)               AS Existe_PMC,
             SUM(data.Duplications_PMC)         AS Duplications_PMC,
             SUM(data.Erreurs_PMC)              AS Erreurs_PMC
        FROM event_data AS data
       WHERE data.Contexte IS NOT NULL
       GROUP BY Date, Partenaire, Contexte
) AS E
  ON M.Partenaire = E.Partenaire
 AND M.Contexte = E.Contexte
 AND DATE(M.Date) = DATE(E.Date)
WHEN MATCHED THEN
     UPDATE SET M.Nouveaux=E.Nouveaux,
                M.Nouveaux_ann= E.Nouveaux_ann,
                M.Duplications= E.Duplications,
                M.Duplications_ann= E.Duplications_ann,
                M.Reactivations= E.Reactivations,
                M.Reactivations_ann= E.Reactivations_ann,
                M.Permanent_Oppostion_List= E.Permanent_Oppostion_List,
                M.Erreurs= E.Erreurs,
                M.Exceptions= E.Exceptions,
                M.Total_Acquisition= E.Total_Acquisition,
                M.Total_Acquisition_ann= E.Total_Acquisition_ann,
                M.Nouveaux_PMC= E. Nouveaux_PMC,
                M.Existe_PMC= E.Existe_PMC,
                M.Duplications_PMC= E.Duplications_PMC,
                M.Erreurs_PMC= E. Erreurs_PMC
WHEN NOT MATCHED THEN
     INSERT (Partenaire, Contexte, Date, Nouveaux, Nouveaux_ann, Duplications, Duplications_ann, Reactivations, Reactivations_ann, Permanent_Oppostion_List, Erreurs, Exceptions, Total_Acquisition, Total_Acquisition_ann, Nouveaux_PMC, Existe_PMC, Duplications_PMC, Erreurs_PMC)
     VALUES (E.Partenaire, E.Contexte, CAST(E.Date AS TIMESTAMP), E.Nouveaux, E.Nouveaux_ann, E.Duplications, E.Duplications_ann, E.Reactivations, E.Reactivations_ann, E.Permanent_Oppostion_List, E.Erreurs, E.Exceptions, E.Total_Acquisition, E.Total_Acquisition_ann, E. Nouveaux_PMC, E.Existe_PMC, E.Duplications_PMC, E. Erreurs_PMC);
