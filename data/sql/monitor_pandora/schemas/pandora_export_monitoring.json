[{"mode": "NULLABLE", "name": "Partenaire", "type": "STRING"}, {"mode": "NULLABLE", "name": "Contexte", "type": "STRING"}, {"mode": "NULLABLE", "name": "Date", "type": "Date"}, {"mode": "NULLABLE", "name": "Nouveaux", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "Nouveaux_ann", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "Duplications", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "Duplications_ann", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "Reactivations", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "Reactivations_ann", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "Permanent_Oppostion_List", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "<PERSON><PERSON><PERSON>", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "Exceptions", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "Total_Acquisition", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "Total_Acquisition_ann", "type": "INTEGER"}]