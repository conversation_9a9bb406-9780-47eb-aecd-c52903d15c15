[{"mode": "NULLABLE", "name": "id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "name", "type": "STRING"}, {"mode": "NULLABLE", "name": "create_date", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "update_date", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "require_validation", "type": "BOOLEAN"}, {"mode": "NULLABLE", "name": "kickbox_validation", "type": "BOOLEAN"}, {"mode": "NULLABLE", "name": "has_pmc", "type": "BOOLEAN"}, {"mode": "NULLABLE", "name": "has_welcome", "type": "BOOLEAN"}]