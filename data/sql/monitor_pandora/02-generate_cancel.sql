-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

SELECT CAST(create_date AS TIMESTAMP) AS event_date,
       event_id AS pandora_event_id,
       email_event_id,
       has_deleted_pmc
  FROM `{{params.bq_project}}.import.pandora_cancelled_{{execution_date.strftime("%Y_%m_%d_%H%M")}}` -- pm-prod-matrix.import.pandora_cancelled_2021_10_20_0915
 WHERE event_id NOT IN (
       SELECT pandora_event_id
         FROM `{{params.bq_project}}.store_pandora.cancelled` -- pm-prod-matrix.store_pandora.cancelled
);
