-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE OR REPLACE TABLE `temp.sub_shopping_part_consent_profiles_{{ params.brand_trigram }}`
AS  SELECT
    pec.profile_master_id,
    pec.brand_id,
    ANY_VALUE(pec.email_consent_id) AS email_consent_id
  FROM `store_matrix_email.profiles_email_consents` AS pec
  JOIN `refined_data.email_base` AS eb
    ON eb.brand_id = {{ params.brand_id }}
        AND pec.email_consent_id = eb.consent_id
            AND eb.consent_type = 'part'
                AND eb.consent_public_ref like '%shopping_part'
  WHERE pec.consent_status = 'sub'
  GROUP BY 1,2
  LIMIT {{ params.limit }}
;