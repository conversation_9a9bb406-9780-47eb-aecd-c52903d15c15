-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

WITH profiles_to_unsub AS (
    SELECT
        pmi.id AS profile_master_id,
        ANY_VALUE(pmi.email_sha256) AS email_hash,
        STRING_AGG(distinct CAST(ec.id AS STRING) , ',')  AS consent_ids

    FROM `store_matrix_email.profiles_email_consents`   AS pec
    JOIN `store_matrix_email.profile_master_id`         AS pmi
        ON pec.profile_master_id = pmi.id
    JOIN `store_karinto.email_consent`                  AS ec
        ON ec.id = pec.email_consent_id
            AND ec.public_ref IN ({{ params.deprecated_consents }})
    WHERE pec.consent_status = 'sub'
    GROUP BY 1
), format_for_email_event AS (
    -- we create the table for the email event queue
    SELECT
    CURRENT_TIMESTAMP() AS event_date,
    'unsub' AS event_type,
    '\\N' AS email,
    email_hash,
    '{'
        || '"ev": 100,'
        || '"app": "mozart",'
        || '"process": "{{ dag.dag_id }}",'
        || '"source": "{{ params.source }}",'
        || '"medium": "{{ params.medium }}",'
        || '"profile_master_id":"'|| profile_master_id  ||'",'
        || '"consent_ids":['|| consent_ids  ||']'
        || '}' AS payload
    FROM profiles_to_unsub
)
SELECT *
FROM format_for_email_event
LIMIT {{ params.limit }}


