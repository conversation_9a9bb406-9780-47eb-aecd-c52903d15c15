-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
CREATE OR REPLACE TABLE `temp.purge_shopping_part_consent_profiles_events_{{ next_execution_date.strftime("%Y%m%d%H") }}` (
    event_date    TIMESTAMP      NOT NULL    OPTIONS(description="Date of event"),
    event_type    STRING         NOT NULL    OPTIONS(description="type of event"),
    email         STRING                     OPTIONS(description="email"),
    email_hash    STRING                     OPTIONS(description="email hash"),
    payload       STRING                     OPTIONS(description="payload of event")
    ) OPTIONS(
                 expiration_timestamp=NULL,
                 description="Recently generated email_events for profiles to clear unsub from shopping_part and to sub to part.\n"
                 || "\n\n"
                 || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);

INSERT INTO `temp.purge_shopping_part_consent_profiles_events_{{ next_execution_date.strftime("%Y%m%d%H") }}`
WITH profiles_to_unsub AS (
    SELECT
        pmi.id AS profile_master_id,
        ANY_VALUE(pmi.email_md5) AS email_hash,
        STRING_AGG(distinct CAST(email_consent_id AS STRING) , ',') AS consent_ids

    FROM `temp.sub_shopping_part_consent_profiles_*` AS src
    JOIN `store_matrix_email.profile_master_id` AS pmi
        ON pmi.id = src.profile_master_id
    GROUP BY 1
)
-- we create the table for the email event queue
SELECT
    CURRENT_TIMESTAMP() AS event_date,
    'unsub' AS event_type,
    '\\N' AS email,
    email_hash,
    '{'
        || '"ev": 100,'
        || '"app": "mozart",'
        || '"process": "{{ dag.dag_id }}",'
        || '"source": "airflow",'
        || '"medium": "MATRIX-380: purge shopping_part consent",'
        || '"profile_master_id":"'|| profile_master_id  ||'",'
        || '"consent_ids":['|| consent_ids  ||']'
        || '}'
    AS payload
FROM profiles_to_unsub
;


-- sub to part consent
INSERT INTO `temp.purge_shopping_part_consent_profiles_events_{{ next_execution_date.strftime("%Y%m%d%H") }}`
WITH consent_part AS (
      SELECT
      brand_id,
      consent_id,
      consent_public_ref
  FROM `refined_data.email_base` AS eb
  WHERE eb.consent_is_active = True
    AND eb.consent_type = 'part'
    -- only prisma and cerise brand
    AND eb.brand_trigram NOT IN ('PMS', 'PRM', 'PMC', 'OPA')
    -- exclude shopping_part consent
    AND eb.consent_public_ref not like '%shopping_part'
    AND eb.consent_public_ref like '%_part'
), profiles_to_sub AS (
    SELECT
        pmi.id AS profile_master_id,
        ANY_VALUE(pmi.email_sha256) AS email_hash,
        ANY_VALUE(pmi.email) AS email,
        STRING_AGG(distinct CAST(cp.consent_id AS STRING) , ',')  AS consent_ids

    FROM `temp.sub_shopping_part_consent_profiles_*` AS src
    JOIN `store_matrix_email.profile_master_id` AS pmi
        ON pmi.id = src.profile_master_id
    JOIN consent_part AS cp
        ON cp.brand_id = src.brand_id
    LEFT JOIN `store_matrix_email.profiles_email_consents` AS pec
        ON src.profile_master_id = pec.profile_master_id
            AND cp.consent_id = pec.email_consent_id
                AND pec.consent_status = 'sub'
    WHERE pec.email_consent_id IS NULL
    GROUP BY 1
)
SELECT
    CURRENT_TIMESTAMP() AS event_date,
    'sub' AS event_type,
    email AS email,
    email_hash,
    '{'
        || '"ev": 100,'
        || '"app": "mozart",'
        || '"process": "{{ dag.dag_id }}",'
        || '"source": "airflow",'
        || '"medium": "MATRIX-380: purge shopping_part consent",'
        || '"consent_ids":['|| consent_ids  ||']'
        || '}' AS payload
FROM profiles_to_sub
;


