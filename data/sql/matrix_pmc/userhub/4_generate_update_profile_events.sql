-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
DECLARE pmc_brand_id INT64;

SET pmc_brand_id = (
   SELECT id AS pmc_brand_id FROM `{{params.bq_project}}.store_karinto.brand` WHERE trigram = 'PMC'
);

CREATE TABLE IF NOT EXISTS `{{params.bq_project}}.export_matrix_email.pmc_profile` (
    email           STRING      OPTIONS(description="Email"),
    email_sha256    STRING      OPTIONS(description="Email SHA256"),
    uuid            STRING      OPTIONS(description="PMC User id"),
    civility        STRING      OPTIONS(description="Civility F/M"),
    firstname       STRING      OPTIONS(description="Firstname"),
    lastname        STRING      OPTIONS(description="Lastname"),
    birthdate       DATE        OPTIONS(description="Birthdate"),
    address1        STRING      OPTIONS(description="Adress"),
    address2        STRING      OPTIONS(description="Complementary address"),
    city            STRING      OPTIONS(description="City"),
    postal_code     STRING      OPTIONS(description="Postal Code"),
    country         STRING      OPTIONS(description="Country"),
    fingerprint     INTEGER     OPTIONS(description="Row fingerprint to compare with previous version")
) OPTIONS (description="All PMC profiles to update.\n\n"
                 || "DAG: {{ dag.dag_id }}, TASK: {{ task.task_id }} \n"
                 || "Sync: daily");

-- for the first run, _previous table does not exists. we must create it.
CREATE TABLE IF NOT EXISTS `{{params.bq_project}}.export_matrix_email.pmc_profile_previous`
LIKE `{{params.bq_project}}.export_matrix_email.pmc_profile`;

CREATE TABLE IF NOT EXISTS `{{params.bq_project}}.export_matrix_email.pmc_update_profile_events` (
    email_hash          STRING      NOT NULL    OPTIONS(description="Email sha256"),
    create_date         TIMESTAMP   NOT NULL    OPTIONS(description="creation date")
    ) OPTIONS(
                 expiration_timestamp=NULL,
                 description="Recently generated update_profile email_event for PMC profiles.\n"
                 || "\n\n"
                 || "PK=event_id,uuid."
                 || "\n\n"
                 || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);

-- generate actual profiles state (personal data with row fingerprint) for today
TRUNCATE TABLE `{{params.bq_project}}.export_matrix_email.pmc_profile`;
INSERT INTO `{{params.bq_project}}.export_matrix_email.pmc_profile`
WITH pmc_profile AS (
    SELECT
        pmi.email,
        pmi.email_sha256,
        p.uuid,
        CASE p.profile_data.gender
                WHEN 'W' THEN 'F'
                WHEN 'M' THEN 'M'
        ELSE NULL END AS civility,
        LEFT(p.profile_data.firstname, 50) AS firstname,
        LEFT(p.profile_data.lastname, 50) AS lastname,
        p.profile_data.birthdate AS birthdate,
        LEFT(p.profile_data.address, 100) AS address1,
        LEFT(p.profile_data.complementary_address, 100) AS address2,
        LEFT(p.profile_data.city, 200) AS city,
        LEFT(p.profile_data.zipcode, 200) AS postal_code,
        LEFT(p.profile_data.country, 90) AS country
    FROM `{{params.bq_userhub_project}}.refined_data.pmc_profile_live` AS p
    JOIN `{{params.bq_project}}.store_matrix_pmc.profile_master_id` AS pmi ON p.uuid = pmi.uuid
    WHERE pmi.id IS NOT NULL
    {% if params.full_export|lower != 'true' %}
        -- incremental
        AND DATE(p.update_date) >= DATE_SUB(DATE('{{ next_ds }}'), INTERVAL {{params.interval_update}})
    {% endif %}
        AND NOT EXISTS (
            -- skip already handled update_profiles events
            SELECT 1 FROM `{{params.bq_project}}.export_matrix_email.pmc_update_profile_events` AS a
            WHERE a.email_hash = pmi.email_sha256
                AND DATE(a.create_date) = DATE(p.update_date)
        )
)
SELECT
    email,
    email_sha256,
    uuid,
    civility,
    firstname,
    lastname,
    birthdate,
    address1,
    address2,
    city,
    postal_code,
    country,
    -- fingerprint of each row
    FARM_FINGERPRINT(CONCAT(
        CAST(email AS STRING),
        IFNULL(CAST(civility AS STRING), ''),
        IFNULL(CAST(firstname AS STRING), ''),
        IFNULL(CAST(lastname AS STRING), ''),
        IFNULL(CAST(birthdate AS STRING), ''),
        IFNULL(CAST(address1 AS STRING), ''),
        IFNULL(CAST(address2 AS STRING), ''),
        IFNULL(CAST(city AS STRING), ''),
        IFNULL(CAST(postal_code AS STRING), ''),
        IFNULL(CAST(country AS STRING), '')
    )) as fingerprint
FROM pmc_profile;

-- generate previous profiles state (personal data with row fingerprint)
TRUNCATE TABLE `{{params.bq_project}}.export_matrix_email.pmc_profile_previous`;
INSERT INTO `{{params.bq_project}}.export_matrix_email.pmc_profile_previous`
WITH pmc_profile AS (
    SELECT
        pmi.email,
        pmi.email_sha256,
        pmcpmi.uuid,
        p.gender AS civility,
        p.firstname,
        p.lastname,
        p.birthdate,
        p.address_line1 AS address1,
        p.address_line2 AS address2,
        p.city,
        p.postal_code,
        p.country
    FROM `{{params.bq_project}}.store_matrix_email.profile` AS p
    JOIN `{{params.bq_project}}.store_matrix_email.profile_master_id` AS pmi ON p.profile_master_id = pmi.id
    JOIN `{{params.bq_project}}.store_matrix_pmc.profile_master_id` AS pmcpmi ON pmi.email_sha256 = pmcpmi.email_sha256
    WHERE p.brand_id = pmc_brand_id
        -- optimize by selecting profiles which have been recently updated only
        AND EXISTS (
            SELECT 1 FROM `{{params.bq_project}}.export_matrix_email.pmc_profile` AS a
            WHERE a.uuid = pmcpmi.uuid
        )
)
SELECT
    email,
    email_sha256,
    uuid,
    civility,
    firstname,
    lastname,
    birthdate,
    address1,
    address2,
    city,
    postal_code,
    country,
    -- fingerprint of each row
    FARM_FINGERPRINT(CONCAT(
        CAST(email AS STRING),
        IFNULL(CAST(civility AS STRING), ''),
        IFNULL(CAST(firstname AS STRING), ''),
        IFNULL(CAST(lastname AS STRING), ''),
        IFNULL(CAST(birthdate AS STRING), ''),
        IFNULL(CAST(address1 AS STRING), ''),
        IFNULL(CAST(address2 AS STRING), ''),
        IFNULL(CAST(city AS STRING), ''),
        IFNULL(CAST(postal_code AS STRING), ''),
        IFNULL(CAST(country AS STRING), '')
    )) as fingerprint
FROM pmc_profile;


-- ------------------------------------------------------------------------------------------------
-- Generate update_profile event for changed personal info updated profiles
-- ------------------------------------------------------------------------------------------------
INSERT INTO `{{params.bq_project}}.export_matrix_email.pmc_profile_events`
(create_date, type, email, email_hash, payload)
WITH pmc_to_update AS (
    SELECT DISTINCT
        p.*,
        ec.id as consent_id,
        p.fingerprint AS current_fingerprint,
        o.fingerprint AS previous_fingerprint
    FROM `{{params.bq_project}}.export_matrix_email.pmc_profile` AS p
    JOIN `{{params.bq_project}}.store_matrix_pmc.profile_master_id` AS pmi ON p.uuid = pmi.uuid
    JOIN `{{params.bq_project}}.store_karinto.email_consent` AS ec ON ec.public_ref = 'prisma_connect_crm'
    LEFT JOIN `{{params.bq_project}}.export_matrix_email.pmc_profile_previous` AS o ON p.uuid = o.uuid
)
SELECT
    CURRENT_TIMESTAMP() AS create_date,
    'update_profile' AS type,
    p.email email,
    p.email_sha256 AS email_hash,
    JSON_EXTRACT(CONCAT('{'
    , '"profile":{'
        , '"civility":'   || (CASE WHEN p.civility  IS NULL THEN '""' ELSE TO_JSON_STRING(p.civility)  END) || ','
        , '"lastname":'   || (CASE WHEN p.lastname  IS NULL THEN '""' ELSE TO_JSON_STRING(p.lastname)  END) || ','
        , '"firstname":'  || (CASE WHEN p.firstname IS NULL THEN '""' ELSE TO_JSON_STRING(p.firstname) END) || ','
        , '"birthdate":'  || (CASE WHEN p.birthdate IS NULL THEN 'null' ELSE TO_JSON_STRING(FORMAT_DATE('%Y-%m-%d', p.birthdate)) END) || ','
        , '"address":{'
            , '"address1":'     || (CASE WHEN p.address1    IS NULL THEN '""' ELSE TO_JSON_STRING(p.address1)    END) || ','
            , '"address2":'     || (CASE WHEN p.address2    IS NULL THEN '""' ELSE TO_JSON_STRING(p.address2)    END) || ','
            , '"city":'         || (CASE WHEN p.city        IS NULL THEN '""' ELSE TO_JSON_STRING(p.city)        END) || ','
            , '"postal_code": ' || (CASE WHEN p.postal_code IS NULL THEN '""' ELSE TO_JSON_STRING(p.postal_code) END) || ','
            , '"country": '     || (CASE WHEN p.country     IS NULL THEN '""' ELSE TO_JSON_STRING(p.country)     END)
        , '}'
    , '},'
    , '"ev": 100,'
    , '"source": "pmc",'
    , '"app": "mozart",'
    , '"process": "{{ dag.dag_id }}",'
    , '"medium": "update pmc profile data",'
    , '"consent_ids": [' || CAST(consent_id AS STRING) || ']'
    , '}'), '$') AS payload
FROM pmc_to_update AS p
WHERE current_fingerprint != previous_fingerprint
    OR previous_fingerprint IS NULL
;