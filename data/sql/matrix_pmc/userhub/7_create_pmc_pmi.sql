-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

INSERT INTO matrix__pmc.profile_master_id (uuid, email, email_md5, email_sha256, create_date, update_date, web_id)
SELECT distinct uuid::uuid        AS uuid,
       email,
       email_md5,
       email_sha256,
       CURRENT_TIMESTAMP AS create_date,
       CURRENT_TIMESTAMP AS update_date,
       web_id            AS web_id
FROM {{params.import_table}} AS import 
ON CONFLICT(uuid)
DO UPDATE
SET email=excluded.email,
    email_md5=excluded.email_md5,
    email_sha256=excluded.email_sha256,
    update_date=CURRENT_TIMESTAMP
;