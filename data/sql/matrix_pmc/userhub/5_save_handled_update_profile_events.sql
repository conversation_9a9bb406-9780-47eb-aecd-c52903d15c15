-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- delete history older than 3 days
DELETE FROM `{{params.bq_project}}.export_matrix_email.pmc_update_profile_events`
WHERE DATE(create_date) < DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY);

-- log handled events (useful to not re-generate the same handled events)
MERGE `{{params.bq_project}}.export_matrix_email.pmc_update_profile_events` dest
USING (
    SELECT DISTINCT email_hash, create_date
    FROM `{{params.bq_project}}.export_matrix_email.pmc_profile_events`
    WHERE type = 'update_profile'
        AND DATE(create_date) >= DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
) source
ON DATE(dest.create_date) = DATE(source.create_date)
    AND source.email_hash = dest.email_hash
WHEN MATCHED THEN
    UPDATE
        SET create_date = source.create_date
WHEN NOT MATCHED THEN
  INSERT (email_hash, create_date)
  VALUES (source.email_hash, source.create_date)
;