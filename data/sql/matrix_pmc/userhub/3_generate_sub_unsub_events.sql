-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- ------------------------------------------------------------------------------------------------
-- Generate sub events for each new profile
-- ------------------------------------------------------------------------------------------------
INSERT INTO `{{params.bq_project}}.export_matrix_email.pmc_profile_events`
(create_date, type, email, email_hash, payload)
WITH pmc_to_sub AS (
    SELECT
        p.uuid,
        CASE p.profile_data.gender
                WHEN 'W' THEN 'F'
                WHEN 'M' THEN 'M'
        ELSE NULL END AS civility,
        LEFT(TRIM(p.profile_data.firstname), 50) AS firstname,
        LEFT(TRIM(p.profile_data.lastname), 50) AS lastname,
        p.profile_data.birthdate AS birthdate,
        LEFT(TRIM(p.profile_data.address), 100) AS address1,
        LEFT(TRIM(p.profile_data.complementary_address), 100) AS address2,
        LEFT(TRIM(p.profile_data.city), 200) AS city,
        LEFT(TRIM(p.profile_data.zipcode), 200) AS postal_code,
        LEFT(TRIM(p.profile_data.country), 90) AS country,
        np.email,
        np.email_sha256,
        ec.id as consent_id
    FROM `{{params.bq_project}}.export_matrix_pmc.new_pmc_profile` AS np
    JOIN `{{params.bq_userhub_project}}.refined_data.pmc_profile_live` AS p
        ON np.uuid = p.uuid
            AND np.email = p.profile_data.email
    JOIN `{{params.bq_project}}.store_karinto.email_consent` AS ec ON ec.public_ref = 'prisma_connect_crm'
    LEFT JOIN `{{params.bq_project}}.store_partner.splio_lop_snapshot` AS splio_lop
        ON splio_lop.email = np.email
            AND splio_lop.univers_name = 'prisma_connect'
    WHERE splio_lop.email IS NULL
)
SELECT
    CURRENT_TIMESTAMP() AS create_date,
    'sub' AS type,
    email,
    email_sha256 AS email_hash,
    JSON_EXTRACT(CONCAT('{'
    , '"profile":{'
        , '"civility":'   || (CASE WHEN p.civility  IS NULL THEN 'null' ELSE TO_JSON_STRING(p.civility)  END) || ','
        , '"lastname":'   || (CASE WHEN p.lastname  IS NULL THEN 'null' ELSE TO_JSON_STRING(p.lastname)  END) || ','
        , '"firstname":'  || (CASE WHEN p.firstname IS NULL THEN 'null' ELSE TO_JSON_STRING(p.firstname) END) || ','
        , '"birthdate":'  || (CASE WHEN p.birthdate IS NULL THEN 'null' ELSE TO_JSON_STRING(FORMAT_DATE('%Y-%m-%d', p.birthdate)) END) || ','
        , '"address":{'
            , '"address1":'     || (CASE WHEN p.address1    IS NULL THEN 'null' ELSE TO_JSON_STRING(p.address1)    END) || ','
            , '"address2":'     || (CASE WHEN p.address2    IS NULL THEN 'null' ELSE TO_JSON_STRING(p.address2)    END) || ','
            , '"city":'         || (CASE WHEN p.city        IS NULL THEN 'null' ELSE TO_JSON_STRING(p.city)        END) || ','
            , '"postal_code": ' || (CASE WHEN p.postal_code IS NULL THEN 'null' ELSE TO_JSON_STRING(p.postal_code) END) || ','
            , '"country": '     || (CASE WHEN p.country     IS NULL THEN 'null' ELSE TO_JSON_STRING(p.country)     END)
        , '}'
    , '},'
    , '"ev": 100,'
    , '"source": "pmc",'
    , '"app": "mozart",'
    , '"process": "{{ dag.dag_id }}",'
    , '"medium": "sub new pmc profile",'
    , '"consent_ids": [' || CAST(consent_id AS STRING) || ']'
    , '}'), '$') AS payload
FROM pmc_to_sub AS p
;

-- ------------------------------------------------------------------------------------------------
-- Generate unsub events for each deleted profile
-- ------------------------------------------------------------------------------------------------
INSERT INTO `{{params.bq_project}}.export_matrix_email.pmc_profile_events`
(create_date, type, email, email_hash, payload)
WITH pmc_to_unsub AS (
    SELECT
        del.uuid,
        pmi.email,
        pmi.email_sha256,
        ARRAY_AGG(DISTINCT pec.email_consent_id) AS consent_ids
    FROM `{{params.bq_project}}.export_matrix_pmc.deleted_pmc_profile` AS del
    JOIN `{{params.bq_project}}.store_matrix_pmc.profile_master_id` AS pmc_pmi ON pmc_pmi.uuid = del.uuid
    JOIN `{{params.bq_project}}.store_matrix_email.profile_master_id` AS pmi ON pmi.email_sha256 = pmc_pmi.email_sha256
	JOIN `{{params.bq_project}}.store_matrix_email.profiles_email_consents` AS pec ON pec.profile_master_id = pmi.id
	WHERE pec.email_consent_id IN (
   		SELECT eb.consent_id
		FROM `{{params.bq_project}}.refined_data.email_base` as eb
		WHERE
		    -- skip GAL consents (we preserve subs to GALA consents)
	        eb.brand_trigram != 'GAL'
	)
  	    AND pec.consent_status = 'sub'
	GROUP BY 1,2,3
)
SELECT
    CURRENT_TIMESTAMP() AS create_date,
    'unsub' AS type,
    LOWER(TRIM(p.email)) AS email,
    TO_HEX(SHA256(p.email)) AS email_hash,
    JSON_EXTRACT(CONCAT('{'
        , '"ev": 100,'
        , '"source": "pmc",'
        , '"app": "mozart",'
        , '"process": "{{ dag.dag_id }}",'
        , '"medium": "unsub deleted pmc profile",'
        , '"consent_ids":' , TO_JSON_STRING(consent_ids)
        , '}'), '$'
    ) AS payload
FROM pmc_to_unsub AS p;