-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}


/**
Source table:
matrix__email_queue.tunnel_event
  - email_hash : varchar(64)
  - profile_master_id : bigint
  - create_date : datetime
  - tunnel_name : varchar(25) / "welcome"
  - type : enter | exit
---
Destination Table:
matrix__email_splio.datahub_005
- universe_id : integer
- email : varchar(120)
- update_date : datetime
- tunnel_exclusion : varchar(500)

*/


------------------------------------------------------------------------------------------
-- INIT THE TUNNEL QUEUE TABLE
------------------------------------------------------------------------------------------

/* --

    INSERT INTO matrix__email_queue.tunnel_event
    (email_hash, profile_master_id, create_date, tunnel_name, type)
    SELECT
        pmi.email_sha256,
        pmi.id AS profile_master_id,
        now() AS update_date,
        'welcome' AS tunnel_name,
    	'exit'::matrix__email_queue.tunnel_event_type
    FROM matrix__email.profiles_email_consents AS pec
    JOIN matrix__email.profile_master_id AS pmi ON pmi.id = pec.profile_master_id
    JOIN matrix__email_splio.splio_list AS sl ON pec.email_consent_id = sl.email_consent_id
    WHERE pec.email_consent_id IN (
        SELECT id FROM karinto.email_consent WHERE public_ref like 'prisma_connect_crm%'
    )
    AND pec.status = 'sub'
    GROUP BY pmi.email, pmi.id, sl.universe_id
    --PREPROD: INSERT 0 3906677
    --Query returned successfully in 2 min 5 secs.

*/


------------------------------------------------------------------------------------------
-- Process :
------------------------------------------------------------------------------------------
--- create tmp table to get events (from queue + ref table:matrix__email_splio.datahub_005 ) grouped by email , pmi , universe
--- prepare tunnel_exclusion by pmi et univers
--- ** DEAL WITH EXIT Events only **
------------------------------------------------------------------------------------------

DROP TABLE IF EXISTS matrix__email_tmp.datahub_005;
CREATE TABLE matrix__email_tmp.datahub_005(
    email varchar(120) ,
    profile_master_id bigint ,
    update_date timestamp with time zone DEFAULT now(),
    tunnel_exclusion varchar(500),
    universe_id integer
);
ALTER TABLE matrix__email_tmp.datahub_005 OWNER TO matrix_email;

WITH previous_data AS (
    -- get event from last datahub version
    -- PI: pmi can be null (exp: change email event)
    SELECT
        ref.email,
        pmi.id AS profile_master_id,
        ref.update_date,
        tunnel_name.name AS tunnel_name,
        tunnel_name.nr AS tunnel_order,
        universe_id
    FROM matrix__email_splio.datahub_005 AS ref
    LEFT JOIN matrix__email.profile_master_id AS pmi ON pmi.email = ref.email,
    -- unnest keeping the order in which the tunnel_exclusion has
    unnest(string_to_array(trim('|' from ref.tunnel_exclusion), '|')) WITH ORDINALITY AS tunnel_name(name, nr)

), new_data AS (
    -- get recents events from matrix__email_queue.tunnel_event
    SELECT
        pmi.email,
        pmi.id AS profile_master_id,
        MAX(queue.create_date) AS update_date,
        queue.tunnel_name AS tunnel_name,
        ROW_NUMBER() OVER (PARTITION BY queue.tunnel_name ORDER BY MAX(queue.create_date) DESC) AS tunnel_order,
        sl.universe_id
    FROM
    matrix__email_queue.tunnel_event AS queue
    JOIN matrix__email.profile_master_id AS pmi ON pmi.id = queue.profile_master_id
    JOIN matrix__email.profiles_email_consents AS pec ON pec.profile_master_id = pmi.id
    JOIN matrix__email_splio.profiles_universes  AS pu ON pmi.id = pu.email_profile_master_id
    JOIN matrix__email_splio.splio_list AS sl ON pec.email_consent_id = sl.email_consent_id AND sl.universe_id = pu.universe_id
    WHERE pec.email_consent_id IN (
        SELECT id FROM karinto.email_consent WHERE public_ref like 'prisma_connect_crm%'
    )
    AND pec.status = 'sub'
    AND queue.type = 'exit'
    --------
    -- AND queue.create_date > NOW() - interval '3 days'  @toAdd get only recent events after full export to splio
    --------
    GROUP BY pmi.email, pmi.id, sl.universe_id, queue.tunnel_name
), merged_data AS (
    SELECT * FROM previous_data
    UNION ALL
    SELECT * FROM new_data
), data AS (
    SELECT
        MAX(email) AS email,
        profile_master_id,
        MAX(update_date) AS update_date,
        tunnel_name,
        MAX(tunnel_order) AS tunnel_order,
        universe_id
    FROM merged_data
    GROUP BY profile_master_id, universe_id, tunnel_name, universe_id
    ORDER BY tunnel_name DESC
)
INSERT INTO matrix__email_tmp.datahub_005 AS tmp
    (email, profile_master_id, update_date, tunnel_exclusion, universe_id)
SELECT
    MAX(email) AS email,
    profile_master_id,
    MAX(update_date) AS update_date,
    -- string_agg order by update_date to have rescent tag first
    -- add "|last" to close the string agg (and not remove the last tag from "regex_replace"
    -- substring to cut the field, as it's a maximum varchar(250)
    -- use regex_replace to remove "cut" tags, or "|last"
    -- concat "|" at the beginning and the end of the resulting string
    -- example : tags = [ toto ] -- string_agg -> "toto|last" -- regex_replace --> "toto" -> "|toto|"
    -- example : tags = [ toto, hello, world ] -- string_agg -> "toto|hello|world|last" -- cut (13) --> "toto|hello|wo" -- regex_replace --> "toto|hello" -> "|toto|hello|"
    '|' ||
    regexp_replace(
        substring(
            STRING_AGG(tunnel_name, '|' ORDER BY update_date DESC, tunnel_order ASC) || '|last'
            FOR 498
        ),
        '[|][^|]*$', ''
    ) || '|' AS tunnel_exclusion,
    universe_id
FROM data
GROUP BY profile_master_id, universe_id
;
------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------
--- Build new datahub_005 table
------------------------------------------------------------------------------------------
TRUNCATE TABLE matrix__email_splio.datahub_005;

INSERT INTO matrix__email_splio.datahub_005
    (universe_id, email, update_date, tunnel_exclusion)
SELECT
    universe_id,
    email,
    update_date,
    tunnel_exclusion
FROM matrix__email_tmp.datahub_005
WHERE profile_master_id IS NOT NULL
;
