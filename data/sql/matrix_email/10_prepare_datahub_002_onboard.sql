-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

--
-- COMMENT to do
-- 2/ backup datahub_002_onboard_pmc data to previous table
-- 3/ create datahub_002_onboard_pmc with full data
-- 4/ compute difference during export to update splio universe
--
-----------------------------------------------------------------------------------------------
-- 1/ create store table for first time
-- DROP TABLE IF EXISTS `store_partner.splio_profiles_universes`;
CREATE TABLE IF NOT EXISTS `store_partner.splio_profiles_universes` (
    profile_master_id   INTEGER     OPTIONS(description="Email profile_master_id"),
    universe_id         INTEGER     OPTIONS(description="Id universe"),
    universe_type       STRING      OPTIONS(description="Splio universe type"),
    universe_name       STRING      OPTIONS(description="Splio universe name"),
    first_sub_date      TIMESTAMP   OPTIONS(description="Fist universe sub date")
)
OPTIONS(description="Email pmi universes list.\n\n"
                 || "DAG: {{ dag.dag_id }}, TASK: {{ task.task_id }} \n"
                 || "Sync: daily update");

TRUNCATE TABLE `store_partner.splio_profiles_universes`;

INSERT INTO `store_partner.splio_profiles_universes`
    (profile_master_id, universe_id, universe_type, universe_name, first_sub_date)
SELECT
    p.profile_master_id,
    sl.universe_id,
    ANY_VALUE(univ.type) AS universe_type,
    ANY_VALUE(univ.name) AS universe_name,
    MIN(p.create_date) AS first_sub_date
FROM `store_matrix_email.profiles_email_consents` AS p
JOIN `refined_data.email_base` AS eb
    ON p.email_consent_id = eb.consent_id
    AND eb.consent_is_active =TRUE
JOIN `store_karinto.splio_list` AS sl
    ON sl.email_consent_id = p.email_consent_id
JOIN `store_karinto.universe` AS univ ON univ.id = sl.universe_id
WHERE p.consent_status = 'sub'
GROUP BY 1, 2
;
-- This statement added 46,349,177 rows to splio_profiles_universes.
-- 9.38 GB, 18 sec

-- ------------------------------------------------------------------------------------------------
-- Compute between email and pmc profiles
-- ------------------------------------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS `store_matrix_pmc.email_and_pmc_join` (
    email_pmi                       INTEGER     OPTIONS(description="Email profile_master_id"),
    pmc_pmi                         INTEGER     OPTIONS(description="PMC profile_master_id"),
    email                           STRING      OPTIONS(description="Full email"),
    pmc_creation_date               TIMESTAMP   OPTIONS(description="Pmc profile create date"),
    pmc_last_activity_date          TIMESTAMP   OPTIONS(description="Pmc profile last activity date"),
    pmc_signup_service              STRING      OPTIONS(description="Pmc profile signup service"),
    pmc_last_login_service          STRING      OPTIONS(description="Pmc profile last login date")
)
OPTIONS(description="Compute between email and pmc profiles.\n\n"
                 || "DAG: {{ dag.dag_id }}, TASK: {{ task.task_id }} \n"
                 || "Sync: daily update");

TRUNCATE TABLE `store_matrix_pmc.email_and_pmc_join`;

INSERT INTO `store_matrix_pmc.email_and_pmc_join`
 (email_pmi, pmc_pmi, email,
  pmc_creation_date, pmc_last_activity_date,
  pmc_signup_service, pmc_last_login_service)
WITH pmc_profiles AS (
    SELECT
        email_pmi.id                                    AS email_pmi,
        pmc_pmi.id                                      AS pmc_pmi,
        email_pmi.email                                 AS email,
        pmc_profile.create_date                         AS pmc_creation_date,
        pmc_profile.system_data.last_activity_at        AS pmc_last_activity_date,
        pmc_profile.system_data.signup_service          AS pmc_signup_service,
        pmc_profile.system_data.last_login_service      AS pmc_last_login_service
    FROM `store_matrix_email.profile_master_id` AS email_pmi
    LEFT JOIN `store_matrix_pmc.profile_master_id` AS pmc_pmi
        ON email_pmi.email_md5 = pmc_pmi.email_md5
    LEFT JOIN `{{params.bq_userhub_project}}.refined_data.pmc_profile` AS pmc_profile
        ON pmc_profile.uuid = pmc_pmi.uuid
)
SELECT
    email_pmi,
    pmc_pmi,
    email,
    pmc_creation_date,
    pmc_last_activity_date,
    pmc_signup_service,
    pmc_last_login_service
FROM pmc_profiles;
-- This statement added 40,971,171 rows to email_and_pmc_join.
-- 5.92 GB, 13 sec


-- 1/ create store table 002 for first time
CREATE TABLE IF NOT EXISTS `store.datahub_002_onboard_pmc` (
    email_pmi                       INTEGER     OPTIONS(description="Email profile_master_id"),
    pmc_pmi                         INTEGER     OPTIONS(description="PMC profile_master_id"),
    email                           STRING      OPTIONS(description="Full email"),
    pmc_creation_date               TIMESTAMP   OPTIONS(description="Pmc profile create date"),
    pmc_last_activity_date          TIMESTAMP   OPTIONS(description="Pmc profile last activity date"),
    pmc_signup_service              STRING      OPTIONS(description="Pmc profile signup service"),
    pmc_last_login_service          STRING      OPTIONS(description="Pmc profile last login service"),
    email_first_sub_date            TIMESTAMP   OPTIONS(description="First email subscreption date"),
    universe_id                     INTEGER     OPTIONS(description="Id universe"),
    fingerprint                     INTEGER     OPTIONS(description="Row fingerprint to compare with previous version")
)
OPTIONS(description="PMC fields exported to Splio Nl universes.\n\n"
                 || "DAG: {{ dag.dag_id }}, TASK: {{ task.task_id }} \n"
                 || "Sync: daily");

-- 2/ backup last data of main_consent data to previous table
DROP TABLE IF EXISTS `prepare.datahub_002_onboard_pmc_previous_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}`;
DROP TABLE IF EXISTS `prepare.datahub_002_onboard_pmc_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}`;

CREATE TABLE `prepare.datahub_002_onboard_pmc_previous_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}`
AS SELECT * FROM `store.datahub_002_onboard_pmc`;

-- 3/ create datahub_002_onboard_pmc full data table
CREATE TABLE `prepare.datahub_002_onboard_pmc_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}`(
    email_pmi                       INTEGER     OPTIONS(description="Email profile_master_id"),
    pmc_pmi                         INTEGER     OPTIONS(description="Email profile_master_id"),
    email                           STRING      OPTIONS(description="Full email"),
    pmc_creation_date               TIMESTAMP   OPTIONS(description="Pmc profile create date"),
    pmc_last_activity_date          TIMESTAMP   OPTIONS(description="Pmc profile last activity date"),
    pmc_signup_service              STRING      OPTIONS(description="Pmc profile signup service"),
    pmc_last_login_service          STRING      OPTIONS(description="Pmc profile last login date"),
    email_first_sub_date            TIMESTAMP   OPTIONS(description="First email subscreption date"),
    universe_id                     INTEGER     OPTIONS(description="Id universe"),
    fingerprint                     INTEGER     OPTIONS(description="Row fingerprint to compare with previous version")
);

-- for the first run, _previous table does not exists. we must create it.
-- get last sub main consents for all universes
-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
INSERT INTO `prepare.datahub_002_onboard_pmc_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}`
    (email_pmi, pmc_pmi, email, pmc_creation_date, pmc_last_activity_date, pmc_signup_service, pmc_last_login_service,
     email_first_sub_date, universe_id, fingerprint)
WITH pmc_and_email_infos AS (
    SELECT
      pmc_data.email_pmi AS email_pmi,
      pmc_data.pmc_pmi AS pmc_pmi,
      pmc_data.email AS email,
      pmc_data.pmc_creation_date,
      pmc_data.pmc_last_activity_date,
      pmc_data.pmc_signup_service,
      pmc_data.pmc_last_login_service,
      puniv.first_sub_date AS email_first_sub_date,
      puniv.universe_id AS universe_id
    FROM `store_matrix_pmc.email_and_pmc_join` AS pmc_data
    JOIN `store_partner.splio_profiles_universes` AS puniv
        ON puniv.profile_master_id = pmc_data.email_pmi
    WHERE puniv.universe_type IN ('nl', 'crm')
    AND puniv.universe_name != 'prisma_connect'
    --
    --AND puniv.profile_master_id = 25003214
)
SELECT
    email_pmi,
    pmc_pmi,
    email,
    pmc_creation_date,
    pmc_last_activity_date,
    pmc_signup_service,
    pmc_last_login_service,
    email_first_sub_date,
    universe_id,
      -- fingerprint of each row
    FARM_FINGERPRINT(CONCAT(
        CAST(email_pmi AS STRING),
        CAST(pmc_pmi AS STRING),
        IFNULL(CAST(email AS STRING), ''),
        IFNULL(CAST(pmc_creation_date AS STRING), ''),
        IFNULL(CAST(pmc_last_activity_date AS STRING), ''),
        IFNULL(CAST(pmc_signup_service AS STRING), ''),
        IFNULL(CAST(pmc_last_login_service AS STRING), ''),
        IFNULL(CAST(email_first_sub_date AS STRING), ''),
        CAST(universe_id AS STRING)
    )) as fingerprint
FROM pmc_and_email_infos AS p
;
-- This statement added 12,962,120 rows to datahub_002_onboard_pmc_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}.
-- 4.88 GB, 10 sec


-- 4/ compute difference during export to update splio universe
-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
DROP TABLE IF EXISTS `export_matrix_email.datahub_002_onboard_pmc`;
CREATE TABLE `export_matrix_email.datahub_002_onboard_pmc` AS
WITH compare AS (
  SELECT
    IFNULL(cur.email_pmi, prev.email_pmi) AS email_pmi,
    IFNULL(cur.pmc_pmi, prev.pmc_pmi) AS pmc_pmi,
    IFNULL(cur.email, prev.email) AS email,
    IFNULL(cur.universe_id, prev.universe_id) AS universe_id,
    cur.* EXCEPT(email_pmi, pmc_pmi, email, universe_id, fingerprint),
    cur.fingerprint   AS cur_fingerprint,
    prev.fingerprint  AS prev_fingerprint
  FROM `prepare.datahub_002_onboard_pmc_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}` AS cur
  FULL JOIN `prepare.datahub_002_onboard_pmc_previous_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}` AS prev USING (email_pmi, pmc_pmi, universe_id)
), to_update AS (
  SELECT
    * EXCEPT(email_pmi, pmc_pmi, cur_fingerprint, prev_fingerprint), 'oui' AS pmc_exists
  FROM compare
  WHERE cur_fingerprint IS NOT NULL
    AND (prev_fingerprint IS NULL OR cur_fingerprint != prev_fingerprint)
), to_unsub AS (
  SELECT
    compare.email,
    IFNULL(pmc_join.pmc_creation_date, TIMESTAMP('1970-01-01 00:00:00')) AS pmc_creation_date,
    IFNULL(pmc_join.pmc_last_activity_date, TIMESTAMP('1970-01-01 00:00:00')) AS pmc_last_activity_date,
    IFNULL(pmc_join.pmc_signup_service, '') AS pmc_signup_service,
    IFNULL(pmc_join.pmc_last_login_service, '') AS pmc_last_login_service,
    universe_id,
    CASE WHEN pmc_join.pmc_pmi IS NOT NULL THEN 'oui' ELSE 'non' END AS pmc_exists
  FROM compare
  LEFT JOIN `store_matrix_pmc.email_and_pmc_join` AS pmc_join
    ON (pmc_join.email = compare.email AND pmc_join.pmc_pmi IS NOT NULL)
  WHERE cur_fingerprint IS NULL
), order_to_push AS (
  SELECT email, pmc_exists, pmc_creation_date, pmc_last_activity_date, pmc_signup_service, pmc_last_login_service, universe_id FROM to_update
  UNION ALL
  SELECT email, pmc_exists, pmc_creation_date, pmc_last_activity_date, pmc_signup_service, pmc_last_login_service, universe_id FROM to_unsub
)
SELECT
    email,
    pmc_exists,
    FORMAT_TIMESTAMP('%Y-%m-%d %H:%M:%S', pmc_creation_date) AS pmc_creation_date,
    FORMAT_TIMESTAMP('%Y-%m-%d %H:%M:%S', pmc_last_activity_date) AS pmc_last_activity_date,
    pmc_signup_service,
    pmc_last_login_service,
    universe_id,
    FORMAT_TIMESTAMP('%Y-%m-%d %H:%M:%S', CURRENT_TIMESTAMP()) AS datahub_002_update
FROM order_to_push;
-- This statement added 13,865,563 rows to export_matrix_email.datahub_002_onboard_pmc .
-- 1.59 GB
