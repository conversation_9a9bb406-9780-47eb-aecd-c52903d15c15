-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- create gala_alert_events to export to postgres,
-- it groups all events generated for gala follow-stars
CREATE TABLE IF NOT EXISTS `{{params.bq_project}}.export_matrix_email.gala_alert_events` (
    create_date        TIMESTAMP   NOT NULL    OPTIONS(description="Creation date of the event"),
    type               STRING      NOT NULL    OPTIONS(description="Type of the event. enum = sub, unsub"),
    email              STRING                  OPTIONS(description="ref : userhub.store.gala_alert_event::payload::userId"),
    email_hash         STRING      NOT NULL    OPTIONS(description="ref : calculated email sha256"),
    payload            STRING                  OPTIONS(description="Additionnal information, depending on event type")
    ) OPTIONS(
                 expiration_timestamp=NULL,
                 description="Gala alert sub/unsub events.\n"
                 || "\n\n"
                 || "daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);

TRUNCATE TABLE `{{params.bq_project}}.export_matrix_email.gala_alert_events`;
-- ------------------------------------------------------------------------------------------------
-- Generate sub/unsub events
-- ------------------------------------------------------------------------------------------------
INSERT INTO `{{params.bq_project}}.export_matrix_email.gala_alert_events`
(create_date, type, email, email_hash, payload)
WITH recent_alert_events AS (
    -- get most recent event body from user events by user
    SELECT DISTINCT TRIM(LOWER(JSON_VALUE(payload, '$.userId'))) AS email, FIRST_VALUE(payload) OVER w AS payload
    FROM `{{params.bq_userhub_project}}.store.gala_alert_event_live`
    -- do not generate sub events for old profiles (this was handled by PMC through data-direct, and guess what, it's buggy :D, I do not care)
    -- gala alert will be newly activated through the website gala in 2024
    WHERE DATE(event_date) >= '2024-01-01'
        AND type = 'upsert'
        -- validate email (tests sometimes contain uuid)
        AND JSON_VALUE(payload, '$.userId') like '%@%'
    WINDOW w AS (PARTITION BY JSON_VALUE(payload, '$.userId') ORDER BY event_date DESC)
), sub_events AS (
    SELECT
        CURRENT_TIMESTAMP() AS event_date,
        'sub' AS event_type,
        email AS email,
        TO_HEX(SHA256(email)) AS email_hash,
        JSON_EXTRACT(CONCAT('{'
            , '"ev": 100,'
            , '"source": "gala",'
            , '"app": "mozart",'
            , '"process": "{{ dag.dag_id }}",'
            , '"medium": "sub through gala follow-star",'
            , '"consent_ids": [' , TO_JSON_STRING(ec.id) , ']'
            , '}'), '$'
        ) AS payload
    FROM recent_alert_events AS e
    JOIN `{{params.bq_project}}.store_karinto.email_consent` AS ec ON ec.public_ref = 'gala_alert'
    LEFT JOIN `{{params.bq_project}}.refined_data.profile_email` AS p ON p.info.email = e.email
    -- make sure we have tags to generate a sub
    WHERE JSON_VALUE(payload, '$.shortIds') != '|'
        -- make sure profile is not already sub to not generate dead events !
        AND (
            (p.info.email IS NOT NULL AND ec.public_ref NOT IN UNNEST(service.active.consents))
            OR info.email IS NULL
        )
), unsub_events AS (
    SELECT
        CURRENT_TIMESTAMP() AS event_date,
        'unsub' AS event_type,
        CAST(NULL AS STRING) AS email,
        TO_HEX(SHA256(email)) AS email_hash,
        JSON_EXTRACT(CONCAT('{'
            , '"ev": 100,'
            , '"source": "gala",'
            , '"app": "mozart",'
            , '"process": "{{ dag.dag_id }}",'
            , '"medium": "unsub through gala follow-star",'
            , '"consent_ids": [' , TO_JSON_STRING(ec.id) , ']'
            , '}'), '$'
        ) AS payload
    FROM recent_alert_events AS e
    JOIN `{{params.bq_project}}.store_karinto.email_consent` AS ec ON ec.public_ref = 'gala_alert'
    JOIN `{{params.bq_project}}.refined_data.profile_email` AS p ON p.info.email = e.email
    -- filter on empty tags
    WHERE JSON_VALUE(payload, '$.shortIds') = '|'
        -- make sure profile is sub to not generate dead events !
        AND ec.public_ref IN UNNEST(service.active.consents)
)
SELECT * FROM sub_events
UNION ALL
SELECT * FROM unsub_events
;