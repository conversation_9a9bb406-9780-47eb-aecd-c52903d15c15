-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DROP TABLE IF EXISTS `store.datahub_014_main_consent_previous`;

ALTER TABLE IF EXISTS `store.datahub_014_main_consent`
RENAME TO `datahub_014_main_consent_previous`;

DROP TABLE IF EXISTS `store.datahub_014_main_consent`;

CREATE TABLE `store.datahub_014_main_consent`(
  profile_master_id                     INTEGER     OPTIONS(description="Email profile_master_id"),
  email                                 STRING      OPTIONS(description="Full email"),
  -- datahub 008 infos
  main_consent_name                     STRING      OPTIONS(description="Last subscribed consent label per universe, Example Gala"),
  main_consent_public_ref               STRING      OPTIONS(description="Last subscribed consent pblic ref per universe, Example gala_part"),
  main_consent_unsub_domain             STRING      OPTIONS(description="Last subscribed consent unsub domain per universe, Example dm4.gala-news.fr"),
  universe_id                           INTEGER     OPTIONS(description="Id universe"),
  fingerprint                           INTEGER     OPTIONS(description="Row fingerprint to compare with previous version")
)
OPTIONS(description="Main consent informations for all profile sub per universe. Destroyed and Created every day.\n\n"
                 || "DAG: {{ dag.dag_id }}, TASK: {{ task.task_id }} \n"
                 || "Sync: daily");

INSERT INTO `store.datahub_014_main_consent`
(profile_master_id,email,main_consent_name,main_consent_public_ref,main_consent_unsub_domain ,universe_id,fingerprint)
SELECT profile_master_id,email,main_consent_name,main_consent_public_ref,main_consent_unsub_domain ,universe_id,fingerprint
FROM `prepare.datahub_014_main_consent_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}` AS pre;




