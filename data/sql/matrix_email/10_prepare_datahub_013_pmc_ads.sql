-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

--
-- COMMENT to do
-- 2/ backup datahub_013_pmc_ads data to previous table
-- 3/ create datahub_013_pmc_ads with full data
-- 4/ compute difference during export to update splio universe
--
-----------------------------------------------------------------------------------------------
{% if params.full_export|lower == 'true' %}

CREATE TABLE `prepare.datahub_013_pmc_ads_backup_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}`
AS SELECT * FROM `store.datahub_013_pmc_ads`;

DROP TABLE IF EXISTS `store.datahub_013_pmc_ads`;
{% endif %}
-- 1/ create store table for first time
CREATE TABLE IF NOT EXISTS `store.datahub_013_pmc_ads`(
  uuid                                  STRING     OPTIONS(description="Email uuid"),
  email                                 STRING      OPTIONS(description="Full email"),
  -- datahub 013 infos
  pmc_personalized_ads                  STRING      OPTIONS(description="Has Client  disabled the ads targeting ?"),
  universe_id                           INTEGER     OPTIONS(description="Id universe"),
  fingerprint                           INTEGER     OPTIONS(description="Row fingerprint to compare with previous version")
)
OPTIONS(description="Synchronise refuse of ad targeting of PMC profile to Nl universe. Destroyed and Created every day.\n\n"
                 || "DAG: {{ dag.dag_id }}, TASK: {{ task.task_id }} \n"
                 || "Sync: daily");

-- 2/ backup last data of main_consent data to previous table
DROP TABLE IF EXISTS `prepare.datahub_013_pmc_ads_previous_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}`;
DROP TABLE IF EXISTS `prepare.datahub_013_pmc_ads_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}`;

CREATE TABLE `prepare.datahub_013_pmc_ads_previous_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}`
AS SELECT * FROM `store.datahub_013_pmc_ads`;

-- 3/ create datahub_013_pmc_ads full data table
CREATE TABLE `prepare.datahub_013_pmc_ads_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}`(
  uuid                                  STRING     OPTIONS(description="PMC uuid"),
  email                                 STRING      OPTIONS(description="Full email"),
  -- datahub 013 infos
  pmc_personalized_ads                  STRING      OPTIONS(description="Has Client  disabled the ads targeting ?"),
  universe_id                           INTEGER     OPTIONS(description="Id universe"),
  fingerprint                           INTEGER     OPTIONS(description="Row fingerprint to compare with previous version")
);

-- for the first run, _previous table does not exists. we must create it.
-- get last sub main consents for all universes
-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
INSERT INTO `prepare.datahub_013_pmc_ads_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}`
    (uuid, email, pmc_personalized_ads, universe_id, fingerprint)
WITH pmc_ads AS (
    -- get last subscribed consent for each profile per universe
    SELECT store.uuid,
           store.profile_data.email,
           CASE ANY_VALUE(store.system_data.personalized_ads)
               WHEN FALSE THEN 'disabled'
               ELSE ''
               END        AS pmc_personalized_ads,
           ub.universe_id AS universe_id
    FROM `pm-{{params.env}}-userhub.refined_data.pmc_profile` AS store
        JOIN `pm-{{params.env}}-matrix.refined_data.profile_email` AS email
    ON email.info.email = store.profile_data.email,
        UNNEST(email.service.active.nl.brands) AS brand
        JOIN `pm-{{params.env}}-matrix.store_karinto.universe_brand` AS ub ON UPPER(ub.brand_trigram) = UPPER(brand)
    WHERE LOWER(ub.universe_type) = 'nl'
    GROUP BY 1, 2, 4
)
SELECT
    pmc_ads.uuid,
    pmc_ads.email,
    pmc_ads.pmc_personalized_ads,
    pmc_ads.universe_id,
    FARM_FINGERPRINT(CONCAT(
        CAST(pmc_ads.uuid AS STRING),
        IFNULL(CAST(pmc_ads.email AS STRING), ''),
        IFNULL(CAST(pmc_ads.pmc_personalized_ads AS STRING), ''),
        IFNULL(CAST(pmc_ads.universe_id AS STRING), '')
    )) as fingerprint
FROM pmc_ads
;
-- This statement added 15,666,663 rows to datahub_013_pmc_ads_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}.
-- 1.64 GB


-- 4/ compute difference during export to update splio universe
-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
DROP TABLE IF EXISTS `export_matrix_email.datahub_013_pmc_ads`;
CREATE TABLE `export_matrix_email.datahub_013_pmc_ads` AS
WITH compare AS (
  SELECT
    IFNULL(cur.uuid, prev.uuid) AS uuid,
    IFNULL(cur.email, prev.email) AS email,
    IFNULL(cur.universe_id, prev.universe_id) AS universe_id,
    cur.* EXCEPT(uuid, email, universe_id, fingerprint),
    cur.fingerprint   AS cur_fingerprint,
    prev.fingerprint  AS prev_fingerprint
  FROM `prepare.datahub_013_pmc_ads_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}` AS cur
  FULL JOIN `prepare.datahub_013_pmc_ads_previous_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}` AS prev USING (uuid, universe_id)
), to_update AS (
  SELECT
    * EXCEPT(uuid, cur_fingerprint, prev_fingerprint),
  FROM compare
  WHERE cur_fingerprint IS NOT NULL
    AND (prev_fingerprint IS NULL OR cur_fingerprint != prev_fingerprint)
), to_unsub AS (
  SELECT
    email,
    '' AS pmc_personalized_ads,
    universe_id
  FROM compare
  WHERE cur_fingerprint IS NULL
), order_to_push AS (
  SELECT email, pmc_personalized_ads, universe_id FROM to_update
  UNION ALL
  SELECT email, pmc_personalized_ads, universe_id FROM to_unsub
)
SELECT
  email,
  pmc_personalized_ads,
  universe_id,
  FORMAT_TIMESTAMP('%Y-%m-%d %H:%M:%S', CURRENT_TIMESTAMP()) AS datahub_013_update
FROM order_to_push;
-- This statement added 15,666,663 rows to export_matrix_email.datahub_013_pmc_ads .
-- 1.18 GB
