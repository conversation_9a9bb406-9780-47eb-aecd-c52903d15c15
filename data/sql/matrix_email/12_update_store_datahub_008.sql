-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DROP TABLE IF EXISTS `store.datahub_008_nl_shopping_previous`;

ALTER TABLE IF EXISTS `store.datahub_008_nl_shopping`
RENAME TO `datahub_008_nl_shopping_previous`;

-- prepare new store table
DROP TABLE IF EXISTS `store.datahub_008_nl_shopping`;
CREATE TABLE `store.datahub_008_nl_shopping`(
  profile_master_id                     INTEGER     OPTIONS(description="Email profile_master_id"),
  email                                 STRING      OPTIONS(description="Full email"),
  -- datahub 008 infos
  main_consent_name                     STRING      OPTIONS(description="Last subscribed consent label, Example Gala"),
  main_consent_public_ref               STRING      OPTIONS(description="Last subscribed consent pblic ref, Example gala_part"),
  main_consent_unsub_domain             STRING      OPTIONS(description="Last subscribed consent unsub domain, Example dm4.gala-news.fr"),
  all_part_consents                     STRING      OPTIONS(description="All subscribed public ref for partner consents only, surrounded by pipe, Example |capital_part|gala_part| "),
  all_brands                            STRING      OPTIONS(description="All subscribed brands, surrounded by pipe, Example |GEN|CAP|GAL|VOI|GEO|"),
  theme_unsub                           STRING      OPTIONS(description="Unsubscribed theme, surrounded by pipe, Example |beaute|mode|"),
  consent_part_click_last_date          TIMESTAMP   OPTIONS(description="Last click date for part consents"),
  consent_part_open_last_date           TIMESTAMP   OPTIONS(description="Last open date for part consents"),
  email_consent_part_last_activity_date TIMESTAMP   OPTIONS(description="Last click/open date for part consents"),
  pmc_last_activity_date                TIMESTAMP   OPTIONS(description="Last PMC profile activity date"),

  --datahub 000 infos
  date_update                           TIMESTAMP   OPTIONS(description="Last email profile update"),
  -- profile data
  firstname                             STRING      OPTIONS(description="PMC firstname info"),
  lastname                              STRING      OPTIONS(description="PMC lastname info"),
  civ                                   STRING      OPTIONS(description="PMC gender info"),
  date_naissance                        DATE        OPTIONS(description="PMC birthdate info"),
  annee_naissance                       INTEGER     OPTIONS(description="PMC birth year info"),
  code_postal                           STRING      OPTIONS(description="PMC postal code info"),
  departement                           STRING      OPTIONS(description="PMC depatment code info"),
  ville                                 STRING      OPTIONS(description="PMC city info"),

  -- disabled the ads targeting ?
  pmc_personalized_ads                  BOOL      OPTIONS(description="Has Client  disabled the ads targeting ?"),

  fingerprint                           INTEGER     OPTIONS(description="Row fingerprint to compare with previous version")
)
OPTIONS(description="All profile which should be present in nl-shopping univers with their data. Destroyed and Created every day.\n\n"
                 || "DAG: {{ dag.dag_id }}, TASK: {{ task.task_id }} \n"
                 || "Sync: daily");

INSERT INTO `store.datahub_008_nl_shopping`
(profile_master_id, email, firstname, lastname, civ, date_naissance, annee_naissance, code_postal,
       departement, ville, date_update, main_consent_name, main_consent_public_ref, main_consent_unsub_domain,
       all_part_consents, all_brands, theme_unsub, consent_part_click_last_date, consent_part_open_last_date,
       email_consent_part_last_activity_date, pmc_last_activity_date, pmc_personalized_ads, fingerprint)
SELECT profile_master_id, email, firstname, lastname, civ, date_naissance, annee_naissance, code_postal,
       departement, ville, date_update, main_consent_name, main_consent_public_ref, main_consent_unsub_domain,
       all_part_consents, all_brands, theme_unsub, consent_part_click_last_date, consent_part_open_last_date,
       email_consent_part_last_activity_date, pmc_last_activity_date, pmc_personalized_ads, fingerprint
FROM `prepare.datahub_008_nl_shopping_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}` AS pre;




