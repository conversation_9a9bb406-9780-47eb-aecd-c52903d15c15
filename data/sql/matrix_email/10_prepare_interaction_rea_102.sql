-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
-- Acquisition REA
DROP TABLE IF EXISTS matrix__email_tmp.pandora_rea_interaction;
CREATE TABLE IF NOT EXISTS matrix__email_tmp.pandora_rea_interaction (
    ext_id varchar(50),
    provider varchar(50),
    event varchar(50),
    event_date timestamp with time zone NOT NULL,
    source_type varchar(50),
    source_id varchar(50),
    source_date  timestamp with time zone NOT NULL,
    channel varchar(50),
    channel_details_id varchar(50),
    channel_details_name varchar(150),
    individual_id varchar(120),
    universe_id integer
);
ALTER TABLE matrix__email_tmp.pandora_rea_interaction OWNER TO matrix_email;

WITH rea_events AS (
    SELECT
        rea.profile_master_id AS profile_master_id,
        MAX(pe.id) AS pandora_event_id,
        MAX(pe.create_date) AS pandora_create_date,
        MAX(pmi.email) AS email,
        sl.universe_id AS universe_id
    FROM matrix__email_stockpile.reoptin_reactivity AS rea
    JOIN matrix__email.profile_master_id AS pmi
        ON pmi.id = rea.profile_master_id
    JOIN pandora.event AS pe
        ON pmi.email = pe.payload->>'email'
        AND rea.reactivity_date BETWEEN pe.create_date - INTERVAL '1 min' AND pe.create_date + INTERVAL '1 min'
        AND pe.response IN ('REA', 'NEW')
    JOIN karinto.email_consent AS ec
        ON ec.brand_id = rea.brand_id
    JOIN matrix__email_splio.splio_list AS sl
        ON sl.email_consent_id = ec.id
    JOIN matrix__email_splio.universe AS univ
        ON univ.id = sl.universe_id
        AND univ.type='nl'
    JOIN matrix__email.profiles_email_consents AS pec
        ON pec.email_consent_id = ec.id
        AND pec.profile_master_id = rea.profile_master_id
        AND pec.status = 'sub'
    {% if params.full_export|lower != 'true' %}
    WHERE date(pe.create_date) > NOW() - INTERVAL '{{params.interval}}'
    {% endif %}
	GROUP BY rea.profile_master_id, sl.universe_id
)
INSERT INTO matrix__email_tmp.pandora_rea_interaction
    (ext_id, provider, event, event_date, source_type, source_id, source_date,
     channel, channel_details_id, channel_details_name, individual_id,
     universe_id)
SELECT
    rea.pandora_event_id::varchar  || '_int102' AS ext_id,
    null                            AS provider,
    'Reactivation'                  AS event,
    rea.pandora_create_date         AS event_date,
    null                            AS source_type,
    null                            AS source_id,
    rea.pandora_create_date         AS source_date,
    'matrix'                        AS channel,
    null                            AS channel_details_id,
    null                            AS channel_details_name,
    email                           AS individual_id,
    rea.universe_id                 AS universe_id      -- (use prisma_test with preprod id = 79)
FROM rea_events AS rea
;