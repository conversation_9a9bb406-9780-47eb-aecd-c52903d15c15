-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
-- Purpose of this script :
-- * compute pmc_email_token for each profile. matrix__email_stockpile.pmc_email_token
-- * Prepare data to send to Splio via datahub 006
--

-- --------------------------------------------------------------------------
-- Compute pmc_email_token
-- --------------------------------------------------------------------------
TRUNCATE TABLE matrix__email_stockpile.pmc_email_token;

WITH emails_from_event AS (
    SELECT DISTINCT email FROM matrix__email_queue.email_event
    WHERE type = 'sub'
    {% if params.full_export|lower != 'true' %}
      AND create_date > NOW() - INTERVAL '{{params.interval}}'
    {% endif %}
    UNION ALL
    SELECT DISTINCT email FROM matrix__email_queue.simulated_sub_unsub_event
    WHERE type = 'sub'
    {% if params.full_export|lower != 'true' %}
      AND create_date > NOW() - INTERVAL '{{params.interval}}'
    {% endif %}
)
INSERT INTO matrix__email_stockpile.pmc_email_token
    (profile_master_id, pmc_email_token)
SELECT
    id,
    substring(matrix__email_queue.digest(
        'h6kkW47^-aJtUuLw' || email_sha256 || 'L3?UF8qVaqLgk_8*',
        'sha256'
    )::varchar(255), 3, 59)
FROM emails_from_event AS q
JOIN matrix__email.profile_master_id AS pmi ON q.email = pmi.email;
-- Time: 188171,158 ms on preprod

-- --------------------------------------------------------------------------
-- Prepare splio datahub data to export
-- --------------------------------------------------------------------------

TRUNCATE matrix__email_splio.datahub_006;

INSERT INTO matrix__email_splio.datahub_006
    (email, pmc_email_token, universe_id)
SELECT
    pmi.email, pet.pmc_email_token, pu.universe_id
FROM matrix__email_splio.profiles_universes AS pu
JOIN matrix__email.profile_master_id AS pmi ON pu.email_profile_master_id = pmi.id
JOIN matrix__email_stockpile.pmc_email_token AS pet ON pet.profile_master_id = pmi.id
;
-- Time: 334358,694 ms on preprod