-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

BEGIN;

    -- ################################################################################################
    -- check sub with invalid email_hash value
    -- ################################################################################################
    INSERT INTO matrix__email_queue.email_event_with_bad_hash
    (email_event_id, execution_id, create_date)
    SELECT
        ee.id AS email_event_id,
        '{{ execution_date.strftime("%Y%m%d_%H%M") }}' AS execution_id,
        NOW()
    FROM matrix__email_queue.email_event AS ee
    WHERE ee.email IS NOT NULL
      AND ee.id > {{ task_instance.xcom_pull(task_ids='save_chunk_ids', key ='start_id') | int }}
      AND ee.id <= {{ task_instance.xcom_pull(task_ids='save_chunk_ids', key ='end_id') | int }}
      AND char_length(email_hash) = 64
      AND substring(matrix__email_queue.digest(lower(trim(ee.email)), 'sha256')::varchar(255), 3, 64) != ee.email_hash
    -- @todo: CREATE INDEX email_lower_trim_idx ON matrix__email_queue.email_event (LOWER(TRIM(email)));

    ON CONFLICT (email_event_id)
        DO UPDATE SET execution_id = '{{ execution_date.strftime("%Y%m%d_%H%M") }}',
                    create_date = NOW()
    ;

    -- save error
    INSERT INTO matrix__email.email_event_error (code, payload, email_event_id)
    SELECT 410, ('{"reason": "Event with bash email_hash value"}')::json, eebh.email_event_id
    FROM matrix__email_queue.email_event_with_bad_hash AS eebh;

    -- -- -- -- -- -- -- -- -- -- -- --
COMMIT;