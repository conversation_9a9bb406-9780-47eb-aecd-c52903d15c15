-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

BEGIN;

    -- ################################################################################################
    -- ################################################################################################
    -- profile_master_id
    -- ################################################################################################

    -- Purpose of this script is :
    -- * create new profile_master_id
    -- * get profile_master_id.id used in queued events
    -- * create a temporary table for other processes : matrix__email_tmp.profile_master_id_<Ymd_HM>
    -- * This temp table is used by sub_unsub, blacklist and change_email
    -- * Then go change_email
    -- * And create profile in profile_master_id
    --
    -- We put bad events (as profile are not good) away, in the email_event_error table.
    --
    -- In this script, "reference" table refers to matrix__email tables
    -- Whereas, "tmp" table are table we are currently working on.

    -- ------------------------------------------------------------------------------------------------
    -- Get data from email_event
    -- ------------------------------------------------------------------------------------------------
    -- insert by email_md5 hash
    INSERT INTO matrix__email_tmp.profile_master_id
      (event_id, profile_master_id, email_md5, email_sha256, email, create_date, event_date, type)
    SELECT
      id,
      null::bigint,
      CASE email_hash  WHEN '\N' THEN null ELSE email_hash   END,
      null,
      CASE email       WHEN '\N' THEN null ELSE lower(trim(email)) END,
      create_date,
      create_date,
      type
    FROM matrix__email_queue.email_event
    WHERE id > {{ task_instance.xcom_pull(task_ids='save_chunk_ids', key ='start_id') | int }}
      AND id <= {{ task_instance.xcom_pull(task_ids='save_chunk_ids', key ='end_id') | int }}
      AND char_length(email_hash) = 32
      AND type != 'anonymise_profile'
    UNION ALL
    SELECT
      id,
      null::bigint,
      null,
      CASE email_hash  WHEN '\N' THEN null ELSE email_hash   END,
      CASE email       WHEN '\N' THEN null ELSE lower(trim(email)) END,
      create_date,
      create_date,
      type
    FROM matrix__email_queue.email_event
    WHERE id > {{ task_instance.xcom_pull(task_ids='save_chunk_ids', key ='start_id') | int }}
      AND id <= {{ task_instance.xcom_pull(task_ids='save_chunk_ids', key ='end_id') | int }}
      AND char_length(email_hash) = 64
      AND type != 'anonymise_profile'
    ;

    -- remove recette email
    DELETE FROM matrix__email_tmp.profile_master_id
    WHERE email like '%<EMAIL>';

    -- remove pmi generated from email event with bad hash
    DELETE FROM matrix__email_tmp.profile_master_id AS tmp_pmi
    USING matrix__email_queue.email_event_with_bad_hash AS eebh
    WHERE execution_id = '{{ execution_date.strftime("%Y%m%d_%H%M") }}'
    AND eebh.email_event_id = tmp_pmi.event_id
    ;

    -- Generate error for 'sub' event with empty email
    INSERT INTO matrix__email.email_event_error (code, payload, email_event_id)
    SELECT 400, ('{"reason": "Cannot process \"sub\" event without \"email\"."}')::json, event_id
    FROM matrix__email_tmp.profile_master_id
    WHERE email IS NULL and type = 'sub';

    -- we ignore 'sub' event with empty email
    DELETE FROM matrix__email_tmp.profile_master_id
    WHERE email IS NULL and type = 'sub';

    -- ------------------------------------------------------------------------------------------------
    -- Enrich with profile_master_id, email_md5 and email_sha256
    -- ------------------------------------------------------------------------------------------------

    -- COMPUTE email_sha256 for unknown profiles
    UPDATE matrix__email_tmp.profile_master_id
    SET email_sha256 = substring(matrix__email_queue.digest(lower(trim(email)), 'sha256')::varchar(255), 3, 64)
    Where email_sha256 IS NULL and email IS NOT NULL;

    -- COMPUTE email_md5 for unknown profiles
    UPDATE matrix__email_tmp.profile_master_id
    SET email_md5 = substring(matrix__email_queue.digest(lower(matrix__email_queue.unaccent(trim(email))), 'md5')::varchar(255), 3, 32)
    Where email_md5 IS NULL and email IS NOT NULL;

    -- GET profile_master_id.id from reference table
    UPDATE matrix__email_tmp.profile_master_id AS tmp
    SET profile_master_id = ref.id,
        email_md5 = ref.email_md5,
        email_sha256 = ref.email_sha256,
        create_date = ref.create_date
    FROM matrix__email.profile_master_id AS ref
    WHERE ref.email_md5 = tmp.email_md5
       OR ref.email_sha256 = tmp.email_sha256;

    -- ------------------------------------------------------------------------------------------------
    -- Deal with change email events to attach profile_master_id
    -- ------------------------------------------------------------------------------------------------

    -- -- -- -- -- -- -- -- -- -- -- --
COMMIT;