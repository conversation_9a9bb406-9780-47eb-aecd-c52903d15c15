-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

SELECT (
    EXISTS (
        SELECT FROM matrix__email_queue.email_event
        WHERE id > {{ task_instance.xcom_pull(task_ids='save_chunk_ids', key ='start_id') | int }}
            AND id <= {{ task_instance.xcom_pull(task_ids='save_chunk_ids', key ='end_id') | int }}
            AND type IN ({{ "'" + params.event_types|join("', '") + "'" }})
    )
) AS has_events
;