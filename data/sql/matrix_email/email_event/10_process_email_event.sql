-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

BEGIN;

    -- ------------------------------------------------------------------------------------------------
    -- Create new profile in matrix__email.profile_master_id table
    -- ------------------------------------------------------------------------------------------------
    -- note: only "sub" event can create new profiles !
    INSERT INTO matrix__email.profile_master_id (email_md5, email_sha256, email, create_date, update_date)
    SELECT DISTINCT ON (email)
      email_md5, email_sha256, email, create_date, create_date
    FROM matrix__email_tmp.profile_master_id
    WHERE profile_master_id IS NULL
      AND type = 'sub'
      AND email IS NOT NULL
    ORDER BY email, create_date ASC
    ;

    -- GET newly created profile_master_id back into tmp table
    UPDATE matrix__email_tmp.profile_master_id AS tmp
    SET profile_master_id = ref.id
    FROM matrix__email.profile_master_id AS ref
    WHERE tmp.profile_master_id IS NULL
      AND (ref.email_md5 = tmp.email_md5 OR ref.email_sha256 = tmp.email_sha256);

    -- get email for unsub events
    UPDATE matrix__email_tmp.profile_master_id AS tmp
    SET email = pmi.email
    FROM matrix__email.profile_master_id AS pmi
    WHERE tmp.email IS NULL
      AND (pmi.email_md5 = tmp.email_md5 OR pmi.email_sha256 = tmp.email_sha256);

    -- Errors dealing : put event with empty profile_master_id on error
    INSERT INTO matrix__email.email_event_error (code, payload, email_event_id)
    SELECT 404, '{"reason": "Cannot find profile_master_id for this event."}', event_id
    FROM matrix__email_tmp.profile_master_id
    WHERE profile_master_id IS NULL;

    -- remove un-created profile_master_id (unsub on email_md5, AS we don't have email)
    DELETE FROM matrix__email_tmp.profile_master_id
    WHERE profile_master_id IS NULL;

    -- ################################################################################################
    -- store simulated_sub_unsub_event events
    -- ################################################################################################

    -- mark already existing unsub_deleted_profile_event
    UPDATE matrix__email_tmp.simulated_sub_unsub_event AS tmp
    SET to_insert = false
    FROM matrix__email_queue.simulated_sub_unsub_event AS ref
    WHERE tmp.email_event_id = ref.email_event_id;

    -- @todo need to change constraint : simulated_sub_unsub_event_pk
    -- ALTER TABLE matrix__email_queue.simulated_sub_unsub_event DROP CONSTRAINT simulated_sub_unsub_event_pk;
    -- ALTER TABLE matrix__email_queue.simulated_sub_unsub_event ADD CONSTRAINT simulated_sub_unsub_event_pk PRIMARY KEY (email_event_id, type, email_hash);

    -- insert events into simulated_sub_unsub_event
    INSERT INTO matrix__email_queue.simulated_sub_unsub_event
        (email_event_id, create_date, type, email_hash, email, payload)
    SELECT  email_event_id, create_date, type, email_hash, email, payload
    FROM matrix__email_tmp.simulated_sub_unsub_event
    WHERE to_insert = true;


    -- ################################################################################################
    -- ################################################################################################
    -- sub_unsub
    -- ################################################################################################

    -- Script purpose :
    -- * Get all "sub" and "unsub" events
    -- * Create/Update brand's profile into matrix__email.profile
    -- * Create/Update consent status in matrix__email.profiles_email_consents
    --
    -- Here, we have matrix__email_tmp.profile_master_id table
    -- containing all profile_master_id for considered events
    -- see 01_profile_master_id.sql for more info
    -- ------------------------------------------------------------------------------------------------
    -- Import events
    -- ------------------------------------------------------------------------------------------------

    -- insert event coming from re-platformed application (like pandora), ev = 99
    INSERT INTO matrix__email_tmp.profile_and_consent
        (event_id, email_consent_id, status, firstname, lastname, gender, birthdate,
         address_line1, address_line2, postal_code, city, country, source,
         profile_create_date, profile_email_consent_create_date, update_date)
    SELECT
        id,
        replace(json_array_elements(payload->'consent_ids')::varchar(150), '"', '')::integer,
        type,
        matrix__email.replace_html_entities(left(payload->'profile'->>'firstname', 50)),
        matrix__email.replace_html_entities(left(payload->'profile'->>'lastname', 50)),
        payload->'profile'->>'civility',
        CAST(REPLACE(payload->'profile'->>'birthdate', 'undefined', NULL) AS timestamptz),
        matrix__email.replace_html_entities(left(payload->'profile'->'address'->>'address1', 100)),
        matrix__email.replace_html_entities(left(payload->'profile'->'address'->>'address2', 100)),
        left(payload->'profile'->'address'->>'postal_code',18),
        matrix__email.replace_html_entities(left(payload->'profile'->'address'->>'city',200)),
        matrix__email.replace_html_entities(left(payload->'profile'->'address'->>'country',90)),
        left(concat('m:', payload->>'medium', ' / s:', payload->>'source'), 100),
        create_date,
        create_date,
        create_date
    FROM matrix__email_queue.email_event
    WHERE id > {{ task_instance.xcom_pull(task_ids='save_chunk_ids', key ='start_id') | int }}
      AND id <= {{ task_instance.xcom_pull(task_ids='save_chunk_ids', key ='end_id') | int }}
      AND type IN ('sub', 'unsub', 'update_profile')
      AND CAST(payload->>'ev' AS integer) >= 99
    ;

    -- @change-event-destination-sub-to-part-only
    -- sub destination to all source consents except GALA
    WITH ce_email_destination_subs AS (
          SELECT
            ce.event_id AS event_id,
            e.create_date + interval '1 second' AS event_create_date,
            src_pec.email_consent_id AS email_consent_id,
            ce.dst_profile_master_id AS profile_master_id,
            src_pec.brand_id AS brand_id,
            src_pec.create_date AS pec_create_date,
            src_pec.update_date AS pec_update_date,
            src_pr.create_date AS profile_create_date,
            src_pr.update_date AS profile_update_date,
            src_pr.firstname, src_pr.lastname,
            src_pr.gender, src_pr.birthdate,
            src_pr.address_line1, src_pr.address_line2,
            src_pr.postal_code, src_pr.city, src_pr.country,
            src_pec.source
      FROM matrix__email_tmp.change_email AS ce
      JOIN matrix__email_queue.email_event AS e
        ON ce.event_id = e.id
      JOIN matrix__email.profiles_email_consents AS src_pec
        ON src_pec.profile_master_id = ce.profile_master_id
      JOIN matrix__email.profile AS src_pr
        ON src_pr.profile_master_id = ce.profile_master_id AND src_pec.brand_id = src_pr.brand_id
      -- get all email source consents except GALA
      JOIN karinto.brand AS br ON br.id = src_pec.brand_id AND br.trigram != 'GAL'
      WHERE src_pec.status = 'sub' AND ce.dst_has_part_consent = true
    )
    INSERT INTO matrix__email_tmp.profile_and_consent
      (event_id, profile_master_id, email_consent_id, status,
       profile_email_consent_create_date,
       profile_email_consent_to_create,
       profile_create_date,
       profile_to_create,
       -- profile infos
       firstname, lastname,
       gender, birthdate,
       address_line1, address_line2,
       postal_code, city, country,
       source,
       update_date
      )
    SELECT
      tmp_sub.event_id,
      tmp_sub.profile_master_id,
      tmp_sub.email_consent_id,
      'sub'::matrix__email_queue.email_event_type,
      tmp_sub.pec_create_date,
      true,
      tmp_sub.profile_create_date,
      true,
      -- source profile infos
      tmp_sub.firstname, tmp_sub.lastname, tmp_sub.gender, tmp_sub.birthdate,
      tmp_sub.address_line1, tmp_sub.address_line2, tmp_sub.postal_code,
      tmp_sub.city, tmp_sub.country,
      tmp_sub.source,
      event_create_date
    FROM ce_email_destination_subs AS tmp_sub
    WHERE NOT EXISTS (
        SELECT 1 FROM matrix__email.profiles_email_consents AS pec
        WHERE pec.profile_master_id = tmp_sub.profile_master_id
        AND pec.email_consent_id = tmp_sub.email_consent_id
        AND pec.status = 'sub'
    )
    ;

    -- @change-event-destination-sub-to-part-only
    -- unsub source from all consents except GALA
    WITH ce_email_source_subs AS (
          SELECT ce.event_id AS event_id,
             e.create_date AS event_create_date,
             src_pec.email_consent_id AS email_consent_id,
             ce.profile_master_id AS profile_master_id,
             src_pec.brand_id AS brand_id,
             src_pec.create_date AS pec_create_date,
             src_pec.update_date AS pec_update_date,
             src_pr.create_date AS profile_create_date,
             src_pr.update_date AS profile_update_date,
             src_pec.source
      FROM matrix__email_tmp.change_email AS ce
      JOIN matrix__email_queue.email_event AS e
        ON ce.event_id = e.id
      JOIN matrix__email.profiles_email_consents AS src_pec
        ON src_pec.profile_master_id = ce.profile_master_id
      JOIN matrix__email.profile AS src_pr
        ON src_pr.profile_master_id = ce.profile_master_id AND src_pec.brand_id = src_pr.brand_id
      -- get all email source consents except GALA
      JOIN karinto.brand AS br ON br.id = src_pec.brand_id AND br.trigram != 'GAL'
      WHERE src_pec.status = 'sub' AND ce.dst_has_part_consent = true
    )
    INSERT INTO matrix__email_tmp.profile_and_consent
      (event_id, profile_master_id, email_consent_id, status,
       profile_email_consent_create_date,
       profile_email_consent_to_create,
       profile_create_date,
       profile_to_create,
       update_date
        )
    SELECT
      tmp_sub.event_id,
      tmp_sub.profile_master_id,
      tmp_sub.email_consent_id,
      'unsub'::matrix__email_queue.email_event_type,
      tmp_sub.pec_create_date,
      false,
      tmp_sub.profile_create_date,
      false,
      event_create_date
    FROM ce_email_source_subs AS tmp_sub
    ;

    -- remove pec where email event has bad email_hash value
    DELETE FROM matrix__email_tmp.profile_and_consent AS tmp_pec
    USING matrix__email_queue.email_event_with_bad_hash AS eebh
    WHERE execution_id = '{{ execution_date.strftime("%Y%m%d_%H%M") }}'
    AND eebh.email_event_id = tmp_pec.event_id
    ;
    -- ------------------------------------------------------------------------------------------------
    -- CNIL RULES: Import events
    -- ------------------------------------------------------------------------------------------------
    -- CNIL: add clear_pii events
    INSERT INTO matrix__email_tmp.profiles_to_clean
        (event_id, profile_master_id, brand_id, type, create_date)
    SELECT
        id,
        CAST(payload->>'profile_master_id' AS bigint),
        CAST(payload->>'brand_id' AS integer),
        type,
        create_date
    FROM matrix__email_queue.email_event
    WHERE id > {{ task_instance.xcom_pull(task_ids='save_chunk_ids', key ='start_id') | int }}
      AND id <= {{ task_instance.xcom_pull(task_ids='save_chunk_ids', key ='end_id') | int }}
      AND type = 'clear_pii'
    ;

    -- ------------------------------------------------------------------------------------------------
    -- Retrieve profile_master_id, email_consent_id and brand_id
    -- ------------------------------------------------------------------------------------------------

    -- get consent_id and brand_id (for mongo events only)
    UPDATE matrix__email_tmp.profile_and_consent AS tmp
    SET email_consent_id = ref.id, brand_id = ref.brand_id
    FROM karinto.email_consent AS ref
    WHERE tmp.public_ref = ref.public_ref
      AND tmp.public_ref IS NOT NULL;

    -- get karinto.id for consent_id = karinto.public_id
    UPDATE matrix__email_tmp.profile_and_consent AS tmp
    SET email_consent_id = ref.id
    FROM karinto.email_consent AS ref
    WHERE tmp.email_consent_id = ref.public_id
      AND tmp.public_ref IS NOT NULL;

    -- get brand_id for event without public_ref
    UPDATE matrix__email_tmp.profile_and_consent AS tmp
    SET brand_id = ref.brand_id,
        public_ref = ref.public_ref
    FROM karinto.email_consent AS ref
    WHERE tmp.email_consent_id = ref.id
      AND tmp.public_ref IS NULL;

    -- get profile master id
    UPDATE matrix__email_tmp.profile_and_consent AS tmp
    SET profile_master_id = ref.profile_master_id
    FROM matrix__email_tmp.profile_master_id AS ref
    WHERE tmp.event_id = ref.event_id
    AND tmp.profile_master_id IS NULL
    ;

    -- ------------------------------------------------------------------------------------------------
    -- Deal with errors
    -- ------------------------------------------------------------------------------------------------

    -- delete event on unknown profile_master_id (dealt in 01_profile_master_id.sql)
    DELETE from matrix__email_tmp.profile_and_consent
    WHERE profile_master_id IS NULL;

    -- handle email event errors
    WITH email_errors AS (
        -- add error for events with empty brand_id or empty consent_id on error
        SELECT 444, ('{"reason": "Cannot found email_consent public_ref \"' || public_ref || '\" for this event."}')::json, event_id
        FROM matrix__email_tmp.profile_and_consent
        WHERE (brand_id is NULL OR email_consent_id IS NULL) AND public_ref IS NOT NULL
        UNION ALL
        -- add error for events with unknown email_consent_id on error (as we don't retrieve brand_id)
        SELECT 445, ('{"reason": "Cannot found brand_id for email_consent_id \"' || email_consent_id::varchar(20) || '\"."}')::json, event_id
        FROM matrix__email_tmp.profile_and_consent
        WHERE brand_id is NULL AND public_ref IS NULL
        UNION ALL
        -- add error for events with unknown email_consent_id on error (as the email_consent_id does not exist in karinto)
        SELECT 446, ('{"reason": "Cannot found email_consent_id \"' || email_consent_id::varchar(20) || '\"."}')::json, event_id
        FROM matrix__email_tmp.profile_and_consent AS tmp
        WHERE email_consent_id NOT IN (SELECT id FROM karinto.email_consent)
        UNION ALL
        -- add error for profiles with email not like '@prismamedia.com' and trying to sub/unsub with public_ref that ends with "_test"
        SELECT 447, ('{"reason": "Not authorized to ' || tmp.status || ' on the email_consent_id \"' || tmp.email_consent_id::varchar(20) || '\"."}')::json, tmp.event_id
        FROM matrix__email_tmp.profile_and_consent AS tmp
        JOIN matrix__email_tmp.profile_master_id AS tmp_pmi ON tmp.profile_master_id = tmp_pmi.profile_master_id
        WHERE tmp.email_consent_id IN (SELECT id FROM karinto.email_consent WHERE public_ref LIKE '%_test')
        AND lower(trim(tmp_pmi.email)) NOT LIKE '%@prismamedia.com%'
        UNION ALL
        -- add error for events related to deactivated consents
        SELECT 448, ('{"reason": "Not authorized to sub on deactivated email_consent_id \"' || tmp.email_consent_id::varchar(20) || '\"."}')::json, tmp.event_id
        FROM matrix__email_tmp.profile_and_consent AS tmp
        JOIN karinto.email_consent AS ec ON ec.id = tmp.email_consent_id
        WHERE ec.active = false
        AND tmp.status = 'sub'
    )
    INSERT INTO matrix__email.email_event_error (code, payload, email_event_id)
    SELECT * FROM email_errors
    ;

    -- remove event with bad brand_id and email_consent_id
    DELETE FROM matrix__email_tmp.profile_and_consent
    WHERE brand_id IS NULL OR email_consent_id IS NULL;

    -- remove event with unknown email_consent_id
    DELETE FROM matrix__email_tmp.profile_and_consent
    WHERE email_consent_id NOT IN (SELECT id FROM karinto.email_consent);

    -- remove user with email not like '@prismamedia.com' not authorized
    -- to sub/unsub on consents with public_ref's end "_test"
    DELETE FROM matrix__email_tmp.profile_and_consent AS tmp
    USING matrix__email_tmp.profile_master_id AS tmp_pmi
    WHERE tmp.profile_master_id = tmp_pmi.profile_master_id
    AND tmp.email_consent_id IN (SELECT id FROM karinto.email_consent WHERE public_ref LIKE '%_test')
    AND lower(trim(tmp_pmi.email)) NOT LIKE '%@prismamedia.com'
    ;

    -- delete sub events with deactivated consents
    DELETE FROM matrix__email_tmp.profile_and_consent AS tmp
    USING karinto.email_consent AS ec
    WHERE ec.id = tmp.email_consent_id
    AND tmp.status = 'sub'
    AND ec.active = false;

    -- delete sub events for already anonymize profiles
    DELETE FROM matrix__email_tmp.profile_and_consent AS pec_tmp
    USING matrix__email.profile_master_id_anonymous AS anonym
    WHERE anonym.id = pec_tmp.profile_master_id AND pec_tmp.status = 'sub';

    -- ------------------------------------------------------------------------------------------------
    -- deal with blacklist
    -- ------------------------------------------------------------------------------------------------

    UPDATE matrix__email_tmp.profile_and_consent AS tmp
    SET profile_blacklisted = true
    FROM (
        -- Mark blacklisted profiles from reference
        SELECT profile_master_id, brand_id FROM matrix__email.blacklist
        UNION ALL
        -- Mark blacklisted profiles from current events
        SELECT profile_master_id, brand_id FROM matrix__email_tmp.blacklist
    ) AS ref
    WHERE tmp.profile_master_id = ref.profile_master_id
      AND tmp.brand_id = ref.brand_id;

    -- Mark un-blacklisted event due to unblacklist event
    WITH last_bl_status AS (
        SELECT DISTINCT ON (profile_master_id, brand_id)
            profile_master_id, brand_id, type, create_date
        FROM matrix__email_tmp.blacklist
        ORDER BY profile_master_id, brand_id, create_date DESC
    )
    UPDATE matrix__email_tmp.profile_and_consent AS tmp
    SET profile_blacklisted = false
    FROM last_bl_status AS bl
    WHERE bl.type = 'unblacklist'
      AND tmp.profile_master_id = bl.profile_master_id
      AND tmp.brand_id = bl.brand_id
      AND tmp.update_date > bl.create_date;

    -- add error for event with profile master id already LOP on brand
    INSERT INTO matrix__email.email_event_error (code, payload, email_event_id)
    SELECT 452, ('{"reason": "Cannot sub profile ' || CAST(pec_tmp.profile_master_id AS varchar) || ' already LOP on brand  \"' || CAST(pec_tmp.brand_id AS varchar) || '\"."}')::json, pec_tmp.event_id
    FROM matrix__email_tmp.profile_and_consent AS pec_tmp
    WHERE profile_blacklisted = true AND pec_tmp.status = 'sub';

    -- Now delete all blacklisted events
    DELETE FROM matrix__email_tmp.profile_and_consent
    WHERE profile_blacklisted = true;

    -- ------------------------------------------------------------------------------------------------
    -- Retrieve create_date for profile and profiles_email_consents
    -- ------------------------------------------------------------------------------------------------

    -- retrieve profile create_date
    UPDATE matrix__email_tmp.profile_and_consent AS t
    SET profile_create_date = ref.create_date,
        profile_to_create = false
    FROM matrix__email.profile AS ref
    WHERE t.brand_id = ref.brand_id AND t.profile_master_id = ref.profile_master_id;

    -- retrieve profiles_email_consents create_date
    UPDATE matrix__email_tmp.profile_and_consent AS t
    SET profile_email_consent_create_date = ref.create_date,
        profile_email_consent_to_create = false
    FROM matrix__email.profiles_email_consents AS ref
    WHERE t.email_consent_id = ref.email_consent_id AND t.profile_master_id = ref.profile_master_id;


    -- ------------------------------------------------------------------------------------------------
    -- Last seen email hashes
    -- ------------------------------------------------------------------------------------------------
    -- We update existing email hashes last seen date
    -- We insert new ones
    -- ------------------------------------------------------------------------------------------------
    UPDATE matrix__email.seen_email_hash
    SET last_seen_date = pac.profile_email_consent_create_date
    FROM matrix__email_tmp.profile_and_consent AS pac
    JOIN matrix__email.profile_master_id AS pmi ON pac.profile_master_id = pmi.id
    WHERE pmi.email_sha256 = matrix__email.seen_email_hash.email_hash;

    WITH unexisting_hashes AS (
        SELECT pmi.email_sha256,
               pac.profile_email_consent_create_date
        FROM matrix__email_tmp.profile_and_consent AS pac
        JOIN matrix__email.profile_master_id AS pmi ON pac.profile_master_id = pmi.id
        WHERE NOT EXISTS (
            SELECT 1
            FROM matrix__email.seen_email_hash AS seh
            WHERE seh.email_hash = pmi.email_sha256
        )
    )
    INSERT INTO matrix__email.seen_email_hash
      (email_hash, last_seen_date)
    SELECT email_sha256, MAX(profile_email_consent_create_date)
    FROM unexisting_hashes
    GROUP BY 1;


    -- ------------------------------------------------------------------------------------------------
    -- profile
    -- ------------------------------------------------------------------------------------------------

    -- CREATE profile
    -- summary : give last data on profile
    -- first_event : give the first profile_create_date (for profile.create_date)
    WITH summary AS (
        SELECT DISTINCT ON (profile_master_id, brand_id)
            profile_master_id, brand_id,
            firstname, lastname, gender, birthdate,
            address_line1, address_line2, postal_code, city, country,
            update_date
        FROM matrix__email_tmp.profile_and_consent
        WHERE profile_to_create = true AND status = 'sub'
        ORDER BY profile_master_id, brand_id, update_date DESC
    ), first_event AS (
        SELECT DISTINCT ON (profile_master_id, brand_id)
            profile_master_id, brand_id, profile_create_date
        FROM matrix__email_tmp.profile_and_consent
        WHERE profile_to_create = true AND status = 'sub'
        ORDER BY profile_master_id, brand_id, update_date ASC
    )
    INSERT INTO matrix__email.profile (
        profile_master_id, brand_id, firstname, lastname, gender, birthdate,
        address_line1, address_line2, postal_code, city, country, update_date,
        create_date)
    SELECT summary.profile_master_id, summary.brand_id, summary.firstname, summary.lastname, summary.gender,
        summary.birthdate, summary.address_line1, summary.address_line2, summary.postal_code, summary.city,
        summary.country, summary.update_date,
        first_event.profile_create_date
    FROM summary
    JOIN first_event ON summary.profile_master_id = first_event.profile_master_id
                    AND summary.brand_id = first_event.brand_id;

    -- UPDATE profile
    WITH summary AS (
        SELECT DISTINCT ON (profile_master_id, brand_id)
            profile_master_id, brand_id,
            firstname, lastname, gender, birthdate,
            address_line1, address_line2, postal_code, city, country,
            update_date
        FROM matrix__email_tmp.profile_and_consent
        WHERE (profile_to_create = false and status = 'sub') or status = 'update_profile'
        ORDER BY profile_master_id, brand_id, update_date DESC
    )
    UPDATE matrix__email.profile AS t
    SET firstname = COALESCE(ref.firstname, t.firstname),
        lastname = COALESCE(ref.lastname, t.lastname),
        gender = COALESCE(ref.gender, t.gender),
        -- enable nullable birthdate
        birthdate = ref.birthdate,
        address_line1 = COALESCE(ref.address_line1, t.address_line1),
        address_line2 = COALESCE(ref.address_line2, t.address_line2),
        postal_code = COALESCE(ref.postal_code, t.postal_code),
        city = COALESCE(ref.city, t.city),
        country = COALESCE(ref.country, t.country),
        update_date = COALESCE(ref.update_date, t.update_date)
    FROM summary AS ref
    WHERE t.brand_id = ref.brand_id
      AND t.profile_master_id = ref.profile_master_id;

    -- ------------------------------------------------------------------------------------------------
    -- CNIL RULES: Apply
    -- ------------------------------------------------------------------------------------------------

    -- CNIL: clear personal profile data for deleted profiles
    UPDATE matrix__email.profile AS p
    SET firstname = null,
        lastname = null,
        gender = null,
        birthdate = null,
        address_line1 = null,
        address_line2 = null,
        postal_code = null,
        city = null,
        country = null,
        update_date = tmp.create_date
    FROM matrix__email_tmp.profiles_to_clean AS tmp
    WHERE tmp.profile_master_id = p.profile_master_id
        AND tmp.brand_id = p.brand_id
        AND tmp.type = 'clear_pii'
    ;

    -- ------------------------------------------------------------------------------------------------
    -- profiles_email_consents
    -- ------------------------------------------------------------------------------------------------

    -- CREATE profiles_email_consents
    -- summary : give last data on profile email_consent
    -- first_event : give the first profile_email_consent_create_date (for profiles_email_consents.create_date)
    WITH summary AS (
        SELECT DISTINCT ON (profile_master_id, email_consent_id)
            profile_master_id, brand_id, status, update_date, email_consent_id
        FROM matrix__email_tmp.profile_and_consent
        WHERE profile_email_consent_to_create = true AND status in ('sub', 'unsub')
        ORDER BY profile_master_id, email_consent_id, update_date DESC, event_id DESC
    ), first_event AS (
        SELECT DISTINCT ON (profile_master_id, email_consent_id)
            profile_master_id, email_consent_id, profile_email_consent_create_date, source
        FROM matrix__email_tmp.profile_and_consent
        WHERE profile_email_consent_to_create = true AND status = 'sub'
        ORDER BY profile_master_id, email_consent_id, update_date, event_id ASC
    )
    INSERT INTO matrix__email.profiles_email_consents (profile_master_id, brand_id, status, source, create_date, update_date, email_consent_id)
    SELECT summary.profile_master_id, summary.brand_id, CAST(CAST(summary.status AS varchar(10)) AS matrix__email.consent_status),
           first_event.source, first_event.profile_email_consent_create_date,
           summary.update_date, summary.email_consent_id
    FROM summary
    JOIN first_event ON summary.profile_master_id = first_event.profile_master_id
                    AND summary.email_consent_id = first_event.email_consent_id;

    -- UPDATE profiles_email_consents
    WITH summary AS (
        SELECT DISTINCT ON (profile_master_id, email_consent_id)
            profile_master_id, brand_id, status, update_date, email_consent_id
        FROM matrix__email_tmp.profile_and_consent as ref
        WHERE ref.profile_email_consent_to_create = false AND status in ('sub', 'unsub')
        ORDER BY profile_master_id, email_consent_id, update_date DESC , event_id DESC
    )
    UPDATE matrix__email.profiles_email_consents AS ref
    SET status = CAST(CAST(summary.status AS varchar(10)) AS matrix__email.consent_status),
        update_date = summary.update_date
    FROM summary
    WHERE summary.email_consent_id = ref.email_consent_id
      AND summary.profile_master_id = ref.profile_master_id;



    -- ################################################################################################
    -- Update Mapping Table : matrix__email_splio.profiles_universes ( is_full_mapping ? = {{ params.is_full_mapping }} )
    -- ################################################################################################
    {% if params.is_full_mapping == True %}

        INSERT INTO matrix__email_splio.profiles_universes
            (email_profile_master_id, universe_id)
        SELECT DISTINCT ON (pec.profile_master_id, sl.universe_id)
            pec.profile_master_id,
            sl.universe_id
        FROM matrix__email_splio.splio_list AS sl
        JOIN matrix__email.profiles_email_consents AS pec ON sl.email_consent_id = pec.email_consent_id
        WHERE pec.status = 'sub';

    {% else %}

        -- Init new profiles universe mapping for pmi with first event
        INSERT INTO matrix__email_splio.profiles_universes
            (email_profile_master_id, universe_id)
        SELECT DISTINCT ON (profile_master_id, universe_id)
            profile_master_id, sl.universe_id
        FROM matrix__email_tmp.profile_and_consent AS tmp_pec
        JOIN matrix__email_splio.splio_list AS sl ON sl.email_consent_id = tmp_pec.email_consent_id
        WHERE profile_email_consent_to_create = true AND status = 'sub'
        ON CONFLICT DO NOTHING;

        -- DEAL WITH Already existed profiles in mapping table
        WITH already_existed_pmi AS (
          SELECT DISTINCT ON (profile_master_id) profile_master_id
          FROM matrix__email_tmp.profile_and_consent AS tmp_pec
          WHERE profile_email_consent_to_create = false
          OR (profile_email_consent_to_create = true AND status = 'unsub')
        )
        DELETE FROM matrix__email_splio.profiles_universes AS ref
        WHERE ref.email_profile_master_id IN (SELECT profile_master_id FROM already_existed_pmi);

        -- re-create mapping for already_existed_pmi
        WITH already_existed_pmi AS (
          SELECT DISTINCT ON (profile_master_id) profile_master_id
          FROM matrix__email_tmp.profile_and_consent AS tmp_pec
          WHERE profile_email_consent_to_create = false
          OR (profile_email_consent_to_create = true AND status = 'unsub')
        )
        INSERT INTO matrix__email_splio.profiles_universes
            (email_profile_master_id, universe_id)
        SELECT DISTINCT ON (pec.profile_master_id, sl.universe_id)
            pec.profile_master_id,
            sl.universe_id
        FROM already_existed_pmi AS ref
        JOIN matrix__email.profiles_email_consents AS pec ON ref.profile_master_id = pec.profile_master_id
        JOIN matrix__email_splio.splio_list AS sl ON sl.email_consent_id = pec.email_consent_id
        WHERE pec.status = 'sub'
        ON CONFLICT DO NOTHING;

    {% endif %}

    -- -- -- -- -- -- -- -- -- -- -- --
COMMIT;