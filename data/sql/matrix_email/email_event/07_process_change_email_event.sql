-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

BEGIN;
    -- ################################################################################################
    -- ################################################################################################
    --  change_email
    -- ################################################################################################

    WITH matched_events AS (
      SELECT ee.id, cee.source_email, cee.destination_email
      FROM matrix__email_queue.email_event ee
      JOIN minority_report.change_email_event cee
        ON cee.source_email = lower(trim(ee.payload->>'source_email'))
        AND cee.destination_email = lower(trim(ee.payload->>'destination_email'))
      WHERE ee.type = 'change_email'
        AND ee.id > {{ task_instance.xcom_pull(task_ids='save_chunk_ids', key ='start_id') | int }}
        AND ee.id <= {{ task_instance.xcom_pull(task_ids='save_chunk_ids', key ='end_id') | int }}
        AND DATE(ee.create_date) >= DATE(cee.update_date) - 1
        AND cee.email_event_id IS NULL
    )
    UPDATE minority_report.change_email_event cee
    SET email_event_id = me.id
    FROM matched_events me
    WHERE cee.source_email = me.source_email
      AND cee.destination_email = me.destination_email;

    -- ------------------------------------------------------------------------------------------------
    -- Deal with change email events to attach profile_master_id
    -- ------------------------------------------------------------------------------------------------
    -- Get profile_master_id for change_email event (for old and new email)
    UPDATE matrix__email_tmp.profile_master_id AS tmp
    SET profile_master_id = ref.profile_master_id
    FROM minority_report.change_email_event AS ref
    WHERE tmp.event_id = ref.email_event_id;


    -- change_email : set profile_master_id for source email
    UPDATE matrix__email_tmp.profile_master_id AS tmp
    SET profile_master_id = ref.profile_master_id
    FROM matrix__email_tmp.profile_master_id AS ref
    JOIN minority_report.change_email_event AS source_email ON ref.event_id = source_email.email_event_id
    WHERE tmp.email = lower(trim(source_email.source_email))
       OR tmp.email_md5 = substring(matrix__email_queue.digest(lower(matrix__email_queue.unaccent(trim(source_email.source_email))), 'md5')::varchar(255), 3, 32)
       OR tmp.email_sha256 = substring(matrix__email_queue.digest(lower(trim(source_email.source_email)), 'sha256')::varchar(255), 3, 64);

    -- change_email : set profile_master_id for destination email
    UPDATE matrix__email_tmp.profile_master_id AS tmp
    SET profile_master_id = ref.profile_master_id
    FROM matrix__email_tmp.profile_master_id AS ref
    JOIN minority_report.change_email_event AS dest_email ON ref.event_id = dest_email.email_event_id
    WHERE tmp.email = lower(trim(dest_email.destination_email))
       OR tmp.email_md5 = substring(matrix__email_queue.digest(lower(matrix__email_queue.unaccent(trim(dest_email.destination_email))), 'md5')::varchar(255), 3, 32)
       OR tmp.email_sha256 = substring(matrix__email_queue.digest(lower(trim(dest_email.destination_email)), 'sha256')::varchar(255), 3, 64);

    -- remove profile_master_id for source_email AFTER change_email event !
    -- if there is a 'sub' after on 'source_email' email after change, it creates a new profile
    UPDATE matrix__email_tmp.profile_master_id AS tmp
    SET profile_master_id = NULL
    FROM matrix__email_tmp.profile_master_id AS ref
    JOIN minority_report.change_email_event AS source_email ON ref.event_id = source_email.email_event_id
    WHERE tmp.event_date > ref.event_date
      -- make sure we do not already have this id, to not force recreating it
      AND NOT EXISTS (
        SELECT 1 FROM matrix__email.profile_master_id AS pmi
        WHERE pmi.id IS NULL
      )
      AND (tmp.email = lower(trim(source_email.source_email))
           OR tmp.email_md5 = substring(matrix__email_queue.digest(lower(matrix__email_queue.unaccent(trim(source_email.source_email))), 'md5')::varchar(255), 3, 32)
           OR tmp.email_sha256 = substring(matrix__email_queue.digest(lower(trim(source_email.source_email)), 'sha256')::varchar(255), 3, 64));

    -- -- remove profile_master_id for destination_email BEFORE change_email event !
    -- -- IMPORTANT : this must never happened, as minority-report forget it ! => comment this
    -- UPDATE matrix__email_tmp.profile_master_id AS tmp
    -- SET profile_master_id = NULL
    -- FROM matrix__email_tmp.profile_master_id AS ref
    -- JOIN minority_report.change_email_event AS dst_email ON ref.event_id = dst_email.email_event_id
    -- WHERE tmp.event_date < ref.event_date
    --   AND (tmp.email = lower(dst_email.destination_email)
    --        OR tmp.email_md5 = substring(matrix__email_queue.digest(lower(matrix__email_queue.unaccent(dst_email.destination_email)), 'md5')::varchar(255), 3, 32)
    --        OR tmp.email_sha256 = substring(matrix__email_queue.digest(lower(dst_email.destination_email), 'sha256')::varchar(255), 3, 64));

    -- ------------------------------------------------------------------------------------------------
    -- DO change_email
    -- ------------------------------------------------------------------------------------------------
    -- ------------------------------------------------------------------------------------------------
    -- Load data and enrich it
    -- ------------------------------------------------------------------------------------------------
    -- get info from old payload format with ev=99
    INSERT INTO matrix__email_tmp.change_email
        (event_id, profile_master_id, email_src, email_dst, update_date)
    SELECT id,
        null::bigint,
        lower(trim(payload->>'source')),
        lower(trim(payload->>'destination')),
        CAST(payload->>'d' AS timestamptz)
    FROM matrix__email_queue.email_event AS ref
    WHERE ref.id > {{ task_instance.xcom_pull(task_ids='save_chunk_ids', key ='start_id') | int }}
      AND ref.id <= {{ task_instance.xcom_pull(task_ids='save_chunk_ids', key ='end_id') | int }}
      AND type = 'change_email'
        AND CAST(payload->>'ev' AS integer) = 99
    UNION ALL
    SELECT id,
        null::bigint,
        lower(trim(payload->>'source_email')),
        lower(trim(payload->>'destination_email')),
        CAST(payload->>'request_date' AS timestamptz)
    FROM matrix__email_queue.email_event AS ref
    WHERE ref.id > {{ task_instance.xcom_pull(task_ids='save_chunk_ids', key ='start_id') | int }}
      AND ref.id <= {{ task_instance.xcom_pull(task_ids='save_chunk_ids', key ='end_id') | int }}
      AND type = 'change_email'
        AND CAST(payload->>'ev' AS integer) > 99;

    -- Load data from profile_master_id
    UPDATE matrix__email_tmp.change_email AS tmp
    SET profile_master_id = ref.profile_master_id
    FROM matrix__email_tmp.profile_master_id AS ref
    WHERE tmp.event_id = ref.event_id;

    -- remove event without profile_master_id (dealt in 01_profile_master_id.sql)
    DELETE FROM matrix__email_tmp.change_email
    WHERE profile_master_id IS NULL;

    -- ------------------------------------------------------------------------------------------------
    -- Already existent email destination
    -- ------------------------------------------------------------------------------------------------

    -- mark already existent email_dst
    -- @fixme : if we redo the email_event journal, we will get this error !
    UPDATE matrix__email_tmp.change_email AS tmp
    SET email_dst_exists = true,
        dst_profile_master_id = pmi.id,
        dst_has_part_consent = CASE WHEN ec.id IS NOT NULL THEN true ELSE false END
    FROM matrix__email.profile_master_id AS pmi
    LEFT JOIN matrix__email.profiles_email_consents AS pec
        ON pmi.id = pec.profile_master_id
        AND pec.status = 'sub'
    LEFT JOIN karinto.email_consent AS ec
        ON ec.id = pec.email_consent_id
        AND ec.type = 'part'
    WHERE pmi.email = tmp.email_dst;

    -- save error
    INSERT INTO matrix__email.email_event_error (code, payload, email_event_id)
    SELECT 401, ('{"reason": "Cannot change email \"' || email_src || '\" to \"' || email_dst || '\", as destination email already exists."}')::json, event_id
    FROM matrix__email_tmp.change_email AS tmp
    WHERE tmp.email_dst_exists = true
        AND dst_has_part_consent = false;

    -- remove email_dst already exist
    DELETE FROM matrix__email_tmp.change_email
    WHERE email_dst_exists = true
        AND dst_has_part_consent = false;

    -- ------------------------------------------------------------------------------------------------
    -- Create events in change_email_sub_unsub_event
    -- ------------------------------------------------------------------------------------------------
    -- snapshot the profile consents into change_email_sub_unsub_event. We create a 'unsub' event
    -- on the source email, and a 'sub' event on the destination to simulate 'change_email' on external
    -- systems which do not support 'change_email' event.

    -- create unsub event for email_src associated to change_email event
    WITH data AS (
        SELECT ce.event_id AS event_id,
             e.create_date AS create_date,
             lower(trim(ce.email_src)) AS email,
             substring(matrix__email_queue.digest(lower(trim(ce.email_src)), 'sha256')::varchar(255), 3, 64) AS email_sha256,
             substring(matrix__email_queue.digest(lower(matrix__email_queue.unaccent(trim(ce.email_src))), 'md5')::varchar(255), 3, 32) AS email_md5,
             pec.email_consent_id AS email_consent_id,
             pec.brand_id AS brand_id,
             ce.profile_master_id AS profile_master_id
        FROM matrix__email_tmp.change_email AS ce
        JOIN matrix__email_queue.email_event AS e ON ce.event_id = e.id
        JOIN matrix__email.profiles_email_consents AS pec ON pec.profile_master_id = ce.profile_master_id
        JOIN karinto.brand AS br ON br.id = pec.brand_id
        WHERE pec.status = 'sub'
    ), simulated_unsub_events AS (
        SELECT
            data.event_id,
            data.create_date,
            'unsub'::matrix__email_queue.email_event_type,
            data.email_sha256,
            data.email,
            CAST('{'
                || '"source": "change email event",'
                || '"medium": "process email_event - unsub on email_src",'
                || '"brand_ids": [' || STRING_AGG(DISTINCT CAST(data.brand_id AS varchar), ',') || '],'
                || '"profile_master_id": ' || CAST(data.profile_master_id AS varchar) || ','
                || '"email_md5": "' || data.email_md5 || '",'
                || '"email_sha256": "' || data.email_sha256 || '",'
                || '"consent_ids": [' || STRING_AGG(CAST(data.email_consent_id AS varchar), ',') || ']'
                || '}' AS json) AS payload
        FROM data
        GROUP BY 1, 2, 4, 5, data.profile_master_id, data.email_md5
    ), simulated_sub_events AS (
        -- create 'sub' events for each previously 'unsub' generated events, one second after the 'unsub'.
        SELECT
            tmp_unsub.event_id,
            tmp_unsub.create_date + interval '1 second',
            'sub'::matrix__email_queue.email_event_type,
            substring(matrix__email_queue.digest(lower(trim(ce.email_dst)), 'sha256')::varchar(255), 3, 64),
            ce.email_dst,
            CAST('{'
                || '"source": "change email event",'
                || '"medium": "process email_event - sub on email_dst",'
                || '"brand_ids": ' || CAST(tmp_unsub.payload->'brand_ids' AS varchar) || ','
                || '"profile_master_id": ' || CAST(tmp_unsub.payload->'profile_master_id' AS varchar) || ','
                || '"consent_ids": ' || CAST(tmp_unsub.payload->'consent_ids' AS varchar)
                || '}' AS json)
        FROM simulated_unsub_events AS tmp_unsub
        JOIN matrix__email_tmp.change_email AS ce
            ON tmp_unsub.event_id = ce.event_id
    )
    INSERT INTO matrix__email_tmp.simulated_sub_unsub_event
        (email_event_id, create_date, type, email_hash, email, payload)
    SELECT * FROM simulated_unsub_events
    UNION ALL
    SELECT * FROM simulated_sub_events
    ;

    -- ------------------------------------------------------------------------------------------------
    -- Do the change in matrix_email.profile_master_id
    -- ------------------------------------------------------------------------------------------------
    UPDATE matrix__email.profile_master_id AS ref
    SET email = lower(trim(tmp.email_dst)),
        email_md5 = substring(matrix__email_queue.digest(lower(matrix__email_queue.unaccent(trim(tmp.email_dst))), 'md5')::varchar(255), 3, 32),
        email_sha256 = substring(matrix__email_queue.digest(lower(trim(tmp.email_dst)), 'sha256')::varchar(255), 3, 64),
        update_date = tmp.update_date
    FROM matrix__email_tmp.change_email AS tmp
    WHERE tmp.profile_master_id = ref.id
    AND tmp.email_dst_exists = FALSE;


    -- GET profile_master_id from change_email destination
    UPDATE matrix__email_tmp.profile_master_id AS pmi_tmp
    SET profile_master_id = pmi.id
    FROM matrix__email.profile_master_id AS pmi
    JOIN matrix__email_tmp.change_email AS ce
        ON ce.profile_master_id = pmi.id
    WHERE pmi_tmp.profile_master_id IS NULL
    AND pmi_tmp.email = pmi.email;


COMMIT;