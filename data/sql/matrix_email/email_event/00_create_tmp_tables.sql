-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}


-- Create temporary working table
CREATE TABLE IF NOT EXISTS matrix__email_tmp.email_event_sequence (
    LIKE matrix__email.email_event_sequence
);
ALTER TABLE matrix__email_tmp.email_event_sequence OWNER TO matrix_email;

CREATE TABLE IF NOT EXISTS matrix__email_tmp.profile_master_id (
    event_id bigint,
    profile_master_id bigint,
    email_md5 varchar(32),
    email_sha256 varchar(64),
    email varchar(120),
    create_date timestamptz,
    event_date timestamptz,
    type matrix__email_queue.email_event_type
);
ALTER TABLE matrix__email_tmp.profile_master_id OWNER TO matrix_email;

CREATE TABLE IF NOT EXISTS matrix__email_tmp.profiles_to_anonymise (
    event_id bigint,
    type matrix__email_queue.email_event_type,
    profile_master_id bigint,
    brand_id integer,
    create_date timestamp
);
ALTER TABLE matrix__email_tmp.profiles_to_anonymise OWNER TO matrix_email;


CREATE TABLE IF NOT EXISTS matrix__email_queue.email_event_with_bad_hash
(
    email_event_id bigint NOT NULL,
    execution_id varchar(250),
    create_date timestamp with time zone NOT NULL default now(),
    CONSTRAINT email_event_with_bad_hash_pk PRIMARY KEY (email_event_id)
);
ALTER TABLE matrix__email_queue.email_event_with_bad_hash OWNER TO matrix_email;

CREATE TABLE IF NOT EXISTS matrix__email_tmp.profile_master_id (
    event_id bigint,
    profile_master_id bigint,
    email_md5 varchar(32),
    email_sha256 varchar(64),
    email varchar(120),
    create_date timestamptz,
    event_date timestamptz,
    type matrix__email_queue.email_event_type
);
ALTER TABLE matrix__email_tmp.profile_master_id OWNER TO matrix_email;
--- index on email and create_date

CREATE TABLE IF NOT EXISTS matrix__email_tmp.change_email (
    event_id bigint,

    profile_master_id bigint,

    email_src varchar(120),
    email_dst varchar(120),

    -- does the destination email exist ??
    email_dst_exists boolean default false,
    -- identify destination profile pmi id
    dst_profile_master_id bigint default NULL,

    -- check if destination email hash sub to part consents
    dst_has_part_consent boolean default false,

    update_date timestamptz
);
ALTER TABLE matrix__email_tmp.change_email OWNER TO matrix_email;

CREATE TABLE IF NOT EXISTS matrix__email_tmp.simulated_sub_unsub_event (
    email_event_id bigint NOT NULL,
    create_date timestamp with time zone NOT NULL,
    type matrix__email_queue.email_event_type NOT NULL,
    email_hash varchar(64) NOT NULL,
    email varchar(120),
    payload json,
    to_insert boolean default true
);
ALTER TABLE matrix__email_tmp.simulated_sub_unsub_event OWNER TO matrix_email;
--- index on email, create_date, email_event_id

CREATE TABLE IF NOT EXISTS matrix__email_tmp.blacklist (
    event_id bigint,
    type matrix__email_queue.email_event_type,

    profile_master_id bigint,
    brand_id integer,

    source varchar(50),
    payload json,
    create_date timestamptz,
    to_insert boolean default true
);
ALTER TABLE matrix__email_tmp.blacklist OWNER TO matrix_email;
--- index on profile_master_id, brand_id, create_date

CREATE TABLE IF NOT EXISTS matrix__email_tmp.deleted (
    event_id bigint,
    profile_master_id bigint,
    source varchar(50),
    payload json,
    create_date timestamptz,
    to_delete boolean default false
);
ALTER TABLE matrix__email_tmp.deleted OWNER TO matrix_email;

CREATE TABLE IF NOT EXISTS matrix__email_tmp.profile_and_consent (
    -- matrix__email_queue.email_event.id
    event_id bigint,
    -- matrix__email.profile_master_id.id
    profile_master_id  bigint,
    -- consent public_ref (see karinto.email_consent)
    public_ref varchar(150) default null,
    -- karinto.email_consent.id
    email_consent_id integer,
    -- karinto.email_consent.brand_id
    brand_id integer,
    -- sub or unsub
    status matrix__email_queue.email_event_type NOT NULL,

    -- profile data
    firstname varchar(50),
    lastname varchar(50),
    gender varchar(4),
    birthdate date,
    address_line1 varchar(100),
    address_line2 varchar(100),
    postal_code varchar(18),
    city varchar(200),
    country varchar(90),
    source varchar(100),

    -- creation date of the profile is the date of the older event for this profile
    profile_create_date timestamp with time zone,
    -- creation date of the 'sub' event
    profile_email_consent_create_date timestamp with time zone,

    -- required to update pec table
    update_date timestamp with time zone NOT NULL,

    -- do we have to create a profile
    profile_to_create boolean DEFAULT true,
    -- do we have to create rows in profiles_email_consents
    profile_email_consent_to_create boolean DEFAULT true,

    -- is the profile blacklisted ?
    profile_blacklisted boolean DEFAULT false
);
ALTER TABLE matrix__email_tmp.profile_and_consent OWNER TO matrix_email;
--- index on profile_master_id, brand_id, email_consent_id, update_date, event_id

CREATE TABLE IF NOT EXISTS matrix__email_tmp.profiles_to_clean (
    event_id bigint,
    type matrix__email_queue.email_event_type,
    profile_master_id bigint,
    brand_id integer,
    create_date timestamp
);
ALTER TABLE matrix__email_tmp.profiles_to_clean OWNER TO matrix_email;

CREATE TABLE IF NOT EXISTS matrix__email_tmp.sub_interactions (
    ext_id varchar(50),
    pandora_event_id bigint,
    provider varchar(50),
    event varchar(50),
    event_date timestamp with time zone NOT NULL,
    source_date timestamp with time zone NOT NULL,
    source_type varchar(50),
    source_id varchar(50),
    channel varchar(50),
    channel_details_id varchar(50),
    channel_details_name varchar(150),
    individual_id varchar(120),
    context varchar(120),
    partner varchar(120),
    universe_id integer
);
ALTER TABLE matrix__email_tmp.sub_interactions OWNER TO matrix_email;

CREATE TABLE IF NOT EXISTS matrix__email_tmp.pandora_sub_interactions (
    ext_id varchar(50),
    provider varchar(50),
    event varchar(50),
    event_date timestamp with time zone NOT NULL,
    source_type varchar(50),
    source_id varchar(50),
    source_date  timestamp with time zone NOT NULL,
    channel varchar(50),
    channel_details_id varchar(50),
    channel_details_name varchar(150),
    individual_id varchar(120),
    universe_id integer
);
ALTER TABLE matrix__email_tmp.pandora_sub_interactions OWNER TO matrix_email;

CREATE TABLE IF NOT EXISTS matrix__email_tmp.sub_acq_organique_interaction (
    ext_id varchar(50),
    provider varchar(50),
    event varchar(50),
    event_date timestamp with time zone NOT NULL,
    source_type varchar(50),
    source_id varchar(50),
    source_date  timestamp with time zone NOT NULL,
    channel varchar(50),
    channel_details_id varchar(50),
    channel_details_name varchar(150),
    individual_id varchar(120),
    universe_id integer
);
ALTER TABLE matrix__email_tmp.sub_acq_organique_interaction OWNER TO matrix_email;


CREATE TABLE IF NOT EXISTS matrix__email_tmp.change_email_interactions (
    ext_id varchar(50),
    pandora_event_id bigint,
    provider varchar(50),
    event varchar(50),
    event_date timestamp with time zone NOT NULL,
    source_date timestamp with time zone NOT NULL,
    source_type varchar(50),
    source_id varchar(50),
    channel varchar(50),
    channel_details_id varchar(50),
    channel_details_name varchar(150),
    individual_id varchar(120),
    universe_id integer
);
ALTER TABLE matrix__email_tmp.change_email_interactions OWNER TO matrix_email;

CREATE TABLE IF NOT EXISTS matrix__email_tmp.splio_sub_unsub_event (
    -- event
    event_id bigint,
    email varchar(120),  -- needed to distinct on email, and deal with change_email event

    -- matrix__email
    profile_master_id  bigint,  -- used to get profile information
    brand_id integer,           -- used to get the right brand profile information
    email_consent_id integer,   -- for list mapping
    status matrix__email_queue.email_event_type,
    update_date timestamptz     -- get the last event
);
ALTER TABLE matrix__email_tmp.splio_sub_unsub_event OWNER TO matrix_email;
--- index profile_master_id, email, email_consent_id, update_date, event_id

CREATE TABLE IF NOT EXISTS matrix__email_tmp.splio_universe_mapping (
    email varchar(120),  -- needed to distinct on email, and deal with change_email event
    -- matrix__email
    profile_master_id  bigint,  -- used to get profile info
    brand_id integer,           -- .. and the right brand profile info
    email_consent_id integer,   -- for splio list mapping
    status matrix__email_queue.email_event_type,
    update_date timestamptz,
    -- splio data
    universe_id integer,        -- which universe will receive the profile ?
    list_num integer,           -- which list will be modified
    one_sub varchar,             -- splio version of the event, ie +0, -1, ...
    enriched boolean default false -- true if enriched universe else false
);
ALTER TABLE matrix__email_tmp.splio_universe_mapping OWNER TO matrix_email;

CREATE TABLE IF NOT EXISTS matrix__email_tmp.splio_datahub_data (
    -- splio info
    universe_id integer,
    subscriptions varchar,

    -- event datas
    email varchar(120),         -- needed to distinct on email, and deal with change_email event
    profile_master_id  bigint,  -- used to get profile info
    brand_id integer,           -- .. and the right brand profile info
    date_update timestamptz,

    -- profile master data
    --email varchar(120),
    email_md5 varchar(32),
    email_sha256 varchar(64),

    -- profile data
    firstname varchar(50),
    lastname varchar(50),
    civ varchar(4),
    date_naissance date,
    annee_naissance varchar(4),
    code_postal varchar(18),
    departement varchar(2),
    ville varchar(200)
);
ALTER TABLE matrix__email_tmp.splio_datahub_data OWNER TO matrix_email;

TRUNCATE TABLE matrix__email_tmp.profile_master_id;
TRUNCATE TABLE matrix__email_tmp.change_email;
TRUNCATE TABLE matrix__email_tmp.simulated_sub_unsub_event;
TRUNCATE TABLE matrix__email_tmp.blacklist;
TRUNCATE TABLE matrix__email_tmp.deleted;
TRUNCATE TABLE matrix__email_tmp.profile_and_consent;
TRUNCATE TABLE matrix__email_tmp.profiles_to_clean;
TRUNCATE TABLE matrix__email_tmp.sub_interactions;
TRUNCATE TABLE matrix__email_tmp.pandora_sub_interactions;
TRUNCATE TABLE matrix__email_tmp.sub_acq_organique_interaction;
TRUNCATE TABLE matrix__email_tmp.change_email_interactions;
TRUNCATE TABLE matrix__email_splio.profiles_universes;
TRUNCATE TABLE matrix__email_tmp.splio_sub_unsub_event;
-- use to generate splio univers and list mapping for each event
TRUNCATE TABLE matrix__email_tmp.splio_universe_mapping;
-- use to create the datahub data to export
TRUNCATE TABLE matrix__email_tmp.splio_datahub_data;
