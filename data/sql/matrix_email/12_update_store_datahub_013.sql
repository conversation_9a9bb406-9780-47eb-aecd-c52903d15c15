-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DROP TABLE IF EXISTS `store.datahub_013_pmc_ads_previous`;

ALTER TABLE IF EXISTS `store.datahub_013_pmc_ads`
RENAME TO `datahub_013_pmc_ads_previous`;

-- prepare new store table
DROP TABLE IF EXISTS `store.datahub_013_pmc_ads`;
CREATE TABLE IF NOT EXISTS `store.datahub_013_pmc_ads`(
  uuid                                  STRING     OPTIONS(description="Email uuid"),
  email                                 STRING      OPTIONS(description="Full email"),
  -- datahub 013 infos
  pmc_personalized_ads                  STRING      OPTIONS(description="Has <PERSON><PERSON>  disabled the ads targeting ?"),
  universe_id                           INTEGER     OPTIONS(description="Id universe"),
  fingerprint                           INTEGER     OPTIONS(description="Row fingerprint to compare with previous version")
)
OPTIONS(description="Synchronise refuse of ad targeting of PMC profile to Nl universe. Destroyed and Created every day.\n\n"
                 || "DAG: {{ dag.dag_id }}, TASK: {{ task.task_id }} \n"
                 || "Sync: daily");

INSERT INTO `store.datahub_013_pmc_ads`
(uuid, email, pmc_personalized_ads, universe_id, fingerprint)
SELECT uuid, email, pmc_personalized_ads, universe_id, fingerprint
FROM `prepare.datahub_013_pmc_ads_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}` AS pre;




