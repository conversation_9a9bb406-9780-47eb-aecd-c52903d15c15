-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
/*
-- source vue structure: pixel_tracking_logs.click_pmc

http_host	STRING	NULLABLE
click_date	TIMESTAMP	NULLABLE
message_id	STRING	NULLABLE
client_hash	STRING	NULLABLE
session_id	STRING	NULLABLE
start_time	STRING	NULL<PERSON>LE
end_time	STRING	NULLABLE

*/
DROP TABLE IF EXISTS `prepare.pmc_redirect_event_{{next_execution_date.strftime("%Y-%m-%d_%H%M")}}`;
CREATE TABLE `prepare.pmc_redirect_event_{{next_execution_date.strftime("%Y-%m-%d_%H%M")}}`
AS SELECT
       cm.http_host,
       cm.message_id,
       cm.client_hash,
       cm.session_id,
       cm.start_time,
       cm.end_time,
       cm.click_date,
       pmi.id  AS profile_master_id,
       CASE
           WHEN cm.http_host LIKE "click-pmc.%.bifr-news.fr" THEN "BIN"
           WHEN cm.http_host LIKE "click-pmc.%.caminteresse-news.fr" THEN "CAM"
           WHEN cm.http_host LIKE "click-pmc.%.capital-news.fr" THEN "CAP"
           WHEN cm.http_host LIKE "click-pmc.%.cuisine-news.fr" THEN "CAC"
           WHEN cm.http_host LIKE "click-pmc.%.femmeactuelle-news.fr" THEN "FAC"
           WHEN cm.http_host LIKE "click-pmc.%.gala-news.fr" THEN "GAL"
           WHEN cm.http_host LIKE "click-pmc.%.gentside-news.fr" THEN "GEN"
           WHEN cm.http_host LIKE "click-pmc.%.geo-mag.fr" THEN "GEO"
           WHEN cm.http_host LIKE "click-pmc.%.hbrfrance-news.fr" THEN "HBR"
           WHEN cm.http_host LIKE "click-pmc.%.listen-news.fr" THEN "LIS"
           WHEN cm.http_host LIKE "click-pmc.%.neon-news.fr" THEN "NEO"
           WHEN cm.http_host LIKE "click-pmc.%.ohmymag-news.fr" THEN "OMM"
           WHEN cm.http_host LIKE "click-pmc.%.tv-news.fr" THEN "TEL"
           WHEN cm.http_host LIKE "click-pmc.%.t2s-news.fr" THEN "T2S"
           WHEN cm.http_host LIKE "click-pmc.%.voici-news.fr" THEN "VOI"
           WHEN cm.http_host LIKE "click-pmc.%.prismaconnect-gestion.fr" THEN "CIL"
           ELSE "OTHER"
           END AS trigram
FROM `{{params.tracking_project}}.pixel_tracking_logs.click_pmc` AS cm
JOIN `store_matrix_email.profile_master_id` AS pmi
    ON char_length(cm.client_hash) = 64 AND cm.client_hash = email_sha256
WHERE cm.click_date > CURRENT_TIMESTAMP() - INTERVAL {{params.interval}}
;
-- INTERVAL 2 DAY:  5.93 GB
-- INTERVAL 1 DAY:  4.59 GB
-- INTERVAL 4 HOUR: 3.21 GB