-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
WITH FLATTEN_FULL_USER as (
    SELECT
    profile_master_id,
    domain.http_host,
    consent.public_id,
    consent.datetime_open,
    consent.datetime_click
    FROM  `{{params.table_prepare_user}}_{{next_execution_date.strftime("%Y-%m-%d_%H%M")}}`,
    UNNEST(domain) domain,
    UNNEST(domain.consent) consent
),

GROUP_BY_USER_PREPARE AS (
  SELECT
      profile_master_id,
      http_host,
      public_id,
      max(if(type='open',datetime,null)) datetime_open,
      max(if(type='click',datetime,null)) datetime_click
  FROM
      `{{params.table_store_full_import}}`
  group by profile_master_id,
      http_host,
      public_id
),

UNION_FLATTEN_AND_PREPARE as (
    SELECT * FROM FLATTEN_FULL_USER
    UNION ALL
    SELECT * FROM GROUP_BY_USER_PREPARE
),

GROUP_BY_HOST as (SELECT
    profile_master_id, http_host,
    ARRAY_AGG(STRUCT(public_id,datetime_open,datetime_click)) as consent,
    max(datetime_open) datetime_host_open,
    max(datetime_click) datetime_host_click
FROM
    UNION_FLATTEN_AND_PREPARE
GROUP BY
    profile_master_id,http_host)

SELECT
    profile_master_id,
    ARRAY_AGG(STRUCT(http_host, datetime_host_open, datetime_host_click, consent)) as domain,
    max(datetime_host_open) datetime_user_open,
    max(datetime_host_click) datetime_user_click
FROM
    GROUP_BY_HOST
GROUP BY
    profile_master_id