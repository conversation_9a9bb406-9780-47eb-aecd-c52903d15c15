-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `pm-prod-matrix.store_tracking.splio_targets` (
    profile_master_id            INT64       OPTIONS(description="PMI from store_matrix_email.profile_master_id"),
    universe                     STRING      OPTIONS(description="The universe name (ref: store_karinto.universe.name)"),
    date                         TIMESTAMP   OPTIONS(description="File reception timestamp"),
    shoot_date                   TIMESTAMP   OPTIONS(description="Shoot time"),
    rogue_one_email_id           INT64       OPTIONS(description="Identifier for the rogue one email"),
    campaign_id                  STRING      OPTIONS(description="Campaign reference")
) PARTITION BY DATE(date)
  OPTIONS (
    description="Table storing Splio targets for tracking store activities.\n"
             || "\n\n"
             || "DAG: {{dag.dag_id}}."
             || "Sync: daily."
);

DELETE FROM `pm-prod-matrix.store_tracking.splio_targets`
WHERE CAST(date AS STRING) like '{{ ds_nodash }}%';

INSERT INTO `pm-prod-matrix.store_tracking.splio_targets`

    SELECT
       profile_master_id,
       universe,
       date,
       shoot_date,
       rogue_one_email_id,
       campaign_id
    FROM `pm-prod-matrix.prepare.tracking_splio_targets_{{ ds_nodash }}`;
