-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_tracking.splio_report_smtp`(
    id                  INTEGER     OPTIONS(description="Unique identifier for the report entry (if applicable)."),
    univers             STRING      OPTIONS(description="Universe or category to which the report belongs."),
    canal               STRING      OPTIONS(description="Channel associated with the report (e.g., SMTP)."),
    reference           STRING      OPTIONS(description="Reference identifier for the campaign or message."),
    jour                DATE        OPTIONS(description="The specific date to which the report statistics refer."),
    sujet               STRING      OPTIONS(description="Subject line of the email message."),
    premier_mail        TIMESTAMP   OPTIONS(description="Timestamp of the first email sent for this report period/campaign."),
    dernier_mail        TIMESTAMP   OPTIONS(description="Timestamp of the last email sent for this report period/campaign."),
    nb_total            INTEGER     OPTIONS(description="Total number of emails initially targeted or processed."),
    nb_bloques          INTEGER     OPTIONS(description="Number of emails blocked before sending."),
    nb_livres           INTEGER     OPTIONS(description="Number of emails successfully delivered."),
    nb_ouverts          INTEGER     OPTIONS(description="Total number of email opens (can include multiple opens by one user)."),
    nb_ouverts_unique   INTEGER     OPTIONS(description="Number of unique recipients who opened the email at least once."),
    nb_clicks           INTEGER     OPTIONS(description="Total number of clicks on links within the email (can include multiple clicks by one user)."),
    nb_clickers         INTEGER     OPTIONS(description="Number of unique recipients who clicked at least one link."),
    nb_soft             INTEGER     OPTIONS(description="Number of soft bounces (temporary delivery failures)."),
    nb_hard             INTEGER     OPTIONS(description="Number of hard bounces (permanent delivery failures)."),
    nb_desabo           INTEGER     OPTIONS(description="Number of recipients who unsubscribed via this email."),
    nb_spam             INTEGER     OPTIONS(description="Number of spam complaints received."),
    nb_achats           INTEGER     OPTIONS(description="Number of purchases attributed to this email/campaign."),
    total_ca            FLOAT64     OPTIONS(description="Total revenue (Chiffre d'Affaires) attributed to this email/campaign.")
)
PARTITION BY jour
OPTIONS(
    description="Aggregated daily statistics for Splio SMTP mailings.\n" ||
                "DAG: {{ dag.dag_id }}.\n" ||
                "Sync: daily"
);

MERGE INTO `{{ params.bq_project }}.store_tracking.splio_report_smtp` AS T
USING `{{ params.bq_project }}.import.tracking_splio_smtp_report_data_{{ ds_nodash }}` AS S
ON
    T.id = S.id
    AND T.univers = S.univers
    AND T.reference = S.reference
    AND T.jour = S.jour

WHEN MATCHED THEN
    UPDATE SET
        T.canal = S.canal,
        T.sujet = S.sujet,
        T.premier_mail = S.premier_mail,
        T.dernier_mail = S.dernier_mail,
        T.nb_total = S.nb_total,
        T.nb_bloques = S.nb_bloques,
        T.nb_livres = S.nb_livres,
        T.nb_ouverts = S.nb_ouverts,
        T.nb_ouverts_unique = S.nb_ouverts_unique,
        T.nb_clicks = S.nb_clicks,
        T.nb_clickers = S.nb_clickers,
        T.nb_soft = S.nb_soft,
        T.nb_hard = S.nb_hard,
        T.nb_desabo = S.nb_desabo,
        T.nb_spam = S.nb_spam,
        T.nb_achats = S.nb_achats,
        T.total_ca = S.total_ca

-- Action when a row from the source does not exist in the target
WHEN NOT MATCHED BY TARGET THEN
    INSERT (
        id,
        univers,
        canal,
        reference,
        jour,
        sujet,
        premier_mail,
        dernier_mail,
        nb_total,
        nb_bloques,
        nb_livres,
        nb_ouverts,
        nb_ouverts_unique,
        nb_clicks,
        nb_clickers,
        nb_soft,
        nb_hard,
        nb_desabo,
        nb_spam,
        nb_achats,
        total_ca
    )
    VALUES (
        S.id,
        S.univers,
        S.canal,
        S.reference,
        S.jour,
        S.sujet,
        S.premier_mail,
        S.dernier_mail,
        S.nb_total,
        S.nb_bloques,
        S.nb_livres,
        S.nb_ouverts,
        S.nb_ouverts_unique,
        S.nb_clicks,
        S.nb_clickers,
        S.nb_soft,
        S.nb_hard,
        S.nb_desabo,
        S.nb_spam,
        S.nb_achats,
        S.total_ca
    );
