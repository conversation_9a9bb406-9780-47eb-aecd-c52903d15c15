-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
-- Update report_id from temporary table
UPDATE matrix__email_tmp.splio_report_import AS tmp 
    SET report_id = ref.id, ref_create_date = ref.create_date 
FROM matrix__email_splio.report as ref 
WHERE tmp.sendout_id = ref.sendout_ref 
  AND tmp.message_id = ref.message_ref 
  AND tmp.campaign_id = ref.campaign_ref;

-- Delete outdated row
DELETE
FROM matrix__email_tmp.splio_report_import
WHERE create_date < ref_create_date;

-- Create temporary table matrix__email_tmp.splio_report
DROP TABLE IF EXISTS matrix__email_tmp.splio_report;
CREATE TABLE matrix__email_tmp.splio_report (
    report_id bigint,
    sendout_ref varchar(10) NOT NULL,
    campaign_ref varchar(10) NOT NULL,
    message_ref varchar(10) NOT NULL,
    rogue_one_email_id bigint,
    starttime timestamp with time zone NOT NULL,
    endtime timestamp with time zone NOT NULL,
    create_date timestamp with time zone NOT NULL DEFAULT now(),
    update_date timestamp with time zone NOT NULL DEFAULT now(),
    targets integer,
    sent integer,
    delivered integer,
    soft_bounce integer,
    hard_bounces integer,
    clickers integer,
    openers integer,
    opens integer,
    clicks integer,
    spam_complaints integer,
    unsubscribes integer,
    universe_id bigint
);
ALTER TABLE matrix__email_tmp.splio_report OWNER TO matrix_email;

-- Import data from matrix__email_tmp.splio_report_import into matrix__email_tmp.splio_report
INSERT INTO matrix__email_tmp.splio_report
(report_id, sendout_ref, campaign_ref, message_ref, rogue_one_email_id, starttime, endtime,
 targets, sent, delivered, soft_bounce, hard_bounces, clickers, openers, opens, clicks, spam_complaints,
 unsubscribes, universe_id)
SELECT
    report_id, sendout_id, campaign_id, message_id, rogue_one_email_id,
    TO_TIMESTAMP(tmp.data->>'starttime', 'YYYY-MM-DD HH24:MI:SS') AS starttime,
    CASE WHEN tmp.data->>'endtime' IS NULL OR tmp.data->>'endtime' = ''
        THEN TO_TIMESTAMP(tmp.data->>'starttime', 'YYYY-MM-DD HH24:MI:SS') 
        ELSE TO_TIMESTAMP(tmp.data->>'endtime', 'YYYY-MM-DD HH24:MI:SS') 
    END AS endtime, 
    CAST(tmp.data->'recipients'->>'total' AS integer) AS targets,
    CAST(tmp.data->'recipients'->>'sent' AS integer) AS sent,
    CAST(tmp.data->'recipients'->>'delivered' AS integer) AS delivered,
    CAST(tmp.data->'recipients'->>'soft bounces' AS integer) AS soft_bounce,
    CAST(tmp.data->'recipients'->>'hard bounces' AS integer) AS hard_bounces,
    CAST(tmp.data->'recipients'->>'clickers' AS integer) AS clickers, 
    CAST(tmp.data->'recipients'->>'openers' AS integer) AS openers, 
    CAST(tmp.data->'reactions'->>'opens' AS integer) AS opens, 
    CAST(tmp.data->'reactions'->>'clicks' AS integer) AS clicks,
    CAST(tmp.data->'reactions'->>'spam complaints' AS integer) AS spam_complaints,
    CAST(tmp.data->'reactions'->>'unsubscribes' AS integer) AS unsubscribes,
   universe_id
FROM matrix__email_tmp.splio_report_import AS tmp;

-- Insert matrix__email_tmp.splio_report into matrix__email_spliot.report
INSERT INTO matrix__email_splio.report 
(sendout_ref, campaign_ref, message_ref, rogue_one_email_id, starttime, endtime, targets, sent, delivered, 
  soft_bounce, hard_bounces, clickers, openers, opens, clicks, spam_complaints, unsubscribes,universe_id)
SELECT sendout_ref, campaign_ref, message_ref, rogue_one_email_id, starttime, endtime, targets, sent, delivered, 
       soft_bounce, hard_bounces, clickers, openers, opens, clicks, spam_complaints, unsubscribes, universe_id
FROM matrix__email_tmp.splio_report AS tmp
WHERE report_id IS NULL;

-- Update matrix__email_spliot.report from matrix__email_tmp.splio_report
UPDATE matrix__email_splio.report
SET
    sendout_ref = tmp.sendout_ref,
    campaign_ref = tmp.campaign_ref,
    message_ref = tmp.message_ref,
    rogue_one_email_id = tmp.rogue_one_email_id,
    starttime = tmp.starttime,
    endtime = tmp.endtime,
    targets = tmp.targets,
    sent = tmp.sent,
    delivered = tmp.delivered,
    soft_bounce = tmp.soft_bounce,
    hard_bounces = tmp.hard_bounces,
    clickers = tmp.clickers,
    openers = tmp.openers,
    opens = tmp.opens,
    clicks = tmp.clicks,
    spam_complaints = tmp.spam_complaints,
    unsubscribes = tmp.unsubscribes,
    universe_id = tmp.universe_id
FROM matrix__email_tmp.splio_report AS tmp
WHERE id = tmp.report_id;

-- Create temporary matrix__email_tmp.splio_report_data 
DROP TABLE IF EXISTS matrix__email_tmp.splio_report_data;
CREATE TABLE matrix__email_tmp.splio_report_data(
    report_id bigint NOT NULL,
    data json NOT NULL,
    action_type varchar 
);
ALTER TABLE matrix__email_tmp.splio_report_data OWNER TO matrix_email;

-- Import data from matrix__email_tmp.splio_report_import into matrix__email_tmp.splio_report_data
INSERT INTO matrix__email_tmp.splio_report_data (report_id, data, action_type)
    SELECT r.id AS report_id, sri.data,
        CASE WHEN sri.report_id is NULL THEN 'insert'
             ELSE 'update'
        END 
    FROM matrix__email_tmp.splio_report_import AS sri
        JOIN matrix__email_splio.report AS r ON r.sendout_ref = sri.sendout_id 
            AND r.campaign_ref = sri.campaign_id
            AND r.message_ref = sri.message_id;
            
-- Insert ...
INSERT INTO matrix__email_splio.report_data (report_id, data)
    SELECT report_id, data
    FROM matrix__email_tmp.splio_report_data
    WHERE action_type = 'insert';

-- Update ...
UPDATE matrix__email_splio.report_data
SET data = sri.data
FROM matrix__email_tmp.splio_report_data AS sri
WHERE action_type = 'update'
    AND report_data.report_id = sri.report_id;

-- Create temporary table matrix__email_tmp.splio_report_link
DROP TABLE IF EXISTS matrix__email_tmp.splio_report_link;
CREATE TABLE matrix__email_tmp.splio_report_link(
    report_id bigint NOT NULL,
    link_id smallint NOT NULL,
    clicks integer,
    url varchar NOT NULL,
    name varchar(100),
    category varchar(100),
    action_type varchar
);
ALTER TABLE matrix__email_tmp.splio_report_link OWNER TO matrix_email;

-- Import ...
INSERT INTO matrix__email_tmp.splio_report_link (report_id, link_id, clicks, url, name, category, action_type)
    WITH links AS (
        SELECT id AS report_id, sendout_id, campaign_id, message_id, json_object_keys(data->'links') AS link_id,
            CASE WHEN report_id is NULL THEN 'insert'
                 ELSE 'update'
            END as action_type
         FROM matrix__email_tmp.splio_report_import 
            JOIN matrix__email_splio.report ON campaign_ref = campaign_id 
                AND sendout_id = sendout_ref 
                AND message_id = message_ref
    )
    SELECT links.report_id,
           CAST(link_id AS integer),
           CAST(data->'links'->link_id->>'clicks' AS integer) AS clicks,
           data->'links'->link_id->>'url' AS url,
           data->'links'->link_id->>'name' AS name,
           data->'links'->link_id->>'category' AS category,
           action_type
    FROM links
        JOIN matrix__email_tmp.splio_report_import AS sri ON links.sendout_id = sri.sendout_id
            AND links.campaign_id = sri.campaign_id
            AND links.sendout_id = sri.sendout_id;

-- Insert ...
INSERT INTO matrix__email_splio.report_link (report_id, link_id, clicks, url, name, category)
    SELECT report_id, link_id, clicks, url, name, category
    FROM matrix__email_tmp.splio_report_link
    WHERE action_type = 'insert';

-- Update ...
UPDATE matrix__email_splio.report_link 
SET
    clicks = srl.clicks,
    url = srl.url, 
    name = srl.name, 
    category = srl.category
FROM matrix__email_tmp.splio_report_link AS srl
WHERE action_type = 'update'
    AND report_link.report_id = srl.report_id 
    AND report_link.link_id = srl.link_id;