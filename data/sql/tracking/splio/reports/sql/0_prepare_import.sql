-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
DELETE FROM matrix__email_splio.report WHERE create_date < NOW() - INTERVAL '7 days';

DROP TABLE IF EXISTS matrix__email_tmp.splio_report_import;
CREATE TABLE matrix__email_tmp.splio_report_import
(
	universe_id BIGINT NOT NULL,
	create_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
	sendout_id varchar(10) NOT NULL,
	campaign_id varchar(10) NOT NULL,
	message_id varchar(10) NOT NULL,
	data json,
	report_id BIGINT DEFAULT NULL,
	rogue_one_email_id BIGINT DEFAULT NULL,
	ref_create_date  TIMESTAMP WITH TIME ZONE DEFAULT NULL,
	UNIQUE (sendout_id, campaign_id, message_id)
);
ALTER TABLE matrix__email_tmp.splio_report_import OWNER TO matrix_email;