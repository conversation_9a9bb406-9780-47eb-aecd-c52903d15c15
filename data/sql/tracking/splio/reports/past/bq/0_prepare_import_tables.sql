-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- <PERSON>reate combined raw import table for all chunks
CREATE OR REPLACE TABLE `{{ params.bq_project }}.import.splio_report_raw_{{ ds_nodash }}`
(
  universe_id INT64 NOT NULL,
  sendout_id STRING NOT NULL,
  campaign_id STRING NOT NULL,
  message_id STRING NOT NULL,
  api_data STRING NOT NULL,
  rogue_one_email_id INT64,
  create_timestamp TIMESTAMP NOT NULL,
  chunk_id STRING
);

-- Create processing tables
CREATE OR REPLACE TABLE `{{ params.bq_project }}.prepare.splio_report_{{ ds_nodash }}`
(
  id INT64,
  sendout_id STRING NOT NULL,
  campaign_id STRING NOT NULL,
  message_id STRING NOT NULL,
  rogue_one_email_id INT64,
  starttime TIMESTAMP NOT NULL,
  endtime TIMESTAMP NOT NULL,
  create_date TIMESTAMP NOT NULL,
  update_date TIMESTAMP NOT NULL,
  targets INT64,
  sent INT64,
  delivered INT64,
  soft_bounce INT64,
  hard_bounces INT64,
  clickers INT64,
  openers INT64,
  opens INT64,
  clicks INT64,
  spam_complaints INT64,
  unsubscribes INT64,
  universe_id INT64 NOT NULL,
  action STRING
);

CREATE OR REPLACE TABLE `{{ params.bq_project }}.prepare.splio_report_data_{{ ds_nodash }}`
(
  report_id INT64 NOT NULL,
  data STRING NOT NULL,
  action STRING
);

CREATE OR REPLACE TABLE `{{ params.bq_project }}.prepare.splio_report_link_{{ ds_nodash }}`
(
  report_id INT64 NOT NULL,
  link_id INT64 NOT NULL,
  clicks INT64,
  url STRING NOT NULL,
  name STRING,
  category STRING,
  action STRING
);