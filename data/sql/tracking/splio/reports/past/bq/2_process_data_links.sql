-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- Process report data (JSON payload) using REAL IDs from store table
-- This mirrors the PostgreSQL logic: JOIN to store table to get real IDs
CREATE OR REPLACE TABLE `{{ params.bq_project }}.prepare.splio_report_data_{{ ds_nodash }}` AS
SELECT
  store_report.id AS report_id,  -- REAL ID from store table!
  raw.api_data AS data,
  CASE 
    WHEN prep.action = 'insert' THEN 'insert'
    ELSE 'update'
  END AS action
FROM `{{ params.bq_project }}.import.splio_report_raw_{{ ds_nodash }}` AS raw
JOIN `{{ params.bq_project }}.prepare.splio_report_{{ ds_nodash }}` AS prep
  ON raw.sendout_id = prep.sendout_id
  AND raw.campaign_id = prep.campaign_id
  AND raw.message_id = prep.message_id
JOIN `{{ params.bq_project }}.store_tracking.splio_report` AS store_report
  ON raw.sendout_id = store_report.sendout_ref
  AND raw.campaign_id = store_report.campaign_ref
  AND raw.message_id = store_report.message_ref;

-- Process report links using REAL IDs from store table
CREATE OR REPLACE TABLE `{{ params.bq_project }}.prepare.splio_report_link_{{ ds_nodash }}` AS
WITH link_data AS (
  SELECT
    store_report.id AS report_id,  -- REAL ID from store table!
    SAFE_CAST(link_key AS INT64) AS link_id,
    SAFE_CAST(
      REGEXP_EXTRACT(
        raw.api_data, 
        CONCAT('"', link_key, '":\\s*{[^}]*"clicks":\\s*([0-9]+)')
      ) AS INT64
    ) AS clicks,
    REGEXP_EXTRACT(
      raw.api_data, 
      CONCAT('"', link_key, '":\\s*{[^}]*"url":\\s*"([^"]*)"')
    ) AS url,
    REGEXP_EXTRACT(
      raw.api_data, 
      CONCAT('"', link_key, '":\\s*{[^}]*"name":\\s*"([^"]*)"')
    ) AS name,
    REGEXP_EXTRACT(
      raw.api_data, 
      CONCAT('"', link_key, '":\\s*{[^}]*"category":\\s*"([^"]*)"')
    ) AS category,
    CASE 
      WHEN prep.action = 'insert' THEN 'insert'
      ELSE 'update'
    END AS action
  FROM `{{ params.bq_project }}.import.splio_report_raw_{{ ds_nodash }}` AS raw
  JOIN `{{ params.bq_project }}.prepare.splio_report_{{ ds_nodash }}` AS prep
    ON raw.sendout_id = prep.sendout_id
    AND raw.campaign_id = prep.campaign_id
    AND raw.message_id = prep.message_id
  JOIN `{{ params.bq_project }}.store_tracking.splio_report` AS store_report
    ON raw.sendout_id = store_report.sendout_ref
    AND raw.campaign_id = store_report.campaign_ref
    AND raw.message_id = store_report.message_ref
  CROSS JOIN UNNEST(REGEXP_EXTRACT_ALL(raw.api_data, r'"links":\s*{\s*"([^"]+)"')) AS link_key
  WHERE JSON_EXTRACT(raw.api_data, '$.links') IS NOT NULL
    AND SAFE_CAST(link_key AS INT64) IS NOT NULL
)
SELECT 
  report_id,
  link_id,
  clicks,
  url,
  name,
  category,
  action
FROM link_data
WHERE url IS NOT NULL;

-- Insert new report data (now with real IDs!)
INSERT INTO `{{ params.bq_project }}.store_tracking.splio_report_data`
(report_id, data)
SELECT DISTINCT report_id, data
FROM `{{ params.bq_project }}.prepare.splio_report_data_{{ ds_nodash }}`
WHERE action = 'insert';

-- Update existing report data
UPDATE `{{ params.bq_project }}.store_tracking.splio_report_data` AS target
SET data = source.data
FROM (
  SELECT DISTINCT report_id, data FROM `{{ params.bq_project }}.prepare.splio_report_data_{{ ds_nodash }}`
  WHERE action = 'update'
) AS source
WHERE target.report_id = source.report_id;

-- Insert new report links (now with real IDs!)
INSERT INTO `{{ params.bq_project }}.store_tracking.splio_report_link`
(report_id, link_id, clicks, url, name, category)
SELECT DISTINCT report_id, link_id, clicks, url, name, category
FROM `{{ params.bq_project }}.prepare.splio_report_link_{{ ds_nodash }}`
WHERE action = 'insert';

-- Update existing report links
UPDATE `{{ params.bq_project }}.store_tracking.splio_report_link` AS target
SET 
  clicks = source.clicks,
  url = source.url,
  name = source.name,
  category = source.category
FROM (
  SELECT DISTINCT report_id, link_id, clicks, url, name, category 
  FROM `{{ params.bq_project }}.prepare.splio_report_link_{{ ds_nodash }}`
  WHERE action = 'update'
) AS source
WHERE target.report_id = source.report_id 
  AND target.link_id = source.link_id;