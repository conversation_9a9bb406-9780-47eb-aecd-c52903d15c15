-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- Clean up temporary tables
DROP TABLE IF EXISTS `{{ params.bq_project }}.import.splio_report_raw_{{ ds_nodash }}`;
DROP TABLE IF EXISTS `{{ params.bq_project }}.prepare.splio_report_{{ ds_nodash }}`;
DROP TABLE IF EXISTS `{{ params.bq_project }}.prepare.splio_report_data_{{ ds_nodash }}`;
DROP TABLE IF EXISTS `{{ params.bq_project }}.prepare.splio_report_link_{{ ds_nodash }}`;

-- Clean up individual chunk tables using wildcard
FOR chunk_table IN (
  SELECT CONCAT('`{{ params.bq_project }}.import.', table_name, '`') as table_name
  FROM `{{ params.bq_project }}.import.INFORMATION_SCHEMA.TABLES`
  WHERE table_name LIKE 'splio_report_raw_%_{{ ds_nodash }}'
    AND table_name != 'splio_report_raw_{{ ds_nodash }}'
)
DO
  EXECUTE IMMEDIATE CONCAT('DROP TABLE IF EXISTS ', chunk_table.table_name);
END FOR;