-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- Combine all chunk data into single raw table using wildcard matching
CREATE OR REPLACE TABLE `{{ params.bq_project }}.import.splio_report_raw_{{ ds_nodash }}` AS
SELECT 
  universe_id,
  sendout_id,
  campaign_id,
  message_id,
  api_data,
  rogue_one_email_id,
  create_timestamp,
  REGEXP_EXTRACT(_TABLE_SUFFIX, r'(\d+)_{{ ds_nodash }}$') as chunk_id
FROM `{{ params.bq_project }}.import.splio_report_raw_*`
WHERE _TABLE_SUFFIX LIKE '%{{ ds_nodash }}';

-- Process JSON data into structured format for reports only
CREATE OR REPLACE TABLE `{{ params.bq_project }}.prepare.splio_report_{{ ds_nodash }}` AS
SELECT
  CAST(NULL AS INT64) AS id,
  raw.sendout_id,
  raw.campaign_id,
  raw.message_id,
  raw.rogue_one_email_id,
  
  -- Parse timestamps from JSON
  PARSE_TIMESTAMP('%Y-%m-%d %H:%M:%S', JSON_VALUE(raw.api_data, '$.starttime')) AS starttime,
  COALESCE(
    PARSE_TIMESTAMP('%Y-%m-%d %H:%M:%S', JSON_VALUE(raw.api_data, '$.endtime')),
    PARSE_TIMESTAMP('%Y-%m-%d %H:%M:%S', JSON_VALUE(raw.api_data, '$.starttime'))
  ) AS endtime,
  
  raw.create_timestamp AS create_date,
  CURRENT_TIMESTAMP() AS update_date,
  
  -- Extract metrics from JSON with safe casting
  SAFE_CAST(JSON_VALUE(raw.api_data, '$.recipients.total') AS INT64) AS targets,
  SAFE_CAST(JSON_VALUE(raw.api_data, '$.recipients.sent') AS INT64) AS sent,
  SAFE_CAST(JSON_VALUE(raw.api_data, '$.recipients.delivered') AS INT64) AS delivered,
  SAFE_CAST(JSON_VALUE(raw.api_data, '$."recipients"."soft bounces"') AS INT64) AS soft_bounce,
  SAFE_CAST(JSON_VALUE(raw.api_data, '$."recipients"."hard bounces"') AS INT64) AS hard_bounces,
  SAFE_CAST(JSON_VALUE(raw.api_data, '$.recipients.clickers') AS INT64) AS clickers,
  SAFE_CAST(JSON_VALUE(raw.api_data, '$.recipients.openers') AS INT64) AS openers,
  SAFE_CAST(JSON_VALUE(raw.api_data, '$.reactions.opens') AS INT64) AS opens,
  SAFE_CAST(JSON_VALUE(raw.api_data, '$.reactions.clicks') AS INT64) AS clicks,
  SAFE_CAST(JSON_VALUE(raw.api_data, '$."reactions"."spam complaints"') AS INT64) AS spam_complaints,
  SAFE_CAST(JSON_VALUE(raw.api_data, '$.reactions.unsubscribes') AS INT64) AS unsubscribes,
  
  raw.universe_id,
  CAST(NULL AS STRING) AS action
FROM `{{ params.bq_project }}.import.splio_report_raw_{{ ds_nodash }}` AS raw;

-- Update with existing report IDs and determine action
UPDATE `{{ params.bq_project }}.prepare.splio_report_{{ ds_nodash }}` AS prep
SET 
  id = existing.id,
  action = 'update'
FROM `{{ params.bq_project }}.store_tracking.splio_report` AS existing
WHERE prep.sendout_id = existing.sendout_ref
  AND prep.campaign_id = existing.campaign_ref
  AND prep.message_id = existing.message_ref;

-- Set action for new records
UPDATE `{{ params.bq_project }}.prepare.splio_report_{{ ds_nodash }}`
SET action = 'insert'
WHERE action IS NULL;

-- Insert new reports (deduplicated) with auto-generated IDs
INSERT INTO `{{ params.bq_project }}.store_tracking.splio_report`
(id, sendout_ref, campaign_ref, message_ref, rogue_one_email_id, starttime, endtime,
 create_date, update_date, targets, sent, delivered, soft_bounce, hard_bounces,
 clickers, openers, opens, clicks, spam_complaints, unsubscribes, universe_id)
SELECT DISTINCT
  -- Generate sequential IDs starting from max existing ID + 1
  (SELECT COALESCE(MAX(id), 0) FROM `{{ params.bq_project }}.store_tracking.splio_report`) + 
  ROW_NUMBER() OVER (ORDER BY sendout_id, campaign_id, message_id) AS id,
  sendout_id, campaign_id, message_id, rogue_one_email_id, starttime, endtime,
  create_date, update_date, targets, sent, delivered, soft_bounce, hard_bounces,
  clickers, openers, opens, clicks, spam_complaints, unsubscribes, universe_id
FROM `{{ params.bq_project }}.prepare.splio_report_{{ ds_nodash }}`
WHERE action = 'insert';

-- Update existing reports (deduplicated source)
UPDATE `{{ params.bq_project }}.store_tracking.splio_report` AS target
SET 
  sendout_ref = source.sendout_id,
  campaign_ref = source.campaign_id,
  message_ref = source.message_id,
  rogue_one_email_id = source.rogue_one_email_id,
  starttime = source.starttime,
  endtime = source.endtime,
  create_date = source.create_date,
  update_date = source.update_date,
  targets = source.targets,
  sent = source.sent,
  delivered = source.delivered,
  soft_bounce = source.soft_bounce,
  hard_bounces = source.hard_bounces,
  clickers = source.clickers,
  openers = source.openers,
  opens = source.opens,
  clicks = source.clicks,
  spam_complaints = source.spam_complaints,
  unsubscribes = source.unsubscribes,
  universe_id = source.universe_id
FROM (
  SELECT DISTINCT * FROM `{{ params.bq_project }}.prepare.splio_report_{{ ds_nodash }}`
  WHERE action = 'update'
) AS source
WHERE target.id = source.id;