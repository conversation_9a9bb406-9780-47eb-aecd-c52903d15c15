-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
-- Generate prisma snapshot
DROP TABLE IF EXISTS `store_matrix_email.prisma_snapshot`
;
CREATE TABLE `store_matrix_email.prisma_snapshot` AS
SELECT profile_master_id, consent_status, consent_public_ref, email_consent_id
FROM `store_matrix_email.prisma_snapshot_latest`
;

DROP TABLE IF EXISTS `store_matrix_email.prisma_snapshot_latest`
;
CREATE TABLE `store_matrix_email.prisma_snapshot_latest` AS
SELECT pmi.id AS profile_master_id, consent_status, public_ref AS consent_public_ref, ec.id AS email_consent_id
FROM `pm-prod-matrix.store_matrix_email.profile_master_id` AS pmi
JOIN `pm-prod-matrix.store_matrix_email.profile` AS p ON p.profile_master_id = pmi.id
JOIN `pm-prod-matrix.store_matrix_email.profiles_email_consents` as pec ON pec.profile_master_id = p.profile_master_id
JOIN `pm-prod-matrix.store_karinto.email_consent` AS ec ON pec.email_consent_id = ec.id
    AND pec.brand_id = p.brand_id
;
