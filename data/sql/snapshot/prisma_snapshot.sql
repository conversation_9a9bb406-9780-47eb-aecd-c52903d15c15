-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
-- Generate prisma snapshot

 CREATE TABLE IF NOT EXISTS `pm-prod-matrix.store_matrix_email.prisma_snapshot_previous_d1`
    (
        profile_master_id   INT64    OPTIONS(description="email profile master id"),
        consent_status      STRING    OPTIONS(description="consent status at the time of the snapshot"),
        consent_public_ref  STRING    OPTIONS(description="type of consent"),
        email_consent_id    INT64     OPTIONS(description="id of the consentement"),
        update_date         TIMESTAMP OPTIONS(description="update date of either the profile, pec, or pmi"),
        subscription_date   TIMESTAMP OPTIONS(description="date of the subcription")
    )
    PARTITION BY DATE(update_date)
    CLUSTER BY profile_master_id, email_consent_id
    OPTIONS(description="This table is a snapshot of our base status for yesterday. "||
                        "It's a daily table. "||
                        "DAG: {{ dag.dag_id }}. "||
                        "Sync: daily");

 CREATE TABLE IF NOT EXISTS `pm-prod-matrix.store_matrix_email.prisma_snapshot`
    (
        profile_master_id   INT64    OPTIONS(description="email profile master id"),
        consent_status      STRING    OPTIONS(description="consent status at the time of the snapshot"),
        consent_public_ref  STRING    OPTIONS(description="type of consent"),
        email_consent_id    INT64     OPTIONS(description="id of the consentement"),
        update_date         TIMESTAMP OPTIONS(description="update date of either the profile, pec, or pmi"),
        subscription_date   TIMESTAMP OPTIONS(description="date of the subcription")
    )
    PARTITION BY DATE(update_date)
    CLUSTER BY profile_master_id, email_consent_id
    OPTIONS(description="This table is a snapshot of our base status for today. "||
                        "It's a daily table. "||
                        "DAG: {{ dag.dag_id }}. "||
                        "Sync: daily");

 CREATE TABLE IF NOT EXISTS `pm-prod-matrix.store_matrix_email.prisma_snapshot_previous_d2`
    (
        profile_master_id   INT64    OPTIONS(description="email profile master id"),
        consent_status      STRING    OPTIONS(description="consent status at the time of the snapshot"),
        consent_public_ref  STRING    OPTIONS(description="type of consent"),
        email_consent_id    INT64     OPTIONS(description="id of the consentement"),
        update_date         TIMESTAMP OPTIONS(description="update date of either the profile, pec, or pmi"),
        subscription_date   TIMESTAMP OPTIONS(description="date of the subcription")
    )
    PARTITION BY DATE(update_date)
    CLUSTER BY profile_master_id, email_consent_id
    OPTIONS(description="This table is a snapshot of our base status for the day before yesterday. "||
                        "It's a daily table. "||
                        "DAG: {{ dag.dag_id }}. "||
                        "Sync: daily");



-- Deal with snapshot J-2 :

TRUNCATE TABLE `pm-prod-matrix.store_matrix_email.prisma_snapshot_previous_d2`;

INSERT INTO `pm-prod-matrix.store_matrix_email.prisma_snapshot_previous_d2`
SELECT profile_master_id, consent_status, consent_public_ref, email_consent_id, update_date,subscription_date
FROM `pm-prod-matrix.store_matrix_email.prisma_snapshot_previous_d1`
;

-- Deal with snapshot J-1 :

TRUNCATE TABLE `pm-prod-matrix.store_matrix_email.prisma_snapshot_previous_d1`;

INSERT INTO `pm-prod-matrix.store_matrix_email.prisma_snapshot_previous_d1`
SELECT profile_master_id, consent_status, consent_public_ref, email_consent_id, update_date,subscription_date
FROM `pm-prod-matrix.store_matrix_email.prisma_snapshot`
;

-- Deal with snapshot J :

TRUNCATE TABLE `pm-prod-matrix.store_matrix_email.prisma_snapshot`;

INSERT INTO `pm-prod-matrix.store_matrix_email.prisma_snapshot`
SELECT pmi.id AS profile_master_id, consent_status, public_ref AS consent_public_ref, ec.id AS email_consent_id,
       (select max(x) from UNNEST([p.update_date, pec.update_date, pmi.update_date]) as x) as update_date,
       pec.create_date as subscription_date
FROM `pm-prod-matrix.store_matrix_email.profile_master_id` AS pmi
JOIN `pm-prod-matrix.store_matrix_email.profile` AS p ON p.profile_master_id = pmi.id
JOIN `pm-prod-matrix.store_matrix_email.profiles_email_consents` as pec ON pec.profile_master_id = p.profile_master_id
JOIN `pm-prod-matrix.store_karinto.email_consent` AS ec ON pec.email_consent_id = ec.id
    AND pec.brand_id = p.brand_id;