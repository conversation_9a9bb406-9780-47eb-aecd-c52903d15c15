-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DROP TABLE IF EXISTS `{{ params.bq_project }}.datastudio.heatmap_clicks_nl_kpi`;

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.datastudio.heatmap_clicks_nl_kpi` (
    start_date                DATE       NOT NULL    OPTIONS(description="Start date for the NL campaign"),
    rogue_one_email_id        INTEGER                OPTIONS(description="Rogue-One ID"),
    consent_public_ref        STRING                 OPTIONS(description="Email consent public ref"),
    block_master_id           INTEGER                OPTIONS(description="Block position"),
    block_type                STRING                 OPTIONS(description="Block type : Header, Body, Footer, ..."),
    url                       STRING                 OPTIONS(description="Destination URL"),
    url_type                  STRING                 OPTIONS(description="URL type as enum=['content','ads']"),
    url_target                STRING                 OPTIONS(description="URL type as enum=['site_<brand_trigram>','powerspace','ividence',...']"),
    total_clicks              INTEGER                OPTIONS(description="Click volume"),
    cumul_clicks              INTEGER                OPTIONS(description="Cumulative click volume from the first block of the NL to this block"),
    median_clicks             INTEGER                OPTIONS(description="Median click volume"),
    block_id_median           INTEGER                OPTIONS(description="Block that corresponds to the border between 0%-50% clicks and 50%-100% clicks")
)
PARTITION BY start_date
OPTIONS(
    description="This table contains details of click performance on newsletters by block position and by block category" ||
                "Dashboard : \n" ||
                "- LookerStudio : https://lookerstudio.google.com/u/0/reporting/d28ff128-8374-474a-85c1-d24801949ebd \n" ||
                "- Atlas : ... \n" ||
                "" ||
                "Sync: Daily. DAG: {{ dag.dag_id }}"
);


-- The blocks must be built.
-- This means grouping successive URLs (or not) which are, within the newsletter, one and the same block.
-- These blocks can be of the editorial, advertising content type, ...
INSERT INTO `{{ params.bq_project }}.datastudio.heatmap_clicks_nl_kpi` (
    WITH clicks_per_shoot AS (
      SELECT
        *,
        SUM(total_clicks) OVER (PARTITION BY consent_public_ref,rogue_one_email_id) AS total_clicks_rogue_one
      FROM `{{ params.bq_project }}.generated_data.heatmap_clicks_nl`
    ),

    cumulative_clicks AS (
      SELECT
        *,
        SUM(total_clicks) OVER (PARTITION BY consent_public_ref, rogue_one_email_id ORDER BY block_master_id) AS cumul_clicks,
        CAST(ROUND(total_clicks_rogue_one/2, 0) AS INT64) AS median_clicks
      FROM clicks_per_shoot
    )

    SELECT
        DATE(start_date) AS start_date,
        rogue_one_email_id,
        consent_public_ref,
        block_master_id,
        block_type,
        url,
        url_type,
        url_target,
        total_clicks,
        cumul_clicks,
        median_clicks,
        MIN(IF(cumul_clicks >= median_clicks, block_master_id, NULL)) OVER (PARTITION BY rogue_one_email_id) AS block_id_median
    FROM cumulative_clicks
)