-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE start_date DATE DEFAULT DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL {{ params.interval }});
DECLARE end_date DATE DEFAULT DATE("{{ data_interval_end }}");

{% if params.is_full %}
SET start_date = DATE("{{ params.start_date }}");
{% endif %}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.datastudio.kpi_email_by_base`(
    observation_date           DATE      OPTIONS(description="date"),
    pole_name                  STRING    OPTIONS(description="pole name. ref: {{ params.bq_project }}.store_karinto.pole.name"),
    brand_trigram              STRING    OPTIONS(description="brand trigram. ref: {{ params.bq_project }}.store_karinto.brand.trigram"),
    email_consent_public_ref   STRING    OPTIONS(description="email_consent_public_ref. ref: {{ params.bq_project }}.refined_data.email_base"),
    sub_volume                 INTEGER   OPTIONS(description="volume of subscriber. ref: {{ params.bq_project }}.refined_data.email_event_sub_unsub"),
    unsub_volume               INTEGER   OPTIONS(description="volume of unsubscriber. ref: {{ params.bq_project }}.refined_data.email_event_sub_unsub"),
    sent_volume                INTEGER   OPTIONS(description="volume of email delivered. ref: {{ params.bq_project }}.refined_data.email_event_sent"),
    open_volume                INTEGER   OPTIONS(description="volume of openings. ref: {{ params.bq_project }}.refined_data.email_event_open"),
    opener_volume              INTEGER   OPTIONS(description="volume of unique openers. ref: {{ params.bq_project }}.refined_data.email_event_open"),
    click_volume               INTEGER   OPTIONS(description="volume of click. ref: {{ params.bq_project }}.refined_data.email_event_click"),
    clicker_volume             INTEGER   OPTIONS(description="volume of unique clickers. ref: {{ params.bq_project }}.refined_data.email_event_click")
)
PARTITION BY observation_date
OPTIONS(description="Compute volume of sub/unsub by base per date."
                  ||"\n"
                  ||"Then We compute volume of open/openers by base per date."
                  ||"\n"
                  ||"We compute also volume of click/clickers by base per date."
                  ||"\n\n"
                  ||"Datastudio: https://datastudio.google.com/reporting/e678a595-22a7-41e2-b5c6-ed7700bee809"
                  ||"\n\n"
                  ||"DAG: {{ dag.dag_id }}."
                  ||"\n\n"
                  ||"Sync: daily");

DELETE FROM `{{ params.bq_project }}.datastudio.kpi_email_by_base`
WHERE observation_date BETWEEN DATE(start_date) AND DATE(end_date);

INSERT INTO `{{ params.bq_project }}.datastudio.kpi_email_by_base`
WITH generate_dates AS(
    -- generate dates from start to end dates ! 
    SELECT 
        observation_date
    FROM UNNEST(GENERATE_DATE_ARRAY(start_date, end_date, INTERVAL 1 DAY)) AS observation_date
), sub_unsub_flag AS(
    -- flag sub/unsub events at observation date and consent
    SELECT  
        gd.observation_date, 
        eb.pole_name,
        eb.brand_trigram,
        email_consent_public_ref, 
        email_profile_master_id,
        (observation_date >= DATE(sub_date) AND unsub_date IS NULL) 
        OR (observation_date >= DATE(sub_date) AND unsub_date IS NOT NULL AND observation_date < DATE(unsub_date)) AS is_sub,
        (unsub_date IS NOT NULL AND observation_date >= DATE(unsub_date)) AS is_unsub
    FROM `{{ params.bq_project }}.generated_data.email_profile_lifecycle_by_base` AS eplcb, generate_dates AS gd
    JOIN `{{ params.bq_project }}.refined_data.email_base` AS eb ON eb.consent_public_ref = eplcb.email_consent_public_ref
    WHERE 
        eb.consent_type = "nl"
        AND eb.consent_is_active
        AND observation_date BETWEEN start_date AND end_date
    GROUP BY 1, 2, 3, 4, 5, 6, 7
), sub_unsub_count AS(
    -- count volume of sub & unsub subscriptions at observation date by consent
    SELECT 
        observation_date, 
        pole_name,
        brand_trigram,
        email_consent_public_ref,
        COUNT(DISTINCT CASE WHEN is_sub THEN email_profile_master_id ELSE NULL END)  AS sub_volume,
        COUNT(DISTINCT CASE WHEN is_unsub THEN email_profile_master_id ELSE NULL END) AS unsub_volume
    FROM sub_unsub_flag
    GROUP BY 1, 2, 3, 4
), sent_count AS(
    -- count delivered emails at observation date by consent
    SELECT 
        DATE(ees.sent_date) AS observation_date,
        eb.pole_name,
        eb.brand_trigram,
        ees.email_consent_public_ref,
        COUNT(*) AS sent_volume
    FROM `{{ params.bq_project }}.refined_data.email_event_sent` AS ees
    JOIN `{{ params.bq_project }}.refined_data.email_base` AS eb ON eb.consent_public_ref = ees.email_consent_public_ref
    WHERE 
        eb.consent_type = "nl"
        AND eb.consent_is_active
        -- keep only sent emails on period !
        AND (DATE(ees.sent_date) BETWEEN start_date AND end_date)
    GROUP BY 1, 2, 3, 4
), open_count AS (
    -- count opened/openers emails at observation date by consent
    SELECT 
        DATE(open_date) AS observation_date,
        eb.pole_name,
        eb.brand_trigram,
        email_consent_public_ref,
        COUNT(*) AS open_volume,
        COUNT(DISTINCT email_profile_master_id) AS opener_volume
    FROM `{{ params.bq_project }}.refined_data.email_event_open`
    JOIN `{{ params.bq_project }}.refined_data.email_base` AS eb ON eb.consent_public_ref = email_consent_public_ref
    WHERE 
        eb.consent_type = "nl"
        AND eb.consent_is_active
        -- keep only sent emails on period !
        AND (DATE(open_date) BETWEEN start_date AND end_date)
    GROUP BY 1, 2, 3, 4
), click_count AS (
    -- count clicked/clickers emails at observation date by consent
    SELECT 
        DATE(click_date) AS observation_date,
        eb.pole_name,
        eb.brand_trigram,
        email_consent_public_ref,
        COUNT(*) AS click_volume,
        COUNT(DISTINCT email_profile_master_id) AS clicker_volume
    FROM `{{ params.bq_project }}.refined_data.email_event_click`
    JOIN `{{ params.bq_project }}.refined_data.email_base` AS eb ON eb.consent_public_ref = email_consent_public_ref
    WHERE 
        eb.consent_type = "nl"
        AND eb.consent_is_active
        -- keep only sent emails on period !
        AND (DATE(click_date) BETWEEN start_date AND end_date)
    GROUP BY 1, 2, 3, 4
)
SELECT 
    suc.*,
    IFNULL(sc.sent_volume, 0) AS sent_volume, 
    IFNULL(oc.open_volume, 0) AS open_volume,
    IFNULL(oc.opener_volume, 0) AS opener_volume,
    IFNULL(cc.click_volume, 0) AS click_volume,
    IFNULL(cc.clicker_volume, 0) AS clicker_volume 
FROM sub_unsub_count AS suc
LEFT JOIN sent_count AS sc USING(observation_date, email_consent_public_ref)
LEFT JOIN open_count AS oc USING(observation_date, email_consent_public_ref)
LEFT JOIN click_count AS cc USING(observation_date, email_consent_public_ref)
ORDER BY suc.observation_date;
