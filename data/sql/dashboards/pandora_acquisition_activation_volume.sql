-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DROP TABLE IF EXISTS `{{ params.bq_project }}.datastudio.pandora_acquisition_activation_volume` ;
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.datastudio.pandora_acquisition_activation_volume`(
    sub_date           DATE       OPTIONS(description="Acquisition date on the context"),
    first_open_date    DATE       OPTIONS(description="First open date on the context"),
    is_activated       INTEGER    OPTIONS(description="Boolean indicating if profiles have opened an nl"),
    pole_name          STRING     OPTIONS(description="Pole name of the newsletter subscribed \n ref:{{ params.bq_project }}.store_karinto.pole"),
    brand_trigram      STRING     OPTIONS(description="Brand trigram of the newsletter subscribed \n ref:{{ params.bq_project }}.store_karinto.brand"),
    partner            STRING     OPTIONS(description="Partner of acquisition \n ref:{{ params.bq_project }}.refined_data.pandora_events"),
    lever              STRING     OPTIONS(description="Lever according to the context of acquisition \n ref:{{ params.bq_project }}.store_pandora.context.lever"),
    context_type       STRING     OPTIONS(description="Type of context of acquisition \n ref:{{ params.bq_project }}.refined_data.pandora_events"),
    context            STRING     OPTIONS(description="Context of acquisition \n ref:{{ params.bq_project }}.refined_data.pandora_events"),
    gender             STRING     OPTIONS(description="Gender from business_data.profile_digital_360 as enum=['Femme', 'Homme']"),
    age_bin            STRING     OPTIONS(description="Age bins from business_data.profile_digital_360 as enum=['0-17', '18-24', '25-34', '35-44', '45-54', '55-64', '65+']"),
    esp                STRING     OPTIONS(description="Domain from info.email business_data.profile_digital_360"),
    status             STRING     OPTIONS(description="Status of profiles, enum=['NEW', 'REA']"),
    volume             INTEGER    OPTIONS(description="Number of profiles sub or openers")
)
OPTIONS(description="Compute volume of pandora's activation after their first NL open/click by gender, age bin, ESP/domain, brand, partner, context by subscription and first open date."
                  ||"\n"
                  ||"We keep only open/click datetime on the 90 or 180 days after subscription datetime time frame."
                  ||"\n\n"
                  ||"This table is used in datastudio : Pandora - Suivi de l'activation:"
                  ||"\n"
                  ||"- Atlas prod: https://atlas.prismadata.fr/data-product/99-pandora-suivi-de-l-activation-sans-gala"
                  ||"\n"
                  ||"- Atlas prod: https://atlas.preprod.prismadata.fr/data-product/104-pandora-suivi-de-l-activation"
                  ||"\n\n"
                  ||"DAG: {{ dag.dag_id }}."
                  ||"\n\n"
                  ||"Sync: Daily.",
    labels=[
        ('owner', 'mozart'),
        ('dashboard_id', '99')
    ]);


INSERT INTO `{{ params.bq_project }}.datastudio.pandora_acquisition_activation_volume`
WITH declare_default_consent AS (
  SELECT
    "CAM" AS brand_trigram,
    "ca_minteresse_nl" AS default_consent_public_ref
  UNION ALL
    SELECT
      "CAP" AS brand_trigram,
      "capital_quotidienne_nl" AS default_consent_public_ref
  UNION ALL
    SELECT
      "CAC" AS brand_trigram,
      "cuisine_actuelle_quotidienne_nl" AS default_consent_public_ref
  UNION ALL
    SELECT
      "FAC" AS brand_trigram,
      "femme_actuelle_quotidienne_nl" AS default_consent_public_ref
  UNION ALL
    SELECT
      "GEN" AS brand_trigram,
      "gentside_buzz_nl" AS default_consent_public_ref
  UNION ALL
    SELECT
      "GEO" AS brand_trigram,
      "geo_quotidienne_nl" AS default_consent_public_ref
  UNION ALL
    SELECT
      "OMM" AS brand_trigram,
      "ohmymag_news_nl" AS default_consent_public_ref
  UNION ALL
    SELECT
      "T2S" AS brand_trigram,
      "tele_2_semaines_quotidienne_nl" AS default_consent_public_ref
  UNION ALL
    SELECT
      "TEL" AS brand_trigram,
      "tele_loisirs_buzz_nl" AS default_consent_public_ref
  UNION ALL
    SELECT
      "VOI" AS brand_trigram,
      "voici_quotidienne_nl" AS default_consent_public_ref
), simulate_consent_opens AS (
  -- Simulate consent for activation theme
  SELECT
      open_date,
      email_profile_master_id,
      CASE
        WHEN STARTS_WITH(o.theme, "activation-") THEN ddc.default_consent_public_ref
        ELSE o.email_consent_public_ref
        END AS email_consent_public_ref
  FROM `{{ params.bq_project }}.refined_data.email_event_open` AS o
  LEFT JOIN `{{ params.bq_project }}.refined_data.email_base_repository` AS ebr ON ebr.theme = o.theme
                                                                        AND STARTS_WITH(ebr.theme, "activation-")
  LEFT JOIN declare_default_consent AS ddc ON ddc.brand_trigram = ebr.brand_trigram
  GROUP BY ALL
), simulate_consent_clicks AS (
  -- Simulate consent for activation theme
  SELECT
      click_date,
      email_profile_master_id,
      CASE
        WHEN STARTS_WITH(c.theme, "activation-") THEN ddc.default_consent_public_ref
        ELSE c.email_consent_public_ref
        END AS email_consent_public_ref
  FROM `{{ params.bq_project }}.refined_data.email_event_click` AS c
  LEFT JOIN `{{ params.bq_project }}.refined_data.email_base_repository` AS ebr ON ebr.theme = c.theme
                                                                        AND STARTS_WITH(ebr.theme, "activation-")
  LEFT JOIN declare_default_consent AS ddc ON ddc.brand_trigram = ebr.brand_trigram
  GROUP BY ALL
), sub_profiles_init AS (
    -- get "NEW" Pandora events and (partner, context information)
    SELECT DISTINCT
        e.event_id,
        e.action.email.event_id                                             AS email_event_id,
        e.email_profile_master_id                                           AS profile_master_id,
        DATE(e.event_date)                                                  AS sub_date,
        IF(e.response.email.status = 'REO', 'REA', e.response.email.status) AS status,
        DATE(action.email.unsub_date)                                       AS unsub_date,
        action.email.is_cancelled,
        e.setup.context.name                                                AS context,
        e.setup.context.type                                                AS context_type,
        e.setup.partner.name                                                AS partner
    FROM `{{ params.bq_project }}.refined_data.pandora_events` AS e
    WHERE
        DATE(e.event_date) >= '2022-01-01'
        -- only NEW/REA events
        AND e.response.email.status IN ('NEW','REA','REO') -- REO means reoptin, it's the former name for REA
), email_base AS (
    SELECT
        event_id,
        email_base.pole_name,
        email_base.brand_trigram,
        IF(ENDS_WITH(email_base.consent_public_ref, "_shopping_part"), "capital_part", email_base.consent_public_ref) AS email_consent_public_ref
    FROM `{{ params.bq_project }}.refined_data.pandora_events` AS e,
         UNNEST(e.action.email.base) AS email_base
), sub_profiles AS (
    SELECT
        sp.*,
        eb.* EXCEPT(event_id)
    FROM sub_profiles_init AS sp
    LEFT JOIN email_base AS eb USING(event_id)
), first_activity_per_context AS (
    -- get first open/click after Pandora acquisition
    SELECT DISTINCT
        sp.*,
        MIN(DATE(o.open_date)) OVER (PARTITION BY email_event_id) AS first_open_date,
        MIN(DATE(c.click_date)) OVER (PARTITION BY email_event_id) AS first_click_date
    FROM sub_profiles AS sp
    LEFT JOIN simulate_consent_opens AS o
        ON o.open_date <= COALESCE(sp.unsub_date, DATE_ADD(sp.sub_date, INTERVAL 90 DAY))
        AND o.open_date >= sp.sub_date
        AND sp.profile_master_id = o.email_profile_master_id
        AND sp.email_consent_public_ref = o.email_consent_public_ref
    LEFT JOIN simulate_consent_clicks AS c
        ON c.click_date <= COALESCE(sp.unsub_date, DATE_ADD(sp.sub_date, INTERVAL 180 DAY))
        AND c.click_date >= sp.sub_date
        AND c.click_date >= o.open_date
        AND sp.email_consent_public_ref  = c.email_consent_public_ref
        AND sp.profile_master_id = c.email_profile_master_id
), aggregated_data AS (
    -- get activation date based on first open/click
    SELECT
        event_id,
        email_event_id,
        profile_master_id,
        sub_date AS sub_date,
        is_cancelled,
        IF(first_open_date IS NOT NULL, first_open_date, NULL) AS activation_date,
        pole_name,
        brand_trigram,
        status,
        partner,
        context_type,
        context,
        CASE
            WHEN pd.info.gender = 'F' THEN 'Femme'
            WHEN pd.info.gender = 'M' THEN 'Homme'
            ELSE '-'
        END AS gender,
        CASE
            WHEN IF(LENGTH(SPLIT(info.birthdate, '-')[ordinal(1)]) < 4, null, CAST(FLOOR(DATE_DIFF(CURRENT_DATE()+1,CAST(info.birthdate AS DATE),DAY)/365.25) AS INT)) BETWEEN  0 AND 17  THEN  '0-17'
            WHEN IF(LENGTH(SPLIT(info.birthdate, '-')[ordinal(1)]) < 4, null, CAST(FLOOR(DATE_DIFF(CURRENT_DATE()+1,CAST(info.birthdate AS DATE),DAY)/365.25) AS INT)) BETWEEN 18 AND 24  THEN '18-24'
            WHEN IF(LENGTH(SPLIT(info.birthdate, '-')[ordinal(1)]) < 4, null, CAST(FLOOR(DATE_DIFF(CURRENT_DATE()+1,CAST(info.birthdate AS DATE),DAY)/365.25) AS INT)) BETWEEN 25 AND 34  THEN '25-34'
            WHEN IF(LENGTH(SPLIT(info.birthdate, '-')[ordinal(1)]) < 4, null, CAST(FLOOR(DATE_DIFF(CURRENT_DATE()+1,CAST(info.birthdate AS DATE),DAY)/365.25) AS INT)) BETWEEN 35 AND 44  THEN '35-44'
            WHEN IF(LENGTH(SPLIT(info.birthdate, '-')[ordinal(1)]) < 4, null, CAST(FLOOR(DATE_DIFF(CURRENT_DATE()+1,CAST(info.birthdate AS DATE),DAY)/365.25) AS INT)) BETWEEN 45 AND 54  THEN '45-54'
            WHEN IF(LENGTH(SPLIT(info.birthdate, '-')[ordinal(1)]) < 4, null, CAST(FLOOR(DATE_DIFF(CURRENT_DATE()+1,CAST(info.birthdate AS DATE),DAY)/365.25) AS INT)) BETWEEN 55 AND 64  THEN '55-64'
            WHEN IF(LENGTH(SPLIT(info.birthdate, '-')[ordinal(1)]) < 4, null, CAST(FLOOR(DATE_DIFF(CURRENT_DATE()+1,CAST(info.birthdate AS DATE),DAY)/365.25) AS INT)) >= 65              THEN '65+'
            ELSE '-'
        END AS age_bin,
        IF(pd.id.email_profile_master_id IS NOT NULL, SPLIT(pd.info.email, '@')[ordinal(2)], 'TBD') AS esp
    FROM first_activity_per_context AS facp
    LEFT JOIN `{{ params.bq_project }}.business_data.profile_digital_360` AS pd
        ON pd.id.email_profile_master_id = facp.profile_master_id
), complete_Data AS (
    SELECT
        agg.*,
        pc.lever
    FROM aggregated_data AS agg
  LEFT JOIN `{{ params.bq_project }}.store_pandora.context` AS pc ON pc.name = agg.context
)

-- count Pandora acquisition events
SELECT
    sub_date,
    DATE(activation_date) AS first_open_date,
    IF(activation_date IS NOT NULL, 1, 0) AS is_activated,
    pole_name,
    brand_trigram,
    partner,
    lever,
    context_type,
    context,
    gender,
    age_bin,
    esp,
    status,
    COUNT(DISTINCT IF(is_cancelled IS FALSE, event_id, NULL)) AS volume
FROM complete_data
GROUP BY 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13;
