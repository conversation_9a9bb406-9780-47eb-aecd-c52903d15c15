-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DROP TABLE IF EXISTS `{{ params.bq_project }}.datastudio.pandora_activation_new_rea`;

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.datastudio.pandora_activation_new_rea`(
    acquisition_date              DATE          OPTIONS(description="Acquisition date on the context"),
    activation_date               DATE          OPTIONS(description="First open date on the context"),
    activation_click_date         DATE          OPTIONS(description="First click date on the context"),
    cat_activation_delay          STRING        OPTIONS(description="Category of delay of activation based on First open date on the context"),
    cat_click_activation_delay    STRING        OPTIONS(description="Category of delay of activation based on First click date on the context"),
    is_activated                  INTEGER       OPTIONS(description="Boolean indicating if profiles have opened an nl"),
    is_click_activated            INTEGER       OPTIONS(description="Boolean indicating if profiles have clicked an nl"),
    pole_name          STRING        OPTIONS(description="Pole name of the newsletter subscribed \n ref:{{ params.bq_project }}.store_karinto.pole"),
    brand_trigram      STRING        OPTIONS(description="Brand trigram of the newsletter subscribed \n ref:{{ params.bq_project }}.store_karinto.brand"),
    partner            STRING        OPTIONS(description="Partner of acquisition \n ref:{{ params.bq_project }}.refined_data.pandora_events"),
    collect_lever      STRING        OPTIONS(description="Lever according to the context of acquisition \n ref:{{ params.bq_project }}.refined_data.collect_lever"),
    context            STRING        OPTIONS(description="Context of acquisition \n ref:{{ params.bq_project }}.refined_data.pandora_events"),
    gender             STRING        OPTIONS(description="Gender from business_data.profile_digital_360 as enum=['Femme','Homme']"),
    age_bin            STRING        OPTIONS(description="Age bins from business_data.profile_digital_360 as enum=['0-17', '18-24', '25-34', '35-44', '45-54', '55-64', '65+']"),
    esp                STRING        OPTIONS(description="Domain from info.email business_data.profile_digital_360"),
    esp_category       STRING        OPTIONS(description="IF this concerns more than 0.5% of acquisitions THEN esp ELSE 'autre'"),
    status             STRING        OPTIONS(description="Status of profiles, enum=['NEW', 'REA']"),
    volume             INTEGER       OPTIONS(description="Number of profiles sub or openers")
)
PARTITION BY acquisition_date
OPTIONS(description="Compute volume of pandora's activation after their first NL open/click by gender, age bin, ESP/domain, brand, partner, context by subscription and first open date."
                  ||"\n"
                  ||"We keep only open/click datetime on the 90 or 180 days after subscription datetime time frame."
                  ||"\n\n"
                  ||"This table is used in datastudio:"
                  ||"\n"
                  ||"Pandora - Suivi de l'activation: https://datastudio.google.com/reporting/d97b43a0-968f-437a-a538-d17ce55ffbca"
                  ||"\n\n"
                  ||"DAG: {{ dag.dag_id }}."
                  ||"\n\n"
                  ||"Sync: Daily",
    labels=[
        ('owner', 'mozart'),
        ('dashboard_id', '99')
    ]);

INSERT INTO `{{ params.bq_project }}.datastudio.pandora_activation_new_rea`
WITH declare_default_consent AS (
  SELECT
    "CAM" AS brand_trigram,
    "ca_minteresse_nl" AS default_consent_public_ref
  UNION ALL
    SELECT
      "CAP" AS brand_trigram,
      "capital_quotidienne_nl" AS default_consent_public_ref
  UNION ALL
    SELECT
      "CAC" AS brand_trigram,
      "cuisine_actuelle_quotidienne_nl" AS default_consent_public_ref
  UNION ALL
    SELECT
      "FAC" AS brand_trigram,
      "femme_actuelle_quotidienne_nl" AS default_consent_public_ref
  UNION ALL
    SELECT
      "GEN" AS brand_trigram,
      "gentside_buzz_nl" AS default_consent_public_ref
  UNION ALL
    SELECT
      "GEO" AS brand_trigram,
      "geo_quotidienne_nl" AS default_consent_public_ref
  UNION ALL
    SELECT
      "OMM" AS brand_trigram,
      "ohmymag_news_nl" AS default_consent_public_ref
  UNION ALL
    SELECT
      "T2S" AS brand_trigram,
      "tele_2_semaines_quotidienne_nl" AS default_consent_public_ref
  UNION ALL
    SELECT
      "TEL" AS brand_trigram,
      "tele_loisirs_buzz_nl" AS default_consent_public_ref
  UNION ALL
    SELECT
      "VOI" AS brand_trigram,
      "voici_quotidienne_nl" AS default_consent_public_ref
), simulate_consent_opens AS (
  -- Simulate consent for activation theme
  SELECT
      open_date,
      email_profile_master_id,
      CASE
        WHEN STARTS_WITH(o.theme, "activation-") THEN ddc.default_consent_public_ref
        ELSE o.email_consent_public_ref
        END AS email_consent_public_ref
  FROM `{{ params.bq_project }}.refined_data.email_event_open` AS o
  LEFT JOIN `{{ params.bq_project }}.refined_data.email_base_repository` AS ebr ON ebr.theme = o.theme
                                                                        AND STARTS_WITH(ebr.theme, "activation-")
  LEFT JOIN declare_default_consent AS ddc ON ddc.brand_trigram = ebr.brand_trigram
  GROUP BY ALL
), simulate_consent_clicks AS (
  -- Simulate consent for activation theme
  SELECT
      click_date,
      email_profile_master_id,
      CASE
        WHEN STARTS_WITH(c.theme, "activation-") THEN ddc.default_consent_public_ref
        ELSE c.email_consent_public_ref
        END AS email_consent_public_ref
  FROM `{{ params.bq_project }}.refined_data.email_event_click` AS c
  LEFT JOIN `{{ params.bq_project }}.refined_data.email_base_repository` AS ebr ON ebr.theme = c.theme
                                                                        AND STARTS_WITH(ebr.theme, "activation-")
  LEFT JOIN declare_default_consent AS ddc ON ddc.brand_trigram = ebr.brand_trigram
  GROUP BY ALL
), sub_profiles AS (
    -- get NEW/REA Pandora events and (partner, context information)
    SELECT DISTINCT
        e.event_id                                                          AS event_id,
        e.action.email.event_id                                             AS email_event_id,
        e.email_profile_master_id                                           AS profile_master_id,
        e.event_date                                                        AS acquisition_date,
        IF(e.response.email.status = 'REO', 'REA', e.response.email.status) AS status,
        action.email.unsub_date                                             AS unsub_date,
        action.email.is_cancelled                                           AS is_cancelled,
        email_base.consent_public_ref                                       AS email_consent_public_ref,
        email_base.brand_trigram                                            AS brand_trigram,
        email_base.pole_name                                                AS pole_name,
        e.setup.context.name                                                AS context,
        e.setup.partner.name                                                AS partner,
        e.collect_lever                                                     AS collect_lever
    FROM `{{ params.bq_project }}.refined_data.pandora_events` AS e,
      UNNEST(action.email.base) AS email_base
    WHERE
      -- Last 3 years
      DATE(e.event_date) >= DATE_SUB(CURRENT_DATE(), INTERVAL 3 YEAR)
      -- only 'NEW' and 'REA' events
      AND e.response.email.status IN ('NEW', 'REA', 'REO')
), first_activity_per_context AS (
    -- get first open/click after Pandora acquisition
    SELECT DISTINCT
        sp.*,
        MIN(DATE(o.open_date)) OVER (PARTITION BY event_id, sp.context) AS first_open_date,
        MIN(DATE(c.click_date)) OVER (PARTITION BY event_id, sp.context) AS first_click_date
    FROM sub_profiles AS sp
    LEFT JOIN simulate_consent_opens AS o
        ON  DATE(o.open_date) >= DATE_SUB(CURRENT_DATE(), INTERVAL 3 YEAR)
        AND DATE(o.open_date) <= DATE(COALESCE(DATE(sp.unsub_date), DATE_ADD(DATE(sp.acquisition_date), INTERVAL 90 DAY)))
        AND DATE(o.open_date) >= DATE(sp.acquisition_date)
        AND sp.profile_master_id = o.email_profile_master_id
        AND sp.email_consent_public_ref = o.email_consent_public_ref
    LEFT JOIN simulate_consent_clicks AS c
        ON  DATE(c.click_date) >= DATE_SUB(CURRENT_DATE(), INTERVAL 3 YEAR)
        AND DATE(c.click_date) <= DATE(COALESCE(DATE(sp.unsub_date), DATE_ADD(DATE(sp.acquisition_date), INTERVAL 180 DAY)))
        AND DATE(c.click_date) >= DATE(sp.acquisition_date)
        AND DATE(c.click_date) >= DATE(o.open_date)
        AND sp.email_consent_public_ref = c.email_consent_public_ref
        AND sp.profile_master_id = c.email_profile_master_id
), aggregated_data AS (
    -- get activation date based on first open/click
    SELECT
        event_id,
        email_event_id,
        profile_master_id,
        DATE(acquisition_date) AS acquisition_date,
        is_cancelled,
        CASE
            WHEN first_open_date IS NULL AND first_click_date IS NULL     THEN NULL
            WHEN first_open_date IS NOT NULL AND first_click_date IS NULL THEN first_open_date
            WHEN first_open_date IS NULL AND first_click_date IS NOT NULL THEN first_click_date
            WHEN first_open_date < first_click_date                       THEN first_open_date
            WHEN first_open_date >= first_click_date                      THEN first_click_date
            ELSE NULL
        END AS activation_date,
        first_click_date  AS activation_click_date,
        pole_name,
        brand_trigram,
        status,
        partner,
        context,
        collect_lever,
        CASE
            WHEN pd.info.gender = 'F' THEN 'Femme'
            WHEN pd.info.gender = 'M' THEN 'Homme'
            ELSE '-'
        END AS gender,
        CASE
            WHEN IF(LENGTH(SPLIT(info.birthdate, '-')[ordinal(1)]) < 4, NULL, CAST(FLOOR(DATE_DIFF(CURRENT_DATE()+1,CAST(info.birthdate AS DATE),DAY)/365.25) AS INT)) BETWEEN  0 AND 17 THEN  '0-17'
            WHEN IF(LENGTH(SPLIT(info.birthdate, '-')[ordinal(1)]) < 4, NULL, CAST(FLOOR(DATE_DIFF(CURRENT_DATE()+1,CAST(info.birthdate AS DATE),DAY)/365.25) AS INT)) BETWEEN 18 AND 24 THEN '18-24'
            WHEN IF(LENGTH(SPLIT(info.birthdate, '-')[ordinal(1)]) < 4, NULL, CAST(FLOOR(DATE_DIFF(CURRENT_DATE()+1,CAST(info.birthdate AS DATE),DAY)/365.25) AS INT)) BETWEEN 25 AND 34 THEN '25-34'
            WHEN IF(LENGTH(SPLIT(info.birthdate, '-')[ordinal(1)]) < 4, NULL, CAST(FLOOR(DATE_DIFF(CURRENT_DATE()+1,CAST(info.birthdate AS DATE),DAY)/365.25) AS INT)) BETWEEN 35 AND 44 THEN '35-44'
            WHEN IF(LENGTH(SPLIT(info.birthdate, '-')[ordinal(1)]) < 4, NULL, CAST(FLOOR(DATE_DIFF(CURRENT_DATE()+1,CAST(info.birthdate AS DATE),DAY)/365.25) AS INT)) BETWEEN 45 AND 54 THEN '45-54'
            WHEN IF(LENGTH(SPLIT(info.birthdate, '-')[ordinal(1)]) < 4, NULL, CAST(FLOOR(DATE_DIFF(CURRENT_DATE()+1,CAST(info.birthdate AS DATE),DAY)/365.25) AS INT)) BETWEEN 55 AND 64 THEN '55-64'
            WHEN IF(LENGTH(SPLIT(info.birthdate, '-')[ordinal(1)]) < 4, NULL, CAST(FLOOR(DATE_DIFF(CURRENT_DATE()+1,CAST(info.birthdate AS DATE),DAY)/365.25) AS INT)) >= 65             THEN '65+'
            ELSE '-'
        END AS age_bin,
        IF(pd.id.email_profile_master_id IS NOT NULL, SPLIT(pd.info.email, '@')[ordinal(2)], 'TBD') AS esp
    FROM first_activity_per_context AS facp
    LEFT JOIN `{{ params.bq_project }}.business_data.profile_digital_360` AS pd
        ON pd.id.email_profile_master_id = facp.profile_master_id
), final_data AS (
    -- count Pandora acquisition events
    SELECT
        DATE(acquisition_date)                                    AS acquisition_date,
        DATE(activation_date)                                     AS activation_date,
        DATE(activation_click_date)                               AS activation_click_date,
        IF(activation_date IS NOT NULL, 1, 0)                     AS is_activated,
        IF(activation_click_date IS NOT NULL, 1, 0)               AS is_click_activated,
        pole_name                                                 AS pole_name,
        brand_trigram                                             AS brand_trigram,
        partner                                                   AS partner,
        collect_lever                                             AS collect_lever,
        context                                                   AS context,
        gender                                                    AS gender,
        age_bin                                                   AS age_bin,
        esp                                                       AS esp,
        status                                                    AS status,
        COUNT(DISTINCT IF(is_cancelled IS FALSE, event_id, NULL)) AS volume
    FROM aggregated_data
    GROUP BY ALL
)
SELECT
        acquisition_date,
        activation_date,
        activation_click_date,
        CASE
	WHEN DATE_DIFF(activation_date,acquisition_date, DAY) <=    7  THEN '0-7 jours'
	WHEN DATE_DIFF(activation_date,acquisition_date, DAY) <=   15  THEN '7-15 jours'
	WHEN DATE_DIFF(activation_date,acquisition_date, DAY) <=   30  THEN '15-30 jours'
	WHEN DATE_DIFF(activation_date,acquisition_date, DAY) <=  60  THEN '30-60 jours'
	WHEN DATE_DIFF(activation_date,acquisition_date, DAY) <= 180  THEN '60-180 jours'
        ELSE "(not set)"
        END AS cat_activation_delay,
        CASE
	WHEN DATE_DIFF(activation_click_date,acquisition_date, DAY) <=    7  THEN '0-7 jours'
	WHEN DATE_DIFF(activation_click_date,acquisition_date, DAY) <=  15  THEN '7-15 jours'
	WHEN DATE_DIFF(activation_click_date,acquisition_date, DAY) <=   30  THEN '15-30 jours'
	WHEN DATE_DIFF(activation_click_date,acquisition_date, DAY) <=  60  THEN '30-60 jours'
	WHEN DATE_DIFF(activation_click_date,acquisition_date, DAY) <=  180  THEN '60-180 jours'
        ELSE "(not set)"
        END AS cat_click_activation_delay,
        is_activated,
        is_click_activated,
        pole_name,
        brand_trigram,
        partner,
        collect_lever,
        context,
        gender,
        age_bin,
        esp,
        IF(SAFE_DIVIDE(SUM (volume) OVER (PARTITION BY esp), SUM (volume) OVER (PARTITION BY 1) ) >= 0.005, esp, 'autre') AS esp_category,
        status,
        volume
FROM final_data;
