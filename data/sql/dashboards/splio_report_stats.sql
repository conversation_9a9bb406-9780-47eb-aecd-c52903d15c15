-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
DROP TABLE IF EXISTS `{{ params.bq_project }}.datastudio.splio_report_stats`;
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.datastudio.splio_report_stats`(
 start_date                          TIMESTAMP               OPTIONS(description="campaign creation datetime. ref: {{ params.bq_project }}.store_tracking.splio_report_data#stattime"),
    end_date                            TIMESTAMP               OPTIONS(description="report last update datetime. ref: {{ params.bq_project }}.store_tracking.splio_report_data#endtime"),
    id STRUCT <
        message_ref                         STRING                  OPTIONS(description="campaign message id. ref: {{ params.bq_project }}.store_tracking.splio_report_data#data#message#id"),
        campaign_ref                        STRING                  OPTIONS(description="ref: {{ params.bq_project }}.store_tracking.splio_report_data.data#campaign"),
        sendout_ref                         STRING                  OPTIONS(description="ref: {{ params.bq_project }}.store_tracking.splio_report_data.data#id"),
        report_id                           INTEGER                 OPTIONS(description="splio report id. ref: {{ params.bq_project }}.store_tracking.splio_report_data.report_id")
    >                                                           OPTIONS(description="IDs informations"),
    campaign STRUCT<
        mode                       STRING                           OPTIONS(description="enum = ['auto', 'alert', 'manual', 'other'] extracted from message_name"),
        universe_name              STRING                           OPTIONS(description="ref: {{ params.bq_project }}.store_tracking.store_karinto.universe.name"),
        universe_type              STRING                           OPTIONS(description="ref: {{ params.bq_project }}.store_tracking.store_karinto.universe.type"),
        segment                    STRING                           OPTIONS(description="campaign segement extracted from message name as enum=[A2, A0, X0, etc....]. ref: {{ params.bq_project }}.store_tracking.splio_report_data.data#message#name"),
        sender_email               STRING                           OPTIONS(description="campaign subject. ref: {{ params.bq_project }}.store_tracking.splio_report_data.data#sender_email"),
        sender_name                STRING                           OPTIONS(description="campaign subject. ref: {{ params.bq_project }}.store_tracking.splio_report_data.data#sender_name"),
        message_name               STRING                           OPTIONS(description="campaign message name. ref: {{ params.bq_project }}.store_tracking.splio_report_data#message#name"),
        message_subject            STRING                           OPTIONS(description="campaign message name. ref: {{ params.bq_project }}.store_tracking.splio_report_data#message#subject"),
        source                     STRING                           OPTIONS(description="utm source extracted from splio links. It contains NL name and it's the most frequent by report id")
    >                                                           OPTIONS(description="campaigns informations"),
    rogue_one STRUCT <
        id                              INTEGER                     OPTIONS(description="rogue-one id. ref: {{ params.bq_project }}.store_tracking.splio_report.rogue_one_email_id"),
        shoot_date                      TIMESTAMP                   OPTIONS(description="rogue-one shoot datetime. ref: {{ params.bq_project }}.store_tracking.splio_report.rogue_one_email_id"),
        email_base  STRUCT<
            owner_name                  STRING                          OPTIONS(description="owner name. ref: {{ params.bq_project }}.store_karinto.owner.name"),
            pole_name                   STRING                          OPTIONS(description="owner name. ref: {{ params.bq_project }}.store_karinto.pole.name"),
            brand_trigram               STRING                          OPTIONS(description="owner name. ref: {{ params.bq_project }}.store_karinto.brand.trigram"),
            email_consent_public_ref    STRING                          OPTIONS(description="email consent public ref. ref:  {{ params.bq_project }}.store_karinto.consent.public_ref")
        >                                                           OPTIONS(description="consent information based on rogue-one email id extarcted from links."),
        theme                           STRING                      OPTIONS(description="theme as 'NL shoopping', 'Geo destination', ...")
    >                                                           OPTIONS(description="rogue-one's informations based on consents IDs extracted from links"),
    stats    STRUCT<
        targets                 INTEGER                             OPTIONS(description="number of targets email. ref: {{ params.bq_project }}.store_tracking.splio_report_data.recipients#targets"),
        sent                    INTEGER                             OPTIONS(description="number of sent email. ref: {{ params.bq_project }}.store_tracking.splio_report_data.recipients#sent"),
        delivered               INTEGER                             OPTIONS(description="number of arrived email to recipients. ref: {{ params.bq_project }}.store_tracking.splio_report_data.recipients#delivered"),
        opens                   INTEGER                             OPTIONS(description="number of opened emails. ref: {{ params.bq_project }}.store_tracking.splio_report_data.reactions#opens"),
        openers                 INTEGER                             OPTIONS(description="number of distinct users that opens an email at least. ref: {{ params.bq_project }}.store_tracking.splio_report_data.recipients#openers"),
        clicks                  INTEGER                             OPTIONS(description="number of clicked emails. ref: {{ params.bq_project }}.store_tracking.splio_report_data.reactions#clicks"),
        clickers                INTEGER                             OPTIONS(description="number of distinct users that clicks into an email at least. ref: {{ params.bq_project }}.store_tracking.splio_report_data.recipients#clickers"),
        unsubscribes            INTEGER                             OPTIONS(description="number of unsubscribes. ref: {{ params.bq_project }}.store_tracking.splio_report_data.reactions#unsubscribes"),
        soft_bounces            INTEGER                             OPTIONS(description="number of soft bounced emails. ref: {{ params.bq_project }}.store_tracking.splio_report_data.recipients#soft_bounce"),
        hard_bounces            INTEGER                             OPTIONS(description="number of hard bounced email. ref: {{ params.bq_project }}.store_tracking.splio_report_data.recipients#hard_bounce"),
        spam_complaints         INTEGER                             OPTIONS(description="number of spam complaints. ref: {{ params.bq_project }}.store_tracking.splio_report_data.reactions#spam_complaints")
    >                                                               OPTIONS(description="campaigns stats")
)PARTITION BY TIMESTAMP_TRUNC(start_date, DAY)
OPTIONS(description="Lists splio report campaigns statistics. "||
                    "It's used by these dashboards:  "||
                    "* https://datastudio.google.com/reporting/4c214ddd-72c0-4d36-8611-044d3df9ac18 "||
                    "* https://datastudio.google.com/reporting/ecc514a2-fa41-4575-ae33-0503a2abe559 "||
                    "* https://datastudio.google.com/reporting/5e25930c-77c4-4c4e-a32d-ce218e9f37ae "||
                    "* https://datastudio.google.com/reporting/e119ba05-9ac7-4b43-a2e0-449a117b007d "||
                    "* https://datastudio.google.com/reporting/fdff76b9-9796-47eb-92e6-a873e0149e10 "||
                    "* https://datastudio.google.com/reporting/df519def-a040-43d1-917b-c718c9f9f27d "||
                    "* https://datastudio.google.com/reporting/1a283539-609a-4bfb-a989-f04adb166a6e "||
                    "DAG: {{ dag.dag_id }}. "||
                    "Sync: daily");
INSERT INTO `{{ params.bq_project }}.datastudio.splio_report_stats`
SELECT * FROM `{{ params.bq_project }}.refined_data.splio_report`;
