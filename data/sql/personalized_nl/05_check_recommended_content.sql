-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

SELECT IF(COUNT(*) > 0, TRUE, FALSE)
FROM `pm-prod-datab2b.share_itdata.personalized_nl_recommend_content` AS rc
JOIN `store.personalized_nl_campaign` AS c ON rc.rogue_one_email_id = c.rogue_one_email_id
WHERE rc.subject IS NOT NULL
    AND rc.preheader IS NOT NULL
    AND rc.content IS NOT NULL
    AND DATE(rc.create_date) >= CURRENT_DATE()
    -- exclude already exported data
    AND NOT EXISTS (
        SELECT 1 FROM `export_matrix_email.personalized_nl_recommended_content` AS erc
        WHERE erc.rogue_one_email_id = c.rogue_one_email_id
    )
    -- exclude already synced data (imported into postgres)
    AND NOT EXISTS (
        SELECT 1 FROM `store_stream.personalized_nl_email` AS e
        WHERE c.email_campaign_id = e.email_campaign_id
    );
