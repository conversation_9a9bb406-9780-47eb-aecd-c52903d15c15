-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

SELECT
    ec.id AS campaign_id,
    ec.rogue_one_email_id,
    ec.status,
    TO_CHAR(ec.shoot_date at time zone 'Europe/Paris', 'YYYY-MM-DD HH24:MI') AS shoot_date,
    COALESCE(e.status, '') AS email_status,
    COALESCE(COUNT(e.profile_master_id), 0) AS total_profiles
FROM personalized_nl.email_campaign AS ec
LEFT JOIN personalized_nl.email AS e ON e.email_campaign_id = ec.id
WHERE
    (
        (
            ec.status = 'NEW'
            AND shoot_date >= NOW() - '2 hour':: interval
        )
        OR (
            ec.status = 'PREPARING'
            AND shoot_date >= NOW() - '1 hour':: interval
        )
    )
	 -- shoot date is today
	AND shoot_date >= DATE_TRUNC('day', NOW())
	AND shoot_date < DATE_TRUNC('day', NOW()) + '1 day'::interval
    AND NOT EXISTS (
        SELECT 1 FROM personalized_nl.email_campaign_alert
        WHERE email_campaign_id = ec.id
            -- alert was sent in the last 30 minutes
            AND alert_date >= NOW() - '30 minute'::interval
    )
GROUP BY 1, 2, 3, 4, 5
ORDER BY shoot_date, ec.id;