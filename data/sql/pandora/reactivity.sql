-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
CREATE TABLE IF NOT EXISTS `{{params.bq_project}}.refined_data.pandora_reactivity` (
    observation_date      DATE          NOT NULL     OPTIONS(description="Observation date"),
    profile_master_id     INTEGER       NOT NULL     OPTIONS(description="Creation date of profile."),
    response              STRING                     OPTIONS(description="Acquisition API response in store_pandora.event"),
    sub_date              TIMESTAMP     NOT NULL     OPTIONS(description="Profile subscription's Date"),
    partner_id            INTEGER       NOT NULL     OPTIONS(description="ref: store_pandora.partner.id"),
    partner_name          STRING        NOT NULL     OPTIONS(description="Acquisition partner's name"),
    context_id            INTEGER       NOT NULL     OPTIONS(description="ref: store_pandora.context.id"),
    context_name          STRING        NOT NULL     OPTIONS(description="Acquisition context's name"),
    consent_id            INTEGER       NOT NULL     OPTIONS(description="ref: store_karinto.email_consent.id"),
    public_ref            STRING        NOT NULL     OPTIONS(description="ref: store_karinto.email_consent.public_ref"),
    nb_open    	          INTEGER                    OPTIONS(description="Number of opens at this observation_date"),
    nb_click    	      INTEGER                    OPTIONS(description="Number of clicks at this observation_date"),
    nb_open_uniq	      INTEGER                    OPTIONS(description="Number of unique opens at this observation_date"),
    nb_click_uniq	      INTEGER                    OPTIONS(description="Number of unique clicks at this observation_date")
) PARTITION BY observation_date OPTIONS(
  expiration_timestamp=NULL,
  description="Pandora reactivity.\n"
              ||"\n\n"
              ||"DAG: {{ dag.dag_id }}."
              ||"Sync: daily");

INSERT INTO `{{params.bq_project}}.refined_data.pandora_reactivity`
WITH observe AS (
  SELECT CAST('{{ execution_date.strftime("%Y-%m-%d") }}' AS DATE) AS date
), pandora_profile AS (
    SELECT pl.observation_date,
        pl.profile_master_id,
        -- needed for next CTE
        pl.consent_id,
        pl.sub_date,
        COALESCE(pl.unsub_date, TIMESTAMP(o.date + 1)) AS unsub_date
    FROM `{{params.bq_project}}.refined_data.pandora_profile_lifecycle` AS pl
    CROSS JOIN observe AS o
    WHERE pl.observation_date = o.date
), tracking_data AS (
    SELECT
        p.observation_date,
        p.profile_master_id,
        p.consent_id,
        o.rogue_one_email_id,
        'open' AS type
    FROM pandora_profile AS p
    JOIN `{{params.bq_project}}.store_karinto.email_consent` ec ON p.consent_id = ec.id
    JOIN `{{params.bq_project}}.refined_data.email_event_open` o
        ON o.email_consent_public_ref = ec.public_ref
        AND o.email_profile_master_id = p.profile_master_id
        -- activity before observation
        AND DATE(o.open_date) = p.observation_date
        -- profile must be alive
        AND o.open_date >= p.sub_date
        AND o.open_date < p.unsub_date
    UNION ALL
    SELECT
        p.observation_date,
        p.profile_master_id,
        p.consent_id,
        c.rogue_one_email_id,
        'click' AS type
    FROM pandora_profile AS p
    JOIN `{{params.bq_project}}.store_karinto.email_consent` ec ON p.consent_id = ec.id
    JOIN `{{params.bq_project}}.refined_data.email_event_click` c
        ON c.email_consent_public_ref = ec.public_ref
        AND c.email_profile_master_id = p.profile_master_id
        -- activity before observation
        AND DATE(c.click_date) = p.observation_date
        -- profile must be alive
        AND c.click_date >= p.sub_date
        AND c.click_date < p.unsub_date

), reactivity_volume AS (
    SELECT observation_date,
        profile_master_id,
        consent_id,
        COUNTIF(type = 'open')  AS nb_open,
        COUNT(DISTINCT IF(type = 'open', rogue_one_email_id, null)) AS nb_open_uniq,
        COUNTIF(type = 'click')  AS nb_click,
        COUNT(DISTINCT IF(type = 'click', rogue_one_email_id, null)) AS nb_click_uniq
    FROM tracking_data
    GROUP BY 1, 2, 3
)
SELECT pl.observation_date,
   pl.profile_master_id,
   pl.response,
   pl.sub_date,
   pl.partner_id,
   pl.partner_name,
   pl.context_id,
   pl.context_name,
   pl.consent_id,
   pl.public_ref,
   rv.nb_open,
   rv.nb_click,
   rv.nb_open_uniq,
   rv.nb_click_uniq
FROM `{{params.bq_project}}.refined_data.pandora_profile_lifecycle` AS pl
JOIN reactivity_volume AS rv
    ON pl.observation_date = rv.observation_date
    AND pl.profile_master_id = rv.profile_master_id
    AND pl.consent_id = rv.consent_id;
