-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
--gsutil -m cp -R production/data/sql/generic_export_full/*  gs://europe-west1-mozart-cluster-4128dbb8-bucket/data/sql/generic_export_full/

START TRANSACTION;

-- ---------------------------------------------
-- get profile data
-- ---------------------------------------------
CREATE TABLE IF NOT EXISTS matrix__email_generic_export.profile_full_{{ next_execution_date.strftime("%Y%m%d") }}
AS SELECT * FROM matrix__email.profile;
ALTER TABLE matrix__email_generic_export.profile_full_{{ next_execution_date.strftime("%Y%m%d") }} OWNER TO matrix_email;
-- Time: 


-- ---------------------------------------------
-- get profile_master_id ---- INUTILE ---- 
-- ---------------------------------------------
CREATE TABLE IF NOT EXISTS matrix__email_generic_export.profile_master_id_full_{{ next_execution_date.strftime("%Y%m%d") }}
AS SELECT * FROM matrix__email.profile_master_id;
ALTER TABLE matrix__email_generic_export.profile_master_id_full_{{ next_execution_date.strftime("%Y%m%d") }} OWNER TO matrix_email;
-- Time:

-- ---------------------------------------------
-- get blacklist
-- ---------------------------------------------
CREATE TABLE IF NOT EXISTS matrix__email_generic_export.blacklist_full_{{ next_execution_date.strftime("%Y%m%d") }}
AS SELECT * FROM matrix__email.blacklist;
ALTER TABLE matrix__email_generic_export.blacklist_full_{{ next_execution_date.strftime("%Y%m%d") }} OWNER TO matrix_email;
-- Time:

DROP TABLE IF EXISTS matrix__email_generic_export.profile_full;
DROP TABLE IF EXISTS matrix__email_generic_export.profile_master_id_full;
DROP TABLE IF EXISTS matrix__email_generic_export.blacklist_full;

-- rename current tmp snapshots tables
ALTER TABLE IF EXISTS matrix__email_generic_export.profile_full_{{ next_execution_date.strftime("%Y%m%d") }}
  RENAME TO profile_full;

ALTER TABLE IF EXISTS matrix__email_generic_export.profile_master_id_full_{{ next_execution_date.strftime("%Y%m%d") }}
  RENAME TO profile_master_id_full;

ALTER TABLE IF EXISTS matrix__email_generic_export.blacklist_full_{{ next_execution_date.strftime("%Y%m%d") }}
  RENAME TO blacklist_full;

-- ---------------------------------------------
-- ---------------------------------------------
-- get last 7 days data only from profiles_email_consents (sub / unsub)
-- ---------------------------------------------
CREATE TABLE IF NOT EXISTS matrix__email_generic_export.profiles_email_consents_weekly_{{ next_execution_date.strftime("%Y%m%d") }}
AS (
	SELECT * FROM matrix__email.profiles_email_consents
	WHERE update_date > NOW() - INTERVAL '7 days'
)
;
ALTER TABLE matrix__email_generic_export.profiles_email_consents_weekly_{{ next_execution_date.strftime("%Y%m%d") }} OWNER TO matrix_email;
-- Time:
DROP TABLE IF EXISTS matrix__email_generic_export.profiles_email_consents_weekly;

ALTER TABLE IF EXISTS matrix__email_generic_export.profiles_email_consents_weekly_{{ next_execution_date.strftime("%Y%m%d") }}
  RENAME TO profiles_email_consents_weekly;


-- ---------------------------------------------
-- ---------------------------------------------


COMMIT;
