-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- set dynamic incremental condition
DECLARE min_date DEFAULT (SELECT DATE(MIN(create_date)) AS date FROM `{{ params.bq_project }}.prepare.batch_event_{{ params.platform }}_{{ params.current_date }}`);

{% if  not params.is_web %}
    CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_batch.event_{{ params.platform }}`
    (
        event_id                STRING      NOT NULL    OPTIONS(description="event id"),
        create_date             TIMESTAMP               OPTIONS(description="event timestamp"),
        custom_user_id          STRING                  OPTIONS(description="user id"),
        install_id              STRING                  OPTIONS(description="install id created for the first batch app install"),
        event_type              STRING                  OPTIONS(description="event type as enum = ['push_optin', 'push_optout', 'push_sent', 'push_error', 'token_deleted']"),
        push_type               STRING                  OPTIONS(description="available only for event type sent, opened and error else is null. push type as enum = ['campaign', 'transactional']"),
        campaign_token          STRING                  OPTIONS(description="campaign/transactional token available only for event type :  push_sent, push_opened, push_error else is NULL"),
        group_id                STRING                  OPTIONS(description="group id  available only for event type :  push_sent, push_opened, push_error for transactionnal campaign else is NULL "),
        notif_state             STRING                  OPTIONS(description="enums = ['on', 'off']"),
        advertising_id          STRING                  OPTIONS(description="advertising id"),
        position_clicked_cta    INTEGER                 OPTIONS(description="click position"),
        cta_action              STRING                  OPTIONS(description="cta action")

    )
    PARTITION BY TIMESTAMP_TRUNC(create_date, DAY)
    OPTIONS(
        description="Contains batch events.\n"
                  ||"Epic link : https://pmdtech.atlassian.net/browse/ITDATA-1104 \n"
                  ||"\n\n"
                  ||"DAG: {{dag.dag_id}}. \n"
                  ||"Sync: daily")
    ;

    MERGE `{{ params.bq_project }}.store_batch.event_{{ params.platform }}` AS store
    USING `{{ params.bq_project }}.prepare.batch_event_{{ params.platform }}_{{ params.current_date }}` AS prepare
    ON
        -- key (event_id, create_date, install_id, event_type)
        prepare.event_id = store.event_id
        AND prepare.create_date = store.create_date
        AND prepare.install_id = store.install_id
        AND prepare.event_type = store.event_type
        AND DATE(store.create_date) >= min_date
    WHEN NOT MATCHED BY TARGET THEN
        INSERT (event_id, create_date, custom_user_id, install_id, event_type, push_type, campaign_token, group_id, notif_state, advertising_id, position_clicked_cta, cta_action)
        VALUES (event_id, create_date, custom_user_id, install_id, event_type, push_type, campaign_token, group_id, notif_state, advertising_id, position_clicked_cta, cta_action);

{% else %}
     CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_batch.event_{{ params.platform }}`
    (
        event_id                STRING      NOT NULL    OPTIONS(description="event id"),
        create_date             TIMESTAMP               OPTIONS(description="event timestamp"),
        custom_user_id          STRING                  OPTIONS(description="user id"),
        install_id              STRING                  OPTIONS(description="install id created for the first batch app install"),
        event_type              STRING                  OPTIONS(description="event type as enum = ['push_optin', 'push_optout', 'push_sent', 'push_error', 'token_deleted']"),
        push_type               STRING                  OPTIONS(description="available only for event type sent, opened and error else is null. push type as enum = ['campaign', 'transactional']"),
        campaign_token          STRING                  OPTIONS(description="campaign/transactional token available only for event type :  push_sent, push_opened, push_error else is NULL"),
        group_id                STRING                  OPTIONS(description="group id  available only for event type :  push_sent, push_opened, push_error for transactionnal campaign else is NULL ")
    )
    PARTITION BY TIMESTAMP_TRUNC(create_date, DAY)
    OPTIONS(
        description="Contains batch events.\n"
                  ||"Epic link : https://pmdtech.atlassian.net/browse/ITDATA-1104 \n"
                  ||"\n\n"
                  ||"DAG: {{dag.dag_id}}. \n"
                  ||"Sync: daily")
    ;

    MERGE `{{ params.bq_project }}.store_batch.event_{{ params.platform }}` AS store
    USING `{{ params.bq_project }}.prepare.batch_event_{{ params.platform }}_{{ params.current_date }}` AS prepare
    ON
        -- key (event_id, create_date, install_id, event_type)
        prepare.event_id = store.event_id
        AND prepare.create_date = store.create_date
        AND prepare.install_id = store.install_id
        AND prepare.event_type = store.event_type
        AND DATE(store.create_date) >= min_date
    WHEN NOT MATCHED BY TARGET THEN
        INSERT (event_id, create_date, custom_user_id, install_id, event_type, push_type, campaign_token)
        VALUES (event_id, create_date, custom_user_id, install_id, event_type, push_type, campaign_token);
{% endif %}
