-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

---------------------------------------------------------------
-- Backup event before removing from store email_event
---------------------------------------------------------------
INSERT INTO `store_archive.events_unapplied`
(id,profile_master_id,create_date,type,payload, inserted_date, inserted_lot_number)
SELECT
    id,profile_master_id,create_date,type,payload, inserted_date, inserted_lot_number
FROM `prepare.events_unapplied_lot2` AS prepare
WHERE NOT EXISTS (
        SELECT 1
        FROM `store_archive.events_unapplied` AS ref
        WHERE ref.id = prepare.id
        AND ref.type = prepare.type
        AND ref.inserted_date = prepare.inserted_date
        AND ref.inserted_lot_number = prepare.inserted_lot_number
    )
;

---------------------------------------------------------------
-- Delete events from email_event store
---------------------------------------------------------------
{% if params.is_test_run|lower != 'true' %}
DELETE FROM `store_matrix_email.email_event` AS ee
WHERE EXISTS (
  SELECT
    unapp.id AS email_event_id
  FROM `store_archive.events_unapplied` AS unapp
  WHERE  unapp.id = ee.id
  AND inserted_lot_number = 2
  AND DATE(unapp.inserted_date) = CURRENT_DATE()
);
{% endif %}
---------------------------------------------------------------
------------    LOT 2 - Events to update ----------------------
---------------------------------------------------------------

---------------------------------------------------------------
-- backup events to archive table before modify them in the store email_event
-- keep old payload in case we need to rollback :p 
---------------------------------------------------------------
MERGE `store_archive.events_with_payload_modified` AS store
USING `prepare.events_with_payload_modified_lot2` AS last_update
    ON last_update.id = store.id
WHEN MATCHED THEN
    UPDATE SET
        store.new_payload = last_update.new_payload,
        store.inserted_date = CURRENT_TIMESTAMP(),
        store.inserted_lot_number = 2
WHEN NOT MATCHED BY TARGET THEN
    INSERT (id,profile_master_id,create_date,type,payload,new_payload, inserted_date, inserted_lot_number)
    VALUES (id,profile_master_id,create_date,type,payload,new_payload, inserted_date, inserted_lot_number)
  ;
---------------------------------------------------------------
-- Update email_event store with the new payload
---------------------------------------------------------------
{% if params.is_test_run|lower != 'true' %}

UPDATE `store_matrix_email.email_event` AS ee 
  SET payload = new_ee.new_payload
FROM `store_archive.events_with_payload_modified` AS new_ee
WHERE new_ee.id = ee.id
AND DATE(new_ee.inserted_date) = CURRENT_DATE()
	AND inserted_lot_number = 2
;

DELETE
FROM `store_matrix_email.simulated_sub_unsub_event`
WHERE email_event_id NOT IN (
  SELECT id AS email_event_id FROM `store_matrix_email.email_event`
);
{% endif %}