-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
-- ---------------------------------------------
-- create email_base table
-- ---------------------------------------------
DROP TABLE IF EXISTS `{{params.bq_project}}.refined_data.email_base`;
CREATE TABLE IF NOT EXISTS `{{params.bq_project}}.refined_data.email_base`
(
    owner_id INT64 OPTIONS(description="owner id, ref:pm-prod-matrix.store_karinto.owner"),
    owner_name STRING OPTIONS(description="owner name, ref:pm-prod-matrix.store_karinto.owner"),
    pole_id INT64 OPTIONS(description="pole id, ref:pm-prod-matrix.store_karinto.pole"),
    pole_name STRING OPTIONS(description="pole name, ref:pm-prod-matrix.store_karinto.pole"),
    brand_id INT64 OPTIONS(description="brand id, ref:pm-prod-matrix.store_karinto.brand"),
    brand_name STRING OPTIONS(description="brand name, ref:pm-prod-matrix.store_karinto.brand"),
    brand_trigram STRING OPTIONS(description="brand name trigram, ref:pm-prod-matrix.store_karinto.brand"),
    brand_logo STRING OPTIONS(description="brand logo URL, ref:pm-prod-matrix.store_karinto.brand"),
    consent_id INT64 OPTIONS(description="consent technical id, ref:pm-prod-matrix.store_karinto.email_consent"),
    consent_public_ref STRING OPTIONS(description="consent reference, ref:pm-prod-matrix.store_karinto.email_consent"),
    consent_type STRING OPTIONS(description="consent type:nl, ref:pm-prod-matrix.store_karinto.email_consent"),
    consent_is_active BOOLEAN OPTIONS(description="is active? False=0, True=1, ref:pm-prod-matrix.store_karinto.email_consent"),
    consent_label STRING OPTIONS(description="consent name, ref:pm-prod-matrix.store_karinto.email_consent"),
    consent_unsubscribe_domain STRING OPTIONS(description="consent unsubscribe domain, ref:pm-prod-matrix.store_karinto.email_consent"),
    consent_logo STRING OPTIONS(description="consent logo URL, ref:pm-prod-matrix.store_karinto.email_consent"),
    consent_public_id INT64 OPTIONS(description="consent public id, ref:pm-prod-matrix.store_karinto.email_consent"),
    consent_description STRING OPTIONS(description="consent description, ref:pm-prod-matrix.store_karinto.email_consent")
)
OPTIONS(
    description="Source: pm-prod-matrix.store_karinto Sync: toutes les 4 heures Dags: matrix__backup_karinto"
);
INSERT INTO `{{params.bq_project}}.refined_data.email_base`
SELECT
    o.id                    AS owner_id,
    o.name                  AS owner_name,
    p.id                    AS pole_id,
    p.name                  AS pole_name,
    b.id                    AS brand_id,
    b.name                  AS brand_name,
    b.trigram               AS brand_trigram,
    b.logo                  AS brand_logo,
    ec.id                   AS consent_id,
    ec.public_ref           AS consent_public_ref,
    ec.type                 AS consent_type,
    ec.active               AS consent_is_active,
    ec.label                AS consent_label,
    ec.unsubscribe_domain   AS consent_unsubscribe_domain,
    ec.logo                 AS consent_logo,
    ec.public_id            AS consent_public_id,
    ec.description          AS consent_description
FROM `{{params.bq_project}}.store_karinto.email_consent` ec
LEFT JOIN `{{params.bq_project}}.store_karinto.brand` b
ON ec.brand_id = b.id
LEFT JOIN `{{params.bq_project}}.store_karinto.poles_brands` pb
ON b.id = pb.brand_id
LEFT JOIN `{{params.bq_project}}.store_karinto.pole` p
ON p.id =  pb.pole_id
LEFT JOIN `{{params.bq_project}}.store_karinto.owner` o
ON o.id = b.owner_id;

-- ---------------------------------------------
-- ---------------------------------------------
