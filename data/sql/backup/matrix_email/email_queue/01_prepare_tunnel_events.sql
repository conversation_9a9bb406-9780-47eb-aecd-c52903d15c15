-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE last_tunnel_event_id DEFAULT (SELECT MAX(id) FROM `store_matrix_email.tunnel_event`);

CREATE OR REPLACE TABLE `prepare.matrix_tunnel_event_{{next_execution_date.strftime("%Y_%m_%d_%H%M")}}`
AS
SELECT
    stream_tun.id,
    stream_tun.profile_master_id,
    stream_tun.create_date,
    stream_tun.tunnel_name,
    stream_tun.type,
    TO_JSON_STRING(stream_tun.payload) AS payload
FROM `store_stream.matrix__email_queue_tunnel_event` AS stream_tun
{% if params.full_export|lower != 'true' %}
    WHERE stream_tun.id > last_tunnel_event_id
{% endif %}
;
