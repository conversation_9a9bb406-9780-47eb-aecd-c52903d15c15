-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

MERGE `store_matrix_email.profiles_email_consents`  store
USING `prepare.profiles_email_consents_{{next_execution_date.strftime("%Y_%m_%d_%H%M")}}`  tmp
ON
    store.profile_master_id = tmp.profile_master_id
    AND
    store.email_consent_id = tmp.email_consent_id
WHEN MATCHED THEN
UPDATE SET
    brand_id = tmp.brand_id,
    consent_status = tmp.consent_status,
    source = tmp.source,
    create_date = tmp.create_date,
    update_date = tmp.update_date,
    update_date_trunc = CAST (TIMESTAMP_TRUNC(tmp.update_date, MONTH, 'UTC') AS DATE),
    sync_date = (
    CASE
        -- if something changes, update sync_date field
        WHEN store.brand_id != tmp.brand_id THEN CURRENT_TIMESTAMP ()
        WHEN store.consent_status != tmp.consent_status THEN CURRENT_TIMESTAMP ()
        WHEN store.source != tmp.source THEN CURRENT_TIMESTAMP ()
        WHEN store.create_date != tmp.create_date THEN CURRENT_TIMESTAMP ()
        WHEN store.update_date != tmp.update_date THEN CURRENT_TIMESTAMP ()
        -- don't change sync_date
        ELSE store.sync_date
    END
    )
    WHEN NOT MATCHED THEN
INSERT (profile_master_id, brand_id, consent_status,
        source, create_date, update_date, email_consent_id, update_date_trunc, sync_date)
VALUES (tmp.profile_master_id,
        brand_id,
        consent_status,
        source,
        create_date,
        update_date,
        tmp.email_consent_id,
        CAST(TIMESTAMP_TRUNC(update_date, MONTH, 'UTC') AS DATE),
        CURRENT_TIMESTAMP())
;