-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- Check if import table exists
-- If not, create table as store table
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.import.context_email_consent`
CLONE `{{ params.bq_project }}.store_pandora.context_email_consent`;

-- Init table
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_pandora.context_email_consent` (
    context_id          INTEGER    NOT NULL    OPTIONS(description="context id"),
    email_consent_id    INTEGER    NOT NULL    OPTIONS(description="email consent id")
)
OPTIONS(description="This table provides a reference between contexts and consents.\n" ||
                     "It is imported from Postgres by the DAG {{ dag.dag_id }} every hour at **h40");

-- Clean data
TRUNCATE TABLE `{{ params.bq_project }}.store_pandora.context_email_consent`;

-- Update data with new import
INSERT INTO `{{ params.bq_project }}.store_pandora.context_email_consent`
SELECT
    context_id,
    email_consent_id
FROM `{{ params.bq_project }}.import.context_email_consent`;
