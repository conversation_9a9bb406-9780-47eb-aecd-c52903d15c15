-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}



UPDATE pandora.pandora_backup_sequence as dest
SET pandora_event_id = ref.pandora_event_id
FROM pandora.pandora_backup_sequence AS ref
WHERE dest.process_name = 'process_pandora_event_start_tmp'
AND ref.process_name = 'process_pandora_event_start';

UPDATE pandora.pandora_backup_sequence as dest
SET pandora_event_id = ref.pandora_event_id
FROM pandora.pandora_backup_sequence AS ref
WHERE dest.process_name = 'process_pandora_event_end_tmp'
  AND ref.process_name = 'process_pandora_event_end';

-- end = start + chunk_size
-- to the latest pandora event WITH an email_event_id to make sure that generated email_events were processed
WITH chunk AS (
    SELECT id
    FROM pandora.event
    WHERE id >= (
        SELECT pandora_event_id
        FROM pandora.pandora_backup_sequence
        WHERE process_name = 'process_pandora_event_start_tmp'
    )
    AND (
        email_event_id <= (
            SELECT email_event_id
            FROM matrix__email.email_event_sequence
            WHERE process_name = 'process_email_event_end'
        )
        OR
        email_event_id IS NULL
    )
  LIMIT {{ params.chunk_size }}
)
UPDATE pandora.pandora_backup_sequence
  SET pandora_event_id = (SELECT MAX(chunk.id) FROM chunk)
WHERE process_name = 'process_pandora_event_end_tmp';