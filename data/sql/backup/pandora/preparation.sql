-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
-- Create temporary working table

-- object: pandora.pandora_backup_sequence | type: TABLE --
-- DROP TABLE IF EXISTS pandora.pandora_backup_sequence CASCADE;
CREATE TABLE IF NOT EXISTS pandora.pandora_backup_sequence(
	process_name varchar(50) NOT NULL,
	pandora_event_id bigint NOT NULL,
	CONSTRAINT pandora_backup_sequence_process_name UNIQUE (process_name)

); 
-- ddl-end --
ALTER TABLE pandora.pandora_backup_sequence OWNER TO pandora;
-- ddl-end --
INSERT INTO pandora.pandora_backup_sequence(
	process_name, pandora_event_id)
	VALUES
	('process_pandora_event_start', 0),
	 ('process_pandora_event_end', 0),
	  ('process_pandora_event_start_tmp', 0),
	   ('process_pandora_event_end_tmp', 0)
;