-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
UPDATE pandora.pandora_backup_sequence as dest
SET pandora_event_id = ref.pandora_event_id
FROM pandora.pandora_backup_sequence AS ref
WHERE dest.process_name = 'process_pandora_event_start'
  AND ref.process_name = 'process_pandora_event_end_tmp';

UPDATE pandora.pandora_backup_sequence as dest
SET pandora_event_id = ref.pandora_event_id
FROM pandora.pandora_backup_sequence AS ref
WHERE dest.process_name = 'process_pandora_event_end'
  AND ref.process_name = 'process_pandora_event_end_tmp';