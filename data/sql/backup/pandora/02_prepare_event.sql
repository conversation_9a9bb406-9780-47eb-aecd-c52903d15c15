-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
WITH prepare_event AS (
    SELECT SAFE_CAST(id AS INT64)                                                  AS id,
           response,
           SAFE_CAST(create_date AS TIMESTAMP)                                     AS create_date,
           REPLACE(REPLACE(REPLACE(payload, 'None', 'null'), '\\"', ''), "\\", "") AS payload,
           SAFE_CAST(cancel AS Boolean)                                            AS cancel,
           SAFE_CAST(REPLACE(email_event_id, '\\N', '') AS INT64)                  AS email_event_id,
           SAFE_CAST(REPLACE(context_id, '\\N', '') AS INT64)                      AS context_id,
           SAFE_CAST(REPLACE(partner_id, '\\N', '') AS INT64)                      AS partner_id
      FROM `{{params.import_table}}_{{execution_date.strftime("%Y_%m_%d_%H%M")}}`
     WHERE id NOT IN (
        SELECT SAFE_CAST(id AS INT64)
          FROM `{{params.store_table}}`
    )
)
SELECT pe.id,
       SAFE_CAST(pmi.id AS INT64) AS profile_master_id,
       response,
       pe.create_date,
       `{{params.store_email}}`.removeElementsFromJson(pe.payload,
       ['email', 'profile', 'profile_given', 'email_md5', 'email_sha256'])  AS payload,
       pe.cancel,
       pe.email_event_id,
       pe.context_id,
       pe.partner_id
  FROM prepare_event AS pe
  LEFT JOIN `{{params.pmi_global}}` AS pmi
    ON JSON_EXTRACT_SCALAR(pe.payload, '$.email') = pmi.email
;
