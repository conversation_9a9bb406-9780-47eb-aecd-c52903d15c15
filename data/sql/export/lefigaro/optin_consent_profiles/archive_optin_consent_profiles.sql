-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- Keep historical data (For Debug later)

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.{{ params.bq_dataset }}.optin_consent_profiles_history`
(
  observation_date          DATE   NOT NULL  OPTIONS(description="observation date."),
  email                     STRING NOT NULL OPTIONS(description="Profile email. ref: {{ params.bq_project }}.store_matrix_email.profile_master_id#email."),
  email_consent_public_ref  STRING NOT NULL OPTIONS(description="Email consent public ref. ref: {{ params.bq_project }}.store_karinto.consent#consent_public_ref."),
  email_consent_label       STRING NOT NULL OPTIONS(description="Email consent label. ref: {{ params.bq_project }}.store_karinto.consent#consent_label."),
  optin_date                DATE   NOT NULL OPTIONS(description="last optin date. ref: {{ params.bq_project }}.store_matrix_email.profiles_email_consents#update_date."),
  last_activity_date        DATE            OPTIONS(description="last open/click date. ref: {{ params.bq_project }}.generated_data.last_activity_by_base.")
)
OPTIONS(description="This table contains historical optin profiles exported to Le Figaro based on these conditions 👇🏻 : \n"||
                    "1. JIRA Ticket 👉🏻 https://pmdtech.atlassian.net/browse/MOZ-782 \n"||
                    "2. JIRA Ticket 👉🏻 https://pmdtech.atlassian.net/browse/MOZ-783 \n"||
                    "3. JIRA Ticket 👉🏻 https://pmdtech.atlassian.net/browse/MOZ-784 \n"||
                    "4. JIRA Ticket 👉🏻 https://pmdtech.atlassian.net/browse/MOZ-785 \n"||
                    "DAG : {{ dag.dag_id }}. \n"||
                    "Sync: Daily."
);
MERGE `{{ params.bq_project }}.{{ params.bq_dataset }}.optin_consent_profiles_history` AS dst
USING ( 
  SELECT 
    DATE("{{ next_ds }}") AS observation_date, 
    lefop.*
  FROM `{{ params.bq_project }}.{{ params.bq_dataset }}.{{ params.bq_table }}` AS lefop
) AS ref 
ON
  ref.observation_date = dst.observation_date
  AND
  ref.email = dst.email
  AND
  ref.email_consent_public_ref = dst.email_consent_public_ref
  AND
  ref.email_consent_label = dst.email_consent_label

WHEN MATCHED THEN
UPDATE SET
  dst.optin_date = ref.optin_date,
  dst.last_activity_date = ref.last_activity_date

WHEN NOT MATCHED BY TARGET THEN
INSERT(observation_date, email, email_consent_public_ref, email_consent_label, optin_date, last_activity_date)
VALUES(ref.observation_date, ref.email, ref.email_consent_public_ref, ref.email_consent_label, ref.optin_date, ref.last_activity_date);

