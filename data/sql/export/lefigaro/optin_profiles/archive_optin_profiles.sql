-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- Keep historical data (For Debug later)

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.{{ params.bq_dataset }}.optin_profiles_history`
(
  observation_date  DATE    NOT NULL  OPTIONS(description="observation date."),
  email             STRING  NOT NULL  OPTIONS(description="Profile email. ref: {{ params.bq_project }}.store_matrix_email.profile_master_id#email ."),
  first_name        STRING            OPTIONS(description="Profile first name. ref: {{ params.bq_project }}.store_matrix_email.profile_master_id#firstname ."),
  last_name         STRING            OPTIONS(description="Profile last name. ref: {{ params.bq_project }}.store_matrix_email.profile_master_id#lastname ."),
  birthdate         DATE              OPTIONS(description="Profile birthdate. ref: {{ params.bq_project }}.store_matrix_email.profile_master_id#birthdate ."),
  gender            STRING            OPTIONS(description="Profile gender. ref: {{ params.bq_project }}.store_matrix_email.profile_master_id#gender ."),
  address           STRING            OPTIONS(description="Profile address. ref: {{ params.bq_project }}.store_matrix_email.profile_master_id#adress ."),
  city              STRING            OPTIONS(description="Profile city. ref: {{ params.bq_project }}.store_matrix_email.profile_master_id#city ."),
  zipcode           STRING            OPTIONS(description="Profile zipcode. ref: {{ params.bq_project }}.store_matrix_email.profile_master_id#zipcode ."),
  country           STRING            OPTIONS(description="Profile country. ref: {{ params.bq_project }}.store_matrix_email.profile_master_id#country ."),
  phone_number      STRING            OPTIONS(description="Profile phone number. ref: {{ params.bq_project }}.business_data.profile_digital_360#phone."),
  create_date       DATE              OPTIONS(description="Profile creation date. ref: {{ params.bq_project }}.store_matrix_email.profile_master_id#create_date ."),
  update_date       DATE              OPTIONS(description="Profile last update date. ref: {{ params.bq_project }}.store_matrix_email.profile_master_id#update_date .")

)
OPTIONS(description="This table contains historical optin profiles exported to Le Figaro based on these conditions 👇🏻 : \n"||
                    "1. JIRA Ticket 👉🏻 https://pmdtech.atlassian.net/browse/MOZ-782 \n"||
                    "2. JIRA Ticket 👉🏻 https://pmdtech.atlassian.net/browse/MOZ-783 \n"||
                    "3. JIRA Ticket 👉🏻 https://pmdtech.atlassian.net/browse/MOZ-784 \n"||
                    "4. JIRA Ticket 👉🏻 https://pmdtech.atlassian.net/browse/MOZ-785 \n"||
                    "DAG : {{ dag.dag_id }}. \n"||
                    "Sync: Daily."
);
MERGE `{{ params.bq_project }}.{{ params.bq_dataset }}.optin_profiles_history` AS dst
USING (
  SELECT
    DATE("{{ next_ds }}") AS observation_date,
    lefop.*
  FROM `{{ params.bq_project }}.{{ params.bq_dataset }}.{{ params.bq_table }}` AS lefop
) AS ref
ON
  ref.observation_date = dst.observation_date
  AND
  ref.email = dst.email

WHEN MATCHED THEN
UPDATE SET
  dst.first_name = ref.first_name,
  dst.last_name = ref.last_name,
  dst.birthdate = ref.birthdate,
  dst.gender = ref.gender,
  dst.address = ref.address,
  dst.city = ref.city,
  dst.zipcode = ref.zipcode,
  dst.country = ref.country,
  dst.phone_number = ref.phone_number,
  dst.create_date = ref.create_date,
  dst.update_date = ref.update_date

WHEN NOT MATCHED BY TARGET THEN
INSERT(observation_date, email, first_name, last_name, birthdate, gender, address, city, zipcode, country, phone_number, create_date, update_date)
VALUES(ref.observation_date, ref.email, ref.first_name, ref.last_name, ref.birthdate, ref.gender, ref.address, ref.city, ref.zipcode, ref.country, ref.phone_number, ref.create_date, ref.update_date);
