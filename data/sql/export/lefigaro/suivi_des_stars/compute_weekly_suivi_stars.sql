-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.{{ params.bq_dataset }}.{{ params.bq_table }}`(
    profile_email            STRING      NOT NULL  OPTIONS(description="profile email."),
    bookmark_content_id      STRING      NOT NULL  OPTIONS(description="bookmark content id."),
    technical_id             STRING      NOT NULL  OPTIONS(description="technical bookmark id."),
    bookmark_content_title   STRING      NOT NULL  OPTIONS(description="bookmark content title"),
    sub_date                 TIMESTAMP             OPTIONS(description="bookmark content subscription datetime."),
    unsub_date               TIMESTAMP             OPTIONS(description="bookmark content unsubscription datetime.")
)
OPTIONS(
    description="This table contains Suivi des Stars data to be exported to Gala teams.\n\n"
              ||"DAG: {{ dag.dag_id }}.\n\n"
              ||"Sync: Weekly.",
    labels=[
        ('project', 'gala'),
        ('scope', 'suivi_des_stars'),
        ('owner', 'rodrigo_santana'),
        ('backup_owner', 'anes_ben_ramdhan'),
        ('context', 'production')
    ]
);

TRUNCATE TABLE `{{ params.bq_project }}.{{ params.bq_dataset }}.{{ params.bq_table }}`;

INSERT INTO `{{ params.bq_project }}.{{ params.bq_dataset }}.{{ params.bq_table }}`
SELECT
    profile_email,
    bookmark_content_id,
    technical_id,
    bookmark_content_title,
    sub_date,
    unsub_date
FROM `pm-{{ params.env }}-userhub.generated_data.gala_follow_stars_profile_lifecycle`
{% if params.is_staging %}
ORDER BY sub_date DESC
LIMIT {{ params.sample_size }}
{% endif %}
;
