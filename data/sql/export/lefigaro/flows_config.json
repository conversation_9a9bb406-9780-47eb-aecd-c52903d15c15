[{"secret_key": {"preprod": {"project_id": 83423634369, "secret_id": "airflow-connections-aws_s3_le_figaro", "secret_version": "latest"}, "prod": {"project_id": 386359793924, "secret_id": "airflow-connections-aws_s3_le_figaro", "secret_version": "latest"}}, "flow_name": "optin_profiles", "flow_description": "Compute Gala optin profiles daily", "flow_sql_file_name": {"compute": "optin_profiles/compute_optin_profiles.sql", "archive": "optin_profiles/archive_optin_profiles.sql"}, "bq_project": {"preprod": {"matrix": "pm-preprod-matrix"}, "prod": {"matrix": "pm-prod-matrix"}}, "gcs_bucket": {"preprod": {"matrix": "gs://it-data-preprod-matrix-preprod-pipeline/"}, "prod": {"matrix": "gs://it-data-prod-matrix-pipeline/"}}, "bq_dataset": "export_lefigaro", "bq_table": "optin_profiles", "is_staging": false, "is_full": false, "sample_size": 10, "cron_expression": "0 1 * * *", "flow_state": "ON", "source_file_name": {"preprod": "<current_date_no_dash>_optin_gala_sample", "prod": "<current_date_no_dash>_optin_gala"}, "destination_path": "s3://lefigaro-app/Input/crm/gala/full/profil/year=<current_year>/month=<current_month>/day=<current_day>/", "destination_file_name": {"preprod": "<current_date_no_dash>_profil_gala_sample.csv", "prod": "<current_date_no_dash>_profil_gala.csv"}}, {"secret_key": {"preprod": {"project_id": 83423634369, "secret_id": "airflow-connections-aws_s3_le_figaro", "secret_version": "latest"}, "prod": {"project_id": 386359793924, "secret_id": "airflow-connections-aws_s3_le_figaro", "secret_version": "latest"}}, "flow_name": "optin_consent_profiles", "flow_description": "Compute Gala optin profiles by consent daily", "flow_sql_file_name": {"compute": "optin_consent_profiles/compute_optin_consent_profiles.sql", "archive": "optin_consent_profiles/archive_optin_consent_profiles.sql"}, "bq_project": {"preprod": {"matrix": "pm-preprod-matrix"}, "prod": {"matrix": "pm-prod-matrix"}}, "gcs_bucket": {"preprod": {"matrix": "gs://it-data-preprod-matrix-preprod-pipeline/"}, "prod": {"matrix": "gs://it-data-prod-matrix-pipeline/"}}, "is_staging": false, "is_full": false, "sample_size": 10, "cron_expression": "0 1 * * *", "flow_state": "ON", "bq_dataset": "export_lefigaro", "bq_table": "optin_consent_profiles", "source_file_name": {"preprod": "<current_date_no_dash>_optin_consent_gala_sample", "prod": "<current_date_no_dash>_optin_consent_gala"}, "destination_path": "s3://lefigaro-app/Input/crm/gala/full/optin/year=<current_year>/month=<current_month>/day=<current_day>/", "destination_file_name": {"preprod": "<current_date_no_dash>_optin_gala_sample.csv", "prod": "<current_date_no_dash>_optin_gala.csv"}}, {"secret_key": {"preprod": {"project_id": 83423634369, "secret_id": "airflow-connections-aws_s3_le_figaro", "secret_version": "latest"}, "prod": {"project_id": 386359793924, "secret_id": "airflow-connections-aws_s3_le_figaro", "secret_version": "latest"}}, "flow_name": "weekly_stats", "flow_description": "Compute Gala weekly stats (optin & optout volumes) by consent (NL, part)", "flow_sql_file_name": {"compute": "weekly_stats/compute_weekly_stats.sql", "archive": "weekly_stats/archive_weekly_stats.sql"}, "bq_project": {"preprod": {"matrix": "pm-preprod-matrix"}, "prod": {"matrix": "pm-prod-matrix"}}, "gcs_bucket": {"preprod": {"matrix": "gs://it-data-preprod-matrix-preprod-pipeline/"}, "prod": {"matrix": "gs://it-data-prod-matrix-pipeline/"}}, "bq_dataset": "export_lefigaro", "bq_table": "weekly_stats", "is_staging": false, "is_full": false, "sample_size": 10, "cron_expression": "0 1 * * mon", "flow_state": "ON", "source_file_name": {"preprod": "<current_date_no_dash>_weekly_stats_sample", "prod": "<current_date_no_dash>_weekly_stats"}, "destination_path": "s3://lefigaro-app/Input/crm/gala/full/stats/year=<current_year>/month=<current_month>/day=<current_day>/", "destination_file_name": {"preprod": "<current_date_no_dash>_weekly_stats_sample.csv", "prod": "<current_date_no_dash>_weekly_stats.csv"}}, {"secret_key": {"preprod": {"project_id": 83423634369, "secret_id": "airflow-connections-aws_s3_le_figaro", "secret_version": "latest"}, "prod": {"project_id": 386359793924, "secret_id": "airflow-connections-aws_s3_le_figaro", "secret_version": "latest"}}, "flow_name": "weekly_suivi_stars", "flow_description": "Weekly flow containing Suivi des Stars data", "flow_sql_file_name": {"compute": "suivi_des_stars/compute_weekly_suivi_stars.sql", "archive": "suivi_des_stars/archive_weekly_suivi_stars.sql"}, "bq_project": {"preprod": {"matrix": "pm-preprod-matrix"}, "prod": {"matrix": "pm-prod-matrix"}}, "gcs_bucket": {"preprod": {"matrix": "gs://it-data-preprod-matrix-preprod-pipeline/"}, "prod": {"matrix": "gs://it-data-prod-matrix-pipeline/"}}, "bq_dataset": "export_lefigaro", "bq_table": "weekly_suivi_stars", "is_staging": false, "is_full": true, "sample_size": 10, "cron_expression": "0 1 * * mon", "flow_state": "ON", "source_file_name": {"preprod": "<current_date_no_dash>_alerte_star_sample", "prod": "<current_date_no_dash>_alerte_star"}, "destination_path": "s3://lefigaro-app/Input/crm/gala/full/star/year=<current_year>/month=<current_month>/day=<current_day>/", "destination_file_name": {"preprod": "<current_date_no_dash>_alerte_star_sample.csv", "prod": "<current_date_no_dash>_alerte_star.csv"}}]