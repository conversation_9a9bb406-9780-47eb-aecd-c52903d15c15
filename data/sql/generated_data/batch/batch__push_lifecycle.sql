-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

{% if params.full_export %}
-- Full import
DROP TABLE IF EXISTS `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_histo }}`;
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_histo }}` (
  install_id          STRING      NOT NULL    OPTIONS(description="ref: store_batch.event_<brand_trigram>_<support>#install_id"),
  support             STRING      NOT NULL    OPTIONS(description="support enum=[web, ios, android]"),
  brand_trigram       STRING      NOT NULL    OPTIONS(description="brand trigram enum=[CAC,FAC,CAP,VOI,...]"),
  optin_date          TIMESTAMP               OPTIONS(description="push sub datetime. ref: store_batch.event_<brand_trigram>_<support>#create_date"),
  optout_date         TIMESTAMP               OPTIONS(description="push unsub datetime. ref: store_batch.event_<brand_trigram>_<support>#create_date"),
  deleted_date        TIMESTAMP               OPTIONS(description="uninstall datetime. ref: store_batch.event_<brand_trigram>_<support>#create_date"),
  push_count          INTEGER                 OPTIONS(description="push volume before unsub at current date. ref: store_batch.event_<brand_trigram>_<support>#create_date"),
  last_push_date      TIMESTAMP               OPTIONS(description="last push datetime before unsub. ref: store_batch.event_<brand_trigram>_<support>#create_date")
-- , status              STRING                  OPTIONS(description="status enum=['push_optin', 'push_optout', 'token_deleted']")
)
PARTITION BY TIMESTAMP_TRUNC(optin_date, DAY)
OPTIONS(description="push sub/unsub/uninstall lifecycle\n"
                  ||"We generate data from the refined_data dataset"
                  ||"\n\n"
                  ||"DAG: {{ dag.dag_id }}"
                  ||"Update: Daily"
                  );

INSERT INTO `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_histo }}` (
  WITH push_optin AS (
    SELECT DISTINCT
      install_id    AS install_id,
      support       AS support,
      brand_trigram AS brand_trigram,
      create_date   AS optin_date
    FROM `{{ params.bq_project }}.{{ params.bq_dataset_refined }}.batch_event`
    WHERE DATE(create_date) >= "2022-02-07" AND DATE(create_date) <= DATE_SUB(CURRENT_DATE(), INTERVAL {{ params.interval }}) AND
          event_type  = "push_optin"
  ), push_optin_status AS (
    SELECT
      po.install_id                                              AS install_id,
      po.support                                                 AS support,
      po.brand_trigram                                           AS brand_trigram,
      po.optin_date                                              AS optin_date,
      MIN(IF(event_type="push_optout", pod.create_date, NULL))   AS optout_date,
      MIN(IF(event_type="token_deleted", pod.create_date, NULL)) AS deleted_date
    FROM push_optin AS po
    LEFT JOIN `{{ params.bq_project }}.{{ params.bq_dataset_refined }}.batch_event` AS pod
      ON pod.install_id = po.install_id AND
         pod.support = po.support AND
         pod.brand_trigram = po.brand_trigram AND
         pod.create_date >= po.optin_date
    WHERE DATE(create_date) >= "2022-02-07" AND DATE(create_date) <= DATE_SUB(CURRENT_DATE(), INTERVAL {{ params.interval }}) AND
          event_type IN ("push_optin","push_optout","token_deleted")
    GROUP BY 1, 2, 3, 4
  ), push_sent AS (
    SELECT
      install_id,
      support,
      brand_trigram,
      create_date,
      event_type
    FROM `{{ params.bq_project }}.{{ params.bq_dataset_refined }}.batch_event`
    WHERE DATE(create_date) >= "2022-02-07" AND DATE(create_date) <= DATE_SUB(CURRENT_DATE(), INTERVAL {{ params.interval }}) AND
      event_type = "push_sent"
  )

  SELECT
    po.install_id,
    po.support,
    po.brand_trigram,
    po.optin_date,
    MIN(po.optout_date) AS optout_date,
    MIN(po.deleted_date) AS deleted_date,
    COUNTIF(ps.event_type="push_sent") AS push_count,
    MAX(ps.create_date) AS last_push_date
  FROM push_optin_status AS po
  LEFT JOIN push_sent AS ps
    ON ps.install_id = po.install_id AND
      ps.support = po.support AND
      ps.brand_trigram = po.brand_trigram AND (
        (
          po.optin_date IS NULL OR
          ps.create_date >= po.optin_date
        ) AND (
          po.optout_date IS NULL OR
          ps.create_date <= po.optout_date
        ) AND (
          po.deleted_date IS NULL OR
          ps.create_date <= po.deleted_date
        )
      )
  WHERE po.optin_date <= po.optout_date OR
        po.optout_date IS NULL
  GROUP BY 1, 2, 3, 4
  ORDER BY 1, 2, 3, 4, 5, 6
);

{% else %}
-- Incremental
DROP TABLE `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_maxdate }}`;
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_maxdate }}` (
  max_date    DATE     NOT NULL      OPTIONS(description="equal to max(optin_date,optout_date,deleted_date,last_push_date) in `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_histo }}`")
)
OPTIONS(description="max date into table `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_histo }}`"
      ||"\n\n"
      ||"DAG: {{ dag.dag_id }}"
      ||"Update: Daily");

INSERT INTO `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_maxdate }}` (
  WITH max_dates AS (
    SELECT
      DATE(MAX(optin_date)) AS optin_max,
    DATE(MAX(optout_date)) AS optout_max,
      DATE(MAX(deleted_date)) AS deleted_max,
      DATE(MAX(last_push_date)) AS last_push_max
    FROM `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_histo }}`
  )
  SELECT
    CASE
      WHEN optin_max >= optout_max AND optin_max >= deleted_max AND optin_max >= last_push_max THEN optin_max
      WHEN optout_max >= optin_max AND optout_max >= deleted_max AND optout_max >= last_push_max THEN optout_max
      WHEN deleted_max >= optin_max AND deleted_max >= optout_max AND deleted_max >= last_push_max THEN deleted_max
      WHEN last_push_max >= optin_max AND last_push_max >= optout_max AND last_push_max >= deleted_max THEN last_push_max
    END AS max_date
  FROM max_dates
);

DROP TABLE IF EXISTS `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_new }}`;
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_new }}` (
  install_id          STRING      NOT NULL    OPTIONS(description="ref: store_batch.event_<brand_trigram>_<support>#install_id"),
  support             STRING      NOT NULL    OPTIONS(description="support enum=[web, ios, android]"),
  brand_trigram       STRING      NOT NULL    OPTIONS(description="brand trigram enum=[CAC,FAC,CAP,VOI,...]"),
  optin_date          TIMESTAMP               OPTIONS(description="push sub datetime. ref: store_batch.event_<brand_trigram>_<support>#create_date"),
  optout_date         TIMESTAMP               OPTIONS(description="push unsub datetime. ref: store_batch.event_<brand_trigram>_<support>#create_date"),
  deleted_date        TIMESTAMP               OPTIONS(description="uninstall datetime. ref: store_batch.event_<brand_trigram>_<support>#create_date"),
  push_count          INTEGER                 OPTIONS(description="push volume before unsub at current date. ref: store_batch.event_<brand_trigram>_<support>#create_date"),
  last_push_date      TIMESTAMP               OPTIONS(description="last push datetime before unsub. ref: store_batch.event_<brand_trigram>_<support>#create_date")
-- , status              STRING                  OPTIONS(description="status enum=['push_optin', 'push_optout', 'token_deleted']")
) PARTITION BY TIMESTAMP_TRUNC(optin_date, DAY)
OPTIONS(description="push sub/unsub/uninstall lifecycle temporary table"
                    || "We generate data from the refined_data dataset"
                    ||"\n\n"
                    ||"DAG: {{ dag.dag_id }}"
                    ||"Update: Daily");

-- Inserting all new push event (optin, sent, optout, token deleted) in temporary table
INSERT INTO `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_new }}` (
  WITH push_optin AS (
    -- new push optin
    SELECT DISTINCT
      install_id    AS install_id,
      support       AS support,
      brand_trigram AS brand_trigram,
      create_date   AS optin_date
    FROM `{{ params.bq_project }}.{{ params.bq_dataset_refined }}.batch_event`
    WHERE DATE(create_date) > (SELECT max_date FROM `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_maxdate }}`) AND
          event_type  = "push_optin"
  ), push_optin_status AS (
    -- new push status event
    SELECT
      po.install_id                                              AS install_id,
      po.support                                                 AS support,
      po.brand_trigram                                           AS brand_trigram,
      po.optin_date                                              AS optin_date,
      MIN(IF(event_type="push_optout", pod.create_date, NULL))   AS optout_date,
      MIN(IF(event_type="token_deleted", pod.create_date, NULL)) AS deleted_date
    FROM push_optin AS po
    LEFT JOIN `{{ params.bq_project }}.{{ params.bq_dataset_refined }}.batch_event` AS pod
      ON pod.install_id = po.install_id AND
         pod.support = po.support AND
         pod.brand_trigram = po.brand_trigram AND
         pod.create_date >= po.optin_date
    WHERE DATE(pod.create_date) > (SELECT max_date FROM `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_maxdate }}`) AND
          pod.event_type IN ("push_optin","push_optout","token_deleted")
    GROUP BY 1, 2, 3, 4
  ), push_sent AS (
    -- new push sent
    SELECT
      install_id,
      support,
      brand_trigram,
      create_date,
      event_type
    FROM `{{ params.bq_project }}.{{ params.bq_dataset_refined }}.batch_event`
    WHERE DATE(create_date) > (SELECT max_date FROM `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_maxdate }}`) AND
      event_type = "push_sent"
  )

  SELECT
    COALESCE(po.install_id, ps.install_id) AS install_id,
    COALESCE(po.support, ps.support) AS support,
    COALESCE(po.brand_trigram, ps.brand_trigram) AS brand_trigram,
    po.optin_date AS optin_date,
    MIN(po.optout_date) AS optout_date,
    MIN(po.deleted_date) AS deleted_date,
    COUNTIF(ps.event_type="push_sent") AS push_count,
    MAX(ps.create_date) AS last_push_date
  FROM push_optin_status AS po
  FULL JOIN push_sent AS ps
    ON ps.install_id = po.install_id AND
      ps.support = po.support AND
      ps.brand_trigram = po.brand_trigram AND (
        po.optin_date IS NULL OR
        ps.create_date >= po.optin_date
      ) AND (
        po.optout_date IS NULL OR
        ps.create_date <= po.optout_date
      ) AND (
        po.deleted_date IS NULL OR
        ps.create_date <= po.deleted_date
      )
  GROUP BY 1, 2, 3, 4
);

-- For new optin, inserting rows
-- New optin = new install_id OR old install_id with with new optin_date
-- install_id does not change for push optin on web if it's the same brand
INSERT INTO `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_histo }}` (
  SELECT
    install_id,
    support,
    brand_trigram,
    optin_date,
    optout_date,
    deleted_date,
    push_count,
    last_push_date
    -- , NULL AS status
  FROM `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_new }}`
  WHERE optin_date IS NOT NULL AND DATE(optin_date) > (SELECT max_date FROM `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_maxdate }}`)
);

-- For current optin status in push_lifecycle, updating optout_date
UPDATE `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_histo }}` a
SET a.optout_date = COALESCE(b.optout_date)
FROM `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_new }}` b
WHERE a.install_id    = b.install_id    AND
      a.support       = b.support       AND
      a.brand_trigram = b.brand_trigram AND
      (a.optin_date = b.optin_date OR b.optin_date IS NULL) AND
      a.optout_date IS NULL AND
      DATE(b.optout_date) > (SELECT max_date FROM `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_maxdate }}`);

-- For current optin status in push_lifecycle, updating deleted_date
UPDATE `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_histo }}` a
SET a.deleted_date = COALESCE(b.deleted_date)
FROM `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_new }}` b
WHERE a.install_id    = b.install_id    AND
      a.support       = b.support       AND
      a.brand_trigram = b.brand_trigram AND
      (a.optin_date = b.optin_date OR b.optin_date IS NULL) AND
      a.deleted_date IS NULL AND
      DATE(b.deleted_date) >= (SELECT max_date FROM `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_maxdate }}`);

-- For current optin status in push_lifecycle, updating push_count
UPDATE `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_histo }}` a
-- Adding new push sent on old push sent
SET a.push_count = a.push_count + b.push_count
FROM `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_new }}` b
WHERE a.install_id    = b.install_id    AND
      a.support       = b.support       AND
      a.brand_trigram = b.brand_trigram AND
      a.optout_date IS NULL AND
      a.deleted_date IS NULL AND
      b.optin_date IS NULL;

-- For current optin status in push_lifecycle, updating last_push_date
UPDATE `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_histo }}` a
SET a.last_push_date = b.last_push_date
FROM `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_new }}` b
WHERE a.install_id    = b.install_id    AND
      a.support       = b.support       AND
      a.brand_trigram = b.brand_trigram AND
      a.optout_date IS NULL AND
      a.deleted_date IS NULL AND
      b.optin_date IS NULL;

DROP TABLE IF EXISTS `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_new }}`;


-- Updating status
--UPDATE `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_histo }}`
--SET optin_status = 'push_optin'
--WHERE status != 'push_optin' AND
--      optin_date IS NOT NULL AND
--      optout_date IS NULL AND
--      deleted_date IS NULL;
--UPDATE `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_histo }}`
--SET status = 'push_optout'
--WHERE status != 'push_optout' AND
--      optin_date IS NOT NULL AND
--      optout_date IS NOT NULL AND
--      deleted_date IS NULL;
--UPDATE `{{ params.bq_project }}.{{ params.bq_dataset_generated }}.{{ params.push_lifecycle_histo }}`
--SET status = 'token_deleted'
--WHERE status != 'token_deleted' AND
--      optin_date IS NOT NULL AND
--      deleted_date IS NOT NULL;

{% endif %}
