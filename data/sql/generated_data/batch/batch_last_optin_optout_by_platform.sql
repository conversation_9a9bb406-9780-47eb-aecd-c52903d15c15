-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- Based on `last_event_by_platform`, we compute the last optin/optout dates and determine whether a profile is alive.

-- Steps:
-- 1. Create target table (`batch_last_optin_optout_by_platform`) if not exists
-- 2. Copy target table (`batch_last_optin_optout_by_platform_{{ ds_nodash }}`) as backup and for later processing if not exists
-- 3. Truncate target table
-- 4. Get last optin and optout dates by platform using `last_event_by_platform` --> CTEs last_optin_by_user and last_optout_by_user
-- 5. Full join last_optin_by_user and last_optout_by_user --> last_optin_optout_by_user
-- 6. Compare last_optin_date and last_optout_date to determine if user is alive
-- 7. Full join last_optin_optout_by_user and copy table not to need a full scan on `last_event_by_platform`
-- 8. Insert resulting data into target table

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.batch_last_optin_optout_by_platform` (
    install_id          STRING      NOT NULL        OPTIONS(description="install id."),
    support             STRING      NOT NULL        OPTIONS(description="enums=('web', 'mobile')."),
    device_type         STRING      NOT NULL        OPTIONS(description="enums=('web', 'ios', 'android')."),
    brand_trigram       STRING      NOT NULL        OPTIONS(description="brand trigram"),
    last_optin_date     TIMESTAMP                   OPTIONS(description="date of the last optin event. If null, last optin happened before 2023-01-01."),
    last_optout_date    TIMESTAMP                   OPTIONS(description="date of the last optout event."),
    cumulated_optin     INT64                       OPTIONS(description="number of optin events since 2023-01-01."),
    is_alive            BOOL        NOT NULL        OPTIONS(description="true if last_optin_date > last_optout_date."),
    PRIMARY KEY(install_id, device_type, brand_trigram) NOT ENFORCED
)
CLUSTER BY install_id, brand_trigram, support
OPTIONS(description="Contains information about users' status concerning push notifications (last optin and optout dates, cumulated number of optins, if they are alive)."
                  ||"We generate data from last_event_by_platform"
                  ||"This table has data since 2023-01-01. Hence, profiles having opted-in before this date will have NULL as last_optin_date"
                  ||"\n\n"
                  ||"DAG: {{ dag.dag_id }}"
                  ||"\n\n"
                  ||"Update: Daily");

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.batch_last_optin_optout_by_platform_{{ ds_nodash }}` -- yesterday table's snapshot
CLONE `{{ params.bq_project }}.generated_data.batch_last_optin_optout_by_platform`
OPTIONS(expiration_timestamp = TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL {{ params.table_expiration_interval }}));    -- keep as history of changes

TRUNCATE TABLE `{{ params.bq_project }}.generated_data.batch_last_optin_optout_by_platform`;                            -- truncate table to make an insert

INSERT INTO `{{ params.bq_project }}.generated_data.batch_last_optin_optout_by_platform`
WITH get_userbase AS (
-- get users from batch_userbase
-- goal: prepare data for join
  SELECT DISTINCT
    install_id,
    brand_trigram,
    CASE
      WHEN support = 'web' THEN 'web'
      WHEN support = 'ios' THEN 'mobile'
      WHEN support = 'android' THEN 'mobile'
      ELSE NULL
    END     AS support,
    support AS device_type
  FROM `{{ params.bq_project }}.refined_data.batch_userbase`
),

last_optin_by_user AS (
-- get last optin date and event count by user and platform
  SELECT
    install_id,
    brand_trigram,
    CASE
        WHEN support = 'web' THEN 'web'
        WHEN support = 'ios' THEN 'mobile'
        WHEN support = 'android' THEN 'mobile'
        ELSE NULL
    END              AS support,
    support          AS device_type,
    last_event_date  AS last_optin_date,
    event_count      AS sum_optin
  FROM `{{ params.bq_project }}.generated_data.last_event_by_platform`
  WHERE
  {% if not params.is_full %}
    DATE(last_event_date) >= DATE_SUB(DATE("{{ next_ds }}"), INTERVAL 2 DAY)  -- last_event_by_platform recency is D-2 (ex. on 2023-11-29, most recent date is 2023-11-27)
  {% else %}
    DATE(last_event_date) >= "{{ params.start_date }}"
  {% endif %}
    AND event_type = 'push_optin'
),

last_optout_by_user AS (
-- get last optout date by user and platform
    SELECT
        install_id,
        brand_trigram,
        CASE
            WHEN support = 'web' THEN 'web'
            WHEN support = 'ios' THEN 'mobile'
            WHEN support = 'android' THEN 'mobile'
            ELSE NULL
        END             AS support,
        support         AS device_type,
        last_event_date AS last_optout_date
    FROM `{{ params.bq_project }}.generated_data.last_event_by_platform`
    WHERE
    {% if not params.is_full %}
        DATE(last_event_date) >= DATE_SUB(DATE("{{ next_ds }}"), INTERVAL 2 DAY)  -- last_event_by_platform recency is D-2 (ex. on 2023-11-29, most recent date is 2023-11-27)
    {% else %}
        DATE(last_event_date) >= "{{ params.start_date }}"
    {% endif %}
        AND event_type = 'push_optout'
),

last_optin_optout_by_user AS (
-- merge optin / optout dates
    SELECT
        install_id,
        support,
        device_type,
        brand_trigram,
        last_optin_date,
        last_optout_date,
        sum_optin
    FROM get_userbase
    LEFT JOIN last_optin_by_user USING(install_id, support, brand_trigram, device_type)
    LEFT JOIN last_optout_by_user USING(install_id, support, brand_trigram, device_type)
)

SELECT
  install_id,
  support,
  device_type,
  brand_trigram,
  COALESCE(loo.last_optin_date, prev.last_optin_date)    AS last_optin_date,         -- if no optin in the queried period, returns the already existing date
  COALESCE(loo.last_optout_date, prev.last_optout_date)  AS last_optout_date,        -- if no optout in the queried period, returns the already existing date
  IFNULL(COALESCE(sum_optin, prev.cumulated_optin), 1)   AS cumulated_optin,         -- sum with null returns null
  COALESCE(loo.last_optin_date, prev.last_optin_date, '1990-01-02') > COALESCE(loo.last_optout_date, prev.last_optout_date, '1990-01-01') AS is_alive  -- comparison between a null and a date returns null
FROM last_optin_optout_by_user AS loo
FULL OUTER JOIN `{{ params.bq_project }}.generated_data.batch_last_optin_optout_by_platform_{{ ds_nodash }}` AS prev
    USING(install_id, support, brand_trigram, device_type);
