-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project.matrix }}.{{ params.generated_data_dataset }}.affinity_tag` (
    email_profile_master_id   INTEGER          NOT NULL     OPTIONS(description="ref : store_matrix_email.profile_master_id.id"),
    segment_names             ARRAY<STRING>                 OPTIONS(description="affinity tags as list")
)OPTIONS(description="Affinity tags by profile.\n"
                  ||"\n\n"
                  ||"DAG: {{ dag.dag_id }}"
                  ||"Sync: Daily"
);

TRUNCATE TABLE `{{ params.bq_project.matrix }}.{{ params.generated_data_dataset }}.affinity_tag`;

INSERT INTO `{{ params.bq_project.matrix }}.{{ params.generated_data_dataset }}.affinity_tag`
-- Generate affinity_tag.
-- Result : email_profile_master_id, segment_names
WITH segment_data AS (
    -- get all affinity segments
    SELECT DISTINCT
        pe.email_profile_master_id         AS profile_master_id,
        LOWER(_TABLE_SUFFIX) AS segment_name
    FROM `{{ params.bq_project.matrix }}.{{ params.store_segment_dataset }}.segment-sha256_*` AS s
    JOIN `{{ params.bq_project.matrix }}.refined_data.profile_email`  AS pe ON pe.email_sha256 = s.email_sha256
)
-- aggregate segments by profile
SELECT
    s.profile_master_id,
    ARRAY_AGG(s.segment_name ORDER BY s.segment_name) AS segment_names
FROM segment_data  AS s
GROUP BY 1;
