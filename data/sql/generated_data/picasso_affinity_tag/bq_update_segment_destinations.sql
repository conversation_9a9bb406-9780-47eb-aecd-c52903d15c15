-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

{%- macro proces_splio_destination(tag, payload) -%}
    -- Merge into tag_universe table for the 'splio' destination
    MERGE INTO `{{ params.bq_project.matrix }}.store_affinity_segment.tag_universe` AS target
    USING (SELECT '{{tag}}' AS tag, ARRAY[{{ payload.universes|join(',') }}] AS universe_ids) AS source
    ON target.tag = source.tag
    WHEN MATCHED THEN
        UPDATE SET target.universe_ids = source.universe_ids
    WHEN NOT MATCHED THEN
        INSERT (tag, universe_ids) VALUES (source.tag, source.universe_ids);
{%- endmacro -%}

{%- macro proces_batch_destination(tag, payload) -%}
    -- Merge into tag_platform table for the 'batch' destination
    MERGE INTO `{{ params.bq_project.matrix }}.store_affinity_segment.tag_platform` AS target
    USING (
        SELECT '{{ tag }}' AS tag,
        ARRAY_AGG(
            STRUCT<
                brand_name STRING,
                platforms ARRAY<STRING>
            >(brand, ARRAY(SELECT platform FROM UNNEST(platforms) platform))
        ) AS brands
        FROM UNNEST([
            {%- if payload.brands|length %}
                {% for brand, platforms in payload.brands.items() -%}
                STRUCT('{{ brand }}' AS brand, ARRAY['{{ platforms|join("', '") }}'] AS platforms)
                {% if not loop.last %},{% endif %}
                {% endfor -%}
            {% endif %}
        ]) AS brand_struct
        GROUP BY tag
    ) AS source
    ON target.tag = source.tag
    WHEN MATCHED THEN
        UPDATE SET target.brands = source.brands
    WHEN NOT MATCHED THEN
        INSERT (tag, brands) VALUES (source.tag, source.brands);
{%- endmacro -%}

{%- macro process_destination(tag, destination) -%}
    -- For destination: [{{ destination.name }}]
    {% if destination.name == 'splio' -%}
        {{ proces_splio_destination(tag, destination.payload) }}
    {% elif destination.name == 'batch' -%}
        {{ proces_batch_destination(tag, destination.payload) }}
    {% endif -%}
{%- endmacro -%}

{%- macro add_platforms_labels(tag, destination_platforms) -%}
    {%- set label_platforms = [] -%}
    {%- for destination in destination_platforms -%}
        {% set _ = label_platforms.append(destination.name) -%}
    {% endfor -%}
    -- Add activation_platform label to the table {{ params.bq_project.matrix }}.{{ params.store_segment_dataset }}.segment-sha256_{{tag}}
    ALTER TABLE `{{ params.bq_project.matrix }}.{{ params.store_segment_dataset }}.segment-sha256_{{tag}}`
    SET OPTIONS (labels = [('activation_platform', '{{ label_platforms|join("-") }}')]);
{%- endmacro -%}

{%- for theme, variable in params.affinity_variable.items() -%}
    {%- set segment_names = [] -%}
    {%- if variable.has_category -%}
        {%- for level, limits in variable.category.items() -%}
            {%- set _ = segment_names.append('profile_score_' ~ (theme|lower) ~ '_' ~ (level|lower)) -%}
        {% endfor -%}
    {% else -%}
        {%- set _ = segment_names.append('profile_score_' ~ (theme|lower)) -%}
    {% endif -%}

    {%- set destination_platforms = variable.destination_platforms -%}
    {%- for segment_name in segment_names -%}
        {%- for destination in destination_platforms -%}
            {{- process_destination(segment_name, destination) -}}
        {% endfor -%}
        {{- add_platforms_labels(segment_name, destination_platforms) }}
    {% endfor -%}
{% endfor -%}