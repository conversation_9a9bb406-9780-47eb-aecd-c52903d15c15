-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{params.bq_project}}.generated_data.prisma_connect_service` (
    brand_trigram       STRING                  OPTIONS(description="ref : store_karinto.brand.name"),
    service             STRING      NOT NULL    OPTIONS(description="service name"),
    enter_date          TIMESTAMP   NOT NULL    OPTIONS(description="Creation date of PMC profile."),
    last_activity_date  TIMESTAMP               OPTIONS(description="PMC last activity date. ref : store_matrix_pmc.profile.last_activity_date"),
    exit_date           TIMESTAMP               OPTIONS(description="Deletion date of PMC profile"),
    email_pmi           INTEGER     NOT NULL    OPTIONS(description="ref : store_matrix_email.profile_master_id.id"),
    pmc_pmi             INTEGER                 OPTIONS(description="ref : store_matrix_pmc.profile_master_id_v2.id"),
    pmc_cgu_validated   BOOLEAN     NOT NULL    OPTIONS(description="Has the PMC profile validated the CGU ?")
) OPTIONS(
  expiration_timestamp=NULL,
  description="PMC service card.\n"
              || "\n\n"
              || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);

-- previously scheduled query `service_pmc_crm`, every day 04:00 am
TRUNCATE TABLE `{{params.bq_project}}.generated_data.prisma_connect_service`;

INSERT INTO `{{params.bq_project}}.generated_data.prisma_connect_service`
-- all profiles related to PMC service (prisma_connect_crm)
WITH pmc_profiles AS (
    -- inspired by `compute_dmp_orders.sql`
    SELECT
        d.* EXCEPT(rank),
        -- we use karinto brand table (see below)
        kb.trigram AS brand_trigram
    FROM (
        SELECT
            p.pmc_profile_master_id AS profile_master_id,
            p.email_md5,
            p.update_date,
            p.system_data.last_activity_date,
            p.service.has_valid_cgu,
            p.service.source_brand_trigram AS pmc_brand_trigram,
            -- email_md5 can be common among more than one profile,
            -- when duplicates co-exist, we keep the "latest active" one.
            -- (there are approx. 10k such 'redundant' profiles as of mid-2021)
            ROW_NUMBER() OVER(PARTITION BY p.email_md5
                              ORDER BY COALESCE(p.system_data.last_activity_date, p.system_data.last_login_date) DESC) AS rank
        FROM `{{params.bq_project}}.refined_data.profile_pmc`         AS p
    ) AS d
    -- use store_karinto.brand to determine if the brand is prisma or cerise or none
    LEFT JOIN `{{params.bq_project}}.store_karinto.brand` AS kb
        -- special case : kb.trigram = BI for BIN in pmc
        ON kb.trigram = IF(d.pmc_brand_trigram = 'BIN', 'BI', d.pmc_brand_trigram)
    -- keep only one email_md5
    WHERE rank = 1
), pmc_crm AS (
    SELECT id
    FROM `{{params.bq_project}}.store_karinto.email_consent`
    WHERE public_ref = 'prisma_connect_crm'
)
SELECT
    pmc.brand_trigram                   AS brand_trigram,
    'prisma_connect'                    AS service,
    pec.create_date                     AS enter_date,
    pmc.last_activity_date              AS last_activity_date,
    IF(consent_status = 'unsub', pec.update_date, NULL) AS exit_date,
    pec.profile_master_id               AS email_pmi,
    pmc.profile_master_id               AS pmc_pmi,
    COALESCE(pmc.has_valid_cgu, false)  AS pmc_cgu_validated
FROM `{{params.bq_project}}.store_matrix_email.profiles_email_consents` AS pec
JOIN `{{params.bq_project}}.store_matrix_email.profile_master_id` AS pmi ON pmi.id = pec.profile_master_id
JOIN pmc_profiles AS pmc ON pmc.email_md5 = pmi.email_md5
CROSS JOIN pmc_crm
WHERE pec.email_consent_id = pmc_crm.id
;

CREATE TABLE IF NOT EXISTS `generated_data.service_nl_free` (
    brand_trigram       STRING                  OPTIONS(description="ref : store_karinto.brand.name"),
    service             STRING      NOT NULL    OPTIONS(description="service name"),
    enter_date          TIMESTAMP   NOT NULL    OPTIONS(description="Creation date of profile."),
    last_activity_date  TIMESTAMP               OPTIONS(description="last activity date, ie. last click date."),
    exit_date           TIMESTAMP               OPTIONS(description="Deletion date of profile"),
    email_pmi           INTEGER     NOT NULL    OPTIONS(description="ref : store_matrix_email.profile_master_id.id"),
    pmc_pmi             INTEGER                 OPTIONS(description="ref : store_matrix_pmc.profile_master_id_v2.id")
) OPTIONS(
  expiration_timestamp=NULL,
  description="Newsletter service card, with free subscription.\n"
              || "\n\n"
              || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);

-- previously scheduled query `service_nl_free`, every day 04:00 am
TRUNCATE TABLE `generated_data.service_nl_free`;

INSERT INTO `generated_data.service_nl_free`
WITH nl_sub_per_profile_and_brand AS (
    -- flatten subscription to newsletters by (profile, brand)
    SELECT
        kb.trigram              AS brand_trigram,
        pec.profile_master_id   AS email_pmi,
        MIN(pec.create_date)    AS min_create_date,
        MAX(pec.update_date)    AS max_update_date,
        COUNT(*)                AS nb_consent,
        COUNTIF(consent_status = 'sub')     AS nb_sub,
        COUNTIF(consent_status = 'unsub')   AS nb_unsub
    FROM `{{params.bq_project}}.store_matrix_email.profiles_email_consents` AS pec
    JOIN `{{params.bq_project}}.store_karinto.email_consent` AS kec ON pec.email_consent_id = kec.id AND kec.type = 'nl'
    JOIN `{{params.bq_project}}.store_karinto.brand` AS kb ON kb.id = kec.brand_id
    GROUP BY 1, 2
), nl_sub_per_profile AS (
    -- flatten subscription to newsletters by profile only, using previous CTE
    SELECT
        email_pmi,
        MIN(min_create_date) AS min_create_date,
        MAX(max_update_date) AS max_update_date,
        SUM(nb_consent)      AS nb_consent,
        SUM(nb_sub)          AS nb_sub,
        SUM(nb_unsub)        AS nb_unsub
    FROM nl_sub_per_profile_and_brand
    GROUP BY 1
), nl_activity_per_profile_and_brand AS (
    -- get last click date per profile and brand during the last 3 years
    SELECT
        profile_master_id AS email_pmi,
        -- function code saved below
        `{{params.bq_project}}.store_tracking.host_to_brand`(http_host) AS brand_trigram,
        MAX(datetime) AS click_last_date
    FROM `{{params.bq_project}}.store_tracking.prisma_full_data`
    WHERE DATE(datetime) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 YEAR)
      AND type = 'click'
      -- function code save below
      AND `{{params.bq_project}}.store_tracking.is_nl_host`(http_host) = True
    GROUP BY 1,2
), nl_activity_per_profile AS (
    -- compute last click date per profile
    SELECT
        email_pmi,
        MAX(click_last_date) AS click_last_date
    FROM nl_activity_per_profile_and_brand
    GROUP BY 1
), service_card_nl AS (
    -- join previous CTE and compute enter_date and exit_date
        SELECT
            sub.brand_trigram,
            sub.min_create_date AS enter_date,
            act.click_last_date AS last_activity_date,
            -- user has existed if all email consent status = unsub
            IF(sub.nb_consent = sub.nb_unsub, sub.max_update_date, null) AS exit_date,
            sub.email_pmi
        FROM nl_sub_per_profile_and_brand AS sub
        LEFT JOIN nl_activity_per_profile_and_brand AS act ON sub.email_pmi = act.email_pmi
                                                     AND sub.brand_trigram = act.brand_trigram
    UNION ALL
        SELECT
            'ANY'               AS brand_trigram,
            sub.min_create_date AS enter_date,
            act.click_last_date AS last_activity_date,
            -- user has existed if all email consent status = unsub
            IF(sub.nb_consent = sub.nb_unsub, sub.max_update_date, null) AS exit_date,
            sub.email_pmi
        FROM nl_sub_per_profile AS sub
        LEFT JOIN nl_activity_per_profile AS act ON sub.email_pmi = act.email_pmi
)
SELECT
    d.brand_trigram,
    'nl_free' AS service,
    d.enter_date,
    d.last_activity_date,
    d.exit_date,
    d.email_pmi AS email_pmi,
    pmc.pmc_pmi AS pmc_pmi
FROM service_card_nl AS d
JOIN `{{params.bq_project}}.store_matrix_email.profile_master_id` AS pmi ON pmi.id = d.email_pmi
-- try getting pmc profile_master_id
LEFT JOIN `{{params.bq_project}}.generated_data.prisma_connect_service` AS pmc ON pmc.email_pmi = d.email_pmi
-- for security, email can not be null
WHERE pmi.email IS NOT NULL;


-- CREATE OR REPLACE FUNCTION `pm-prod-matrix.store_tracking.host_to_brand`(http_host STRING) RETURNS STRING
-- OPTIONS (description="Convert tracking hostname to brand trigram (ref: store_karinto.brand.trigram)\nNote : hostname must begins with 'open.' or 'click.'.") AS (
-- CASE REPLACE(REPLACE(http_host, 'open.', ''), 'click.', '')
--         WHEN "bifr-news.fr"             THEN "BI"
--         WHEN "caminteresse-news.fr"     THEN "CAM"
--         WHEN "capital-news.fr"          THEN "CAP"
--         WHEN "cuisine-news.fr"          THEN "CAC"
--         WHEN "femmeactuelle-news.fr"    THEN "FAC"
--         WHEN "serengo-news.fr"          THEN "FAC"
--         WHEN "gala-news.fr"             THEN "GAL"
--         WHEN "gentside-news.fr"         THEN "GEN"
--         WHEN "geo-mag.fr"               THEN "GEO"
--         WHEN "hbrfrance-news.fr"        THEN "HBR"
--         WHEN "listen-news.fr"           THEN "LIS"
--         WHEN "neon-news.fr"             THEN "NEO"
--         WHEN "ohmymag-news.fr"          THEN "OMM"
--         WHEN "t2s-news.fr"              THEN "T2S"
--         WHEN "tv-news.fr"               THEN "TEL"
--         WHEN "voici-news.fr"            THEN "VOI"
--         -- partners
--         WHEN "bases-capital.com"			THEN "CAP"
--         WHEN "bases-cuisineactuelle.com"	THEN "CAC"
--         WHEN "bases-femmeactuelle.com"		THEN "FAC"
--         WHEN "bases-gala.com"				THEN "GAL"
--         WHEN "bases-gentside.com"			THEN "GEN"
--         WHEN "bases-geo.com"				THEN "GEO"
--         WHEN "bases-gs.com"					THEN "GEN"
--         WHEN "bases-ohmymag.com"			THEN "OMM"
--         WHEN "bases-omm.com"				THEN "OMM"
--         WHEN "bases-prisma.com"				THEN "PRM"
--         WHEN "bases-tele2semaines.com"		THEN "T2S"
--         WHEN "bases-teleloisirs.com"		THEN "TEL"
--         WHEN "bases-voici.com"				THEN "VOI"
--         -- old domains
--         WHEN "beauteaddict-news.fr"			THEN "FAC"
--         WHEN "capital-mail.fr"				THEN "CAP"
--         WHEN "gentside-emails.com"			THEN "GEN"
--         WHEN "gentside-news.eu"				THEN "GEN"
--         WHEN "hellocoton-news.fr"			THEN "FAC"
--         WHEN "neon-news.fr"					THEN "NEO"
--         WHEN "ohmymag-news.eu"				THEN "OMM"
--         WHEN "prismaconnect-gestion.fr"		THEN "PMC"
--         WHEN "prisme-news.com"				THEN "PRM"
--         WHEN "serengo-mail.fr"				THEN "FAC"
--         WHEN "videos-news.fr"				THEN "TEL"
--     ELSE ''
--     END
-- );

-- CREATE OR REPLACE FUNCTION `pm-prod-matrix.store_tracking.is_nl_host`(http_host STRING) RETURNS BOOL
-- OPTIONS (description="Tell whether a hostname is used for newsletter (or email alert).") AS (
-- CASE REPLACE(REPLACE(http_host, 'open.', ''), 'click.', '')
--         WHEN "bifr-news.fr"             THEN True
--         WHEN "caminteresse-news.fr"     THEN True
--         WHEN "capital-news.fr"          THEN True
--         WHEN "cuisine-news.fr"          THEN True
--         WHEN "femmeactuelle-news.fr"    THEN True
--         WHEN "serengo-news.fr"          THEN True
--         WHEN "gala-news.fr"             THEN True
--         WHEN "gentside-news.fr"         THEN True
--         WHEN "geo-mag.fr"               THEN True
--         WHEN "hbrfrance-news.fr"        THEN True
--         WHEN "listen-news.fr"           THEN True
--         WHEN "neon-news.fr"             THEN True
--         WHEN "ohmymag-news.fr"          THEN True
--         WHEN "t2s-news.fr"              THEN True
--         WHEN "tv-news.fr"               THEN True
--         WHEN "voici-news.fr"            THEN True
--     ELSE False
--     END
-- );
