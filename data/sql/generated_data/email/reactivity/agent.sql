-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- partition optim : get last open / click date processed by this process.
DECLARE open_start_date  DATE;
DECLARE click_start_date DATE;
DECLARE default_date DEFAULT DATE("1900-01-01");

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.last_activity_by_useragent`(
    email_profile_master_id     INTEGER                 OPTIONS(description="email profile Id. ref: {{ params.bq_project }}.store_matrix_pmc.profile_master_id_v2.id"),
    universe_name               STRING                  OPTIONS(description="Splio universe name. universe_name = 'not set' for partenaires (reviera, webrivage) tracking. ref: {{ params.bq_project }}.store_karinto.universe#name"),
    brand_trigram               STRING                  OPTIONS(description="brand trigram. ref: {{ params.bq_project }}.refined_data.email_base"),
    email_consent_public_ref    STRING                  OPTIONS(description="email consent public ref. ref: {{ params.bq_project }}.refined_data.email_base"),
    last_open_date              DATE                    OPTIONS(description="last open date. ref: {{ params.bq_project }}.refined_data.email_event_open"),
    last_click_date             DATE                    OPTIONS(description="last click date. ref: {{ params.bq_project }}.refined_data.email_event_click"),
    user_agent                  STRING                  OPTIONS(description="user agent which corresponds to the user agent that performed the opening, ref: refined_data.user_agent.")
)
OPTIONS(description="Last activity (open, click) by base(nl, part, loy, ...)"
                  ||"\n\n"
                  ||"DAG: {{ dag.dag_id }}."
                  ||"\n"
                  ||"Sync: daily");

{% if params.is_full %}
    TRUNCATE TABLE `{{ params.bq_project }}.generated_data.last_activity_by_useragent`;
    SET open_start_date  = default_date;
    SET click_start_date = default_date;
{% else %}
    SET open_start_date  = (SELECT DATE_SUB(DATE("{{ next_ds }}"), INTERVAL {{ params.interval }}));
    SET click_start_date = (SELECT DATE_SUB(DATE("{{ next_ds }}"), INTERVAL {{ params.interval }}));
{% endif %}

MERGE `{{ params.bq_project }}.generated_data.last_activity_by_useragent` AS dst
USING (
    WITH reactivity_open AS (
        -- get last open date by consent
        SELECT
            email_profile_master_id,
            universe_name,
            eb.brand_trigram,
            email_consent_public_ref,
            user_agent,
            MAX(DATE(open_date)) AS last_open_date
        FROM (
            SELECT email_profile_master_id, email_consent_public_ref, IFNULL(universe_name, "not set") AS universe_name, open_date, user_agent
            FROM `{{ params.bq_project }}.refined_data.email_event_open`
            WHERE DATE(open_date) >= open_start_date
            UNION ALL
            SELECT email_profile_master_id, email_consent_public_ref, IFNULL(universe_name, "not set") AS universe_name, open_date, user_agent
            FROM `{{ params.bq_project }}.generated_data.email_event_simulated_open`
            WHERE DATE(open_date) >= open_start_date
        ) AS ro
        LEFT JOIN `{{ params.bq_project }}.refined_data.email_base` AS eb ON eb.consent_public_ref = ro.email_consent_public_ref
        GROUP BY 1, 2, 3, 4, 5
    ), reactivity_click AS (
        -- get last click date by consent
        SELECT
            email_profile_master_id,
            IFNULL(universe_name, "not set") AS universe_name,
            IFNULL(eb.brand_trigram, "not set") AS brand_trigram,
            IFNULL(email_consent_public_ref ,"not set") AS email_consent_public_ref,
            click_info.user_agent AS user_agent,
            MAX(DATE(click_date)) AS last_click_date
        FROM `{{ params.bq_project }}.refined_data.email_event_click` AS rc, UNNEST(click_info) AS click_info
            --- using a left join to include all activity (including the nl shopping related)
        LEFT JOIN `{{ params.bq_project }}.refined_data.email_base` AS eb ON eb.consent_public_ref = rc.email_consent_public_ref
        WHERE
            DATE(click_date) >= click_start_date
        GROUP BY 1, 2, 3, 4, 5
    )
    -- merge open & click reactivity
    SELECT DISTINCT
        COALESCE(ro.email_profile_master_id, rc.email_profile_master_id)   AS email_profile_master_id,
        COALESCE(ro.universe_name, rc.universe_name)                       AS universe_name,
        COALESCE(ro.brand_trigram, rc.brand_trigram)                       AS brand_trigram,
        COALESCE(ro.email_consent_public_ref, rc.email_consent_public_ref) AS email_consent_public_ref,
        last_open_date,
        last_click_date,
        COALESCE(ro.user_agent, rc.user_agent) AS user_agent
    FROM reactivity_open       AS ro
    FULL JOIN reactivity_click AS rc USING(email_profile_master_id, email_consent_public_ref, brand_trigram, universe_name, user_agent)
) AS ref
    ON dst.email_profile_master_id = ref.email_profile_master_id
    AND dst.email_consent_public_ref = ref.email_consent_public_ref
    AND dst.brand_trigram = ref.brand_trigram
    AND dst.universe_name = ref.universe_name
    AND dst.user_agent = ref.user_agent
WHEN MATCHED THEN
    -- set last_open_date and last_click_date to the latest value
    UPDATE SET
        last_open_date  = IF(
                            GREATEST(IFNULL(dst.last_open_date, default_date), IFNULL(ref.last_open_date, default_date)) = default_date,
                            NULL,
                            GREATEST(IFNULL(dst.last_open_date, default_date), IFNULL(ref.last_open_date, default_date))
                            ),
        last_click_date = IF(
                            GREATEST(IFNULL(dst.last_click_date, default_date), IFNULL(ref.last_click_date, default_date)) = default_date,
                            NULL,
                            GREATEST(IFNULL(dst.last_click_date, default_date), IFNULL(ref.last_click_date, default_date))
                            )
WHEN NOT MATCHED BY TARGET THEN
    INSERT (email_profile_master_id, universe_name, brand_trigram, email_consent_public_ref, last_open_date, last_click_date, user_agent)
    VALUES (ref.email_profile_master_id, ref.universe_name, ref.brand_trigram, ref.email_consent_public_ref, ref.last_open_date, ref.last_click_date, ref.user_agent);
