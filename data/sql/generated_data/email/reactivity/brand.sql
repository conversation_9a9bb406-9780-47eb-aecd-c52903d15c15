-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- partition optim : get last open / click date processed by this process.
DECLARE open_start_date  DATE;
DECLARE click_start_date DATE;
DECLARE default_date DEFAULT DATE("1900-01-01");

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.last_activity_by_brand`(
    email_profile_master_id     INTEGER                 OPTIONS(description="email profile Id. ref: {{ params.bq_project }}.store_matrix_pmc.profile_master_id_v2.id"),
    brand_trigram               STRING                  OPTIONS(description="brand tigram. ref: {{ params.bq_project }}.refined_data.email_base"),
    last_open_date              DATE                    OPTIONS(description="last open date. ref: {{ params.bq_project }}.refined_data.email_event_open"),
    last_click_date             DATE                    OPTIONS(description="last click date. ref: {{ params.bq_project }}.refined_data.email_event_click")
)
OPTIONS(description="Last activity (open, click) by brand"
                   ||"\n\n"
                   ||"DAG: {{ dag.dag_id }}."
                   ||"\n\n"
                   ||"Sync: daily");

{% if params.is_full %}
    TRUNCATE TABLE `{{ params.bq_project }}.generated_data.last_activity_by_brand`;
    SET open_start_date  = default_date;
    SET click_start_date = default_date;
{% else %}
    SET open_start_date  = (SELECT DATE_SUB(DATE("{{ next_ds }}"), INTERVAL {{ params.interval }}));
    SET click_start_date = (SELECT DATE_SUB(DATE("{{ next_ds }}"), INTERVAL {{ params.interval }}));
{% endif %}

MERGE `{{ params.bq_project }}.generated_data.last_activity_by_brand` AS dst
USING (
    -- get last open & click date by brand
    SELECT
        email_profile_master_id,
        brand_trigram,
        MAX(last_open_date)   AS last_open_date,
        MAX(last_click_date)  AS last_click_date
    FROM (
        SELECT email_profile_master_id, brand_trigram, last_open_date, last_click_date
        FROM `{{ params.bq_project }}.generated_data.last_activity_by_base`
        WHERE
            last_open_date >= open_start_date
            AND
            (last_click_date >= click_start_date OR last_click_date IS NULL)
        UNION ALL
        SELECT email_profile_master_id, brand_trigram, last_open_date, last_click_date
        FROM `{{ params.bq_project }}.generated_data.last_activity_by_theme`
        WHERE
            last_open_date >= open_start_date
            AND
            (last_click_date >= click_start_date OR last_click_date IS NULL)
    ) AS rd
    GROUP BY 1, 2
) AS ref
    ON dst.email_profile_master_id = ref.email_profile_master_id
    AND dst.brand_trigram = ref.brand_trigram
WHEN MATCHED THEN
    -- set last_open_date and last_click_date to the latest value
    UPDATE SET
        last_open_date  = IF(
                            GREATEST(IFNULL(dst.last_open_date, default_date), IFNULL(ref.last_open_date, default_date)) = default_date,
                            NULL,
                            GREATEST(IFNULL(dst.last_open_date, default_date), IFNULL(ref.last_open_date, default_date))
                            ),
        last_click_date = IF(
                            GREATEST(IFNULL(dst.last_click_date, default_date), IFNULL(ref.last_click_date, default_date)) = default_date,
                            NULL,
                            GREATEST(IFNULL(dst.last_click_date, default_date), IFNULL(ref.last_click_date, default_date))
                            )
WHEN NOT MATCHED BY TARGET THEN
    INSERT (email_profile_master_id, brand_trigram, last_open_date, last_click_date)
    VALUES (ref.email_profile_master_id, ref.brand_trigram, ref.last_open_date, ref.last_click_date);
