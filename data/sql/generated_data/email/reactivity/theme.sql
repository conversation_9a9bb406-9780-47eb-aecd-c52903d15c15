-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- partition optim : get last open / click date processed by this process.
DECLARE open_start_date  DATE;
DECLARE click_start_date DATE;
DECLARE default_date DEFAULT DATE("1900-01-01");

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.last_activity_by_theme`(
    email_profile_master_id     INTEGER  NOT NULL       OPTIONS(description="Email profile Id. ref: {{ params.bq_project }}.store_matrix_email.profile_master_id.id."),
    universe_name               STRING   NOT NULL       OPTIONS(description="Splio universe name. ref: {{ params.bq_project }}.store_karinto.universe#name."),
    brand_trigram               STRING   NOT NULL       OPTIONS(description="Brand trigram. ref: {{ params.bq_project }}.refined_data.email_base."),
    theme                       STRING   NOT NULL       OPTIONS(description="Theme as NL shopping, coaching, ... For NL shopping, theme = <nl_shopping_name>_nls. ref: {{ params.bq_project }}.store_matrix_email.rogue_one_theme.theme."),
    last_open_date              DATE                    OPTIONS(description="Last open date. ref: {{ params.bq_project }}.refined_data.email_event_open."),
    last_click_date             DATE                    OPTIONS(description="Last click date. ref: {{ params.bq_project }}.refined_data.email_event_click."),
    PRIMARY KEY(email_profile_master_id, universe_name, brand_trigram, theme) NOT ENFORCED
)OPTIONS(description="This table is based on refined data email event open/click and simulated email event open tables."
                   ||"\n"
                   ||"Last activity (open, click) for NL Shopping, NL-Bestof, ...."
                   ||"\n\n"
                   ||"DAG: {{ dag.dag_id }}."
                   ||"\n"
                   ||"Sync: daily");

{% if params.is_full %}
    TRUNCATE TABLE `{{ params.bq_project }}.generated_data.last_activity_by_theme` ;
    SET open_start_date  = default_date;
    SET click_start_date = default_date;
{% else %}
    SET open_start_date  = (SELECT DATE_SUB(DATE("{{ next_ds }}"), INTERVAL {{ params.interval }}));
    SET click_start_date = (SELECT DATE_SUB(DATE("{{ next_ds }}"), INTERVAL {{ params.interval }}));
{% endif %}

MERGE `{{ params.bq_project }}.generated_data.last_activity_by_theme` AS ref
USING (
    WITH reactivity_open AS (
        -- get last open date by theme
        SELECT
            email_profile_master_id,
            universe_name,
            IFNULL(brand_trigram, "(not set)") AS brand_trigram,
            theme,
            MAX(DATE(open_date)) AS last_open_date
        FROM (
            SELECT email_profile_master_id, universe_name, theme, open_date,
            FROM `{{ params.bq_project }}.refined_data.email_event_open`
            WHERE
              DATE(open_date) >= open_start_date
              AND
              universe_name != "(not set)"
              AND
              theme IS NOT NULL
            UNION ALL
            SELECT email_profile_master_id, universe_name, theme, open_date,
            FROM `{{ params.bq_project }}.generated_data.email_event_simulated_open`
            WHERE
              DATE(open_date) >= open_start_date
              AND
              universe_name != "(not set)"
              AND
              theme IS NOT NULL
        ) AS ro
        JOIN `{{ params.bq_project }}.refined_data.universe_theme` AS ub USING(universe_name, theme)
        WHERE
          theme IS NOT NULL
        GROUP BY 1, 2, 3, 4
    ), reactivity_click AS (
        -- get last click date by theme
        SELECT
            email_profile_master_id,
            universe_name,
            IFNULL(brand_trigram, "(not set)") AS brand_trigram,
            theme,
            MAX(DATE(click_date)) AS last_click_date
        FROM `{{ params.bq_project }}.refined_data.email_event_click` AS rc
        JOIN `{{ params.bq_project }}.refined_data.universe_theme`    AS ub USING(universe_name, theme)
        WHERE
            DATE(click_date) >= click_start_date
            AND
            universe_name != "(not set)"
            AND
            theme IS NOT NULL
        GROUP BY 1, 2, 3, 4
    )
    -- merge open & click reactivity
    SELECT DISTINCT
        email_profile_master_id,
        universe_name,
        brand_trigram,
        theme,
        last_open_date,
        last_click_date
    FROM reactivity_open AS ro
    FULL JOIN reactivity_click AS rc USING (email_profile_master_id, theme, brand_trigram, universe_name)
) AS dst
    ON ref.email_profile_master_id = dst.email_profile_master_id
    AND ref.theme = dst.theme
    AND ref.brand_trigram = dst.brand_trigram
    AND ref.universe_name = dst.universe_name

WHEN MATCHED THEN
    -- set last open date and last click date to the latest value
    UPDATE SET
        last_open_date  = IF(
                            GREATEST(IFNULL(dst.last_open_date, default_date), IFNULL(ref.last_open_date, default_date)) = default_date,
                            NULL,
                            GREATEST(IFNULL(dst.last_open_date, default_date), IFNULL(ref.last_open_date, default_date))
                            ),
        last_click_date = IF(
                            GREATEST(IFNULL(dst.last_click_date, default_date), IFNULL(ref.last_click_date, default_date)) = default_date,
                            NULL,
                            GREATEST(IFNULL(dst.last_click_date, default_date), IFNULL(ref.last_click_date, default_date))
                            )
WHEN NOT MATCHED BY TARGET THEN
    INSERT (email_profile_master_id, universe_name, brand_trigram, theme, last_open_date, last_click_date)
    VALUES (dst.email_profile_master_id, dst.universe_name, dst.brand_trigram, dst.theme, dst.last_open_date, dst.last_click_date);
