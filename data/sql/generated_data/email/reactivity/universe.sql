-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- partition optim : get last open / click date processed by this process.
DECLARE open_start_date  DATE;
DECLARE click_start_date DATE;
DECLARE default_date DEFAULT DATE("1900-01-01");


CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.last_activity_by_universe`(
    email_profile_master_id     INTEGER NOT NULL        OPTIONS(description="Email profile Id. ref: {{ params.bq_project }}.store_matrix_pmc.profile_master_id_v2.id ."),
    universe_name               STRING  NOT NULL        OPTIONS(description="Splio universe name. ref: {{ params.bq_project }}.store_karinto.universe#name ."),
    brand_trigram               STRING  NOT NULL        OPTIONS(description="Brand tigram. ref: {{ params.bq_project }}.refined_data.email_base ."),
    last_open_date              DATE                    OPTIONS(description="Last open date. ref: {{ params.bq_project }}.refined_data.email_event_open."),
    last_click_date             DATE                    OPTIONS(description="Last click date. ref: {{ params.bq_project }}.refined_data.email_event_click."),
    PRIMARY KEY(email_profile_master_id, universe_name, brand_trigram) NOT ENFORCED
)
OPTIONS(description="This table is based on last activities by (base, theme) tables. \n "
                  ||"Last activity (open, click) by Splio universe ."
                  ||"\n\n"
                  || "DAG: {{ dag.dag_id }}.\n"
                  || "Sync: daily"
);

{% if params.is_full %}
    TRUNCATE TABLE `{{ params.bq_project }}.generated_data.last_activity_by_universe` ;
    SET open_start_date  = default_date;
    SET click_start_date = default_date;
{% else %}
    SET open_start_date  = (SELECT DATE_SUB(DATE("{{ next_ds }}"), INTERVAL {{ params.interval }}));
    SET click_start_date = (SELECT DATE_SUB(DATE("{{ next_ds }}"), INTERVAL {{ params.interval }}));
{% endif %}

MERGE `{{ params.bq_project }}.generated_data.last_activity_by_universe` AS ref
USING (
    -- get last open & click date by universe
    SELECT
        email_profile_master_id,
        universe_name,
        IFNULL(brand_trigram, "(not set") AS brand_trigram,
        MAX(last_open_date)               AS last_open_date,
        MAX(last_click_date)              AS last_click_date
    FROM (
        SELECT email_profile_master_id, universe_name, brand_trigram, last_open_date, last_click_date
        FROM `{{ params.bq_project }}.generated_data.last_activity_by_base`
        WHERE
            last_open_date >= open_start_date
            AND
            (last_click_date >= click_start_date OR last_click_date IS NULL)
            AND
            universe_name != "(not set)"
        UNION ALL
        SELECT email_profile_master_id, universe_name, brand_trigram, last_open_date, last_click_date
        FROM `{{ params.bq_project }}.generated_data.last_activity_by_theme`
        WHERE
            last_open_date >= open_start_date
            AND
            (last_click_date >= click_start_date OR last_click_date IS NULL)
            AND
            universe_name != "(not set)"
    ) AS rd
    GROUP BY 1, 2, 3
) AS dst
ON  ref.email_profile_master_id = dst.email_profile_master_id
AND ref.universe_name = dst.universe_name
AND ref.brand_trigram = dst.brand_trigram
WHEN MATCHED THEN
    -- set last_open_date and last_click_date to the lastest value
    UPDATE SET
        last_open_date  = IF(
                            GREATEST(IFNULL(dst.last_open_date, default_date), IFNULL(ref.last_open_date, default_date)) = default_date,
                            NULL,
                            GREATEST(IFNULL(dst.last_open_date, default_date), IFNULL(ref.last_open_date, default_date))
                            ),
        last_click_date = IF(
                            GREATEST(IFNULL(dst.last_click_date, default_date), IFNULL(ref.last_click_date, default_date)) = default_date,
                            NULL,
                            GREATEST(IFNULL(dst.last_click_date, default_date), IFNULL(ref.last_click_date, default_date))
                            )
WHEN NOT MATCHED BY TARGET THEN
    INSERT (email_profile_master_id, universe_name, brand_trigram, last_open_date, last_click_date)
    VALUES (dst.email_profile_master_id, dst.universe_name, dst.brand_trigram, dst.last_open_date, dst.last_click_date);
