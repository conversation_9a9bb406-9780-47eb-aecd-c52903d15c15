-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE start_date DEFAULT DATE("{{ params.start_date }}");
DECLARE end_date DEFAULT DATE("{{ params.end_date }}");


CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.pandora_profile_nl_shopping_activation`
(
    email_profile_master_id    INT64     NOT NULL   OPTIONS(description="Email profile master id"),
    acquisition_date           DATE      NOT NULL   OPTIONS(description="Acquisition date. ref: refined_data.pandora_events.event_date"),
    partner                    STRING    NOT NULL   OPTIONS(description="Partner for acquisition"),
    context                    STRING    NOT NULL   OPTIONS(description="Acquisition context"),
    collect_lever              STRING    NOT NULL   OPTIONS(description="Collect lever as enum=['popin_consentement', 'cosponsoring', 'coregistration', 'jeu_cobrandé', '\\N', 'facebook', 'gamification']"),
    nl_shopping_reactivity     STRUCT<
        open                       STRUCT<
            date                       DATE                 OPTIONS(description="Open date. ref: refined_data.email_event_open"),
            universe                   STRING               OPTIONS(description="Open universe. ref: refined_data.email_event_open"),
            theme                      STRING               OPTIONS(description="Open on theme. ref: refined_data.email_event_open")
        > OPTIONS(description="Open info"),
        click                      STRUCT<
            date                       DATE                 OPTIONS(description="Click date. ref: refined_data.email_event_click"),
            universe                   STRING               OPTIONS(description="Click universe. ref: refined_data.email_event_click"),
            theme                      STRING               OPTIONS(description="Click on theme. ref: refined_data.email_event_click")
        > OPTIONS(description="Open info")
    > OPTIONS(description="Reactivity on NL Shopping universe"),
    PRIMARY KEY (acquisition_date, email_profile_master_id, partner, context, collect_lever) NOT ENFORCED
)
PARTITION BY acquisition_date
CLUSTER BY partner, context, collect_lever
OPTIONS(
    description="This table contains reactivity info on NL Shopping universe for profiles with pandora events on part context."
               ||"\n"
               ||"For each profile acquired on part context, we get global reactivity on NL Shopping universe."
               ||"\n\n"
               ||"This table is used in the dashboard: Pandora - Suivi de l'activation"
               ||"\n"
               ||"[ATLAS PROD] https://atlas.prismadata.fr/data-product/99-pandora-suivi-de-l-activation-sans-gala"
               ||"\n"
               ||"[ATLAS PREPROD] https://atlas.preprod.prismadata.fr/data-product/104-pandora-suivi-de-l-activation"
               ||"\n\n"
               ||"DAG: {{ dag.dag_id }}"
               ||"\n\n"
               ||"Sync: Daily.");

{% if params.is_full != true %}
SET start_date = DATE_SUB(DATE("{{ next_ds }}"), INTERVAL {{ params.time_interval }});
SET end_date = CURRENT_DATE();
{% endif %}

DELETE FROM `{{ params.bq_project }}.generated_data.pandora_profile_nl_shopping_activation`
WHERE DATE(acquisition_date) BETWEEN start_date AND end_date;

INSERT INTO `{{ params.bq_project }}.generated_data.pandora_profile_nl_shopping_activation`
WITH pandora_part_events AS (
    SELECT
        DATE(event_date) AS acquisition_date,
        email_profile_master_id,
        event_id AS pandora_event_id,
        setup.partner.name AS partner,
        setup.context.name AS context,
        collect_lever,
        IF(response.email.status = 'REO', 'REA', response.email.status) AS reponse,
        event_has_rea
    FROM `{{ params.bq_project }}.refined_data.pandora_events`
    WHERE
        DATE(event_date) BETWEEN start_date AND end_date
        AND
        setup.context.name LIKE '%_part' -- is NL Shopping context
        AND
        response.email.status IN ('NEW', 'REA', 'REO')
        AND
        NOT action.email.is_cancelled
)

-- We extract the theme for opens and clicks in case we want to associate the theme with the email_consent
-- in order to keep only the actual activation on the correct theme corresponding to the acquisition context
-- Tips: there is a table with email_consent and theme: store_karinto.email_consent_theme
SELECT
    p.email_profile_master_id,
    p.acquisition_date,
    p.partner,
    p.context,
    p.collect_lever,
    STRUCT(
        STRUCT(
            o.open_date AS date,
            o.universe_name AS universe,
            o.theme AS theme
            ) AS open,
        STRUCT(
            c.click_date AS date,
            c.universe_name AS universe,
            o.theme AS theme
            ) AS click
    ) AS nl_shopping_reactivity
FROM pandora_part_events AS p
LEFT JOIN `{{ params.bq_project }}.refined_data.email_event_open` AS o
    ON o.email_profile_master_id = p.email_profile_master_id
    AND DATE(o.open_date) BETWEEN DATE(p.acquisition_date) AND DATE_ADD(DATE(p.acquisition_date), INTERVAL 90 DAY)
LEFT JOIN `{{ params.bq_project }}.refined_data.email_event_click` AS c
    ON c.email_profile_master_id = p.email_profile_master_id
    AND DATE(c.click_date) BETWEEN DATE(p.acquisition_date) AND DATE_ADD(DATE(p.acquisition_date), INTERVAL 180 DAY)
WHERE
    (
      DATE(o.open_date) BETWEEN start_date AND DATE_ADD(end_date, INTERVAL 90 DAY)
      OR
      DATE(c.click_date) BETWEEN start_date AND DATE_ADD(end_date, INTERVAL 180 DAY)
    )
    AND
    (o.universe_name = 'nl_shopping' OR c.universe_name = 'nl_shopping');
