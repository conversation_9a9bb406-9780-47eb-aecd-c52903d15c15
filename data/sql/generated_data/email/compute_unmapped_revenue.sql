-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.business_data.arpu_unmapped_revenue` (
    alert        STRING   OPTIONS(description="Type of alert ('Revenu de l'ad name sur 1 mois','Revenu historique de l'ad name','Revenu total du mois')"),
    partner      STRING   OPTIONS(description="Partner name. Possible values: ['Powerspace', 'Ividence']"),
    month        DATE     OPTIONS(description="The month in which the revenue was generated (YYYY/MM/01)"),
    ad_name      STRING   OPTIONS(description="The ad_name that's not mapped to a consent in the mapping Google Sheet"),
    threshold    INTEGER  OPTIONS(description="The threshold that was exceeded (arbitrarily set by the POs in the mapping Google Sheet)"),
    alert_value  FLOAT64  OPTIONS(description="The unmapped revenue (in euros)")
)
PARTITION BY month
OPTIONS(description="This view returns all of the cases in which the unmapped revenue for an adname and/or a Month has exceeded the defined threshold to send an alert.\n\n"
                     || "\n"
                     || "Mapping Gsheet : https://docs.google.com/spreadsheets/d/11NR31L-jHcPZ3v4SLDb2qJWG7pGPWnGlW72MPJnxi3I/edit?usp=sharing"
                     || "\n"
                     || "DAG: {{ dag.dag_id }}."
                     || "\n\n"
                     || "Sync: daily");

TRUNCATE TABLE `{{ params.bq_project }}.business_data.arpu_unmapped_revenue`;

INSERT INTO `{{ params.bq_project }}.business_data.arpu_unmapped_revenue`

WITH monthly_revenue AS (
SELECT
  'Powerspace' AS partner,
  DATE_TRUNC(Tracking_Datestuff_Date,month) AS month,
  CONCAT(pr.Website_Name, "*", pr.Position_Name) AS ad_name,
  SUM(Tracking_Commissions) AS revenue
FROM `visibilite-dfp.referentiel_share_it_data.table_partner_POWSPA_DEFAULT` AS pr
LEFT JOIN `{{ params.bq_project }}.workspace.powerspace_email_base_mapping` AS pebm ON pebm.ad_name = CONCAT(pr.Website_Name, "*", pr.Position_Name)
WHERE
    pebm.email_consent_public_ref IS NULL
AND
    (Advertiser_Reseller_Name LIKE '%Powerspace%'
    OR
    Advertiser_Reseller_Name LIKE '%Prisma%')
GROUP BY ALL

UNION ALL

SELECT
  'Ividence' AS partner,
  DATE_TRUNC(date, month) AS month,
  ir.context_name,
  SUM(revenue)
FROM
  `visibilite-dfp.referentiel_share_it_data.table_partner_IVIDEN_DEFAULT` AS ir
LEFT JOIN
  `{{ params.bq_project }}.workspace.ividence_email_base_mapping` AS iebm
ON
  iebm.ad_name = ir.context_name
WHERE iebm.email_consent_public_ref IS NULL
GROUP BY ALL
ORDER BY 4 DESC)

SELECT DISTINCT
  "Revenu de l'ad name sur 1 mois" AS alert,
  partner AS partner,
  month AS month,
  ad_name AS ad_name,
  500 AS threshold,
  SUM(revenue) OVER (PARTITION BY month, ad_name) AS alert_value
FROM  monthly_revenue
LEFT JOIN `{{ params.bq_project }}.business_data.arpu_alert_thresholds` aat ON 1=1
QUALIFY SUM(revenue) OVER (PARTITION BY month, ad_name) > aat.seuil_adname_mois

UNION ALL

SELECT DISTINCT
      "Revenu historique de l'ad name",
      partner,
      CAST(NULL AS DATE),
      ad_name,
      1000,
      SUM(revenue) OVER (PARTITION BY ad_name)
FROM  monthly_revenue
LEFT JOIN `{{ params.bq_project }}.business_data.arpu_alert_thresholds` aat ON 1=1
QUALIFY SUM(revenue) OVER (PARTITION BY ad_name) > aat.seuil_adname

UNION ALL

SELECT DISTINCT
      "Revenu total du mois",
      partner,
      month,
      CAST(NULL AS STRING),
      1000,
      SUM(revenue) OVER (PARTITION BY MONTH)
FROM  monthly_revenue
LEFT JOIN `{{ params.bq_project }}.business_data.arpu_alert_thresholds` aat ON 1=1
QUALIFY SUM(revenue) OVER (PARTITION BY MONTH) > seuil_mois
