-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- Update generated_data.user_agent_ua-parser
CREATE OR REPLACE FUNCTION `{{params.bq_project}}.{{params.bq_dataset_generated}}.UAParser` (ua STRING)
    RETURNS STRUCT <
        type           STRING,
        model          STRING,
        vendor         STRING,
        os             STRING,
        os_version     STRING,
        browsername    STRING,
        browserversion STRING
        >
    LANGUAGE js
    AS """
        return {
            type:UAParser(ua).device.type,
            model:UAParser(ua).device.model,
            vendor:UAParser(ua).device.vendor,
            os:UAParser(ua).os.name,
            os_version:U<PERSON>arser(ua).os.version,
            browsername:UAParser(ua).browser.name,
            browserversion:UAParser(ua).browser.version

        };
    """
    OPTIONS(library=["{{params.gspath_to_parser_lib}}"]);

CREATE TABLE IF NOT EXISTS `{{params.bq_project}}.{{params.bq_dataset_generated}}.{{params.generated_table}}` (
    user_agent        STRING      NOT NULL  OPTIONS(description="http_user_agent from store_tracking.prisma_full_data"),
    user_agent_hash   STRING      NOT NULL  OPTIONS(description="hashed http_user_agent"),
    update_date       TIMESTAMP              OPTIONS(description="insertion date"),
    client STRUCT <
        device STRUCT <
            type         STRING   OPTIONS(description="device type as enum = [console, mobile, tablet, smarttv, wearable, embedded]"),
            vendor       STRING   OPTIONS(description="device vendor"),
            model        STRING   OPTIONS(description="device model"),
            os           STRING   OPTIONS(description="device os"),
            os_version   STRING   OPTIONS(description="device os version")
        >   OPTIONS(description="device info"),
        browser STRUCT <
            name         STRING   OPTIONS(description="device name"),
            version      STRING   OPTIONS(description="device operating system version")
        >   OPTIONS(description="browser info")
    >   OPTIONS(description="device and browser info")
) OPTIONS(
  expiration_timestamp=NULL,
  description="Table with all user agent information parsed with UA-Parser library.\n"
            || "We generate data from the store_tracking dataset"
            || "\n\n"
            || "Daily updates thru the Airflow DAG {{ dag.dag_id }}. Content dated " || DATE("{{next_ds}}") || " CET."
);

INSERT INTO `{{params.bq_project}}.{{params.bq_dataset_generated}}.{{params.generated_table}}` (
  WITH new_user_agent AS (
    SELECT DISTINCT
        http_user_agent AS user_agent
    FROM `pm-prod-matrix.store_tracking.prisma_full_data` AS ua_new
    WHERE ua_new.datetime BETWEEN DATE_SUB(TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY, 'UTC'), INTERVAL 1 DAY) AND
      TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY, 'UTC')
  ),

  old_user_agent AS (
    SELECT
      user_agent,
      1 AS old
    FROM `{{params.bq_project}}.{{params.bq_dataset_generated}}.user_agent_ua-parser`
  ),

  reconciled_user_agent AS (
    SELECT
      ua_new.user_agent AS user_agent,
      ua_old.old AS old
    FROM new_user_agent AS ua_new
    LEFT JOIN  old_user_agent AS ua_old
        ON ua_new.user_agent = ua_old.user_agent
  ),

  deduplicated_user_agent AS (
    SELECT DISTINCT
      user_agent,
      TO_HEX(SHA256(user_agent)) AS user_agent_hash
    FROM reconciled_user_agent
    WHERE old IS NULL
    GROUP BY user_agent, TO_HEX(SHA256(user_agent))
  )

  SELECT
    user_agent,
    user_agent_hash,
    CURRENT_TIMESTAMP() AS update_date,
    STRUCT(
        STRUCT(
            `{{params.bq_project}}.{{params.bq_dataset_generated}}.UAParser`(user_agent).type           AS type,
            `{{params.bq_project}}.{{params.bq_dataset_generated}}.UAParser`(user_agent).vendor         AS vendor,
            `{{params.bq_project}}.{{params.bq_dataset_generated}}.UAParser`(user_agent).model          AS model,
            `{{params.bq_project}}.{{params.bq_dataset_generated}}.UAParser`(user_agent).os             AS os,
            `{{params.bq_project}}.{{params.bq_dataset_generated}}.UAParser`(user_agent).os_version     AS os_version
        ) AS device,
        STRUCT(
            `{{params.bq_project}}.{{params.bq_dataset_generated}}.UAParser`(user_agent).browsername    AS name,
            `{{params.bq_project}}.{{params.bq_dataset_generated}}.UAParser`(user_agent).browserversion AS version
        ) AS browser
    ) AS client
  FROM deduplicated_user_agent
)