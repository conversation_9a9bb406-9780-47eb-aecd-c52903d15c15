-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE start_date  DEFAULT DATE("2021-04-01");
DECLARE end_date   DEFAULT DATE("{{ next_ds }}");

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.email_reactivity_history_by_base` (
    observation_date          DATE          NOT NULL     OPTIONS(description="observation date"),
    email_profile_master_id   INTEGER       NOT NULL     OPTIONS(description="profile master id"),
    email_consent_public_ref  STRING        NOT NULL     OPTIONS(description="email consent public ref. ref: store_karinto.email_consent.public_ref"),
    sub_email_event_id        INTEGER                    OPTIONS(description="email event id. ref: store_matrix_email.email_event.id"),
    consent_type              STRING                     OPTIONS(description="consent type. ref: email_base.consent_type"),
    -- reactivity information
    volume STRUCT<
        open                   INTEGER                       OPTIONS(description="open volume"),
        unique_open            INTEGER                       OPTIONS(description="unique open volume"),
        click                  INTEGER                       OPTIONS(description="click volume"),
        unique_click           INTEGER                       OPTIONS(description="unique click volume")
    > OPTIONS(description="reactivity volume")
)
PARTITION BY observation_date
CLUSTER BY email_consent_public_ref, email_profile_master_id
OPTIONS(description="Email 'open' and 'click' volume history by email base."
                  ||"\n\n"
                  ||"DAG: {{ dag.dag_id }}"
                  ||"\n\n"
                  ||"Sync: daily");

{% if params.is_full %}
    TRUNCATE TABLE `{{ params.bq_project }}.generated_data.email_reactivity_history_by_base`;
{% else %}
    -- keep 1 day history
    SET start_date = DATE_SUB(DATE("{{ next_ds }}"), INTERVAL 1 DAY);
{% endif %}

MERGE `{{ params.bq_project }}.generated_data.email_reactivity_history_by_base` AS dst
USING (
    WITH generate_dates AS (
        -- generated dates from start_date to end_date
        SELECT
            observation_date
        FROM UNNEST(GENERATE_DATE_ARRAY(start_date, end_date, INTERVAL 1 DAY)) AS observation_date
        ORDER BY observation_date DESC
    ), get_profiles_alive_during_period AS (
        -- get sub/unsub email events by profile and consent
        SELECT DISTINCT
            gd.observation_date,
            email_profile_master_id,
            email_consent_public_ref,
            LAST_VALUE(sub_email_event_id) OVER one_day_window AS sub_email_event_id
        FROM `{{ params.bq_project }}.generated_data.email_profile_lifecycle_by_base`
        JOIN generate_dates AS gd
            ON  DATE(sub_date) <= gd.observation_date
                AND
                (gd.observation_date <= DATE(unsub_date) OR unsub_date IS NULL)
                -- in case user subscribe and unsubscribe several times in the same day
        WINDOW one_day_window AS (
            PARTITION BY DATE(sub_date), email_profile_master_id, email_consent_public_ref
            ORDER BY DATE(sub_date) ASC
        )
    ), tracking_open AS (
        -- compute open volume
        SELECT DISTINCT
            DATE(open_date)                    AS observation_date,
            email_profile_master_id,
            email_consent_public_ref,
            COUNT(*)                           AS nb_open,
            COUNT(DISTINCT rogue_one_email_id) AS nb_unique_open
        FROM (
            SELECT open_date, email_profile_master_id, email_consent_public_ref, rogue_one_email_id
            FROM `{{ params.bq_project }}.refined_data.email_event_open`
            WHERE DATE(open_date) BETWEEN start_date AND end_date
            UNION ALL
            -- we generate simulated open events when we have a click event without open event
            SELECT open_date, email_profile_master_id, email_consent_public_ref, rogue_one_email_id
            FROM `{{ params.bq_project }}.generated_data.email_event_simulated_open`
            WHERE DATE(open_date) BETWEEN start_date AND end_date
        )
        GROUP BY ALL
    ), tracking_click AS (
        -- compute click volume
        SELECT DISTINCT
            click_date AS observation_date,
            email_profile_master_id,
            email_consent_public_ref,
            -- --> Create ticket to nest url information in Array of struct
            COUNT(*) AS nb_click,
            COUNT(DISTINCT rogue_one_email_id) AS nb_unique_click
        FROM `{{ params.bq_project }}.refined_data.email_event_click`, UNNEST(click_info) AS clk
        WHERE DATE(click_date) BETWEEN start_date AND end_date
        GROUP BY ALL
    ), tracking AS (
        SELECT DISTINCT
            observation_date,
            email_profile_master_id,
            email_consent_public_ref,
            nb_open,
            nb_unique_open,
            nb_click,
            nb_unique_click
        FROM tracking_open
        LEFT JOIN tracking_click USING(observation_date, email_profile_master_id, email_consent_public_ref)
    )
    SELECT
        observation_date,
        email_profile_master_id,
        email_consent_public_ref,
        sub_email_event_id,
        eb.consent_type,
        STRUCT(
            -- open
            nb_open,
            nb_unique_open,
            -- click
            nb_click,
            nb_unique_click
        ) AS volume
    FROM get_profiles_alive_during_period AS p
    JOIN tracking USING(observation_date, email_profile_master_id, email_consent_public_ref)
    JOIN `{{ params.bq_project }}.refined_data.email_base` AS eb
        ON eb.consent_public_ref = p.email_consent_public_ref
) AS ref
    ON dst.consent_type              = ref.consent_type
    AND dst.observation_date         = ref.observation_date
    AND dst.email_profile_master_id  = ref.email_profile_master_id
    AND dst.email_consent_public_ref = ref.email_consent_public_ref
    AND dst.sub_email_event_id       = ref.sub_email_event_id

WHEN MATCHED THEN
    UPDATE SET dst.volume = ref.volume

WHEN NOT MATCHED BY TARGET THEN
    INSERT (
        observation_date,
        email_profile_master_id,
        email_consent_public_ref,
        sub_email_event_id,
        consent_type,
        volume
    )
    VALUES (
        ref.observation_date,
        ref.email_profile_master_id,
        ref.email_consent_public_ref,
        ref.sub_email_event_id,
        ref.consent_type,
        ref.volume
    );
