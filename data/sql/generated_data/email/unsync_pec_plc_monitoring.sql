-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.unsync_pec_plc_monitoring`
(
    observation_date            DATE    NOT NULL  OPTIONS(description="observation date"),
    email_consent_public_ref    STRING  NOT NULL  OPTIONS(description="email consent public ref"),
    pec_profile_status          STRING  NOT NULL  OPTIONS(description="profile status in pec per consent ['sub', 'unsub']"),
    pec_create_date             DATE    NOT NULL  OPTIONS(description="first sub date by consent"),
    pec_update_date             DATE    NOT NULL  OPTIONS(description="last update date by consent"),
    profile_volume              INTEGER NOT NULL  OPTIONS(description="profile count")
)
PARTITION BY observation_date
CLUSTER BY email_consent_public_ref, pec_profile_status
OPTIONS(
    description="Contains unsync profile's count between (profile email consent) PEC and (profile lifecycle) PLC daily by consent. \n"
              ||"This table will be used to synchronise two tables as we can see here JIRA ticket : https://pmdtech.atlassian.net/browse/ITDATA-2632?atlOrigin=eyJpIjoiMzU0NWI2OWY2YWZlNDFhZmEyOGY3OTk3MzFhYTM0YWYiLCJwIjoiaiJ9 .\n\n"
              ||"DAG: {{ dag.dag_id }}. \n"
              ||"Sync: daily")
;



MERGE `{{ params.bq_project }}.generated_data.unsync_pec_plc_monitoring` AS dst
USING (
  WITH unsync_profile AS(
    -- From profile_lifecycle, get missing profiles which are present in profiles_email_consents
    -- This means we have to create simulated email_event, so the profile arrive at profile_lifecycle table
    SELECT
      pec.profile_master_id,
      eb.consent_public_ref,
      pec.consent_status    AS profile_status,
      pec.create_date       AS pec_create_date,
      pec.update_date       AS pec_update_date
    FROM `{{ params.bq_project }}.store_matrix_email.profiles_email_consents`           AS pec
    JOIN `{{ params.bq_project }}.refined_data.email_base`                              AS eb   ON  pec.email_consent_id = eb.consent_id
    LEFT JOIN `{{ params.bq_project }}.generated_data.email_profile_lifecycle_by_base`  AS epl  ON  pec.profile_master_id = epl.email_profile_master_id
                                                                                                AND epl.email_consent_public_ref = eb.consent_public_ref
    WHERE
      -- skip current date event's
      (
        DATE(pec.create_date) < DATE_SUB(DATE("{{ next_ds }}"), INTERVAL 2 DAY)
        AND
        DATE(pec.update_date) < DATE_SUB(DATE("{{ next_ds }}"), INTERVAL 2 DAY)
      )
        AND
        -- profile lifecycle missing !
        epl.email_profile_master_id IS NULL
  )
  -- count daily by consent unsync profile between PEC & PLC. We took PEC as reference table
  SELECT
    DATE("{{ next_ds }}")     AS observation_date,
    consent_public_ref        AS email_consent_public_ref,
    profile_status            AS pec_profile_status,
    DATE(pec_create_date)     AS pec_create_date,
    DATE(pec_update_date)     AS pec_update_date,
    COUNT(profile_master_id)  AS profile_volume
  FROM unsync_profile
  GROUP BY 1,2,3,4,5
) AS ref
ON
    dst.observation_date = ref.observation_date
    AND
    dst.email_consent_public_ref = ref.email_consent_public_ref
    AND
    dst.pec_profile_status = ref.pec_profile_status
WHEN NOT MATCHED BY TARGET THEN
INSERT
    (observation_date, email_consent_public_ref, pec_profile_status, pec_create_date, pec_update_date, profile_volume)
VALUES
    (ref.observation_date, ref.email_consent_public_ref, ref.pec_profile_status, ref.pec_create_date, ref.pec_update_date, ref.profile_volume)
WHEN MATCHED THEN
UPDATE SET
    dst.pec_create_date = ref.pec_create_date,
    dst.pec_update_date = ref.pec_update_date,
    dst.profile_volume  = ref.profile_volume;
