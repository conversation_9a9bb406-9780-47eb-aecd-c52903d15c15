-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE start_date DATE DEFAULT DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL 1 MONTH);
DECLARE end_date DATE DEFAULT DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL 1 DAY);

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.email_profile_consent_reactivity_by_month` (
    observation_month           DATE     NOT NULL    OPTIONS(description="Observation month"),
    email_profile_master_id     INT64    NOT NULL   OPTIONS(description="Profile ID"),
    reactivity                  ARRAY<STRUCT<
        sub_email_event_id              INT64      OPTIONS(description="Email event id"),
        email_consent_public_ref        STRING      OPTIONS(description="Consent public ref"),
        click_volume                    INT64       OPTIONS(description="Sum of click events in the observation month"),
        open_volume                     INT64       OPTIONS(description="Sum of open events in the observation month"),
        unique_click_volume             INT64       OPTIONS(description="Sum of unique click events in the observation month"),
        unique_open_volume              INT64       OPTIONS(description="Sum of the unique open events in the observation month"),
        total_event_volume              INT64       OPTIONS(description="Sum of all events (open + click) in the observation month"),
        total_unique_event_volume       INT64       OPTIONS(description="Sum of all unique events (unique open + unique click) in the observation month"),
        effective_open_activity         INT64       OPTIONS(description="Count of distinct days with an open event in the observation month"),
        effective_click_activity        INT64       OPTIONS(description="Count of distinct days with a click event in the observation month"),
        effective_events_activity       INT64       OPTIONS(description="Count of distinct days with an event (open or click) in the observation month"),
        first_observation_date          DATE        OPTIONS(description="Date of the first event (click or open) registered in the observation month"),
        last_observation_date           DATE        OPTIONS(description="Date of the last event (click or open) registered in the observation month"),
        last_click_date                 DATE        OPTIONS(description="Date of the last click event registered in the observation month"),
        last_open_date                  DATE        OPTIONS(description="Date of the last open event registered in the observation month")
        >
    >                                       OPTIONS(description="Array with reactivity data"),
    PRIMARY KEY(observation_month, email_profile_master_id) NOT ENFORCED
)
PARTITION BY observation_month
CLUSTER BY email_profile_master_id
OPTIONS(
    description="This table contains email reactivity data aggregated by month and consent\n"
              ||"Gala consents are not included"
              ||"\n\n"
              ||"DAG: {{ dag.dag_id }}"
              ||"\n\n"
              ||"Sync: Monthly at the 1st day of the month",
    labels=[
        ('project', 'pandora_ltv'),
        ('owner', 'rodrigo_santana'),
        ('backup_owner', 'anes_ben_ramdhan'),
        ('context', 'production')
    ]
);

{% if params.is_full %}
TRUNCATE TABLE `{{ params.bq_project }}.generated_data.email_profile_consent_reactivity_by_month`;
{% else %}
DELETE FROM `{{ params.bq_project }}.generated_data.email_profile_consent_reactivity_by_month`
WHERE observation_month BETWEEN start_date AND end_date;
{% endif %}

INSERT INTO `{{ params.bq_project }}.generated_data.email_profile_consent_reactivity_by_month`
WITH compute_reactivity AS (
    -- compute reactivity KPIs at consent level
    SELECT
        DATE_TRUNC(observation_date, MONTH) AS observation_month,
        email_profile_master_id,
        email_consent_public_ref,
        sub_email_event_id,
        SUM(IFNULL(volume.click, 0)) AS click_volume,
        SUM(IFNULL(volume.open, 0)) AS open_volume,
        SUM(IFNULL(volume.unique_click, 0)) AS unique_click_volume,
        SUM(IFNULL(volume.unique_open, 0)) AS unique_open_volume,
        SUM(IFNULL(volume.click, 0) + IFNULL(volume.open, 0)) AS total_event_volume,
        SUM(IFNULL(volume.unique_click, 0) + IFNULL(volume.unique_open, 0)) AS total_unique_event_volume,
        COUNT(DISTINCT IF(IFNULL(volume.open, 0) > 0, observation_date, NULL)) AS effective_open_activity,
        COUNT(DISTINCT IF(IFNULL(volume.click, 0) > 0, observation_date, NULL)) AS effective_click_activity,
        COUNT(DISTINCT IF(IFNULL(volume.click, 0) > 0 OR IFNULL(volume.open, 0) > 0, observation_date, NULL)) AS effective_events_activity,
        MIN(IF(volume.open > 0 OR volume.click > 0, observation_date, NULL)) AS first_observation_date,
        MAX(IF(volume.open > 0 OR volume.click > 0, observation_date, NULL)) AS last_observation_date,
        MAX(IF(volume.click > 0, observation_date, NULL)) AS last_click_date,
        MAX(IF(volume.open > 0, observation_date, NULL)) AS last_open_date
    FROM `{{ params.bq_project }}.generated_data.email_reactivity_history_by_base`
    WHERE
        email_consent_public_ref NOT LIKE "%gala%"
        AND sub_email_event_id IS NOT NULL
        AND observation_date <= end_date
    {% if not params.is_full %}
        AND observation_date >= start_date
    {% endif %}
    GROUP BY ALL
)

SELECT
    observation_month,
    email_profile_master_id,
    ARRAY_AGG(
        STRUCT(
            sub_email_event_id,
            email_consent_public_ref,
            click_volume,
            open_volume,
            unique_click_volume,
            unique_open_volume,
            total_event_volume,
            total_unique_event_volume,
            effective_open_activity,
            effective_click_activity,
            effective_events_activity,
            first_observation_date,
            last_observation_date,
            last_click_date,
            last_open_date
        ) ORDER BY email_consent_public_ref
    ) AS reactivity
FROM compute_reactivity
GROUP BY ALL;
