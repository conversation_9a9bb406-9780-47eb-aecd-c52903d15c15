-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.profile_bookmark_content_status`(
    pmc_uuid        STRING      NOT NULL  OPTIONS(description="user uuid. ref:{{ params.bq_project }}.store_bookmark.boomark.user_id"),
    brand_trigram   STRING      NOT NULL  OPTIONS(description="brand trigram. ref:{{ params.bq_project }}.store_bookmark.bookmark_content.brand"),
    content_id      STRING      NOT NULL  OPTIONS(description="content id. ref:{{ params.bq_project }}.store_one_person.person.id"),
    content_title   STRING      NOT NULL  OPTIONS(description="bookmark content name. ref:{{ params.bq_project }}.refined_data.bookmark.content_title"),
    bookmark_type   STRING      NOT NULL  OPTIONS(description="type of bookmark. ref:{{ params.bq_project }}.refined_data.bookmark.bookmark.type"),
    create_date     TIMESTAMP   NOT NULL  OPTIONS(description="datetime of bookmark content creation by user. ref:{{ params.bq_project }}.store_bookmark.bookmark.created_at"),
    update_date     TIMESTAMP   NOT NULL  OPTIONS(description="datetime of bookmark content last update by user. ref:{{ params.bq_project }}.store_bookmark.bookmark.updated_at"),
    is_alive        BOOLEAN     NOT NULL  OPTIONS(description="true if bookmark content has not been deleted by user (deleted = 0). ref:{{ params.bq_project }}.store_bookmark.bookmark.deleted"),
    is_new_sub      BOOLEAN     NOT NULL  OPTIONS(description="true if bookmark content has been created by user between the first and the last day of the current month. ref:{{ params.bq_project }}.store_bookmark.bookmark.created_at"),
    is_new_unsub    BOOLEAN     NOT NULL  OPTIONS(description="true if bookmark content has been deleted by user between the first and the last day of the current month. ref:{{ params.bq_project }}.store_bookmark.bookmark.updated_at")
)
PARTITION BY DATE(update_date)
OPTIONS(description="This table contains the status (alive, new sub or new unsub) of each profile having a bookmark created"
                  ||"\n"
                  ||"A profile is considered alive on a bookmark as long as the bookmark is not deleted"
                  ||"\n"
                  ||"A profile is considered a new subscriber on a bookmark if the bookmark is created during the current month"
                  ||"\n"
                  ||"A profile is considered a new unsubscriber on a bookmark if the bookmark is deleted during the current month"
                  ||"\n\n"
                  ||"DAG: {{ dag.dag_id }}"
                  ||"\n\n"
                  ||"Sync: Daily");

{% if params.is_full %}
TRUNCATE TABLE `{{ params.bq_project }}.generated_data.profile_bookmark_content_status`;
{% endif %}

MERGE `{{ params.bq_project }}.generated_data.profile_bookmark_content_status` AS dst
USING(

-- WITH sub-queries:
-- * follow_people: extract users with a follow:people bookmark
-- * classify_profiles: count subscriber (old and new) and new unsubscriber number

    WITH extract_bookmark AS (
        -- extract all bookmarks
        SELECT
            content_id,
            content_title,
            brand_trigram,
            bmk.create_date,
            bmk.update_date,
            bmk.is_deleted,
            bmk.type AS bookmark_type,
            shoot_id,
            bmk.user.pmc_uuid
        FROM `{{ params.bq_project }}.refined_data.bookmark`
        LEFT JOIN UNNEST(bookmark) AS bmk
        WHERE
            content_title IS NOT NULL
        {% if not params.is_full %}
            AND DATE(bmk.update_date) >= DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL 1 DAY)
        {% endif %}
    ), classify_profiles AS(
        -- count subscriber (old and new) and new unsubscriber number
        SELECT
            pmc_uuid,
            content_id,
            content_title,
            bookmark_type,
            brand_trigram,
            create_date,
            update_date,
            --@to be revised --> cf. Gala's Follow Star process
            is_deleted = 0 AS is_alive,
            is_deleted = 0 AND DATE(create_date) BETWEEN DATE_TRUNC(DATE("{{ data_interval_end }}"), MONTH) AND LAST_DAY(DATE("{{ data_interval_end }}")) AS is_new_sub,
            is_deleted = 1 AND DATE(update_date) BETWEEN DATE_TRUNC(DATE("{{ data_interval_end }}"), MONTH) AND LAST_DAY(DATE("{{ data_interval_end }}")) AS is_new_unsub
        FROM extract_bookmark
    )
    SELECT DISTINCT
        cp.pmc_uuid,
        cp.brand_trigram,
        cp.content_id,
        cp.content_title,
        cp.bookmark_type,
        cp.create_date,
        cp.update_date,
        cp.is_alive,
        cp.is_new_sub,
        cp.is_new_unsub
    FROM classify_profiles AS cp
) AS ref
    ON dst.pmc_uuid = ref.pmc_uuid
    AND dst.brand_trigram = ref.brand_trigram
    AND dst.content_id = ref.content_id

WHEN MATCHED THEN
    UPDATE SET
        dst.update_date = ref.update_date,
        dst.is_alive = ref.is_alive,
        dst.is_new_sub = ref.is_new_sub,
        dst.is_new_unsub = ref.is_new_unsub

WHEN NOT MATCHED THEN
    INSERT(
        pmc_uuid,
        brand_trigram,
        content_id,
        content_title,
        bookmark_type,
        create_date,
        update_date,
        is_alive,
        is_new_sub,
        is_new_unsub
    )
    VALUES(
        ref.pmc_uuid,
        ref.brand_trigram,
        ref.content_id,
        ref.content_title,
        ref.bookmark_type,
        ref.create_date,
        ref.update_date,
        ref.is_alive,
        ref.is_new_sub,
        ref.is_new_unsub
    );
