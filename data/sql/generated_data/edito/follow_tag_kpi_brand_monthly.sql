-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DROP TABLE IF EXISTS `{{ params.bq_project.mirror }}.generated_data.follow_tag_kpi_brand_monthly`;
CREATE TABLE IF NOT EXISTS `{{ params.bq_project.mirror }}.generated_data.follow_tag_kpi_brand_monthly`(
    start_month                 DATE       NOT NULL     OPTIONS(description="start day of month . example : '2021-01-01'"),
    brand_trigram               STRING                  OPTIONS(description="brand trigram"),
    kpi                         STRUCT<
        subscriber_number           INTEGER                 OPTIONS(description="number of subscribers on start month per brand"),
        new_sub_number              INTEGER                 OPTIONS(description="number of new subscription on the month per brand"),
        new_unsub_number            INTEGER                 OPTIONS(description="number of new unsubscription on the month per brand"),
        alert_number                INTEGER                 OPTIONS(description="number of alert on the month per brand"),
        delivered                   INTEGER                 OPTIONS(description="number of delivered email on the month per brand"),
        opens                       INTEGER                 OPTIONS(description="number of opened email on the month per brand"),
        openers                     INTEGER                 OPTIONS(description="number of unique openers on the month per brand"),
        clicks                      INTEGER                 OPTIONS(description="number of clicked email on the month per brand"),
        clickers                    INTEGER                 OPTIONS(description="number of unique clickers on the month per brand"),
            evolution               STRUCT<
                subcriber_number        FLOAT64                 OPTIONS(description="subscriber number evolution between previous month and current month per brand"),
                new_sub_number          FLOAT64                 OPTIONS(description="new subscription number evolution between previous month and current month per brand"),
                new_unsub_number        FLOAT64                 OPTIONS(description="new unsubscription evolution between previous month and current month per brand"),
                alert_number            FLOAT64                 OPTIONS(description="alert number evolution between previous month and current month per brand"),
                delievered              FLOAT64                 OPTIONS(description="number of delivered email evolution between previous month and current month per brand"),
                opens                   FLOAT64                 OPTIONS(description="number of opened email evolution between previous month and current month per brand"),
                openers                 FLOAT64                 OPTIONS(description="number of unique openers evolution between previous month and current month per brand"),
                clicks                  FLOAT64                 OPTIONS(description="number of clicked email evolution between previous month and current month per brand"),
                clickers                FLOAT64                 OPTIONS(description="number of unique clickers evolution between previous month and current month per brand")
            >                                                   OPTIONS(description="list kpis evolution between current vs previous month")
    >                                                    OPTIONS(description="list all kpis at brand scale"),
    tag                      STRUCT<
        slug                        ARRAY<STRING>               OPTIONS(description="tag slug. ref: {{ params.bq_project.mirror }}.store_one_article.tag.slug"),
        kpi                         STRUCT<
            subscriber_number           ARRAY<INTEGER>          OPTIONS(description="list[] of number of subscribers on start month per brand per tag"),
            new_sub_number              ARRAY<INTEGER>          OPTIONS(description="list[] of number of new subscription on the month per brand per tag"),
            new_unsub_number            ARRAY<INTEGER>          OPTIONS(description="list[] of number of new unsubscription on the month per brand per tag"),
            alert_number                ARRAY<INTEGER>          OPTIONS(description="list[] of number of sent alerts on the month per brand per tag"),
            delivered                   ARRAY<INTEGER>          OPTIONS(description="list[] of number of delivered emails on the month per brand per tag"),
            opens                       ARRAY<INTEGER>          OPTIONS(description="list[] of opened emails on the month per brand per tag"),
            openers                     ARRAY<INTEGER>          OPTIONS(description="list[] of distinct openers on the month per brand per tag"),
            clicks                      ARRAY<INTEGER>          OPTIONS(description="list[] of clicked email on the month per brand per tag"),
            clickers                    ARRAY<INTEGER>          OPTIONS(description="list[] of distinct clickers on the month per brand per tag"),
            rank                        STRUCT<
                subcriber_number            ARRAY<INTEGER>          OPTIONS(description="list[] of tag ranking per brand per month based on subscriber_number"),
                new_sub_number              ARRAY<INTEGER>          OPTIONS(description="list[] of tag ranking per brand per month based on new_sub_number"),
                new_unsub_number            ARRAY<INTEGER>          OPTIONS(description="list[] of tag ranking per brand per month based on new_unsub_number"),
                alert_number                ARRAY<INTEGER>          OPTIONS(description="list[] of tag ranking per brand per month based on alert_number"),
                delivered                   ARRAY<INTEGER>          OPTIONS(description="list[] of tag ranking per brand per month based on number of delivered emails"),
                opens                       ARRAY<INTEGER>          OPTIONS(description="list[] of tag ranking per brand per month based on number of opened emails"),
                openers                     ARRAY<INTEGER>          OPTIONS(description="list[] of tag ranking per brand per month based on number of distinct openers"),
                clicks                      ARRAY<INTEGER>          OPTIONS(description="list[] of tag ranking per brand per month based on number of clicked emails"),
                clickers                    ARRAY<INTEGER>          OPTIONS(description="list[] of tag ranking per brand per month based on number of distinct openers")
            >                                                       OPTIONS(description="list tag rank on the current month based on listed kpi"),
            evolution                   STRUCT<
                subscriber_number           ARRAY<FLOAT64>          OPTIONS(description="list[] of subscriber number evolution between previous month and current month"),
                new_sub_number              ARRAY<FLOAT64>          OPTIONS(description="list[] of new subscription number evolution between previous month and current month"),
                new_unsub_number            ARRAY<FLOAT64>          OPTIONS(description="list[] of new unsubscription number evolution between previous month and current month"),
                alert_number                ARRAY<FLOAT64>          OPTIONS(description="list[] of alert number between previous month and current month"),
                delivered                   ARRAY<FLOAT64>          OPTIONS(description="list[] of delivered kpi evolution between previous month and current month"),
                opens                       ARRAY<FLOAT64>          OPTIONS(description="list[] of opens kpi evolution between previous month and current month"),
                openers                     ARRAY<FLOAT64>          OPTIONS(description="list[] of openers kpi evolution between previous month and current month"),
                clicks                      ARRAY<FLOAT64>          OPTIONS(description="list[] of clicks kpi evolution between previous month and current month"),
                clickers                    ARRAY<FLOAT64>          OPTIONS(description="list[] of clickers kpi evolution between previous month and current month"),
                rank_subscriber_number      ARRAY<INTEGER>          OPTIONS(description="list[] of tag ranking evolution between previous and current month based on subscriber number"),
                rank_new_sub_number         ARRAY<INTEGER>          OPTIONS(description="list[] of tag ranking evolution between previous and current month based on new subscription number"),
                rank_new_unsub_number       ARRAY<INTEGER>          OPTIONS(description="list[] of tag ranking evolution between previous and current month based on new unsubscription number"),
                rank_alert_number           ARRAY<INTEGER>          OPTIONS(description="list[] of tag ranking evolution between previous and current month based on alert number"),
                rank_delivered              ARRAY<INTEGER>          OPTIONS(description="list[] of tag ranking evolution between previous and current month based on delivered kpi"),
                rank_opens                  ARRAY<INTEGER>          OPTIONS(description="list[] of tag ranking evolution between previous and current month based on opens kpi"),
                rank_openers                ARRAY<INTEGER>          OPTIONS(description="list[] of tag ranking evolution between previous and current month based on openers kpi"),
                rank_clicks                 ARRAY<INTEGER>          OPTIONS(description="list[] of tag ranking evolution between previous and current month based on clicks kpi"),
                rank_clickers               ARRAY<INTEGER>          OPTIONS(description="list[] of tag ranking evolution between previous and current month based on clickers kpi")
            >                                                   OPTIONS(description="list kpis evolution between current vs previous month")
        >                                                       OPTIONS(description="tag kpis")
    >                                                       OPTIONS(description="list all tag information")
)
PARTITION BY start_month
OPTIONS(description="List follow tags kpis per start month per brand."||
                    "To generate this table, we took only bookmarks with type='follow:tag' and 'follow'."||
                    "These kpis are:" ||
                    "* number of subscribers on start month."||
                    "* number of new subscription on current month."||
                    "* number of new unsubscription on current month."||
                    "* number of generated alerts on current month."||
                    "* tags ranking based on these stats per month per brand."||
                    "* evolution of tag following based on these stats per month per brand."||
                    "DAG: {{ dag.dag_id }}."||
                    "Sync: Monthly at 00:00AM UTC+2");

INSERT INTO `{{ params.bq_project.mirror }}.generated_data.follow_tag_kpi_brand_monthly`
-- WITH sub-queries :
-- * start_months : generate first day of each month
-- * month_couple : generate array of months(first day, end day)
-- * tag : select tag information from tag table
-- * bmk_follow_tag : extract bookmark = 'follow:tag' and enrich bookmark with slug
-- * bmk_follow : extract bookmark = 'follow' and enrich bookmark with slug
-- * bmk_data : union of 'follow:tag' and 'follow' bookmarks
-- * alert_tag : select alert information from alert table for 'follow:tag' bookmarks and enrich it with slug
-- * alert_follow : select alert information from alert table for 'follow' bookmarks and enrich it with slug
-- * alert_data : union of 'follow:tag' and 'follow' alerts
-- * tmail_splio : get alert/bookmark related to splio campaigns
-- * subscribers : add is_subscribed boolean variable depending on (start month, create_date, update_date)
-- * count_subscribers : count subscribers number based of start month
-- * count_new_sub : count subscription depending on creation date between (start month, end month)
-- * count_new_unsub : count unsubscription depending on update date between (start month, end month)
-- * count_alert : count send alert number per tag and brand on the current month
-- * count_email : count delivered, opens, openers, and clickers email per month per brand per tag
-- * tag_kpi : join all counts together and add tag slug
-- * tag_kpi_evolution : get evolution of all tag KPI between previous month and current month
-- * count_brand_subscribers : count subscribers per month per brand
-- * count_brand_new_sub : count new subscription per month per brand
-- * count_brand_new_unsub : count new unsubscription per month per brand
-- * count_brand_alert : count alert per month per brand
-- * count_brand_email : count delivered, opens, openers, and clickers email per month per brand
-- * brand_kpi : get all brand kpis together
-- * brand_kpi_evolution : compute brand kpis evolutions
-- lastly, put all kpis together and put them into struct

-- To test :
-- slug = 'confinement', brand_trigram = CAP, start_month = '2020-06-01'
-- glossary : * subscriber_number --> abonée
--            * new_sub_number --> souscription
--            * new_unsub_number --> désinscription
--            * alert_number --> diffusion
-- NB : In the bookmark table, there are some bookmark.update_date < bookmark.create_date
WITH start_months AS(
    -- generate first day of each month
    -- min date= '2017-11-01'
    SELECT
        start_month
    FROM UNNEST(GENERATE_DATE_ARRAY(CAST('2017-11-01' AS DATE), CURRENT_DATE(), INTERVAL 1 MONTH)) AS start_month
), month_couple AS(
     -- generate array of months(first day, end day)
    SELECT
        start_month,
        DATE_SUB(DATE_TRUNC(DATE_ADD(start_month, INTERVAL 1 MONTH), MONTH), INTERVAL 1 DAY) AS end_month
    FROM start_months
), tag AS(
    -- select tag information from tag table
    SELECT DISTINCT
        id,
        slug
    FROM `{{ params.bq_project.mirror }}.store_one_article.tag`
    WHERE slug IS NOT NULL
), bmk_follow_tag AS(
    -- extract bookmark = 'follow:tag' and enrich bookmark with slug
    SELECT
        brand_trigram,
        COALESCE(old_t.slug, new_t.slug)    AS slug,
        bmk.create_date,
        bmk.update_date,
        bmk.is_deleted,
        shoot_id,
        bmk.user.pmc_uuid
    FROM `{{ params.bq_project.mirror }}.refined_data.bookmark` AS b
    LEFT JOIN UNNEST(b.bookmark) AS bmk
    LEFT JOIN tag AS old_t ON old_t.id = content_id
    LEFT JOIN tag AS new_t ON new_t.slug = content_id
    WHERE bmk.type = 'follow:tag'
), bmk_follow AS(
    -- extract bookmark = 'follow' and enrich bookmark with slug
    SELECT
        brand_trigram,
        t.slug,
        bmk.create_date,
        bmk.update_date,
        bmk.is_deleted,
        shoot_id,
        bmk.user.pmc_uuid
    FROM `{{ params.bq_project.mirror }}.refined_data.bookmark` AS b
    LEFT JOIN UNNEST(b.bookmark) AS bmk
    JOIN tag AS t ON t.slug = content_id
    WHERE bmk.type = 'follow'
), bmk_data AS(
    -- union of 'follow:tag' and 'follow' bookmarks
    SELECT * FROM bmk_follow_tag
    UNION DISTINCT
    SELECT * FROM bmk_follow
), alert_tag AS(
    -- select alert information from alert table for 'follow:tag' bookmarks and enrich it with slug
    SELECT DISTINCT
        brand_trigram,
        t.slug,
        resource_id,
        create_date
    FROM `{{ params.bq_project.mirror }}.refined_data.alert`    AS a,  UNNEST(a.content_id) AS content_id WITH OFFSET OFF
    JOIN tag                                                    AS t ON t.id = content_id
), alert_follow AS (
    -- select alert information from alert table for 'follow' bookmarks and enrich it with slug
    SELECT DISTINCT
        brand_trigram,
        t.slug,
        resource_id,
        create_date
    FROM `{{ params.bq_project.mirror }}.refined_data.alert`    AS a,  UNNEST(a.content_id) AS content_id WITH OFFSET OFF
    JOIN tag                                                    AS t ON t.slug = content_id
), alert_data AS(
    -- union of 'follow:tag' and 'follow' alerts
    SELECT * FROM alert_tag
    UNION DISTINCT
    SELECT * FROM alert_follow
), tmail_splio AS(
    -- get alert/bookmark related to splio campaigns
    SELECT DISTINCT
        mc.start_month,
        bd.brand_trigram,
        bd.slug,
        s.stats.delivered,
        s.stats.opens,
        s.stats.openers,
        s.stats.clicks,
        s.stats.clickers
    FROM `{{ params.bq_project.matrix }}.refined_data.tmail_alert`  AS ta, UNNEST(ta.content_id) AS shoot_id WITH OFFSET OFF
    JOIN month_couple                                               AS mc   ON  mc.start_month <= DATE(ta.update_date)
                                                                            AND DATE(ta.update_date) <= mc.end_month
    JOIN `{{ params.bq_project.matrix }}.refined_data.splio_report` AS s    ON  s.id.campaign_ref = ta.campaign
                                                                            AND mc.start_month <= DATE(s.start_date)
                                                                            AND DATE(s.end_date) <= mc.end_month
    JOIN bmk_data                                                   AS bd   ON  bd.shoot_id = SAFE_CAST(shoot_id AS INT64)
                                                                            AND bd.brand_trigram = ta.brand_trigram
                                                                            AND mc.start_month <= DATE(bd.update_date)
                                                                            AND DATE(bd.update_date) <= mc.end_month
    WHERE ta.brand_trigram IS NOT NULL
), subscribers AS(
    -- add is_subscribed boolean variable depending on (start month, create_date, update_date)
    SELECT
        mc.start_month,
        bd.*,
        IF(bd.is_deleted = 0 OR (bd.is_deleted = 1 AND DATE(bd.update_date) > mc.start_month) ,1, 0) AS is_subscribed
    FROM bmk_data       AS bd
    JOIN month_couple   AS mc ON DATE(create_date) <= mc.start_month
), count_subscribers AS(
    -- count subscribers number based of start month
    SELECT
        start_month,
        brand_trigram,
        slug,
        COUNT(DISTINCT (CASE WHEN is_subscribed=1 THEN pmc_uuid ELSE NULL END)) AS subscriber_number
    FROM subscribers
    GROUP BY 1, 2, 3
), count_new_sub AS(
    -- count subscription depending on creation date between (start month, end month)
    SELECT DISTINCT
        mc.start_month,
        bd.brand_trigram,
        bd.slug,
        COUNT(bd.pmc_uuid) AS new_sub_number
    FROM  bmk_data      AS bd
    JOIN month_couple   AS mc  ON  mc.start_month <= DATE(bd.create_date) AND DATE(bd.create_date) <= mc.end_month
    GROUP BY 1, 2, 3
), count_new_unsub AS(
    -- count unsubscription depending on update date between (start month, end month)
    SELECT
        mc.start_month,
        bd.brand_trigram,
        bd.slug,
        COUNTIF(bd.is_deleted=1) AS new_unsub_number
    FROM bmk_data       AS bd
    JOIN month_couple   AS mc  ON  mc.start_month <= DATE(bd.update_date) AND DATE(bd.update_date) <= mc.end_month
    GROUP BY 1, 2, 3
), alert_bmk AS(
    -- join follow_tag and alert together to compute alert number kpi later
    SELECT
        mc.start_month,
        bd.brand_trigram,
        bd.slug,
        a.resource_id
    FROM alert_data AS a
    JOIN month_couple AS mc
        ON mc.start_month <= DATE(a.create_date)
        AND DATE(a.create_date) <= mc.end_month
    JOIN bmk_data AS bd
        ON bd.brand_trigram = a.brand_trigram
        AND bd.slug = a.slug
), count_alert AS (
    -- count send alert number per tag and brand on the current month
    SELECT
        start_month,
        brand_trigram,
        slug,
        COUNT(DISTINCT resource_id) AS alert_number
    FROM alert_bmk
    GROUP BY ALL
), count_email AS(
    -- count delivered, opens, openers, and clickers email per month per brand per tag
    SELECT DISTINCT
        start_month,
        brand_trigram,
        slug,
        SUM(delivered)    AS delivered,
        SUM(opens)        AS opens,
        SUM(openers)      AS openers,
        SUM(clicks)       AS clicks,
        SUM(clickers)     AS clickers
    FROM tmail_splio
    GROUP BY ALL
), tag_kpi AS (
    -- join all counts together and add tag slug
    SELECT
        cs.start_month,
        cs.brand_trigram,
        cs.slug,
        IFNULL(cs.subscriber_number, 0)                                                                 AS subscriber_number,
        IFNULL(cns.new_sub_number, 0)                                                                   AS new_sub_number,
        IFNULL(cnu.new_unsub_number, 0)                                                                 AS new_unsub_number,
        IFNULL(ca.alert_number, 0)                                                                      AS alert_number,
        IFNULL(ce.delivered, 0)                                                                         AS delivered,
        IFNULL(ce.opens, 0)                                                                             AS opens,
        IFNULL(ce.openers, 0)                                                                           AS openers,
        IFNULL(ce.clicks, 0)                                                                            AS clicks,
        IFNULL(ce.clickers, 0)                                                                          AS clickers,
        ROW_NUMBER() OVER(PARTITION BY start_month, cs.brand_trigram ORDER BY subscriber_number DESC)   AS rank_by_subcriber_number,
        ROW_NUMBER() OVER(PARTITION BY start_month, cs.brand_trigram ORDER BY new_sub_number DESC)      AS rank_by_new_sub_number,
        ROW_NUMBER() OVER(PARTITION BY start_month, cs.brand_trigram ORDER BY new_unsub_number DESC)    AS rank_by_new_unsub_number,
        ROW_NUMBER() OVER(PARTITION BY start_month, cs.brand_trigram ORDER BY alert_number DESC)        AS rank_by_alert_number,
        ROW_NUMBER() OVER(PARTITION BY start_month, cs.brand_trigram ORDER BY delivered DESC)           AS rank_by_delivered,
        ROW_NUMBER() OVER(PARTITION BY start_month, cs.brand_trigram ORDER BY opens DESC)               AS rank_by_opens,
        ROW_NUMBER() OVER(PARTITION BY start_month, cs.brand_trigram ORDER BY openers DESC)             AS rank_by_openers,
        ROW_NUMBER() OVER(PARTITION BY start_month, cs.brand_trigram ORDER BY clicks DESC)              AS rank_by_clicks,
        ROW_NUMBER() OVER(PARTITION BY start_month, cs.brand_trigram ORDER BY clickers DESC)            AS rank_by_clickers
    FROM count_subscribers  AS cs
    LEFT JOIN count_new_sub      AS cns  USING (start_month, brand_trigram, slug)
    LEFT JOIN count_new_unsub    AS cnu  USING (start_month, brand_trigram, slug)
    LEFT JOIN count_alert        AS ca   USING(start_month, brand_trigram, slug)
    LEFT JOIN count_email        AS ce   USING(start_month, brand_trigram, slug)
    -- To test the query
    --WHERE slug = "confinement" AND brand_trigram = "CAP" AND start_month = '2020-06-01'
), tag_kpi_evolution AS(
    -- get evolution of all tag KPI between previous month and current month
    SELECT
        curr.start_month,
        curr.brand_trigram,
        ARRAY_AGG(curr.slug IGNORE NULLS)                                                                                                       AS slug,
        ARRAY_AGG(curr.subscriber_number IGNORE NULLS)                                                                                          AS subscriber_number,
        ARRAY_AGG(curr.new_sub_number IGNORE NULLS)                                                                                             AS new_sub_number,
        ARRAY_AGG(curr.new_unsub_number IGNORE NULLS)                                                                                           AS new_unsub_number,
        ARRAY_AGG(curr.alert_number IGNORE NULLS)                                                                                               AS alert_number,
        ARRAY_AGG(curr.delivered IGNORE NULLS)                                                                                                  AS delivered,
        ARRAY_AGG(curr.opens IGNORE NULLS)                                                                                                      AS opens,
        ARRAY_AGG(curr.openers IGNORE NULLS)                                                                                                    AS openers,
        ARRAY_AGG(curr.clicks IGNORE NULLS)                                                                                                     AS clicks,
        ARRAY_AGG(curr.clickers IGNORE NULLS)                                                                                                   AS clickers,
        ARRAY_AGG(curr.rank_by_subcriber_number IGNORE NULLS)                                                                                   AS rank_by_subcriber_number,
        ARRAY_AGG(curr.rank_by_new_sub_number IGNORE NULLS)                                                                                     AS rank_by_new_sub_number,
        ARRAY_AGG(curr.rank_by_new_unsub_number IGNORE NULLS)                                                                                   AS rank_by_new_unsub_number,
        ARRAY_AGG(curr.rank_by_alert_number IGNORE NULLS)                                                                                       AS rank_by_alert_number,
        ARRAY_AGG(curr.rank_by_delivered IGNORE NULLS)                                                                                          AS rank_by_delivered,
        ARRAY_AGG(curr.rank_by_opens IGNORE NULLS)                                                                                              AS rank_by_opens,
        ARRAY_AGG(curr.rank_by_openers IGNORE NULLS)                                                                                            AS rank_by_openers,
        ARRAY_AGG(curr.rank_by_clicks IGNORE NULLS)                                                                                             AS rank_by_clicks,
        ARRAY_AGG(curr.rank_by_clickers IGNORE NULLS)                                                                                           AS rank_by_clickers,
        ARRAY_AGG(IFNULL(ROUND(SAFE_DIVIDE((curr.subscriber_number - prev.subscriber_number) , prev.subscriber_number), 4), 0) IGNORE NULLS)    AS subscriber_evolution,
        ARRAY_AGG(IFNULL(ROUND(SAFE_DIVIDE((curr.new_sub_number - prev.new_sub_number) , prev.new_sub_number), 4), 0) IGNORE NULLS)             AS new_sub_evolution,
        ARRAY_AGG(IFNULL(ROUND(SAFE_DIVIDE((curr.new_unsub_number - prev.new_unsub_number) , prev.new_unsub_number), 4), 0) IGNORE NULLS)       AS new_unsub_evolution,
        ARRAY_AGG(IFNULL(ROUND(SAFE_DIVIDE((curr.alert_number - prev.alert_number) , prev.alert_number), 4), 0)         IGNORE NULLS)           AS alert_evolution,
        ARRAY_AGG(IFNULL(ROUND(SAFE_DIVIDE((curr.delivered - prev.delivered) , prev.delivered), 4), 0)        IGNORE NULLS)                     AS delivered_evolution,
        ARRAY_AGG(IFNULL(ROUND(SAFE_DIVIDE((curr.opens - prev.opens) , prev.opens), 4), 0)                 IGNORE NULLS)                        AS opens_evolution,
        ARRAY_AGG(IFNULL(ROUND(SAFE_DIVIDE((curr.openers - prev.openers) , prev.openers), 4), 0) IGNORE NULLS)                                  AS openers_evolution,
        ARRAY_AGG(IFNULL(ROUND(SAFE_DIVIDE((curr.clicks - prev.clicks) , prev.clicks), 4), 0)   IGNORE NULLS)                                   AS clicks_evolution,
        ARRAY_AGG(IFNULL(ROUND(SAFE_DIVIDE((curr.clickers - prev.clickers) , prev.clickers), 4), 0)  IGNORE NULLS)                              AS clickers_evolution,
        ARRAY_AGG(IFNULL((prev.rank_by_subcriber_number - curr.rank_by_subcriber_number), 0)  IGNORE NULLS)                                     AS rank_subscriber_evolution,
        ARRAY_AGG(IFNULL((prev.rank_by_new_sub_number - curr.rank_by_new_sub_number), 0)   IGNORE NULLS)                                        AS rank_new_sub_evolution,
        ARRAY_AGG(IFNULL((prev.rank_by_new_unsub_number - curr.rank_by_new_unsub_number), 0) IGNORE NULLS)                                      AS rank_new_unsub_evolution,
        ARRAY_AGG(IFNULL((prev.rank_by_alert_number - curr.rank_by_alert_number), 0)    IGNORE NULLS)                                           AS rank_alert_evolution,
        ARRAY_AGG(IFNULL((prev.rank_by_delivered - curr.rank_by_delivered), 0)       IGNORE NULLS)                                              AS rank_delivered_evolution,
        ARRAY_AGG(IFNULL((prev.rank_by_opens - curr.rank_by_opens), 0)            IGNORE NULLS)                                                 AS rank_opens_evolution,
        ARRAY_AGG(IFNULL((prev.rank_by_openers - curr.rank_by_openers), 0)       IGNORE NULLS)                                                  AS rank_openers_evolution,
        ARRAY_AGG(IFNULL((prev.rank_by_clicks - curr.rank_by_clicks), 0)         IGNORE NULLS)                                                  AS rank_clicks_evolution,
        ARRAY_AGG(IFNULL((prev.rank_by_clickers - curr.rank_by_clickers), 0)     IGNORE NULLS)                                                  AS rank_clickers_evolution
    FROM tag_kpi        AS curr
    LEFT JOIN tag_kpi   AS prev ON curr.brand_trigram = prev.brand_trigram
                                AND UPPER(curr.slug)   = UPPER(prev.slug)
                                AND curr.start_month  = DATE_ADD(prev.start_month, INTERVAL 1 MONTH)
    GROUP BY 1, 2
), count_brand_subscribers AS(
    -- count subscribers per month per brand
    SELECT
        start_month,
        brand_trigram,
        COUNT(DISTINCT (CASE WHEN is_subscribed = 1 THEN pmc_uuid ELSE NULL END)) AS brand_subscriber_number
    FROM subscribers
    GROUP BY 1, 2
), count_brand_new_sub AS(
    -- count new subscription per month per brand
    SELECT
        start_month,
        brand_trigram,
        SUM(new_sub_number) AS brand_new_sub_number
    FROM count_new_sub
    GROUP BY 1, 2
), count_brand_new_unsub AS(
    -- count new unsubscription per month per brand
    SELECT
        start_month,
        brand_trigram,
        SUM(new_unsub_number) AS brand_new_unsub_number
    FROM count_new_unsub
    GROUP BY 1, 2
), count_brand_alert AS(
    -- count alert per month per brand
    SELECT
        start_month,
        brand_trigram,
        COUNT(DISTINCT resource_id) AS brand_alert_number
    FROM alert_bmk
    GROUP BY 1, 2
), count_brand_email AS(
    -- count delivered, opens, openers, and clickers email per month per brand
    SELECT
        start_month,
        brand_trigram,
        SUM(delivered)    AS delivered,
        SUM(opens)        AS opens,
        SUM(openers)      AS openers,
        SUM(clicks)       AS clicks,
        SUM(clickers)     AS clickers
    FROM count_email
    GROUP BY 1, 2
), brand_kpi AS(
    -- get all brand kpis together
    SELECT
        bs.start_month,
        bs.brand_trigram,
        bs.brand_subscriber_number  AS subscriber_number,
        bns.brand_new_sub_number    AS new_sub_number,
        bnu.brand_new_unsub_number  AS new_unsub_number,
        ba.brand_alert_number       AS alert_number,
        e.delivered,
        e.opens,
        e.openers,
        e.clicks,
        e.clickers
    FROM count_brand_subscribers    AS bs
    LEFT JOIN count_brand_new_sub   AS bns USING(start_month, brand_trigram)
    LEFT JOIN count_brand_new_unsub AS bnu USING(start_month, brand_trigram)
    LEFT JOIN count_brand_alert     AS ba  USING(start_month, brand_trigram)
    LEFT JOIN count_brand_email     AS e   USING(start_month, brand_trigram)
), brand_kpi_evolution AS(
    -- compute brand kpis evolutions
    SELECT
        curr.*,
        IFNULL(ROUND(SAFE_DIVIDE((curr.subscriber_number - prev.subscriber_number) , prev.subscriber_number), 4), 0)                    AS brand_subscriber_evolution,
        IFNULL(ROUND(SAFE_DIVIDE((curr.new_sub_number - prev.new_sub_number) , prev.new_sub_number), 4), 0)                             AS brand_new_sub_evolution,
        IFNULL(ROUND(SAFE_DIVIDE((curr.new_unsub_number - prev.new_unsub_number) , prev.new_unsub_number), 4), 0)                       AS brand_new_unsub_evolution,
        IFNULL(ROUND(SAFE_DIVIDE((curr.alert_number - prev.alert_number) , prev.alert_number), 4), 0)                                   AS brand_alert_evolution,
        IFNULL(ROUND(SAFE_DIVIDE((curr.delivered - prev.delivered) , prev.delivered), 4), 0)                                            AS delivered_evolution,
        IFNULL(ROUND(SAFE_DIVIDE((curr.opens - prev.opens) , prev.opens), 4), 0)                                                        AS opens_evolution,
        IFNULL(ROUND(SAFE_DIVIDE((curr.openers - prev.openers) , prev.openers), 4), 0)                                                  AS openers_evolution,
        IFNULL(ROUND(SAFE_DIVIDE((curr.clicks - prev.clicks) , prev.clicks), 4), 0)                                                     AS clicks_evolution,
        IFNULL(ROUND(SAFE_DIVIDE((curr.clickers - prev.clickers) , prev.clickers), 4), 0)                                               AS clickers_evolution
    FROM brand_kpi      AS curr
    LEFT JOIN brand_kpi AS prev ON curr.brand_trigram = prev.brand_trigram
                                AND curr.start_month  = DATE_ADD(prev.start_month, INTERVAL 1 MONTH)
)
-- get all kpis and their evolution
SELECT
    start_month,
    brand_trigram,
    STRUCT(
        b.subscriber_number,
        b.new_sub_number,
        b.new_unsub_number,
        b.alert_number,
        b.delivered,
        b.opens,
        b.openers,
        b.clicks,
        b.clickers,
        STRUCT(
            b.brand_subscriber_evolution  AS subscriber_number,
            b.brand_new_sub_evolution     AS new_sub_number,
            b.brand_new_unsub_evolution   AS new_unsub_number,
            b.brand_alert_evolution       AS alert_number,
            b.delivered_evolution         AS delivered,
            b.opens_evolution             AS opens,
            b.openers_evolution           AS openers,
            b.clicks_evolution            AS clikcks,
            b.clickers_evolution          As clickers
        ) AS evolution
    ) AS kpi,
    STRUCT(
        t.slug                            AS slug,
        STRUCT(
            t.subscriber_number            AS subscriber_number,
            t.new_sub_number               AS new_sub_number,
            t.new_unsub_number             AS new_unsub_number,
            t.alert_number                 AS alert_number,
            t.delivered                    AS delivered,
            t.opens                        AS opens,
            t.openers                      AS openers,
            t.clicks                       AS clicks,
            t.clickers                     AS clickers,
            STRUCT(
                t.rank_by_subcriber_number     AS subcriber_number,
                t.rank_by_new_sub_number       AS new_sub_number,
                t.rank_by_new_unsub_number     AS new_unsub_number,
                t.rank_by_alert_number         AS alert_number,
                t.rank_by_delivered            AS delivered,
                t.rank_by_opens                AS opens,
                t.rank_by_openers              AS openers,
                t.rank_by_clicks               AS clicks,
                t.rank_by_clickers             AS clickers
                ) AS rank,
            STRUCT(
                t.subscriber_evolution        AS subcriber_number,
                t.new_sub_evolution           AS new_sub_number,
                t.new_unsub_evolution         AS new_unsub_number,
                t.alert_evolution             AS alert_number,
                t.delivered_evolution         AS delivered,
                t.opens_evolution             AS opens,
                t.openers_evolution           AS openers,
                t.clicks_evolution            AS clicks,
                t.clickers_evolution          AS clickers,
                t.rank_subscriber_evolution   AS rank_subscriber_number,
                t.rank_new_sub_evolution      AS rank_new_sub_number,
                t.rank_new_unsub_evolution    AS rank_new_unsub_number,
                t.rank_alert_evolution        AS rank_alert_number,
                t.rank_delivered_evolution    AS rank_delivered,
                t.rank_opens_evolution        AS rank_opens,
                t.rank_openers_evolution      AS rank_openers,
                t.rank_clicks_evolution       AS rank_clicks,
                t.rank_clickers_evolution     AS rank_clickers
                ) AS evolution
            ) AS kpi
    ) AS person
FROM tag_kpi_evolution          AS t
LEFT JOIN brand_kpi_evolution   AS b USING (start_month, brand_trigram);
