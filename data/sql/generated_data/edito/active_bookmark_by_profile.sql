-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.active_bookmark_content_by_profile`(
    pmc_uuid                STRING      NOT NULL  OPTIONS(description="user uuid. ref:{{ params.bq_project }}.generated_data.profile_bookmark_content_status.user_id"),
    brand_trigram           STRING      NOT NULL  OPTIONS(description="brand trigram. ref:{{ params.bq_project }}.generated_data.profile_bookmark_content_status.brand_trigram"),
    bookmark_type           STRING      NOT NULL  OPTIONS(description="bookmark type. ref:{{ params.bq_project }}.generated_data.profile_bookmark_content_status.bookmark_type"),
    active_bookmarks        INT64       NOT NULL  OPTIONS(description="number of currently active (not deleted by the user) bookmark contents. ref:{{ params.bq_project }}.generated_data.profile_bookmark_content_status.is_alive"),
    inactive_bookmarks      INT64       NOT NULL  OPTIONS(description="number of currently inactive (deleted by the user) bookmark contents. ref:{{ params.bq_project }}.generated_data.profile_bookmark_content_status.is_alive"),
    new_active_bookmarks    INT64       NOT NULL  OPTIONS(description="number of bookmark contents created by the user during the current month. ref:{{ params.bq_project }}.generated_data.profile_bookmark_content_status.is_new_sub"),
    new_inactive_bookmarks  INT64       NOT NULL  OPTIONS(description="number of bookmark contents deleted by the user during the current month. ref:{{ params.bq_project }}.generated_data.profile_bookmark_content_status.is_new_unsub"),
    first_create_date       DATE        NOT NULL  OPTIONS(description="date of first bookmark content creation by the user. ref:{{ params.bq_project }}.generated_data.profile_bookmark_content_status.create_date"),
    last_update_date        DATE        NOT NULL  OPTIONS(description="latest date of bookmark content update by the user. ref:{{ params.bq_project }}.generated_data.profile_bookmark_content_status.update_date")
)
PARTITION BY last_update_date
OPTIONS(description="This table contains bookmark content data aggregated by profile and brand"
                  ||"\n\n"
                  ||"DAG: {{ dag.dag_id }}"
                  ||"\n\n"
                  ||"Sync: Daily");

TRUNCATE TABLE `{{ params.bq_project }}.generated_data.active_bookmark_content_by_profile`;

INSERT INTO `{{ params.bq_project }}.generated_data.active_bookmark_content_by_profile`
-- for each profile, brand and bookmark_type:
-- count the number of active (new and old) and inactive (new and old) bookmark contents
-- get the first creation date and the last update date
SELECT
    pmc_uuid,
    brand_trigram,
    bookmark_type,
    --@to be revised --> cf. Gala's Follow Star process
    COUNTIF(is_alive) AS active_bookmarks,
    COUNTIF(NOT is_alive) AS inactive_bookmarks,
    COUNTIF(is_new_sub) AS new_active_bookmarks,
    COUNTIF(is_new_unsub) AS new_inactive_bookmarks,
    DATE(MIN(create_date)) AS first_create_date,
    DATE(MAX(update_date)) AS last_update_date
FROM `{{ params.bq_project }}.generated_data.profile_bookmark_content_status`
GROUP BY ALL;
