-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
CREATE TABLE IF NOT EXISTS `{{ params.bq_project.matrix }}.workspace.article_keywords`(
   article_id    STRING  OPTIONS(description="article uuid. ref: {{ params.bq_project.mirror }}.store_one_article.article.id"),
   brand_trigram STRING  OPTIONS(description="brand trigram"),
   url           STRING  OPTIONS(description="list of articles url concatenastring separated by ','"),
   key_words     STRING  OPTIONS(description="keywords concatenated from title, url, tag, head and categories of article")
)
OPTIONS(description="This table extracts keyword from each article. "||
                    "It is used to compute profile affinity score. "||
                    "DAG: {{ dag.dag_id }}. "||
                    "Sync: weekly on Saturday at 3:02PM UTC+2");
TRUNCATE TABLE `{{ params.bq_project.matrix }}.workspace.article_keywords` ;

INSERT INTO `{{ params.bq_project.matrix }}.workspace.article_keywords`
WITH article AS(
    -- select non array columns
    SELECT
        a.article_id,
        a.brand_trigram,
        a.content.title AS title,
        a.content.head AS head
    FROM `{{ params.bq_project.mirror }}.refined_data.article` AS a
), unnest_url AS(
    -- unnest public url column separately, becasue sometimes public url is null .
    -- concat all article URLs
    SELECT
        article_id,
        STRING_AGG(DISTINCT public_url, ', ')  AS url
    FROM  `{{ params.bq_project.mirror }}.refined_data.article` AS a, UNNEST(a.content.url.public) AS public_url
    GROUP BY 1
), unnest_tag AS(
    -- unnest tag structure and concat all article tags
    SELECT
        article_id,
        STRING_AGG(DISTINCT LOWER(tag.title), ', ') AS tag
    FROM `{{ params.bq_project.mirror }}.refined_data.article` AS a, UNNEST(content.tag) AS tag WITH OFFSET OFF
    GROUP BY 1
), unnest_categories AS(
     -- unnest category structure and concat all article categories
    SELECT
        article_id,
        STRING_AGG(CONCAT(IFNULL(LOWER(category.cat_1), ''), '/', IFNULL(LOWER(category.cat_2), ''), '/', IFNULL(LOWER(category.cat_3), ''), '/', IFNULL(LOWER(category.cat_4), ''), '/', IFNULL(LOWER(category.cat_5), ''))) AS concat_category
    FROM `{{ params.bq_project.mirror }}.refined_data.article`, UNNEST(content.category) AS category WITH OFFSET OFF
    GROUP BY 1
)

SELECT
    article_id,
    brand_trigram,
    url,
    LOWER(CONCAT(IFNULL(title, ''), ', ', IFNULL(url,''), ', ', IFNULL(head,''), ', ', IFNULL(tag, ''), ', ', IFNULL(concat_category, '')))     AS key_words
FROM article
LEFT JOIN unnest_url            AS ur USING(article_id)
LEFT JOIN unnest_tag            AS ut USING(article_id)
LEFT JOIN unnest_categories     AS c USING(article_id);






