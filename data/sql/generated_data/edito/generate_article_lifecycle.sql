-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.article_lifecycle`
(
    article_id     STRING     NOT NULL     OPTIONS(description="Article UUID"),
    brand_trigram  STRING     NOT NULL     OPTIONS(description="Brand trigram"),
    article_status STRING     NOT NULL     OPTIONS(description="Article status as 'draft', 'pending', 'published', ..."),
    create_date    TIMESTAMP  NOT NULL     OPTIONS(description="Article status creation date"),
    user_id        STRING     NOT NULL     OPTIONS(description="User UUID"),
    PRIMARY KEY (article_id, brand_trigram, article_status, create_date) NOT ENFORCED
)
PARTITION BY DATE(create_date)
CLUSTER BY brand_trigram, article_status, user_id
OPTIONS(description="This table contains article lifecycle. \n"
                    ||"Sync: Daily \n"
                    ||"DAG: {{ dag.dag_id }} \n"
);

TRUNCATE TABLE  `{{ params.bq_project }}.generated_data.article_lifecycle`;

INSERT INTO `{{ params.bq_project }}.generated_data.article_lifecycle`
SELECT
  a.id          AS article_id,
  a.brandKey    AS brand_trigram,
  LOWER(
    JSON_VALUE(snapshot, "$.status")
  ) AS article_status,
  ah.createdAt  AS create_date,
  SPLIT(userId, ":")[SAFE_ORDINAL(2)] AS user_id
FROM `{{ params.bq_project }}.store_one_article.articleHistory` AS ah
JOIN `{{ params.bq_project }}.store_one_article.article` AS a ON a.intId =  ah.articleIntId
WHERE ah.changes = '["status"]'
ORDER BY article_id, create_date;