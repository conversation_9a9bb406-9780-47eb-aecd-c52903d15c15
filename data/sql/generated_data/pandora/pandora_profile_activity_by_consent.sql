-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE start_date DATE DEFAULT DATE("{{ params.start_date }}");                                                        -- by default 2021-04-01
DECLARE end_date DATE DEFAULT DATE_SUB(DATE_TRUNC(DATE("{{ data_interval_end }}"), MONTH), INTERVAL 1 MONTH);

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.pandora_profile_activity_by_consent` (
    reference_month	                DATE         NOT NULL   OPTIONS(description="Reference month."),
    email_profile_master_id	        INT64        NOT NULL   OPTIONS(description="Profile email master ID."),
    acquisition_date	            DATE         NOT NULL   OPTIONS(description="Profile's first acquisition date for the consent."),
    email_consent_public_ref	    STRING       NOT NULL   OPTIONS(description="Email consent public ref."),
    consent_type                    STRING       NOT NULL   OPTIONS(description="Type of consent."),
    brand_trigram                   STRING       NOT NULL   OPTIONS(description="Brand trigram."),
    owner_name                      STRING       NOT NULL   OPTIONS(description="Owner name as 'prisma' or 'cerise'."),
    reactivity_data	        	        ARRAY<STRUCT<
        observation_date	    DATE            OPTIONS(description="Activity days in the reference month."),
        open_volume	            INT64           OPTIONS(description="Volume of open events."),
        unique_open_volume	    INT64           OPTIONS(description="Volume of unique open events"),
        click_volume	        INT64           OPTIONS(description="Volume of click events"),
        unique_click_volume	    INT64           OPTIONS(description="Volume of unique click events.")
        >
    >                                                       OPTIONS(description="Profile reactivity data calculated in the reference month."),
    is_alive                        BOOL         NOT NULL   OPTIONS(description="True if the profile is alive in the reference month, false otherwise.\n"
                                                                              ||"To be considered alive, a profile must:\n"
                                                                              ||"-have no unsub\n"
                                                                              ||"- or have an unsub after the reference month"),
    last_alive_date                 DATE                    OPTIONS(description="Last alive date in the reference month, if any.It is calculated as:\n"
                                                                              ||"- NULL if no sub or unsub in the reference month\n"
                                                                              ||"- Last day of the reference month if no unsub in the reference month\n"
                                                                              ||"- unsub date otherwise"),
    PRIMARY KEY(reference_month, email_profile_master_id, email_consent_public_ref) NOT ENFORCED
)
PARTITION BY reference_month
CLUSTER BY brand_trigram, email_consent_public_ref
OPTIONS(
    description="This table contains Pandora profiles' activity at consent level. It is calculated monthly, regardless of the acquisition date.\n"
              ||"For each profile, we consider only the first acquisition date for each consent.\n\n"
              ||"DAG: {{ dag.dag_id }}.\n\n"
              ||"Sync: Monthly at the 1st day of month.",
    labels=[
        ('scope', 'pandora'),
        ('project', 'pandora_ltv'),
        ('creator', 'rodrigo_santana')
    ]
);

TRUNCATE TABLE `{{ params.bq_project }}.generated_data.pandora_profile_activity_by_consent`;

INSERT INTO `{{ params.bq_project }}.generated_data.pandora_profile_activity_by_consent`
WITH filter_acquired_profiles AS (
    -- get acquired profiles from start_date to end_date and their FIRST acquisition date
    -- ignore GALA profiles
    SELECT
        pap.email_profile_master_id,
        pap.email_consent_public_ref,
        eb.owner_name,
        eb.brand_trigram,
        eb.consent_type,
        MIN(pap.acquisition_date) AS acquisition_date
    FROM `{{ params.bq_project }}.generated_data.pandora_acquired_profiles` AS pap
    JOIN `{{ params.bq_project }}.refined_data.email_base` AS eb ON eb.consent_public_ref = pap.email_consent_public_ref
    WHERE
        DATE(pap.acquisition_date) BETWEEN start_date AND end_date
        AND pap.email_consent_public_ref NOT LIKE "%gala%"
        AND pap.context NOT LIKE "%gala%"
        AND eb.consent_is_active
    GROUP BY ALL
),

get_reference_months AS (
    -- generate array of dates from the acquisition date up to the previous month
    -- this array assures the user will be accounted for every month (even if they do not click/open nor sub/unsub)
    SELECT
        email_profile_master_id,
        email_consent_public_ref,
        owner_name,
        brand_trigram,
        acquisition_date,
        consent_type,
        GENERATE_DATE_ARRAY(DATE_TRUNC(acquisition_date, MONTH), end_date, INTERVAL 1 MONTH) AS reference_months
    FROM filter_acquired_profiles
),

get_events_history AS (
    -- retrieve events for each month
    -- we do not calculate KPIs in here so that we keep a general table, useful for building consent-, brand-, global-level tables in business data
    SELECT
        reference_month,
        grm.email_profile_master_id,
        grm.email_consent_public_ref,
        grm.brand_trigram,
        grm.owner_name,
        grm.consent_type,
        grm.acquisition_date,
        erh.observation_date,
        erh.volume.open AS open_volume,
        erh.volume.unique_open AS unique_open_volume,
        erh.volume.click AS click_volume,
        erh.volume.unique_click AS unique_click_volume
    FROM get_reference_months AS grm, UNNEST(reference_months) AS reference_month
    LEFT JOIN `{{ params.bq_project }}.generated_data.email_reactivity_history_by_base` AS erh
        ON grm.email_profile_master_id = erh.email_profile_master_id
        AND grm.email_consent_public_ref = erh.email_consent_public_ref
        AND erh.observation_date >= grm.acquisition_date                                    -- only events after first acquisition (safety net)
        AND erh.observation_date BETWEEN start_date AND end_date                            -- same effect as a WHERE
        AND erh.observation_date BETWEEN reference_month AND LAST_DAY(reference_month)      -- only consider events in the respective month
    GROUP BY ALL
),

get_last_alive_date AS (
    -- generate an array of months between the last sub event in the month and the previous month
    -- this array will make sure we keep track of a profile even in months when they do not sub/unsub
    -- consider only subs after or in the same day as the acquisition date
    SELECT
        DATE_TRUNC(month, MONTH) AS alive_month,
        fap.email_profile_master_id,
        fap.email_consent_public_ref,
        plc.sub_date,
        plc.unsub_date,
    FROM `{{ params.bq_project }}.generated_data.email_profile_lifecycle_by_base_new` AS plc
    LEFT JOIN UNNEST(GENERATE_DATE_ARRAY(DATE_TRUNC(DATE(plc.sub_date), MONTH), end_date, INTERVAL 1 MONTH)) AS month
    JOIN filter_acquired_profiles AS fap
        ON fap.email_profile_master_id = plc.email_profile_master_id
        AND fap.acquisition_date <= DATE(plc.sub_date)
        AND fap.email_consent_public_ref = plc.email_consent_public_ref
    WHERE
        plc.email_consent_public_ref NOT LIKE "%gala%"
        AND DATE(plc.sub_date) BETWEEN start_date AND end_date
    QUALIFY ROW_NUMBER() OVER (PARTITION BY email_profile_master_id, DATE_TRUNC(month, MONTH), email_consent_public_ref ORDER BY sub_date DESC) = 1
)

SELECT
    reference_month,
    geh.email_profile_master_id,
    geh.acquisition_date,
    geh.email_consent_public_ref,
    geh.consent_type,
    geh.brand_trigram,
    geh.owner_name,
    ARRAY_AGG(
        STRUCT(
            geh.observation_date,
            geh.open_volume,
            geh.unique_open_volume,
            geh.click_volume,
            geh.unique_click_volume
        ) ORDER BY geh.observation_date
    ) AS reactivity_data,
    CASE
        WHEN lad.alive_month IS NOT NULL AND lad.unsub_date IS NULL THEN TRUE                                                   -- no unsub = still alive
        WHEN lad.alive_month IS NOT NULL AND DATE(lad.unsub_date) > LAST_DAY(geh.reference_month) THEN TRUE                     -- unsub in the next month = still alive in the reference month
        ELSE FALSE                                                                                                              -- not alive otherwise
    END AS is_alive,
    CASE
        WHEN DATE(lad.sub_date) > LAST_DAY(geh.reference_month) AND lad.unsub_date IS NULL THEN NULL                            -- no unsub nor sub in the reference month
        WHEN DATE(lad.sub_date) <= LAST_DAY(geh.reference_month) AND lad.unsub_date IS NULL THEN LAST_DAY(geh.reference_month)  -- sub in the reference month without unsub --> last alive date = last day of reference month
        WHEN DATE(lad.unsub_date) > LAST_DAY(geh.reference_month) THEN LAST_DAY(geh.reference_month)                            -- unsub after reference month --> last alive date = last day of the reference month
        ELSE DATE(lad.unsub_date)                                                                                               -- unsub in the reference month --> last alive date = unsub date
    END AS last_alive_date,
FROM get_events_history AS geh
LEFT JOIN get_last_alive_date AS lad
    ON geh.email_profile_master_id = lad.email_profile_master_id
    AND geh.email_consent_public_ref = lad.email_consent_public_ref
    AND geh.reference_month = lad.alive_month
GROUP BY ALL;
