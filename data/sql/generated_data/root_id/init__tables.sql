-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- Init all tables before processing root id

-- ORDERS
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.profile_root_id_orders_history`(
  observation_date        DATE              OPTIONS(description="Observation date."),
  root_id                 INT64             OPTIONS(description="Root id generated as auto-increment."),
  domain_name             STRING            OPTIONS(description="Domain name as ('email', 'pmc', 'batch', ...)."),
  create_date             TIMESTAMP         OPTIONS(description="Row creation datetime."),
  profile_master_id       STRING            OPTIONS(description="The profile master id for each domain."),
  action                  STRING            OPTIONS(description="The action to be applied on the Root id per domain. It's an enum: ('to_insert', 'to_delete', 'to_not_update', 'to_investigate')"),
  PRIMARY KEY (observation_date, root_id, domain_name, profile_master_id) NOT ENFORCED
)
PARTITION BY observation_date
CLUSTER BY domain_name, action
OPTIONS(
    description="This table contains the history of all orders applied.\n\n"
              ||"DAG: {{ dag.dag_id }}.\n\n"
              ||"Sync: Daily."
);

-- current orders
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.profile_root_id_orders`(
    root_id                 INT64     OPTIONS(description="Root id generated as auto-increment."),
    domain_name             STRING    OPTIONS(description="Domain name as ('email', 'pmc', 'batch', 'magazine', ...)."),
    create_date             TIMESTAMP OPTIONS(description="Row creation datetime."),
    profile_master_id       STRING    OPTIONS(description="The profile master id for each domain."),
    action                  STRING    OPTIONS(description="The action to be applied on the Root id per domain. It's an enum: ('to_insert', 'to_delete', 'to_not_update', 'to_investigate')"),
    PRIMARY KEY (root_id, domain_name, profile_master_id) NOT ENFORCED
)
PARTITION BY TIMESTAMP_TRUNC(create_date, DAY)
CLUSTER BY domain_name, action
OPTIONS(
    description="This table contains current orders based on comparison between D-1 and current snapshots of root id. \n\n"
              ||"DAG: {{ dag.dag_id }}. \n\n"
              ||"Sync: Daily."
);

-- ROOT ID
-- current root ids
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.profile_root_id_current_{{ data_interval_end.strftime('%Y%m%d') }}`(
    root_id                 INT64     OPTIONS(description="Root id generated as auto-increment."),
    domain_name             STRING    OPTIONS(description="Domain name as ('email', 'pmc', 'batch', ...)."),
    create_date             TIMESTAMP OPTIONS(description="Row Creation datetime."),
    profile_master_id       STRING    OPTIONS(description="The profile master id for each domain."),
    PRIMARY KEY (root_id, domain_name, profile_master_id) NOT ENFORCED
)
PARTITION BY TIMESTAMP_TRUNC(create_date, DAY)
CLUSTER BY domain_name
OPTIONS(
    description="This table contains current snapshot of Root ID. \n\n"
              ||"DAG: {{ dag.dag_id }}. \n\n"
              ||"Sync: Daily.",
    expiration_timestamp = TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL {{ params.expiration_interval }} DAY)
);

-- previous root id
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.profile_root_id_previous`(
    root_id                 INT64     OPTIONS(description="Root id generated as auto-increment."),
    domain_name             STRING    OPTIONS(description="Domain name as ('email', 'pmc', 'batch', ...)."),
    create_date             TIMESTAMP OPTIONS(description="Row Creation datetime."),
    profile_master_id       STRING    OPTIONS(description="The profile master id for each domain."),
    PRIMARY KEY (root_id, domain_name, profile_master_id) NOT ENFORCED
)
PARTITION BY TIMESTAMP_TRUNC(create_date, DAY)
CLUSTER BY domain_name
OPTIONS(
    description="This table contains D-1 snapshot of Root ID. \n\n"
              ||"DAG: {{ dag.dag_id }}. \n\n"
              ||"Sync: Daily.",
    expiration_timestamp = TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL {{ params.expiration_interval }} DAY)
);

-- final root id
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.profile_root_id`(
    root_id                 INT64     OPTIONS(description="Root id generated as auto-increment."),
    domain_name             STRING    OPTIONS(description="Domain name as ('email', 'pmc', 'batch', ...)."),
    create_date             TIMESTAMP OPTIONS(description="Row creation datetime."),
    profile_master_id       STRING    OPTIONS(description="The profile master id for each domain."),
    PRIMARY KEY (root_id, domain_name, profile_master_id) NOT ENFORCED
)
PARTITION BY TIMESTAMP_TRUNC(create_date, DAY)
CLUSTER BY domain_name
OPTIONS(
    description="This table contains Root id.\n"
              ||"Root Id maps profile ids between cross-domain.\n"
              ||"To compute daily Root id, we capture changes between D-1 and current snapshots.\n\n"
              ||"DAG: {{ dag.dag_id }}. \n\n"
              ||"Sync: Daily."
);

-- last root id
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.last_root_id`(
  last_root_id                 INT64     OPTIONS(description="Last Root id."),
  update_date                  TIMESTAMP OPTIONS(description="Row Update datetime."),
  PRIMARY KEY (last_root_id) NOT ENFORCED
)
OPTIONS(description="This table contains last root Id\n\n"
                  ||"DAG: {{ dag.dag_id }}.\n\n"
                  ||"Sync: Daily."
);

{% if params.is_first_run %}
-- Executes only in the first run of the process
TRUNCATE TABLE `{{ params.bq_project }}.generated_data.profile_root_id`;

TRUNCATE TABLE `{{ params.bq_project }}.generated_data.last_root_id`;

TRUNCATE TABLE `{{ params.bq_project }}.generated_data.profile_root_id_orders_history`;

TRUNCATE TABLE `{{ params.bq_project }}.generated_data.profile_root_id_current_{{ data_interval_end.strftime('%Y%m%d') }}`;

TRUNCATE TABLE `{{ params.bq_project }}.generated_data.profile_root_id_orders`;

TRUNCATE TABLE `{{ params.bq_project }}.generated_data.profile_root_id_previous`;

-- Init last root ID table
INSERT INTO `{{ params.bq_project }}.generated_data.last_root_id`
SELECT
    0 AS last_root_id,
    CURRENT_TIMESTAMP() AS update_date;
{% endif %}
