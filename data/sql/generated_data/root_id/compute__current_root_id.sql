-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- In order to compute the current Root ID, we loop through each domain inside the `root_id.json` file and:
-- 1. We check if the domain is active (via `is_active` key)
-- 2. For active domains, we extract its key id (used for root id) and the secondary keys (used in JOINs) using CTEs
-- 3. We join all CTEs created using the secondary keys and the links cross-domain (`linked_domain` dict)
-- 4. We concat all key ids to create a hash id
-- 5. With the hash id, we attribute a rank (using DENSE_RANK and `domain_order`) to each profile
-- 6. We pivot the resulting table --> 1 row = 1 (profile, domain)

DECLARE last_root_id INT64 DEFAULT (SELECT last_root_id FROM `{{ params.bq_project }}.generated_data.last_root_id`);

TRUNCATE TABLE `{{ params.bq_project }}.generated_data.profile_root_id_current_{{ data_interval_end.strftime('%Y%m%d') }}`;

INSERT INTO `{{ params.bq_project }}.generated_data.profile_root_id_current_{{ data_interval_end.strftime('%Y%m%d') }}`

-- namespace variables are persistent throughout the template

{% set counter = namespace() -%}
{%- set counter.active_domain = 0 -%}
{%- set counter.processed_comma = 0 -%}
{%- set counter.processed_union = 0 -%}
{%- for config in params.config -%}
    {%- for domain_name, domain_config in config.items() %}
        {%- if domain_config["is_active"] -%}
            {% set counter.active_domain = counter.active_domain + 1 %}
        {%- endif -%}
    {%- endfor -%}
{%- endfor -%}

WITH
-- loop through the list of domains
-- for each domain, get its configs
-- checks if the domain is active
-- get domain configs

{% for config in params.config %}
    {%- for domain_name, domain_config in config.items() %}
        {% if domain_config["is_active"] %}
            {%- set profile_table_schema = domain_config["profile_table_schema"] -%}
            {%- set profile_master_id = domain_config["profile_master_id"]["name"] -%}
            {%- set profile_secondary_ids = domain_config["profile_secondary_id"] -%}
            {%- set filter = domain_config["filter"] -%}

            get_profile_{{ domain_name }} AS (
            -- Get {{ domain_name }} profiles
                SELECT
                    {{ profile_master_id }},
                    -- get all foreign keys used to join CTEs
                {%- for profile_secondary_id in profile_secondary_ids %}
                    {{ profile_secondary_id["name"] }} AS {{ profile_secondary_id["alias"] }}{% if not loop.last -%}, {% endif -%}
                {% endfor %}
                FROM {{ profile_table_schema }}
            ),
        {% endif %}
    {%- endfor %}
{%- endfor -%}

prepare_root_id AS (
-- Create a primary key per profile
    SELECT
{%- for config in params.config -%}
    {%- for domain_name, domain_config in config.items() %}
        {% if domain_config["is_active"] %}
            {%- set profile_master_id = domain_config["profile_master_id"]["name"] -%}
                {{ profile_master_id }},
        {%- endif %}
    {%- endfor %}
{%- endfor -%}
        CONCAT(
{%- for config in params.config %}
    {%- for domain_name, domain_config in config.items() %}
        {% if domain_config["is_active"] %}
            {%- set counter.processed_comma = counter.processed_comma + 1 -%}
            {%- set profile_master_id = domain_config["profile_master_id"]["name"] -%}
            IFNULL(CAST(get_profile_{{ domain_name }}.{{ profile_master_id }} AS STRING) , "#"), "/"{% if counter.processed_comma < counter.active_domain -%}, {% endif %}
        {%- endif %}
    {%- endfor %}
{%- endfor -%}
        ) AS profile_master_id_hash
{% for config in params.config %}
    {%- for domain_name, domain_config in config.items() %}
        {%- if domain_config["is_active"] %}
            {%- set domain_order = domain_config["domain_order"] -%}
            {%- if domain_order == 0 -%}
                FROM get_profile_{{ domain_name }}
            {% endif %}
            {%- set linked_domain = domain_config["linked_domain"] -%}
            {%- if linked_domain|length > 0 -%}
                {% for linked_domain_name , linked_domain_config in linked_domain.items() %}
                    {%- for config in params.config -%}
                        {%- for linked_name, linked_config in config.items() %}
                            {%- if linked_name == linked_domain_name and linked_config["is_active"] -%}
                                {%- set profile_link_id = linked_domain_config["profile_link_id"] -%}
                                    FULL JOIN get_profile_{{ linked_domain_name }} USING({{ profile_link_id }})
                            {%- endif %}
                        {%- endfor %}
                    {%- endfor %}
                {%- endfor %}
            {%- endif %}
        {%- endif %}
    {%- endfor %}
{%- endfor %}
),

init_root_id AS (
    -- Initiate root id per profile
    -- active domains only
    SELECT
        profile_master_id_hash,
        last_root_id + DENSE_RANK() OVER(ORDER BY profile_master_id_hash) AS root_id,
{% for config in params.config %}
    {%- for domain_name, domain_config in config.items() %}
        {%- if domain_config["is_active"] %}
            {%- set profile_master_id = domain_config["profile_master_id"]["name"] -%}
            {{ profile_master_id }}{% if not loop.last -%}, {% endif %}
        {% endif %}
    {%- endfor %}
{%- endfor -%}
    FROM prepare_root_id
),

pivot_profiles AS (
    -- Transpose columns to rows
{% for config in params.config %}
    {%- for domain_name, domain_config in config.items() %}
        {% if domain_config["is_active"] %}
            {%- set counter.processed_union = counter.processed_union + 1 -%}
            {%- set profile_master_id = domain_config["profile_master_id"]["name"] -%}
            SELECT
                root_id,
                "{{ domain_name }}" AS domain_name,
                CURRENT_TIMESTAMP() AS create_date,
                CAST({{ profile_master_id }} AS STRING) AS profile_master_id
            FROM init_root_id
            WHERE {{ profile_master_id }} IS NOT NULL
            {% if counter.processed_union < counter.active_domain -%}UNION DISTINCT {% endif %}
        {%- endif -%}
    {%- endfor -%}
{%- endfor -%}
)

SELECT
    root_id,
    domain_name,
    create_date,
    profile_master_id
FROM pivot_profiles
ORDER BY root_id, domain_name;
