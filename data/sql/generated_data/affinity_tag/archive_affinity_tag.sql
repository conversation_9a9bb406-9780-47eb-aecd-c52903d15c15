-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project.matrix }}.generated_data.affinity_tag_history` (
    observation_date          DATE        NOT NULL    OPTIONS(description="observation date"),
    email_profile_master_id   INTEGER     NOT NULL    OPTIONS(description="ref : store_matrix_email.profile_master_id.id"),
    segment_names             ARRAY<STRING>           OPTIONS(description="affinity tags as list")
    ) PARTITION BY observation_date
    OPTIONS(description="Daily Affinity Tag by profile.\n"
                     || "\n\n"
                     || "DAG: '{{ dag.dag_id }}'"
                     || "\n"
                     || "Sync: Daily"
);

-- archive daily Affinity tag segments by profile
INSERT INTO `{{ params.bq_project.matrix }}.generated_data.affinity_tag_history`
SELECT 
    DATE("{{ next_ds }}") AS observation_date,
    atag.email_profile_master_id,
    atag.segment_names
FROM `{{ params.bq_project.matrix }}.generated_data.affinity_tag` AS atag
;
