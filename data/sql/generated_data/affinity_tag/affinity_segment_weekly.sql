-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

{% for theme, variable in params.affinity_variable.items() %}
    {% if variable["refresh"]|lower == "weekly" %}
        {% if variable['has_category'] %}
            {% for level, limits in variable['category'].items() %}
                CREATE TABLE IF NOT EXISTS `{{ params.bq_project.matrix }}.store_affinity_segment.segment-sha256_profile_score_{{ theme|lower }}_{{ level|lower }}` (

                email_sha256        STRING                  OPTIONS(description="email sha256. ref: {{ params.bq_project.matrix }}.store_matrix_email.profile_master_id.email_sha256"),
                update_date         DATE                    OPTIONS(description="creation date")
                )
                OPTIONS(description="Lists affinity segemts for {{ theme|lower }} theme and {{ level|lower }} category."
                                  ||"\n\n"
                                  ||"DAG: {{ dag.dag_id }}. "
                                  ||"\n"
                                  ||"Sync: daily")
                ;
                TRUNCATE TABLE `{{ params.bq_project.matrix }}.store_affinity_segment.segment-sha256_profile_score_{{ theme|lower }}_{{ level|lower }}`
                ;
                INSERT INTO `{{ params.bq_project.matrix }}.store_affinity_segment.segment-sha256_profile_score_{{ theme|lower }}_{{ level|lower }}`
                SELECT DISTINCT
                    s.email_sha256,
                    DATE('{{ next_ds }}') AS update_date
                FROM `{{ params.bq_project.matrix }}.generated_data.affinity_score`                 AS s
                {% if variable['has_prune'] %}
                    JOIN `{{ params.bq_project.matrix }}.generated_data.affinity_prune`             AS ap   ON  ap.email_sha256 = s.email_sha256
                                                                                                            AND ap.theme = "{{ theme|lower }}"
                {% endif %}
                -- set segment conditions
                WHERE
                    s.theme = "{{ theme|lower }}"
                    AND
                    s.category = "{{ level|lower }}"
                ;
            {% endfor %}
        {% else %}
            CREATE TABLE IF NOT EXISTS `{{ params.bq_project.matrix }}.store_affinity_segment.segment-sha256_profile_score_{{ theme|lower }}` (
            email_sha256        STRING                  OPTIONS(description="email sha256. ref: {{ params.bq_project.matrix }}.store_matrix_email.profile_master_id.email_sha256"),
            update_date         DATE                    OPTIONS(description="creation date")
            )
            OPTIONS(description="Lists affinity segments for {{ theme|lower }} theme."
                              ||"\n\n"
                              ||"DAG: {{ dag.dag_id }}. "
                              ||"\n"
                              ||"Sync: daily")
            ;
            TRUNCATE TABLE `{{ params.bq_project.matrix }}.store_affinity_segment.segment-sha256_profile_score_{{ theme|lower }}`;
            INSERT INTO `{{ params.bq_project.matrix }}.store_affinity_segment.segment-sha256_profile_score_{{ theme|lower }}`
            SELECT DISTINCT
                s.email_sha256,
                DATE('{{ next_ds }}') AS update_date
            FROM `{{ params.bq_project.matrix }}.generated_data.affinity_kpi`                   AS s
            {% if variable['has_prune'] %}
                JOIN `{{ params.bq_project.matrix }}.generated_data.affinity_prune`             AS ap   ON  ap.email_sha256 = s.email_sha256
                                                                                                        AND ap.theme = "{{ theme|lower }}"
            {% endif %}
            -- set segment conditions
            WHERE
                s.theme = "{{ theme|lower }}"
            ;
        {% endif %}
    {% endif %}
{% endfor %}
