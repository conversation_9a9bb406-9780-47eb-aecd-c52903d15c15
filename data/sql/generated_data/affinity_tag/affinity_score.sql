-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}


CREATE TABLE IF NOT EXISTS `{{ params.bq_project.matrix }}.generated_data.affinity_score`
(
    fake_date           DATE    NOT NULL    OPTIONS(description="set a fake date to permit clustering on theme"),
    email_sha256        STRING  NOT NULL    OPTIONS(description="email sha256. ref: {{ params.bq_project.matrix }}.store_matrix_email.profile_master_id.email_sha256"),
    theme               STRING  NOT NULL    OPTIONS(description="affinity segment theme as 'svod', 'game', 'health', ..."),
    score               FLOAT64 NOT NULL    OPTIONS(description="affinity score compute = recency_weight * normalized_recency +  frequency_weight * normalized_frequency + monetary_weight * normalized_monetary + monetary_theme_percent_weight * normalized_monetary_theme_percent"),
    category            STRING  NOT NULL    OPTIONS(description="category defined by stakeholders based on affinity score as ('high', 'medium', 'low', 'zero_affinity', 'first_percentile', 'second_percentile', ...'total_affinity')")
)
PARTITION BY fake_date
CLUSTER BY theme
OPTIONS(description="Contains profile affinity score across a defined time slot."||
                    "DAG : {{ dag.dag_id }}" ||
                    "Sync: Daily")
;
{% for theme, variable in params.affinity_variable.items() %}
    -- to avoid duplicates
    DELETE FROM `{{ params.bq_project.matrix }}.generated_data.affinity_score` AS ak
    WHERE ak.theme = "{{ theme|lower }}"
    ;
    -- insert into table
    INSERT INTO `{{ params.bq_project.matrix }}.generated_data.affinity_score`
    WITH scaling_kpi_ref AS (
        -- compute average and standard deviation of each KPI
        SELECT
            AVG(recency)                        AS recency_avg,
            AVG(frequency)                      AS frequency_avg,
            AVG(monetary_theme)                 AS monetary_theme_avg,
            AVG(monetary_theme_percent)         AS monetary_theme_percent_avg,
            STDDEV(recency)                     AS recency_std,
            STDDEV(frequency)                   AS frequency_std,
            STDDEV(monetary_theme)              AS monetary_theme_std,
            STDDEV(monetary_theme_percent)      AS monetary_theme_percent_std
        FROM `{{ params.bq_project.matrix }}.generated_data.affinity_kpi` AS ak
        WHERE ak.theme = "{{ theme|lower }}"
    ), normalized_kpi AS (
        -- compute normalized kpis based on their scaling reference
        SELECT
            fake_date,
            email_sha256,
            "{{ theme|lower }}" AS theme,
            (recency - recency_avg) / recency_std                                               AS normalized_recency,
            (frequency - frequency_avg) / frequency_std                                         AS normalized_frequency,
            (monetary_theme - monetary_theme_avg) / monetary_theme_std                          AS normalized_monetary_theme,
            (monetary_theme_percent - monetary_theme_percent_avg) / monetary_theme_percent_std  AS normalized_monetary_theme_percent
        FROM `{{ params.bq_project.matrix }}.generated_data.affinity_kpi` AS ak, scaling_kpi_ref
        WHERE ak.theme = "{{ theme|lower }}"
    ), compute_score AS (
        -- compute affinity score based on kpis and their weights
        SELECT
            fake_date,
            email_sha256,
            theme,
            {{ variable['weights']['recency'] }} * normalized_recency + {{ variable['weights']['frequency'] }}  * normalized_frequency + {{ variable['weights']['monetary_theme'] }}  * normalized_monetary_theme + {{ variable['weights']['monetary_theme_percent'] }}  * normalized_monetary_theme_percent AS score
        FROM normalized_kpi
        WHERE normalized_recency <> -99
            AND normalized_frequency <> 0
            AND normalized_monetary_theme <> 0
            AND normalized_monetary_theme_percent <> 0
    ), score_quartile AS (
        -- compute score quartile
        SELECT
            fake_date,
            email_sha256,
            theme,
            score,
            PERCENTILE_CONT(score, 0.5) OVER()  AS median,
            PERCENTILE_CONT(score, 0.25) OVER() AS q1,
            PERCENTILE_CONT(score, 0.75) OVER() AS q3
        FROM compute_score
    ), score_limit AS(
        -- set score >= q3 + 1.5*(q3-q1) to q3 + 1.5*(q3-q1)
        SELECT
            fake_date,
            email_sha256,
            theme,
            score,
            IF(score >= q3 + 1.5*(q3-q1),  q3 + 1.5*(q3-q1), score) AS upper_limit
        FROM score_quartile
    ), scaling_score_ref AS (
        -- compute average and standard deviation of score
        SELECT
            AVG(upper_limit)      AS avg_score,
            STDDEV(upper_limit)   AS std_score,
            MIN(upper_limit)      AS min_score,
            MAX(upper_limit)      AS max_score
        FROM score_limit
    ), normalized_score AS (
         -- compute normalized score based on its scaling reference
        SELECT
            fake_date,
            email_sha256,
            theme,
            ((upper_limit - avg_score) / std_score)               AS score_standardized,
            MAX(((upper_limit - avg_score) / std_score)) OVER ()  AS max_standardized,
            MIN(((upper_limit - avg_score) / std_score)) OVER()   AS min_standardized
        FROM score_limit, scaling_score_ref
    ), final_score AS (
        -- final score between [0,1]
        SELECT
            fake_date,
            email_sha256,
            theme,
            (score_standardized - min_standardized) / (max_standardized - min_standardized) AS score
        FROM normalized_score
    )
    -- add category for each profile
    SELECT
        fake_date,
        email_sha256,
        theme,
        score,
        CASE WHEN score = 0 THEN "uninterested"
        -- set categories dynamically
        {% for level, limits in variable['category'].items() %}
            WHEN
                score >  {{ limits['lower_limit'] }}
                AND score <= {{ limits['upper_limit'] }}
            THEN "{{ level|lower }}"
        {%endfor%}
        ELSE NULL
        END AS category
    FROM final_score
    ;
{% endfor %}
