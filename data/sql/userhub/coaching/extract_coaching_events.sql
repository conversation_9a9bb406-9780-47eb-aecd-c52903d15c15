-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{params.bq_project}}.store.coaching_event` (
    id                 STRING     NOT NULL    OPTIONS(description="ref : user_activities.flat_event.id"),
    event_date         TIMESTAMP  NOT NULL    OPTIONS(description="Event date"),
    version            STRING                 OPTIONS(description="Event version"),
    uuid               STRING     NOT NULL    OPTIONS(description="PMC profile uuid"),
    type               STRING     NOT NULL    OPTIONS(description="Type of Event, upsert|delete"),
    payload            STRING     NOT NULL    OPTIONS(description="Payload of Event")
    ) PARTITION BY TIMESTAMP_TRUNC(event_date, DAY) OPTIONS(
                 expiration_timestamp=NULL,
                 description="PMC coaching events.\n"
                 || "\n\n"
                 || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);

-- in case we have multiple events @ the same millisecond and we handled just one of them in the first execution,
-- during the next execution we will lose the other events according to the next query (last_date)
-- => it's more secure to delete last day records before inserting incremental data to overcome non fully handled overlapped events
DELETE FROM `{{params.bq_project}}.store.coaching_event`
WHERE DATE(event_date) > DATE_SUB(DATE('{{ next_ds }}'), INTERVAL 1 DAY)
;


INSERT INTO `{{params.bq_project}}.store.coaching_event`
-- Extract service user events from flat_event.
WITH last_coaching_date AS (
    SELECT IFNULL(MAX(event_date), TIMESTAMP_TRUNC(CAST('2021-10-01' AS TIMESTAMP), SECOND)) AS max_event_date
    FROM `{{params.bq_project}}.store.coaching_event`
), userhub_coaching_events AS (
    SELECT e.id,
        e.event_date,
        e.version,
        JSON_VALUE(content, '$.key') AS uuid,
        e.name AS type,
        content AS payload,
    FROM `{{params.bq_project}}.user_activities.flat_event` e
    CROSS JOIN last_coaching_date AS ld
    -- filter coaching events only
    WHERE content_type = 'coaching'
        -- make sure we have uuid
        AND JSON_VALUE(content, '$.key') IS NOT NULL
        -- incremental
        AND e.event_date > ld.max_event_date
        -- optimize depending on partition
        AND partition_date > DATE_SUB(DATE('{{ next_ds }}'), INTERVAL 5 DAY)
)
SELECT *
FROM userhub_coaching_events
;