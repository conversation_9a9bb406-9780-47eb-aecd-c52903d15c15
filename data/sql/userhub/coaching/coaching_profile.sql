-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{params.bq_userhub_project}}.refined_data.coaching_profile` (
    uuid                    STRING      NOT NULL    OPTIONS(description="PMC uuid"),
    container               STRING      NOT NULL    OPTIONS(description="Coaching container"),
    firstname               STRING                  OPTIONS(description="Coaching firstname"),
    last_activity_date      TIMESTAMP   NOT NULL    OPTIONS(description="last activity date of profile."),
    start_date              TIMESTAMP   NOT NULL    OPTIONS(description="start date of coaching"),
    end_date                TIMESTAMP   NOT NULL    OPTIONS(description="end date of coaching"),
    create_date             TIMESTAMP   NOT NULL    OPTIONS(description="coaching profile create date"),
    update_date             TIMESTAMP   NOT NULL    OPTIONS(description="coaching profile update date")
    ) OPTIONS(
                 expiration_timestamp=NULL,
                 description="PMC User coching profile by container.\n"
                 || "\n\n"
                 || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);

-- Apply upsert events
MERGE `{{params.bq_userhub_project}}.refined_data.coaching_profile` dest
USING (
        SELECT
            uuid,
            container,
            JSON_VALUE(payload, '$.data.inputForm.firstname') AS firstname,
            COALESCE(
                SAFE_CAST(JSON_VALUE(payload, '$.data.updatedAt') AS TIMESTAMP),
                TIMESTAMP_MILLIS(SAFE_CAST(JSON_VALUE(payload, '$.data.updatedAt') AS INT64))
            ) AS last_activity_date,
            COALESCE(
                SAFE_CAST(JSON_VALUE(payload, '$.data.startAt') AS TIMESTAMP),
                TIMESTAMP_MILLIS(SAFE_CAST(JSON_VALUE(payload, '$.data.startAt') AS INT64))
            ) AS start_date,
            COALESCE(
                SAFE_CAST(JSON_VALUE(payload, '$.data.endAt') AS TIMESTAMP),
                TIMESTAMP_MILLIS(SAFE_CAST(JSON_VALUE(payload, '$.data.endAt') AS INT64))
            ) AS end_date,
            COALESCE(
                SAFE_CAST(JSON_VALUE(payload, '$.data.createdAt') AS TIMESTAMP),
                TIMESTAMP_MILLIS(SAFE_CAST(JSON_VALUE(payload, '$.data.createdAt') AS INT64))
            ) AS create_date,
            COALESCE(
                SAFE_CAST(JSON_VALUE(payload, '$.data.updatedAt') AS TIMESTAMP),
                TIMESTAMP_MILLIS(SAFE_CAST(JSON_VALUE(payload, '$.data.updatedAt') AS INT64))
            ) AS update_date,
        FROM `{{params.bq_userhub_project}}.refined_data.coaching_event`
        WHERE type = 'upsert'
            AND DATE(event_date) > DATE_SUB(DATE('{{ next_ds }}'), INTERVAL {{params.interval}})

) source
ON dest.uuid = source.uuid
    AND dest.container = source.container
WHEN MATCHED THEN
UPDATE
    SET firstname = source.firstname,
        last_activity_date = source.last_activity_date,
        start_date = source.start_date,
        end_date = source.end_date,
        update_date = source.update_date
WHEN NOT MATCHED THEN
  INSERT (uuid, container, firstname, last_activity_date, start_date, end_date,
          create_date, update_date)
  VALUES (source.uuid, source.container, source.firstname, source.last_activity_date, source.start_date, source.end_date,
    source.create_date, source.update_date)
;