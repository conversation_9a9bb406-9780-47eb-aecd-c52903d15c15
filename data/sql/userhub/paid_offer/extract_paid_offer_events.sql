-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{params.bq_project}}.store.user_paid_offer_event` (
    id                 STRING     NOT NULL    OPTIONS(description="ref : user_activities.flat_event.id"),
    event_date         TIMESTAMP  NOT NULL    OPTIONS(description="Event date"),
    version            STRING                 OPTIONS(description="Event version"),
    uuid               STRING                 OPTIONS(description="PMC profile uuid"),
    type               STRING                 OPTIONS(description="Type of Event, sub|unsub"),
    payload            STRING                 OPTIONS(description="Payload of Event")
    )  PARTITION BY TIMESTAMP_TRUNC(event_date, DAY) OPTIONS(
                 expiration_timestamp=NULL,
                 description="PMC userhub paid offers events.\n"
                 || "\n\n"
                 || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);

-- in case we have multiple events @ the same millisecond and we handled just one of them,
-- during the next execution we will lose the other events according to the next query
-- => it's more secure to delete last day records before inserting incremental data to overcome non fully handled overlapped events
DELETE FROM `{{params.bq_project}}.store.user_paid_offer_event`
WHERE DATE(event_date) > DATE_SUB(DATE('{{ next_ds }}'), INTERVAL 1 DAY);

INSERT INTO `{{params.bq_project}}.store.user_paid_offer_event`
-- Extract user paid offer subscription events from flat_event.
WITH last_date AS (
    SELECT IFNULL(MAX(event_date), TIMESTAMP_TRUNC(CAST('2022-03-29' AS TIMESTAMP), SECOND)) AS max_event_date
    FROM `{{params.bq_project}}.store.user_paid_offer_event`
), userhub_paid_offer_events AS (
    SELECT e.id,
        e.event_date,
        e.version,
        JSON_VALUE(content, '$.userId') as uuid,
        JSON_VALUE(content, '$.context.product.id') AS offer_ref,
        JSON_VALUE(content, '$.context.subscriptionId') AS subscription_ref,
        CASE WHEN JSON_VALUE(content, '$.eventName') = 'PaymentSubscribe' THEN 'sub' ELSE 'unsub' END as `type`,
        content AS payload
    FROM `{{params.bq_project}}.user_activities.flat_event` e
    CROSS JOIN last_date AS ld
    -- optimize and filter
    WHERE partition_date > DATE_SUB(DATE('{{ next_ds }}'), INTERVAL 10 DAY)
        AND content_type = 'user-event'
        AND JSON_VALUE(content, '$.eventName') IN ('PaymentSubscribe', 'PaymentUnsubscribe')
        -- ignore events from PMC brand (tests)
        AND JSON_VALUE(content, '$.brand') != 'PMC'
        AND JSON_VALUE(content, '$.context.product.id') IS NOT NULL
        -- incremental
        AND e.event_date > ld.max_event_date
), paid_offer_events_rank AS (
    SELECT * EXCEPT (offer_ref, subscription_ref),
      (
        -- current event is "unsub"
        type = 'unsub'
        -- AND previous event is a "sub"
        AND LAG(type, 1) OVER w = 'sub'
        -- AND previous event occurred less than 5 minutes ago
        AND TIMESTAMP_DIFF(LAG(event_date, 1) OVER w, event_date, MINUTE) <= 5
      ) AS ignore_event
    FROM userhub_paid_offer_events
    WINDOW w AS (PARTITION BY uuid, offer_ref ORDER BY event_date DESC)
)
SELECT * EXCEPT(ignore_event)
FROM paid_offer_events_rank
-- ignore duplicated identical events
WHERE ignore_event = FALSE
    -- keep unsub un-duplicated events
    OR ignore_event IS NULL
;