-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{params.bq_project}}.refined_data.pmc_profile_paid_offer_ppm`
    (
        uuid                STRING      NOT NULL    OPTIONS(description="PMC uuid"),
        enter_date          TIMESTAMP   NOT NULL    OPTIONS(description="subscription date"),
        exit_date           TIMESTAMP               OPTIONS(description="unsubscription date")
    ) OPTIONS(
                 expiration_timestamp=NULL,
                 description="PMC profiles sub/unsub to PPM offer (Pass Prisma Media).\n"
                 || "\n\n"
                 || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);

{% if params.full_export|lower != 'false'%}
TRUNCATE TABLE `{{params.bq_project}}.refined_data.pmc_profile_paid_offer_ppm`;
{% endif %}

-- deal with last sub from ppm offer
MERGE `{{params.bq_project}}.refined_data.pmc_profile_paid_offer_ppm` AS dest
USING (
  SELECT DISTINCT
    uuid,
    FIRST_VALUE(COALESCE(TIMESTAMP_MILLIS(CAST(JSON_VALUE(payload, '$.context.startAt') AS INT64)), event_date)) OVER w AS enter_date
  FROM `{{params.bq_project}}.store.user_paid_offer_event`
  WHERE event_date >= '2022-01-01'
    AND JSON_VALUE(payload, '$.context.product.id') = 'paywall'
    AND type = 'sub'
    {% if params.full_export|lower != 'true'%}
    AND date(event_date) > DATE_SUB(CURRENT_DATE(), INTERVAL {{params.interval}})
    {% endif %}
  WINDOW w AS (PARTITION BY uuid ORDER BY event_date DESC)
) AS source
ON
    dest.uuid = source.uuid
WHEN MATCHED THEN
  UPDATE SET enter_date = source.enter_date,
    exit_date = null
WHEN NOT MATCHED THEN
  INSERT (uuid, enter_date, exit_date)
  VALUES (source.uuid, source.enter_date, null)
;

-- deal with last unsub from ppm offer
MERGE `{{params.bq_project}}.refined_data.pmc_profile_paid_offer_ppm` AS dest
USING (
    SELECT DISTINCT
      uuid,
      FIRST_VALUE(event_date) OVER w AS exit_date
    FROM `{{params.bq_project}}.store.user_paid_offer_event`
    WHERE event_date >= '2022-01-01'
      AND JSON_VALUE(payload, '$.context.product.id') = 'paywall'
      AND type = 'unsub'
      {% if params.full_export|lower != 'true'%}
      AND date(event_date) > DATE_SUB(CURRENT_DATE(), INTERVAL {{params.interval}})
      {% endif %}
    WINDOW w AS (PARTITION BY uuid ORDER BY event_date DESC)
) AS source
ON
    dest.uuid = source.uuid
    AND source.exit_date > dest.enter_date
WHEN MATCHED THEN
  UPDATE SET exit_date = source.exit_date
;