-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- Generate the table dynamo-user from store_matrix_pmc.profile_active

DROP TABLE IF EXISTS `{{ params.bq_project }}.generated_data.dynamodb-user`;

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.dynamodb-user` (
  pmc_uuid	               STRING       NOT NULL    OPTIONS(description="PMC uuid from $.id in store_matrix_pmc.profile_active.data"),
  created_at	           TIMESTAMP  	            OPTIONS(description="create date from $.createdAt in store_matrix_pmc.profile_active.data"),
  updated_at	           TIMESTAMP   	            OPTIONS(description="update date from $.updatedAt in store_matrix_pmc.profile_active.data"),
  cgu_validation_date	   TIMESTAMP   	            OPTIONS(description="cgu validation date from $.legal.cgu.CGU_PMC_GEN.at in store_matrix_pmc.profile_active.data"),
  deleted	               BOOLEAN	   	            OPTIONS(description="false by default"),
  signup_service      	   STRING	                OPTIONS(description="SignUp Service from $.signup.service in store_matrix_pmc.profile_active.data"),
  signup_device	           STRING	                OPTIONS(description="SignUp Device from $.signup.device in store_matrix_pmc.profile_active.data"),
  signup_date	           TIMESTAMP   	            OPTIONS(description="SignUp date from $.signup.date in store_matrix_pmc.profile_active.data"),
  signup_brand	           STRING	                OPTIONS(description="SignUp brand from $.signup.brand in store_matrix_pmc.profile_active.data"),
  lastActivity_date   	   TIMESTAMP   	          OPTIONS(description="Last activity date from $.lastActivity.date in store_matrix_pmc.profile_active.data"),
  lastActivity_role	       STRING	                OPTIONS(description="Last activity role from $.lastActivity.role in store_matrix_pmc.profile_active.data"),
  lastActivity_brand  	   STRING	                OPTIONS(description="Last activity brand from $.lastActivity.brand in store_matrix_pmc.profile_active.data"),
  lastLogin_date      	   TIMESTAMP   	          OPTIONS(description="Last login date from $.lastLogin.date in store_matrix_pmc.profile_active.data"),
  lastLogin_role      	   STRING	                OPTIONS(description="Last login role from $.lastLogin.role in store_matrix_pmc.profile_active.data"),
  lastLogin_brand     	   STRING	                OPTIONS(description="Last login brand from $.lastLogin.brand in store_matrix_pmc.profile_active.data")
)
PARTITION BY DATE(updated_at)
CLUSTER BY signup_service, lastActivity_role, lastLogin_brand
OPTIONS(description="This table contains all informations on pmc profile activities" ||
                    "\n\n" ||
                    "DAG: {{ dag.dag_id }}" ||
                    "Sync: Daily at 2:01 am");

INSERT INTO `{{ params.bq_project }}.generated_data.dynamodb-user`
SELECT
  -- general info
  uuid        AS pmc_uuid,
  create_date AS created_at,
  update_date AS updated_at,
  create_date AS cgu_validation_date,
  FALSE       AS deleted,
  -- signup
  signup_service.service        AS signup_service,
  signup_service.device         AS signup_device,
  system_data.signup_date       AS signup_date,
  service.source_brand_trigram  AS signup_brand,
  -- last activity
  system_data.last_activity_date  AS last_activity_date,
  system_data.last_activity_role  AS last_activitty_role,
  system_data.last_activity_brand AS last_activity_brand,
  -- last login
  system_data.last_login_date   AS last_login_date,
  system_data.last_login_role   AS last_login_role,
  system_data.last_login_brand  AS last_login_brand
FROM `{{ params.bq_project }}.refined_data.profile_pmc`
WHERE DATE(update_date) >= DATE_SUB(CURRENT_DATE(), INTERVAL 10 YEAR);
