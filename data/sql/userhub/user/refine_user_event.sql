-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{params.bq_project}}.refined_data.user_event` (
    uuid               STRING                 OPTIONS(description="PMC profile uuid"),
    type               STRING                 OPTIONS(description="Type of Event, upsert|delete"),
    event_date         TIMESTAMP  NOT NULL    OPTIONS(description="Event date"),
    payload            STRING                 OPTIONS(description="Payload of Event")
    ) PARTITION BY TIMESTAMP_TRUNC(event_date, DAY) CLUSTER BY type OPTIONS(
                 expiration_timestamp=NULL,
                 description="PMC refined user events, last event by uuid.\n"
                 || "\n\n"
                 || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);

{% if params.full_export|lower == 'true'%}
    TRUNCATE TABLE `{{params.bq_project}}.refined_data.user_event`;
{% endif %}

MERGE `{{params.bq_project}}.refined_data.user_event` dest
USING (
     WITH recent_user_event AS (
        -- get most recent event body from user events by user/event type
        SELECT DISTINCT
            uuid,
            FIRST_VALUE(type) OVER w AS type,
            FIRST_VALUE(event_date) OVER w AS event_date,
            FIRST_VALUE(payload) OVER w AS payload
        FROM `{{params.bq_project}}.store.user_event`
        WHERE ((type = 'upsert'
                -- skip upserts with deleteInfo (created because of a delete event)
                AND JSON_VALUE(payload, '$.deleteInfo') IS NULL
                )
                OR type = 'delete'
            )
        {% if params.full_export|lower != 'true'%}
            AND DATE(event_date) > DATE_SUB(DATE('{{ next_ds }}'), INTERVAL {{params.interval}})
        {% endif %}
        WINDOW w AS (PARTITION BY uuid ORDER BY event_date DESC)
    )
    SELECT
        uuid,
        type,
        event_date,
        payload
    FROM recent_user_event
) source
ON dest.uuid = source.uuid
WHEN MATCHED THEN
UPDATE
    SET payload = source.payload,
        event_date = source.event_date,
        type = source.type
WHEN NOT MATCHED THEN
  INSERT (uuid, type, event_date, payload)
  VALUES (source.uuid, source.type, source.event_date, source.payload)
;