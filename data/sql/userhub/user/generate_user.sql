-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{params.bq_userhub_project}}.refined_data.pmc_profile` (
    uuid                STRING      NOT NULL    OPTIONS(description="PMC uuid"),
    web_id              STRING                  OPTIONS(description="PMC web id, useful for tracking"),
    profile_data             STRUCT <
        email                   STRING    OPTIONS(description="email"),
        firstname               STRING    OPTIONS(description="firstname"),
        lastname                STRING    OPTIONS(description="Lastname"),
        birthdate               DATE      OPTIONS(description="birthdate"),
        gender                  STRING    OPTIONS(description="gender: M|W"),
        phone                   STRING    OPTIONS(description="telephone number"),
        address                 STRING    OPTIONS(description="address"),
        complementary_address   STRING    OPTIONS(description="complementary address"),
        zipcode                 STRING    OPTIONS(description="postal code"),
        city                    STRING    OPTIONS(description="city"),
        country                 STRING    OPTIONS(description="country")
        >                       OPTIONS(description="personnal information"),
    system_data         STRUCT <
        signup_service      STRING      OPTIONS(description="service name used upon signup"),
        signup_source       STRING      OPTIONS(description="source of signup"),
        signup_device       STRING      OPTIONS(description="device used upon signup"),
        signup_date         TIMESTAMP   OPTIONS(description="date of signup"),
        email_verified      BOOL        OPTIONS(description="email is verified"),
        email_verified_at   TIMESTAMP   OPTIONS(description="email verified date"),
        last_login_at       TIMESTAMP   OPTIONS(description="last login date"),
        last_login_service  STRING      OPTIONS(description="last login service"),
        last_login_brand    STRING      OPTIONS(description="last login brand"),
        last_login_role     STRING      OPTIONS(description="last login role"),
        last_activity_at    TIMESTAMP   OPTIONS(description="last activity date"),
        last_activity_brand STRING      OPTIONS(description="last activity brand"),
        last_activity_role  STRING      OPTIONS(description="last activity role"),
        personalized_ads    BOOL        OPTIONS(description="user agreed to receive personalized ADs")
        >                       OPTIONS(description="system information"),
    create_date         TIMESTAMP               OPTIONS(description="create date of profile."),
    update_date         TIMESTAMP               OPTIONS(description="update date of profile"),
    ) OPTIONS(
                 expiration_timestamp=NULL,
                 description="PMC User profile.\n"
                 || "\n\n"
                 || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);

{% if params.full_export|lower != 'false'%}
TRUNCATE TABLE `{{params.bq_userhub_project}}.refined_data.pmc_profile`;
{% endif %}

-- Apply upsert events
MERGE `{{params.bq_userhub_project}}.refined_data.pmc_profile` dest
USING (
    WITH recent_user_event AS (
        SELECT
            uuid,
            payload,
            MIN(TIMESTAMP_MILLIS(CAST(JSON_VALUE(payload, '$.createdAt') AS INT64))) AS create_date,
            MAX(TIMESTAMP_MILLIS(CAST(JSON_VALUE(payload, '$.updatedAt') AS INT64))) AS update_date
        FROM `{{params.bq_userhub_project}}.refined_data.user_event`
        WHERE type = 'upsert'
        {% if params.full_export|lower != 'true'%}
            AND DATE(event_date) > DATE_SUB(DATE('{{ next_ds }}'), INTERVAL {{params.interval}})
        {% endif %}
        GROUP BY 1,2
    )
    SELECT recent_user_event.uuid,
        JSON_VALUE(payload, '$.webId') AS web_id,
        STRUCT(
            JSON_VALUE(payload, '$.email') AS email,
            TRIM(JSON_VALUE(payload, '$.firstname')) AS firstname,
            TRIM(JSON_VALUE(payload, '$.lastname')) AS lastname,
            SAFE_CAST(JSON_VALUE(payload, '$.birthday') AS DATE) AS birthdate,
            JSON_VALUE(payload, '$.gender') AS gender,
            JSON_VALUE(payload, '$.phone') AS phone,
            TRIM(JSON_VALUE(payload, '$.address')) AS address,
            TRIM(JSON_VALUE(payload, '$.address_complement')) AS complementary_address,
            JSON_VALUE(payload, '$.zipcode') AS zipcode,
            TRIM(JSON_VALUE(payload, '$.city')) AS city,
            TRIM(JSON_VALUE(payload, '$.country')) AS country
        ) AS profile_data,
        STRUCT(
            JSON_VALUE(payload, '$.signupService') AS signup_service,
            JSON_VALUE(payload, '$.signupSource') AS signup_source,
            JSON_VALUE(payload, '$.signup.device') AS signup_device,
            TIMESTAMP_MILLIS(CAST(JSON_VALUE(payload, '$.signup.date') AS INT64))  AS signup_date,
            CAST(COALESCE(JSON_VALUE(payload, '$.emailVerified'), 'false') AS BOOL) AS email_verified,
            TIMESTAMP_MILLIS(CAST(JSON_VALUE(payload, '$.emailVerifiedAt') AS INT64)) AS email_verified_at,
            TIMESTAMP_MILLIS(CAST(JSON_VALUE(payload, '$.lastLoginAt') AS INT64)) AS last_login_at,
            JSON_VALUE(payload, '$.lastLoginService') AS last_login_service,
            JSON_VALUE(payload, '$.lastLogin.brand') AS last_login_brand,
            JSON_EXTRACT(payload, '$.lastLogin.role') AS last_login_role,
            TIMESTAMP_MILLIS(CAST(JSON_VALUE(payload, '$.lastActivityAt') AS INT64)) AS last_activity_at,
            JSON_VALUE(payload, '$.lastActivity.brand') AS last_activity_brand,
            JSON_VALUE(payload, '$.lastActivity.role') AS last_activity_role,
            CASE
                WHEN (ppm.uuid IS NOT NULL AND ppm.exit_date IS NULL) THEN FALSE
                ELSE CAST(COALESCE(JSON_VALUE(payload, '$.personalInformationShared'), 'true') AS BOOL)
            END  AS personalized_ads
        ) as system_data,
        create_date,
        update_date
    FROM recent_user_event
    LEFT JOIN `{{ params.bq_userhub_project }}.refined_data.pmc_profile_paid_offer_ppm_live` AS ppm
        ON ppm.uuid = recent_user_event.uuid
) source
ON dest.uuid = source.uuid
WHEN MATCHED THEN
UPDATE
    SET web_id = source.web_id,
        profile_data = source.profile_data,
        system_data = source.system_data,
        create_date = source.create_date,
        update_date = source.update_date
WHEN NOT MATCHED THEN
  INSERT (uuid, web_id, profile_data, system_data, create_date, update_date)
  VALUES (source.uuid, source.web_id, source.profile_data, source.system_data, source.create_date, source.update_date)
;

-- Apply delete events
MERGE `{{params.bq_userhub_project}}.refined_data.pmc_profile` dest
USING (
    WITH recent_deleted_user_event AS (
        -- get recently user events of type "delete"
        SELECT uuid, event_date AS event_date
        FROM `{{params.bq_userhub_project}}.refined_data.user_event`
        WHERE type = 'delete'
        {% if params.full_export|lower != 'true'%}
            AND DATE(event_date) > DATE_SUB(CURRENT_DATE(), INTERVAL {{params.interval}})
        {% endif %}
    )
    SELECT e.uuid
    FROM recent_deleted_user_event AS e
    JOIN `{{params.bq_userhub_project}}.refined_data.pmc_profile` AS p ON e.uuid = p.uuid
    WHERE (e.event_date >= p.create_date AND p.update_date IS NULL)
        OR (e.event_date >= p.update_date)
) source
ON dest.uuid = source.uuid
WHEN MATCHED THEN DELETE
;