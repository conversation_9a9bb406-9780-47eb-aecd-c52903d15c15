-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE start_date DATE DEFAULT DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL {{ params.interval }});
DECLARE end_date DATE DEFAULT DATE("{{ data_interval_end }}");

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store.service_card_event` (
    id                 STRING     NOT NULL    OPTIONS(description="ref : user_activities.flat_event.id"),
    event_date         TIMESTAMP  NOT NULL    OPTIONS(description="Event date"),
    version            STRING                 OPTIONS(description="Event version"),
    uuid               STRING                 OPTIONS(description="PMC profile uuid"),
    services           ARRAY<STRING>          OPTIONS(description="all services represented as an array of json")
    )  PARTITION BY TIMESTAMP_TRUNC(event_date, DAY) OPTIONS(
                 expiration_timestamp=NULL,
                 description="PMC userhub events.\n"
                 || "\n\n"
                 || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);

DELETE FROM `{{ params.bq_project }}.store.service_card_event`
WHERE DATE(event_date) BETWEEN start_date AND end_date;

INSERT INTO `{{ params.bq_project }}.store.service_card_event`
-- Extract service card events from flat_event.
WITH userhub_service_card_events AS (
    SELECT e.id,
        e.event_date,
        e.version,
        JSON_VALUE(content, '$.userId') as uuid,
        JSON_EXTRACT_ARRAY(content, '$.services') AS services
    FROM `{{ params.bq_project }}.user_activities.flat_event` e
    -- filter service card events only
    WHERE content_type = 'service-activity'
    {% if params.full_export|lower != 'true'%}
        -- incremental
        AND DATE(e.event_date) BETWEEN start_date AND end_date
        -- optimize depending on partition
        AND partition_date > DATE_SUB(DATE('{{ next_ds }}'), INTERVAL 10 DAY)
    {% endif %}
), clean_events AS (
    SELECT sce.* EXCEPT (services), service
    FROM userhub_service_card_events AS sce,
        UNNEST(services) AS service
    WHERE JSON_VALUE(service, '$.id') IS NOT NULL
        -- remove un-needed services
        AND JSON_VALUE(service, '$.id') NOT IN ('undefined','nl_free', 'login', 'logout')
        -- remove legacy services (brand::service)
        AND JSON_VALUE(service, '$.id') NOT LIKE '%::%'
        -- remove services with no brand
        AND JSON_VALUE(service, '$.brand') IS NOT NULL
        -- remove services wrong brand
        AND JSON_VALUE(service, '$.brand')  NOT IN ('PMC', 'BMK')
        -- remove anonymous uuid
        AND sce.uuid NOT LIKE 'anonymous%'
)
-- re-aggregate cleaned services by event and uuid
SELECT id, event_date, version, uuid, ARRAY_AGG(service) AS services
FROM clean_events
GROUP BY 1,2,3,4
;