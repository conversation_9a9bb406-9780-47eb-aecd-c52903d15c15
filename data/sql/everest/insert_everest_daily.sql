-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- Create table
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_email_deliverability.everest_daily` (
    export_date                                 DATE      NOT NULL   OPTIONS(description="Date for data extraction from Everest API"),
    report_date                                 DATE      NOT NULL   OPTIONS(description="Report Date as YYYY-MM-DD"),
    ip_address                                  STRING    NOT NULL   OPTIONS(description="IP address"),
	status__microsoft                           STRING               OPTIONS(description="Microsoft status as enum=['enabled','suspended']"),
	status__yahoo                               STRING               OPTIONS(description="Yahoo status as enum=['enabled','suspended']"),
	status__global                              STRING               OPTIONS(description="Global status as enum=['enabled','suspended']"),
	status__safelist                            STRING               OPTIONS(description="Safelist status as enum=['enabled','suspended']"),
	status__aol                                 STRING               OPTIONS(description="AOL status as enum=['enabled','suspended']"),
    microsoft_srd__volume                       INTEGER              OPTIONS(description="Microsoft SRD volume"),
    microsoft_srd__complaints                   INTEGER              OPTIONS(description="Microsoft SRD complaints"),
    microsoft_srd__complaint_rate               FLOAT64              OPTIONS(description="Microsoft SRD complaints rate"),
    microsoft_total__volume                     INTEGER              OPTIONS(description="Microsoft total volume"),
    microsoft_total__complaints                 INTEGER              OPTIONS(description="Microsoft total complaints volume"),
    microsoft_total__complaint_rate             FLOAT64              OPTIONS(description="Microsoft total complaints rate"),
    microsoft_inbox__volume                     INTEGER              OPTIONS(description="Microsoft total volume"),
    microsoft_inbox__complaints                 INTEGER              OPTIONS(description="Microsoft total complaints volume"),
    microsoft_inbox__complaint_rate             FLOAT64              OPTIONS(description="Microsoft total complaints rate"),
    yahoo_total__volume                         INTEGER              OPTIONS(description="Yahoo Inbox volume"),
	yahoo_total__complaints                     INTEGER              OPTIONS(description="Yahoo Inbox complaints volume"),
	yahoo_total__complaint_rate                 FLOAT64              OPTIONS(description="Yahoo Inbox complaints rate"),
	yahoo_inbox__volume                         INTEGER              OPTIONS(description="Yahoo Inbox volume"),
	yahoo_inbox__complaints                     INTEGER              OPTIONS(description="Yahoo Inbox complaints volume"),
	yahoo_inbox__complaint_rate                 FLOAT64              OPTIONS(description="Yahoo Inbox complaints rate"),
	rp_network1_traps__count                    INTEGER              OPTIONS(description="Count of rp network traps"),
	cloudmark_traps__count                      INTEGER              OPTIONS(description="Count of cloudmark traps"),
	critical_traps__count                       INTEGER              OPTIONS(description="Count of critical traps"),
	significant_traps__count                    INTEGER              OPTIONS(description="Count of significant traps"),
	comcast_total__volume                       INTEGER              OPTIONS(description="Comcast total volume"),
	comcast__volume                             INTEGER              OPTIONS(description="Comcast volume"),
	comcast__complaints                         INTEGER              OPTIONS(description="Comcast complaints volume"),
	comcast__complaint_rate                     FLOAT64              OPTIONS(description="Comcast complaints rate"),
    unknown_users__volume                       INTEGER              OPTIONS(description=""),
    unknown_users__complaints                   INTEGER              OPTIONS(description=""),
    unknown_users__complaint_rate               FLOAT64              OPTIONS(description=""),
    orange__complaints                          INTEGER              OPTIONS(description=""),
    sfr__complaints                             INTEGER              OPTIONS(description=""),
    spamcop__volume                             INTEGER              OPTIONS(description=""),
    vade_secure__volume                         INTEGER              OPTIONS(description=""),
    vade_secure__complaints                     INTEGER              OPTIONS(description=""),
    vade_secure__spam_count                     INTEGER              OPTIONS(description=""),
    aol_delivered__volume                       INTEGER              OPTIONS(description=""),
    aol_delivered__complaints                   INTEGER              OPTIONS(description=""),
    aol_delivered__complaint_rate               FLOAT64              OPTIONS(description="")
)
OPTIONS(
    expiration_timestamp=NULL,
    description="Everest Validity daily data from Everest API.\n"
             || "\n\n"
             || "Daily updates thru the Airflow DAG {{ dag.dag_id }}\n"
             || "Sync: Daily."
);


-- Import data from import dataset to store

{% if params.full_export != "True" %} -- import incremental data

DELETE FROM `{{ params.bq_project }}.store_email_deliverability.everest_daily`
WHERE DATE(report_date) >= DATE_SUB(CURRENT_DATE(), INTERVAL {{ params.interval }});

INSERT INTO `{{ params.bq_project }}.store_email_deliverability.everest_daily`
SELECT
    export_date,
    report_date,
    ip_address,
	status__microsoft,
	status__yahoo,
	status__global,
	status__safelist,
	status__aol,
    microsoft_srd__volume,
    microsoft_srd__complaints,
    microsoft_srd__complaint_rate,
    microsoft_total__volume,
    microsoft_total__complaints,
    microsoft_total__complaint_rate,
    microsoft_inbox__volume,
    microsoft_inbox__complaints,
    microsoft_inbox__complaint_rate,
    yahoo_total__volume,
	yahoo_total__complaints,
	yahoo_total__complaint_rate,
	yahoo_inbox__volume,
	yahoo_inbox__complaints,
	yahoo_inbox__complaint_rate,
	rp_network1_traps__count,
	cloudmark_traps__count,
	critical_traps__count,
	significant_traps__count,
	comcast_total__volume,
	comcast__volume,
	comcast__complaints,
	comcast__complaint_rate,
    unknown_users__volume,
    unknown_users__complaints,
    unknown_users__complaint_rate,
    orange__complaints,
    sfr__complaints,
    spamcop__volume,
    vade_secure__volume,
    vade_secure__complaints,
    vade_secure__spam_count,
    aol_delivered__volume,
    aol_delivered__complaints,
    aol_delivered__complaint_rate
FROM `{{ params.bq_project }}.import.everest_daily_{{ next_ds_nodash }}`;

{% else %} -- import full data
TRUNCATE TABLE `{{ params.bq_project }}.store_email_deliverability.everest_daily`;

INSERT INTO `{{ params.bq_project }}.store_email_deliverability.everest_daily`
SELECT
    export_date,
    report_date,
    ip_address,
	status__microsoft,
	status__yahoo,
	status__global,
	status__safelist,
	status__aol,
    microsoft_srd__volume,
    microsoft_srd__complaints,
    microsoft_srd__complaint_rate,
    microsoft_total__volume,
    microsoft_total__complaints,
    microsoft_total__complaint_rate,
    microsoft_inbox__volume,
    microsoft_inbox__complaints,
    microsoft_inbox__complaint_rate,
    yahoo_total__volume,
	yahoo_total__complaints,
	yahoo_total__complaint_rate,
	yahoo_inbox__volume,
	yahoo_inbox__complaints,
	yahoo_inbox__complaint_rate,
	rp_network1_traps__count,
	cloudmark_traps__count,
	critical_traps__count,
	significant_traps__count,
	comcast_total__volume,
	comcast__volume,
	comcast__complaints,
	comcast__complaint_rate,
    unknown_users__volume,
    unknown_users__complaints,
    unknown_users__complaint_rate,
    orange__complaints,
    sfr__complaints,
    spamcop__volume,
    vade_secure__volume,
    vade_secure__complaints,
    vade_secure__spam_count,
    aol_delivered__volume,
    aol_delivered__complaints,
    aol_delivered__complaint_rate
FROM `{{ params.bq_project }}.import.everest_daily_{{ next_ds_nodash }}`;

{% endif %}