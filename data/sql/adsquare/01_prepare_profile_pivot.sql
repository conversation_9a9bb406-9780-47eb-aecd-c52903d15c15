-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

--111 all_profil_liked_with_thematics
--Next step we group (pmi with postal code with tuple (critere, modalite, segment) ,
-- we also specify if the postal code is valid or not)
DROP TABLE IF EXISTS `{{ params.bq_project }}.temp.mapping_profile_cp_theme`;
CREATE TABLE `{{ params.bq_project }}.temp.mapping_profile_cp_theme`(
    profile_master_id         INT64  NOT NULL    OPTIONS(description="profile_master_id"),
    postal_code_cleaned        STRING     OPTIONS(description="postal code cleaned, it can be non valid"),
    zipcode_valid         BOOL  NOT NULL    OPTIONS(description="boolean to check if is valid zipcode"),
    critere           STRING  NOT NULL    OPTIONS(description="critere"),
    modalite           STRING  NOT NULL    OPTIONS(description="modalite"),
    segment           STRING  NOT NULL    OPTIONS(description="segment")
) OPTIONS(
  expiration_timestamp=NULL,
  description="table used to create a mapping profile with cp and theme.\n"
         --     || "Daily updates (at 2am) thru the Airflow DAG '{{ dag.dag_id }}'."
);

INSERT INTO `{{ params.bq_project }}.temp.mapping_profile_cp_theme`
WITH consents_theme AS (
  -- we select all consent which have a thematic already (1consent = 1theme)
  SELECT
        eb.consent_id as email_consent_id,
        eb.consent_public_ref,
        REGEXP_REPLACE(kt.thematic,'[^a-z0-9]','_') AS thematic
  FROM `{{ params.bq_project }}.refined_data.email_base` as eb
  JOIN `{{ params.bq_project }}.store_karinto.email_consent_theme` AS kt ON eb.consent_public_ref = kt.public_ref
  WHERE (eb.consent_type = 'nl' OR eb.consent_type = 'part')
),active_consent AS (
    -- For all profil sub to any consents, we match their thematics 
    -- and take some personal info for the segment gender and age
    SELECT 
        pd.id.email_profile_master_id as profile_master_id,
        pd.info.enriched.age as age,
        CASE 
            WHEN pd.info.gender = "F" THEN 'Femme'
            WHEN pd.info.gender = "M" THEN 'Homme'
            ELSE 'no civility'
        END AS civility,
        --info.zipcode as postal_code,
        trim( REGEXP_REPLACE(COALESCE(pd.info.zipcode,cp.postal_code), r'[^\d]+', '')) as postal_code_cleaned,
        -- boolean to know if postal code is valide
        CASE 
            WHEN ((length(trim(REGEXP_REPLACE(COALESCE(pd.info.zipcode,cp.postal_code), r'[^\d]+', ''))) = 5) or left(COALESCE(pd.info.zipcode,cp.postal_code), 2) in ('2A', '2B'))
                            and trim( REGEXP_REPLACE(COALESCE(pd.info.zipcode,cp.postal_code), r'[^\d]+', '')) != '00000'
                   THEN true
            ELSE false
        END as zipcode_valid,
        active_consents,
        ct.thematic
    FROM `{{ params.bq_project }}.business_data.profile_digital_360` as pd, unnest(service.email.active.consents) as active_consents
    LEFT JOIN `{{ params.bq_project }}.store_matrix_email.consolidated_profile` as cp on cp.profile_master_id = pd.id.email_profile_master_id
    JOIN consents_theme AS ct ON active_consents = ct.consent_public_ref
    -- We take only person with consnet sub
    WHERE ARRAY_LENGTH(service.email.active.consents) > 0
), subs_thematics_zipcode_valid AS (
    -- we first select all thematics from consent 
    SELECT 
        profile_master_id,postal_code_cleaned,zipcode_valid,thematic
    FROM active_consent
    GROUP BY 1,2,3,4 -- for unicity

    -- We do an union all to add theme from personal info like gender and age 
    UNION ALL
    
    SELECT
        ac.profile_master_id,
        ac.postal_code_cleaned,
        zipcode_valid,
        CASE    
                when ac.age between 15 and 18 then '0_18ans'
                when ac.age between 19 and 25 then '19_25ans'
                when ac.age between 26 and 35 then '26_35ans'
                when ac.age between 36 and 45 then '36_45ans'
                when ac.age between 46 and 55 then '46_55ans'
                when ac.age between 56 and 65 then '55_65ans'
                when ac.age between 66 and 130 then 'plus_65ans'
                else 'age_missing'
        END as thematic 
    FROM active_consent AS ac
    GROUP BY 1,2,3,4 -- to keep unicity

    UNION ALL
      
    SELECT
        ac.profile_master_id,
        ac.postal_code_cleaned,
        zipcode_valid,
        CASE 
            when civility = 'Homme' then 'homme'
            when civility = 'Femme' then 'femme'
            else  'civility_missing'
        END as thematic 
    FROM active_consent AS ac
    GROUP BY 1,2,3,4 -- to keep unicity
 
)

-- WE have all matching even if the zipcode is not correct
SELECT 
    st.profile_master_id,
    st.postal_code_cleaned,
    zipcode_valid,
    adc.critere,
    adc.modalite,
    adc.segment
FROM subs_thematics_zipcode_valid as st
LEFT JOIN `{{ params.bq_project }}.temp.adsquare_matching_theme` as adc ON adc.theme = st.thematic
GROUP BY 1,2,3,4,5,6 -- to keep unicity


