-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- first step we have to create a matching table for theme VS le tuple (critere,modalite,segment)
DROP TABLE IF EXISTS `{{ params.bq_project }}.temp.adsquare_matching_theme`;
CREATE TABLE `{{ params.bq_project }}.temp.adsquare_matching_theme` (
    critere       STRING  NOT NULL    OPTIONS(description="critere"),
    modalite      STRING  NOT NULL    OPTIONS(description="modalite"),
    segment       STRING  NOT NULL    OPTIONS(description="segment"),
    theme       STRING  NOT NULL    OPTIONS(description="theme")
) OPTIONS(
  expiration_timestamp=NULL,
  description="table used to create a mapping fro adsquare.\n"
         --     || "Daily updates (at 2am) thru the Airflow DAG '{{ dag.dag_id }}'."
);

INSERT INTO `{{ params.bq_project }}.temp.adsquare_matching_theme`
SELECT "Genre", "Femme", "ds_femme", "femme" UNION ALL 
SELECT "Genre", "Homme", "ds_homme", "homme" UNION ALL 
SELECT "Genre", "no civility", "ds_civility_missing", "civility_missing" UNION ALL 
SELECT "Categorie Age", "+ 65 ans", "ds_plus_65ans", "plus_65ans" UNION ALL 
SELECT "Categorie Age", "0-18 ans", "ds_0_18ans", "0_18ans" UNION ALL 
SELECT "Categorie Age", "Age non renseigné", "ds_age_missing", "age_missing" UNION ALL 
SELECT "Categorie Age", "26-35 ans", "ds_26_35ans", "26_35ans" UNION ALL 
SELECT "Categorie Age", "36-45 ans", "ds_36_45ans", "36_45ans" UNION ALL 
SELECT "Categorie Age", "46-55 ans", "ds_46_55ans", "46_55ans" UNION ALL 
SELECT "Categorie Age", "19-25 ans", "ds_19_25ans", "19_25ans" UNION ALL 
SELECT "Categorie Age", "56-65 ans", "ds_55_65ans", "55_65ans" UNION ALL 
SELECT "Thématique", "Culture", "ds_culture", "culture" UNION ALL 
SELECT "Thématique", "Finance", "ds_finance", "finance" UNION ALL 
SELECT "Thématique", "Male", "ds_male", "male" UNION ALL 
SELECT "Thématique", "Environment", "ds_environment", "environment" UNION ALL 
SELECT "Thématique", "Sport", "ds_sport", "sport" UNION ALL 
SELECT "Thématique", "Home", "ds_home", "home" UNION ALL 
SELECT "Thématique", "Cinema", "ds_cinema", "cinema" UNION ALL 
SELECT "Thématique", "TV", "ds_tv", "tv" UNION ALL 
SELECT "Thématique", "Fashion", "ds_fashion", "fashion" UNION ALL 
SELECT "Thématique", "Professional Carrer", "ds_professional_carrer", "professional_carreer" UNION ALL 
SELECT "Thématique", "Food", "ds_food", "food" UNION ALL 
SELECT "Thématique", "Health", "ds_health", "health" UNION ALL 
SELECT "Thématique", "Senior", "ds_senior", "senior" UNION ALL 
SELECT "Thématique", "Astrology", "ds_astrology", "astrology" UNION ALL 
SELECT "Thématique", "Travel", "ds_travel", "travel" UNION ALL 
SELECT "Thématique", "People", "ds_people", "people" UNION ALL 
SELECT "Thématique", "Automobile", "ds_auto", "automobile" UNION ALL 
SELECT "Thématique", "Busines", "ds_busines", "busines" UNION ALL 
SELECT "Thématique", "Game Hobbies", "ds_game_hobbies", "games_hobbies" UNION ALL 
SELECT "Thématique", "Feminine", "ds_feminine", "feminine" UNION ALL 
SELECT "Thématique", "Beauty", "ds_beauty", "beauty" UNION ALL 
SELECT "Thématique", "Lifestyle", "ds_lifestyle", "lifestyle"