-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

----
-- TRUNCATE CONSOLIDATED PROFILE TABLE
----
TRUNCATE TABLE matrix__email.consolidated_profile;


----
-- STORE CONSOLIDATED PROFILE FROM IMPORT BQ DATA TABLE
----
INSERT INTO matrix__email.consolidated_profile
    (profile_master_id, firstname, lastname, gender, birthdate, address_line1,
     address_line2, postal_code, city, country, create_date, update_date)
SELECT DISTINCT ON (profile_master_id)
  profile_master_id,
  firstname,
  lastname,
  gender,
  CASE
    WHEN birthdate <> '' THEN TO_DATE(birthdate, 'YYYY-MM-DD')
    ELSE NULL
  END
  AS birthdate,
  address_line1,
  address_line2,
  postal_code,
  city,
  country,
  CAST(cp.create_date AS TIMESTAMP with time zone) AS create_date,
  CAST(cp.update_date AS TIMESTAMP with time zone) AS update_date
FROM {{ params.import_consolidated_table }} AS cp
JOIN matrix__email.profile_master_id AS pmi ON pmi.id = cp.profile_master_id
ORDER BY profile_master_id, cp.update_date DESC;
