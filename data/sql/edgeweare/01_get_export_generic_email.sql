-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
SELECT
email,
email_md5,
email_sha256,
ANY_VALUE(gender) AS gender, 
ANY_VALUE(lastname) AS lastname,
ANY_VALUE(firstname) AS firstname,
ANY_VALUE(birthdate) AS birthday,
ANY_VALUE(ip_address) AS ip_address,
ANY_VALUE(REPLACE(adress1,'\\N','')) AS address1,
ANY_VALUE(REPLACE(adress2,'\\N','')) AS address2,
ANY_VALUE(REPLACE(zipcode,'\\N','')) AS zipcode,
ANY_VALUE(REPLACE(town,'\\N','')) AS town,
ANY_VALUE(REPLACE(country,'\\N','')) AS country,
ANY_VALUE(REPLACE(department,'\\N','')) AS department,
ANY_VALUE(last_update) AS last_update,
ANY_VALUE(subscription_date) AS subscription_date,
ANY_VALUE(ec.public_ref) AS header,
'0' AS Optin_Adress,
'1' AS Optin_DDN,
'1' AS Optin_Email,
'0' AS Optin_Phone,
'0' AS Optin_Mobile

FROM `pm-prod-matrix.store_matrix_email.generic_export` AS ge
JOIN `pm-prod-matrix.store_karinto.email_consent` AS ec ON ec.id = ge.email_consent_id
WHERE ge.status='sub'
AND ec.type='part'
AND ec.id NOT IN (100,102)
AND ec.brand_id IN (SELECT id FROM `pm-prod-matrix.store_karinto.brand` WHERE owner_id IN(1,2)) 

GROUP BY
email,
email_md5,
email_sha256