-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

WITH remaining_days_definitions AS (
    SELECT
        id,
        universe,
        created_by,
        status,
        type,
        expire_at,
        name,
        description,
        created_at,
        DATE_PART('day', expire_at - NOW()) AS remaining_days
    FROM picasso.segment_definition
    WHERE expire_at < NOW()
)
SELECT id, name, created_by, status, type, universe, expire_at, description, remaining_days
FROM remaining_days_definitions
WHERE status LIKE 'PUBLISHED'
ORDER BY remaining_days ASC;
