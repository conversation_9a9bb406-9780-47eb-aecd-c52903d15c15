-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.{{params.store_segment_dataset}}.segment-sha256_{{params.segment_definition.name|lower}}` (
  email_sha256          STRING    NOT NULL     OPTIONS(description="email_sha256 from business_data.profile_digital_360"),
  update_date           DATE      NOT NULL     OPTIONS(description="segment last update date ")
)

OPTIONS(description="Segment created from segment definition on segment manager with description: " ||
                    " \n" ||
                    "{{params.segment_definition.description}}" ||
                    " \n" ||
                    "DAG: {{dag.dag_id}}" ||
                    " \n" ||
                    "Sync.: {{params.segment_definition.frequency|capitalize}}");

TRUNCATE TABLE `{{ params.bq_project }}.{{params.store_segment_dataset}}.segment-sha256_{{params.segment_definition.name|lower}}`;

INSERT INTO `{{ params.bq_project }}.{{params.store_segment_dataset}}.segment-sha256_{{params.segment_definition.name|lower}}`
SELECT DISTINCT
  id.email_sha256  AS email_sha256,
  CURRENT_DATE()   AS update_date
FROM `{{ params.bq_project }}.business_data.profile_digital_360`
WHERE id.email_profile_master_id IS NOT NULL AND ( {{params.segment_definition.sql_definition}} );
