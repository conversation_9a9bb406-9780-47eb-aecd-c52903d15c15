-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

SELECT id,
       name,
       category,
       theme,
       type,
       tags,
       CASE WHEN active IS TRUE
            THEN 't'
            ELSE 'f'
        END AS active,
       CASE WHEN nb_urls IS NULL
            THEN 0
            ELSE nb_urls
        END AS nb_urls,
       CASE WHEN nb_visitors_3m IS NULL
            THEN 0
            ELSE nb_visitors_3m
        END AS nb_visitors_3m,
       create_date,
       update_date
  FROM `{{ params.bq_project }}.store_picasso.contextual_segment`;
