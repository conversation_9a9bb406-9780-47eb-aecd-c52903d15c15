-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
-- Purpose: Compute differences between current state and requested changes
-- Dependencies: daily_segment_* tables must be populated from Google Sheets

-- resetting actions table
--
TRUNCATE TABLE `{{ params.bq_project }}.store_picasso.segment_actions`;

-- =============================================================================
-- SECTION 1: Process DELETE requests
-- =============================================================================
-- dealing with deleted segments
--
INSERT INTO `{{ params.bq_project }}.store_picasso.segment_actions`
(id, name, short_description, query_id, query_language, query_text, action)
SELECT sd.id, NULL, NULL, sd.query_id, NULL, NULL, 'delete'
  FROM `{{ params.bq_project }}.store_picasso.daily_segment_deletes` AS sd
  LEFT JOIN `{{ params.bq_project }}.store_picasso.segment_queries` AS sq
    ON sq.id = sd.id
  LEFT JOIN `{{ params.bq_project }}.store_picasso.segment_action_snapshot` sa
    ON sa.id = sd.id
 WHERE sd.name IS NOT NULL
   AND sd.short_description IS NOT NULL
   AND sd.query_id IS NOT NULL
   AND sd.query_language IS NOT NULL
   AND sd.query_text IS NOT NULL
   -- Only process if not already processed (not in snapshot)
   AND sa.id IS NULL;

-- =============================================================================
-- SECTION 2: Process UPDATE requests
-- =============================================================================
-- dealing with updated segments
--
INSERT INTO `{{ params.bq_project }}.store_picasso.segment_actions`
(id, name, short_description, query_id, query_language, query_text, action)
SELECT su.id, su.name, su.short_description, su.query_id, su.query_language, su.query_text, 'update'
  FROM `{{ params.bq_project }}.store_picasso.daily_segment_updates` AS su
  JOIN `{{ params.bq_project }}.store_picasso.segment_queries` AS sq
    ON sq.id = su.id
  LEFT JOIN `{{ params.bq_project }}.store_picasso.segment_action_snapshot` sa
    ON sa.id = su.id
 WHERE (sq.name != su.name
    OR sq.short_description != su.short_description
    OR (sq.short_description IS NULL AND su.short_description IS NOT NULL)
    OR sq.query_id != su.query_id
    OR sq.query_language != su.query_language
    OR sq.query_text != su.query_text)
   AND sa.id IS NULL;

-- =============================================================================
-- SECTION 3: Process CREATE requests
-- =============================================================================
-- dealing with new segments
--
INSERT INTO `{{ params.bq_project }}.store_picasso.segment_actions`
(id, name, short_description, query_id, query_language, query_text, action)
SELECT NULL, sc.name, sc.short_description, NULL, sc.query_language, sc.query_text, 'create'
  FROM `{{ params.bq_project }}.store_picasso.daily_segment_creates` AS sc
  LEFT JOIN `{{ params.bq_project }}.store_picasso.segment_queries` AS sq
    ON sq.name = sc.name
  LEFT JOIN `{{ params.bq_project }}.store_picasso.segment_action_snapshot` sa
    ON sa.name = sc.name
 WHERE sc.name IS NOT NULL
   AND sc.query_language IS NOT NULL
   AND sc.query_text IS NOT NULL
   AND sa.name IS NULL
   AND sq.id IS NULL;

-- =============================================================================
-- SECTION 4: Clean up already processed actions
-- =============================================================================
-- dealing with already pushed actions
DELETE FROM `{{ params.bq_project }}.store_picasso.segment_actions` sa
 WHERE sa.id IN (
       SELECT s.id
         FROM `{{ params.bq_project }}.store_picasso.segment_action_snapshot` s
        WHERE s.action = 'delete'
          AND s.id IS NOT NULL
          AND s.query_id IS NULL
 );

DELETE FROM `{{ params.bq_project }}.store_picasso.segment_actions` sa
 WHERE sa.id IN (
       SELECT s.id
         FROM `{{ params.bq_project }}.store_picasso.segment_action_snapshot` s
        WHERE s.action = 'create'
          AND s.id IS NOT NULL
);

DELETE FROM `{{ params.bq_project }}.store_picasso.segment_actions` sa
 WHERE sa.id IN (
       SELECT a.id
         FROM `{{ params.bq_project }}.store_picasso.segment_action_snapshot` s
         JOIN `{{ params.bq_project }}.store_picasso.segment_actions` a
           ON a.id = s.id
        WHERE s.action = 'update'
          AND a.name = s.name
          AND a.short_description = s.short_description
          AND a.query_id = s.query_id
          AND a.query_language = s.query_language
          AND a.query_text = s.query_text
);
