-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- Create segment_actions table
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_picasso.segment_actions` (
    id                INTEGER              OPTIONS(description="ID of segment"),
    name              STRING               OPTIONS(description="Segment name"),
    short_description STRING               OPTIONS(description="Short description of segment"),
    query_id          INTEGER              OPTIONS(description="Query ID related to the segment"),
    query_language    STRING               OPTIONS(description="Language used to create the query"),
    query_text        STRING               OPTIONS(description="Query in full text"),
    action            STRING  NOT NULL     OPTIONS(description="Action to perform: create, update, delete")
) OPTIONS(
  expiration_timestamp=NULL,
  description="Temporary table to store segment actions to be processed.\n"
              || "\n\n"
              || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);

-- Create daily_segment_updates table (populated from Google Sheets)
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_picasso.daily_segment_updates` (
    id                INTEGER              OPTIONS(description="ID of segment"),
    name              STRING               OPTIONS(description="Segment name"),
    short_description STRING               OPTIONS(description="Short description of segment"),
    query_id          INTEGER              OPTIONS(description="Query ID related to the segment"),
    query_language    STRING               OPTIONS(description="Language used to create the query"),
    query_text        STRING               OPTIONS(description="Query in full text")
) OPTIONS(
  expiration_timestamp=NULL,
  description="Daily segment updates from Google Sheets.\n"
              || "\n\n"
              || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);

-- Create daily_segment_creates table (populated from Google Sheets)
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_picasso.daily_segment_creates` (
    id                INTEGER              OPTIONS(description="ID of segment"),
    name              STRING               OPTIONS(description="Segment name"),
    short_description STRING               OPTIONS(description="Short description of segment"),
    query_id          INTEGER              OPTIONS(description="Query ID related to the segment"),
    query_language    STRING               OPTIONS(description="Language used to create the query"),
    query_text        STRING               OPTIONS(description="Query in full text")
) OPTIONS(
  expiration_timestamp=NULL,
  description="Daily segment creates from Google Sheets.\n"
              || "\n\n"
              || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);

-- Create daily_segment_deletes table (populated from Google Sheets)
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_picasso.daily_segment_deletes` (
    id                INTEGER              OPTIONS(description="ID of segment"),
    name              STRING               OPTIONS(description="Segment name"),
    short_description STRING               OPTIONS(description="Short description of segment"),
    query_id          INTEGER              OPTIONS(description="Query ID related to the segment"),
    query_language    STRING               OPTIONS(description="Language used to create the query"),
    query_text        STRING               OPTIONS(description="Query in full text")
) OPTIONS(
  expiration_timestamp=NULL,
  description="Daily segment deletes from Google Sheets.\n"
              || "\n\n"
              || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);

-- Create picasso_segment_update table (dispatch table for updates)
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_picasso.picasso_segment_update` (
    id                INTEGER              OPTIONS(description="ID of segment"),
    name              STRING               OPTIONS(description="Segment name"),
    short_description STRING               OPTIONS(description="Short description of segment"),
    query_id          INTEGER              OPTIONS(description="Query ID related to the segment"),
    query_language    STRING               OPTIONS(description="Language used to create the query"),
    query_text        STRING               OPTIONS(description="Query in full text")
) OPTIONS(
  expiration_timestamp=NULL,
  description="Segments to be updated via API.\n"
              || "\n\n"
              || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);

-- Create picasso_segment_create table (dispatch table for creates)
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_picasso.picasso_segment_create` (
    id                INTEGER              OPTIONS(description="ID of segment"),
    name              STRING               OPTIONS(description="Segment name"),
    short_description STRING               OPTIONS(description="Short description of segment"),
    query_id          INTEGER              OPTIONS(description="Query ID related to the segment"),
    query_language    STRING               OPTIONS(description="Language used to create the query"),
    query_text        STRING               OPTIONS(description="Query in full text")
) OPTIONS(
  expiration_timestamp=NULL,
  description="Segments to be created via API.\n"
              || "\n\n"
              || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);

-- Create picasso_segment_delete table (dispatch table for deletes)
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_picasso.picasso_segment_delete` (
    id                INTEGER              OPTIONS(description="ID of segment"),
    name              STRING               OPTIONS(description="Segment name"),
    short_description STRING               OPTIONS(description="Short description of segment"),
    query_id          INTEGER              OPTIONS(description="Query ID related to the segment"),
    query_language    STRING               OPTIONS(description="Language used to create the query"),
    query_text        STRING               OPTIONS(description="Query in full text")
) OPTIONS(
  expiration_timestamp=NULL,
  description="Segments to be deleted via API.\n"
              || "\n\n"
              || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);
