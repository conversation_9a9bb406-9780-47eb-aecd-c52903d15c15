-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

{% if params.check_type == 'initial' %}
-- Check if there are any changes to process
WITH counts AS (
    SELECT COUNT(*) AS nb_rows
      FROM `{{ params.bq_project }}.store_picasso.daily_segment_updates`
     WHERE name IS NOT NULL
     UNION ALL
    SELECT COUNT(*) AS nb_rows
      FROM `{{ params.bq_project }}.store_picasso.daily_segment_creates`
     WHERE name IS NOT NULL
     UNION ALL
    SELECT COUNT(*) AS nb_rows
      FROM `{{ params.bq_project }}.store_picasso.daily_segment_deletes`
     WHERE id IS NOT NULL
)
SELECT SUM(nb_rows) AS total_changes
  FROM counts;

{% elif params.check_type == 'execution' %}
-- Check if computed actions are ready for execution
SELECT COUNT(*) AS nb_actions
  FROM `{{ params.bq_project }}.store_picasso.segment_actions`;

{% endif %}
