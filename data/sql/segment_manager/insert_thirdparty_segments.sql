-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_picasso.thirdparty_segment` (
    id                          INTEGER     NOT NULL OPTIONS(description="Google AdManager segment identifier"),
    ref                         STRING      NOT NULL OPTIONS(description="Google AdManager segment full name (reference)	"),
    name                        STRING      NOT NULL OPTIONS(description="Deducted name by splitting ref field"),
    source                      STRING      NOT NULL OPTIONS(description="Data Provider of the segment"),
    vertical                    STRING               OPTIONS(description="Deducted vertical by splitting ref field"),
    type                        STRING               OPTIONS(description="Deducted type by splitting ref field"),
    description                 STRING               OPTIONS(description="Google AdManager segment description"),
    size                        INTEGER              OPTIONS(description="Google AdManager segment size"),
    mobile_web_size             INTEGER              OPTIONS(description="Google AdManager segment mobile size"),
    idfa_size                   INTEGER              OPTIONS(description="Google AdManager segment IDFA size"),
    adid_size                   INTEGER              OPTIONS(description="Google AdManager segment Ad ID size"),
    ppid_size                   INTEGER              OPTIONS(description="Google AdManager segment PPID size"),
    cost                        STRING               OPTIONS(description="Google AdManager segment cost")
) OPTIONS(
            expiration_timestamp=NULL,
            description="Google AdManager Third Party Segments.\n"
            || "\n\n"
            || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);

TRUNCATE TABLE `{{ params.bq_project }}.store_picasso.thirdparty_segment`;

INSERT INTO `{{ params.bq_project }}.store_picasso.thirdparty_segment`
(id,
 ref,
 name,
 source,
 vertical,
 type,
 description,
 size,
 mobile_web_size,
 idfa_size,
 adid_size,
 ppid_size,
 cost
)
SELECT DISTINCT id,
                ref,
                name,
                source,
                vertical,
                type,
                description,
                size,
                mobile_web_size,
                idfa_size,
                adid_size,
                ppid_size,
                cost
  FROM `{{ params.bq_project }}.import.picasso_thirdparty_segment_{{ next_ds_nodash }}`;
