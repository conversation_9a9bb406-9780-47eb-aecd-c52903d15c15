-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `store_picasso.segmentation_tags_info` (
    segment_name        STRING     NOT NULL             OPTIONS(description="ref: segmentation_tag_snapshot.segment_name"),
    create_date         DATE       NOT NULL             OPTIONS(description="creation date of the segment table"),
    update_date         DATE                            OPTIONS(description="last update date of the segment table")
) OPTIONS(
            expiration_timestamp=NULL,
            description="Segmentation Tags meta information.\n"
            || "\n\n"
            || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);

MERGE `store_picasso.segmentation_tags_info` dest
USING (
    WITH segments_snapshots AS (
        SELECT segment_name, MIN(snapshot_date) AS create_date, MAX(snapshot_date) AS update_date
        FROM `generated_data.segmentation_tag_snapshot`,
            UNNEST(segment_names) AS segment_name
        GROUP BY 1
    ), segments_info AS (
        SELECT
            SUBSTR(table_id, 9) AS segment_name,
            DATE(TIMESTAMP_MILLIS(CAST (creation_time AS INT64))) AS create_date,
            DATE(TIMESTAMP_MILLIS(CAST (last_modified_time AS INT64))) AS update_date,
        FROM `store_email_segment`.__TABLES__
        WHERE table_id  LIKE 'segment%'
    )
    SELECT s.segment_name,
        COALESCE(LEAST(s.create_date, i.create_date), s.create_date) AS create_date,
        COALESCE(LEAST(s.update_date, i.update_date), s.update_date) AS update_date,
    FROM segments_snapshots AS s
    -- left join as we do not have access on __TABLES__ of segments in other projects
    LEFT JOIN segments_info AS i ON s.segment_name = i.segment_name
) source
ON dest.segment_name = source.segment_name
WHEN MATCHED THEN
UPDATE
    SET update_date = source.update_date
WHEN NOT MATCHED THEN
  INSERT (segment_name, create_date, update_date)
  VALUES (source.segment_name, source.create_date, source.update_date)
;