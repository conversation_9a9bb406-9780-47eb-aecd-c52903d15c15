-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- Update tag_universe
----- Merge into tag_universe
MERGE INTO `{{ params.bq_project }}.store_email_segment.tag_universe`  AS target
USING `{{ params.bq_project }}.store_email_segment.tag_universe_buffer` AS source
ON target.tag = source.tag
WHEN MATCHED THEN
    UPDATE SET target.universe_ids = source.universe_ids
WHEN NOT MATCHED BY TARGET THEN
    INSERT (tag, universe_ids) VALUES (source.tag, source.universe_ids);

-- Update tag_platform
----- Remove duplication
CREATE OR REPLACE TABLE `{{ params.bq_project }}.store_email_segment.tag_platform_buffer` AS
SELECT
  tag,
  ARRAY_AGG(STRUCT(brands) LIMIT 1)[OFFSET(0)].*
FROM `{{ params.bq_project }}.store_email_segment.tag_platform_buffer`
GROUP BY tag;
----- Merge into tag_platform
MERGE INTO `{{ params.bq_project }}.store_email_segment.tag_platform` AS target
USING `{{ params.bq_project }}.store_email_segment.tag_platform_buffer` AS source
ON target.tag = source.tag
WHEN MATCHED THEN
    UPDATE SET target.brands = source.brands
WHEN NOT MATCHED BY TARGET THEN
    INSERT (tag, brands) VALUES (source.tag, source.brands);

