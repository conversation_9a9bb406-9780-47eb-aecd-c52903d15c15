-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- Create splio tag_universe table if not exist
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_email_segment.tag_universe` (
  tag STRING OPTIONS(description="Segmentation Tag name"),
  universe_ids ARRAY<INT64> OPTIONS(description="Spilio universe ids as list")
)
OPTIONS(
  description="Segment/Splio universes Mapping, used for Segmentation Tags"
);

-- Create splio tag_universe table if not exist
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_email_segment.tag_universe_buffer` (
  tag STRING OPTIONS(description="Segmentation Tag name"),
  universe_ids ARRAY<INT64> OPTIONS(description="Spilio universe ids as list")
)
OPTIONS(
  description="Temp table to hold segment/Splio universes Mapping, used for Segmentation Tags before merge"
);

-- Empty this table.
TRUNCATE TABLE `{{ params.bq_project }}.store_email_segment.tag_universe_buffer`;

-- Create batch tag_platform table if not exist
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_email_segment.tag_platform` (
  tag STRING OPTIONS(description="Segmentation Tag name"),
  brands ARRAY<STRUCT<
    brand_name STRING OPTIONS(description="brand trigram"),
    platforms ARRAY<STRING> OPTIONS(description="list of supported platforms")
  >> OPTIONS(description="Struct to store brand and related platforms")
)
OPTIONS(
  description="Splio/Batch platforms Mapping, used for Segmentation Tags"
);

-- Create batch tag_platform table if not exist
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_email_segment.tag_platform_buffer` (
  tag STRING OPTIONS(description="Segmentation Tag name"),
  brands ARRAY<STRUCT<
    brand_name STRING OPTIONS(description="brand trigram"),
    platforms ARRAY<STRING> OPTIONS(description="list of supported platforms")
  >> OPTIONS(description="Struct to store brand and related platforms")
)
OPTIONS(
  description="Temp table to hold Splio/Batch platforms Mapping, used for Segmentation Tags"
);

-- Empty this table.
TRUNCATE TABLE `{{ params.bq_project }}.store_email_segment.tag_platform_buffer`;
