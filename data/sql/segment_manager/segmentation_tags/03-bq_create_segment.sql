-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
-- ENV: {{ environment }}

CREATE TABLE IF NOT EXISTS `{{ bq_project }}.{{store_segment_dataset}}.segment-sha256_{{segment_definition.name|lower}}` (
  email_sha256          STRING    NOT NULL     OPTIONS(description="email_sha256 from business_data.profile_digital_360"),
  update_date           DATE      NOT NULL     OPTIONS(description="segment last update date ")
)

OPTIONS(description="Segment created from segment definition on segment manager with description: " ||
                    " \n" ||
                    "{{segment_definition.description}}" ||
                    " \n" ||
                    "DAG: {{dag.dag_id}}" ||
                    " \n" ||
                    "Sync.: {{segment_definition.frequency|capitalize}}");

TRUNCATE TABLE `{{ bq_project }}.{{store_segment_dataset}}.segment-sha256_{{segment_definition.name|lower}}`;

INSERT INTO `{{ bq_project }}.{{store_segment_dataset}}.segment-sha256_{{segment_definition.name|lower}}`
SELECT DISTINCT
  id.email_sha256  AS email_sha256,
  CURRENT_DATE()   AS update_date
FROM `{{ bq_project }}.business_data.profile_digital_360`
WHERE id.email_profile_master_id IS NOT NULL
    {% if environment == 'prod' %}
    AND ( {{ segment_definition.sql_definition | replace("\\n", "\n") | replace("\\r", " ") }} );
    {% else %}
    -- WHEN preprod only get 400 Profiles to reduce BigQuery bill.
    LIMIT 400;
    {% endif %}
