-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

UPDATE picasso.segment
SET is_main = 't'
WHERE id IN (
	SELECT id
 	FROM (
		SELECT
			DISTINCT s.short_description,
        	s.id,
            ROW_NUMBER () OVER (
				PARTITION BY s.short_description
                ORDER BY LENGTH(s.ref) ASC, s.ref ASC
            ) AS row_number
		FROM picasso.segment s
		LEFT JOIN picasso.segment_display sd ON s.id = sd.id
		WHERE s.id > 650
	) x
	WHERE row_number = 1
);
