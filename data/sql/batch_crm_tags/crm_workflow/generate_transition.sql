-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

{% for config in params.granularities %}
    DROP TABLE IF EXISTS `{{ params.bq_project }}.store_batch_workflow.batch_crm_transition_{{ config['granularity'] }}{{ config['name_suffix'].replace("|", "_") }}`;

    CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_batch_workflow.batch_crm_transition_{{ config['granularity'] }}{{ config['name_suffix'].replace("|", "_") }}`
    (
        install_id      STRING NOT NULL OPTIONS(description="install id"),
        transition      STRING NOT NULL OPTIONS(description="<previous_state>_to_<current_state>")
    )
    OPTIONS(description="Contains the transition from the previous state to the current state by profile for the {{ config['granularity'] }} granularity.\n"
                      ||"For more details, check the workflow: https://drive.google.com/file/d/1YmABgmw9P6FafrD2K2NSi75sX3G6Sk1s/view?usp=sharing."
                      ||"\n\n"
                      ||"DAG: {{ dag.dag_id }}."
                      ||"\n\n"
                      ||"Sync: daily");

    INSERT INTO `{{ params.bq_project }}.store_batch_workflow.batch_crm_transition_{{ config['granularity'] }}{{ config['name_suffix'].replace("|", "_") }}`
    SELECT
        cur.install_id,
        CONCAT(prev.state, "-to-", cur.state) AS transition
    FROM `{{ params.bq_project }}.store_batch_workflow.batch_crm_state_{{ config['granularity'] }}{{ config['name_suffix'].replace("|", "_") }}` AS cur
    JOIN `{{ params.bq_project }}.store_batch_workflow.previous_batch_crm_state_{{ config['granularity'] }}{{ config['name_suffix'].replace("|", "_") }}` AS prev
        ON cur.install_id = prev.install_id
        AND cur.state != prev.state;
{% endfor %}
