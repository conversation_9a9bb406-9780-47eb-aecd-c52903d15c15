-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

SELECT
--    catalog_name AS project_name,
    schema_name AS dataset_name
FROM `{{ project_dict.project_id }}.region-{{ project_dict.project_region }}`.INFORMATION_SCHEMA.SCHEMATA
WHERE
{% if project_dict.is_all_digit %}
    REGEXP_CONTAINS(schema_name, r'^\d+$')
{% elif project_dict.has_pattern %}
    REGEXP_CONTAINS(schema_name, r'^{{ project_dict.pattern }}_\d+$')
{% else %}
    1 = 0
{% endif %};
