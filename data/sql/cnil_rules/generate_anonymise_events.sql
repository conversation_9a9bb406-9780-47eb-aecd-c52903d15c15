-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `export_matrix_email.profile_to_anonymise_events` (
    event_date    TIMESTAMP      NOT NULL    OPTIONS(description="Date of event"),
    event_type    STRING         NOT NULL    OPTIONS(description="type of event"),
    email         STRING                     OPTIONS(description="email"),
    email_hash    STRING                     OPTIONS(description="email hash"),
    payload       STRING                     OPTIONS(description="payload of event")
    ) OPTIONS(
                 expiration_timestamp=NULL,
                 description="Recently generated email_events for profiles to clear PII data by brand.\n"
                 || "\n\n"
                 || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);


TRUNCATE TABLE `export_matrix_email.profile_to_anonymise_events`;

INSERT INTO `export_matrix_email.profile_to_anonymise_events`
-- Extract profiles to anonymise
WITH profile_email_consents AS (
    SELECT
        id.email_profile_master_id,
        SUM(ARRAY_LENGTH(service.email.active.consents)) AS nb_consent,
        SUM(
            IF('b2b_prisme_crm' IN UNNEST(service.email.inactive.consents), 1, 0)
        ) + SUM(
            IF('b2b_prisme_kpi_crm' IN UNNEST(service.email.inactive.consents), 1, 0)
        ) AS nb_prisme_consent,
    FROM `business_data.profile_digital_360`
    GROUP BY 1
    -- profile has no active consent globally
    HAVING nb_consent = 0
        -- profile has no b2b inactive consents
        AND nb_prisme_consent = 0
), profile_info AS (
    SELECT
        pe.id.email_profile_master_id,
        IF(pe.id.mag_customer_id IS NULL, False, True) AS is_paid_user,
        IF(pe.id.pmc_profile_master_id IS NULL, FALSE, TRUE) AS is_pmc,
        DATE(pe.activities.email.last_unsub_date) AS email_last_unsub_date
    FROM `business_data.profile_digital_360` AS pe
    JOIN profile_email_consents  AS pec ON pec.email_profile_master_id = pe.id.email_profile_master_id
), custom_blacklisted_profile AS (
    SELECT profile_master_id
    FROM `store_matrix_email.blacklist`
    WHERE source IN ('minority-report', 'manual : DPO')
), technical_blacklisted_profile AS (
    SELECT profile_master_id
    FROM `refined_data.technical_blacklist`
), profiles_to_anonymise AS (
    SELECT DISTINCT email_profile_master_id
    FROM profile_info p
    LEFT JOIN custom_blacklisted_profile AS cbp ON p.email_profile_master_id = cbp.profile_master_id
    LEFT JOIN technical_blacklisted_profile AS tbp ON p.email_profile_master_id = tbp.profile_master_id
    WHERE is_pmc = False
        AND is_paid_user = False
        -- last_unsub_date on brand is older than a week ago
        -- needed to safely anonymise profiles
        AND DATE (email_last_unsub_date) < DATE_SUB(CURRENT_DATE (), INTERVAL 7 DAY)
        -- profile is not is custom lop
        AND cbp.profile_master_id IS NULL
        -- profile is not is tech lop
        AND tbp.profile_master_id IS NULL
)
-- Generate anonymise_profile events
SELECT
    CURRENT_TIMESTAMP() AS event_date,
    'anonymise_profile' AS event_type,
    email AS email,
    email_sha256 AS email_hash,
    JSON_EXTRACT('{'
        || '"ev": 100,'
        || '"app": "mozart",'
        || '"process": "{{ dag.dag_id }}",'
        || '"source": "matrix",'
        || '"medium": "cnil rules - anonymise profile",'
        || '"profile_master_id":' || pmi.id
        || '}', '$') AS payload
FROM profiles_to_anonymise AS p
JOIN `{{params.bq_project}}.store_matrix_email.profile_master_id` pmi ON p.email_profile_master_id = pmi.id
-- start by older profiles
ORDER BY pmi.id
{% if params.limit != 0 %}
    LIMIT {{params.limit}}
{% endif %}
;


-- We have to communicate to our external system the list of profile to clean / delete.
CREATE TABLE IF NOT EXISTS `export_partner.gdpr_profile_to_delete` (
    event_date    TIMESTAMP      NOT NULL    OPTIONS(description="Date of event"),
    email         STRING         NOT NULL    OPTIONS(description="email"),
    email_hash    STRING         NOT NULL    OPTIONS(description="email sha256 hash"),
    email_consents ARRAY<STRING>             OPTIONS(description="list of email_consent public_ref where the user WAS subscribed (currently unsub)")
) OPTIONS(
     description="Recently generated email_events for profiles to delete from systems.\n"
     || "Keep profiles for 10 days maximum, so that we have time to send orders to external systems."
     || "\n\n"
     || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);

-- delete older events
DELETE FROM `export_partner.gdpr_profile_to_delete`
WHERE event_date <= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 DAY);

-- add today events
INSERT INTO `export_partner.gdpr_profile_to_delete`
SELECT
    ae.event_date,
    ae.email,
    ae.email_hash,
    p360.service.email.inactive.consents as inactive_consents
FROM `export_matrix_email.profile_to_anonymise_events` AS ae
JOIN `business_data.profile_digital_360` AS p360 ON ae.email_hash = p360.id.email_sha256
WHERE email IS NOT NULL
;









