-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{params.bq_project}}.export_matrix_email.brand_profile_to_clear_pii_events` (
    event_date    TIMESTAMP      NOT NULL    OPTIONS(description="Date of event"),
    event_type    STRING         NOT NULL    OPTIONS(description="type of event"),
    email         STRING                     OPTIONS(description="email"),
    email_hash    STRING                     OPTIONS(description="email hash"),
    payload       STRING                     OPTIONS(description="payload of event")
    ) OPTIONS(
                 expiration_timestamp=NULL,
                 description="Recently generated email_events for profiles to clear PII data by brand.\n"
                 || "\n\n"
                 || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);


TRUNCATE TABLE `{{params.bq_project}}.export_matrix_email.brand_profile_to_clear_pii_events`;

INSERT INTO `{{params.bq_project}}.export_matrix_email.brand_profile_to_clear_pii_events`
-- Extract profiles to clean by brand
WITH unsub_profiles_by_brand AS (
  SELECT profile_master_id,
    pec.brand_id,
    COUNTIF(pec.consent_status = 'sub') sub_consents,
    MAX(IF(pec.consent_status = 'unsub', update_date, NULL)) AS last_unsub_date
  FROM `{{params.bq_project}}.store_matrix_email.profiles_email_consents` AS pec
  JOIN `{{params.bq_project}}.refined_data.email_base` AS eb ON eb.consent_id = pec.email_consent_id
  -- skip b2b consents
  WHERE eb.consent_public_ref NOT IN ('b2b_prisme_crm', 'b2b_prisme_kpi_crm')
  GROUP BY 1,2
), profile_to_clean_for_brand AS (
    SELECT profile_master_id, brand_id
    FROM unsub_profiles_by_brand AS upbb
    JOIN `{{params.bq_project}}.refined_data.profile_email` AS pe ON pe.email_profile_master_id = upbb.profile_master_id
    WHERE upbb.sub_consents = 0
      -- last_unsub_date on brand is older than a week ago
      -- needed to safely clean pii for all unsub-by-brand
      AND DATE (last_unsub_date) < DATE_SUB(CURRENT_DATE (), INTERVAL 7 DAY)
      -- user still have personal data
      AND (pe.info.firstname IS NOT NULL
          OR pe.info.lastname IS NOT NULL
          OR pe.info.birthdate IS NOT NULL
          OR pe.info.gender IS NOT NULL
          OR pe.info.address IS NOT NULL
          OR pe.info.address_sup IS NOT NULL
          OR pe.info.zipcode IS NOT NULL
          OR pe.info.city IS NOT NULL
          OR pe.info.country IS NOT NULL
      )
)
-- Generate clean_brand_profile_pii events
SELECT
    CURRENT_TIMESTAMP() AS event_date,
    'clear_pii' AS event_type,
    email AS email,
    email_sha256 AS email_hash,
    JSON_EXTRACT('{'
        || '"ev": 100,'
        || '"app": "mozart",'
        || '"process": "{{ dag.dag_id }}",'
        || '"source": "matrix",'
        || '"medium": "cnil rules - clear profile pii",'
        || '"brand_id":' || brand_id || ','
        || '"profile_master_id":' || profile_master_id
        || '}', '$') AS payload
FROM profile_to_clean_for_brand AS p
JOIN `{{params.bq_project}}.store_matrix_email.profile_master_id` pmi ON p.profile_master_id = pmi.id
{% if params.limit != 0 %}
    LIMIT {{params.limit}}
{% endif %}

;