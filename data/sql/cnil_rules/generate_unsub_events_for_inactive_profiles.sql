-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{params.bq_project}}.export_matrix_email.clean_profile_events` (
    event_date    TIMESTAMP      NOT NULL    OPTIONS(description="Date of event"),
    event_type    STRING         NOT NULL    OPTIONS(description="type of event"),
    email         STRING                     OPTIONS(description="email"),
    email_hash    STRING                     OPTIONS(description="email hash"),
    payload       STRING                     OPTIONS(description="payload of event")
    ) OPTIONS(expiration_timestamp=NULL,
              description="Recently generated email_events for inactive profiles to unsub."
                        ||"\n\n"
                        ||"DAG: {{ dag.dag_id }}."
                        ||"\n\n"
                        ||"Sync: Daily");

TRUNCATE TABLE `{{params.bq_project}}.export_matrix_email.clean_profile_events`;

INSERT INTO `{{params.bq_project}}.export_matrix_email.clean_profile_events`
-- Extract profiles to clean
WITH all_click AS (
    SELECT
        pmi.id AS profile_master_id,
        DATE(datetime) AS click_date
    FROM `{{params.bq_project}}.store_archive.tracking_prisma_full_data` t
    JOIN `{{params.bq_project}}.store_matrix_email.profile_master_id` pmi ON t.recipient_email_md5 = pmi.email_md5
    WHERE type = 'click'
    UNION ALL
    SELECT
        email_profile_master_id AS profile_master_id,
        DATE(click_date) AS click_date
    FROM `{{params.bq_project}}.refined_data.email_event_click`
), last_click AS (
    SELECT
        profile_master_id,
        MAX(DATE(click_date)) AS last_click_date
    FROM all_click
    GROUP BY 1
), profile_info AS (
    SELECT
        pe.id.email_profile_master_id,
        IF(pe.id.mag_customer_id IS NULL, FALSE, TRUE) AS is_paid_user,
        IF(pe.id.pmc_profile_master_id IS NULL, FALSE, TRUE) AS is_pmc,
        DATE(pe.activities.email.last_sub_date) AS email_last_sub_date,
        DATE(pe.activities.pmc.last_login_date) AS pmc_last_login_date,
        DATE(pe.activities.pmc.last_activity_date) AS pmc_last_activity_date,
        lc.last_click_date AS email_last_click_date,
        email_consent AS email_consent_ref
    FROM `{{params.bq_project}}.business_data.profile_digital_360` AS pe,
         UNNEST(service.email.active.consents) AS email_consent
    LEFT JOIN last_click AS lc ON pe.id.email_profile_master_id = lc.profile_master_id
), profile_to_clean AS (
    SELECT
        email_profile_master_id,
        eb.consent_id
    FROM profile_info AS pi
    JOIN `{{params.bq_project}}.refined_data.email_base` AS eb ON eb.consent_public_ref = pi.email_consent_ref
    WHERE
        -- skip crm and loy consents
        eb.consent_type NOT IN ('loy', 'crm')
        -- skip prismashop_offres_prisma
        AND eb.consent_public_ref != 'prismashop_offres_prisma'
        -- (last_click occurred more than 3 years ago OR never occurred)
        AND (email_last_click_date < DATE_SUB(CURRENT_DATE(), INTERVAL 3 YEAR) OR email_last_click_date IS NULL)
        -- AND last_sub occurred more than 3 years ago
        AND (email_last_sub_date < DATE_SUB(CURRENT_DATE(), INTERVAL 3 YEAR) OR email_last_sub_date IS NULL)
        -- AND user does not have paid services
        AND is_paid_user = False
)

SELECT
    CURRENT_TIMESTAMP() AS event_date,
    'unsub' AS event_type,
    email AS email,
    email_sha256 AS email_hash,
    JSON_EXTRACT(CONCAT('{'
        , '"ev": 100,'
        , '"app": "mozart",'
        , '"process": "{{ dag.dag_id }}",'
        , '"source": "matrix",'
        , '"medium": "cnil rules - clean inactive profile",'
        , '"consent_ids":' || TO_JSON_STRING(ARRAY_AGG(DISTINCT consent_id))
        , '}'), '$') AS payload
FROM profile_to_clean AS p
JOIN `{{params.bq_project}}.store_matrix_email.profile_master_id` pmi ON p.email_profile_master_id = pmi.id
GROUP BY 1, 2, 3, 4
{% if params.limit != 0 %}
    LIMIT {{ params.limit }}
{% endif %}
;
