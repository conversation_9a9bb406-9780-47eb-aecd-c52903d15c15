-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
DROP TABLE IF EXISTS `{{ params.bq_project }}.export_postal.addresscompany_{{ next_ds_nodash }}`;
DROP TABLE IF EXISTS `{{ params.bq_project }}.export_postal.refcivilt_{{ next_ds_nodash }}`;
DROP TABLE IF EXISTS `{{ params.bq_project }}.export_postal.refordpmt_{{ next_ds_nodash }}`;
DROP TABLE IF EXISTS `{{ params.bq_project }}.export_postal.refprdmat_{{ next_ds_nodash }}`;
DROP TABLE IF EXISTS `{{ params.bq_project }}.export_postal.refprdprt_{{ next_ds_nodash }}`;

CREATE TABLE `{{ params.bq_project }}.export_postal.addresscompany_{{ next_ds_nodash }}` (
    ID_CLIENT                   STRING,
    ID_CIVILITE                 INTEGER,
    NOM                         STRING,
    PRENOM                      STRING,
    INFO_SUPP_CLIENT            STRING,
    INFO_SUPP_ADR               STRING,
    INFO_SUPP_RUE               STRING,
    RUE                         STRING,
    VILLE                       STRING,
    CODE_POSTAL                 STRING,
    CODE_INSEE                  STRING,
    TELEPHONE                   STRING,
    DATE_NAISSANCE              DATE,
    NUMERO_ABO                  STRING,
    ID_TITRE                    INTEGER,
    DATE_DEBUT_ABO              DATE,
    DATE_FIN_ABO                DATE,
    STATUT_ABONNEMENT           STRING,
    FIDELITE                    INTEGER,
    ID_MODE_PAIEMENT_DETAILLE   INTEGER,
    CANAL_RECRUTEMENT           STRING,
    MONTANT_FACTURE_TTC         FLOAT64,
    DIRECT_GROUPEUR             STRING,
    TYPE_ABO                    STRING,
    NOM_ENTREPRISE              STRING,
    ID_PRODUIT                  INTEGER,
    QUANTITE                    INTEGER,
    DATE_COMMANDE               DATE,
    CODE_IRIS                   STRING,
    MODE_LIVRAISON              STRING
);
CREATE TABLE `{{ params.bq_project }}.export_postal.refcivilt_{{ next_ds_nodash }}` (
    ID      INTEGER,
    LABEL   STRING
);
CREATE TABLE `{{ params.bq_project }}.export_postal.refordpmt_{{ next_ds_nodash }}` (
    ID      INTEGER,
    LABEL   STRING
);
CREATE TABLE `{{ params.bq_project }}.export_postal.refprdmat_{{ next_ds_nodash }}` (
    ID_TITRE INTEGER,
    TITRE    STRING
);
CREATE TABLE `{{ params.bq_project }}.export_postal.refprdprt_{{ next_ds_nodash }}` (
    ID      INTEGER,
    LABEL   STRING
);

INSERT INTO `{{ params.bq_project }}.export_postal.addresscompany_{{ next_ds_nodash }}`
SELECT ID_CLIENT,
       ID_CIVILITE,
       NOM,
       PRENOM,
       INFO_SUPP_CLIENT,
       INFO_SUPP_ADR,
       INFO_SUPP_RUE,
       RUE,
       VILLE,
       CODE_POSTAL,
       CODE_INSEE,
       TELEPHONE,
       DATE_NAISSANCE,
       NUMERO_ABO,
       ID_TITRE,
       DATE_DEBUT_ABO,
       DATE_FIN_ABO,
       STATUT_ABONNEMENT,
       FIDELITE,
       ID_MODE_PAIEMENT_DETAILLE,
       CANAL_RECRUTEMENT,
       MONTANT_FACTURE_TTC,
       DIRECT_GROUPEUR,
       TYPE_ABO,
       NOM_ENTREPRISE,
       ID_PRODUIT,
       QUANTITE,
       DATE_COMMANDE,
       CODE_IRIS,
       MODE_LIVRAISON
  FROM `{{ params.bi_bq_project }}.export_it_data.adress_company`;

INSERT INTO `{{ params.bq_project }}.export_postal.refcivilt_{{ next_ds_nodash }}`
SELECT ID, LABEL
  FROM `{{ params.bi_bq_project }}.export_it_data.referentiel_civilite`;

INSERT INTO `{{ params.bq_project }}.export_postal.refordpmt_{{ next_ds_nodash }}`
SELECT ID, LABEL
  FROM `{{ params.bi_bq_project }}.export_it_data.referentiel_paiement`;

INSERT INTO `{{ params.bq_project }}.export_postal.refprdmat_{{ next_ds_nodash }}`
SELECT ID_TITRE, TITRE
  FROM `{{ params.bi_bq_project }}.export_it_data.referentiel_titre`;

INSERT INTO `{{ params.bq_project }}.export_postal.refprdprt_{{ next_ds_nodash }}`
SELECT ID, LABEL
  FROM `{{ params.bi_bq_project }}.export_it_data.referentiel_produit`;
