-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

--1: CREATE TABLE IF NOT EXISTS
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.sifflet.dashboard_scan` (
    observation_date        DATE                NOT NULL    OPTIONS(description="date when the data is inserted"),
    id                      INTEGER             NOT NULL    OPTIONS(description="data product id"),
    title                   STRING              NOT NULL    OPTIONS(description="data product title"),
    type                    STRING                          OPTIONS(description="data product type"),
    status                  STRING                          OPTIONS(description="indicates whether the dashboard status (publish, draft)"),
    description             STRING                          OPTIONS(description="short summary of dashboard, its purpose/objectives/sources"),
    keywords                ARRAY<STRING>                   OPTIONS(description="keywords text"),
    embed_url               STRING                          OPTIONS(description="data product embed url"),
    thumbnail               STRING                          OPTIONS(description="data product thumbnail"),
    created_date            TIMESTAMP                       OPTIONS(description="data product create date"),
    updated_date            TIMESTAMP                       OPTIONS(description="data product updated date"),
    published_date          TIMESTAMP                       OPTIONS(description="data product published date"),
    PRIMARY KEY(observation_date, id) NOT ENFORCED

)
PARTITION BY observation_date
CLUSTER BY id
OPTIONS(description="Retrieve, every day, all the dashboards publish with different information that are related.\n\n"||
                    "DAG: {{ dag.dag_id }}.\n\n"||
                    "Sync: daily.");
--2: CLEAR THE TABLE
{% if params.is_full %}
TRUNCATE TABLE `{{ params.bq_project }}.sifflet.dashboard_scan`;

{% else %}
DELETE FROM `{{ params.bq_project }}.sifflet.dashboard_scan`
WHERE observation_date = DATE("{{ data_interval_end }}");
{% endif %}
--3: FEED THE TABLE
INSERT INTO `{{ params.bq_project }}.sifflet.dashboard_scan`
SELECT
    DATE("{{ data_interval_end }}") AS observation_date,
    dp.id,
    dp.title,
    dp.type,
    dp.status,
    dp.description,
    ARRAY_AGG(kw.keyword IGNORE NULLS) AS keywords,
    dp.embed_url,
    dp.thumbnail,
    dp.created_date,
    dp.updated_date,
    dp.published_date
FROM `{{ params.bq_project }}.store_atlas.data_product` AS dp
LEFT JOIN `{{ params.bq_project }}.store_atlas.data_products_keywords` AS dpkw
    ON dpkw.data_product_id = dp.id
LEFT JOIN `{{ params.bq_project }}.store_atlas.keyword` AS kw
    ON dpkw.keyword_id = kw.id
GROUP BY ALL
