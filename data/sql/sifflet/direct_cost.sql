-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

--1: CREATE TABLE IF NOT EXISTS
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.sifflet.direct_cost` (
  observation_date          DATE            NOT NULL OPTIONS(description="Date when the data is inserted"),
  dashboard_id              INT64           NOT NULL OPTIONS(description="Dashboard ID"),
  dashboard_name            STRING          NOT NULL OPTIONS(description="Name of the dashboard that has direct costs associated"),
  total_cost_dollar         FLOAT64         NOT NULL OPTIONS(description="Total direct costs associated with this dashboard"),
  total_billed_bytes        INT64           NOT NULL OPTIONS(description="Total billed bytes associated with this dashboard"),
  details ARRAY<STRUCT<
    cost_dollar             FLOAT64         NOT NULL OPTIONS(description="Cost of this request"),
    totalBilledBytes        INT64           NOT NULL OPTIONS(description="Billed Bytes count for this request"),
    query                   STRING          NOT NULL OPTIONS(description="The query that was launched and billed"),
    source_table            ARRAY<STRING>            OPTIONS(description="Table targeted by the query, where the data is retrieved"),
    job_execution_timestamp TIMESTAMP       NOT NULL OPTIONS(description="Exact time or this request was executed")
  >>                                                 OPTIONS(description="details of each query launched associated with this dashboard"),
  PRIMARY KEY(observation_date, dashboard_name) NOT ENFORCED
)
PARTITION BY observation_date
CLUSTER BY dashboard_name
OPTIONS(description="Brings together information about the different queries <NAME_EMAIL> when using dashboards by users, as well as the direct costs generated by these queries.\n\n"||
                    "DAG: {{ dag.dag_id }}.\n\n"||
                    "Sync: daily.");

--2: CLEAR THE TABLE
{% if params.is_full %}
TRUNCATE TABLE `{{ params.bq_project }}.sifflet.direct_cost`;

{% else %}
DELETE FROM `{{ params.bq_project }}.sifflet.direct_cost`
WHERE observation_date = DATE("{{ data_interval_end }}");
{% endif %}

--3: FEED THE TABLE
INSERT INTO `{{ params.bq_project }}.sifflet.direct_cost`
WITH direct_cost_by_table AS (--Get the direct costs of a table
  SELECT
    protopayload_auditlog.servicedata_v1_bigquery.jobCompletedEvent.job.jobStatistics.totalBilledBytes AS totalBilledBytes,
    protopayload_auditlog.servicedata_v1_bigquery.jobCompletedEvent.job.jobConfiguration.query.query AS query,
    REGEXP_EXTRACT_ALL(protopayload_auditlog.servicedata_v1_bigquery.jobCompletedEvent.job.jobConfiguration.query.query,r'\bFROM\s+`([^`]+)`') AS source_table,
    REGEXP_EXTRACT_ALL(protopayload_auditlog.servicedata_v1_bigquery.jobCompletedEvent.job.jobConfiguration.query.query,r'FROM\s+`pm-prod-([^.`]+)') [SAFE_OFFSET(0)] AS project,
    REGEXP_EXTRACT_ALL(protopayload_auditlog.servicedata_v1_bigquery.jobCompletedEvent.job.jobConfiguration.query.query,r'FROM\s+`pm-prod-[^.`]+\.(\w+)') [SAFE_OFFSET(0)] AS dataset,
    REGEXP_EXTRACT_ALL(protopayload_auditlog.servicedata_v1_bigquery.jobCompletedEvent.job.jobConfiguration.query.query,r'FROM\s+`pm-prod-[^.`]+\.[^.`]+\.(\w+)`') [SAFE_OFFSET(0)] AS table,
    protopayload_auditlog.servicedata_v1_bigquery.jobCompletedEvent.job.jobStatistics.totalBilledBytes * 0.000000000001 * {{ params.bq_cost_by_TB }} AS cost_dollar,
    timestamp AS job_execution_timestamp
  FROM `{{ params.query_cost_project }}.billing.cloudaudit_googleapis_com_data_access_*`
  WHERE
    protopayload_auditlog.authenticationInfo.principalEmail LIKE '%<EMAIL>%'
{% if params.is_full %}
    AND _TABLE_SUFFIX >= "{{ params.start_date }}"
    AND DATE(timestamp) >= PARSE_DATE("%Y%m%d", "{{ params.start_date }}")
{% else %}
    AND _TABLE_SUFFIX = "{{ macros.ds_format(ds, '%Y-%m-%d', '%Y%m%d') }}"
    AND DATE(timestamp) = "{{ ds }}"
{% endif %}
  GROUP BY ALL),

split_ids AS (
  SELECT
    DISTINCT
    PROJECT,
    dataset,
    TABLE,
    d.type,
    SPLIT(label.value, "-") AS ids
  FROM `{{ params.bq_project }}.generated_data.store__bq_metadata`
  LEFT JOIN UNNEST(DATA) AS d
  LEFT JOIN UNNEST(d.labels) AS label
  WHERE
    observation_date = CURRENT_DATE()
    AND d.type = "VIEW"
    AND label.key = "dashboard_id"
),

table_for_dashboard AS (
  SELECT
    si.PROJECT,
    si.dataset,
    si.TABLE,
    si.type,
    id AS dashboard_id,
    adp.title AS dashboard_name,
  FROM split_ids si,
  UNNEST(si.ids) AS id
  LEFT JOIN `{{ params.bq_project }}.refined_data.atlas_data_products` AS adp ON id = CAST(adp.id AS STRING)
)

SELECT -- Get in a STRUCT all the separate cost on a specific dashboard
  CURRENT_DATE() AS observation_date,
  SAFE_CAST(td.dashboard_id AS INT64) AS dashboard_id,
  td.dashboard_name AS dashboard_name,
  SUM(dct.cost_dollar) AS total_cost_dollar,
  SUM(dct.totalBilledBytes) AS total_billed_bytes,
  ARRAY_AGG(STRUCT(
    dct.cost_dollar,
    dct.totalBilledBytes,
    dct.query,
    dct.source_table,
    dct.job_execution_timestamp
  )) AS details,
FROM direct_cost_by_table AS dct
JOIN table_for_dashboard AS td ON (dct.project=td.project AND dct.dataset=td.dataset AND dct.table=td.table)
WHERE
  totalBilledBytes IS NOT NULL
GROUP BY ALL;
