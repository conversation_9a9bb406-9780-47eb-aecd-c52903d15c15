-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

--1: CREATE TABLE IF NOT EXISTS
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.sifflet.cost_tracking_dashboard_governance` (
  observation_date          DATE            NOT NULL OPTIONS(description="date when the data is inserted"),
  dashboard_id              INT64           NOT NULL OPTIONS(description="dashboard ID"),
  dashboard_name            STRING                   OPTIONS(description="name of the dashboard that has direct costs associated"),
  total_cost                FLOAT64                  OPTIONS(description="total cost of the dashboard"),
  direct_cost_dollar        FLOAT64                  OPTIONS(description="direct cost of the dashboard"),
  indirect_cost_dollar      FLOAT64                  OPTIONS(description="indirect cost of the dashboard"),
  dashboard_state           STRING                   OPTIONS(description="State of the dashboard"),
  PRIMARY KEY(observation_date, dashboard_name) NOT ENFORCED
)
PARTITION BY observation_date
CLUSTER BY dashboard_name
OPTIONS(description=".\n\n"||
                    "DAG: {{ dag.dag_id }}.\n\n"||
                    "Sync: daily.");
--2: CLEAR THE TABLE
{% if params.is_full %}
TRUNCATE TABLE `{{ params.bq_project }}.sifflet.cost_tracking_dashboard_governance`;

{% else %}
DELETE FROM `{{ params.bq_project }}.sifflet.cost_tracking_dashboard_governance`
WHERE observation_date = DATE("{{ data_interval_end }}");
{% endif %}

--3: FEED THE TABLE
INSERT INTO `{{ params.bq_project }}.sifflet.cost_tracking_dashboard_governance`
WITH
split_ids AS (
    SELECT
        DISTINCT PROJECT,
        dataset,
        TABLE,
        d.type,
        SPLIT(label.value, "-") AS ids,
        used_table
    FROM
        `{{ params.bq_project }}.generated_data.store__bq_metadata`
    LEFT JOIN
        UNNEST(DATA) AS d
    LEFT JOIN
        UNNEST(d.labels) AS label
    LEFT JOIN
        UNNEST(d.used_table) AS used_table
    WHERE
        observation_date = DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL 1 DAY)
        AND label.key = "dashboard_id"
),

sum_costs AS (
    SELECT
        final_table,
        dataset_name,
        project_name,
        SUM(cost_dollar) AS cost_dollar,
    FROM `{{ params.bq_project }}.sifflet.lineage_and_indirect_cost`
    WHERE
    {% if params.is_full %}
        observation_date >= "{{ params.start_date }}"
    {% else %}
        observation_date = DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL 1 DAY)
    {% endif %}
    GROUP BY ALL
),

indirect_cost AS (
    SELECT
        ticd.final_table,
        ticd.dataset_name,
        ticd.project_name,
        cost_dollar,
        si.type,
        si.ids
    FROM sum_costs AS  ticd
    JOIN split_ids AS si
        ON  ticd.final_table = si.used_table
        OR CONCAT( ticd.dataset_name,".", ticd.final_table) = si.used_table
        OR CONCAT( ticd.project_name,".", ticd.dataset_name,".", ticd.final_table) = si.used_table
    GROUP BY ALL
),

-- total_indirect_cost_dollar AS (
total_indirect_cost_dollar AS (
    SELECT
        id,
        SUM(cost_dollar) AS total_indirect_cost_dollar
    FROM
        indirect_cost,
        UNNEST(ids) AS id
    --WHERE cost_dollar IS NOT NULL
    GROUP BY
      id
),

direct_cost AS (
    SELECT
        total_cost_dollar,
        dashboard_id
    FROM `{{ params.bq_project }}.sifflet.direct_cost`
    WHERE
    {% if params.is_full %}
        observation_date >= "{{ params.start_date }}"
    {% else %}
        observation_date = DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL 1 DAY)
    {% endif %}
)

SELECT
  DATE("{{ data_interval_end }}") AS observation_date,
  ds.id,
  ds.title,
  IFNULL(dc.total_cost_dollar, 0) + IFNULL(ticd.total_indirect_cost_dollar, 0) AS total_cost,
  IFNULL(dc.total_cost_dollar,0) AS direct_cost_dollar,
  IFNULL(ticd.total_indirect_cost_dollar,0) AS indirect_cost_dollar,
  ds.status AS dashboard_state
FROM `{{ params.bq_project }}.sifflet.dashboard_scan` AS ds
LEFT JOIN `direct_cost` AS dc
  ON ds.id = dc.dashboard_id
LEFT JOIN total_indirect_cost_dollar AS ticd
  ON CAST(ds.id AS STRING) = ticd.id
