-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

--1: CREATE TABLE IF NOT EXISTS
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.sifflet.recursive_lineage` (
  observation_date              DATE    NOT NULL OPTIONS(description = "Date when the data is inserted"),
  table_id                      STRING  NOT NULL OPTIONS(description = "Unique identifier (ID) of the downstream table"),
  final_table                   STRING  NOT NULL OPTIONS(description = "Name of the downstream table"),
  dataset_name                  STRING  NOT NULL OPTIONS(description = "Dataset (datasource) name of the downstream table"),
  project_name                  STRING  NOT NULL OPTIONS(description = "GCP project name containing the downstream table"),

  lineage_info ARRAY<STRUCT<
    parent_table_id         STRING,
    parent_table_name       STRING,
    parent_dataset_name     STRING,
    parent_project_name     STRING
  >> OPTIONS(description = "Ordered list of parent tables and their metadata representing the lineage"),
  PRIMARY KEY(observation_date, table_id) NOT ENFORCED
)
PARTITION BY observation_date
CLUSTER BY final_table
OPTIONS(partition_expiration_days=7,
        description="Compile and order the result produced in the table {{ params.bq_project }}.sifflet.get_asset_info to determine the final table of each table, or its lineage.\n"||
                    "DAG: {{ dag.dag_id }}.\n"||
                    "Sync: daily.");

--2: CLEAR THE TABLE
{% if params.is_full %}
TRUNCATE TABLE `{{ params.bq_project }}.sifflet.recursive_lineage`;

{% else %}
DELETE FROM `{{ params.bq_project }}.sifflet.recursive_lineage`
WHERE observation_date = DATE("{{ data_interval_end }}");
{% endif %}

--3: FEED THE TABLE
INSERT INTO `{{ params.bq_project }}.sifflet.recursive_lineage`
WITH RECURSIVE LineageRecursive AS (
        -- Base case
        SELECT
            table_id,
            final_table,
            dataset_name,
            project_name,
            ARRAY<STRUCT<
                parent_table_id STRING,
                parent_table_name STRING,
                parent_dataset_name STRING,
                parent_project_name STRING
            >>[STRUCT(parent_table_id, parent_table_name, parent_dataset_name, parent_project_name)] AS parent_lineage,
            ARRAY<STRING>[table_id] AS visited_tables
        FROM
            `{{ params.bq_project }}.sifflet.get_asset_info`
        WHERE
            parent_table_id IS NOT NULL
            AND observation_date = DATE("{{ data_interval_end }}")


        UNION ALL

        -- Recursive case
        SELECT
            lr.table_id,
            lr.final_table,
            lr.dataset_name,
            lr.project_name,
            ARRAY_CONCAT(
                lr.parent_lineage,
                [STRUCT(
                    gai.parent_table_id,
                    gai.parent_table_name,
                    gai.parent_dataset_name,
                    gai.parent_project_name
                )]
            ) AS parent_lineage,
            ARRAY_CONCAT(lr.visited_tables, [gai.parent_table_id]) AS visited_tables
        FROM LineageRecursive lr
        JOIN `{{ params.bq_project }}.sifflet.get_asset_info` AS gai
            ON lr.parent_lineage[SAFE_OFFSET(ARRAY_LENGTH(lr.parent_lineage) - 1)].parent_table_id = gai.table_id
            -- -- The information of the parent table (gai) is joined with the last element of the current line (lr), in order to extend recursively the chain of ascending dependency.
        WHERE
            gai.parent_table_id IS NOT NULL
            AND NOT gai.parent_table_id IN UNNEST(lr.visited_tables)
            AND observation_date = DATE("{{ data_interval_end }}")
),

deduplicate AS (
    SELECT DISTINCT * EXCEPT(parent_lineage, visited_tables)
    FROM LineageRecursive, UNNEST(parent_lineage) AS parent_element
)

SELECT
    DATE("{{ data_interval_end }}") AS observation_date,
    table_id,
    final_table,
    dataset_name,
    project_name,
    ARRAY_AGG(
        STRUCT(
            parent_table_id,
            parent_table_name,
            parent_dataset_name,
            parent_project_name
        ) ORDER BY parent_table_name
    ) AS lineage_info
FROM deduplicate
GROUP BY ALL;
