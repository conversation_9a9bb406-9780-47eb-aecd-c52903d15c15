-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

--1: CREATE TABLE IF NOT EXISTS
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.sifflet.get_asset_info` (
  observation_date              DATE   NOT NULL OPTIONS(description="Date when the data is inserted"),
  final_table                   STRING NOT NULL OPTIONS(description="Name of the child table in the lineage"),
  table_id                      STRING NOT NULL OPTIONS(description="Unique identifier (ID) of the child table"),
  domain_name                   STRING NOT NULL OPTIONS(description="Domain name to which the child table belongs"),
  dataset_name                  STRING NOT NULL OPTIONS(description="Dataset (datasource) name of the child table"),
  project_name                  STRING NOT NULL OPTIONS(description="GCP project name containing the child table"),
  parent_table_id               STRING NOT NULL OPTIONS(description="Unique identifier (ID) of the parent table (source)"),
  parent_table_name             STRING NOT NULL OPTIONS(description="Name of the parent table (source)"),
  parent_table_type             STRING NOT NULL OPTIONS(description="Type of the parent asset (e.g., Table, View, etc.)"),
  parent_domain_name            STRING NOT NULL OPTIONS(description="Domain name to which the parent table belongs"),
  parent_dataset_name           STRING NOT NULL OPTIONS(description="Dataset name of the parent table"),
  parent_project_name           STRING NOT NULL OPTIONS(description="GCP project name containing the parent table"),
  PRIMARY KEY(observation_date, table_id, parent_table_id) NOT ENFORCED
)
PARTITION BY observation_date
CLUSTER BY final_table
OPTIONS(partition_expiration_days=7,
        description="Get infos for each table and get the N+1 and N-1 lineage for each table.\n"||
                    "DAG: {{ dag.dag_id }}.\n"||
                    "Sync: daily.");

--2: CLEAR THE TABLE
{% if params.is_full %}
TRUNCATE TABLE `{{ params.bq_project }}.sifflet.get_asset_info`;

{% else %}
DELETE FROM `{{ params.bq_project }}.sifflet.get_asset_info`
WHERE observation_date = DATE("{{ data_interval_end }}");
{% endif %}

--3: FEED THE TABLE
INSERT INTO `{{ params.bq_project }}.sifflet.get_asset_info`
WITH get_asset_info AS (
  SELECT DISTINCT
    asset.ID AS asset_id,
    asset.ASSET_NAME AS parent_table_name,
    asset.ASSET_TYPE AS parent_table_type,
    dom.DOMAIN_NAME AS domain_name,
    JSON_VALUE(dts.PARAMS, '$.datasetId') AS dataset_name,
    JSON_VALUE(dts.PARAMS, '$.projectId') AS project_name
  FROM `{{ params.bq_project }}.sifflet.asset_{{ params.today }}` AS asset
  JOIN `{{ params.bq_project }}.sifflet.relationship_asset_domain_{{ params.today }}` AS rad ON rad.ASSET_ID = asset.ID
  JOIN `{{ params.bq_project }}.sifflet.domain_{{ params.today }}` AS dom ON rad.DOMAIN_ID = dom.ID
  JOIN `{{ params.bq_project }}.sifflet.datasource_{{ params.today }}` AS dts ON asset.DATASOURCE_ID = dts.ID
  WHERE LOWER(dom.DOMAIN_NAME) IN ("matrix","email","ga4")
{% if params.is_full %}
{% else %}
    AND DATE(asset.LAST_MODIFIED_AT) >= DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL 1 DAY)
    AND DATE(dts.LAST_MODIFIED_AT) >= DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL 1 DAY)
{% endif %}
)

SELECT DISTINCT
    DATE("{{ data_interval_end }}") AS observation_date,
    dsa.parent_table_name AS final_table,
    dsa.asset_id AS table_id,
    dsa.domain_name,
    dsa.dataset_name,
    dsa.project_name,
    usa.asset_id AS parent_table_id,
    usa.parent_table_name,
    usa.parent_table_type,
    usa.domain_name,
    usa.dataset_name,
    usa.project_name
FROM `{{ params.bq_project }}.sifflet.lineage_{{ params.today }}` AS lin
JOIN get_asset_info AS dsa ON dsa.asset_id = lin.DOWNSTREAM_ASSET_ID
JOIN get_asset_info AS usa ON usa.asset_id = lin.UPSTREAM_ASSET_ID
{% if params.is_full %}
{% else %}
WHERE DATE(lin.LAST_MODIFIED_AT) >= DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL 1 DAY)
{% endif %}
;
