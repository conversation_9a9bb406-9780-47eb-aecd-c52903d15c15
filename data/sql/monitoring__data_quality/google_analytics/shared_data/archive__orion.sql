-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE start_date DATE DEFAULT DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL 2 DAY);
DECLARE end_date DATE DEFAULT DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL 1 DAY);

{% if params.is_full -%}
SET start_date = DATE("{{ params.start_date }}");
SET end_date = DATE("{{ params.end_date }}");
{% endif %}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.ga_events_orion_monitoring`(
    observation_date                        DATE      NOT NULL  OPTIONS(description="Observation date in format 'YYYY-MM-DD'"),
    payload                                 ARRAY<
        STRUCT<
            property                                STRUCT<
                id                                      INT64     NOT NULL  OPTIONS(description="Property ID. For more information, refer to https://docs.google.com/spreadsheets/d/1YSLpOX02vMZoRScP1nMfjnNeUQ9aqnIcM-j9bg1Yqxo/edit#gid=2143015465"),
                platform                                STRING    NOT NULL  OPTIONS(description="Property platform. Possible values = ['WEB', 'APP']"),
                brand_trigram                           STRING    NOT NULL  OPTIONS(description="Brand trigram linked to the property. For more information, refer to https://docs.google.com/spreadsheets/d/1YSLpOX02vMZoRScP1nMfjnNeUQ9aqnIcM-j9bg1Yqxo/edit#gid=2143015465"),
                country                                 STRING    NOT NULL  OPTIONS(description="Country linked to the property. For more information, refer to https://docs.google.com/spreadsheets/d/1YSLpOX02vMZoRScP1nMfjnNeUQ9aqnIcM-j9bg1Yqxo/edit#gid=2143015465"),
                section                                 STRING    NOT NULL  OPTIONS(description="Section to linked to the property. For more information, refer to https://docs.google.com/spreadsheets/d/1YSLpOX02vMZoRScP1nMfjnNeUQ9aqnIcM-j9bg1Yqxo/edit#gid=2143015465"),
                slug                                    STRING    NOT NULL  OPTIONS(description="Property slug: Concatenation of brand trigram, platform, country and section")
            >                                                           OPTIONS(description="Property data"),
            event                                   STRUCT<
                event_number                            INT64     NOT NULL  OPTIONS(description="Number of events"),
                null_name_number                        INT64     NOT NULL  OPTIONS(description="Number of events with event_name NULL"),
                view_number                             INT64     NOT NULL  OPTIONS(description="If platform = 'WEB', number of 'page_view' events, otherwise number of 'screen_view' events"),
                click_number                            INT64     NOT NULL  OPTIONS(description="Number of 'click' events. Only available for WEB properties"),
                login_number                            INT64     NOT NULL  OPTIONS(description="Number of 'login' events"),
                signup_number                           INT64     NOT NULL  OPTIONS(description="Number of 'signup' events"),
                pmc_session_start_number                INT64     NOT NULL  OPTIONS(description="Number of 'pmc_session_start' events"),
                null_page_location_number               INT64     NOT NULL  OPTIONS(description="Number of events with page location NULL. Only available for WEB properties"),
                null_name_perc                          FLOAT64   NOT NULL  OPTIONS(description="Number of events with 'event_name' NULL / total events"),
                view_perc                               FLOAT64   NOT NULL  OPTIONS(description="If platform = 'WEB', number of 'page_view' events, otherwise number of 'screen_view' events / total events"),
                click_perc                              FLOAT64   NOT NULL  OPTIONS(description="Number of click events / total events"),
                login_perc                              FLOAT64   NOT NULL  OPTIONS(description="Number of 'login' events / total events"),
                signup_perc                             FLOAT64   NOT NULL  OPTIONS(description="Number of 'signup' events / total events"),
                pmc_session_start_perc                  FLOAT64   NOT NULL  OPTIONS(description="Number of 'pmc_session_start' events / total events"),
                null_page_location_perc                 FLOAT64   NOT NULL  OPTIONS(description="Number of events with page location NULL / total events. Only available for WEB properties")
            >                                                           OPTIONS(description="Event-level monitoring data"),
            session                                 STRUCT<
                session_number                          INT64     NOT NULL  OPTIONS(description="Number of distinct sessions, calculated by concatenating user_pseudo_id and ga_session_id"),
                no_engagement_time_number               INT64     NOT NULL  OPTIONS(description="Number of distinct sessions with engagement duration = 0s"),
                avg_effective_engagement_time_sec       FLOAT64   NOT NULL  OPTIONS(description="Average effective engagement time in seconds. We only consider engaged sessions"),
                avg_engagement_time_sec                 FLOAT64   NOT NULL  OPTIONS(description="Average engagement time in seconds"),
                engaged_number                          INT64     NOT NULL  OPTIONS(description="Number of distinct engaged sessions. \n"
                                                                                              ||"For more information, refer to https://support.google.com/analytics/answer/12798876?hl=en#:~:text=An%20engaged%20session%20is%20a,least%202%20pageviews%20or%20screenviews."),
                logged_number                           INT64     NOT NULL  OPTIONS(description="Number of distinct logged sessions. A session is considered as logged if 'web_pmc_id' is NOT NULL"),
                avg_duration_sec                        FLOAT64   NOT NULL  OPTIONS(description="Average duration, in seconds.\n"
                                                                                              ||"Duration calculation method: Last event timestamp - first event timestamp \n"
                                                                                              ||"If the session contains only one session, we use the sum of engagement time instead"),
                avg_event_by_session                    FLOAT64   NOT NULL  OPTIONS(description="Average number of events by session"),
                no_duration_number                      INT64     NOT NULL  OPTIONS(description="Number of distinct sessions with total duration = 0s"),
                long_duration_number                    INT64     NOT NULL  OPTIONS(description="Number of distinct sessions with an abnormally long duration (longer than {{ params.long_session_duration }} seconds)"),
                no_engagement_time_perc                 FLOAT64   NOT NULL  OPTIONS(description="Number of distinct sessions with engagement duration = 0s / total sessions"),
                engaged_perc                            FLOAT64   NOT NULL  OPTIONS(description="Number of distinct engaged sessions / total sessions. \n"
                                                                                              ||"For more information, refer to https://support.google.com/analytics/answer/12798876?hl=en#:~:text=An%20engaged%20session%20is%20a,least%202%20pageviews%20or%20screenviews."),
                logged_perc                             FLOAT64   NOT NULL  OPTIONS(description="Number of distinct logged sessions / total sessions. A session is considered as logged if 'web_pmc_id' is NOT NULL"),
                no_duration_perc                        FLOAT64   NOT NULL  OPTIONS(description="Number of distinct sessions with total duration = 0s / total sessions"),
                long_duration_perc                      FLOAT64   NOT NULL  OPTIONS(description="Number of distinct sessions with an abnormally long duration (longer than {{ params.long_session_duration }} seconds) / total sessions")
            >                                                           OPTIONS(description="Session-level monitoring data"),
            user                                    STRUCT<
                user_number                             INT64     NOT NULL  OPTIONS(description="Number of distinct user_pseudo_id"),
                avg_session_by_user                     FLOAT64   NOT NULL  OPTIONS(description="Number of sessions / number of users")
            >                                                           OPTIONS(description="User-level monitoring data"),
            duplicate                               STRUCT<
                duplicate_number                        INT64     NOT NULL  OPTIONS(description="Number of duplicates")
            >                                                           OPTIONS(description="Duplicate monitoring data")
        >
    >                                       OPTIONS(description="Nested field containing data for monitoring purpose"),
    PRIMARY KEY (observation_date) NOT ENFORCED
)
PARTITION BY observation_date
OPTIONS(
    partition_expiration_days=365, -- keep partitions for 365 days
    description="This table contains reactivity KPIs calculated using shared data \n"
              ||"Main KPIs: Event number, session number, distinct pseudo user number, view number"
              ||"\n\n"
              ||"DAG: {{ dag.dag_id }}"
              ||"\n\n"
              ||"Sync: Daily",
    labels=[
        ("project", "data_quality"),
        ("scope", "ga4"),
        ("owner", "rodrigo_santana"),
        ("backup_owner", "anes_ben_ramdhan"),
        ("context", "production"),
        ("version", "2")
    ]
);

DELETE FROM `{{ params.bq_project }}.generated_data.ga_events_orion_monitoring`
WHERE observation_date BETWEEN start_date AND end_date;

INSERT INTO `{{ params.bq_project }}.generated_data.ga_events_orion_monitoring`
WITH compute_duplicates AS (
    -- count doubles at global-level using:
    -- * property data (SiteTagGan)
    -- * event data (Date, EventName, EventDatetime, NewVisits, PageHitId, HitNumber)
    -- * user data (FullVisitorID)
    SELECT
        Date,
        SiteTagGan,
        SUM(identical_row_number - 1) AS duplicate_number                                                               -- one of the rows is the "original" and the others are doubles
    FROM (
        SELECT
            SiteTagGan,
            Date,
            EventName,
            user.FullVisitorID,
            EventDatetime,
            user.NewVisits,
            PageHitId,
            HitNumber,
            SessionId,
            COUNT(*) AS identical_row_number
        FROM `{{ params.bq_project }}.share_orion.ga4_events`
        LEFT JOIN UNNEST(Users) AS user
        WHERE Date BETWEEN start_date AND end_date
        GROUP BY ALL
        HAVING COUNT(*) > 1
    )
    GROUP BY ALL
),

aggregate_session_data AS (
    -- aggregate data at session-level
    -- session_duration:
    --   * difference between first and last event in the session
    --   * if there is only one event in the session, use the engagement time
    SELECT
        Date,
        SiteTagGan,
        SessionId,
        LOGICAL_OR(IdPMC IS NOT NULL OR EventName = "pmc_session_start") AS is_logged,
        SUM(TotalTimeOnPage) AS engagement_time_sec,
        COUNT(EventName) AS event_number
    FROM `{{ params.bq_project }}.share_orion.ga4_events`
    WHERE
        Date BETWEEN start_date AND end_date
        AND EventName != "session_start"
    GROUP BY ALL
),

count_engaged_sessions AS (
    SELECT
        Date,
        SiteTagGan,
        SessionId,
        LOGICAL_OR(Engaged = 1) AS is_engaged,
        TIMESTAMP_DIFF(MAX(EventDatetime), MIN(EventDatetime), SECOND) AS duration_sec
    FROM `{{ params.bq_project }}.share_orion.ga4_events`
    WHERE Date BETWEEN start_date AND end_date
    GROUP BY ALL
),

session_kpis AS (
    -- use session-aggregated data to compute monitoring KPIs
    SELECT
        Date,
        SiteTagGan,
        COUNT(SessionId) AS session_number,
        COUNTIF(engagement_time_sec = 0) AS no_engagement_time_number,
        AVG(IF(engagement_time_sec > 0, engagement_time_sec, NULL)) AS avg_effective_engagement_time_sec,
        AVG(engagement_time_sec) AS avg_engagement_time_sec,
        COUNTIF(is_engaged) AS engaged_number,
        COUNTIF(is_logged) AS logged_number,
        AVG(event_number) AS avg_event_by_session,
        AVG(duration_sec) AS avg_duration_sec,
        COUNTIF(duration_sec = 0) AS no_duration_number,
        COUNTIF(duration_sec > {{ params.long_session_duration }}) AS long_duration_number
    FROM aggregate_session_data AS asd
    LEFT JOIN count_engaged_sessions AS ces USING(Date, SiteTagGan, SessionId)
    GROUP BY ALL
),

event_kpis AS (
    -- compute event-level monitoring KPIs
    SELECT
        Date,
        SiteTagGan,
        Render,
        SiteCode,
        SiteCountry,
        SiteSection,
        COUNT(EventName) AS event_number,
        COUNTIF(EventName IS NULL) AS null_number,
        COUNTIF(EventName IN ("page_view", "screen_view")) AS view_number,
        COUNTIF(EventName = "click") AS click_number,
        COUNTIF(EventName = "login") AS login_number,
        COUNTIF(EventName = "sign_up") AS signup_number,
        COUNTIF(EventName = "pmc_session_start") AS pmc_session_start_number,
        COUNTIF(Url IS NULL) AS null_page_location_number
    FROM `{{ params.bq_project }}.share_orion.ga4_events`
    WHERE
        Date BETWEEN start_date AND end_date
        AND EventName != "session_start"
    GROUP BY ALL
),

user_kpis AS (
    -- compute distinct users
    SELECT
        Date,
        SiteTagGan,
        COUNT(DISTINCT user.FullVisitorID) AS user_number
    FROM `{{ params.bq_project }}.share_orion.ga4_events`
    LEFT JOIN UNNEST(Users) AS user
    WHERE
        Date BETWEEN start_date AND end_date
        AND EventName != "session_start"
    GROUP BY ALL
)

SELECT
    Date AS observation_date,
    ARRAY_AGG(
        STRUCT(
            STRUCT(
                ek.SiteTagGan AS id,
                ek.Render AS platform,
                ek.SiteCode AS brand_trigram,
                ek.SiteCountry AS country,
                ek.SiteSection AS section,
                CONCAT(ek.SiteCode, "_", ek.Render, "_", ek.SiteCountry, "_", ek.SiteSection) AS slug
            ) AS property,
            STRUCT(
                IFNULL(ek.event_number, 0) AS event_number,
                IFNULL(ek.null_number, 0) AS null_name_number,
                IFNULL(ek.view_number, 0) AS view_number,
                IFNULL(ek.click_number, 0) AS click_number,
                IFNULL(ek.login_number, 0) AS login_number,
                IFNULL(ek.signup_number, 0) AS signup_number,
                IFNULL(ek.pmc_session_start_number, 0) AS pmc_session_start_number,
                IFNULL(ek.null_page_location_number, 0) AS null_page_location_number,
                IFNULL(SAFE_DIVIDE(ek.null_number, ek.event_number), 0) AS null_name_perc,
                IFNULL(SAFE_DIVIDE(ek.view_number, ek.event_number), 0) AS view_perc,
                IFNULL(SAFE_DIVIDE(ek.click_number, ek.event_number), 0) AS click_perc,
                IFNULL(SAFE_DIVIDE(ek.login_number, ek.event_number), 0) AS login_perc,
                IFNULL(SAFE_DIVIDE(ek.signup_number, ek.event_number), 0) AS signup_perc,
                IFNULL(SAFE_DIVIDE(ek.pmc_session_start_number, ek.event_number), 0) AS pmc_session_start_perc,
                IFNULL(SAFE_DIVIDE(ek.null_page_location_number, ek.event_number), 0) AS null_page_location_perc
            ) AS event,
            STRUCT(
                IFNULL(sk.session_number, 0) AS session_number,
                IFNULL(sk.no_engagement_time_number, 0) AS no_engagement_time_number,
                IFNULL(sk.avg_effective_engagement_time_sec, 0) AS avg_effective_engagement_time_sec,
                IFNULL(sk.avg_engagement_time_sec, 0) AS avg_engagement_time_sec,
                IFNULL(sk.engaged_number, 0) AS engaged_number,
                IFNULL(sk.logged_number, 0) AS logged_number,
                IFNULL(sk.avg_duration_sec, 0) AS avg_duration_sec,
                IFNULL(sk.avg_event_by_session, 0) AS avg_event_by_session,
                IFNULL(sk.no_duration_number, 0) AS no_duration_number,
                IFNULL(sk.long_duration_number, 0) AS long_duration_number,
                IFNULL(SAFE_DIVIDE(sk.no_engagement_time_number, sk.session_number), 0) AS no_engagement_time_perc,
                IFNULL(SAFE_DIVIDE(sk.engaged_number, sk.session_number), 0) AS engaged_perc,
                IFNULL(SAFE_DIVIDE(sk.logged_number, sk.session_number), 0) AS logged_perc,
                IFNULL(SAFE_DIVIDE(sk.no_duration_number, sk.session_number), 0) AS no_duration_perc,
                IFNULL(SAFE_DIVIDE(sk.long_duration_number, sk.session_number), 0) AS long_duration_perc
            ) AS session,
            STRUCT(
                IFNULL(uk.user_number, 0) AS user_number,
                IFNULL(SAFE_DIVIDE(sk.session_number, uk.user_number), 0) AS avg_session_by_user
            ) AS user,
            STRUCT(
                IFNULL(cd.duplicate_number, 0) AS duplicate_number
            ) AS duplicate
        )
    ) AS payload
FROM event_kpis AS ek
LEFT JOIN session_kpis AS sk USING(Date, SiteTagGan)
LEFT JOIN user_kpis AS uk USING(Date, SiteTagGan)
LEFT JOIN compute_duplicates AS cd USING(Date, SiteTagGan)
GROUP BY ALL;
