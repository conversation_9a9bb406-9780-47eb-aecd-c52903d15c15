-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE start_date DATE DEFAULT DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL 2 DAY);
DECLARE end_date DATE DEFAULT DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL 1 DAY);

{% if params.is_full -%}
SET start_date = DATE("{{ params.start_date }}");
SET end_date = DATE("{{ params.end_date }}");
{% endif %}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.workspace.ga_events_monitoring_refined_{{ params.platform }}_{{ params.brand_trigram }}_{{ params.country }}_{{ params.section }}`(
    observation_date                        DATE      NOT NULL  OPTIONS(description="Observation date in format 'YYYY-MM-DD'"),
    property                                STRUCT<
        id                                      INT64     NOT NULL  OPTIONS(description="Property ID. For more information, refer to https://docs.google.com/spreadsheets/d/1YSLpOX02vMZoRScP1nMfjnNeUQ9aqnIcM-j9bg1Yqxo/edit#gid=2143015465"),
        platform                                STRING    NOT NULL  OPTIONS(description="Property platform. Possible values = ['WEB', 'APP']"),
        brand_trigram                           STRING    NOT NULL  OPTIONS(description="Brand trigram linked to the property. For more information, refer to https://docs.google.com/spreadsheets/d/1YSLpOX02vMZoRScP1nMfjnNeUQ9aqnIcM-j9bg1Yqxo/edit#gid=2143015465"),
        country                                 STRING    NOT NULL  OPTIONS(description="Country linked to the property. For more information, refer to https://docs.google.com/spreadsheets/d/1YSLpOX02vMZoRScP1nMfjnNeUQ9aqnIcM-j9bg1Yqxo/edit#gid=2143015465"),
        section                                 STRING    NOT NULL  OPTIONS(description="Section to linked to the property. For more information, refer to https://docs.google.com/spreadsheets/d/1YSLpOX02vMZoRScP1nMfjnNeUQ9aqnIcM-j9bg1Yqxo/edit#gid=2143015465"),
        slug                                    STRING    NOT NULL  OPTIONS(description="Property slug: Concatenation of brand trigram, platform, country and section")
    >                                                           OPTIONS(description="Property data"),
    event                                   STRUCT<
        event_number                            INT64     NOT NULL  OPTIONS(description="Number of events with event_name NOT NULL"),
        view_number                             INT64     NOT NULL  OPTIONS(description="If 'platform' = 'WEB', number of 'page_view' events, otherwise number of 'screen_view' events"),
        click_number                            INT64     NOT NULL  OPTIONS(description="Number of 'click' events. Only available for WEB properties"),
        login_number                            INT64     NOT NULL  OPTIONS(description="Number of 'login' events"),
        signup_number                           INT64     NOT NULL  OPTIONS(description="Number of 'signup' events"),
        pmc_session_start_number                INT64     NOT NULL  OPTIONS(description="Number of 'pmc_session_start' events"),
        null_page_location_number               INT64     NOT NULL  OPTIONS(description="Number of events with page location NULL. Only available for WEB properties"),
        null_clicked_url_number                 INT64     NOT NULL  OPTIONS(description="Number of 'click' events with clicked URL NULL. Only available for WEB properties"),
        view_perc                               FLOAT64   NOT NULL  OPTIONS(description="If 'platform' = 'WEB', number of 'page_view' events, otherwise number of 'screen_view' events"),
        click_perc                              FLOAT64   NOT NULL  OPTIONS(description="Number of click events / total events"),
        login_perc                              FLOAT64   NOT NULL  OPTIONS(description="Number of 'login' events / total events"),
        signup_perc                             FLOAT64   NOT NULL  OPTIONS(description="Number of 'signup' events / total events"),
        pmc_session_start_perc                  FLOAT64   NOT NULL  OPTIONS(description="Number of 'pmc_session_start' events / total events"),
        null_page_location_perc                 FLOAT64   NOT NULL  OPTIONS(description="Number of events with page location NULL / total events. Only available for WEB properties"),
        null_clicked_url_perc                   FLOAT64   NOT NULL  OPTIONS(description="Number of 'click' events with clicked URL NULL / total events. Only available for WEB properties")
    >                                                           OPTIONS(description="Event-level monitoring data"),
    session                                 STRUCT<
        session_number                          INT64     NOT NULL  OPTIONS(description="Number of distinct sessions, calculated by concatenating user_pseudo_id and ga_session_id"),
        no_engagement_time_number               INT64     NOT NULL  OPTIONS(description="Number of distinct sessions with engagement duration = 0s"),
        avg_effective_engagement_time_sec       FLOAT64   NOT NULL  OPTIONS(description="Average effective engagement time in seconds. We only consider engaged sessions"),
        avg_engagement_time_sec                 FLOAT64   NOT NULL  OPTIONS(description="Average engagement time in seconds, of all sessions whose duration is greater than 0s"),
        engaged_number                          INT64     NOT NULL  OPTIONS(description="Number of distinct engaged sessions. For more information, refer to https://support.google.com/analytics/answer/12798876?hl=en#:~:text=An%20engaged%20session%20is%20a,least%202%20pageviews%20or%20screenviews."),
        logged_number                           INT64     NOT NULL  OPTIONS(description="Number of distinct logged sessions. A session is considered as logged if 'web_pmc_id' is NOT NULL"),
        avg_duration_sec                        FLOAT64   NOT NULL  OPTIONS(description="Average duration, in seconds, of all sessions whose duration is greater than 0s"),
        avg_event_by_session                    FLOAT64   NOT NULL  OPTIONS(description="Average number of events in the session"),
        no_duration_number                      INT64     NOT NULL  OPTIONS(description="Number of distinct sessions with total duration = 0s"),
        long_duration_number                    INT64     NOT NULL  OPTIONS(description="Number of distinct sessions with an abnormally long duration (longer than {{ params.long_session_duration }} seconds)"),
        no_engagement_time_perc                 FLOAT64   NOT NULL  OPTIONS(description="Number of distinct sessions with engagement duration = 0s / total sessions"),
        engaged_perc                            FLOAT64   NOT NULL  OPTIONS(description="Number of distinct engaged sessions / total sessions. For more information, refer to https://support.google.com/analytics/answer/12798876?hl=en#:~:text=An%20engaged%20session%20is%20a,least%202%20pageviews%20or%20screenviews."),
        logged_perc                             FLOAT64   NOT NULL  OPTIONS(description="Number of distinct logged sessions / total sessions. A session is considered as logged if 'web_pmc_id' is NOT NULL"),
        no_duration_perc                        FLOAT64   NOT NULL  OPTIONS(description="Number of distinct sessions with total duration = 0s / total sessions"),
        long_duration_perc                      FLOAT64   NOT NULL  OPTIONS(description="Number of distinct sessions with an abnormally long duration (longer than {{ params.long_session_duration }} seconds) / total sessions")
    >                                                           OPTIONS(description="Session-level monitoring data"),
    user                                    STRUCT<
        user_number                             INT64     NOT NULL  OPTIONS(description="Number of distinct 'pseudo_user_id'"),
        avg_session_by_user                     FLOAT64   NOT NULL  OPTIONS(description="Number of sessions / number of users")
    >                                                           OPTIONS(description="User-level monitoring data"),
    duplicate                               STRUCT<
        session_number                          INT64     NOT NULL  OPTIONS(description="Number of duplicates at session-level"),
        global_number                           INT64     NOT NULL  OPTIONS(description="Number of duplicates at global-level")
    >                                                           OPTIONS(description="Duplicate monitoring data"),
    PRIMARY KEY (observation_date) NOT ENFORCED
)
PARTITION BY observation_date
OPTIONS(
    partition_expiration_days=365, -- keep partitions for 365 days
    description="This table contains reactivity KPIs calculated using refined data for {{ params.brand_trigram }} in country {{ params.country }}, platform {{ params.platform }} and section {{ params.section }}\n"
              ||"Main KPIs: Event number, session number, distinct pseudo user number, view number"
              ||"\n\n"
              ||"DAG: {{ dag.dag_id }}"
              ||"\n\n"
              ||"Sync: Daily",
    labels=[
        ('project', 'data_quality'),
        ('scope', 'ga4'),
        ('owner', 'rodrigo_santana'),
        ('backup_owner', 'anes_ben_ramdhan'),
        ('context', 'production'),
        ('version', '4')
    ]
);

DELETE FROM `{{ params.bq_project }}.workspace.ga_events_monitoring_refined_{{ params.platform }}_{{ params.brand_trigram }}_{{ params.country }}_{{ params.section }}`
WHERE observation_date BETWEEN start_date AND end_date;

INSERT INTO `{{ params.bq_project }}.workspace.ga_events_monitoring_refined_{{ params.platform }}_{{ params.brand_trigram }}_{{ params.country }}_{{ params.section }}`
WITH double_global AS (
    -- count doubles at global-level using:
    -- * visit_date
    -- * property_id
    -- * user_pseudo_id
    SELECT
        visit_date,
        property_id,
        SUM(identical_row_number - 1) AS global_double_number                                                           -- one of the rows is the "original" and the others are doubles
    FROM (
        SELECT
            visit_date,
            property_data.property_id,
            user_pseudo_id,
            COUNT(*) AS identical_row_number
        FROM `{{ params.bq_project }}.refined_data.ga_events_{{ params.platform }}_{{ params.brand_trigram }}_{{ params.country }}_{{ params.section }}`
        WHERE visit_date BETWEEN start_date AND end_date
        GROUP BY ALL
        HAVING COUNT(*) > 1
    )
    GROUP BY ALL
),

double_session AS (
    -- count doubles at session-level using:
    -- * visit_date
    -- * property_id
    -- * user_pseudo_id
    -- * session_id
    SELECT
        visit_date,
        property_id,
        SUM(identical_row_number - 1) AS session_double_number                                                          -- one of the rows is the "original" and the others are doubles
    FROM (
        SELECT
            visit_date,
            property_data.property_id,
            user_pseudo_id,
            session.session_id,
            COUNT(*) AS identical_row_number
        FROM `{{ params.bq_project }}.refined_data.ga_events_{{ params.platform }}_{{ params.brand_trigram }}_{{ params.country }}_{{ params.section }}`
        LEFT JOIN UNNEST(session_data) AS session
        WHERE visit_date BETWEEN start_date AND end_date
        GROUP BY ALL
        HAVING COUNT(*) > 1
    )
    GROUP BY ALL
),

session_data AS (
    -- aggregate data at session-level:
    -- * sum total engagement time by session
    -- * compute session duration
    -- * return session-related data
    SELECT
        visit_date,
        property_data.property_id,
        session_id,
        TIMESTAMP_DIFF(session_end_datetime, session_start_datetime, SECOND) AS session_duration,
        session.is_logged_session,
        session.is_engaged_session,
        COUNT(event.event_name) AS event_number,
        SUM(event.engagement_time_in_seconds) AS total_engagement_time_sec
    FROM `{{ params.bq_project }}.refined_data.ga_events_{{ params.platform }}_{{ params.brand_trigram }}_{{ params.country }}_{{ params.section }}`
    LEFT JOIN UNNEST(session_data) AS session
    {% if params.platform == "WEB" %}
    LEFT JOIN UNNEST(session.page_data) AS data
    {% else %}
    LEFT JOIN UNNEST(session.screen_data) AS data
    {% endif %}
    LEFT JOIN UNNEST(data.event_info) AS event
    WHERE
        visit_date BETWEEN start_date AND end_date
        AND event_name != "session_start"
    GROUP BY ALL
),

session_kpis AS (
    -- use session-aggregated data to compute monitoring KPIs
    SELECT
        visit_date,
        property_id,
        COUNT(session_id) AS session_number,
        COUNTIF(total_engagement_time_sec = 0) AS no_engagement_time_number,
        AVG(IF(total_engagement_time_sec > 0, total_engagement_time_sec, NULL)) AS avg_effective_engagement_time_sec,
        AVG(total_engagement_time_sec) AS avg_engagement_time_sec,
        COUNTIF(is_engaged_session) AS engaged_number,
        COUNTIF(is_logged_session) AS logged_number,
        AVG(event_number) AS avg_event_by_session,
        AVG(session_duration) AS avg_duration_sec,
        COUNTIF(session_duration = 0) AS no_duration_number,
        COUNTIF(session_duration > {{ params.long_session_duration }}) AS long_duration_number
    FROM session_data
    GROUP BY ALL
),

event_data AS (
    -- aggregate data at event-level
    SELECT
        visit_date,
        property_data.property_id,
        property_data.platform,
        property_data.brand_trigram,
        property_data.country,
        property_data.section,
        event.event_name,
    {% if params.platform == "WEB" %}
        event.page_location,
        event.click_info.click_link_url
    {% else %}
        CAST(NULL AS STRING) AS page_location,                                                                          -- available only for WEB
        CAST(NULL AS STRING) AS click_link_url                                                                          -- available only for WEB
    {% endif %}
    FROM `{{ params.bq_project }}.refined_data.ga_events_{{ params.platform }}_{{ params.brand_trigram }}_{{ params.country }}_{{ params.section }}`
    LEFT JOIN UNNEST(session_data) AS session
    {% if params.platform == "WEB" %}
    LEFT JOIN UNNEST(session.page_data) AS data
    {% else %}
    LEFT JOIN UNNEST(session.screen_data) AS data
    {% endif %}
    LEFT JOIN UNNEST(data.event_info) AS event
    WHERE
        visit_date BETWEEN start_date AND end_date
        AND event_name != "session_start"
),

event_kpis AS (
    -- use event-level aggregated data to compute monitoring KPIs
    SELECT
        visit_date,
        property_id,
        brand_trigram,
        country,
        platform,
        section,
        COUNT(event_name) AS event_number,
        COUNTIF(event_name IN ("screen_view", "page_view")) AS view_number,
        COUNTIF(event_name = "click") AS click_number,
        COUNTIF(event_name  = "login") AS login_number,
        COUNTIF(event_name = "sign_up") AS signup_number,
        COUNTIF(event_name = "pmc_session_start") AS pmc_session_start_number,
        COUNTIF(platform = "WEB" AND page_location IS NULL) AS null_page_location_number,
        COUNTIF(platform = "WEB" AND event_name = "click" AND click_link_url IS NULL) AS null_clicked_url_number
    FROM event_data
    GROUP BY ALL
),

user_kpis AS (
    -- calculate user-level monitoring KPIs
    SELECT
        visit_date,
        property_data.property_id,
        COUNT(DISTINCT user_pseudo_id) user_number
    FROM `{{ params.bq_project }}.refined_data.ga_events_{{ params.platform }}_{{ params.brand_trigram }}_{{ params.country }}_{{ params.section }}`
    LEFT JOIN UNNEST(session_data) AS session
    {% if params.platform == "WEB" %}
    LEFT JOIN UNNEST(session.page_data) AS data
    {% else %}
    LEFT JOIN UNNEST(session.screen_data) AS data
    {% endif %}
    LEFT JOIN UNNEST(data.event_info) AS event
    WHERE
        visit_date BETWEEN start_date AND end_date
        AND event_name != "session_start"
    GROUP BY ALL
)

SELECT
    visit_date AS observation_date,
    -- property data
    STRUCT(
        ek.property_id AS id,
        ek.platform,
        ek.brand_trigram,
        ek.country,
        ek.section,
        CONCAT(ek.brand_trigram, "_", ek.platform, "_", ek.country, "_", ek.section) AS slug
    ) AS property_data,
    -- event kpis
    STRUCT(
        IFNULL(ek.event_number, 0) AS event_number,
        IFNULL(ek.view_number, 0) AS view_number,
        IFNULL(ek.click_number, 0) AS click_number,
        IFNULL(ek.login_number, 0) AS login_number,
        IFNULL(ek.signup_number, 0) AS signup_number,
        IFNULL(ek.pmc_session_start_number, 0) AS pmc_session_start_number,
        IFNULL(ek.null_page_location_number, 0) AS null_page_location_number,
        IFNULL(ek.null_clicked_url_number, 0) AS null_clicked_url_number,
        IFNULL(SAFE_DIVIDE(ek.view_number, ek.event_number), 0) AS view_perc,
        IFNULL(SAFE_DIVIDE(ek.click_number, ek.event_number), 0) AS click_perc,
        IFNULL(SAFE_DIVIDE(ek.login_number, ek.event_number), 0) AS login_perc,
        IFNULL(SAFE_DIVIDE(ek.signup_number, ek.event_number), 0) AS signup_perc,
        IFNULL(SAFE_DIVIDE(ek.pmc_session_start_number, ek.event_number), 0) AS pmc_session_start_perc,
        IFNULL(SAFE_DIVIDE(ek.null_page_location_number, ek.event_number), 0) AS null_page_location_perc,
        IFNULL(SAFE_DIVIDE(ek.null_clicked_url_number, ek.click_number), 0) AS null_clicked_url_perc
    ) AS event_data,
    -- session kpis
    STRUCT(
        IFNULL(sk.session_number, 0) AS session_number,
        IFNULL(sk.no_engagement_time_number, 0) AS no_engagement_time_number,
        IFNULL(sk.avg_effective_engagement_time_sec, 0) AS avg_effective_engagement_time_sec,
        IFNULL(sk.avg_engagement_time_sec, 0) AS avg_engagement_time_sec,
        IFNULL(sk.engaged_number, 0) AS engaged_number,
        IFNULL(sk.logged_number, 0) AS logged_number,
        IFNULL(sk.avg_duration_sec, 0) AS avg_duration_sec,
        IFNULL(sk.avg_event_by_session, 0) AS avg_event_by_session,
        IFNULL(sk.no_duration_number, 0) AS no_duration_number,
        IFNULL(sk.long_duration_number, 0) AS long_duration_number,
        IFNULL(SAFE_DIVIDE(sk.no_engagement_time_number, sk.session_number), 0) AS no_engagement_time_perc,
        IFNULL(SAFE_DIVIDE(sk.engaged_number, sk.session_number), 0) AS engaged_perc,
        IFNULL(SAFE_DIVIDE(sk.logged_number, sk.session_number), 0) AS logged_perc,
        IFNULL(SAFE_DIVIDE(sk.no_duration_number, sk.session_number), 0) AS no_duration_perc,
        IFNULL(SAFE_DIVIDE(sk.long_duration_number, sk.session_number), 0) AS long_duration_perc
    ) AS session,
    STRUCT(
        IFNULL(uk.user_number, 0) AS user_number,
        IFNULL(SAFE_DIVIDE(sk.session_number, uk.user_number), 0) AS avg_session_by_user
    ) AS user,
    STRUCT(
        IFNULL(ds.session_double_number, 0) AS session_number,
        IFNULL(dg.global_double_number, 0) AS global_number
    ) AS duplicate
FROM event_kpis As ek
LEFT JOIN session_kpis AS sk USING(property_id, visit_date)
LEFT JOIN user_kpis AS uk USING(property_id, visit_date)
LEFT JOIN double_session AS ds USING(property_id, visit_date)
LEFT JOIN double_global AS dg USING(property_id, visit_date);
