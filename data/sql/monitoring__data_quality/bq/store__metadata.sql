-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.store__bq_metadata`(
    observation_date                  DATE            NOT NULL    OPTIONS(description="Observation date"),
    data                              ARRAY<STRUCT<
        project                           STRING          NOT NULL    OPTIONS(description="Project name in the format `pm-{{ params.env }}-<project>`"),
        dataset                           STRING          NOT NULL    OPTIONS(description="Dataset name"),
        table                             STRING          NOT NULL    OPTIONS(description="Table name"),
        type                              STRING          NOT NULL    OPTIONS(description="Table type"),
        creation_time                     TIMESTAMP       NOT NULL    OPTIONS(description="Table create timestamp"),
        last_modified_time                TIMESTAMP       NOT NULL    OPTIONS(description="Table last update timestamp"),
        day_since_creation_number         INT64                       OPTIONS(description="Date difference between creation date and observation date, in days"),
        is_documented                     BOOLEAN                     OPTIONS(description="True if the table and all its columns have non-empty descriptions"),
        has_description                   BOOLEAN                     OPTIONS(description="True if the table has a non-empty description"),
        column_number                     INT64                       OPTIONS(description="Number of columns"),
        column_with_description_number    INT64                       OPTIONS(description="Number of columns with a non-empty description"),
        all_columns_with_description      BOOLEAN                     OPTIONS(description="True if all columns in the table have a non-empty description"),
        is_partitioned                    BOOLEAN                     OPTIONS(description="True if the table has a partitioning column"),
        require_filter                    BOOLEAN                     OPTIONS(description="True if the table requires a date filter"),
        has_labels                        BOOLEAN                     OPTIONS(description="True if the table has a non-empty label"),
        labels                            ARRAY<STRUCT<
            key                                 STRING                      OPTIONS(description="Label key"),
            value                               STRING                      OPTIONS(description="Label value")
            >
        >                                                             OPTIONS(description="Labels"),

        has_cluster                       BOOLEAN                     OPTIONS(description="True if the table has a clustering column"),
        has_pk                            BOOLEAN                     OPTIONS(description="True if the table has a PK column"),
        nested_column_number              INT64                       OPTIONS(description="Number of nested columns"),
        record_column_number              INT64                       OPTIONS(description="Number of RECORD columns"),
        repeated_column_number            INT64                       OPTIONS(description="Number of REPEATED columns"),
        nullable_column_number            INT64                       OPTIONS(description="Number of NULLABLE columns"),
        pk_column_number                  INT64                       OPTIONS(description="Number of PK columns"),
        clustering_column_number          INT64                       OPTIONS(description="Number of clustering columns"),
        partitioning_column_number        INT64                       OPTIONS(description="Number of partitioning columns"),
        is_tracked                        BOOLEAN                     OPTIONS(description="True if view has the label 'owner': 'bigquery_lab', which indicates that the view is tracked on bigquery-lab"),
        used_table                        ARRAY<STRING>               OPTIONS(description="Source table used to create the view")
        >
    >                                                             OPTIONS(description="Array containing daily metadata KPIs"),
    PRIMARY KEY(observation_date) NOT ENFORCED
)
PARTITION BY observation_date
OPTIONS(
    description="This table contains metadata from BQ datasets for data quality monitoring purposes."
              ||"\n\n"
              ||"DAG: {{ dag.dag_id }}."
              ||"\n\n"
              ||"Sync: Daily.",
    labels=[
        ('scope', 'bq_metadata'),
        ('project', 'data_quality'),
        ('creator', 'rodrigo_santana')
    ]
);


DELETE FROM `{{ params.bq_project }}.generated_data.store__bq_metadata`
WHERE observation_date = DATE("{{ data_interval_end }}");

INSERT INTO `{{ params.bq_project }}.generated_data.store__bq_metadata`
SELECT
    observation_date,
    ARRAY_AGG(
        STRUCT(
            project,
            dataset,
            table,
            type,
            creation_time,
            last_modified_time,
            day_since_creation_number,
            is_documented,
            has_description,
            column_number,
            column_with_description_number,
            all_columns_with_description,
            is_partitioned,
            require_filter,
            has_labels,
            labels,
            has_cluster,
            has_pk,
            nested_column_number,
            record_column_number,
            repeated_column_number,
            nullable_column_number,
            pk_column_number,
            clustering_column_number,
            partitioning_column_number,
            is_tracked,
            used_table
        )
    ) AS data
FROM `{{ params.bq_project }}.temp.extract__bq_metadata_*`
WHERE observation_date = DATE("{{ data_interval_end }}")
GROUP BY ALL;
