-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.temp.extract__bq_metadata_{{ params.project_name }}_{{ params.dataset }}_{{ next_ds_nodash }}`(
    observation_date                  DATE            NOT NULL    OPTIONS(description="Observation date"),
    project                           STRING          NOT NULL    OPTIONS(description="Project name in the format `pm-{{ params.env }}-<project>`"),
    dataset                           STRING          NOT NULL    OPTIONS(description="Dataset name"),
    table                             STRING          NOT NULL    OPTIONS(description="Table name"),
    type                              STRING          NOT NULL    OPTIONS(description="Table type"),
    creation_time                     TIMESTAMP       NOT NULL    OPTIONS(description="Table create timestamp"),
    last_modified_time                TIMESTAMP       NOT NULL    OPTIONS(description="Table last update timestamp"),
    day_since_creation_number         INT64                       OPTIONS(description="Date difference between creation date and observation date, in days"),
    has_description                   BOOLEAN                     OPTIONS(description="True if the table has a non-empty description"),
    has_cluster                       BOOLEAN                     OPTIONS(description="True if the table has a clustering column"),
    has_pk                            BOOLEAN                     OPTIONS(description="True if the table has a PK column"),
    is_partitioned                    BOOLEAN                     OPTIONS(description="True if the table has a partitioning column"),
    require_filter                    BOOLEAN                     OPTIONS(description="True if the table requires a date filter"),
    has_labels                        BOOLEAN                     OPTIONS(description="True if the table has a non-empty label"),
    labels                            ARRAY<STRUCT<
            key                                 STRING                      OPTIONS(description="Label key"),
            value                               STRING                      OPTIONS(description="Label value")
        >
    >                                                             OPTIONS(description="Labels"),
    is_documented                     BOOLEAN                     OPTIONS(description="True if the table and all its columns have non-empty descriptions"),
    column_number                     INT64                       OPTIONS(description="Number of columns"),
    column_with_description_number    INT64                       OPTIONS(description="Number of columns with a non-empty description"),
    all_columns_with_description      BOOLEAN                     OPTIONS(description="True if all columns in the table have a non-empty description"),
    nested_column_number              INT64                       OPTIONS(description="Number of nested columns"),
    record_column_number              INT64                       OPTIONS(description="Number of RECORD columns"),
    repeated_column_number            INT64                       OPTIONS(description="Number of REPEATED columns"),
    nullable_column_number            INT64                       OPTIONS(description="Number of NULLABLE columns"),
    pk_column_number                  INT64                       OPTIONS(description="Number of PK columns"),
    clustering_column_number          INT64                       OPTIONS(description="Number of clustering columns"),
    partitioning_column_number        INT64                       OPTIONS(description="Number of partitioning columns"),
    is_tracked                        BOOLEAN                     OPTIONS(description="True if view has the label 'owner': 'bigquery_lab', which indicates that the view is tracked on bigquery-lab"),
    used_table                        ARRAY<STRING>               OPTIONS(description="Source table used to create the view"),
    PRIMARY KEY(observation_date, project, dataset, table, type) NOT ENFORCED
)
OPTIONS(
    description="This table contains metadata from BQ datasets for data quality monitoring purposes.\n\n"
              ||"DAG: {{ dag.dag_id }}.\n\n"
              ||"Sync: Daily.",
    labels=[
        ('scope', 'bq_metadata'),
        ('project', 'metadata_governance'),
        ('creator', 'rodrigo_santana')
    ]
);

DELETE FROM `{{ params.bq_project }}.temp.extract__bq_metadata_{{ params.project_name }}_{{ params.dataset }}_{{ next_ds_nodash }}`
WHERE observation_date = DATE("{{ data_interval_end }}");

INSERT INTO `{{ params.bq_project }}.temp.extract__bq_metadata_{{ params.project_name }}_{{ params.dataset }}_{{ next_ds_nodash }}`
WITH get_table_options AS (
    SELECT
        table_name AS table,
        option_name,
        option_value
    FROM `pm-{{ params.env }}-{{ params.project_name }}.{{ params.dataset }}.INFORMATION_SCHEMA.TABLE_OPTIONS`
    WHERE
        option_name IN ('description', 'labels', 'require_partition_filter')
        AND option_value IS NOT NULL
),

flag_table_options AS (
    SELECT
        table,
        COUNTIF(option_name = 'description' AND (option_value IS NOT NULL OR option_value != '')) AS has_description,
        COUNTIF(option_name = 'labels' AND (option_value IS NOT NULL OR option_value != '')) AS has_labels,
        COUNTIF(option_name = 'require_partition_filter' AND option_value = 'true') AS require_filter,
        COUNTIF(option_name = 'labels' AND REGEXP_CONTAINS(option_value, r'"owner", "bigquery_lab"')) AS is_tracked
    FROM get_table_options
    GROUP BY ALL
),

extract_table_labels AS (
    SELECT
        table,
        ARRAY_AGG(STRUCT(
        REGEXP_EXTRACT(kv, r'"([^"]+)"') AS `key`,
        REGEXP_EXTRACT(kv, r'"[^"]*",\s*"([^"]+)"') AS `value`
        ) IGNORE NULLS) AS labels
    FROM (
        SELECT
            table,
            SPLIT(option_value, 'STRUCT') AS key_value_pairs
        FROM get_table_options
        WHERE option_name = 'labels'
    ), UNNEST(key_value_pairs) AS kv
    WHERE
        REGEXP_EXTRACT(kv, r'"([^"]+)"') IS NOT NULL
        OR REGEXP_EXTRACT(kv, r'"[^"]*",\s*"([^"]+)"') IS NOT NULL
    GROUP BY ALL
),

get_column_description AS (
    SELECT
        table_name AS table,
        COUNT(column_name) AS column_number,
        COUNTIF(description IS NOT NULL) AS column_with_description_number,
        COUNTIF(REGEXP_CONTAINS(field_path, r'\.')) AS nested_column_number,
        COUNTIF(cfp.data_type LIKE "%STRUCT%") AS record_column_number,
        COUNTIF(cfp.data_type LIKE "ARRAY%") AS repeated_column_number,
        COUNTIF(LOWER(is_nullable) = 'yes') AS nullable_column_number,
        COUNTIF(LOWER(is_partitioning_column) = 'yes') AS partitioning_column_number,
        COUNTIF(clustering_ordinal_position IS NOT NULL) AS clustering_column_number
    FROM `pm-{{ params.env }}-{{ params.project_name }}.{{ params.dataset }}.INFORMATION_SCHEMA.COLUMN_FIELD_PATHS` AS cfp
    LEFT JOIN `pm-{{ params.env }}-{{ params.project_name }}.{{ params.dataset }}.INFORMATION_SCHEMA.COLUMNS` AS ccc
        USING(table_name, column_name)
    GROUP BY ALL
),

extract_useful_data AS (
    -- Get used tables inside SQL script's as array
    SELECT
        table_name AS table,
        REGEXP_EXTRACT_ALL(ddl, r'(?:FROM|JOIN)\s+`?([^\s`]+)`?') AS useful_data_array
    FROM `pm-{{ params.env }}-{{ params.project_name }}.{{ params.dataset }}.INFORMATION_SCHEMA.TABLES` AS ist
    LEFT JOIN `pm-{{ params.env }}-{{ params.project_name }}.{{ params.dataset }}.__TABLES__` AS tbl ON ist.table_name = tbl.table_id
),

get_used_tables AS (
    -- Get unique used table inside SQL script
    SELECT
        table,
        -- Keep tables only; skip CTE
        ARRAY_AGG(DISTINCT IF(NOT CONTAINS_SUBSTR(used_table, "."), NULL, used_table) IGNORE NULLS) AS unique_used_table
    FROM extract_useful_data
    JOIN UNNEST(useful_data_array) AS used_table
    GROUP BY ALL
),

get_creation_time AS (
    SELECT
        table_name AS table,
        table_type AS type,
        TIMESTAMP_MILLIS(last_modified_time) AS last_modified_time,
        TIMESTAMP_MILLIS(tbl.creation_time) AS creation_time,
    FROM `pm-{{ params.env }}-{{ params.project_name }}.{{ params.dataset }}.INFORMATION_SCHEMA.TABLES` AS ist
    LEFT JOIN `pm-{{ params.env }}-{{ params.project_name }}.{{ params.dataset }}.__TABLES__` AS tbl ON ist.table_name = tbl.table_id
    GROUP BY ALL
),

get_pk_number AS (
    SELECT
        table_name AS table,
        COUNT(column_name) AS pk_column_number
    FROM `pm-{{ params.env }}-{{ params.project_name }}.{{ params.dataset }}.INFORMATION_SCHEMA.KEY_COLUMN_USAGE`
    WHERE constraint_name LIKE "%.pk$"
    GROUP BY ALL
)

SELECT
    DATE('{{ data_interval_end }}') AS observation_date,
    "{{ params.project_name }}" AS project,
    "{{ params.dataset }}" AS dataset,
    table,
    type,
    creation_time,
    last_modified_time,
    DATE_DIFF(DATE('{{ data_interval_end }}'), DATE(creation_time), DAY) AS day_since_creation_number,
    fto.table IS NOT NULL AND has_description > 0 AS has_description,
    clustering_column_number > 0 AS has_cluster,
    IFNULL(pk_column_number, 0) > 0 AS has_pk,
    partitioning_column_number > 0 AS is_partitioned,
    fto.table IS NOT NULL AND require_filter > 0 AS require_filter,
    fto.table IS NOT NULL AND has_labels > 0 AS has_labels,
    IF(fto.table IS NOT NULL AND has_labels > 0, etl.labels, NULL) AS labels,
    column_with_description_number = column_number AND fto.table IS NOT NULL AND has_description > 0 AS is_documented,
    column_number,
    column_with_description_number,
    column_with_description_number = column_number AS all_columns_with_description,
    nested_column_number,
    record_column_number,
    repeated_column_number,
    nullable_column_number,
    IFNULL(pk_column_number, 0) AS pk_column_number,
    clustering_column_number,
    partitioning_column_number,
    CASE
        WHEN type = "VIEW" AND is_tracked > 0 THEN TRUE
        WHEN type = "VIEW" AND is_tracked = 0 THEN FALSE
        -- NULL otherwise
    END AS is_tracked,
    unique_used_table AS used_table
FROM get_creation_time AS gct
LEFT JOIN get_column_description AS gcd USING(table)
LEFT JOIN flag_table_options AS fto USING(table)
LEFT JOIN extract_table_labels AS etl USING(table)
LEFT JOIN get_pk_number AS gpn USING(table)
LEFT JOIN get_used_tables AS gut USING(table);