-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE start_date TIMESTAMP DEFAULT TIMESTAMP_SUB(TIMESTAMP_TRUNC(TIMESTAMP("{{ data_interval_end }}"), HOUR), INTERVAL 8 HOUR);
DECLARE end_date TIMESTAMP DEFAULT TIMESTAMP_SUB(TIMESTAMP_TRUNC(TIMESTAMP("{{ data_interval_end }}"), HOUR), INTERVAL 1 MICROSECOND);

{%- if params.is_full -%}
SET start_date = TIMESTAMP("{{ params.start_date }}");
SET end_date = TIMESTAMP("{{ params.end_date }}");
{% endif -%}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.monitoring_prisma_reactivity`
(
    observation_datetime        TIMESTAMP   NOT NULL    OPTIONS(description="Observation timestamp"),
    universe_name               STRING      NOT NULL    OPTIONS(description="Universe name"),
    email_base                  STRUCT<
        email_consent_public_ref    STRING                  OPTIONS(description="Email consent public ref"),
        theme                       STRING                  OPTIONS(description="Theme name for NL-Shopping universes")
        > OPTIONS(description="Email base information"),
    esp_name                    STRING      NOT NULL OPTIONS(description="ESP name"),
    open_volume                 INT64       NOT NULL OPTIONS(description="Open volume hourly"),
    opener_volume               INT64       NOT NULL OPTIONS(description="Opener volume hourly"),
    click_volume                INT64       NOT NULL OPTIONS(description="click volume hourly"),
    clicker_volume              INT64       NOT NULL OPTIONS(description="clicker volume hourly")
)
PARTITION BY DATE(observation_datetime)
CLUSTER BY universe_name, esp_name
OPTIONS(
    description="This table contains aggregated reactivity (open and click) indicators from Prisma.\n"
              ||"Used for monitoring purposes\n\n"
              ||"DAG: {{ dag.dag_id }}.\n\n"
              ||"Sync: every 8 hours",
    labels=[
        ('project', 'monitoring'),
        ('scope', 'email_reactivity'),
        ('creator', 'rodrigo_santana'),
        ('owner', 'mozart')
    ]
);

DELETE FROM `{{ params.bq_project }}.generated_data.monitoring_prisma_reactivity`
WHERE observation_datetime BETWEEN start_date AND end_date;

INSERT INTO `{{ params.bq_project }}.generated_data.monitoring_prisma_reactivity`
WITH open_data AS (
    SELECT
        TIMESTAMP_TRUNC(open_date, HOUR) AS observation_datetime,
        universe_name,
        email_consent_public_ref,
        theme,
        REGEXP_EXTRACT(info.email, r'@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})') AS esp_name,
        COUNT(*) AS open_volume,
        COUNT(DISTINCT email_profile_master_id) AS opener_volume
    FROM `{{ params.bq_project }}.refined_data.prisma_open` AS open
    JOIN `{{ params.bq_project }}.business_data.profile_digital_360` AS p360
        ON open.email_profile_master_id = p360.id.email_profile_master_id
    WHERE open_date BETWEEN start_date AND end_date
    GROUP BY ALL
),

click_data AS (
    SELECT
        TIMESTAMP_TRUNC(click_date, HOUR) AS observation_datetime,
        universe_name,
        email_consent_public_ref,
        theme,
        REGEXP_EXTRACT(info.email, r'@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})') AS esp_name,
        COUNT(*) AS click_volume,
        COUNT(DISTINCT email_profile_master_id) AS clicker_volume
    FROM `{{ params.bq_project }}.refined_data.prisma_click` AS click
    LEFT JOIN UNNEST(article_id)
    JOIN `{{ params.bq_project }}.business_data.profile_digital_360` AS p360
        ON click.email_profile_master_id = p360.id.email_profile_master_id
    WHERE click_date BETWEEN start_date AND end_date
    GROUP BY ALL
)

SELECT
    observation_datetime,
    universe_name,
    STRUCT(
        email_consent_public_ref,
        theme
    ) AS email_base,
    esp_name,
    IFNULL(open_volume, 0) AS open_volume,
    IFNULL(opener_volume, 0) AS opener_volume,
    IFNULL(click_volume, 0) AS click_volume,
    IFNULL(clicker_volume, 0) AS clicker_volume
FROM open_data AS od
FULL OUTER JOIN click_data AS cd USING(universe_name, email_consent_public_ref, theme, esp_name, observation_datetime);
