-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.monitoring_unsync_pec_plc_global` (
  observation_date          DATE        NOT NULL      OPTIONS(description="date of observation"),
  pec_consent_status        STRING                    OPTIONS(description="`Sub` if profile is `sub` to at least one consent in PEC, `unsub` otherwise"),
  plc_consent_status        STRING                    OPTIONS(description="`Sub` if profile is `sub` to at least one consent in PLC, `unsub` otherwise"),
  is_pec                    BOOL                      OPTIONS(description="true if profile is in PEC, false otherwise"),
  is_plc                    BOOL                      OPTIONS(description="true if profile is in PLC, false otherwise"),
  only_pec                  BOOL                      OPTIONS(description="true if profile is only in PEC, false otherwise"),
  only_plc                  BOOL                      OPTIONS(description="true if profile is only in PLC, false otherwise"),
  same_update_date          BOOL                      OPTIONS(description="true if PEC update date equals PLC update date, false otherwise"),
  pec_more_recent           BOOL                      OPTIONS(description="true if PEC update date is more recent than PLC update date, false otherwise"),
  volume                    INT64                     OPTIONS(description="volume of profiles"),
  PRIMARY KEY (observation_date) NOT ENFORCED
)
PARTITION BY observation_date
OPTIONS(description="Table used to monitor unsync between PEC and PLC bases"
                  ||"\n\n"
                  ||"DAG: {{ dag.dag_id }}"
                  ||"\n\n"
                  ||"Sync: daily");

DELETE FROM `{{ params.bq_project }}.generated_data.monitoring_unsync_pec_plc_global`
WHERE observation_date = DATE("{{ data_interval_end }}");

INSERT INTO `{{ params.bq_project }}.generated_data.monitoring_unsync_pec_plc_global`
WITH get_pec_data AS (
    -- Get PEC data
    SELECT
        pec.profile_master_id AS email_profile_master_id,
        eb.consent_public_ref AS email_consent_public_ref,
        pec.consent_status,
        pec.update_date
    FROM `{{ params.bq_project }}.store_matrix_email.profiles_email_consents` AS pec
    JOIN `{{ params.bq_project }}.refined_data.email_base` AS eb ON eb.consent_id = pec.email_consent_id
), aggregate_pec_data AS (
    SELECT
        email_profile_master_id,
        COUNTIF(consent_status = 'sub') AS alive_consent_number,
        MAX(update_date) AS update_date
    FROM get_pec_data
    GROUP BY ALL
), build_pec_from_plc AS (
    -- Build PEC table from PLC
    -- Get last event by user / consent
    SELECT
        plc.email_profile_master_id,
        plc.email_consent_public_ref,
        IF(
            unsub_date IS NULL OR (plc.unsub_date = plc.sub_date AND pec.consent_status = 'sub'),
            'sub',
            'unsub'
        ) AS consent_status,                                                                                            -- fix cases where sub_date = unsub_date --> use PEC as a standard
        COALESCE(unsub_date, sub_date) AS update_date,
        ROW_NUMBER() OVER profile_window AS rnk_profile
    FROM `{{ params.bq_project }}.generated_data.email_profile_lifecycle_by_base_new` AS plc
    JOIN `{{ params.bq_project }}.refined_data.email_event_sub_unsub_new_*` AS eesub
        ON eesub.event_id = COALESCE(plc.unsub_email_event_id, plc.sub_email_event_id)
    LEFT JOIN get_pec_data AS pec
        ON plc.email_profile_master_id = pec.email_profile_master_id
        AND plc.email_consent_public_ref = pec.email_consent_public_ref
    QUALIFY rnk_profile = 1
    WINDOW profile_window AS (PARTITION BY plc.email_profile_master_id, plc.email_consent_public_ref ORDER BY COALESCE(unsub_date, sub_date) DESC, event_id DESC)
), aggregate_plc_data AS (
    SELECT
        email_profile_master_id,
        COUNTIF(consent_status = 'sub') AS alive_consent_number,
        MAX(update_date) AS update_date
    FROM build_pec_from_plc
    GROUP BY ALL
), classify_profiles AS (
    -- Check synchronisation
    -- Classify users (in PEC/PLC, only PEC/PLC...)
    SELECT
        email_profile_master_id,
        IF(plc.alive_consent_number > 0, 'sub', 'unsub') AS plc_consent_status,
        IF(pec.alive_consent_number > 0, 'sub', 'unsub') AS pec_consent_status,
        plc.update_date AS plc_update_date,
        pec.update_date AS pec_update_date,
        plc.email_profile_master_id IS NOT NULL AS is_plc,
        pec.email_profile_master_id IS NOT NULL AS is_pec,
        plc.email_profile_master_id IS NOT NULL AND pec.email_profile_master_id IS NULL AS only_plc,
        plc.email_profile_master_id IS NULL AND pec.email_profile_master_id IS NOT NULL AS only_pec
    FROM aggregate_plc_data AS plc
    FULL JOIN aggregate_pec_data AS pec USING(email_profile_master_id)
)

SELECT
    DATE("{{ data_interval_end }}") AS observation_date,
    pec_consent_status,
    plc_consent_status,
    is_pec,
    is_plc,
    only_pec,
    only_plc,
    pec_update_date IS NOT NULL AND plc_update_date IS NOT NULL AND pec_update_date = plc_update_date AS same_update_date,
    pec_update_date IS NOT NULL AND plc_update_date IS NOT NULL AND pec_update_date > plc_update_date AS pec_more_recent,
    COUNT(DISTINCT email_profile_master_id) AS volume
FROM classify_profiles
WHERE DATE(COALESCE(pec_update_date, plc_update_date)) < DATE("{{ data_interval_end }}")                                -- filter added by Hichem's request
GROUP BY ALL;
