-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE start_date DATE DEFAULT DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL 1 DAY);
DECLARE end_date DATE DEFAULT DATE("{{ data_interval_end }}");

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.data_quality.crm_tag_monitoring`
(
    observation_date        DATE        NOT NULL    OPTIONS(description="Observation date."),
    granularity_level       STRING      NOT NULL    OPTIONS(description="Granularity level of the CRM Tag. Enums=['universe']."),
    granularity_name        STRING      NOT NULL    OPTIONS(description="If granularity level = 'universe', granularity name = universe name\n"
                                                                      ||"If granularity level = 'consent', granularity name = consent public ref."),
    multiple_states_volume  INT64       NOT NULL    OPTIONS(description="Volume of profiles with more than 1 state for the same granularity level and granularity name."),
    no_state_volume         INT64       NOT NULL    OPTIONS(description="Volume of profiles with no state for a given granularity level and granularity name."),
    volume                  ARRAY<STRUCT<
        state_name              STRING                  OPTIONS(description="Name of the state."),
        profile_volume          INT64                   OPTIONS(description="Volume of profiles in the state.")
    >>                                              OPTIONS(description="Struct containing the volume of profiles by state."),
    PRIMARY KEY(observation_date, granularity_level, granularity_name) NOT ENFORCED
)
PARTITION BY observation_date
CLUSTER BY granularity_level, granularity_name
OPTIONS(
    description="This table contains aggregated data from CRM Tag.\n"
              ||"Used for monitoring purposes\n\n"
              ||"DAG: {{ dag.dag_id }}.\n\n"
              ||"Sync: Daily",
    labels=[
        ('project', 'monitoring'),
        ('scope', 'crm_tag'),
        ('creator', 'rodrigo_santana'),
        ('owner', 'mozart')
    ]
);

{% if params.is_full %}
TRUNCATE TABLE `{{ params.bq_project }}.data_quality.crm_tag_monitoring`;
{% else %}
DELETE FROM `{{ params.bq_project }}.data_quality.crm_tag_monitoring`
WHERE observation_date BETWEEN start_date AND end_date;
{% endif %}

INSERT INTO `{{ params.bq_project }}.data_quality.crm_tag_monitoring`
WITH unnest_states AS (
    SELECT
        observation_date,
        SPLIT(state, "-")[SAFE_OFFSET(0)] AS granularity_level,
        SPLIT(state, "-")[SAFE_OFFSET(1)] AS granularity_name,
        SPLIT(state, "-")[SAFE_OFFSET(2)] AS state_name,
        email_profile_master_id
    FROM `{{ params.bq_project }}.generated_data.workflow_state_history`, UNNEST(workflow_states) AS state
    {% if not params.is_full %}
    WHERE observation_date BETWEEN start_date AND end_date
    {% endif %}
),

states_by_profile AS (
    SELECT
        observation_date,
        granularity_level,
        granularity_name,
        email_profile_master_id,
        ARRAY_AGG(state_name IGNORE NULLS) AS states
    FROM unnest_states
    GROUP BY ALL
),

aggregate_states_by_profile AS (
    SELECT
        observation_date,
        granularity_level,
        granularity_name,
        COUNTIF(ARRAY_LENGTH(states) > 1) AS multiple_states_volume,
        COUNTIF(ARRAY_LENGTH(states) = 0) AS no_state_volume
    FROM states_by_profile
    GROUP BY ALL
),

profiles_by_state AS (
    SELECT
        observation_date,
        granularity_level,
        granularity_name,
        state_name,
        COUNT(DISTINCT email_profile_master_id) AS profile_volume
    FROM unnest_states
    GROUP BY ALL
),

aggregate_profiles_by_state AS (
    SELECT
        observation_date,
        granularity_level,
        granularity_name,
        ARRAY_AGG(
            STRUCT(
                state_name,
                profile_volume
            ) ORDER BY state_name
        ) AS volume
    FROM profiles_by_state
    GROUP BY ALL
)

SELECT
    observation_date,
    granularity_level,
    granularity_name,
    multiple_states_volume,
    no_state_volume,
    volume
FROM aggregate_states_by_profile AS asp
JOIN aggregate_profiles_by_state AS aps USING(observation_date, granularity_level, granularity_name);
