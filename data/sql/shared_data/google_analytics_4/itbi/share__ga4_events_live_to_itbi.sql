-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- CREATE TABLE for each platform
-- WEB --
CREATE TABLE IF NOT EXISTS `{{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.{{ params.task_config["destination_bq_dataset"] }}.ga_events_live_WEB`
(
    partition_datetime  TIMESTAMP   NOT NULL    OPTIONS(description="Partition datetime"),
    visit_date          DATE        NOT NULL    OPTIONS(description="Date of visit extracted from event date."),
    user_pseudo_id      STRING      NOT NULL    OPTIONS(description=
                                                "Google Analytics pseudo Id generated by GA Tracker. \n"||
                                                "It depends on device according to Google Documentation. \n"||
                                                "For more details 👉🏻 https://www.optizent.com/blog/what-is-user_pseudo_id-in-ga4-bigquery-export/"
                                               ),
    property_data   STRUCT<
        property_id     INT64   NOT NULL    OPTIONS(description="Google Analytics property Id."),
        brand_trigram   STRING  NOT NULL    OPTIONS(description="Website / Application brand trigram."),
        country         STRING  NOT NULL    OPTIONS(description="Property country. enum = ('ALL', 'UK', 'DE', ...). ‘ALL' is a default value."),
        platform        STRING  NOT NULL    OPTIONS(description="Property platform. enum = ('WEB', 'IOS', 'ANDROID')."),
        section         STRING  NOT NULL    OPTIONS(description="Property section (it's exclusive for Gentside websites). enum = ('ALL', 'SPORT', 'TRIP', ...).")
    >   OPTIONS(description="Google Analytics property information."),
    user_data STRUCT<
        auth_id              ARRAY<STRING>      OPTIONS(description=
                                                         "Authentification Id as custom dimension. \n"||
                                                         "GA dimension: 'auth_id'  \n" ||
                                                         "Is it native or custom dimension ? : custom"
                                                        ),
        batch_user_id        ARRAY<STRING>      OPTIONS(description=
                                                         "Batch install Id as custom dimension. \n"||
                                                         "GA dimension: 'batch_user_id' \n"||
                                                         "Is it native or custom dimension ? : custom"
                                                        ),
        client_id            ARRAY<STRING>      OPTIONS(description=
                                                         "Google Analytics client Id as cookie first-party. \n"||
                                                         "GA dimension: 'client_id' \n"||
                                                         "Is it native or custom dimension ? : native \n"||
                                                         "For more details 👉🏻  https://www.analyticsmania.com/post/google-analytics-client-id/"
                                                        ),
        pmc_web_id           ARRAY<STRING>      OPTIONS(description=
                                                         "PMC web Id generated for logged profiles. \n"||
                                                         "GA dimension: 'web_pmc_id' \n" ||
                                                         "Is it native or custom dimension ? : custom"
                                                        ),
        is_paywall           BOOLEAN            OPTIONS(description=
                                                         "Is profile subscribed to paywall offer ? \n"||
                                                         "GA dimension: 'paywall_subscriber' \n"||
                                                         "Is it native or custom dimension ? : custom"
                                                        ),
        has_followed_star    BOOLEAN            OPTIONS(description=
                                                         "Is profile follow a star ? \n"||
                                                         "GA dimension: 'star_followed' \n"||
                                                         "Is it native or custom dimension ? : custom"
                                                        ),
        tracking_info   STRUCT<
            source                  STRING  OPTIONS(description="Source of the first user acquisition \n"),
            medium                  STRING  OPTIONS(description="Medium of the first user acquisition \n"),
            campaign                STRING  OPTIONS(description="Campaign of the first user acquisition \n"),
            default_channel_group   STRING  OPTIONS(description="Default channel group as defined by Google at user scale. \n"||
                                                    "It's the first channel group from where user has been arrived. \n"||
                                                    "For more details 👉🏻 https://support.google.com/analytics/answer/9756891?hl=en"
                                                    ),
            custom_channel_group    STRING  OPTIONS(description="Custom channel group as defined by Prisma Media at user scale. \n"||
                                                    "It's the first channel group from where user has been arrived. \n"||
                                                    "For more details 👉🏻 https://docs.google.com/spreadsheets/d/15IJMid4OrJKMythVk9udgdZfdKvnLYPuN-YXfRYpcRM/edit?usp=sharing"
                                                    )
        > OPTIONS(description="User first acquisition tracking information.")
    >   OPTIONS(description="User data."),
    session_data ARRAY<
        STRUCT<
            session_id                  STRING      NOT NULL    OPTIONS(description=
                                                                        "Session Id to flag unique session. \n"||
                                                                        "It's a concatenation of 'ga_session_id' and 'user_pseudo_id' \n"||
                                                                        "GA dimension: 'ga_session_id' \n"||
                                                                        "'ga_session_id': It's a native GA dimension and it correspond to stat session timestamp. \n"||
                                                                        "Is it native or custom dimension ? : native \n"||
                                                                        "For more details 👉🏻 https://support.google.com/analytics/answer/9191807"
                                                                       ),
            session_number              INTEGER     NOT NULL    OPTIONS(description=
                                                                        "Number of session that user has started up to the current session. \n"||
                                                                        "GA dimension: 'ga_session_number' \n"||
                                                                        "Is it native or custom dimension ? : native \n"||
                                                                        "For more details 👉🏻 https://support.google.com/analytics/answer/9191807"
                                                                       ),
            session_start_datetime      TIMESTAMP   NOT NULL    OPTIONS(description="First event timestamp's during the session."),
            session_end_datetime        TIMESTAMP   NOT NULL    OPTIONS(description="Last event timestamp's during the session"),
            is_engaged_session          BOOLEAN     NOT NULL    OPTIONS(description=
                                                                        "Is it an engaged session ? \n"||
                                                                        "An engaged session is a session that lasts longer than 10 seconds, has a conversion event, or has at least 2 pageviews or screenviews. \n"||
                                                                        "GA dimension: 'session_engaged' \n"||
                                                                        "Is it native or custom dimension ? : native \n"||
                                                                        "For more details 👉🏻 https://support.google.com/analytics/answer/12195621"
                                                                       ),
            is_logged_session           BOOLEAN     NOT NULL    OPTIONS(description=
                                                                        "Is it a logged session ? \n"||
                                                                        "A logged session is a session that contains a 'pmc_session_start' custom event or a filled PMC Web Id. \n"||
                                                                        "GA dimension: 'web_pmc_id' \n"||
                                                                        "Is it native or custom dimension ? : custom \n"
                                                                       ),
            landing_page                STRING                  OPTIONS(description=
                                                                        "The first page on the session. \n"||
                                                                        "GA dimension: 'entrances' & 'page_location' \n"||
                                                                        "Is it native or custom dimension ? : native \n"
                                                                       ),
            exit_page                   STRING                  OPTIONS(description=
                                                                        "The last page on the session. \n"||
                                                                        "GA dimension: 'page_location' \n"||
                                                                        "Is it native or custom dimension ? : native \n"
                                                                       ),
            tracking_info               STRUCT<
                source                      STRING  OPTIONS(description=
                                                            "Session UTM source. \n"||
                                                            "This UTM source has been redressed (for non-direct) and it's attributed to the last non-direct source by user through the last 30 days. \n"||
                                                            "GA dimension: 'source' \n"||
                                                            "Is it native or custom dimension ? : native \n"||
                                                            "For more details 👉🏻 https://support.google.com/analytics/answer/9191807?hl=en#zippy=%2Cadjust-session-timeout:~:text=How%20session%20attribution%20works,also%20attributed%20to%20%E2%80%9Cgoogle%20/%20organic%E2%80%9D"
                                                            ),
                medium                      STRING  OPTIONS(description=
                                                            "Session UTM medium. \n"||
                                                            "This UTM medium has been redressed (for non-direct) and it's attributed to the last non-direct source by user through the last 30 days. \n"||
                                                            "GA dimension: 'medium' \n"||
                                                            "Is it native or custom dimension ? : native \n"||
                                                            "For more details 👉🏻 https://support.google.com/analytics/answer/9191807?hl=en#zippy=%2Cadjust-session-timeout:~:text=How%20session%20attribution%20works,also%20attributed%20to%20%E2%80%9Cgoogle%20/%20organic%E2%80%9D"
                                                            ),
                campaign                    STRING  OPTIONS(description=
                                                            "Session UTM campaign. \n"||
                                                            "This UTM campaign has been redressed (for non-direct) and it's attributed to the last non-direct source by user through the last 30 days. \n"||
                                                            "GA dimension: 'campaign' \n"||
                                                            "Is it native or custom dimension ? : native \n"||
                                                            "For more details 👉🏻 https://support.google.com/analytics/answer/9191807?hl=en#zippy=%2Cadjust-session-timeout:~:text=How%20session%20attribution%20works,also%20attributed%20to%20%E2%80%9Cgoogle%20/%20organic%E2%80%9D"
                                                            ),
                term                        STRING  OPTIONS(description=
                                                            "Session UTM term. \n"||
                                                            "It's extracted from RAW page path 'utm_term=*' \n"
                                                            ),
                content                     STRING  OPTIONS(description=
                                                            "Session UTM content. \n"||
                                                            "It's extracted from RAW page path 'utm_content=*' \n"
                                                            ),
                default_channel_group       STRING  OPTIONS(description="Default channel group as defined by Google at session scale. \n"||
                                                            "For more details 👉🏻 https://support.google.com/analytics/answer/9756891?hl=en"
                                                            ),
                custom_channel_group        STRING  OPTIONS(description="Custom channel group as defined by Prisma Media at session scale. \n"||
                                                            "For more details 👉🏻 https://docs.google.com/spreadsheets/d/15IJMid4OrJKMythVk9udgdZfdKvnLYPuN-YXfRYpcRM/edit?usp=sharing"
                                                            )
            >   OPTIONS(description="Session Tracking information."),
            device_info STRUCT<
                category                    STRING  OPTIONS(description="The device category (mobile, tablet, desktop)."),
                operating_system            STRING  OPTIONS(description="The operating system of the device."),
                operating_system_version    STRING  OPTIONS(description="The OS version."),
                mobile  STRUCT<
                    brand_name                  STRING  OPTIONS(description="The device brand name."),
                    model_name                  STRING  OPTIONS(description="The device model name."),
                    marketing_name              STRING  OPTIONS(description="The device marketing name."),
                    os_harware_model            STRING  OPTIONS(description="The device model information retrieved directly from the operating system.")
                >   OPTIONS(description="Mobile information"),
                web     STRUCT<
                    hostname                    STRING  OPTIONS(description="The hostname associated with the logged event."),
                    browser                     STRING  OPTIONS(description="The browser in which the user viewed content."),
                    browser_version             STRING  OPTIONS(description="The version of the browser in which the user viewed content.")
                >   OPTIONS(description="Web information")
            >   OPTIONS(description="Device information"),
            geo_info    STRUCT<
                continent       STRING  OPTIONS(description="The continent from which events were reported, based on IP address."),
                sub_continent   STRING  OPTIONS(description="The subcontinent from which events were reported, based on IP address."),
                region          STRING  OPTIONS(description="The region from which events were reported, based on IP address."),
                country         STRING  OPTIONS(description="The country from which events were reported, based on IP address."),
                city            STRING  OPTIONS(description="The city from which events were reported, based on IP address."),
                metro           STRING  OPTIONS(description="The metro from which events were reported, based on IP address.")
            >   OPTIONS(description=
                        "Geographic information. \n"||
                        "For more details 👉🏻 https://support.google.com/analytics/answer/7029846/"
                       ),
            page_data ARRAY<
                STRUCT<
                    page_hit_id     STRING  OPTIONS(description=
                                                        "Screen Id within a hit and it start by <brand_trigram>_id. It serve to the mapping with G-Ad Manager Data. \n"||
                                                        "GA dimension: 'page_hit_id' \n"||
                                                        "Is it native or custom dimension ? : custom"
                                                       ),
                    global_info     STRUCT<
                        page_title              STRING              OPTIONS(description=
                                                                            "Page title. \n"||
                                                                            "GA dimension: 'page_title' \n"||
                                                                            "Is it native or custom dimension ? : custom"
                                                                            ),
                        is_landing_page         BOOLEAN             OPTIONS(description=
                                                                            "Is it landing page ? \n"||
                                                                            "GA dimension: 'entrances' \n"||
                                                                            "Is it native or custom dimension ? : native"
                                                                           ),
                        is_exit_page            BOOLEAN         OPTIONS(description="Is it exit page ? It corresponds to the last viewed page in the session."),
                        is_bottom_page_reached  BOOLEAN         OPTIONS(description=
                                                                        "The first time a user reaches the bottom of each page (i.e., when a 90% vertical depth becomes visible) \n"||
                                                                        "GA dimension: 'percent_scrolled' \n"||
                                                                        "Is it native or custom dimension ? : custom"
                                                                        ),
                        page_canonical_path     STRING              OPTIONS(description=
                                                                            "Page canonical URL extracted from page location. \n"||
                                                                            "GA dimension: 'page_location' \n"||
                                                                            "Is it native or custom dimension ? : custom"
                                                                           ),
                        page_level_1            STRING              OPTIONS(description="Page path first level extracted from page location."),
                        page_level_2            STRING              OPTIONS(description="Page path second level extracted from page location."),
                        page_level_3            STRING              OPTIONS(description="Page path third level extracted from page location."),
                        page_level_4            STRING              OPTIONS(description="Page path fourth level extracted from page location."),
                        page_type               STRING              OPTIONS(description=
                                                                            "Page type. \n"||
                                                                            "GA dimension: 'page_type' \n"||
                                                                            "Is it native or custom dimension ? : custom"
                                                                            ),
                        page_category           STRING              OPTIONS(description=
                                                                            "Page category. \n"||
                                                                            "GA dimension: 'page_catrgory' \n"||
                                                                            "Is it native or custom dimension ? : custom"
                                                                           ),
                        page_keywords           ARRAY<STRING>       OPTIONS(description="List of page keywords."),
                        page_referrer           STRING              OPTIONS(description=
                                                                            "Page referrer. \n"||
                                                                            "GA dimension: 'page_catrgory' \n"||
                                                                            "Is it native or custom dimension ? : custom"
                                                                           ),
                        page_hostname           STRING              OPTIONS(description="Page hostname extracted from page location.")
                    >   OPTIONS(description="Page global information."),
                    content_info        STRUCT<
                        content_uuid            STRING          OPTIONS(description=
                                                                        "Article / Recipe / Diapo uuid extracted from content object second part. \n"||
                                                                        "GA dimension: 'content_object_id' \n"||
                                                                        "Is it native or custom dimension ? : custom"
                                                                        ),
                        content_type            STRING          OPTIONS(description=
                                                                        "Content type as : (Article / Recipe / Diapo,..) extracted from content object first part. \n"||
                                                                        "GA dimension: 'content_object_id' \n"||
                                                                        "Is it native or custom dimension ? : custom"
                                                                        ),
                        content_qualifier       ARRAY<STRING>  OPTIONS(description=
                                                                        "List of content qualifiers \n"||
                                                                        "GA dimension: 'one_qualifier' \n"||
                                                                        "Is it native or custom dimension ? : custom"
                                                                        ),
                        is_diapo_content        BOOLEAN         OPTIONS(description=
                                                                        "Is it Diapo content ? \n"||
                                                                        "GA dimension: 'diapo_content' \n"||
                                                                        "Is it native or custom dimension ? : custom"
                                                                        ),
                        is_live_content         BOOLEAN         OPTIONS(description=
                                                                        "Is it Live content ? \n"||
                                                                        "GA dimension: 'live_content' \n"||
                                                                        "Is it native or custom dimension ? : custom"
                                                                        ),
                        is_sponsored_content    BOOLEAN         OPTIONS(description=
                                                                        "Is it Sponsored content ? \n"||
                                                                        "GA dimension: 'sponsored_content' \n"||
                                                                        "Is it native or custom dimension ? : custom"
                                                                        ),
                        is_provided_content     BOOLEAN         OPTIONS(description=
                                                                        "Is it Provided content ? \n"||
                                                                        "GA dimension: 'provided_content' \n"||
                                                                        "Is it native or custom dimension ? : custom"
                                                                        ),
                        content_author_name     STRING          OPTIONS(description=
                                                                        "Author name.\n"||
                                                                        "GA dimension: 'author_name' \n"||
                                                                        "Is it native or custom dimension ? : custom"
                                                                        ),
                        content_author_group     STRING          OPTIONS(description=
                                                                        "Author group.\n"||
                                                                        "GA dimension: 'author_group' \n"||
                                                                        "Is it native or custom dimension ? : custom"
                                                                        )
                    >   OPTIONS(description="Content information."),
                    event_info ARRAY<
                        STRUCT <
                            event_name                  STRING      NOT NULL    OPTIONS(description=
                                                                                        "Event name. \n"||
                                                                                        "Event can be 'native' like ('start_session', 'page_view', 'user_engagement', ...) or custom like ('bookmark_activated', 'video_dm_media_begin', ...). \n"||
                                                                                        "For more details 👉🏻 https://docs.google.com/spreadsheets/d/1m8_4tFyYqDxutbZ2aGBc7syDjX8E6qAvw9d4tTeHcNI/edit?usp=sharing"
                                                                                        ),
                            event_datetime              TIMESTAMP   NOT NULL    OPTIONS(description="Event datetime."),
                            event_platform              STRING      NOT NULL    OPTIONS(description="Event platform as ('WEB', 'IOS', 'ANDROID') <--> Hit datasource in GA Universal."),
                            engagement_time_in_seconds  FLOAT64                 OPTIONS(description=
                                                                                        "Engagement time in seconds estimated by GA tracker at each event. \n"||
                                                                                        "It's the amount of time by event and added to next one."||
                                                                                        "GA dimension: 'engagement_time_msec' \n"||
                                                                                        "Is it native or custom dimension ? : native \n"||
                                                                                        "For more details 👉🏻 https://support.google.com/analytics/answer/11109416"
                                                                                       ),
                            page_location               STRING                  OPTIONS(description="Page path. \n"||
                                                                                        "GA dimension: 'page_location'. \n"||
                                                                                        "Is it native or custom dimension ? : native \n"
                                                                                        ),
                            kit_component               STRING                  OPTIONS(description=
                                                                                        "UI component from where event is generated. \n"||
                                                                                        "Examples: \n"||
                                                                                        "- encartNL: signup/login from diplayed Pop-in NL in the website. \n"||
                                                                                        "- newsletter: signup/login from NL. \n"||
                                                                                        "- optinPopin: signup/login Opt-in Pop-in in the website. \n"||
                                                                                        "- notificationCenter: Enable/Disable alerts from notification center. \n"||
                                                                                        "- signupBox: signup/login from signup box. \n"||
                                                                                        "GA dimension: 'kit_component' \n"||
                                                                                        "Is it native or custom dimension ? : custom"
                                                                                       ),
                            email_consent_public_ref    ARRAY<STRING>           OPTIONS(description=
                                                                                        "List of email consent public ref which user is subscribed to and it's always linked to 'newsletter_subscribe' custom event. \n"||
                                                                                        "GA dimension: 'newsletter_label' \n"||
                                                                                        "Is it native or custom dimension ? : custom"
                                                                                       ),
                            popin_type                  STRING                  OPTIONS(description=
                                                                                        "Type of popin displayed / clicked. It's related to 'popin_*' custom event. \n"||
                                                                                        "GA dimension: 'popin_type' \n"||
                                                                                        "Is it native or custom dimension ? : custom"
                                                                                       ),
                            signup_service              STRING                  OPTIONS(description=
                                                                                        "Signup service. It's related to muliple `signup_service_*` custom events. \n"||
                                                                                        "NB: It's doesn't mean that if signup_service column is not NULL --> user is signupped ! you should filter on the correct signup_service custom event (@TO BE SET LATER). \n"||
                                                                                        "GA dimension: 'signup_service' \n"||
                                                                                        "Is it native or custom dimension ? : custom"
                                                                                       ),
                            diapo_nb_photo              INTEGER                 OPTIONS(description=
                                                                                        "Number of photos on the Diapo page. \n"||
                                                                                        "It's available only for Diapo contents. \n"||
                                                                                        "GA dimension: 'photos_count' \n"||
                                                                                        "Is it native or custom dimension ? : custom"
                                                                                       ),
                            clicked_button_name         STRING                  OPTIONS(description="CTA button name. It's always linked to 'button_click' native event."),
                            login_info STRUCT<
                                login_role      STRING  OPTIONS(description=
                                                                "Login role as : ('adminlogin', 'adminrole', 'autologin', 'none', NULL). It's linked to login and signup events. \n"||
                                                                "GA dimension: 'login_role' \n"||
                                                                "Is it native or custom dimension ? : custom"
                                                               ),
                                login_method    STRING  OPTIONS(description=
                                                                "Login method as : ('apple', 'beloud', 'email', 'facebook', 'x', 'pinterest', 'magic link', 'mdp'). It''s linked to login and signup events.\n"||
                                                                "GA dimension: 'method' \n"||
                                                                "Is it native or custom dimension ? : custom"
                                                               )
                            > OPTIONS(description="Login information."),
                            signup_info STRUCT<
                                signup_method    STRING  OPTIONS(description=
                                                                "Signup method as : ('apple', 'beloud', 'email', 'facebook', 'x', 'pinterest', 'magic link', 'mdp'). It''s linked to login and signup events.\n"||
                                                                "GA dimension: 'method' \n"||
                                                                "Is it native or custom dimension ? : custom"
                                                                )
                            > OPTIONS(description="Login information."),
                            alert_info STRUCT<
                                alert_mode              STRING  OPTIONS(description=
                                                                        "Alert mode as = ('e-mail', 'webpush') \n."||
                                                                        "GA dimension: 'alert_mode' \n"||
                                                                        "Is it native or custom dimension ? : custom"
                                                                       ),
                                alert_bookmark_type     STRING  OPTIONS(description=
                                                                        "Bookmark type linked to the alert as = ('follow:people', 'follow:tag', ...) \n."||
                                                                        "GA dimension: 'alert_bookmark_type' \n"||
                                                                        "(??) : What's the difference between alert_bookmark_type & bookmark_type ? "||
                                                                        "Is it native or custom dimension ? : custom"
                                                                       )
                            > OPTIONS(description="Alert information."),
                            bookmark_info STRUCT<
                                bookmark_consent    STRING  OPTIONS(description="(??) : I need some explanation. "),
                                bookmark_label      STRING  OPTIONS(description="(??) : I need some explanation. "),
                                bookmark_type       STRING  OPTIONS(description=
                                                                    "Bookmark type as = (''follow:people'', ''follow:tag'', ...) \n."||
                                                                    "GA dimension: 'bookmark_type' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                  ),
                                bookmark_content    STRING  OPTIONS(description=
                                                                    "content id associated to bookmark. \n"||
                                                                    "GA dimension: 'bookmark_content' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                   )
                            > OPTIONS(description="Bookmark information."),
                            click_info STRUCT<
                                click_link_id       STRING  OPTIONS(description=
                                                                    "Clicked URL Id. \n"||
                                                                    "GA dimension: 'link_id' \n"||
                                                                    "Is it native or custom dimension ? : native"
                                                                   ),
                                click_link_url      STRING  OPTIONS(description=
                                                                    "Clicked URL. \n"||
                                                                    "GA dimension: 'link_url' \n"||
                                                                    "Is it native or custom dimension ? : native"
                                                                   ),
                                click_link_domain   STRING  OPTIONS(description=
                                                                    "Clicked URL domain. \n"||
                                                                    "GA dimension: 'link_domain' \n"||
                                                                    "Is it native or custom dimension ? : native"
                                                                   ),
                                click_link_classes  STRING  OPTIONS(description=
                                                                    "Clicked URL class. \n"||
                                                                    "GA dimension: 'link_classes' \n"||
                                                                    "Is it native or custom dimension ? : native"
                                                                   ),
                                is_outbound_link    BOOLEAN OPTIONS(description=
                                                                    "Is Clicked URL outbound ? . \n"||
                                                                    "GA dimension: 'outbound' \n"||
                                                                    "Is it native or custom dimension ? : native"
                                                                   )
                            >   OPTIONS(description="Click information."),
                            review_info STRUCT<
                                user_input_format   STRING  OPTIONS(description=
                                                                    "Input format as : ('text','photo'). \n"||
                                                                    "GA dimension: 'user_input_format' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                   ),
                                user_input_type     STRING  OPTIONS(description=
                                                                    "Input type as : ('comment','score'). \n"||
                                                                    "GA dimension: 'user_input_type' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                   ),
                                user_input_label    STRING   OPTIONS(description=
                                                                    "Input label to flag the content and placement. \n"||
                                                                    "GA dimension: 'user_input_label' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                   )
                            >   OPTIONS(description="User input information (comment/review).It's available only for TEL website."),
                            video_info STRUCT<
                                video_url           STRING  OPTIONS(description=
                                                                    "Video URL. \n"||
                                                                    "GA dimension: 'video_dm_url' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                  ),
                                video_launch_mode   STRING OPTIONS(description=
                                                                    "Video launch mode as : ('autoplay', 'click'). \n"||
                                                                    "GA dimension: 'video_dm_launchmode' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                  ),
                                video_player        STRING OPTIONS(description="??")
                            >   OPTIONS(description="Video information."),
                            audio_info STRUCT<
                                media_title STRING  OPTIONS(description=
                                                            "Media title. \n"||
                                                           "GA dimension: 'media_title' \n"||
                                                            "Is it native or custom dimension ? : custom"
                                                           ),
                                media_url   STRING  OPTIONS(description=
                                                            "Media URL. \n"||
                                                            "GA dimension: 'media_url' \n"||
                                                            "Is it native or custom dimension ? : custom"
                                                           )
                            >   OPTIONS(description="Audio information. It's available only for FAC website."),
                            search_info STRUCT<
                                search_category     STRING  OPTIONS(description=
                                                                        "Search category as : ('price', 'note', 'difficulty') . \n"||
                                                                        "GA dimension: 'search_category' \n"||
                                                                        "Is it native or custom dimension ? : custom"
                                                                   ),
                                search_term         STRING  OPTIONS(description=
                                                                    "Search term as : ('Bon marché') . \n"||
                                                                    "GA dimension: 'search_term' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                   )
                            >   OPTIONS(description="Search information. It's linked to 'search' or 'view_search_results' native Events. "),
                            cooking_info STRUCT<
                                is_vocal_assist_enabled     BOOLEAN OPTIONS(description=
                                                                            "Is vocal assitant enabled ? \n"||
                                                                            "GA dimension: 'cookmode_vocal_assist' \n"||
                                                                            "Is it native or custom dimension ? : custom"
                                                                           ),
                                is_vocal_command_enabled    BOOLEAN OPTIONS(description=
                                                                            "Is vocal assitant enabled ? \n"||
                                                                            "GA dimension: 'cookmode_vocal_command' \n"||
                                                                            "Is it native or custom dimension ? : custom"
                                                                           ),
                                is_micro_enabled            BOOLEAN  OPTIONS(description=
                                                                            "Is micro enabled ? \n"||
                                                                            "GA dimension: 'mciro_status' \n"||
                                                                            "Is it native or custom dimension ? : custom"
                                                                            ),
                                cooking_step                STRING  OPTIONS(description=
                                                                            "Cooking step as : ('onboarding', 'start', 'intermediate', 'ingredients', .., 'end', 'final screen'). \n"||
                                                                            "GA dimension: 'cookmode_step' \n"||
                                                                            "Is it native or custom dimension ? : custom"
                                                                            )
                            >   OPTIONS(description="Cooking information. It's available only for CAC website."),
                            share_info STRUCT<
                                share_method STRING OPTIONS(description="Share method as : ('facebook', 'x', ...)")
                            >   OPTIONS(description="Share information. It's always linked to 'share' events."),
                            ab_test_info STRUCT<
                                    ab_test_variation       STRING  OPTIONS(description="A/B test item. \n"||
                                                                            "GA dimension: 'ab_test_variation' \n"||
                                                                            "Is it native or custom dimension ? : custom"
                                                                            ),
                                    ab_test_variation_ad    STRING  OPTIONS(description="A/B test item for ADs. \n"||
                                                                            "GA dimension: 'ab_test_variation_pub' \n"||
                                                                            "Is it native or custom dimension ? : custom"
                                                                            ),
                                    native_info STRUCT<
                                        abtasty_campaign    STRING  OPTIONS(description="Campaign ID and Campaign Name formatted as [campaign_id] campaign_name. "||
                                                                            "GA dimension: 'abtasty_campaign' \n"||
                                                                            "Is it native or custom dimension ? : native"
                                                                            ),
                                        abtasty_variation   STRING  OPTIONS(description=" Variation ID and Variation Name formatted as [variation_id] variation_name. "||
                                                                            "GA dimension: 'abtasty_variation' \n"||
                                                                            "Is it native or custom dimension ? : native"
                                                                            )
                                    > OPTIONS(description="A/B test info based on native A/B Tasty parameters")
                                >   OPTIONS(description="A/B test information."),
                             tracking_info               STRUCT<
                                source                      STRING  OPTIONS(description=
                                                                            "Event UTM source. \n"||
                                                                            "GA dimension: 'source' \n"||
                                                                            "Is it native or custom dimension ? : native \n"
                                                                            ),
                                medium                      STRING  OPTIONS(description=
                                                                            "Event UTM medium. \n"||
                                                                            "GA dimension: 'medium' \n"||
                                                                            "Is it native or custom dimension ? : native \n"
                                                                            ),
                                campaign                    STRING  OPTIONS(description=
                                                                            "Event UTM campaign. \n"||
                                                                            "GA dimension: 'campaign' \n"||
                                                                            "Is it native or custom dimension ? : native \n"
                                                                            ),
                                term                        STRING  OPTIONS(description=
                                                                            "Event UTM term. \n"||
                                                                            "It's extracted from RAW page path 'utm_term=*' \n"
                                                                            ),
                                content                     STRING  OPTIONS(description=
                                                                            "Event UTM content. \n"||
                                                                            "It's extracted from RAW page path 'utm_content=*' \n"
                                                                            )
                            >   OPTIONS(description="Event Tracking information."),
                            ecommerce_info STRUCT<
                                    transaction STRUCT<
                                        transaction_id      STRING  OPTIONS(description=
                                                                            "The transaction ID collected with the event. \n"||
                                                                            "GA dimension: 'transaction_id' \n"||
                                                                            "Is it native or custom dimension? : native \n"),
                                        total_item_quantity INT64   OPTIONS(description=
                                                                            "The number of items collected with the event. \n"||
                                                                            "GA dimension: 'total_item_quantity' \n"||
                                                                            "Is it native or custom dimension? : native \n"),
                                        unique_items        INT64   OPTIONS(description=
                                                                            "The number of unique items collected with the event. \n"||
                                                                            "GA dimension: 'unique_items' \n"||
                                                                            "Is it native or custom dimension? : native \n"),
                                        purchase_revenue    FLOAT64 OPTIONS(description=
                                                                            "Purchase revenue collected with the event (only for purchase events). \n"||
                                                                            "GA dimension: 'purchase_revenue' \n"||
                                                                            "Is it native or custom dimension? : native \n")
                                                        > OPTIONS(description=  "E-commerce transaction details."),
                                    items ARRAY<STRUCT<
                                        item_name     STRING  OPTIONS(description=
                                                                        "The name of the item collected with the event. \n"||
                                                                        "GA dimension: 'item_name' \n"||
                                                                        "Is it native or custom dimension? : native \n"),
                                        item_category STRING  OPTIONS(description=
                                                                        "The category of the item collected with the event. \n"||
                                                                        "GA dimension: 'item_category' \n"||
                                                                        "Is it native or custom dimension? : native \n"),
                                        quantity      INT64   OPTIONS(description=
                                                                        "The quantity of the item collected with the event. \n"||
                                                                        "GA dimension: 'quantity' \n"||
                                                                        "Is it native or custom dimension? : native \n"),
                                        price         FLOAT64 OPTIONS(description=
                                                                        "The price of the item collected with the event. \n"||
                                                                        "GA dimension: 'price' \n"||
                                                                        "Is it native or custom dimension? : native \n")
                                                    >> OPTIONS(description= "Items purchased in the event.")
                            > OPTIONS(description=  "E-commerce event data including transaction and items details."),
                            navigation_info STRUCT<
                                    navigation_label    STRING  OPTIONS(description=
                                                                        "Navigation label. Only available for VOI WEB and TEL WEB. \n"||
                                                                        "GA dimension: 'navigation_label' \n" ||
                                                                        "Is it native o custom dimension?: custom"),
                                    navigation_name     STRING  OPTIONS(description=
                                                                        "Navigation name. Only available for VOI WEB and TEL WEB. \n"||
                                                                        "GA dimension: 'navigation_name' \n" ||
                                                                        "Is it native o custom dimension?: custom"),
                                    navigation_type     STRING  OPTIONS(description=
                                                                        "Navigation type. Only available for VOI WEB and TEL WEB. \n"||
                                                                        "GA dimension: 'navigation_type' \n" ||
                                                                        "Is it native o custom dimension?: custom")
                                > OPTIONS(description="Navigation information")
                        >
                    >   OPTIONS(description="Event information.")
                >
            >   OPTIONS(description="Page data.")
        >
    >   OPTIONS(description="Session data."),
    PRIMARY KEY(partition_datetime, visit_date, user_pseudo_id) NOT ENFORCED
)
PARTITION BY TIMESTAMP_TRUNC(partition_datetime, HOUR)
OPTIONS(description="It contains refined GA4 events live WEB. \n"||
        "It's generated from Live views refined data. \n"||
        "Table architecture: \n"||
        """visit date:
            |
            user
              |
              [sessions]
                    |
                    [pages]
                        |
                        [events]"""||
        "\n\n"||
        "Sync: Each 30 minutes. \n"||
        "DAG: {{ dag.dag_id }}"
);
-- APP --
CREATE TABLE IF NOT EXISTS `{{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.{{ params.task_config["destination_bq_dataset"] }}.ga_events_live_APP`
(
    partition_datetime  TIMESTAMP   NOT NULL    OPTIONS(description="Partition datetime"),
    visit_date          DATE        NOT NULL    OPTIONS(description="Date of visit extracted from event date."),
    user_pseudo_id      STRING      NOT NULL    OPTIONS(description=
                                                "Google Analytics pseudo Id generated by GA Tracker. \n"||
                                                "It depends on device according to Google Documentation. \n"||
                                                "For more details 👉🏻 https://www.optizent.com/blog/what-is-user_pseudo_id-in-ga4-bigquery-export/"
                                               ),
    property_data   STRUCT<
        property_id     INT64   NOT NULL    OPTIONS(description="Google Analytics property Id."),
        brand_trigram   STRING  NOT NULL    OPTIONS(description="Website / Application brand trigram."),
        country         STRING  NOT NULL    OPTIONS(description="Property country. enum = ('ALL', 'UK', 'DE', ...). ‘ALL' is a default value."),
        platform        STRING  NOT NULL    OPTIONS(description="Property platform. enum = ('WEB', 'IOS', 'ANDROID')."),
        section         STRING  NOT NULL    OPTIONS(description="Property section (it's exclusive for Gentside websites). enum = ('ALL', 'SPORT', 'TRIP', ...)."),
        app_info STRUCT<
            id              STRING OPTIONS(description="Application Id"),
            version         STRING OPTIONS(description="Version number"),
            install_store   STRING OPTIONS(description="Install store"),
            firbase_app_id  STRING OPTIONS(description="Firebase App Id"),
            install_source  STRING OPTIONS(description="Install source")
        > OPTIONS(description="Application Informations")
    >   OPTIONS(description="Google Analytics property information."),
    user_data STRUCT<
        pmc_uuid                    ARRAY<STRING>   OPTIONS(description=
                                                         "PMC uuid as custom dimension. \n"||
                                                         "GA dimension: 'web_pmc_id'  \n" ||
                                                         "Is it native or custom dimension ? : custom"
                                                        ),
        is_connected                BOOLEAN         OPTIONS(description=
                                                         "Is profile connected ?. \n"||
                                                         "GA dimension: 'connected' \n"||
                                                         "Is it native or custom dimension ? : native"
                                                        ),
        is_consent_enabled          BOOLEAN         OPTIONS(description=
                                                         "Is profile consent ? It's linked with Google Consent Mode which interact with Prisma Media CMP. \n"||
                                                         "Enum = 'granted', 'not_answered', 'declined' \n"||
                                                         "GA dimension: 'consents' \n"||
                                                         "Is it native or custom dimension ? : native \n"||
                                                         "For more details 👉🏻 https://support.google.com/analytics/answer/9976101?sjid=12483505493093531101-EU#tag_behavior"
                                                        ),
        is_accessibility_enabled    BOOLEAN         OPTIONS(description=
                                                         "(??) "||
                                                         "GA dimension: 'accessibility_enabled' \n"||
                                                         "Is it native or custom dimension ? : (??)"
                                                        ),
        is_location_enabled         BOOLEAN         OPTIONS(description=
                                                         "Is profile location enabled ? "||
                                                         "GA dimension: 'location_enabled' \n"||
                                                         "Is it native or custom dimension ? : (??)"
                                                        ),
        is_push_enabled             BOOLEAN         OPTIONS(description=
                                                         "Is profile push enabled ? "||
                                                         "GA dimension: 'push_enabled' \n"||
                                                         "Is it native or custom dimension ? : (??)"
                                                        ),
        is_paywall                  BOOLEAN         OPTIONS(description=
                                                         "Is profile subscribed to paywall offer ? \n"||
                                                         "GA dimension: 'paywall_subscriber' \n"||
                                                         "Is it native or custom dimension ? : custom"
                                                        ),
        premium_state               STRING          OPTIONS(description=
                                                         "(??)"||
                                                         "GA dimension: 'premium_level' \n"||
                                                         "Is it native or custom dimension ? : (??)"
                                                        ),
        first_open_time             TIMESTAMP       OPTIONS(description=
                                                        "The first open time \n"||
                                                        "GA dimension: 'first_open_time' \n"||
                                                        "Is it native or custom dimension ? : native"
                                                        ),
        previous_first_open_count   INT64           OPTIONS(description=
                                                         "Number of first open after uninstall / install. \n"||
                                                         "GA dimension: 'previous_first_open_count' \n"||
                                                         "Is it native or custom dimension ? : native"
                                                        ),
        keywords                    ARRAY<STRING>   OPTIONS(description=
                                                            "User keywords as array. \n"||
                                                            "GA dimension: 'keywords'  \n" ||
                                                            "Is it native or custom dimension ? : (??)"
                                                            ),
        tracking_info               STRUCT<
            source                      STRING  OPTIONS(description="Source of the first user acquisition \n"),
            medium                      STRING  OPTIONS(description="Medium of the first user acquisition \n"),
            campaign                    STRING  OPTIONS(description="Campaign of the first user acquisition \n"),
            default_channel_group       STRING  OPTIONS(description="Default channel group as defined by Google at user scale. \n"||
                                                        "It's the first channel group from where user has been arrived. \n"||
                                                        "For more details 👉🏻 https://support.google.com/analytics/answer/9756891?hl=en"
                                                        ),
            custom_channel_group        STRING  OPTIONS(description="Custom channel group as defined by Prisma Media at user scale. \n"||
                                                        "It's the first channel group from where user has been arrived. \n"||
                                                        "For more details 👉🏻 https://docs.google.com/spreadsheets/d/15IJMid4OrJKMythVk9udgdZfdKvnLYPuN-YXfRYpcRM/edit?usp=sharing"
                                                        )
        > OPTIONS(description="User first acquisition tracking information.")

    >   OPTIONS(description="User data."),
    session_data ARRAY<
        STRUCT<
            session_id                  STRING      NOT NULL    OPTIONS(description=
                                                                        "Session Id to flag unique session. \n"||
                                                                        "It's a concatenation of 'ga_session_id' and 'user_pseudo_id' \n"||
                                                                        "GA dimension: 'ga_session_id' \n"||
                                                                        "'ga_session_id': It's a native GA dimension and it correspond to stat session timestamp. \n"||
                                                                        "Is it native or custom dimension ? : native \n"||
                                                                        "For more details 👉🏻 https://support.google.com/analytics/answer/9191807"
                                                                       ),
            session_number              INTEGER     NOT NULL    OPTIONS(description=
                                                                        "Number of session that user has started up to the current session. \n"||
                                                                        "GA dimension: 'ga_session_number' \n"||
                                                                        "Is it native or custom dimension ? : native \n"||
                                                                        "For more details 👉🏻 https://support.google.com/analytics/answer/9191807"
                                                                       ),
            session_start_datetime      TIMESTAMP   NOT NULL    OPTIONS(description="First event timestamp's during the session."),
            session_end_datetime        TIMESTAMP   NOT NULL    OPTIONS(description="Last event timestamp's during the session"),
            is_engaged_session          BOOLEAN     NOT NULL    OPTIONS(description=
                                                                        "Is it session concedered as engaged by GA tracker ? \n"||
                                                                        "An engaged session is a session that lasts longer than 10 seconds, has a conversion event, or has at least 2 screenviews or screenviews. \n"||
                                                                        "GA dimension: 'session_engaged' \n"||
                                                                        "Is it native or custom dimension ? : native \n"||
                                                                        "For more details 👉🏻 https://support.google.com/analytics/answer/12195621"
                                                                       ),
            is_logged_session           BOOLEAN     NOT NULL    OPTIONS(description=
                                                                            "Is it a logged session ? \n"||
                                                                            "A logged session is a session that contains a 'pmc_session_start' custom event or a filled PMC Web Id. \n"||
                                                                            "GA dimension: 'web_pmc_id' \n"||
                                                                            "Is it native or custom dimension ? : custom \n"
                                                                           ),
            landing_screen                STRING                  OPTIONS(description=
                                                                            "The first screen on the session. \n"||
                                                                            "GA dimension: 'entrances' & 'screen_location' \n"||
                                                                            "Is it native or custom dimension ? : native \n"
                                                                           ),
            exit_screen                   STRING                  OPTIONS(description=
                                                                        "The last screen on the session. \n"||
                                                                        "GA dimension: 'screen_location' \n"||
                                                                        "Is it native or custom dimension ? : native \n"
                                                                       ),
            tracking_info               STRUCT<
                source                      STRING  OPTIONS(description=
                                                            "Session UTM source. \n"||
                                                            "This UTM source has been redressed (for non-direct) and it's attributed to the last non-direct source by user through the last 30 days. \n"||
                                                            "GA dimension: 'source' \n"||
                                                            "Is it native or custom dimension ? : native \n"||
                                                            "For more details 👉🏻 https://support.google.com/analytics/answer/9191807?hl=en#zippy=%2Cadjust-session-timeout:~:text=How%20session%20attribution%20works,also%20attributed%20to%20%E2%80%9Cgoogle%20/%20organic%E2%80%9D"
                                                            ),
                medium                      STRING  OPTIONS(description=
                                                            "Session UTM medium. \n"||
                                                            "This UTM medium has been redressed (for non-direct) and it's attributed to the last non-direct source by user through the last 30 days. \n"||
                                                            "GA dimension: 'medium' \n"||
                                                            "Is it native or custom dimension ? : native \n"||
                                                            "For more details 👉🏻 https://support.google.com/analytics/answer/9191807?hl=en#zippy=%2Cadjust-session-timeout:~:text=How%20session%20attribution%20works,also%20attributed%20to%20%E2%80%9Cgoogle%20/%20organic%E2%80%9D"
                                                            ),
                campaign                    STRING  OPTIONS(description=
                                                            "Session UTM campaign. \n"||
                                                            "This UTM campaign has been redressed (for non-direct) and it's attributed to the last non-direct source by user through the last 30 days. \n"||
                                                            "GA dimension: 'campaign' \n"||
                                                            "Is it native or custom dimension ? : native \n"||
                                                            "For more details 👉🏻 https://support.google.com/analytics/answer/9191807?hl=en#zippy=%2Cadjust-session-timeout:~:text=How%20session%20attribution%20works,also%20attributed%20to%20%E2%80%9Cgoogle%20/%20organic%E2%80%9D"
                                                            ),
                default_channel_group       STRING  OPTIONS(description="Default channel group as defined by Google at session scale. \n"||
                                                            "For more details 👉🏻 https://support.google.com/analytics/answer/9756891?hl=en"
                                                            ),
                custom_channel_group        STRING  OPTIONS(description="Custom channel group as defined by Prisma Media at session scale. \n"||
                                                            "For more details 👉🏻 https://docs.google.com/spreadsheets/d/15IJMid4OrJKMythVk9udgdZfdKvnLYPuN-YXfRYpcRM/edit?usp=sharing"
                                                            )
            >   OPTIONS(description="Session Tracking information."),
            device_info STRUCT<
                category                    STRING  OPTIONS(description="The device category (mobile, tablet, desktop)."),
                operating_system            STRING  OPTIONS(description="The operating system of the device."),
                operating_system_version    STRING  OPTIONS(description="The OS version."),
                brand_name                  STRING  OPTIONS(description="The device brand name."),
                model_name                  STRING  OPTIONS(description="The device model name."),
                marketing_name              STRING  OPTIONS(description="The device marketing name."),
                os_harware_model            STRING  OPTIONS(description="The device model information retrieved directly from the operating system.")
            >   OPTIONS(description="Device information"),
            geo_info    STRUCT<
                continent       STRING  OPTIONS(description="The continent from which events were reported, based on IP address."),
                sub_continent   STRING  OPTIONS(description="The subcontinent from which events were reported, based on IP address."),
                region          STRING  OPTIONS(description="The region from which events were reported, based on IP address."),
                country         STRING  OPTIONS(description="The country from which events were reported, based on IP address."),
                city            STRING  OPTIONS(description="The city from which events were reported, based on IP address."),
                metro           STRING  OPTIONS(description="The metro from which events were reported, based on IP address.")
            >   OPTIONS(description=
                        "Geographic information. \n"||
                        "For more details 👉🏻 https://support.google.com/analytics/answer/7029846/"
                       ),
            screen_data ARRAY<
                STRUCT<
                    screen_hit_id   STRING                  OPTIONS(description=
                                                                    "Screen Id within a hit and it start by <brand_trigram>_id. It serve to the mapping with G-Ad Manager Data. \n"||
                                                                    "GA dimension: 'page_hit_id' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                   ),
                    global_info     STRUCT<
                        screen_id           INT64          OPTIONS(description=
                                                                    "Screen Id. \n"||
                                                                    "GA dimension: 'firebase_screen_id' \n"||
                                                                    "Is it native or custom dimension ? : native"
                                                                    ),
                        screen_path         STRING         OPTIONS(description=
                                                                    "Screen title as path. \n"||
                                                                    "GA dimension: 'firebase_screen' \n"||
                                                                    "Is it native or custom dimension ? : native"
                                                                    ),
                        is_landing_screen   BOOLEAN        OPTIONS(description=
                                                                    "Is it landing screen ? \n"||
                                                                    "GA dimension: 'entrances' \n"||
                                                                    "Is it native or custom dimension ? : native"
                                                                   ),
                        is_exit_screen      BOOLEAN         OPTIONS(description="Is it exit screen ? It corresponds to the last viewed screen in the session."),
                        screen_level_1      STRING          OPTIONS(description="Screen path first level extracted from screen location."),
                        screen_level_2      STRING          OPTIONS(description="Screen path second level extracted from screen location."),
                        screen_level_3      STRING          OPTIONS(description="Screen path third level extracted from screen location."),
                        screen_level_4      STRING          OPTIONS(description="Screen path fourth level extracted from screen location."),
                        screen_class        STRING          OPTIONS(description=
                                                                    "Screen type. \n"||
                                                                    "GA dimension: 'firebase_screen_class' \n"||
                                                                    "Is it native or custom dimension ? : native"
                                                                    ),
                        screen_type         STRING          OPTIONS(description=
                                                                "Screen type. \n"||
                                                                "GA dimension: 'firebase_type' \n"||
                                                                "Is it native or custom dimension ? : native"
                                                                ),
                        screen_keywords     ARRAY<STRING>   OPTIONS(description=
                                                                    "List of screen keywords. \n"||
                                                                    "GA dimension: 'keywords' \n"||
                                                                    "Is it native or custom dimension ? : native"
                                                                   )
                    >   OPTIONS(description="Screen global information."),
                    content_info        STRUCT<
                        content_title            STRING          OPTIONS(description=
                                                                        "Content title. \n"||
                                                                        "GA dimension: 'content_title' \n"||
                                                                        "Is it native or custom dimension ? : custom"
                                                                        ),
                        content_type            ARRAY<STRING>   OPTIONS(description=
                                                                        "List of content type. \n"||
                                                                        "GA dimension: 'content_type' \n"||
                                                                        "Is it native or custom dimension ? : custom"
                                                                        ),
                        content_channel         STRING          OPTIONS(description=
                                                                        "Content channel. \n"||
                                                                        "GA dimension: 'content_channel' \n"||
                                                                        "Is it native or custom dimension ? : custom"
                                                                        )
                    >   OPTIONS(description="Content information."),
                    event_info ARRAY<
                        STRUCT <
                            event_name                  STRING      NOT NULL    OPTIONS(description=
                                                                                        "Event name. \n"||
                                                                                        "Event can be 'native' / 'custom' like ('start_session', 'screen_view', 'user_engagement', ...) . \n"||
                                                                                        "For more details 👉🏻 https://docs.google.com/spreadsheets/d/1m8_4tFyYqDxutbZ2aGBc7syDjX8E6qAvw9d4tTeHcNI/edit?usp=sharing"
                                                                                        ),
                            event_datetime              TIMESTAMP   NOT NULL    OPTIONS(description="Event datetime."),
                            event_platform              STRING      NOT NULL    OPTIONS(description="Event platform as ('WEB', 'IOS', 'ANDROID') <--> Hit datasource in GA Universal."),
                            engagement_time_in_seconds  FLOAT64                 OPTIONS(description=
                                                                                        "Engagement time in seconds estimated by GA tracker at each event. \n"||
                                                                                        "It's the amount of time by event and added to next one."||
                                                                                        "GA dimension: 'engagement_time_msec' \n"||
                                                                                        "Is it native or custom dimension ? : native \n"||
                                                                                        "For more details 👉🏻 https://support.google.com/analytics/answer/11109416"
                                                                                       ),
                            bookmark_info STRUCT<
                                bookmark_consent    STRING  OPTIONS(description="(??) : I need some explanation. "),
                                bookmark_label      STRING  OPTIONS(description="(??) : I need some explanation. "),
                                bookmark_type       STRING  OPTIONS(description=
                                                                    "Bookmark type as = (''follow:people'', ''follow:tag'', ...) \n."||
                                                                    "GA dimension: 'bookmark_type' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                  ),
                                bookmark_content    STRING  OPTIONS(description=
                                                                    "content id associated to bookmark. \n"||
                                                                    "GA dimension: 'bookmark_content' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                   )
                            > OPTIONS(description="Bookmark information."),
                             review_info STRUCT<
                                user_input_format   STRING  OPTIONS(description=
                                                                    "Input format as : ('text','photo'). \n"||
                                                                    "GA dimension: 'user_input_format' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                   ),
                                user_input_type     STRING  OPTIONS(description=
                                                                    "Input type as : ('comment','score'). \n"||
                                                                    "GA dimension: 'user_input_type' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                   ),
                                user_input_label    STRING  OPTIONS(description=
                                                                    "Input label to flag the content and placement. \n"||
                                                                    "GA dimension: 'user_input_label' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                   )
                            >   OPTIONS(description="User input information (comment/review).It's available only for TEL website."),
                            video_info STRUCT<
                                video_url           STRING  OPTIONS(description=
                                                                    "Video URL. \n"||
                                                                    "GA dimension: 'video_dm_url' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                  ),
                                video_launch_mode   STRING  OPTIONS(description=
                                                                    "Video launch mode as : ('autoplay', 'click'). \n"||
                                                                    "GA dimension: 'video_dm_launchmode' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                  ),
                                video_player        STRING  OPTIONS(description="??")
                            >   OPTIONS(description="Video information."),
                             magazine_info STRUCT<
                                 magazine_title     STRING  OPTIONS(description=
                                                                    "Magazine title. \n"||
                                                                    "GA dimension: 'magazine_title' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                  )
                            >   OPTIONS(description="Magazine information."),
                            purchase_info STRUCT<
                                 product_id         STRING      OPTIONS(description=
                                                                        "Product Id as 'sub_adfree_cmp', 'cmp_tel_03', ... . \n"||
                                                                        "GA dimension: 'product_id' \n"||
                                                                        "Is it native or custom dimension ? : native"
                                                                      ),
                                 product_name       STRING      OPTIONS(description=
                                                                        "Product name. \n"||
                                                                        "GA dimension: 'product_name' \n"||
                                                                        "Is it native or custom dimension ? : native"
                                                                      ),
                                price_per_unit      INT64       OPTIONS(description=
                                                                        "Price per unit. \n"||
                                                                        "GA dimension: 'price' \n"||
                                                                        "Is it native or custom dimension ? : native"
                                                                      ),
                                currency            STRING      OPTIONS(description=
                                                                        "Currency.\n"||
                                                                        "GA dimension: 'currency' \n"||
                                                                        "Is it native or custom dimension ? : native"
                                                                      ),
                                quantity            INT64       OPTIONS(description=
                                                                        "Quantity.\n"||
                                                                        "GA dimension: 'quantity' \n"||
                                                                        "Is it native or custom dimension ? : native"
                                                                      ),
                                is_validated        BOOLEAN     OPTIONS(description=
                                                                        "Is purchase validated ? .\n"||
                                                                        "GA dimension: 'validated' \n"||
                                                                        "Is it native or custom dimension ? : native"
                                                                      ),
                                is_subscribed        BOOLEAN     OPTIONS(description=
                                                                        "(??) .\n"||
                                                                        "GA dimension: 'subscription' \n"||
                                                                        "Is it native or custom dimension ? : native"
                                                                      )
                            >   OPTIONS(description="Purchase information."),
                             star_info STRUCT<
                                 star_name     STRING   OPTIONS(description=
                                                                    "Star name. \n"||
                                                                    "GA dimension: 'star_name' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                )
                            >   OPTIONS(description="Star information."),
                            user_action_info STRUCT<
                                action_name         STRING  OPTIONS(description=
                                                                    "Action name. \n"||
                                                                    "GA dimension: 'action' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                  ),
                                action_category     STRING  OPTIONS(description=
                                                                    "Action category. \n"||
                                                                    "GA dimension: 'categroy' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                  ),
                                action_label        STRING  OPTIONS(description=
                                                                    "Action label. \n"||
                                                                    "GA dimension: 'categroy' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                  ),
                                clicked_button_name STRING  OPTIONS(description=
                                                                    "Clicked button name. \n"||
                                                                    "GA dimension: 'button_name' \n"||
                                                                    "Is it native or custom dimension ? : custom"
                                                                  )
                            >   OPTIONS(description="User action information."),
                            login_info STRUCT<
                                login_role      STRING  OPTIONS(description=
                                                                "Login role as : ('adminlogin', 'adminrole', 'autologin', 'none', NULL). It's linked to login and signup events. \n"||
                                                                "GA dimension: 'login_role' \n"||
                                                                "Is it native or custom dimension ? : custom"
                                                               ),
                                login_method    STRING  OPTIONS(description=
                                                                "Login method as : ('apple', 'beloud', 'email', 'facebook', 'x', 'pinterest', 'magic link', 'mdp'). It''s linked to login and signup events.\n"||
                                                                "GA dimension: 'method' \n"||
                                                                "Is it native or custom dimension ? : custom"
                                                               )
                                > OPTIONS(description="Login information."),
                            signup_info STRUCT<
                                signup_method   STRING  OPTIONS(description=
                                                                "Signup method as : ('apple', 'beloud', 'email', 'facebook', 'x', 'pinterest', 'magic link', 'mdp'). It''s linked to login and signup events.\n"||
                                                                "GA dimension: 'method' \n"||
                                                                "Is it native or custom dimension ? : custom"
                                                                )
                            > OPTIONS(description="Login information."),
                            previous_screen_info STRUCT<
                                 previous_screen_id     INT64   OPTIONS(description=
                                                                        "Previous screen Id. It's related to `screen_view` event and it's used to link navigation throgh screens.\n"||
                                                                        "GA dimension: 'firebase_previous_id' \n"||
                                                                        "Is it native or custom dimension ? : native"
                                                                        ),
                                previous_screen_path    STRING  OPTIONS(description=
                                                                        "Previous screen Path. It's related to `screen_view` event and it's used to link navigation throgh screens.\n"||
                                                                        "GA dimension: 'firebase_previous_screen' \n"||
                                                                        "Is it native or custom dimension ? : native"
                                                                        ),
                                previous_screen_class   STRING  OPTIONS(description=
                                                                        "Previous screen Class. It's related to `screen_view` event and it's used to link navigation throgh screens.\n"||
                                                                        "GA dimension: 'firebase_previous_class' \n"||
                                                                        "Is it native or custom dimension ? : native"
                                                                        )
                            >   OPTIONS(description="Previous screen information."),
                            tracking_info STRUCT<
                                source      STRING  OPTIONS(description=
                                                            "Event UTM source. \n"||
                                                            "GA dimension: 'source' \n"||
                                                            "Is it native or custom dimension ? : native \n"
                                                            ),
                                medium      STRING  OPTIONS(description=
                                                            "Event UTM medium. \n"||
                                                            "GA dimension: 'medium' \n"||
                                                            "Is it native or custom dimension ? : native \n"
                                                            ),
                                campaign    STRING  OPTIONS(description=
                                                            "Event UTM campaign. \n"||
                                                            "GA dimension: 'campaign' \n"||
                                                            "Is it native or custom dimension ? : native \n"
                                                            )
                            >   OPTIONS(description="Event Tracking information."),
                            ecommerce_info STRUCT<
                                    transaction STRUCT<
                                        transaction_id      STRING  OPTIONS(description=
                                                                            "The transaction ID collected with the event. \n"||
                                                                            "GA dimension: 'transaction_id' \n"||
                                                                            "Is it native or custom dimension? : native \n"),
                                        total_item_quantity INT64   OPTIONS(description=
                                                                            "The number of items collected with the event. \n"||
                                                                            "GA dimension: 'total_item_quantity' \n"||
                                                                            "Is it native or custom dimension? : native \n"),
                                        unique_items        INT64   OPTIONS(description=
                                                                            "The number of unique items collected with the event. \n"||
                                                                            "GA dimension: 'unique_items' \n"||
                                                                            "Is it native or custom dimension? : native \n"),
                                        purchase_revenue    FLOAT64 OPTIONS(description=
                                                                            "Purchase revenue collected with the event (only for purchase events). \n"||
                                                                            "GA dimension: 'purchase_revenue' \n"||
                                                                            "Is it native or custom dimension? : native \n")
                                                        > OPTIONS(description=  "E-commerce transaction details."),
                                    items ARRAY<STRUCT<
                                        item_name     STRING  OPTIONS(description=
                                                                        "The name of the item collected with the event. \n"||
                                                                        "GA dimension: 'item_name' \n"||
                                                                        "Is it native or custom dimension? : native \n"),
                                        item_category STRING  OPTIONS(description=
                                                                        "The category of the item collected with the event. \n"||
                                                                        "GA dimension: 'item_category' \n"||
                                                                        "Is it native or custom dimension? : native \n"),
                                        quantity      INT64   OPTIONS(description=
                                                                        "The quantity of the item collected with the event. \n"||
                                                                        "GA dimension: 'quantity' \n"||
                                                                        "Is it native or custom dimension? : native \n"),
                                        price         FLOAT64 OPTIONS(description=
                                                                        "The price of the item collected with the event. \n"||
                                                                        "GA dimension: 'price' \n"||
                                                                        "Is it native or custom dimension? : native \n")
                                                    >> OPTIONS(description= "Items purchased in the event.")
                            > OPTIONS(description=  "E-commerce event data including transaction and items details.")
                        >
                    >   OPTIONS(description="Event information.")
                >
            >   OPTIONS(description="Screen data.")
        >
    >   OPTIONS(description="Session data."),
    PRIMARY KEY(partition_datetime, visit_date, user_pseudo_id) NOT ENFORCED
)
PARTITION BY TIMESTAMP_TRUNC(partition_datetime, HOUR)
OPTIONS(description="This table contains refined GA4 events live WEB. \n"||
        "It's generated from Live views refined data. \n"||
        "Table architecture: \n"||
        """visit date:
            |
            user
              |
              [sessions]
                    |
                    [pages]
                        |
                        [events]"""||
        "\n\n"||
        "Sync: Each 30 minutes. \n"||
        "DAG: {{ dag.dag_id }}"
);


{% for platform, property_configs in params.property_config.items() %}
    -- INSERT into destination TABLE across all GA properties
    DELETE FROM `{{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.{{ params.task_config["destination_bq_dataset"] }}.ga_events_live_{{ platform }}`
    WHERE partition_datetime = TIMESTAMP_TRUNC(TIMESTAMP("{{ next_execution_date }}"), MINUTE);
    INSERT INTO `{{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.{{ params.task_config["destination_bq_dataset"] }}.ga_events_live_{{ platform }}`
    {% for property_config in property_configs %}
        {% if loop.first %} WITH {% endif %} cte_{{ property_config["platform"] | upper }}_{{ property_config["brand_trigram"] | upper }}_{{ property_config["country"] | upper }}_{{ property_config["section"] | upper }} AS (
        SELECT * FROM `{{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.workspace.ga_events_{{ property_config["platform"] | upper }}_{{ property_config["brand_trigram"] | upper }}_{{ property_config["country"] | upper }}_{{ property_config["section"] | upper }}_intermediate`
        )
        {% if not loop.last %}
        ,
        {% endif %}
    {% endfor %}
    {% for property_config in property_configs %}
        SELECT * FROM cte_{{ property_config["platform"] | upper }}_{{ property_config["brand_trigram"] | upper }}_{{ property_config["country"] | upper }}_{{ property_config["section"] | upper }}
        {% if not loop.last %}
        UNION ALL
        {% endif %}
    {% endfor %}
    ;
{% endfor %}