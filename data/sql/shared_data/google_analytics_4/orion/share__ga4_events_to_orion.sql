-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.{{ params.task_config["destination_bq_dataset"] }}.{{ params.task_config["destination_bq_table"] }}`
(
        Date                      DATE        OPTIONS(description="Date of the visit. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#visit_date."),
        LastModified              DATETIME    OPTIONS(description="Current Datetime ('Europe/Paris')."),
        TransactionDate           TIMESTAMP   OPTIONS(description="Session start datetime. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_start_datetime."),
        Render                    STRING      OPTIONS(description="Platform as ('WEB', 'APP'). ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#property_data.platform ."),
        SiteCode                  STRING      OPTIONS(description="Brand trigram. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#property_data.brand_trigram ."),
        SiteCountry               STRING      OPTIONS(description="GA Property country. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#property_data.country ."),
        SiteSection               STRING      OPTIONS(description="GA Property section. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#property_data.section ."),
        SiteName                  STRING      OPTIONS(description="GA Property name. Value is set to NULL; to be completed by the TEAM. "),
        SiteTagGan                INT64       OPTIONS(description="GA property Id. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#property_data.property_id ."),
        SiteDomain                STRING      OPTIONS(description="GA Property domain name. Value is set to NULL; to be completed by the TEAM. "),
        AppVersion                STRING      OPTIONS(description="Application version available only for applications properties. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#property_data.app_info.version. "),
        DeviceCategory            STRING      OPTIONS(description="The device category (mobile, tablet, desktop) at session level. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.device_info.category ."),
        Os                        STRING      OPTIONS(description="The operating system of the device at session level. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.device_info.operating_system ."),
        MobileDeviceBranding      STRING      OPTIONS(description="The device model name at session level. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.device_info.model_name ."),
        MobileDeviceMarketingName STRING      OPTIONS(description="The device marketing name at session level. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.device_info.marketing_name ."),
        ScreenResolution          STRING      OPTIONS(description="The device screen resolution. Value is set to NULL because isn't available in GA4."),
        GeoNetworkContinent       STRING      OPTIONS(description="The continent from which events were reported, based on IP address at session level. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.geo_info.continent ."),
        GeoNetworkSubContinent    STRING      OPTIONS(description="The subcontinent from which events were reported, based on IP address at session level. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.geo_info.sub_continent ."),
        GeoNetworkCountry         STRING      OPTIONS(description="The country from which events were reported, based on IP address at session level. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.geo_info.country ."),
        GeoNetworkRegion          STRING      OPTIONS(description="The region from which events were reported, based on IP address at session level. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.geo_info.region ."),
        GeoNetworkCity            STRING      OPTIONS(description="The city from which events were reported, based on IP address at session level. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.geo_info.city ."),
        GeoNetworkLatitude        STRING      OPTIONS(description="Network latitude. Value is set to NULL because isn't available in GA4."),
        GeoNetworkLongitude       STRING      OPTIONS(description="Network longitude. Value is set to NULL because isn't available in GA4."),
        DataSource                STRING      OPTIONS(description="Event platform as ('WEB', 'IOS', 'ANDROID') <--> Hit datasource in GA Universal. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.screen/page_data.event_info.event_platform. "),
        Type                      STRING      OPTIONS(description="Event Type as ('PAGE', 'EVENT') based on event name. "),
        LoggedSession             FLOAT64     OPTIONS(description="0/1. 1 = Is logged session 0 otherwise. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.is_logged_session . "),
        EventAction               STRING      OPTIONS(description="Deprecated in GA4. Value is set to NULL. "),
        EventCategory             STRING      OPTIONS(description="Deprecated in GA4. Value is set to NULL. "),
        EventLabel                STRING      OPTIONS(description="Deprecated in GA4. Value is set to NULL. "),
        EventValue                INT64       OPTIONS(description="Deprecated in GA4. Value is set to NULL. "),
        EventName                 STRING      OPTIONS(description="Event name. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.screen/page_data.event_info.event_name ."),
        EventDatetime             TIMESTAMP   OPTIONS(description="Event datetime. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.screen/page_data.event_info.event_datetime ."),
        PagePath                  STRING      OPTIONS(description="Page canonical URL extracted from page location. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.screen/page_data.global_info.page_canonical_path ."),
        Url                       STRING      OPTIONS(description="Raw URL. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.screen/page_data.event_info.page_location ."),
        FirstUrl                  STRING      OPTIONS(description="Entrance URL in the sessions. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.landing_page/screen ."),
        HitNumber                 INT64       OPTIONS(description="Hit number is equivalent to event order inside a session  ."),
        PageHitId                 STRING      OPTIONS(description="Page hit id. ref : {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.screen/page_data.page_hit_id ."),
        SessionId                 STRING      OPTIONS(description="Session Id as CONCAT(user_pseudo_id, session_id). ref : {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.session_id ."),
        PageSessionId             STRING      OPTIONS(description="CONCAT(ev.page_location, user_pseudo_id, s.session_start_datetime). "),
        IdPMC                     STRING      OPTIONS(description="PMC web id. ref : {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#user_data.pmc_web_id ."),
        BatchId                   STRING      OPTIONS(description="Batch Id as PMC uuid in case of connected session otherwise install_id. ref : {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#user_data.batch_user_id ."),
        IsPMC                     BOOLEAN     OPTIONS(description="True = Is PMC profiles, False = isn't a PMC profile. "),
        SignPMC                   STRING      OPTIONS(description="Signup service. ref : {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.page_data.event_info.signup_service ." ),
        ContextPMC                STRING      OPTIONS(description="N/C ." ),
        TargetSegmentPMC          STRING      OPTIONS(description="N/C ." ),
        ContentObjectId           STRING      OPTIONS(description="CONCAT(content_info.content_type, ':', p.content_info.content_uuid) ."),
        ChannelGrouping           STRING      OPTIONS(description="Custom channel group as defined by Prisma Media at session scale. \n"
                                                            ||"ref : {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.tracking_info.custom_channel_group .\n"
                                                            ||"For more details 👉🏻 https://docs.google.com/spreadsheets/d/15IJMid4OrJKMythVk9udgdZfdKvnLYPuN-YXfRYpcRM/edit?usp=sharing . "),
        TsCampaign                STRING      OPTIONS(description="Session UTM campaign. \n"
                                                            ||"This UTM campaign has been redressed (for non-direct) and it's attributed to the last non-direct source by user through the last 30 days. \n"
                                                            ||"For more details 👉🏻 https://support.google.com/analytics/answer/9191807?hl=en#zippy=%2Cadjust-session-timeout:~:text=How%20session%20attribution%20works,also%20attributed%20to%20%E2%80%9Cgoogle%20/%20organic%E2%80%9D "),
        TsSource                  STRING       OPTIONS(description="Session UTM source. \n"
                                                            ||"This UTM source has been redressed (for non-direct) and it''s attributed to the last non-direct source by user through the last 30 days.  \n"
                                                            ||"For more details 👉🏻 https://support.google.com/analytics/answer/9191807?hl=en#zippy=%2Cadjust-session-timeout:~:text=How%20session%20attribution%20works,also%20attributed%20to%20%E2%80%9Cgoogle%20/%20organic%E2%80%9D "),
        TsMedium                  STRING      OPTIONS(description="Session UTM medium. \n"
                                                            ||"This UTM medium has been redressed (for non-direct) and it''s attributed to the last non-direct medium by user through the last 30 days.  \n"
                                                            ||"For more details 👉🏻 https://support.google.com/analytics/answer/9191807?hl=en#zippy=%2Cadjust-session-timeout:~:text=How%20session%20attribution%20works,also%20attributed%20to%20%E2%80%9Cgoogle%20/%20organic%E2%80%9D "),
        Section                   STRING      OPTIONS(description="Page path first level extracted from page location. ref : {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.page/screen_data.global_info.page_level_1 ." ),
        SubSection                STRING      OPTIONS(description="Page path second level extracted from page location . ref : {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.page/screen_data.global_info.page_level_2 ." ),
        HostName                  STRING      OPTIONS(description="Page hostname.\n"
                                                                ||"When available directly from GA4 data, source is ref : {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.page/screen_data.global_info.page_hostname\n"
                                                                ||"Otherwise, we use a mapping table (which source is https://docs.google.com/spreadsheets/d/1YSLpOX02vMZoRScP1nMfjnNeUQ9aqnIcM-j9bg1Yqxo/edit?gid=2143015465#gid=2143015465)."
                                                                ||"In these cases, hostname may not match exactly at page-level, but on property-level."),
        AbTasty                   STRING      OPTIONS(description="A/B test dimension. ref : {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.page/screen_data.event_info.ab_test_info.ab_test_variation ." ),
        AbTastyAd                 STRING      OPTIONS(description="Page path second level extracted from page location . ref : {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.page/screen_data.event_info.ab_test_info.ab_test_variation_ad ." ),
        AbTastyInfo ARRAY<
            STRUCT<
                AbTastyCampaign     STRING      OPTIONS(description="Campaign ID and Campaign Name formatted as [campaign_id] campaign_name. "||
                                                                   "GA dimension: 'abtasty_campaign' \n"||
                                                                   "Is it native or custom dimension ? : native"
                                                                ),
                AbTastyVariation    STRING      OPTIONS(description=" Variation ID and Variation Name formatted as [variation_id] variation_name. "||
                                                                              "GA dimension: 'abtasty_variation' \n"||
                                                                              "Is it native or custom dimension ? : native"
                                                                )
            >
        > OPTIONS(description="A/B Tasty information."),
        AbTastyCampaign           STRING      OPTIONS(description="Campaign ID and Campaign Name formatted as [campaign_id] campaign_name separated by '||'. "||
                                                                   "GA dimension: 'abtasty_campaign' \n"||
                                                                   "Is it native or custom dimension ? : native"
                                                    ),
        AbTastyVariation          STRING      OPTIONS(description=" Variation ID and Variation Name formatted as [variation_id] variation_name separated by '||'. "||
                                                                      "GA dimension: 'abtasty_variation' \n"||
                                                                      "Is it native or custom dimension ? : native"
                                                    ),
        PageType                  STRING      OPTIONS(description="Page Type. ref : {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.page/screen_data.page_data.page_type ." ),
        TypePage                  STRING      OPTIONS(description="Type Page."),
        PageCategory              STRING      OPTIONS(description="Page category. ref : {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.page/screen_data.global_info.page_category ." ),
        PageSubCategory           STRING      OPTIONS(description="Deprecated in GA4. Value is set to NULL. "),
        HasVideo                  STRING      OPTIONS(description="Indicates the name of the video player, in case any exists. ref : {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.page/screen_data.event_info.video_info.video_player ."),
        DataWall                  STRING      OPTIONS(description="Actually value isn't available in GA4. However, this field will available soon. "),
        TypeArticleSession        STRING      OPTIONS(description="Deprecated in GA4. Value is set to NULL. "),
        Tags                      STRING      OPTIONS(description="List of page keywords. ref : {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.page/screen_data.global_info.page_keywords  "),
        ScreenName                STRING      OPTIONS(description="Screen path. NULL for WEB ref : {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.screen_data.? (TBD) "),
        TotalTimeOnPage           FLOAT64     OPTIONS(description="Engagement time in seconds estimated by GA tracker at each event. \n"
                                                            ||"It's the amount of time by event and added to next one. \n"
                                                            ||"https://support.google.com/analytics/answer/11109416 \n"
                                                            ||"ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.screen_data.event_info.engagement_time_in_seconds ."),
        IsAmpUrl                  INT64       OPTIONS(description="Deprecated in GA4. Value is set to NULL. "),
        Bounces                   INT64     OPTIONS(description="1 / LENGTH(user_data): Not engaged session otherwise 0. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.is_engaged_session ."),
        Engaged                   INT64     OPTIONS(description="1 / LENGTH(user_data): engaged session otherwise 0. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.is_engaged_session"),
        Exits                     INT64     OPTIONS(description="1 / LENGTH(user_data): exit page otherwise 0. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.page/screen_data.global_info.is_exit_screen/page ."),
        Entrances                 INT64     OPTIONS(description="1 / LENGTH(user_data): entrance page otherwise 0. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.page/screen_data.global_info.is_entrance_screen/page ."),
        PageViewsNoExit           INT64     OPTIONS(description="1 / LENGTH(user_data): exit page / screen view otherwise 0. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.page/screen_data.global_info.is_exit_screen/page ."),
        Users ARRAY<
            STRUCT<
              FullVisitorID  STRING  OPTIONS(description="Google Analytics pseudo Id generated by GA Tracker. \n"
                                                        ||"It depends on device according to Google Documentation. \n"
                                                        ||"For more details 👉🏻 https://www.optizent.com/blog/what-is-user_pseudo_id-in-ga4-bigquery-export/. \n"
                                                        ||"ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#user_pseudo_id "),
              ClientId       STRING  OPTIONS(description="Google Analytics client Id as cookie first-party.\n"
                                                        ||"For more details 👉🏻 https://www.analyticsmania.com/post/google-analytics-client-id/"
                                                        ||"ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_WEB_*#session_data.user_data.client_id ."),
              VisitNumber    INT64   OPTIONS(description="Number of session by user. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.session_number ."),
              NewVisits      INT64   OPTIONS(description="1: Is new visit otherwise 0. ref: {{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_events_APP/WEB_*#session_data.page_data.event_info.event_name ."),
              VisitStartTime INT64   OPTIONS(description="Session starttime as UNIX timestamp.")
            >
        > OPTIONS(description="Users information's. "),
        PageViews                 INT64       OPTIONS(description="1 / LENGTH(user_data) page views ratio."),
        Sessions                  INT64       OPTIONS(description="1 / LENGTH(user_data) sessions ratio."),
        FirstUrlHostName          STRING      OPTIONS(description="Hostname of the entrance URL in the session. ref: pm-prod-ga4.refined_data.ga_events_APP/WEB_*#session_data.landing_page/screen.")
)
PARTITION BY Date
CLUSTER BY Render, SiteCode, SiteCountry, SiteSection
OPTIONS(description="This table contains refined GA4 event's since {{ params.task_config['start_date']['WEB'] }} for WEB and {{ params.task_config['start_date']['APP'] }} for APP.\n"
                    ||"This table is shared with ORION team. \n"
                    ||"It contains all GA4 (APP / WEB) properties. You can find GA4 properties in this G-sheet 👉🏻 https://docs.google.com/spreadsheets/d/1YSLpOX02vMZoRScP1nMfjnNeUQ9aqnIcM-j9bg1Yqxo/edit?usp=sharing  \n"
                    ||"Sync: Daily. \n"
                    ||"DAG: {{ dag.dag_id }}")
;

-- WEB
{% if params.task_config["is_full_load"]["WEB"] %}
    DELETE FROM `{{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.{{ params.task_config["destination_bq_dataset"] }}.{{ params.task_config["destination_bq_table"] }}`
    WHERE Date BETWEEN DATE("{{ params.task_config['start_date']['WEB'] }}") AND DATE("{{ params.task_config['end_date']['WEB'] }}") AND Render = "WEB";
{% else %}
    DELETE FROM `{{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.{{ params.task_config["destination_bq_dataset"] }}.{{ params.task_config["destination_bq_table"] }}`
    WHERE Date >= DATE_SUB(DATE("{{ next_ds }}"), INTERVAL 2 DAY) AND Render = "WEB";
{% endif %}

-- APP
{% if params.task_config["is_full_load"]["APP"] %}
    DELETE FROM `{{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.{{ params.task_config["destination_bq_dataset"] }}.{{ params.task_config["destination_bq_table"] }}`
    WHERE Date BETWEEN DATE("{{ params.task_config['start_date']['APP'] }}") AND DATE("{{ params.task_config['end_date']['APP'] }}") AND Render = "APP";
{% else %}
    DELETE FROM `{{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.{{ params.task_config["destination_bq_dataset"] }}.{{ params.task_config["destination_bq_table"] }}`
    WHERE Date >= DATE_SUB(DATE("{{ next_ds }}"), INTERVAL 2 DAY) AND Render = "APP";
{% endif %}

-- ga4_events_WEB
INSERT INTO  `{{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.{{ params.task_config["destination_bq_dataset"] }}.{{ params.task_config["destination_bq_table"] }}`
WITH ab_tasty_info AS (
      -- Retrieve A/B Test data for WEB property
      -- Reason for creating this CTE:
      -- A/B testing data is recorded at the event level, specifically tied to "abtasty" events. If we attempt to directly compute the number of sessions or page views, it won't be possible.
      -- Therefore, we create this CTE to filter exclusively for "abtasty" events. Later, this filtered data will be propagated to other events within the same session (refer to the "get_ga4_web" CTE).
      SELECT
        visit_date,
        user_pseudo_id,
        property_data.property_id,
        sessions.session_id,
        -- pages.page_hit_id,
        events.page_location,
        -- at the same Page hit & Page Location, we can have multiple A/B Tasty ! For this reason, we aggregate A/B Tasty information into an ARRAY of STRUCT.
        `{{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.dedup`(ARRAY_AGG(
          IF(
            events.ab_test_info.native_info.abtasty_campaign IS NOT NULL
            AND
            events.ab_test_info.native_info.abtasty_variation IS NOT NULL,
            STRUCT(
              events.ab_test_info.native_info.abtasty_campaign  AS AbTastyCampaign,
              events.ab_test_info.native_info.abtasty_variation AS AbTastyVariation
            ),
            NULL
          )
          IGNORE NULLS
          ORDER BY event_datetime
        )) AS abtasty_info,
        STRING_AGG(events.ab_test_info.native_info.abtasty_campaign, "||" ORDER BY event_datetime)  AS AbTastyCampaign,
        STRING_AGG(events.ab_test_info.native_info.abtasty_variation, "||" ORDER BY event_datetime) AS AbTastyVariation,
        STRING_AGG(DISTINCT events.ab_test_info.ab_test_variation, "||" )     AS AbTasty,
        STRING_AGG(DISTINCT events.ab_test_info.ab_test_variation_ad, "||" )  AS AbTastyAd
      FROM `{{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.{{ params.task_config["source_bq_table"]["WEB"] }}` AS e
      JOIN UNNEST(session_data)   AS sessions
      JOIN UNNEST(page_data)      AS pages
      JOIN UNNEST(event_info)     AS events
      {% if params.task_config["is_full_load"]["WEB"] %}
        WHERE visit_date BETWEEN DATE("{{ params.task_config['start_date']['WEB'] }}") AND DATE("{{ params.task_config['end_date']['WEB'] }}")
      {% else %}
        WHERE visit_date >= DATE_SUB(DATE("{{ next_ds }}"), INTERVAL 2 DAY)
      {% endif %}
      AND
      (
        events.event_name = "abtasty"
        OR
        (
           events.ab_test_info.ab_test_variation IS NOT NULL OR events.ab_test_info.ab_test_variation != ""
        )
        OR
        (
          events.ab_test_info.ab_test_variation_ad IS NOT NULL OR events.ab_test_info.ab_test_variation_ad != ""
        )
      )
      GROUP BY ALL
), get_ga4_web AS (
     -- Get the GA4 events for the selected time window
     -- For all KPIs, we divide by array length (user_data) due to repeated fields !
     SELECT
        e.visit_date AS Date,
        CURRENT_DATETIME('Europe/Paris')    AS LastModified,
        -- Property info
        sessions.session_start_datetime     AS TransactionDate,
        property_data.platform      AS Render, -- NEW
        property_data.brand_trigram AS SiteCode,
        property_data.country       AS SiteCountry, -- NEW
        property_data.section       AS SiteSection,  -- NEW
        CAST(NULL AS STRING)        AS SiteName,    -- Not available
        property_data.property_id   AS SiteTagGan,
        CAST(NULL AS STRING)        AS SiteDomain,  -- Not available
        CAST(NULL AS STRING)        AS AppVersion,  -- Not available
        -- Device Info
        sessions.device_info.category               AS DeviceCategory,
        sessions.device_info.operating_system       AS Os,
        sessions.device_info.mobile.model_name      AS MobileDeviceBranding,
        sessions.device_info.mobile.marketing_name  AS MobileDeviceMarketingName,
        CAST(NULL AS STRING)                        AS ScreenResolution, -- Not available
        -- Geo Info
        sessions.geo_info.continent     AS GeoNetworkContinent,
        sessions.geo_info.sub_continent AS GeoNetworkSubContinent,
        sessions.geo_info.country       AS GeoNetworkCountry,
        sessions.geo_info.region        AS GeoNetworkRegion,
        sessions.geo_info.city          AS GeoNetworkCity,
        CAST(NULL AS STRING)            AS GeoNetworkLatitude, -- Not available
        CAST(NULL AS STRING)            AS GeoNetworkLongitude, -- Not available
        events.event_platform           AS DataSource,
        IF(events.event_name = "page_view", "PAGE", "EVENT") AS Type,
        IF(sessions.is_logged_session AND event_name = "session_start", 1, 0) AS LoggedSession,
        CAST(NULL AS STRING)        AS EventAction, -- Not available
        CAST(NULL AS STRING)        AS EventCategory, -- Not available
        CAST(NULL AS STRING)        AS EventLabel, -- Not available
        CAST(NULL AS INT64)         AS EventValue, -- Not available
        events.event_name           AS EventName, -- NEW
        events.event_datetime       AS EventDatetime, -- NEW
        IF(pages.global_info.page_canonical_path IS NOT NULL, IFNULL(REGEXP_EXTRACT(pages.global_info.page_canonical_path, NET.HOST(pages.global_info.page_canonical_path) || '[^/]*/(.+)'), "/"), NULL) AS PagePath,
        IF(events.page_location IS NOT NULL, IFNULL(REGEXP_EXTRACT(events.page_location, NET.HOST(events.page_location) || '[^/]*/(.+)'), "/"), NULL)       AS Url,
        IF(sessions.landing_page IS NOT NULL, IFNULL(REGEXP_EXTRACT(sessions.landing_page, NET.HOST(sessions.landing_page) || '[^/]*/(.+)'), "/"), NULL)    AS FirstUrl,
        ROW_NUMBER() OVER SESSION_WINDOW AS HitNumber,
        pages.page_hit_id           AS PageHitId,
        sessions.session_id         AS SessionId,
        CONCAT(IF(events.page_location IS NOT NULL, IFNULL(REGEXP_EXTRACT(events.page_location, NET.HOST(events.page_location) || '[^/]*/(.+)'), "/"), ""), "|", e.user_pseudo_id, "|", CAST(UNIX_MILLIS(sessions.session_start_datetime) AS STRING)) AS PageSessionId,
        ARRAY_TO_STRING(user_data.pmc_web_id, "||")     AS IdPMC,
        ARRAY_TO_STRING(user_data.batch_user_id, "||")  AS BatchId,
        (ARRAY_LENGTH(user_data.pmc_web_id) > 0)        AS IsPMC,
        events.signup_service   AS SignPMC,
        "N/C"      AS ContextPMC,
        "N/C"      AS TargetSegmentPMC,
        CONCAT(pages.content_info.content_type, ":", pages.content_info.content_uuid)  AS ContentObjectId,
        sessions.tracking_info.custom_channel_group AS ChannelGrouping,
        sessions.tracking_info.campaign AS TsCampaign,
        sessions.tracking_info.source   AS TsSource,
        sessions.tracking_info.medium   AS TsMedium,
        pages.global_info.page_level_1  AS Section,
        pages.global_info.page_level_2  AS SubSection,
        COALESCE(pages.global_info.page_hostname, gpm.hostname) AS HostName,
        abt.AbTasty,
        abt.AbTastyAd,
        abtasty_info AS AbTastyInfo,
        AbTastyCampaign,
        AbTastyVariation,
        pages.global_info.page_type     AS PageType,
        CAST(NULL AS STRING)            AS TypePage,
        pages.global_info.page_category AS PageCategory,
        CAST(NULL AS STRING)            AS PageSubCategory, -- Not available
        events.video_info.video_player  AS HasVideo,
        CAST(NULL AS STRING) AS DataWall, -- Column value to be defined when NEW events will deployed in GA4.
        CAST(NULL AS STRING) AS TypeArticleSession, -- Not available
        ARRAY_TO_STRING(pages.global_info.page_keywords, "||") AS Tags,
        CAST(NULL AS STRING)                AS ScreenName, -- Available for APP
        events.engagement_time_in_seconds   AS TotalTimeOnPage,
        CAST(NULL AS INT64) AS IsAmpUrl,  -- Not available
        IF(NOT sessions.is_engaged_session  AND event_name = "session_start", 1, 0)         AS Bounces,
        IF(sessions.is_engaged_session AND event_name = "session_start", 1, 0)              AS  Engaged,
        IF(pages.global_info.is_exit_page AND events.event_name = "session_start", 1, 0)    AS Exits,
        IF(pages.global_info.is_landing_page AND events.event_name = "session_start", 1, 0) AS Entrances,
        IF(events.event_name = "page_view" AND NOT pages.global_info.is_exit_page, 1, 0)    AS PageViewsNoExit,
        `pm-prod-ga4.refined_data.dedup`(ARRAY_AGG(
            STRUCT(
              e.user_pseudo_id    AS FullVisitorID,
              ARRAY_TO_STRING(user_data.client_id, "||")  AS ClientId,
              sessions.session_number  AS VisitNumber,
              IF(events.event_name = "first_visit", 1, 0) AS NewVisits,
              SAFE_CAST(
                SPLIT(
                    sessions.session_id, e.user_pseudo_id
                )[SAFE_ORDINAL(2)]
                AS INT64
              ) AS VisitStartTime
            )
          ) OVER SESSION_WINDOW
        ) AS Users,
        IF(events.event_name = "page_view", 1, 0)       AS PageViews,
        IF(events.event_name = "session_start", 1, 0)   AS Sessions,
        -- This Ranking is due to "ab_tasty_info" JOIN because, in the same session we can have multiple A/B tasty per page --> So we use it later to keep one time "session_start" row
        IF(events.event_name = "session_start", ROW_NUMBER() OVER EVENT_WINDOW, -1) AS nb_record_by_event,
        COALESCE(NET.HOST(sessions.landing_page), pages.global_info.page_hostname, gpm.hostname) AS FirstUrlHostName  -- NEW
    FROM `{{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.{{ params.task_config["source_bq_table"]["WEB"] }}` AS e
    -- UNNEST session/page/event data
    JOIN UNNEST(session_data)   AS sessions
    JOIN UNNEST(page_data)      AS pages
    JOIN UNNEST(event_info)     AS events
    --  A/B tasty data
    LEFT JOIN ab_tasty_info AS abt ON abt.visit_date = e.visit_date
                                   AND abt.user_pseudo_id = e.user_pseudo_id
                                   AND abt.property_id  = e.property_data.property_id
                                   AND abt.session_id = sessions.session_id
                                   AND
                                   (
                                      abt.page_location = events.page_location
                                      OR
                                      events.event_name = "session_start"
                                   )
    LEFT JOIN `{{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.ga_property_mapping` AS gpm
        ON e.property_data.property_id = gpm.property_id
    {% if params.task_config["is_full_load"]["WEB"] %}
        WHERE e.visit_date BETWEEN DATE("{{ params.task_config['start_date']['WEB'] }}") AND DATE("{{ params.task_config['end_date']['WEB'] }}")
    {% else %}
        WHERE e.visit_date >= DATE_SUB(DATE("{{ next_ds }}"), INTERVAL 2 DAY)
    {% endif %}
    QUALIFY nb_record_by_event = 1 OR nb_record_by_event = -1
    WINDOW
      SESSION_WINDOW AS (PARTITION BY e.property_data.property_id, e.visit_date, sessions.session_id ORDER BY events.event_datetime),
      EVENT_WINDOW AS (PARTITION BY e.property_data.property_id, e.visit_date, sessions.session_id, events.event_name, events.event_datetime)
), get_ga4_app AS (
    -- Get the GA4 events for the selected time window
    SELECT
        visit_date AS Date,
        CURRENT_DATETIME('Europe/Paris')    AS LastModified,
        -- Property info
        sessions.session_start_datetime     AS TransactionDate,
        property_data.platform              AS Render, -- NEW
        property_data.brand_trigram         AS SiteCode,
        property_data.country               AS SiteCountry, -- NEW
        property_data.section               AS SiteSection,  -- NEW
        CAST(NULL AS STRING)                AS SiteName,    -- Not available
        property_data.property_id           AS SiteTagGan,
        CAST(NULL AS STRING)                AS SiteDomain,  -- Not available
        property_data.app_info.version      AS AppVersion,  -- Not available
        -- Device Info
        sessions.device_info.category              AS DeviceCategory,
        sessions.device_info.operating_system      AS Os,
        sessions.device_info.model_name     AS MobileDeviceBranding,
        sessions.device_info.marketing_name AS MobileDeviceMarketingName,
        CAST(NULL AS STRING)                AS ScreenResolution, -- Not available
        -- Geo Info
        sessions.geo_info.continent     AS GeoNetworkContinent,
        sessions.geo_info.sub_continent AS GeoNetworkSubContinent,
        sessions.geo_info.country       AS GeoNetworkCountry,
        sessions.geo_info.region        AS GeoNetworkRegion,
        sessions.geo_info.city          AS GeoNetworkCity,
        CAST(NULL AS STRING)            AS GeoNetworkLatitude, -- Not available
        CAST(NULL AS STRING)            AS GeoNetworkLongitude, -- Not available
        events.event_platform           AS DataSource,
        IF(events.event_name = "screen_view", "PAGE", "EVENT") AS Type,
        IF(sessions.is_logged_session AND event_name = "session_start", 1, 0) AS LoggedSession,
        CAST(NULL AS STRING)      AS EventAction, -- Not available
        CAST(NULL AS STRING)      AS EventCategory, -- Not available
        CAST(NULL AS STRING)      AS EventLabel, -- Not available
        CAST(NULL AS INT64)       AS EventValue, -- Not available
        events.event_name       AS EventName, -- NEW
        events.event_datetime   AS EventDatetime, -- NEW
        screens.global_info.screen_path AS PagePath,
        screens.global_info.screen_path AS Url,
        sessions.landing_screen          AS FirstUrl,
        ROW_NUMBER() OVER SESSION_WINDOW AS HitNumber,
        screens.screen_hit_id           AS PageHitId,
        sessions.session_id             AS SessionId,
        CONCAT(IFNULL(screens.global_info.screen_path, ""), "|", user_pseudo_id, "|", CAST(UNIX_MILLIS(sessions.session_start_datetime) AS STRING)) AS PageSessionId,
        ARRAY_TO_STRING(user_data.pmc_uuid, "||")   AS IdPMC,
        CAST(NULL AS STRING)                        AS BatchId, -- Not available
        (ARRAY_LENGTH(user_data.pmc_uuid) > 0)      AS IsPMC,
        CAST(NULL AS STRING)    AS SignPMC, -- Not available
        "N/C"      AS ContextPMC,
        "N/C"      AS TargetSegmentPMC,
        CAST(NULL AS STRING)  AS ContentObjectId, -- Not available
        sessions.tracking_info.custom_channel_group AS ChannelGrouping,
        sessions.tracking_info.campaign AS TsCampaign,
        sessions.tracking_info.source   AS TsSource,
        sessions.tracking_info.medium   AS TsMedium,
        screens.global_info.screen_level_1  AS Section,
        screens.global_info.screen_level_2  AS SubSection,
        CAST(NULL AS STRING) AS HostName, -- Not available
        CAST(NULL AS STRING) AS AbTasty,
        CAST(NULL AS STRING) AS AbTastyAd,
        ARRAY_AGG(
          STRUCT(
            CAST(NULL AS STRING) AS AbTastyCampaign,
            CAST(NULL AS STRING) AS AbTastyVariation
          )
        ) OVER SESSION_WINDOW AS AbTastyInfo,
        CAST(NULL AS STRING) AS AbTastyCampaign,
        CAST(NULL AS STRING) AS AbTastyVariation,
        screens.global_info.screen_type     AS PageType,
        CAST(NULL AS STRING) AS TypePage,
        screens.global_info.screen_class    AS PageCategory,
        CAST(NULL AS STRING)        AS PageSubCategory, -- Not available
        events.video_info.video_player AS HasVideo,
        CAST(NULL AS STRING) AS DataWall, -- Column value to be defined when NEW events will deployed in GA4.
        CAST(NULL AS STRING) AS TypeArticleSession, -- Not available
        ARRAY_TO_STRING(screens.global_info.screen_keywords, "||") AS Tags,
        CAST(NULL AS STRING) AS ScreenName, -- Available for APP
        events.engagement_time_in_seconds AS TotalTimeOnPage,
        CAST(NULL AS INT64) AS IsAmpUrl,  -- Not available
        IF(NOT sessions.is_engaged_session AND event_name = "session_start", 1, 0) AS Bounces,
        IF(sessions.is_engaged_session AND event_name = "session_start", 1, 0) AS Engaged,
        IF(screens.global_info.is_exit_screen AND event_name = "session_start", 1, 0) AS Exits,
        IF(screens.global_info.is_landing_screen AND event_name = "session_start", 1, 0) AS Entrances,
        IF(events.event_name = "screen_view" AND NOT screens.global_info.is_exit_screen, 1, 0) AS PageViewsNoExit,
        `{{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.dedup`(
          ARRAY_AGG(
            STRUCT(
              user_pseudo_id    AS FullVisitorID,
              CAST(NULL AS STRING) AS ClientId,
              sessions.session_number  AS VisitNumber,
              IF(events.event_name = "first_open", 1, 0) AS NewVisits,
              SAFE_CAST(
                SPLIT(
                    sessions.session_id, user_pseudo_id
                )[SAFE_ORDINAL(2)]
                AS INT64
              ) AS VisitStartTime
            )
        )
        OVER SESSION_WINDOW
        ) AS Users,
        IF(events.event_name = "screen_view", 1, 0) AS PageViews,
        -- Some sessions doesn't have "session_start" as first event
        IF(events.event_name = "session_start", 1, 0) AS Sessions,
        CAST(NULL AS STRING) AS FirstUrlHostName -- NEW
    FROM `{{ params.task_config["source_bq_project"][ params.env ]["ga4"] }}.refined_data.{{ params.task_config["source_bq_table"]["APP"] }}` AS e
    -- UNNEST session/page/event data
    JOIN UNNEST(session_data)   AS sessions
    JOIN UNNEST(screen_data)    AS screens
    JOIN UNNEST(event_info)     AS events
    {% if params.task_config["is_full_load"]["APP"] %}
        WHERE visit_date BETWEEN DATE("{{ params.task_config['start_date']['APP'] }}") AND DATE("{{ params.task_config['end_date']['APP'] }}")
    {% else %}
        WHERE visit_date >= DATE_SUB(DATE("{{ next_ds }}"), INTERVAL 2 DAY)
    {% endif %}
    WINDOW SESSION_WINDOW AS (PARTITION BY visit_date, property_data.property_id, sessions.session_id ORDER BY events.event_datetime)
)
SELECT * EXCEPT(nb_record_by_event) FROM get_ga4_web
UNION ALL
SELECT * FROM get_ga4_app;
