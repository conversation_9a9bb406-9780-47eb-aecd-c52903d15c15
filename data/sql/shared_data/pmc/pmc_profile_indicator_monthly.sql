-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE start_date DATE DEFAULT DATE_TRUNC(DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL 1 MONTH), MONTH);
DECLARE end_date DATE DEFAULT LAST_DAY(DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL 1 MONTH));

{% if params.task_config['is_full'] %}
SET start_date = DATE("{{ params.task_config['start_date'] }}");
SET end_date = DATE("{{ params.task_config['end_date'] }}");
{% endif %}

CREATE TABLE IF NOT EXISTS `{{ params.task_config['source_bq_project'][params.env]['matrix'] }}.share_marketing.pmc_profile_indicator_monthly`
(
    observation_month     DATE NOT NULL OPTIONS(description="Observation month in the format '%Y-%m-01."),
    indicator STRUCT<
        global_level ARRAY<
            STRUCT<
                nb_pmc_account                INT64 NOT NULL OPTIONS(description="Total of PMC account at the beginning of the month."),
                nb_pmc_account_new            INT64 NOT NULL OPTIONS(description="Total of new PMC account at observation month."),
                nb_pmc_account_active         INT64 NOT NULL OPTIONS(description="Total of active PMC account at the beginning of the month. Active = Login on the last 30 days at the beginning of the month."),
                nb_pmc_account_sub_paid_offer INT64 NOT NULL OPTIONS(description="Total of PMC account having paid subscription at the beginning of the month.")
            >
        > OPTIONS(description="PMC profiles indicators at global level."),
        signup_brand_level ARRAY<
            STRUCT<
                signup_brand_trigram          STRING NOT NULL OPTIONS(description="The brand from which PMC account has been created."),
                nb_pmc_account                INT64 NOT NULL OPTIONS(description="Total of PMC account at the beginning of the month."),
                nb_pmc_account_new            INT64 NOT NULL OPTIONS(description="Total of new PMC account at observation month."),
                nb_pmc_account_active         INT64 NOT NULL OPTIONS(description="Total of active PMC account at the beginning of the month. Active = Login on the last 30 days at the beginning of the month."),
                nb_pmc_account_sub_paid_offer INT64 NOT NULL OPTIONS(description="Total of PMC account having paid subscription at the beginning of the month.")
            >
        > OPTIONS(description="PMC profiles indicators at signup brand level."),
        socio_demographic_level ARRAY<
            STRUCT<
                gender                        STRING NOT NULL OPTIONS(description="The gender of PMC account."),
                age_bin                       STRING NOT NULL OPTIONS(description="Age bin defined as below: \n"
                                                                                ||"BETWEEN 18 AND 24 THEN 'Jeune adulte'\n"
                                                                                ||"BETWEEN 25 AND 34 THEN 'Adulte en développement'\n"
                                                                                ||"BETWEEN 35 AND 44 THEN 'Adulte d'âge moyen'\n"
                                                                                ||"BETWEEN 45 AND 54 THEN 'Adulte accompli'\n"
                                                                                ||"BETWEEN 55 AND 64 THEN 'Préretraité'\n"
                                                                                ||"BETWEEN 65 AND 74 THEN 'Sénior'"),
                nb_pmc_account                INT64 NOT NULL OPTIONS(description="Total of PMC account at the beginning of the month."),
                nb_pmc_account_new            INT64 NOT NULL OPTIONS(description="Total of new PMC account at observation month."),
                nb_pmc_account_active         INT64 NOT NULL OPTIONS(description="Total of active PMC account at the beginning of the month. Active = Login on the last 30 days at the beginning of the month."),
                nb_pmc_account_sub_paid_offer INT64 NOT NULL OPTIONS(description="Total of PMC account having paid subscription at the beginning of the month.")
          >
        > OPTIONS(description="PMC profiles indicators at socio-demographic level."),
        paid_offer_level ARRAY<
            STRUCT<
                paid_offer_brand_trigram      STRING         OPTIONS(description="Brand trigram paid offer."),
                nb_pmc_account                INT64          OPTIONS(description="Total of PMC account at the beginning of the month."),
                nb_pmc_account_new            INT64          OPTIONS(description="Total of new PMC account at observation month."),
                nb_pmc_account_active         INT64          OPTIONS(description="Total of active PMC account at the beginning of the month. Active = Login on the last 30 days at the beginning of the month.")
            >
        > OPTIONS(description="PMC profiles indicators at paid offer level.")
    > OPTIONS(description="Indicators at different level about PMC accounts."),
    PRIMARY KEY(observation_month) NOT ENFORCED
)
PARTITION BY observation_month
OPTIONS(description="This table contains a monthly indicators at different level about PMC account's.\n"
                  ||"All metrics are calculated by referring to the beginning of the observation month.\n"
                  ||"For paid offers, we focus only on 'CAP' and 'HBR' brands.\n\n"
                  ||"DAG: {{ dag.dag_id }} \n\n"
                  ||"Sync: Monthly"
);

DELETE FROM `{{ params.task_config['source_bq_project'][params.env]['matrix'] }}.share_marketing.pmc_profile_indicator_monthly`
WHERE observation_month BETWEEN start_date AND end_date;

INSERT INTO `{{ params.task_config['source_bq_project'][params.env]['matrix'] }}.share_marketing.pmc_profile_indicator_monthly`
WITH generate_date AS (
    -- Generate previous month
    SELECT
        DATE_TRUNC(day_month, MONTH) AS observation_month,
        DATE_TRUNC(day_month, MONTH) AS start_month,
        LAST_DAY(day_month) AS end_month
    FROM UNNEST(GENERATE_DATE_ARRAY(start_date, end_date, INTERVAL 1 MONTH)) AS day_month
),

get_pmc_login AS (
    -- Get PMC Login data
    SELECT
        pmc_uuid,
        log.brand_trigram,
        DATE_TRUNC(log.date, MONTH) AS login_date
    FROM `{{ params.task_config['source_bq_project'][params.env]['userhub'] }}.generated_data.pmc_profile_journey` AS pj, UNNEST(login) AS log
    WHERE log.brand_trigram != "GAL"
    GROUP BY ALL
),

get_paid_offer AS (
    -- Get PMC paid offer data
    SELECT
        uuid AS pmc_uuid,
        brand_trigram,
        DATE(enter_date)  AS sub_date,
        DATE(exit_date)   AS unsub_date
    FROM `{{ params.task_config['source_bq_project'][params.env]['userhub'] }}.refined_data.pmc_profile_paid_offer`
    WHERE brand_trigram != "GAL"
    GROUP BY ALL
),

flag_pmc_account AS (
    -- Flag PMC account before computing indicators
    SELECT
        observation_month,
        p360.id.pmc_uuid,
        DATE(pp.create_date) AS create_date,
        IFNULL(UPPER(p360.service.pmc.source_brand_trigram), "(not set)") AS signup_brand_trigram,
        IFNULL(UPPER(p360.info.gender), "(not set)") AS gender,
        CASE
            WHEN DATE_DIFF(start_month, PARSE_DATE("%Y-%m-%d", p360.info.birthdate), DAY) BETWEEN 18 AND 24 THEN "Jeune adulte"
            WHEN DATE_DIFF(start_month, PARSE_DATE("%Y-%m-%d", p360.info.birthdate), DAY) BETWEEN 25 AND 34 THEN "Adulte en développement"
            WHEN DATE_DIFF(start_month, PARSE_DATE("%Y-%m-%d", p360.info.birthdate), DAY) BETWEEN 35 AND 44 THEN "Adulte d'âge moyen"
            WHEN DATE_DIFF(start_month, PARSE_DATE("%Y-%m-%d", p360.info.birthdate), DAY) BETWEEN 45 AND 54 THEN "Adulte accompli"
            WHEN DATE_DIFF(start_month, PARSE_DATE("%Y-%m-%d", p360.info.birthdate), DAY) BETWEEN 55 AND 64 THEN "Préretraité"
            WHEN DATE_DIFF(start_month, PARSE_DATE("%Y-%m-%d", p360.info.birthdate), DAY) BETWEEN 65 AND 74 THEN "Sénior"
            WHEN DATE_DIFF(start_month, PARSE_DATE("%Y-%m-%d", p360.info.birthdate), DAY) >= 75 THEN "Personne âgée"
            ELSE "(not set)"
        END AS age_bin,
        (DATE(pp.create_date) < observation_month)  AS is_old_account,
        (DATE(pp.create_date) BETWEEN start_month AND end_month) AS is_new_account,
        -- login
        pl.brand_trigram AS login_brand_trigram,
        (pl.login_date BETWEEN start_month AND end_month) AS is_active,
        -- paid offer
        IFNULL(gpo.brand_trigram, "(not set)") AS paid_offer_brand_trigram,
        (gpo.sub_date < observation_month AND sub_date >= DATE(pp.create_date) AND (unsub_date IS NULL OR unsub_date > observation_month)) AS has_paid_offer,
        (sub_date >= DATE(pp.create_date) AND (gpo.sub_date BETWEEN start_month AND end_month) AND (unsub_date IS NULL OR unsub_date > end_month)) AS has_new_paid_offer
    FROM generate_date AS gd
    LEFT JOIN `{{ params.task_config['source_bq_project'][params.env]['matrix'] }}.refined_data.profile_pmc` AS pp ON DATE(pp.create_date) <= gd.observation_month
    LEFT JOIN `{{ params.task_config['source_bq_project'][params.env]['matrix'] }}.business_data.profile_digital_360` AS p360 ON pp.uuid = p360.id.pmc_uuid
    LEFT JOIN get_pmc_login AS pl
        ON pl.pmc_uuid = p360.id.pmc_uuid
        AND pl.login_date <= gd.observation_month
    LEFT JOIN get_paid_offer AS gpo
        ON gpo.pmc_uuid = p360.id.pmc_uuid
        AND gpo.sub_date <= gd.observation_month
    WHERE p360.service.pmc.source_brand_trigram != "GAL"
    GROUP BY ALL
),

compute_indicator_global_level AS (
    -- Compute indicator at global level monthly
    SELECT
        observation_month,
        COUNT(DISTINCT IF(is_old_account, pmc_uuid, NULL)) AS nb_pmc_account,
        COUNT(DISTINCT IF(is_new_account, pmc_uuid, NULL)) AS nb_pmc_account_new,
        COUNT(DISTINCT IF(is_active, pmc_uuid, NULL)) AS nb_pmc_account_active,
        COUNT(DISTINCT IF(has_paid_offer, pmc_uuid, NULL)) AS nb_pmc_account_sub_paid_offer,
        COUNT(DISTINCT IF(has_new_paid_offer, pmc_uuid, NULL)) AS nb_pmc_account_sub_paid_offer_new
    FROM flag_pmc_account
    GROUP BY ALL
),

compute_indicator_signup_brand_level AS (
    -- Compute indicator at signup brand level monthly
    SELECT
        observation_month,
        signup_brand_trigram,
        COUNT(DISTINCT IF(is_old_account, pmc_uuid, NULL)) AS nb_pmc_account,
        COUNT(DISTINCT IF(is_new_account, pmc_uuid, NULL)) AS nb_pmc_account_new,
        COUNT(DISTINCT IF(is_active, pmc_uuid, NULL)) AS nb_pmc_account_active,
        COUNT(DISTINCT IF(has_paid_offer, pmc_uuid, NULL)) AS nb_pmc_account_sub_paid_offer,
        COUNT(DISTINCT IF(has_new_paid_offer, pmc_uuid, NULL)) AS nb_pmc_account_sub_paid_offer_new
    FROM flag_pmc_account
    GROUP BY ALL
),

compute_indicator_socio_demographic_level AS(
    -- Compute indicator at socio-demographic level monthly
    SELECT
        observation_month,
        gender,
        age_bin,
        COUNT(DISTINCT IF(is_old_account, pmc_uuid, NULL)) AS nb_pmc_account,
        COUNT(DISTINCT IF(is_new_account, pmc_uuid, NULL)) AS nb_pmc_account_new,
        COUNT(DISTINCT IF(is_active, pmc_uuid, NULL)) AS nb_pmc_account_active,
        COUNT(DISTINCT IF(has_paid_offer, pmc_uuid, NULL)) AS nb_pmc_account_sub_paid_offer,
        COUNT(DISTINCT IF(has_new_paid_offer, pmc_uuid, NULL)) AS nb_pmc_account_sub_paid_offer_new
    FROM flag_pmc_account
    GROUP BY ALL
),

compute_indicator_paid_offer_level AS (
    -- Compute indicator at paid offer level monthly
    SELECT
        observation_month,
        paid_offer_brand_trigram,
        COUNT(DISTINCT IF(has_paid_offer, pmc_uuid, NULL)) AS nb_pmc_account,
        COUNT(DISTINCT IF(has_new_paid_offer, pmc_uuid, NULL)) AS nb_pmc_account_new,
        COUNT(DISTINCT IF(has_paid_offer AND is_active, pmc_uuid, NULL)) AS nb_pmc_account_active
    FROM flag_pmc_account
    WHERE paid_offer_brand_trigram != "(not set)"
    GROUP BY ALL
)
SELECT 
    cigl.observation_month,
    STRUCT(
        `{{ params.task_config['source_bq_project'][params.env]['matrix'] }}.refined_data.dedup`(
            ARRAY_AGG(
                STRUCT(
                    cigl.nb_pmc_account,
                    cigl.nb_pmc_account_new,
                    cigl.nb_pmc_account_active,
                    cigl.nb_pmc_account_sub_paid_offer
                )
            )
        ) AS global_level,
        `{{ params.task_config['source_bq_project'][params.env]['matrix'] }}.refined_data.dedup`(
            ARRAY_AGG(
                STRUCT(
                    cisbl.signup_brand_trigram,
                    cisbl.nb_pmc_account,
                    cisbl.nb_pmc_account_new,
                    cisbl.nb_pmc_account_active,
                    cisbl.nb_pmc_account_sub_paid_offer
                ) ORDER BY cisbl.signup_brand_trigram DESC
            )
        ) AS signup_brand_level,
        `{{ params.task_config['source_bq_project'][params.env]['matrix'] }}.refined_data.dedup`(
            ARRAY_AGG(
                STRUCT(
                    cisdl.gender,
                    cisdl.age_bin,
                    cisdl.nb_pmc_account,
                    cisdl.nb_pmc_account_new,
                    cisdl.nb_pmc_account_active,
                    cisdl.nb_pmc_account_sub_paid_offer
                ) ORDER BY cisdl.age_bin DESC
            )
        ) AS socio_demographic_level,
        `{{ params.task_config['source_bq_project'][params.env]['matrix'] }}.refined_data.dedup`(
            ARRAY_AGG(
                STRUCT(
                    cipol.paid_offer_brand_trigram AS paid_offer_brand_trigram,
                    cipol.nb_pmc_account AS nb_pmc_account,
                    cipol.nb_pmc_account_new AS nb_pmc_account_new,
                    cipol.nb_pmc_account_active AS nb_pmc_account_active
                ) ORDER BY cipol.paid_offer_brand_trigram DESC
            )
        ) AS paid_offer_level
    ) AS indicator
FROM compute_indicator_global_level             AS cigl
JOIN compute_indicator_signup_brand_level       AS cisbl USING(observation_month)
JOIN compute_indicator_socio_demographic_level  AS cisdl USING(observation_month)
LEFT JOIN compute_indicator_paid_offer_level    AS cipol USING(observation_month)
GROUP BY ALL;
