-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
DROP TABLE IF EXISTS matrix__email_tmp.rogue_one_email_consent_{{ ds_nodash }};
CREATE TABLE matrix__email_tmp.rogue_one_email_consent_{{ ds_nodash }}
AS	
SELECT
	e.shoot_date AS rogue_one_shoot_date,
	e.id AS rogue_one_email_id,
	kec.id AS email_consent_id
FROM rogue_one.email AS e
JOIN rogue_one.base AS b ON e.base_id = b.id
LEFT JOIN karinto.email_consent AS kec ON kec.public_id = b.consent_ref
WHERE b.consent_ref > 0
  AND b.active = true
--  AND e.create_date > '{{ ds }}'
-- OU
  AND e.shoot_date > '{{ ds }}';
 ALTER TABLE matrix__email_tmp.rogue_one_email_consent_{{ ds_nodash }} OWNER TO matrix_email;