-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
DROP TABLE IF EXISTS `pm-prod-matrix.prepare.rogue_one_theme_{{ ds_nodash }}`;
CREATE TABLE `pm-prod-matrix.prepare.rogue_one_theme_{{ ds_nodash }}`
(
    rogue_one_shoot_date TIM<PERSON><PERSON>MP, 
    rogue_one_email_id   INT64, 
    theme                STRING,
    action               STRING
);
 
CREATE TABLE IF NOT EXISTS `pm-prod-matrix.store_matrix_email.rogue_one_theme`
(
    rogue_one_shoot_date TIMESTAMP, 
    rogue_one_email_id   INT64, 
    theme                STRING
);


INSERT INTO `pm-prod-matrix.prepare.rogue_one_theme_{{ ds_nodash }}`
SELECT *, 'insert'
FROM `pm-prod-matrix.import.rogue_one_theme_{{ ds_nodash }}`;


UPDATE `pm-prod-matrix.prepare.rogue_one_theme_{{ ds_nodash }}` AS dst
SET action = 'update'
FROM `pm-prod-matrix.prepare.rogue_one_theme_{{ ds_nodash }}` AS src
JOIN `pm-prod-matrix.store_matrix_email.rogue_one_theme` AS ref 
    ON src.rogue_one_email_id = ref.rogue_one_email_id 
WHERE src.rogue_one_email_id = dst.rogue_one_email_id
