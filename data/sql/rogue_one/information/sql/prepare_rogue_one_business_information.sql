-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.prepare.rogue_one_business_information_{{ ds_nodash }}`
(
    rogue_one_email_id  INT64,
    editor              STRING,
    plateform           STRING,
    advertiser          STRING
);

INSERT INTO `{{ params.bq_project }}.prepare.rogue_one_business_information_{{ ds_nodash }}`
(rogue_one_email_id, editor, plateform, advertiser)
(SELECT
        rogue_one_email_id,
        IF(editor="\\N", NULL, editor)           AS editor,
        IF(plateform="\\N", NULL, plateform)     AS plateform,
        IF(advertiser="\\N", NULL, advertiser)   AS advertiser
FROM `{{ params.bq_project }}.import.rogue_one_business_information_{{ ds_nodash }}`);
