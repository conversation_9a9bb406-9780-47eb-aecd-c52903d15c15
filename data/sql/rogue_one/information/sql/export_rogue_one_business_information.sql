-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- export daily rogue_one information : editor, platform and advertiser
DROP TABLE IF EXISTS matrix__email_tmp.rogue_one_business_information_{{ ds_nodash }};
CREATE TABLE matrix__email_tmp.rogue_one_business_information_{{ ds_nodash }} AS (
SELECT
    e.id AS rogue_one_email_id,
    ed.name AS "editor",
    ps.name AS  "plateform",
    ads.name AS "advertiser"
FROM rogue_one.email e
JOIN rogue_one.base b ON b.id = e.base_id
JOIN rogue_one.editor ed ON ed.id = b.editor_id
LEFT JOIN rogue_one.business_info bi ON bi.email_id = e.id
LEFT JOIN rogue_one.society ps ON ps.id = bi.platform_society_id
LEFT JOIN rogue_one.society ads ON ads.id = bi.advertiser_society_id
WHERE e.status = 'SENT'
{% if params.full_export.lower() != 'true' %}
    -- last sent email will be yesterday
    AND  e.shoot_date >= DATE('{{ ds }}') - INTERVAL '3 days'
{% endif %}
) ;
ALTER TABLE matrix__email_tmp.rogue_one_business_information_{{ ds_nodash }} OWNER TO matrix_email;