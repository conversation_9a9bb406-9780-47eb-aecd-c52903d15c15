-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
--
-- Purpose : Create a new mapping table tmp_rogue_one_utm_consent_mapping (utm_source <-> email_consent)

-- ------------------------------------------------------------------------------------------------
-- Get utm-source for ALL Production Newsletter in NL-Factory
--  and map each utm with its related used consent through rouge-one base
-- ------------------------------------------------------------------------------------------------

--  table=rogue_one.export_rogue_one_utm_consent
DROP TABLE IF EXISTS {{ params.table }};
CREATE TABLE IF NOT EXISTS {{ params.table }} (
    utm_source character varying(255),
    public_ref character varying(255),
    consent_id integer
);
ALTER TABLE {{ params.table }} OWNER TO rogue_one;

GRANT SELECT
   ON TABLE {{ params.table }}
   TO all_read_only;

INSERT INTO {{ params.table }}
WITH nls AS (
    SELECT nl_ref, config->'track_link'->>'utm_source' AS utm_source
    FROM nl_factory.newsletter AS nl
    JOIN nl_factory.public_url AS pu ON pu.newsletter_id = nl.id
    WHERE status = 'PRODUCTION'
), rogue_one_base AS (
    SELECT
        b.router_url,
        REGEXP_REPLACE(content,'\$#\$_url="https:\/\/nl-factory(.*)\.prismadata.fr\/get-nl\/(.*)"_\$#\$','\2') AS nlf_public_url
    FROM rogue_one.base AS b
    JOIN rogue_one.template AS t ON b.default_template_id = t.id
    WHERE b.active = true
), base_with_nlf_template AS (
    SELECT utm_source,
        router_url AS public_ref
    FROM nls AS nl
    JOIN rogue_one_base AS b ON b.nlf_public_url = nl.nl_ref
)
SELECT b.*, coalesce(ec.id, t.email_consent_id, 0) AS consent_id
FROM base_with_nlf_template AS b
LEFT JOIN karinto.email_consent AS ec ON ec.public_ref = b.public_ref AND ec.active = true
LEFT JOIN karinto.thematic AS t ON t.public_ref = b.public_ref
;