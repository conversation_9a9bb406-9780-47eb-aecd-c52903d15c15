-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
-- open
DELETE FROM `pm-prod-matrix.store_partner.splio_event_open` AS sseo
WHERE EXISTS (SELECT 1
              FROM `pm-prod-matrix.prepare.partner_splio_event_open` AS speo
              WHERE sseo.splio_universe_event_ref = speo.id_event
                AND sseo.universe_name = speo.universe_name)
;

INSERT INTO `pm-prod-matrix.store_partner.splio_event_open`
SELECT * EXCEPT (email) FROM `pm-prod-matrix.prepare.partner_splio_event_open`
;

-- clicks
DELETE FROM `pm-prod-matrix.store_partner.splio_event_clicks` AS ssec
WHERE EXISTS (SELECT 1
              FROM `pm-prod-matrix.prepare.partner_splio_event_clicks` AS spec
              WHERE ssec.splio_universe_event_ref = spec.id_event
                AND ssec.universe_name = spec.universe_name)
;

INSERT INTO `pm-prod-matrix.store_partner.splio_event_clicks`
SELECT * EXCEPT (email) FROM `pm-prod-matrix.prepare.partner_splio_event_clicks`
;

-- sent
DELETE FROM `pm-prod-matrix.store_partner.splio_event_sent` AS sses
WHERE EXISTS (SELECT 1
              FROM `pm-prod-matrix.prepare.partner_splio_event_sent` AS spes
              WHERE sses.splio_universe_event_ref = spes.id_event
                AND sses.universe_name = spes.universe_name)
;

INSERT INTO `pm-prod-matrix.store_partner.splio_event_sent`
SELECT * EXCEPT (email) FROM `pm-prod-matrix.prepare.partner_splio_event_sent`
;