-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DROP TABLE IF EXISTS `{{ params.bq_project }}.generated_data.workflow_transition`;
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.generated_data.workflow_transition`
    (

        email_profile_master_id     INTEGER         NOT NULL    OPTIONS(description="email profile matser id. ref: {{ params.bq_project }}.store_matrix_email.profile_master_id.id"),
        workflow_transitions        ARRAY<STRING>               OPTIONS(description="[<workflow_name>-<transition>] \n"
                                                                                 || "workflow_name: 'universe-voici', 'universe-capital', 'consent-voici_quotidienne_nl' \n"
                                                                                 || "transition: state_from-to-state_to as new-to-zombie, ...")

    )
OPTIONS(description="Contains merged workflow transitions by profile at execution date \n"||
                    "DAG : {{ dag.dag_id }} \n"||
                    "SYNC: daily");

INSERT `{{ params.bq_project }}.generated_data.workflow_transition`
-- NEST prepared column by profile
SELECT
    email_profile_master_id,
    ARRAY_AGG(CONCAT(REGEXP_REPLACE(_TABLE_SUFFIX, "^([^_]+)_", r"\1-"), "-", transition)) AS workflow_transition
FROM `{{ params.bq_project }}.store_email_workflow.workflow-transition_*`
GROUP BY 1;
