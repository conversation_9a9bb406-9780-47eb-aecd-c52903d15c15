-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

{% for workflow_config in params.workflows_configs %}
    -- Previous
    DROP TABLE IF EXISTS `{{ params.bq_project }}.store_email_workflow.workflow-previous_{{ workflow_config['granularity'] }}_{{ workflow_config['name_suffix'].replace('|', '_') }}`;
    ALTER TABLE `store_email_workflow.workflow_{{ workflow_config['granularity'] }}_{{ workflow_config['name_suffix'].replace('|', '_') }}` RENAME TO `workflow-previous_{{ workflow_config['granularity'] }}_{{ workflow_config['name_suffix'].replace('|', '_') }}`;
    ALTER TABLE `store_email_workflow.workflow-previous_{{ workflow_config['granularity'] }}_{{ workflow_config['name_suffix'].replace('|', '_') }}`
    SET OPTIONS(description="Contains previous state by profile for {{ workflow_config['name_suffix'] }} {{ workflow_config['granularity'] }}. \n"||
                             "Fore more details in workflow (Workflow - version 3 sheet): https://drive.google.com/file/d/1slT1oPvGchZIoNksY9Ot6Ht890XdizgN/view?usp=sharing \n"||
                             "DAG : {{ dag.dag_id }} \n"||
                             "SYNC: daily");
    -- Current
    CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_email_workflow.workflow_{{ workflow_config['granularity'] }}_{{ workflow_config['name_suffix'].replace('|', '_') }}`
    (
        email_profile_master_id     INTEGER NOT NULL    OPTIONS(description="email profile matser id. ref: {{ params.bq_project }}.store_matrix_email.profile_master_id.id"),
        state                       STRING  NOT NULL    OPTIONS(description="state definition - 'State sheet' : https://docs.google.com/spreadsheets/d/1pe4FaVgNxt4W981B_-Gi2XFHkQNj9Yb5agGCoRq8aAw/edit?usp=sharing")
    )
    OPTIONS(description="Contains state by profile for {{ workflow_config['name_suffix'] }} {{ workflow_config['granularity'] }}  at Execution Date. \n"||
                        "Fore more details in workflow (Workflow - version 3 sheet): https://drive.google.com/file/d/1slT1oPvGchZIoNksY9Ot6Ht890XdizgN/view?usp=sharing \n"||
                        "DAG : {{ dag.dag_id }} \n"||
                        "SYNC: daily");


    INSERT INTO `{{ params.bq_project }}.store_email_workflow.workflow_{{ workflow_config['granularity'] }}_{{ workflow_config['name_suffix'].replace('|', '_') }}`
    SELECT
        email_profile_master_id,
        state
    FROM `{{ params.bq_project }}.workspace.crm_state_count_by_{{ workflow_config['granularity'] }}`, UNNEST(states) AS state
    WHERE LOWER(target) =  "{{ workflow_config['name_suffix'] }}";
{% endfor %}


