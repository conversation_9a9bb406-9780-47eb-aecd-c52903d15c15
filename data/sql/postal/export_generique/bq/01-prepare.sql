-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
-- DROPS
DROP TABLE IF EXISTS `{{ params.bq_project }}.temp.prepare_conexance_transaction_{{ next_ds_nodash }}`;
DROP TABLE IF EXISTS `{{ params.bq_project }}.temp.conexance_transaction_{{ next_ds_nodash }}`;

-- CREATES
CREATE TABLE `{{ params.bq_project }}.temp.prepare_conexance_transaction_{{ next_ds_nodash }}`
(
    customer_ref   STRING,
    magazine_title STRING,
    start_date     STRING,
    end_date       STRING,
    channel        STRING,
    nb_served      INT64,
    amount         STRING,
    payment_method STRING
);
CREATE TABLE `{{ params.bq_project }}.temp.conexance_transaction_{{ next_ds_nodash }}`
(
    ID_CLIENT           STRING,
    DATE_DEBUT_ACTIVITE STRING,
    DATE_FIN_ACTIVITE   STRING,
    CANAL_TRANSACTION   STRING,
    NB_NUM_SERVIS       INT64,
    MONTANT             STRING,
    MOYEN_PAIEMENT      STRING,
    TITRE               STRING
);

-- INSERTS
INSERT INTO `{{ params.bq_project }}.temp.prepare_conexance_transaction_{{ next_ds_nodash }}`
SELECT DISTINCT s.customer_ref                               AS customer_ref,
       s.magazine_title                                      AS magazine_title,
       FIRST_VALUE(CAST(s.start_date AS STRING))      OVER w AS start_date,
       FIRST_VALUE(CAST(s.end_date AS STRING))        OVER w AS end_date,
       FIRST_VALUE(a.CANAL_SOUSCRIPTION)              OVER w AS channel,
       FIRST_VALUE(a.NB_NUMEROS_ABO)                  OVER w AS nb_served,
       FIRST_VALUE(CAST(a.MONTANT_HT_PAYE AS STRING)) OVER w AS amount,
       FIRST_VALUE(a.MODE_PAIEMENT)                   OVER w AS payment_method
  FROM `{{ params.bq_project }}.store_matrix_postal.magazine_subscription` AS s   -- `pm-prod-matrix.store_matrix_postal.magazine_subscription`
  JOIN `{{ params.bi_project }}.export_it_data.abonnement`                 AS a   -- `pm-prod-business-abonnement.export_it_data.abonnement`
    ON a.ID_CLIENT = s.customer_ref
 WHERE s.is_active = true
WINDOW w AS (PARTITION BY s.customer_ref, s.magazine_title ORDER BY s.start_date DESC, end_date DESC);

INSERT INTO `{{ params.bq_project }}.temp.conexance_transaction_{{ next_ds_nodash }}`
SELECT customer_ref         AS ID_CLIENT,
       LEFT(start_date, 10) AS DATE_DEBUT_ACTIVITE,
       LEFT(end_date, 10)   AS DATE_FIN_ACTIVITE,
       channel              AS CANAL_TRANSACTION,
       nb_served            AS NB_NUM_SERVIS,
       amount               AS MONTANT,
       payment_method       AS MOYEN_PAIEMENT,
       magazine_title       AS TITRE
  FROM `{{ params.bq_project }}.temp.prepare_conexance_transaction_{{ next_ds_nodash }}`;
