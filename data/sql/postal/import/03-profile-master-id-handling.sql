-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
-- The purpose of this sql script is :
-- * insert customer_refs on profile_master_id
-- * update import tables with profile_master_id created
--
-- Insert in matrix__postal profile_master_id
INSERT INTO matrix__postal.profile_master_id (customer_ref)
SELECT customer_ref
  FROM matrix__postal_import.profile;

-- Update import_profile table
UPDATE matrix__postal_import.profile
   SET _profile_master_id = matrix__postal.profile_master_id.id
  FROM matrix__postal.profile_master_id
 WHERE matrix__postal_import.profile.customer_ref = matrix__postal.profile_master_id.customer_ref;

-- Update import_profile_enrichment table
UPDATE matrix__postal_import.profile_enrichment
   SET _profile_master_id = matrix__postal.profile_master_id.id
  FROM matrix__postal.profile_master_id
 WHERE matrix__postal_import.profile_enrichment.customer_ref = matrix__postal.profile_master_id.customer_ref;

-- Update import_address table
UPDATE matrix__postal_import.address
   SET _profile_master_id = matrix__postal.profile_master_id.id
  FROM matrix__postal.profile_master_id
 WHERE matrix__postal_import.address.customer_ref = matrix__postal.profile_master_id.customer_ref;

-- Update import_subscription table
UPDATE matrix__postal_import.subscription
   SET _profile_master_id = matrix__postal.profile_master_id.id
  FROM matrix__postal.profile_master_id
 WHERE matrix__postal_import.subscription.customer_ref = matrix__postal.profile_master_id.customer_ref;

-- Cleaning inexisiting magazines subscriptions
DELETE FROM matrix__postal_import.subscription
 WHERE magazine_ref NOT IN (
       SELECT ref
         FROM matrix__postal.magazine
);

DELETE FROM matrix__postal.magazine
 WHERE ref NOT IN (
       SELECT DISTINCT magazine_ref
         FROM matrix__postal_import.subscription
);

DELETE FROM matrix__postal_import.profile
 WHERE _profile_master_id IS NULL;

DELETE FROM matrix__postal_import.profile_enrichment
 WHERE _profile_master_id IS NULL;

DELETE FROM matrix__postal_import.address
 WHERE _profile_master_id IS NULL;

DELETE FROM matrix__postal_import.subscription
 WHERE _profile_master_id IS NULL;
