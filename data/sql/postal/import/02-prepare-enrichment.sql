-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
SELECT customer_ref,
       IF(study_level         IS NULL, '\\N', CAST(study_level         AS STRING)) AS study_level,
       IF(town_size           IS NULL, '\\N', CAST(town_size           AS STRING)) AS town_size,
       IF(marital_status      IS NULL, '\\N', CAST(marital_status      AS STRING)) AS marital_status,
       IF(income              IS NULL, '\\N', CAST(income              AS STRING)) AS income,
       IF(social_pro_category IS NULL, '\\N', CAST(social_pro_category AS STRING)) AS social_pro_category,
       IF(home_status         IS NULL, '\\N', CAST(home_status         AS STRING)) AS home_status,
       IF(children            IS NULL, '\\N', CAST(children            AS STRING)) AS children,
       IF(home_owner          IS NULL, '\\N', CAST(home_owner          AS STRING)) AS home_owner
  FROM `{{ params.bq_project }}.temp.postal_prepare_enrichment_{{ next_ds_nodash }}`;
