-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- fetch pmi email
UPDATE matrix__email_stockpile_import.reactivity_global AS import
  SET profile_master_id =  pmi.id
FROM matrix__email.profile_master_id AS pmi
WHERE import.email_hash = pmi.email_sha256 OR import.email_hash = pmi.email_md5;

-- update to_insert field
UPDATE matrix__email_stockpile_import.reactivity_global AS import
SET to_insert = false
FROM matrix__email_stockpile.reactivity_global AS ref
WHERE import.profile_master_id = ref.profile_master_id;

