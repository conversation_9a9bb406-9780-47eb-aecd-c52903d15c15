-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

SELECT
    ref.email_profile_master_id,
    MAX(ref.last_open_date)     AS last_open_date,
    MAX(ref.last_click_date)    AS last_click_date
FROM `generated_data.last_activity_global` AS ref
JOIN `store_matrix_email.profile_master_id`     AS pmi
    ON pmi.id = ref.email_profile_master_id
        AND pmi.email_md5 = '{{params.email_hash}}'
WHERE
    GREATEST(
        IFNULL(ref.last_open_date, TIMESTAMP('1970-01-01')),
        IFNULL(ref.last_click_date, TIMESTAMP('1970-01-01'))
    ) > CURRENT_TIMESTAMP() - INTERVAL {{params.interval}}
GROUP BY 1
;
