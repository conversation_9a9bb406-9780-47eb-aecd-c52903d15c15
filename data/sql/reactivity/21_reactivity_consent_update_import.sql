-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
-- enrich table with pmi
UPDATE matrix__email_stockpile_import.reactivity AS import
  SET profile_master_id =  pmi.id
FROM matrix__email.profile_master_id AS pmi
WHERE import.email_hash = pmi.email_sha256 OR import.email_hash = pmi.email_md5;


-- retrive profile to be updated only  

UPDATE matrix__email_stockpile_import.reactivity AS import
SET to_insert = false
FROM matrix__email_stockpile.reactivity AS ref
JOIN karinto.email_consent AS ec ON ec.id = ref.email_consent_id
WHERE import.profile_master_id = ref.profile_master_id
AND import.public_id = ec.public_id
;