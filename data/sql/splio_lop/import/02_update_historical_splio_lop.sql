-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

MER<PERSON> `store_partner.historical_splio_lop` AS hist
USING (
    SELECT
    profile_master_id,  email,  univers_name,  univers_id,  lop_type,  date
    FROM `store_partner.splio_lop_snapshot`
    -- WHERE date > CURRENT_DATE() - INTERVAL 2 DAY
    ) AS snap
ON  hist.profile_master_id = snap.profile_master_id
    AND hist.univers_id = snap.univers_id
    AND hist.lop_type = snap.lop_type
    AND hist.date = snap.date
WHEN NOT MATCHED THEN
    INSERT (profile_master_id,  email,  univers_name,  univers_id,  lop_type,  date)
    VALUES (snap.profile_master_id,  snap.email,  snap.univers_name,  snap.univers_id,  snap.lop_type,  snap.date)
;


