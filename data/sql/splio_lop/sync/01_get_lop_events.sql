-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
DROP TABLE IF EXISTS matrix__email_tmp.splio_lop_sync;

CREATE TABLE matrix__email_tmp.splio_lop_sync
AS WITH events AS (
    --- get ev from mail queue ( blacklist / unblacklist )
    SELECT
        --- extract brand id from payload
        replace(
            json_array_elements(payload -> 'brands_ids') :: varchar(150),
            '"',
            ''
        ) :: integer as brand_id,
        type as type,
        email as email,
        to_char(create_date, 'YYYY-MM-DD') AS create_date
    FROM
        matrix__email_queue.email_event as ev
    WHERE
        type IN ('blacklist', 'unblacklist')
        AND CAST(ev.payload ->> 'ev' AS integer) >= 99
        AND ev.create_date > NOW() - INTERVAL '{{ params.interval }}'
    ORDER BY
        id DESC
),
univers AS (
    --- get univers by brand from matrix
    SELECT
        spl.universe_id AS universe_id,
        kec.brand_id AS brand_id,
        univ.name AS universe_name,
        b.name AS brand_name
    FROM
        matrix__email_splio.splio_list AS spl
        JOIN karinto.email_consent AS kec ON kec.id = spl.email_consent_id
        JOIN matrix__email_splio.universe AS univ ON univ.id = spl.universe_id
        JOIN karinto.brand AS b ON b.id = kec.brand_id
)
SELECT
    DISTINCT
--     CASE
--         WHEN ev.type = 'blacklist' THEN 'ADD'
--         WHEN ev.type = 'unblacklist' THEN 'DEL'
--     END AS splio_action,
    --- useful ?
    ev.type as event_type,
    ev.email,
    ev.create_date AS date,
    uv.universe_name,
    'custom' AS source
FROM
    events AS ev
    JOIN univers AS uv ON ev.brand_id = uv.brand_id
WHERE
    uv.universe_name IS NOT NULL
ORDER BY
    email ASC;


ALTER TABLE matrix__email_tmp.splio_lop_sync OWNER TO matrix_email;