<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Monitoring</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>

Bonjour,
<br />
Nous avons pu synchroniser / redresser {{ task_instance.xcom_pull(task_ids='monitoring_diff')['total'] }} profiles dans splio <br/><br/><br/>
<table cellpadding="10" cellspacing="10">
    <thead>
    <tr>
        <th>Univers</th>
        <th>LOP</th>
        <th>UNLOP</th>
    </tr>
    </thead>
    <tbody>
    {% for k, v in task_instance.xcom_pull(task_ids='monitoring_diff').items() %}
    {% if k != 'total' %}
    <tr>
        <td>{{ k }}</td>
        <td>{{ v['blacklist'] }}</td>
        <td>{{ v['unblacklist'] }}</td>
    </tr>
    {% endif %}
    {% endfor %}
    </tbody>

</table>

<br /><br />
Bien cordialement,<br />
Mozart
</body>
</html>