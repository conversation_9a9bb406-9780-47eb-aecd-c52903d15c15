-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
----
---- with this query we need to prepare a schema with this structure
---- REQUIRED :  event_type, email, date, universe_name, source
----

SELECT
       DISTINCT
       IF(ld.is_prisma_lop = true, "blacklist", "unblacklist") AS event_type,
       pmg.email                                               AS email,
       ld.blacklist_date                                       AS date,
       ku.name                                                 AS universe_name,
       'custom'                                                AS source,
        ---ld.is_prisma_lop,
        ---ld.is_splio_lop
FROM `{{params.bq_project}}.import.splio_lop_monitoring_{{ yesterday_ds_nodash }}` AS ld
JOIN `{{params.bq_project}}.store_matrix_email.profile_master_id` AS pmg ON ld.profile_master_id = pmg.id
JOIN `{{params.bq_project}}.store_karinto.universe` AS ku on ld.universe_id = ku.id


