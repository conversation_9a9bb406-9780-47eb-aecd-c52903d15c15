-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}


CREATE TABLE IF NOT EXISTS `prepare.partner_snapshot_{{ ds_nodash }}` (
		ref_splio				STRING,			
		profile_master_id		INTEGER,			
		universe_id				INTEGER,			
		universe				STRING,			
		email					STRING,			
		blacklist_status		STRING,			
		blacklist_date			STRING,			
		blacklist_source		STRING,			
		raw_subscriptions		STRING,			
		subscription_l0			STRING,			
		subscription_l1			STRING,			
		subscription_l2			STRING,			
		subscription_l3			STRING,			
		subscription_l4			STRING,			
		subscription_l5			STRING,			
		subscription_l6			STRING,			
		subscription_l7			STRING,			
		subscription_l8			STRING,			
		subscription_l9			STRING,			
		subscription_l10		STRING,			
		subscription_l11		STRING,			
		subscription_l12		STRING,			
		subscription_l13		STRING,			
		subscription_l14		STRING,			
		subscription_l15		STRING,			
		subscription_l16		STRING,			
		subscription_l17		STRING,			
		subscription_l18		STRING,			
		subscription_l19		STRING,			
		subscription_l20		STRING,			
		subscription_l21		STRING,			
		subscription_l22		STRING,			
		subscription_l23		STRING,			
		subscription_l24		STRING,			
		subscription_l25		STRING,			
		subscription_l26		STRING,			
		subscription_l27		STRING,			
		subscription_l28		STRING,			
		subscription_l29		STRING,			
		subscription_l30		STRING,			
		subscription_l31		STRING,			
		subscription_l32		STRING,			
		subscription_l33		STRING,			
		subscription_l34		STRING,			
		subscription_l35		STRING,			
		subscription_l36		STRING,			
		subscription_l37		STRING,			
		subscription_l38		STRING,			
		subscription_l39		STRING,			
		subscription_l40		STRING,	
		subscription_l41		STRING,			
		subscription_l42		STRING,			
		subscription_l43		STRING,			
		subscription_l44		STRING,			
		subscription_l45		STRING,			
		subscription_l46		STRING,			
		subscription_l47		STRING,			
		subscription_l48		STRING,			
		subscription_l49		STRING,			
		subscription_l50		STRING,			
		subscription_l51		STRING,			
		subscription_l52		STRING,			
		subscription_l53		STRING,			
		subscription_l54		STRING,			
		subscription_l55		STRING,			
		subscription_l56		STRING,			
		subscription_l57		STRING,			
		subscription_l58		STRING,			
		subscription_l59		STRING,			
		subscription_l60		STRING,			
		subscription_l61		STRING,			
		subscription_l62		STRING,			
		subscription_l63		STRING,
		crm_tag 				STRING
	)
	OPTIONS(
	  expiration_timestamp=TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL 5 DAY),
	  description="Table used to prepare data from splio import.\n"
	              || "\n\n"
	              || "Daily updates thru the Airflow DAG {{ dag.dag_id }}. Content dated " --|| current_run_timestamp || " CET."
	);

-- be sure it's empty
TRUNCATE TABLE `prepare.partner_snapshot_{{ ds_nodash }}`;

INSERT INTO `prepare.partner_snapshot_{{ ds_nodash }}`

SELECT
	ss.id as ref_splio,
	pmi.id as profile_master_id,
	ku.id as universe_id,
	ss.* EXCEPT(id)
FROM `import.partner_snapshot_{{ ds_nodash }}` AS ss
LEFT JOIN `store_matrix_email.profile_master_id` AS pmi on ss.email = pmi.email
LEFT JOIN `store_karinto.universe` AS ku ON ss.universe =  ku.name
;

