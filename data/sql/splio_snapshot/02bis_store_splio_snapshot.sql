-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
ALTER TABLE IF EXISTS `{{params.store_splio}}` SET OPTIONS (description='' ||
    '  THE SNAPSHOT OF SPLIO DATED TO {{ yesterday_ds_nodash }} \n\n columns : \n\n' ||
    ' \n -ref_splio:  id of the contact on splio' ||
    ' \n -profile_master_id: the id of the profile on prisma ' ||
    ' \n -univers_id:  id of the univers' ||
    ' \n -univers_name: name of the splio univers  ' ||
    ' \n -blacklist_status: the LOP status on splio side' ||
    ' \n -blacklist_source: the LOP type on splio side' ||
    ' \n -date: date of lop on splio side' ||
    ' \n -raw_subscriptions: raw_subscriptions on splio side' ||
    ' \n -subscription_*: subscription_(*) sub on univers ( 1|0 ) on splio side' ||
    ' \n\n generated by this DAG https://j5fc9f889c8d8fba3p-tp.appspot.com/admin/airflow/tree?dag_id=splio_snapshot_import ' ||
    '');
