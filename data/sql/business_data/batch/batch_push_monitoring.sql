-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- declare time to select for incremental case
-- NB : if campaign_name is null it means the event is related to a campaign_token that dates more than 120 days.

DECLARE start_date DATE DEFAULT DATE(DATE("{{ next_ds }}") - INTERVAL {{ params.interval }});
DECLARE end_date DATE DEFAULT DATE("{{ next_ds }}");

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.business_data.batch_push_monitoring`
(
  event_date              TIMESTAMP NOT NULL  OPTIONS(description="push datetime at hour scale"),
  campaign_name           STRING              OPTIONS(description="name of the campaign"),
  support                 STRING              OPTIONS(description="enum = ['web', 'android', 'ios']"),
  brand_trigram           STRING              OPTIONS(description="brand trigram"),
  source                  STRING              OPTIONS(description="campaign source as 'edito', 'marketing', ...extracted from campaign deep link"),
  tag_type                STRING              OPTIONS(description="article tag type as 'person', 'politic', ..."),
  tag_title               STRING              OPTIONS(description="article tag title"),
  category                STRING              OPTIONS(description="article category"),
  article_format          STRING              OPTIONS(description="aricle format"),
  article_title           STRING              OPTIONS(description="aricle title"),
  global_kpi              STRUCT<
    push_sent_volume        INTEGER             OPTIONS(description="push sent volume"),
    push_open_volume        INTEGER             OPTIONS(description="opened push volume"),
    push_sent_token_volume  INTEGER             OPTIONS(description="opened sent token volume"),
    push_open_token_volume  INTEGER             OPTIONS(description="opened push token volume")
  >                                           OPTIONS(description="push sent and opened count by support, brand"),
  article_kpi               STRUCT<
    push_sent_volume        INTEGER             OPTIONS(description="push sent volume"),
    push_open_volume        INTEGER             OPTIONS(description="opened push volume"),
    push_sent_token_volume  INTEGER             OPTIONS(description="opened sent token volume"),
    push_open_token_volume  INTEGER             OPTIONS(description="opened push token volume")
  >                                           OPTIONS(description="push sent and opened count by article tags and categories")
)
--PARTITION BY TIMESTAMP_TRUNC(event_date, HOUR)
PARTITION BY TIMESTAMP_TRUNC(event_date, DAY)
CLUSTER BY support, brand_trigram
OPTIONS(description= "List batch push sent and opened count by: support, brand, article format, article_title, campaign source, pushed articles tag and category."
                    || "\n\n"
                    || "To get theses kpis, we are based on batch (events, campaigns) and articles."
                    || "\n\n"
                    || "DAG: {{ dag.dag_id }}."
                    || "\n\n"
                    || "Sync: Daily");

{% if params.is_full %}
SET start_date = DATE("{{ params.start_date }}");
SET end_date = DATE("{{ params.end_date }}");
{% endif %}

DELETE FROM `{{ params.bq_project }}.business_data.batch_push_monitoring`
WHERE  DATE(event_date) BETWEEN start_date AND end_date;

MERGE `{{ params.bq_project }}.business_data.batch_push_monitoring` AS target
USING (
  WITH prepared_campaign AS (
    -- get campaign & article informations
    SELECT
        support,
        brand_trigram,
        campaign_token,
        campaign_name,
        source,
        article.tag,
        ARRAY_REVERSE(
                SPLIT(
                        REGEXP_SUBSTR(
                                CONCAT(
                                        IF(article.category.first IS NULL, "*", article.category.first),
                                        IF(article.category.second IS NULL, "*", CONCAT("_", article.category.second)),
                                        IF(article.category.third IS NULL, "*", CONCAT("_", article.category.third)),
                                        IF(article.category.fourth IS NULL, "*", CONCAT("_", article.category.fourth)),
                                        IF(article.category.fifth IS NULL, "*", CONCAT("_", article.category.fifth))
                                ), '[^*]+'
                        ), "_"
                )
        )[SAFE_ORDINAL(1)] AS category,
        article.format     AS article_format,
        article.title      AS article_title
    FROM `{{ params.bq_project }}.refined_data.batch_campaign`
    WHERE DATE(create_date) BETWEEN DATE_SUB(start_date, INTERVAL 120 DAY) AND end_date),
    brand_support_count AS (
        -- count push sent & open by brand and support
        SELECT
            TIMESTAMP_TRUNC(create_date, HOUR)                                                 AS event_date,
            e.brand_trigram,
            e.support,
            COUNT(DISTINCT CASE WHEN event_type = "push_sent" THEN event_id ELSE NULL END)     AS push_sent_volume,
            COUNT(DISTINCT CASE WHEN event_type = "push_opened" THEN event_id ELSE NULL END)   AS push_open_volume,
            COUNT(DISTINCT CASE WHEN event_type = "push_sent" THEN install_id ELSE NULL END)   AS push_sent_token_volume,
            COUNT(DISTINCT CASE WHEN event_type = "push_opened" THEN install_id ELSE NULL END) AS push_open_token_volume
        FROM `{{ params.bq_project }}.refined_data.batch_event` AS e
        WHERE (DATE(create_date) BETWEEN start_date AND end_date)
            AND event_type IN ("push_sent", "push_opened")
        GROUP BY 1, 2, 3),
    tag_category_events AS (
        -- count push sent & open by source, tag and category
        SELECT
            TIMESTAMP_TRUNC(create_date, HOUR)                                                 AS event_date,
            e.brand_trigram,
            e.support,
            e.campaign_token,
            COUNT(DISTINCT CASE WHEN event_type = "push_sent" THEN event_id ELSE NULL END)     AS push_sent_volume,
            COUNT(DISTINCT CASE WHEN event_type = "push_opened" THEN event_id ELSE NULL END)   AS push_open_volume,
            COUNT(DISTINCT CASE WHEN event_type = "push_sent" THEN install_id  ELSE NULL END)  AS push_sent_token_volume,
            COUNT(DISTINCT CASE WHEN event_type = "push_opened" THEN install_id ELSE NULL END) AS push_open_token_volume
        FROM `{{ params.bq_project }}.refined_data.batch_event` AS e
        WHERE (DATE(create_date) BETWEEN start_date AND end_date)
            AND event_type IN ("push_sent", "push_opened")
        GROUP BY 1, 2, 3, 4),
    tag_category_count AS (
        -- count push sent & open by source, tag and category
        SELECT
            e.event_date,
            e.brand_trigram,
            e.support,
            c.campaign_name,
            c.source,
            c.article_format,
            c.article_title,
            c.tag.type  AS tag_type,
            c.tag.title AS tag_title,
            c.category,
            e.push_sent_volume,
            e.push_open_volume,
            e.push_sent_token_volume,
            e.push_open_token_volume
        FROM tag_category_events AS e
        LEFT JOIN prepared_campaign AS c ON
                c.brand_trigram = e.brand_trigram
                    AND c.support = e.support
                    AND c.campaign_token = e.campaign_token)
-- join all KPIs together into struct
SELECT
    bs.event_date,
    tc.campaign_name,
    bs.support,
    bs.brand_trigram,
    tc.source,
    tc.tag_type,
    tc.tag_title,
    tc.category,
    tc.article_format,
    tc.article_title,
    STRUCT(
      bs.push_sent_volume,
      bs.push_open_volume,
      bs.push_sent_token_volume,
      bs.push_open_token_volume
    ) AS global_kpi,
    STRUCT(
      tc.push_sent_volume,
      tc.push_open_volume,
      tc.push_sent_token_volume,
      tc.push_open_token_volume
    ) AS article_kpi
  FROM brand_support_count AS bs
  LEFT JOIN tag_category_count AS tc USING(event_date, support, brand_trigram)
) AS source
ON target.event_date = source.event_date
AND target.support = source.support
AND target.brand_trigram = source.brand_trigram
WHEN MATCHED THEN
  UPDATE SET
    campaign_name = source.campaign_name,
    source = source.source,
    tag_type = source.tag_type,
    tag_title = source.tag_title,
    category = source.category,
    article_format = source.article_format,
    article_title = source.article_title,
    global_kpi = source.global_kpi,
    article_kpi = source.article_kpi
WHEN NOT MATCHED THEN
  INSERT (
    event_date,
    campaign_name,
    support,
    brand_trigram,
    source,
    tag_type,
    tag_title,
    category,
    article_format,
    article_title,
    global_kpi,
    article_kpi
  )
  VALUES (
    source.event_date,
    source.campaign_name,
    source.support,
    source.brand_trigram,
    source.source,
    source.tag_type,
    source.tag_title,
    source.category,
    source.article_format,
    source.article_title,
    source.global_kpi,
    source.article_kpi
  );



