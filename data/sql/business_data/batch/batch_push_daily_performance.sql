-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- declare time to select for incremental case
DECLARE start_date DATE DEFAULT DATE(DATE("{{ next_ds }}") - INTERVAL {{ params.interval }});
DECLARE end_date DATE DEFAULT DATE("{{ next_ds }}");

CREATE TABLE IF NOT EXISTS `{{ params.bq_matrix }}.business_data.batch_push_daily_performance`
(
  event_date              DATE     NOT NULL   OPTIONS(description="Push datetime at hour scale"),
  brand_trigram           STRING   NOT NULL   OPTIONS(description="Brand trigram"),
  support                 STRING              OPTIONS(description="Enum representing the platform: ['web', 'android', 'ios']"),
  sessions                INTEGER             OPTIONS(description="Count of user sessions"),
  page_views              INTEGER             OPTIONS(description="Count of page views"),
  campaign_detail         ARRAY<STRUCT<
                             campaign_name           STRING  OPTIONS(description="Name of the campaign"),
                             source                  STRING  OPTIONS(description="Campaign source, e.g., 'edito', 'marketing', extracted from campaign deep link"),
                             push_sent_volume        INTEGER OPTIONS(description="Number of pushes sent for the campaign"),
                             push_open_volume        INTEGER OPTIONS(description="Number of pushes opened for the campaign"),
                             push_sent_token_volume  INTEGER OPTIONS(description="Number of unique tokens for which push notifications were sent for the campaign"),
                             push_open_token_volume  INTEGER OPTIONS(description="Number of unique tokens for which push notifications were opened for the campaign")
                           >>
                           OPTIONS(description="Details of campaigns including volume metrics for sent and opened push notifications")
)
PARTITION BY event_date
CLUSTER BY brand_trigram, support
OPTIONS(description= "List batch push sent and opened count by: support, brand, campaign source"
                    || "\n\n"
                    || "To get these KPIs, we are based on batch (events, campaigns)."
                    || "\n\n"
                    || "DAG: {{ dag.dag_id }}."
                    || "\n\n"
                    || "Sync: Daily");

{% if params.is_full %}
SET start_date = DATE("{{ params.start_date }}");
SET end_date = DATE("{{ params.end_date }}");
{% endif %}

DELETE FROM `{{ params.bq_matrix }}.business_data.batch_push_daily_performance`
WHERE DATE(event_date) BETWEEN start_date AND end_date;

MERGE `{{ params.bq_matrix }}.business_data.batch_push_daily_performance` AS target
USING (
      WITH monitoring_data AS (
      -- get batch_push_monitoring data
      SELECT
        DATE(event_date) AS event_date,
        campaign_name,
        IF(LOWER(support) = 'web', 'web', 'app') AS support,
        brand_trigram,
        source,
        sum(article_kpi.push_sent_volume) AS push_sent_volume,
        sum(article_kpi.push_open_volume) AS push_open_volume,
        sum(article_kpi.push_sent_token_volume) AS push_sent_token_volume,
        sum(article_kpi.push_open_token_volume) AS push_open_token_volume
      FROM `{{ params.bq_matrix }}.business_data.batch_push_monitoring`
      WHERE
        (DATE(event_date) BETWEEN start_date AND end_date)
      GROUP BY ALL
    ),
    ga_events_data AS (
      -- get ga4 events data from web and app
      SELECT
        DATE(visit_date) AS visit_date,
        'web' AS support,
        property_data.brand_trigram AS brand_trigram,
        COUNT(DISTINCT sessions.session_id) AS sessions,
        COUNTIF(events.event_name = "page_view") AS page_view
      FROM `{{ params.bq_ga4 }}.refined_data.ga_events_WEB_*` AS e
        LEFT JOIN UNNEST(e.session_data) AS sessions
        LEFT JOIN UNNEST(sessions.page_data) AS pages
        LEFT JOIN UNNEST(pages.event_info) AS events
      WHERE (DATE(visit_date) BETWEEN start_date AND end_date)
        AND _TABLE_SUFFIX NOT LIKE "%TEMPLATE%"
        AND _TABLE_SUFFIX NOT LIKE "%_v0%"
        AND user_data.tracking_info.custom_channel_group = 'Push Web'
      GROUP BY ALL

      UNION ALL

      SELECT
        DATE(visit_date) AS visit_date,
        'app' AS support,
        property_data.brand_trigram AS brand_trigram,
        COUNT(DISTINCT sessions.session_id) AS sessions,
        COUNTIF(events.event_name = "screen_view") AS page_view
      FROM `{{ params.bq_ga4 }}.refined_data.ga_events_APP_*` AS e
        LEFT JOIN UNNEST(e.session_data) AS sessions
        LEFT JOIN UNNEST(sessions.screen_data) AS screens
        LEFT JOIN UNNEST(screens.event_info) AS events
      WHERE (DATE(visit_date) BETWEEN start_date AND end_date)
        AND _TABLE_SUFFIX NOT LIKE "%TEMPLATE%"
        AND _TABLE_SUFFIX NOT LIKE "%_v0%"
        AND user_data.tracking_info.custom_channel_group = 'Push App'
      GROUP BY ALL
    )
  SELECT
    md.event_date,
    md.support,
    md.brand_trigram,
    ARRAY_AGG(STRUCT(
        md.campaign_name,
        md.source,
        md.push_sent_volume,
        md.push_open_volume,
        md.push_sent_token_volume,
        md.push_open_token_volume)) AS campaign_detail,
    sum(ga.sessions) AS sessions,
    sum(ga.page_view) AS page_views
  FROM monitoring_data AS md
  LEFT JOIN ga_events_data AS ga
    ON  md.support = ga.support
    AND md.brand_trigram = ga.brand_trigram
    AND md.event_date = ga.visit_date
  GROUP BY ALL
) AS source
ON target.event_date = source.event_date
AND target.support = source.support
AND target.brand_trigram = source.brand_trigram
WHEN MATCHED THEN
  UPDATE SET
    target.campaign_detail = source.campaign_detail,
    target.sessions = source.sessions,
    target.page_views = source.page_views
WHEN NOT MATCHED THEN
  INSERT (
    event_date,
    support,
    brand_trigram,
    campaign_detail,
    sessions,
    page_views
  )
  VALUES (
    source.event_date,
    source.support,
    source.brand_trigram,
    source.campaign_detail,
    source.sessions,
    source.page_views
  );
