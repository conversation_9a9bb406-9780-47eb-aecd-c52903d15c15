-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.business_data.prismashop_metrics_by_campaign`
(
    campaign_name   STRING      NOT NULL OPTIONS(description="Campaign name in lowercase."),
    send_date       TIMESTAMP   NOT NULL OPTIONS(description="Campaign shoot datetime."),
    nb_sent         INT64       NOT NULL OPTIONS(description="Number of sent email."),
    nb_delivered    INT64       NOT NULL OPTIONS(description="Number of delivered email."),
    nb_open         INT64       NOT NULL OPTIONS(description="Number of opened email."),
    nb_click        INT64       NOT NULL OPTIONS(description="Number of clicked email."),
    nb_unsub        INT64       NOT NULL OPTIONS(description="Number of unsubscription. \n\n"
                                                ||"This metric is extracted from: \n"
                                                ||"- IT-BI team for historical campaign.\n"
                                                ||"- Pref-center for new campaign."
                                                ),
    PRIMARY KEY (campaign_name, send_date) NOT ENFORCED
)
PARTITION BY DATE(send_date)
CLUSTER BY campaign_name
OPTIONS(description="This table contains Prismashop campaign metric's. \n"
                    "Metric source is IT-BI and Data-Inights (only for nb_unsub metric). \n\n"
                    ||"Sync: Daily \n"
                    ||"DAG: {{ dag.dag_id }}"
);

TRUNCATE TABLE `{{ params.bq_project }}.business_data.prismashop_metrics_by_campaign`;
INSERT INTO `{{ params.bq_project }}.business_data.prismashop_metrics_by_campaign`
WITH get_campaign_stats AS (
  -- Extract stats shared by IT-BI team
  SELECT
    TRIM(LOWER(NOM_CAMPAGNE))  AS campaign_name,
    DATE_ENVOI          AS send_date,
    NB_ENVOIS           AS nb_sent,
    NB_DELIVRES         AS nb_delivered,
    NB_OUVERTS          AS nb_open,
    NB_CLICS            AS nb_click,
    NB_UNSUB            AS nb_unsub
  FROM pm-prod-business-abonnement.export_it_data.campagnes_actito
  GROUP BY ALL
), compute_nb_unsub AS (
  -- Compute unsub's by campaign
  SELECT
    TRIM(SPLIT(LOWER(event_source.medium), "prismashop unsub -")[SAFE_ORDINAL(2)]) AS campaign_name,
    COUNT(DISTINCT email_profile_master_id) AS nb_unsub
  FROM `{{ params.bq_project }}.refined_data.email_event_sub_unsub_*`
  WHERE
    event_source.medium LIKE "prismashop unsub -%"
    AND event_type = "unsub"
  GROUP BY ALL
)
SELECT
  gcs.* EXCEPT(nb_unsub),
  IFNULL(COALESCE(gcs.nb_unsub, cnu.nb_unsub),  -1) AS nb_unsub
FROM get_campaign_stats AS gcs
LEFT  JOIN compute_nb_unsub AS cnu USING(campaign_name);
