-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.business_data.cross_brand_reactivity`
(
  first_brand_trigram             STRING  NOT NULL OPTIONS(description="First brand trigram"),
  second_brand_trigram            STRING  NOT NULL OPTIONS(description="Second brand trigram"),
  active_volume                   INTEGER NOT NULL OPTIONS(description="Profile has open at least one time on the last 90 days on the first brand."),
  cross_active_volume             INTEGER NOT NULL OPTIONS(description="Profile has open at least one time on the last 90 days on the first brand and the second one.")
)
OPTIONS(description="Active profile volume based on 'open reactivity' on the last 90 OR 'click reactivity' on the last 180 days by brand & cross-brand."
                  ||"\n \n"
                  ||"Sync: Daily \n"
                  ||"DAG: business_data__email"
);


TRUNCATE TABLE `{{ params.bq_project }}.business_data.cross_brand_reactivity`;

INSERT INTO `{{ params.bq_project }}.business_data.cross_brand_reactivity`
-- active account by couple of brand
WITH brand_couple AS(
  -- compute all possible couple (a_brand_trigram, b_brand_trigram)
  -- if list = [a,b], result = [(a,a), (a,b), (b,b)]
    SELECT DISTINCT
            a.brand_trigram                   AS a_brand_trigram,
            b.brand_trigram                   AS b_brand_trigram,
    FROM `{{ params.bq_project }}.refined_data.email_base` AS a
    CROSS JOIN `{{ params.bq_project }}.refined_data.email_base` AS b
    ORDER BY 1, 2
), active_profile AS (
  SELECT
    email_profile_master_id,
    ARRAY_AGG(DISTINCT brand_trigram) AS brand_list
  FROM `{{ params.bq_project }}.generated_data.last_activity_by_brand`
  WHERE
    -- active condition 👇🏻
    (
      DATE(last_open_date) >= DATE_SUB(CURRENT_DATE(), INTERVAL 90 DAY)
      OR
      DATE(last_click_date) >= DATE_SUB(CURRENT_DATE(), INTERVAL 180 DAY)
    )
  GROUP BY 1
)

SELECT DISTINCT
  cs.*,
  COUNT(DISTINCT ap.email_profile_master_id) OVER(PARTITION BY cs.a_brand_trigram)               AS active_volume,
  COUNT(DISTINCT ap.email_profile_master_id) OVER(PARTITION BY cs.a_brand_trigram, b_brand_trigram) AS cross_active_volume,
FROM brand_couple       AS cs
LEFT JOIN active_profile  AS ap ON cs.a_brand_trigram  IN UNNEST(brand_list)
                                AND cs.b_brand_trigram IN UNNEST(brand_list);
