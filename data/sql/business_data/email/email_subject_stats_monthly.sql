-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE task_start_date DEFAULT DATE_SUB(DATE("{{ next_ds }}"),INTERVAL {{ params.time_interval }});
DECLARE task_end_date DEFAULT DATE("{{ next_ds }}");

-- create statement for subjects/monthly
CREATE TABLE IF NOT EXISTS
    `{{ params.bq_project }}.business_data.email_subject_stats_monthly_by_subject`
(
    month_date        DATE   NOT NULL OPTIONS (description ="First day of the month"),
    brand_trigram     STRING OPTIONS (description ="Brand represented as a trigram"),
    newsletter        STRING NOT NULL OPTIONS (description ="Newsletter name"),
    subject           STRING NOT NULL OPTIONS (description ="Email subject"),
    nb_session        FLOAT64 OPTIONS (description ="Total number of sessions for the specified month AND subject"),
    nb_pv             FLOAT64 OPTIONS (description ="Total number of page views for the specified month AND subject"),
    nb_pv_per_session FLOAT64 OPTIONS (description ="Number of page views per session for the specified month AND subject"),
    PRIMARY KEY(month_date,brand_trigram,newsletter,subject) NOT ENFORCED
)
    PARTITION BY month_date
    CLUSTER BY brand_trigram, newsletter, subject
    OPTIONS ( description ="Aggregated data by month, brand, newsletter, AND subject from the daily by subject table.\n" ||
                           "Used for analyzing sessions AND page views.\n" ||
                           "DAG: {{ dag.dag_id }}" );
-- create statement for nls/monthly
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.business_data.email_subject_stats_monthly_by_nl`
(
    month_date          DATE   NOT NULL OPTIONS (description ="First day of the month"),
    brand_trigram       STRING OPTIONS (description ="Brand represented as a trigram"),
    newsletter          STRING NOT NULL OPTIONS (description ="Newsletter name"),
    nb_session          FLOAT64 OPTIONS (description ="Total number of sessions for the specified month AND nl"),
    nb_pv               FLOAT64 OPTIONS (description ="Total number of page views for the specified month AND nl"),
    nb_pv_per_session   FLOAT64 OPTIONS (description ="Number of page views per session for the specified month AND nl"),
    prv_nb_session      FLOAT64 OPTIONS (description ="Previous month number of sessions for the specified month AND nl"),
    prv_nb_pv           FLOAT64 OPTIONS (description ="Previous month number of page views for the specified month AND nl"),
    prv_pv_per_session  FLOAT64 OPTIONS (description ="Previous month number of page views per session for the specified month AND nl"),
    evol_nb_session     FLOAT64 OPTIONS (description ="Evolution of number of sessions compared to the previous month for the specified month AND nl (percentage)"),
    evol_nb_pv          FLOAT64 OPTIONS (description ="Evolution of number of page views compared to the previous month for the specified month AND nl (percentage)"),
    evol_pv_per_session FLOAT64 OPTIONS (description ="Evolution of number of page views per session compared to the previous month for the specified month AND nl (percentage)"),
    PRIMARY KEY (month_date, brand_trigram, newsletter) NOT ENFORCED
)
    PARTITION BY month_date
    CLUSTER BY brand_trigram, newsletter
    OPTIONS ( description ="Aggregated data by month, brand, AND newsletter from the daily by nl table.\n" ||
                           "Used for analyzing sessions AND page views.\n" ||
                           "DAG: {{ dag.dag_id }}" );

-- create statement for brand/monthly
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.business_data.email_subject_stats_monthly_by_brand`
(
    month_date          DATE NOT NULL OPTIONS (description ="First day of the month"),
    brand_trigram       STRING OPTIONS (description ="Brand represented as a trigram"),
    nb_session          FLOAT64 OPTIONS (description ="Total number of sessions for the specified month AND brand"),
    nb_pv               FLOAT64 OPTIONS (description ="Total number of page views for the specified month AND brand"),
    nb_pv_per_session   FLOAT64 OPTIONS (description ="Number of page views per session for the specified month AND brand"),
    prv_nb_session      FLOAT64 OPTIONS (description ="Previous month number of sessions for the specified month AND brand"),
    prv_nb_pv           FLOAT64 OPTIONS (description ="Previous month number of page views for the specified month AND brand"),
    prv_pv_per_session  FLOAT64 OPTIONS (description ="Previous month number of page views per session for the specified month AND brand"),
    evol_nb_session     FLOAT64 OPTIONS (description ="Evolution of number of sessions compared to the previous month for the specified month AND brand (percentage)"),
    evol_nb_pv          FLOAT64 OPTIONS (description ="Evolution of number of page views compared to the previous month for the specified month AND brand (percentage)"),
    evol_pv_per_session FLOAT64 OPTIONS (description ="Evolution of number of page views per session compared to the previous month for the specified month AND brand (percentage)"),
    PRIMARY KEY (month_date, brand_trigram) NOT ENFORCED
)
    PARTITION BY month_date
    CLUSTER BY brand_trigram
    OPTIONS ( description ="Aggregated data by month AND brand from the daily by brand table.\n" ||
                           "Used for analyzing sessions AND page views.\n" || "DAG: {{ dag.dag_id }}" );

{% if params.partial == true %}
    SET task_start_date = DATE("{{ params.start_date }}");
    SET task_end_date = DATE("{{ params.end_date }}");
    -- Delete rows before insert updated data

    DELETE FROM `{{ params.bq_project }}.business_data.email_subject_stats_monthly_by_brand`
    WHERE month_date BETWEEN task_start_date AND task_end_date;

    DELETE FROM `{{ params.bq_project }}.business_data.email_subject_stats_monthly_by_nl`
    WHERE month_date BETWEEN task_start_date AND task_end_date;

    DELETE FROM `{{ params.bq_project }}.business_data.email_subject_stats_monthly_by_subject`
    WHERE month_date BETWEEN task_start_date AND task_end_date;
{% endif %}
-- merge statement for subjects/monthly
MERGE INTO
    `{{ params.bq_project }}.business_data.email_subject_stats_monthly_by_subject` AS dest
    USING
        (
            -- perf per month, brand, newsletter, AND subject (used in merge 2)
            SELECT
                DATE_TRUNC(shoot_date,MONTH)                        AS month_date,
                brand_trigram,
                newsletter,
                subject,
                SUM(nb_session)                                     AS nb_session,
                SUM(nb_pv)                                          AS nb_pv,
                COALESCE(SAFE_DIVIDE(SUM(nb_pv),SUM(nb_session)),0) AS nb_pv_per_session
            FROM `{{ params.bq_project }}.business_data.email_subject_stats_daily_by_subject`
            GROUP BY 1, 2, 3, 4) AS ref
    ON dest.month_date = ref.month_date
            AND dest.brand_trigram = ref.brand_trigram
            AND dest.newsletter = ref.newsletter
            AND dest.subject = ref.subject
    WHEN MATCHED THEN UPDATE SET
        dest.nb_session = ref.nb_session,
        dest.nb_pv = ref.nb_pv,
        dest.nb_pv_per_session = ref.nb_pv_per_session
    WHEN NOT MATCHED
        THEN
        INSERT
            (month_date,
             brand_trigram,
             newsletter,
             subject,
             nb_session,
             nb_pv,
             nb_pv_per_session)
            VALUES
                (ref.month_date,
                 ref.brand_trigram,
                 ref.newsletter,
                 ref.subject,
                 ref.nb_session,
                 ref.nb_pv,
                 ref.nb_pv_per_session);
-- merge statement for nls/monthly
MERGE INTO `{{ params.bq_project }}.business_data.email_subject_stats_monthly_by_nl` AS dest
    USING (
        WITH get_monthly_nl_perf    AS (
            -- monthly perf per brand and nl
            SELECT
                DATE_TRUNC(shoot_date,MONTH) as shoot_date,
                brand_trigram,
                newsletter,
                SUM(nb_session) AS nb_session,
                SUM(nb_pv)      AS nb_pv
            FROM`{{ params.bq_project }}.generated_data.profile_email_subject_stats`
            WHERE shoot_date BETWEEN task_start_date
                      AND task_end_date
            GROUP BY 1, 2, 3),
              get_monthly_nl_compare AS (
                  -- monthly perf per brand and nl completed with previous month data
                  SELECT
                      COALESCE(src.shoot_date,DATE_ADD(prev.shoot_date,INTERVAL 1 MONTH))             AS month_date,
                      COALESCE(src.brand_trigram,prev.brand_trigram)                                  AS brand_trigram,
                      COALESCE(src.newsletter,prev.newsletter)                                        AS newsletter,
                      COALESCE(src.nb_session,0)                                                      AS nb_session,
                      COALESCE(src.nb_pv,0)                                                           AS nb_pv,
                      COALESCE(prev.nb_session,0)                                                     AS prv_nb_session,
                      COALESCE(prev.nb_pv,0)                                                          AS prv_nb_pv,
                      COALESCE(SAFE_DIVIDE(src.nb_pv,src.nb_session),0)                               AS nb_pv_per_session,
                      COALESCE(SAFE_DIVIDE(prev.nb_pv,prev.nb_session),0)                             AS prv_pv_per_session,
                      COALESCE(SAFE_DIVIDE(src.nb_session - prev.nb_session,prev.nb_session),0)       AS evol_nb_session,
                      COALESCE(SAFE_DIVIDE(src.nb_pv - prev.nb_pv,prev.nb_pv),0)                      AS evol_nb_pv
                  FROM get_monthly_nl_perf AS src
                  FULL OUTER JOIN
                  get_monthly_nl_perf AS prev
                  ON src.brand_trigram = prev.brand_trigram and src.newsletter = prev.newsletter
                         -- the condition is done on both sides for the full outer join to function properly
                  AND (src.shoot_date = DATE_ADD(prev.shoot_date,INTERVAL 1 MONTH) OR prev.shoot_date = DATE_SUB(src.shoot_date,INTERVAL 1 MONTH))
                  WHERE src.shoot_date <= task_end_date )
        SELECT
            month_date,
            brand_trigram,
            nb_session                                                                  AS nb_session,
            newsletter,
            nb_pv                                                                       AS nb_pv,
            prv_nb_session,
            prv_nb_pv,
            nb_pv_per_session,
            prv_pv_per_session,
            evol_nb_session,
            evol_nb_pv,
            COALESCE(SAFE_DIVIDE(nb_pv_per_session - prv_pv_per_session,prv_pv_per_session),0)       AS evol_pv_per_session,
        FROM get_monthly_nl_compare ) AS ref
    ON dest.month_date = ref.month_date
        AND dest.brand_trigram = ref.brand_trigram
        AND dest.newsletter = ref.newsletter
    WHEN MATCHED THEN UPDATE SET
        dest.nb_session = ref.nb_session,
        dest.nb_pv = ref.nb_pv,
        dest.nb_pv_per_session = ref.nb_pv_per_session,
        dest.prv_nb_session = ref.prv_nb_session,
        dest.prv_nb_pv = ref.prv_nb_pv,
        dest.prv_pv_per_session = ref.prv_pv_per_session,
        dest.evol_nb_session = ref.evol_nb_session,
        dest.evol_nb_pv = ref.evol_nb_pv,
        dest.evol_pv_per_session = ref.evol_pv_per_session
    WHEN NOT MATCHED THEN INSERT (month_date,
                                  brand_trigram,
                                  newsletter,
                                  nb_session,
                                  nb_pv,
                                  nb_pv_per_session,
                                  prv_nb_session,
                                  prv_nb_pv,
                                  prv_pv_per_session,
                                  evol_nb_session,
                                  evol_nb_pv,
                                  evol_pv_per_session)
        VALUES
            (ref.month_date,
             ref.brand_trigram,
             ref.newsletter,
             ref.nb_session,
             ref.nb_pv,
             ref.nb_pv_per_session,
             ref.prv_nb_session,
             ref.prv_nb_pv,
             ref.prv_pv_per_session,
             ref.evol_nb_session,
             ref.evol_nb_pv,
             ref.evol_pv_per_session);
-- merge statement for brand_trigram/monthly
MERGE INTO
    `{{ params.bq_project }}.business_data.email_subject_stats_monthly_by_brand` AS dest
    USING (
        WITH get_monthly_brand_perf    AS (
            -- monthly perf per  brand
            SELECT
                DATE_TRUNC(shoot_date,MONTH) as shoot_date,
                brand_trigram,
                SUM(nb_session) AS nb_session,
                SUM(nb_pv)      AS nb_pv
            FROM`{{ params.bq_project }}.generated_data.profile_email_subject_stats`
            WHERE shoot_date BETWEEN task_start_date
                      AND task_end_date
            GROUP BY 1, 2),
              get_monthly_brand_compare AS (
                  -- monthly perf per brand completed with previous month data
                  SELECT
                      COALESCE(src.shoot_date,DATE_ADD(prev.shoot_date,INTERVAL 1 MONTH))             AS month_date,
                      COALESCE(src.brand_trigram,prev.brand_trigram)                                  AS brand_trigram,
                      COALESCE(src.nb_session,0)                                                      AS nb_session,
                      COALESCE(src.nb_pv,0)                                                           AS nb_pv,
                      COALESCE(prev.nb_session,0)                                                     AS prv_nb_session,
                      COALESCE(prev.nb_pv,0)                                                          AS prv_nb_pv,
                      COALESCE(SAFE_DIVIDE(src.nb_pv,src.nb_session),0)                               AS nb_pv_per_session,
                      COALESCE(SAFE_DIVIDE(prev.nb_pv,prev.nb_session),0)                             AS prv_pv_per_session,
                      COALESCE(SAFE_DIVIDE(src.nb_session - prev.nb_session,prev.nb_session),0)       AS evol_nb_session,
                      COALESCE(SAFE_DIVIDE(src.nb_pv - prev.nb_pv,prev.nb_pv),0)                      AS evol_nb_pv
                  FROM get_monthly_brand_perf AS src
                  FULL OUTER JOIN
                  get_monthly_brand_perf AS prev
                  ON src.brand_trigram = prev.brand_trigram
                  AND (src.shoot_date = DATE_ADD(prev.shoot_date,INTERVAL 1 MONTH) OR prev.shoot_date = DATE_SUB(src.shoot_date,INTERVAL 1 MONTH))
                  WHERE src.shoot_date <= task_end_date )
        SELECT
            month_date,
            brand_trigram,
            nb_session                                                                  AS nb_session,
            nb_pv                                                                       AS nb_pv,
            prv_nb_session,
            prv_nb_pv,
            nb_pv_per_session,
            prv_pv_per_session,
            evol_nb_session,
            evol_nb_pv,
            COALESCE(SAFE_DIVIDE(nb_pv_per_session - prv_pv_per_session,prv_pv_per_session),0)       AS evol_pv_per_session,
        FROM get_monthly_brand_compare ) AS ref
    ON dest.month_date = ref.month_date
        AND dest.brand_trigram = ref.brand_trigram
    WHEN MATCHED THEN UPDATE SET
        dest.nb_session = ref.nb_session, dest.nb_pv = ref.nb_pv, dest.nb_pv_per_session = ref.nb_pv_per_session,
        dest.prv_nb_session = ref.prv_nb_session, dest.prv_nb_pv = ref.prv_nb_pv, dest.prv_pv_per_session = ref.prv_pv_per_session,
        dest.evol_nb_session = ref.evol_nb_session, dest.evol_nb_pv = ref.evol_nb_pv, dest.evol_pv_per_session = ref.evol_pv_per_session
    WHEN NOT MATCHED THEN INSERT
        (month_date,
         brand_trigram,
         nb_session,
         nb_pv,
         nb_pv_per_session,
         prv_nb_session,
         prv_nb_pv,
         prv_pv_per_session,
         evol_nb_session,
         evol_nb_pv,
         evol_pv_per_session)
        VALUES
            (ref.month_date,
             ref.brand_trigram,
             ref.nb_session,
             ref.nb_pv,
             ref.nb_pv_per_session,
             ref.prv_nb_session,
             ref.prv_nb_pv,
             ref.prv_pv_per_session,
             ref.evol_nb_session,
             ref.evol_nb_pv,
             ref.evol_pv_per_session);