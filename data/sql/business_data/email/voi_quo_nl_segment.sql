-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_email_segment.segment-sha256_voi_quo_nl`(  

    email_sha256        STRING      NOT NULL      OPTIONS(description="ref: {{ params.bq_project }}.store_matrix_email.profile_master_id.email_sha256"),
    update_date         DATE        NOT NULL            OPTIONS(description="creation date")
    )
    OPTIONS(description="Create segment of sub profiles to voici quotidienne Nl."||
                        "DAG: {{ dag.dag_id }}. "||
                        "Sync: daily");

TRUNCATE TABLE `{{ params.bq_project }}.store_email_segment.segment-sha256_voi_quo_nl` ;

INSERT INTO `{{ params.bq_project }}.store_email_segment.segment-sha256_voi_quo_nl`
SELECT 

  pe.email_sha256, 
  DATE("{{ next_ds }}") AS update_date

FROM `{{ params.bq_project }}.store_matrix_email.profiles_email_consents` AS pec
JOIN `{{ params.bq_project }}.refined_data.profile_email` AS pe ON pec.profile_master_id = pe.email_profile_master_id
JOIN `{{ params.bq_project }}.refined_data.email_base` AS eb ON pec.email_consent_id = eb.consent_id
WHERE 
  -- profile sub to voici quotidienne NL
  pec.consent_status = "sub"
    AND 
  eb.consent_public_ref = "voici_quotidienne_nl";
