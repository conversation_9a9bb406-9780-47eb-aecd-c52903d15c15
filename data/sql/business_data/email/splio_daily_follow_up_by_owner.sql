-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE task_start_date DEFAULT DATE_SUB(DATE("{{ next_ds }}"),INTERVAL {{ params.time_interval }});
DECLARE task_end_date DEFAULT DATE("{{ next_ds }}");

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.business_data.splio_stats_by_owner`
(
    campaign_date        DATE    OPTIONS  (description ="Date of the campaign."),
    owner_name           STRING  OPTIONS  (description ="Name of the owner: prisma vs cerise."),
    campaigns_volume     INT64   OPTIONS  (description ="Number of campaigns with email send-outs."),
    target_emails        INT64   OPTIONS  (description ="Total number of target emails."),
    sent_emails          INT64   OPTIONS  (description ="Total number of sent emails."),
    delivered_emails     INT64   OPTIONS  (description ="Total number of delivered emails."),
    opened_emails        INT64   OPTIONS  (description ="Total number of opened emails."),
    clicked_emails       INT64   OPTIONS  (description ="Total number of clicked emails."),
    unsubs_volume        INT64   OPTIONS  (description ="Total number of unsubscribes"),
    send_rate            FLOAT64 OPTIONS  (description ="Rate of emails sent (sent emails / target emails)."),
    open_rate            FLOAT64 OPTIONS  (description ="Rate of email opens (opened emails / sent emails)."),
    click_rate           FLOAT64 OPTIONS  (description ="Rate of email clicks (clicked emails / opened emails)."),
    reactivity_rate      FLOAT64 OPTIONS  (description ="Rate of reactivity (clicked emails / sent emails)."),
    delivery_rate        FLOAT64 OPTIONS  (description ="Rate of email delivery (delivered emails / sent emails)."),
    unsubs_per_open      FLOAT64 OPTIONS  (description ="Rate of email unsubs per open emails (unsubs volume / open emails)."),
    unsubs_per_sent      FLOAT64 OPTIONS  (description ="Rate of email unsubs per sent emails (unsubs volume / sent emails).")
)
    PARTITION BY campaign_date
    OPTIONS (description ="Contains campaign performance metrics by campaign date and owner. \n" ||
                          "DAG: {{ dag.dag_id }} \n" ||
                          "Sync: daily");
---- create monthly table
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.business_data.splio_stats_by_owner_monthly`
(
    campaign_month       DATE    OPTIONS  (description ="Month of the campaign."),
    owner_name           STRING  OPTIONS  (description ="Name of the owner: prisma vs cerise."),
    campaigns_volume     INT64   OPTIONS  (description ="Number of campaigns with email send-outs."),
    target_emails        INT64   OPTIONS  (description ="Total number of target emails."),
    sent_emails          INT64   OPTIONS  (description ="Total number of sent emails."),
    delivered_emails     INT64   OPTIONS  (description ="Total number of delivered emails."),
    opened_emails        INT64   OPTIONS  (description ="Total number of opened emails."),
    clicked_emails       INT64   OPTIONS  (description ="Total number of clicked emails."),
    unsubs_volume        INT64   OPTIONS  (description ="Total number of unsubscribes"),
    send_rate            FLOAT64 OPTIONS  (description ="Rate of emails sent (sent emails / target emails)."),
    open_rate            FLOAT64 OPTIONS  (description ="Rate of email opens (opened emails / sent emails)."),
    click_rate           FLOAT64 OPTIONS  (description ="Rate of email clicks (clicked emails / opened emails)."),
    reactivity_rate      FLOAT64 OPTIONS  (description ="Rate of reactivity (clicked emails / sent emails)."),
    delivery_rate        FLOAT64 OPTIONS  (description ="Rate of email delivery (delivered emails / sent emails)."),
    unsubs_per_open      FLOAT64 OPTIONS  (description ="Rate of email unsubs per open emails (unsubs volume / open emails)."),
    unsubs_per_sent      FLOAT64 OPTIONS  (description ="Rate of email unsubs per sent emails (unsubs volume / sent emails).")
)
    PARTITION BY campaign_month
    OPTIONS (description ="Contains campaign performance metrics by campaign month and owner. \n" ||
                          "DAG: {{ dag.dag_id }} \n" ||
                          "Sync: daily");
{% if params.partial == true %}
    SET task_start_date = DATE("{{ params.start_date }}");
    SET task_end_date = DATE("{{ params.end_date }}");
    -- Delete rows before insert updated data

    DELETE FROM `{{ params.bq_project }}.business_data.splio_stats_by_owner`
    WHERE campaign_date BETWEEN task_start_date AND task_end_date;

    DELETE FROM `{{ params.bq_project }}.business_data.splio_stats_by_owner_monthly`
    WHERE campaign_month BETWEEN DATE_TRUNC(task_start_date, MONTH) AND DATE_TRUNC(task_end_date, MONTH);
{% endif %}

MERGE INTO `{{ params.bq_project }}.business_data.splio_stats_by_owner` AS dest
USING (
    SELECT
        campaign_date,
        splio.owner_name,
        SUM(splio.campaigns_volume) AS campaigns_volume,
        SUM(splio.target_emails) AS target_emails,
        SUM(splio.sent_emails) AS sent_emails,
        SUM(splio.delivered_emails) AS delivered_emails,
        SUM(splio.opened_emails) AS opened_emails,
        SUM(splio.clicked_emails) AS clicked_emails,
        SUM(splio.unsubs_volume) AS unsubs_volume,
        ROUND(SUM(splio.sent_emails) / NULLIF(SUM(splio.target_emails), 0), 3) AS send_rate,
        ROUND(SUM(splio.opened_emails) / NULLIF(SUM(splio.sent_emails), 0), 3) AS open_rate,
        ROUND(SUM(splio.clicked_emails) / NULLIF(SUM(splio.opened_emails), 0), 3) AS click_rate,
        ROUND(SUM(splio.clicked_emails) / NULLIF(SUM(splio.sent_emails), 0), 3) AS reactivity_rate,
        ROUND(SUM(splio.delivered_emails) / NULLIF(SUM(splio.sent_emails), 0), 3) AS delivery_rate,
        ROUND(SUM(splio.unsubs_volume) / NULLIF(SUM(splio.sent_emails), 0), 3) AS unsubs_per_sent,
        ROUND(SUM(splio.unsubs_volume) / NULLIF(SUM(splio.opened_emails), 0), 3) AS unsubs_per_open
    FROM `{{ params.bq_project }}.business_data.splio_stats_by_consent` AS splio
    WHERE campaign_date BETWEEN task_start_date AND task_end_date
    GROUP BY ALL
) AS source
ON
    dest.campaign_date = source.campaign_date
    AND dest.owner_name = source.owner_name
WHEN MATCHED THEN
    UPDATE SET
        dest.campaigns_volume = source.campaigns_volume,
        dest.target_emails = source.target_emails,
        dest.sent_emails = source.sent_emails,
        dest.delivered_emails = source.delivered_emails,
        dest.opened_emails = source.opened_emails,
        dest.clicked_emails = source.clicked_emails,
        dest.unsubs_volume = source.unsubs_volume,
        dest.send_rate = source.send_rate,
        dest.open_rate = source.open_rate,
        dest.click_rate = source.click_rate,
        dest.reactivity_rate = source.reactivity_rate,
        dest.delivery_rate = source.delivery_rate,
        dest.unsubs_per_sent = source.unsubs_per_sent,
        dest.unsubs_per_open = source.unsubs_per_open
WHEN NOT MATCHED THEN
    INSERT (
        campaign_date,
        owner_name,
        campaigns_volume,
        target_emails,
        sent_emails,
        delivered_emails,
        opened_emails,
        clicked_emails,
        unsubs_volume,
        send_rate,
        open_rate,
        click_rate,
        reactivity_rate,
        delivery_rate,
        unsubs_per_sent,
        unsubs_per_open
    ) VALUES (
        source.campaign_date,
        source.owner_name,
        source.campaigns_volume,
        source.target_emails,
        source.sent_emails,
        source.delivered_emails,
        source.opened_emails,
        source.clicked_emails,
        source.unsubs_volume,
        source.send_rate,
        source.open_rate,
        source.click_rate,
        source.reactivity_rate,
        source.delivery_rate,
        source.unsubs_per_sent,
        source.unsubs_per_open
    );

MERGE INTO `{{ params.bq_project }}.business_data.splio_stats_by_owner_monthly` AS dest
USING (
    SELECT
        DATE_TRUNC(campaign_date, MONTH) AS campaign_month,
        splio.owner_name,
        SUM(splio.campaigns_volume) AS campaigns_volume,
        SUM(splio.target_emails) AS target_emails,
        SUM(splio.sent_emails) AS sent_emails,
        SUM(splio.delivered_emails) AS delivered_emails,
        SUM(splio.opened_emails) AS opened_emails,
        SUM(splio.clicked_emails) AS clicked_emails,
        SUM(splio.unsubs_volume) AS unsubs_volume,
        ROUND(SUM(splio.sent_emails) / NULLIF(SUM(splio.target_emails), 0), 3) AS send_rate,
        ROUND(SUM(splio.opened_emails) / NULLIF(SUM(splio.sent_emails), 0), 3) AS open_rate,
        ROUND(SUM(splio.clicked_emails) / NULLIF(SUM(splio.opened_emails), 0), 3) AS click_rate,
        ROUND(SUM(splio.clicked_emails) / NULLIF(SUM(splio.sent_emails), 0), 3) AS reactivity_rate,
        ROUND(SUM(splio.delivered_emails) / NULLIF(SUM(splio.sent_emails), 0), 3) AS delivery_rate,
        ROUND(SUM(splio.unsubs_volume) / NULLIF(SUM(splio.sent_emails), 0), 3) AS unsubs_per_sent,
        ROUND(SUM(splio.unsubs_volume) / NULLIF(SUM(splio.opened_emails), 0), 3) AS unsubs_per_open
    FROM `{{ params.bq_project }}.business_data.splio_stats_by_owner` AS splio
    WHERE DATE_TRUNC(splio.campaign_date, MONTH) BETWEEN DATE_TRUNC(task_start_date, MONTH) AND DATE_TRUNC(task_end_date, MONTH)
    GROUP BY ALL
) AS source
ON
    dest.campaign_month = source.campaign_month
    AND dest.owner_name = source.owner_name
WHEN MATCHED THEN
    UPDATE SET
        dest.campaigns_volume = source.campaigns_volume,
        dest.target_emails = source.target_emails,
        dest.sent_emails = source.sent_emails,
        dest.delivered_emails = source.delivered_emails,
        dest.opened_emails = source.opened_emails,
        dest.clicked_emails = source.clicked_emails,
        dest.unsubs_volume = source.unsubs_volume,
        dest.send_rate = source.send_rate,
        dest.open_rate = source.open_rate,
        dest.click_rate = source.click_rate,
        dest.reactivity_rate = source.reactivity_rate,
        dest.delivery_rate = source.delivery_rate,
        dest.unsubs_per_sent = source.unsubs_per_sent,
        dest.unsubs_per_open = source.unsubs_per_open
WHEN NOT MATCHED THEN
    INSERT (
        campaign_month,
        owner_name,
        campaigns_volume,
        target_emails,
        sent_emails,
        delivered_emails,
        opened_emails,
        clicked_emails,
        unsubs_volume,
        send_rate,
        open_rate,
        click_rate,
        reactivity_rate,
        delivery_rate,
        unsubs_per_sent,
        unsubs_per_open
    ) VALUES (
        source.campaign_month,
        source.owner_name,
        source.campaigns_volume,
        source.target_emails,
        source.sent_emails,
        source.delivered_emails,
        source.opened_emails,
        source.clicked_emails,
        source.unsubs_volume,
        source.send_rate,
        source.open_rate,
        source.click_rate,
        source.reactivity_rate,
        source.delivery_rate,
        source.unsubs_per_sent,
        source.unsubs_per_open
    );
