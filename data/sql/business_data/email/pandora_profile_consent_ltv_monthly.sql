-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.business_data.pandora_profile_consent_ltv_monthly` (
    acquisition_month           DATE       NOT NULL   OPTIONS(description="Acquisition date as month. Format: %Y-%m-01"),
    email_consent_public_ref    STRING     NOT NULL   OPTIONS(description="Email consent. ref: store_matrix.email.email_base"),
    count_acquired              INT64                 OPTIONS(description="Count of distinct acquired profiles by consent with Pandora"),
    count_alive                 INT64                 OPTIONS(description="Count of distinct alive profiles (profiles with unsub date NULL) by consent"),
    count_active                INT64                 OPTIONS(description="Count of distinct active profiles (click in the last 180 days or open in the last 90 days) by consent"),
    active_duration             FLOAT64               OPTIONS(description="Average of activity (open/click) duration since acquisition (in days)"),
    PRIMARY KEY (acquisition_month, email_consent_public_ref) NOT ENFORCED
)
PARTITION BY acquisition_month
CLUSTER BY email_consent_public_ref
OPTIONS(
    description="This table contains business KPIs for lifetime value on profiles acquired with Pandora (NEW or REA) since 2021-04 at consent level."
              ||"\n"
              ||"It's used in Dashboard LTV Macro:\n"
              ||"- Atlas Prod: {{ params.atlas_url_p }}\n"
              ||"- Atlas Preprod: {{ params.atlas_url_pp }}\n"
              ||"\n"
              ||"DAG: {{ dag.dag_id }}."
              ||"Sync: Monthly at the 1st day of month.\n"
);

-- If airflow parameter truncate_table is true, we clean table
{% if params.truncate_table %}
TRUNCATE TABLE `{{ params.bq_project }}.business_data.pandora_profile_consent_ltv_monthly`;
{% endif %}

-- Update table
MERGE `{{ params.bq_project }}.business_data.pandora_profile_consent_ltv_monthly` AS dst
USING (
    WITH count_users AS (
        SELECT
            DATE_TRUNC(acquisition_date, MONTH) AS acquisition_month,
            email_consent_public_ref,
            COUNT(DISTINCT email_profile_master_id) AS count_acquired,
            COUNT(DISTINCT IF(kpi.is_alive, email_profile_master_id, NULL)) AS count_alive,
            COUNT(DISTINCT IF(kpi.is_alive AND kpi.is_active, email_profile_master_id, NULL)) AS count_active,
            SUM(IF(kpi.is_active, GREATEST(IFNULL(kpi.open_since, 0), IFNULL(kpi.click_since, 0)), 0)) AS active_since_cumulated,   -- sum time between acquisition and last click/open in acquisition month by consent
            COUNTIF(kpi.is_active) AS total_active_users                                                                            -- count active profiles (do NOT use `DISTINCT` here)
        FROM `{{ params.bq_project }}.generated_data.pandora_profile_ltv`
        WHERE
{% if params.is_full %}
            -- Full date range
            DATE(acquisition_date) BETWEEN DATE('{{ params.start_date }}')
            AND LAST_DAY(DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL 1 MONTH), MONTH)
{% else %}
            -- Only the last revolved month
            DATE(acquisition_date) BETWEEN DATE_TRUNC(DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL 1 MONTH), MONTH)
            AND LAST_DAY(DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL 1 MONTH))
{% endif %}
        GROUP BY ALL
    )

    SELECT
        acquisition_month,
        email_consent_public_ref,
        count_acquired,
        count_alive,
        count_active,
        SAFE_DIVIDE(active_since_cumulated, total_active_users) AS active_duration
    FROM count_users
) AS ref
    ON ref.acquisition_month = dst.acquisition_month
    AND ref.email_consent_public_ref = dst.email_consent_public_ref

WHEN MATCHED THEN
    UPDATE SET
        dst.count_alive = ref.count_alive,
        dst.count_acquired = ref.count_acquired,
        dst.count_active = ref.count_active,
        dst.active_duration = ref.active_duration

WHEN NOT MATCHED THEN
    INSERT(
        acquisition_month,
        email_consent_public_ref,
        count_alive,
        count_acquired,
        count_active,
        active_duration
    )
    VALUES(
        ref.acquisition_month,
        ref.email_consent_public_ref,
        ref.count_alive,
        ref.count_acquired,
        ref.count_active,
        ref.active_duration);
