-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE start_date DEFAULT DATE("{{ params.start_date }}");
DECLARE end_date DEFAULT DATE("{{ params.end_date }}");

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.business_data.heatmap_nl_block_click`
(
    shoot_date            DATE      NOT NULL    OPTIONS(description="Shoot date"),
    rogue_one_email_id    INT64     NOT NULL    OPTIONS(description="Rogue-One ID"),
    nl_id                 STRING    NOT NULL    OPTIONS(description="Hash of the links"),
    consent_public_ref    STRING    NOT NULL    OPTIONS(description="Consent public ref"),
    consent_label         STRING    NOT NULL    OPTIONS(description="Consent label or Newsletter label"),
    brand_name            STRING    NOT NULL    OPTIONS(description="Brand"),
    brand_trigram         STRING    NOT NULL    OPTIONS(description="Brand as trigram"),
    brand_logo            STRING                OPTIONS(description="Brand logo as image"),
    pole_name             STRING    NOT NULL    OPTIONS(description="Pole name as enum=['People','Femmes','Premium','Cerise','TV']"),
    owner_name            STRING    NOT NULL    OPTIONS(description="Owner name as enum=['prisma','cerise']"),
    frequency             STRING                OPTIONS(description="Frequency as enum=['DAILY','BUSINESS_DAILY','WEEKLY','BI_WEEKLY','MONTHLY','BI_MONTHLY','EVENT']"),
    block_id              INT64     NOT NULL    OPTIONS(description="Block id as position"),
    block_type            STRING    NOT NULL    OPTIONS(description="Block type as enum=['header','body','footer','(not set)']"),
    url_position          INT64     NOT NULL    OPTIONS(description="URL position"),
    url                   STRING                OPTIONS(description="canonical URL"),
    url_type              STRING    NOT NULL    OPTIONS(description="URL type as enum=['ad','content','sub','unsub','mirror',...]"),
    url_target            STRING    NOT NULL    OPTIONS(description="URL target as enum=['powerspace','ividence','prismashop','crea_ad','content_<TRIGRAM>', ...]"),
    click                 INT64                 OPTIONS(description="Clicks on URL"),
    cumul_click           INT64                 OPTIONS(description="Clicks from the start of the newsletter to this position (included)"),
    block_id_median       INT64                 OPTIONS(description="Block ID as median block. It means 50% of clicks are made between the beginning and this block and 50% between this block and the end"),
    PRIMARY KEY (shoot_date, rogue_one_email_id, block_id, url_position) NOT ENFORCED
)
PARTITION BY shoot_date
CLUSTER BY brand_trigram, consent_public_ref, block_type, url_type
OPTIONS(
    description="This table contains clicks on NL per position for each shooted NL.\n" ||
                "It's used in the dahsboard Heatmap NL:\n" ||
                "Atlas Prod: https://atlas.prismadata.fr/data-product/61-heatmap-des-clics-sur-nl\n" ||
                "Atlas Preprod: https://atlas.preprod.prismadata.fr/data-product/112-heatmap-nl\n" ||
                "\n" ||
                "Sync.: Daily.\n" ||
                "DAG: {{ dag.dag_id }}"
);

{% if params.truncate_table %}
TRUNCATE TABLE `{{ params.bq_project }}.business_data.heatmap_nl_block_click`;
{% endif%}

-- if it's incremental mode, we work only on last 2 days
{% if params.is_full != true %}
SET start_date = DATE_SUB(DATE("{{ next_ds }}"), INTERVAL {{ params.time_interval }});
SET end_date = CURRENT_DATE();
{% endif %}

-- Delete rows before insert updated data
DELETE FROM `{{ params.bq_project }}.business_data.heatmap_nl_block_click`
WHERE shoot_date BETWEEN start_date AND end_date;

INSERT INTO `{{ params.bq_project }}.business_data.heatmap_nl_block_click`
WITH enriched_heatmap AS (
    -- Add some information from refined data email_base such as consent label, brand, pole, owner
    SELECT
        shoot_date,
        rogue_one_email_id,
        nl_id,
        consent_public_ref,
        eb.consent_label,
        eb.brand_name,
        eb.brand_trigram,
        eb.brand_logo,
        eb.pole_name,
        eb.owner_name,
        frequency,
        block_id,
        block_type,
        url_position,
        url,
        IFNULL(url_type, '(not set)') AS url_type,
        IFNULL(url_target, '(not set)') AS url_target,
        click,
        SUM(click) OVER (PARTITION BY eb.consent_public_ref, nl_id) AS click_rogue_one
    FROM `{{ params.bq_project }}.generated_data.heatmap_nl_block_click` AS hm
    LEFT JOIN `{{ params.bq_project }}.refined_data.email_base` AS eb USING(consent_public_ref)
    WHERE shoot_date BETWEEN start_date AND end_date
), cumulative_clicks AS (
    -- Cumulative clicks and median block identification
    SELECT
        * EXCEPT(click_rogue_one),
        SUM(click) OVER (PARTITION BY consent_public_ref,nl_id ORDER BY block_id) AS cumul_click,
        CAST(ROUND(click_rogue_one/2, 0) AS INT64) AS median_click
    FROM enriched_heatmap
)

SELECT DISTINCT
    * EXCEPT(median_click),
    -- When the 50% of total clicks are reached, current block corresponds to the "median block"
    MIN(IF(cumul_click >= median_click, block_id, NULL)) OVER (PARTITION BY nl_id) AS block_id_median
FROM cumulative_clicks;
