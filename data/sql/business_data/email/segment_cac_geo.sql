-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_email_segment.segment_cac_geo`(
    email           STRING                  OPTIONS(description="Email"),
    update_date     DATE                    OPTIONS(description="Generation Date")
) OPTIONS (description="CAC GEO Segment.\n"
              || "\n\n"
              || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'.");

-- empty table
TRUNCATE TABLE `{{ params.bq_project }}.store_email_segment.segment_cac_geo`;
-- insert segment data
INSERT INTO `{{ params.bq_project }}.store_email_segment.segment_cac_geo`
WITH cac_geo_sub_consents AS (
    -- get all active profiles subscribed to consents for cac or geo
    SELECT DISTINCT pec.profile_master_id, trigram
    FROM `{{ params.bq_project }}.store_matrix_email.profiles_email_consents` AS pec
    JOIN `{{ params.bq_project }}.store_karinto.email_consent` AS kec ON kec.id = pec.email_consent_id AND kec.type = 'nl'
    JOIN `{{ params.bq_project }}.store_karinto.brand` as b ON pec.brand_id = b.id
    WHERE pec.consent_status = 'sub'
      AND trigram in ("CAC", "GEO")
), geo_cac_subs AS (
    -- get all profiles with both brands present (cac and geo)
    SELECT
        profile_master_id,
        COUNT(1)
    FROM cac_geo_sub_consents AS pec
    GROUP BY 1
    HAVING COUNT(1) = 2
), profile_reactivity_last_90d AS (
    SELECT DISTINCT r.profile_master_id
    FROM `{{ params.bq_project }}.store_tracking.prisma_full_data` r
    WHERE DATE(r.datetime) >= DATE_SUB(CURRENT_DATE(), INTERVAL 90 DAY)
)
SELECT pmi.email, CURRENT_DATE() as update_date
FROM geo_cac_subs gcr
JOIN profile_reactivity_last_90d r ON r.profile_master_id = gcr.profile_master_id
JOIN `{{ params.bq_project }}.store_matrix_email.profile_master_id` pmi ON pmi.id = gcr.profile_master_id;