-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE debut STRING ;
DECLARE fin STRING ;

SET debut = FORMAT_DATETIME('%Y%m%d',DATE_SUB(CURRENT_DATE(), INTERVAL 3 YEAR));
SET fin = FORMAT_DATETIME('%Y%m%d',DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY));


CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_email_segment.segment-pmi_a_relancer_cam`(  

    profile_master_id   INT64    NOT NULL    OPTIONS(description="ref: {{ params.bq_project }}.store_matrix_email.profile_master_id.id"),
    update_date         DATE     NOT NULL    OPTIONS(description="creation date")
    )
    OPTIONS(description="Create segment of inactive profile for the brand CAM prioritized by site activity recency."||
                        "DAG: {{ dag.dag_id }}. "||
                        "Sync: daily at 01:15AM UTC+2");

TRUNCATE TABLE `{{ params.bq_project }}.store_email_segment.segment-pmi_a_relancer_cam` ;
INSERT INTO `{{ params.bq_project }}.store_email_segment.segment-pmi_a_relancer_cam`


    WITH actif_site AS (
    -- get all web id of active profile on Ca M'interesse website
        SELECT    
            hitscustomDimensions.value  AS web_id,
            date                        AS visit_date
        FROM `prisma-gan-bigquery-ojd-export.120214428.ga_sessions_*`,          -- id CAM 
            UNNEST(hits) AS hits , 
            UNNEST(hits.customDimensions) AS hitscustomDimensions
        WHERE _TABLE_SUFFIX BETWEEN debut and fin  
            AND hits.type = "EVENT" 
            AND hitscustomDimensions.index = 25 
            AND hits.eventInfo.eventAction   = "WebId" 
        GROUP BY hitscustomDimensions.value,
                date

    ), last_visit_pmc AS (
        SELECT web_id,
                MAX(visit_date) AS last_visit
        FROM actif_site 
        GROUP BY 1
    ), pmc_id AS (
        -- merge web_id to email_profile_master_id
        SELECT DISTINCT
                act.*,
                dig.id.email_profile_master_id
        FROM last_visit_pmc     AS act
        JOIN `{{ params.bq_project }}.business_data.profile_digital_360`                AS dig ON dig.id.pmc_web_id = act.web_id
        JOIN `{{ params.bq_project }}.store_email_segment.segment-pmi_inactifs_cam`     AS sic ON dig.id.email_profile_master_id = sic.profile_master_id
    )
    , snapshot_data AS (
    -- list profile who have already been shooted by segmentation tag
        SELECT profile_master_id,
                    segment,
                    min(snapshot_date) AS date_shoot
        FROM `{{ params.bq_project }}.generated_data.segmentation_tag_snapshot`, UNNEST(segment_names) AS segment
        WHERE segment NOT LIKE 'inactifs_%'
            AND segment NOT LIKE '%a_relancer%'
        GROUP BY 1, 2 
        HAVING DATE_DIFF(CURRENT_DATE(),min(snapshot_date), DAY) > 30
        ORDER BY 3 ASC
    )

    , list_pmi_visit AS (
    --filter profile who have not yet been shooted
        SELECT DISTINCT 
                    web_id,
                    last_visit,
                    email_profile_master_id AS profile_master_id,
                    CURRENT_DATE()          AS update_date
        FROM pmc_id 
        LEFT JOIN snapshot_data AS snap ON pmc_id.email_profile_master_id = snap.profile_master_id
        WHERE snap.profile_master_id IS NULL
        ORDER BY last_visit DESC
    )
    , segment_data AS (
    SELECT DISTINCT 
            profile_master_id,
            update_date, 
            last_visit
    FROM list_pmi_visit
    LIMIT 5000
    )

SELECT profile_master_id,
        update_date 
FROM segment_data 




