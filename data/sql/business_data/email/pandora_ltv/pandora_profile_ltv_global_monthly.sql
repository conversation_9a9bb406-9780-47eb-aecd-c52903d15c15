-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.business_data.pandora_profile_ltv_global_monthly` (
    reference_month                 DATE    NOT NULL    OPTIONS(description="Reference month for the calculations."),
    all_profiles                    STRUCT<
        profile_volume                  INT64       OPTIONS(description="Volume of distinct profiles in the reference month."),
        alive_number                    INT64       OPTIONS(description="Volume of distinct profiles alive in the reference month.\n"
                                                                      ||"To be considered alive, a profile must:\n"
                                                                      ||"-have no unsub\n"
                                                                      ||"- or have an unsub after the reference month"),
        avg_lifetime                    FLOAT64     OPTIONS(description="Average number of days between the acquisition date and the last alive date up to the reference month."),
        avg_activity_duration           FLOAT64     OPTIONS(description="Average number of days between the first event date and the last event date up to the reference month."),
        opens                           STRUCT<
            open_volume                    INT64       OPTIONS(description="Cumulated number of opens up to the reference month."),
            avg_open_volume                FLOAT64     OPTIONS(description="Average number of opens by profile"),
            median_open_volume             FLOAT64     OPTIONS(description="Daily median number of opens by profile."),
            unique_open_volume             INT64       OPTIONS(description="Cumulated number of unique opens up to the reference month."),
            avg_unique_open_volume         FLOAT64     OPTIONS(description="Average number of unique opens by profile."),
            median_unique_open_volume      FLOAT64     OPTIONS(description="Daily median number of unique opens by profile."),
            avg_distinct_open_day          FLOAT64     OPTIONS(description="Average number of distinct opening days up to the reference month by profile.")
        >                                           OPTIONS(description="Open-related KPIs"),
        clicks                          STRUCT<
            click_volume                   INT64       OPTIONS(description="Cumulated number of clicks up to the reference month."),
            avg_click_volume               FLOAT64     OPTIONS(description="Average number of clicks by profile"),
            median_click_volume            FLOAT64     OPTIONS(description="Daily median number of clicks by profile."),
            unique_click_volume            INT64       OPTIONS(description="Cumulated number of unique clicks up to the reference month."),
            avg_unique_click_volume        FLOAT64     OPTIONS(description="Average number of unique clicks by profile."),
            median_unique_click_volume     FLOAT64     OPTIONS(description="Daily median number of unique clicks by profile."),
            avg_distinct_click_day         FLOAT64     OPTIONS(description="Average number of distinct clicking days up to the reference month by profile.")
        >                                           OPTIONS(description="Click-related KPIs."),
        all_events                      STRUCT<
            total_event_volume               INT64       OPTIONS(description="Cumulated number of events up to the reference month."),
            avg_total_event_volume           FLOAT64     OPTIONS(description="Average number of events by profile"),
            median_total_event_volume        FLOAT64     OPTIONS(description="Daily median number of events by profile."),
            total_unique_event_volume        INT64       OPTIONS(description="Cumulated number of unique events up to the reference month."),
            avg_total_unique_event_volume    FLOAT64     OPTIONS(description="Average number of unique events by profile."),
            median_total_unique_event_volume FLOAT64     OPTIONS(description="Daily median number of unique events by profile."),
            avg_distinct_event_day           FLOAT64     OPTIONS(description="Average number of distinct days with events up to the reference month by profile.")
        >                                           OPTIONS(description="Global KPIs.")
    >                                                               OPTIONS(description="KPIs calculated for all profiles, regardless of their status (alive or not) and reactivity."),
    currently_active                 STRUCT<
        activation_rate                 FLOAT64     OPTIONS(description="Activation rate. It is calculated as:\n"
                                                                      ||"number of active and alive profiles up to the reference month / total number of acquired profiles up to the reference month"),
        profile_volume                  INT64       OPTIONS(description="Volume of distinct alive and currently active profiles in the reference month."),
        avg_lifetime                    FLOAT64     OPTIONS(description="Average number of days between the acquisition date and the last alive date up to the reference month."),
        avg_activity_duration           FLOAT64     OPTIONS(description="Average number of days between the first event date and the last event date up to the reference month."),
        opens                           STRUCT<
            open_volume                    INT64       OPTIONS(description="Cumulated number of opens up to the reference month."),
            avg_open_volume                FLOAT64     OPTIONS(description="Average number of opens by profile"),
            median_open_volume             FLOAT64     OPTIONS(description="Daily median number of opens by profile."),
            unique_open_volume             INT64       OPTIONS(description="Cumulated number of unique opens up to the reference month."),
            avg_unique_open_volume         FLOAT64     OPTIONS(description="Average number of unique opens by profile."),
            median_unique_open_volume      FLOAT64     OPTIONS(description="Daily median number of unique opens by profile."),
            avg_distinct_open_day          FLOAT64     OPTIONS(description="Average number of distinct opening days up to the reference month by profile.")
        >                                           OPTIONS(description="Open-related KPIs"),
        clicks                          STRUCT<
            click_volume                   INT64       OPTIONS(description="Cumulated number of clicks up to the reference month."),
            avg_click_volume               FLOAT64     OPTIONS(description="Average number of clicks by profile"),
            median_click_volume            FLOAT64     OPTIONS(description="Daily median number of clicks by profile."),
            unique_click_volume            INT64       OPTIONS(description="Cumulated number of unique clicks up to the reference month."),
            avg_unique_click_volume        FLOAT64     OPTIONS(description="Average number of unique clicks by profile."),
            median_unique_click_volume     FLOAT64     OPTIONS(description="Daily median number of unique clicks by profile."),
            avg_distinct_click_day         FLOAT64     OPTIONS(description="Average number of distinct clicking days up to the reference month by profile.")
        >                                           OPTIONS(description="Click-related KPIs."),
        all_events                      STRUCT<
            total_event_volume               INT64       OPTIONS(description="Cumulated number of events up to the reference month."),
            avg_total_event_volume           FLOAT64     OPTIONS(description="Average number of events by profile"),
            median_total_event_volume        FLOAT64     OPTIONS(description="Daily median number of events by profile."),
            total_unique_event_volume        INT64       OPTIONS(description="Cumulated number of unique events up to the reference month."),
            avg_total_unique_event_volume    FLOAT64     OPTIONS(description="Average number of unique events by profile."),
            median_total_unique_event_volume FLOAT64     OPTIONS(description="Daily median number of unique events by profile."),
            avg_distinct_event_day           FLOAT64     OPTIONS(description="Average number of distinct days with events up to the reference month by profile.")
        >                                           OPTIONS(description="Global KPIs.")
    >                                                               OPTIONS(description="KPIs calculated for currently active profiles only."),
    active_once                     STRUCT<
        activation_rate                 FLOAT64     OPTIONS(description="Activation rate. It is calculated as:\n"
                                                                      ||"number of profiles with at least one event up to the reference month / total number of acquired profiles up to the reference month"),
        profile_volume                  INT64       OPTIONS(description="Volume of distinct alive and currently active profiles in the reference month."),
        avg_lifetime                    FLOAT64     OPTIONS(description="Average number of days between the acquisition date and the last alive date up to the reference month."),
        avg_activity_duration           FLOAT64     OPTIONS(description="Average number of days between the first event date and the last event date up to the reference month."),
        opens                           STRUCT<
            open_volume                    INT64       OPTIONS(description="Cumulated number of opens up to the reference month."),
            avg_open_volume                FLOAT64     OPTIONS(description="Average number of opens by profile"),
            median_open_volume             FLOAT64     OPTIONS(description="Daily median number of opens by profile."),
            unique_open_volume             INT64       OPTIONS(description="Cumulated number of unique opens up to the reference month."),
            avg_unique_open_volume         FLOAT64     OPTIONS(description="Average number of unique opens by profile."),
            median_unique_open_volume      FLOAT64     OPTIONS(description="Daily median number of unique opens by profile."),
            avg_distinct_open_day          FLOAT64     OPTIONS(description="Average number of distinct opening days up to the reference month by profile.")
        >                                           OPTIONS(description="Open-related KPIs"),
        clicks                          STRUCT<
            click_volume                   INT64       OPTIONS(description="Cumulated number of clicks up to the reference month."),
            avg_click_volume               FLOAT64     OPTIONS(description="Average number of clicks by profile"),
            median_click_volume            FLOAT64     OPTIONS(description="Daily median number of clicks by profile."),
            unique_click_volume            INT64       OPTIONS(description="Cumulated number of unique clicks up to the reference month."),
            avg_unique_click_volume        FLOAT64     OPTIONS(description="Average number of unique clicks by profile."),
            median_unique_click_volume     FLOAT64     OPTIONS(description="Daily median number of unique clicks by profile."),
            avg_distinct_click_day         FLOAT64     OPTIONS(description="Average number of distinct clicking days up to the reference month by profile.")
        >                                           OPTIONS(description="Click-related KPIs."),
        all_events                      STRUCT<
            total_event_volume               INT64       OPTIONS(description="Cumulated number of events up to the reference month."),
            avg_total_event_volume           FLOAT64     OPTIONS(description="Average number of events by profile"),
            median_total_event_volume        FLOAT64     OPTIONS(description="Daily median number of events by profile."),
            total_unique_event_volume        INT64       OPTIONS(description="Cumulated number of unique events up to the reference month."),
            avg_total_unique_event_volume    FLOAT64     OPTIONS(description="Average number of unique events by profile."),
            median_total_unique_event_volume FLOAT64     OPTIONS(description="Daily median number of unique events by profile."),
            avg_distinct_event_day           FLOAT64     OPTIONS(description="Average number of distinct days with events up to the reference month by profile.")
        >                                           OPTIONS(description="Global KPIs.")
    >                                                               OPTIONS(description="KPIs calculated for profiles with at least one event up to the reference month."),
    PRIMARY KEY(reference_month) NOT ENFORCED
)
PARTITION BY reference_month
OPTIONS(
    description="This table contains Pandora profiles' cumulated activity.\n"
              ||"It is calculated monthly, regardless of the acquisition date.\n\n"
              ||"DAG: {{ dag.dag_id }}.\n\n"
              ||"Sync: Monthly at the 1st day of month.",
    labels=[
        ('scope', 'pandora'),
        ('project', 'pandora_ltv'),
        ('creator', 'rodrigo_santana')
    ]
);

TRUNCATE TABLE `{{ params.bq_project }}.business_data.pandora_profile_ltv_global_monthly`;

INSERT INTO `{{ params.bq_project }}.business_data.pandora_profile_ltv_global_monthly`
WITH flatten_data AS (
    -- flatten data and calculate total events
    SELECT
        reference_month,
        email_profile_master_id,
        acquisition_date,
        email_consent_public_ref,
        observation_date,
        open_volume,
        unique_open_volume,
        click_volume,
        unique_click_volume,
        is_alive,
        last_alive_date
    FROM `{{ params.bq_project }}.generated_data.pandora_profile_activity_by_consent`, UNNEST(reactivity_data)
),

get_first_event_date_by_profile AS (
    -- get first event date by profile and brand
    -- used to calculate the activity duration
    SELECT
        email_profile_master_id,
        MIN(observation_date) AS first_event_date,
        MIN(acquisition_date) AS first_acquisition_date
    FROM flatten_data
    GROUP BY ALL
),

get_cumulated_events AS (
    -- get cumulated events by joining unnested and nested versions of table pandora_profile_activity_by_profile
    -- each month keeps events from previous months
    SELECT
        pac.reference_month,
        pac.email_profile_master_id,
        fd.observation_date,
        fd.open_volume,
        fd.unique_open_volume,
        fd.click_volume,
        fd.unique_click_volume,
        IFNULL(fd.open_volume, 0) + IFNULL(fd.click_volume, 0) AS total_event_volume,
        IFNULL(fd.unique_open_volume, 0) + IFNULL(fd.unique_click_volume, 0) AS total_unique_event_volume,
        pac.is_alive,
        pac.last_alive_date
    FROM `{{ params.bq_project }}.generated_data.pandora_profile_activity_by_consent` AS pac
    LEFT JOIN flatten_data AS fd
        ON pac.email_profile_master_id = fd.email_profile_master_id
        AND pac.email_consent_public_ref = fd.email_consent_public_ref
        AND LAST_DAY(pac.reference_month) >= fd.observation_date
),

aggregate_events_by_profile AS (
    -- get last event dates by reference month
    -- sum total events by month
    -- count distinct activity days
    SELECT
        reference_month,
        email_profile_master_id,
        LOGICAL_OR(is_alive) AS is_alive,
        MAX(last_alive_date) AS last_alive_date,
        SUM(open_volume) AS open_volume,
        SUM(unique_open_volume) AS unique_open_volume,
        SUM(click_volume) AS click_volume,
        SUM(unique_click_volume) AS unique_click_volume,
        SUM(total_event_volume) AS total_event_volume,
        SUM(total_unique_event_volume) AS total_unique_event_volume,
        COUNT(DISTINCT IF(click_volume > 0, observation_date, NULL)) AS distinct_click_days,
        COUNT(DISTINCT IF(open_volume > 0, observation_date, NULL)) AS distinct_open_days,
        COUNT(DISTINCT observation_date) AS distinct_event_days,
        MAX(IF(open_volume > 0, observation_date, NULL)) AS last_open_date,
        MAX(IF(click_volume > 0, observation_date, NULL))  AS last_click_date,
        MAX(observation_date) AS last_event_date
    FROM get_cumulated_events
    GROUP BY ALL
),

calculate_durations AS (
    -- calculate durations (lifetime, activity duration)
    -- classify profile as alive/not alive based on the last open/click dates
    SELECT
        ebp.reference_month,
        ebp.email_profile_master_id,
        ebp.is_alive,
        ebp.open_volume,
        ebp.unique_open_volume,
        ebp.click_volume,
        ebp.unique_click_volume,
        ebp.total_event_volume,
        ebp.total_unique_event_volume,
        ebp.distinct_click_days,
        ebp.distinct_open_days,
        ebp.distinct_event_days,
        CASE
            WHEN LAST_DAY(ebp.reference_month) BETWEEN ebp.last_open_date AND DATE_ADD(ebp.last_open_date, INTERVAL 90 DAY) THEN TRUE
            WHEN LAST_DAY(ebp.reference_month) BETWEEN ebp.last_click_date AND DATE_ADD(ebp.last_click_date, INTERVAL 180 DAY) THEN TRUE
            ELSE FALSE
        END AS is_active,
        CASE
            WHEN ebp.last_event_date IS NULL THEN CAST(NULL AS INT64)
            ELSE DATE_DIFF(ebp.last_event_date, fedp.first_event_date, DAY) + 1
        END AS activity_duration,
        CASE
            WHEN ebp.last_alive_date IS NULL THEN CAST(NULL AS INT64)
            ELSE DATE_DIFF(ebp.last_alive_date, fedp.first_acquisition_date, DAY) + 1
        END AS lifetime
    FROM aggregate_events_by_profile AS ebp
    LEFT JOIN get_first_event_date_by_profile AS fedp USING(email_profile_master_id)
),

compute_kpis_all_profiles AS (
    -- compute kpis for all profiles
    SELECT
        reference_month,
        COUNT(email_profile_master_id) AS profile_volume,
        COUNTIF(is_alive) AS alive_volume,
        AVG(lifetime) AS avg_lifetime,
        AVG(activity_duration) AS avg_activity_duration,
        -- open
        SUM(open_volume) AS open_volume,
        AVG(IFNULL(SAFE_DIVIDE(open_volume, distinct_open_days), 0)) AS avg_open_volume,
        APPROX_QUANTILES(IFNULL(SAFE_DIVIDE(open_volume, distinct_open_days), 0), 100)[OFFSET(50)] AS median_open_volume,
        SUM(unique_open_volume) AS unique_open_volume,
        AVG(IFNULL(SAFE_DIVIDE(unique_open_volume, distinct_open_days), 0)) AS avg_unique_open_volume,
        APPROX_QUANTILES(IFNULL(SAFE_DIVIDE(unique_open_volume, distinct_open_days), 0), 100)[OFFSET(50)] AS median_unique_open_volume,
        AVG(distinct_open_days) AS avg_distinct_open_day,
        -- click
        SUM(click_volume) AS click_volume,
        AVG(IFNULL(SAFE_DIVIDE(click_volume, distinct_click_days), 0)) AS avg_click_volume,
        APPROX_QUANTILES(IFNULL(SAFE_DIVIDE(click_volume, distinct_click_days), 0), 100)[OFFSET(50)] AS median_click_volume,
        SUM(unique_click_volume) AS unique_click_volume,
        AVG(IFNULL(SAFE_DIVIDE(unique_click_volume, distinct_click_days), 0)) AS avg_unique_click_volume,
        APPROX_QUANTILES(IFNULL(SAFE_DIVIDE(unique_click_volume, distinct_click_days), 0), 100)[OFFSET(50)] AS median_unique_click_volume,
        AVG(distinct_click_days) AS avg_distinct_click_day,
        -- all event
        SUM(total_event_volume) AS total_event_volume,
        AVG(IFNULL(SAFE_DIVIDE(total_event_volume, distinct_event_days), 0)) AS avg_total_event_volume,
        APPROX_QUANTILES(IFNULL(SAFE_DIVIDE(total_event_volume, distinct_event_days), 0), 100)[OFFSET(50)] AS median_total_event_volume,
        SUM(total_unique_event_volume) AS total_unique_event_volume,
        AVG(IFNULL(SAFE_DIVIDE(total_unique_event_volume, distinct_event_days), 0)) AS avg_total_unique_event_volume,
        APPROX_QUANTILES(IFNULL(SAFE_DIVIDE(total_unique_event_volume, distinct_event_days), 0), 100)[OFFSET(50)] AS median_total_unique_event_volume,
        AVG(distinct_event_days) AS avg_distinct_event_day
    FROM calculate_durations
    GROUP BY ALL
),


compute_kpis_currently_active AS (
    -- compute kpis for currently active profiles only
    -- to be active, a profile must still be alive
    SELECT
        reference_month,
        COUNT(email_profile_master_id) AS profile_volume,
        AVG(lifetime) AS avg_lifetime,
        AVG(activity_duration) AS avg_activity_duration,
        -- open
        SUM(open_volume) AS open_volume,
        AVG(IFNULL(SAFE_DIVIDE(open_volume, distinct_open_days), 0)) AS avg_open_volume,
        APPROX_QUANTILES(IFNULL(SAFE_DIVIDE(open_volume, distinct_open_days), 0), 100)[OFFSET(50)] AS median_open_volume,
        SUM(unique_open_volume) AS unique_open_volume,
        AVG(IFNULL(SAFE_DIVIDE(unique_open_volume, distinct_open_days), 0)) AS avg_unique_open_volume,
        APPROX_QUANTILES(IFNULL(SAFE_DIVIDE(unique_open_volume, distinct_open_days), 0), 100)[OFFSET(50)] AS median_unique_open_volume,
        AVG(distinct_open_days) AS avg_distinct_open_day,
        -- click
        SUM(click_volume) AS click_volume,
        AVG(IFNULL(SAFE_DIVIDE(click_volume, distinct_click_days), 0)) AS avg_click_volume,
        APPROX_QUANTILES(IFNULL(SAFE_DIVIDE(click_volume, distinct_click_days), 0), 100)[OFFSET(50)] AS median_click_volume,
        SUM(unique_click_volume) AS unique_click_volume,
        AVG(IFNULL(SAFE_DIVIDE(unique_click_volume, distinct_click_days), 0)) AS avg_unique_click_volume,
        APPROX_QUANTILES(IFNULL(SAFE_DIVIDE(unique_click_volume, distinct_click_days), 0), 100)[OFFSET(50)] AS median_unique_click_volume,
        AVG(distinct_click_days) AS avg_distinct_click_day,
        -- all event
        SUM(total_event_volume) AS total_event_volume,
        AVG(IFNULL(SAFE_DIVIDE(total_event_volume, distinct_event_days), 0)) AS avg_total_event_volume,
        APPROX_QUANTILES(IFNULL(SAFE_DIVIDE(total_event_volume, distinct_event_days), 0), 100)[OFFSET(50)] AS median_total_event_volume,
        SUM(total_unique_event_volume) AS total_unique_event_volume,
        AVG(IFNULL(SAFE_DIVIDE(total_unique_event_volume, distinct_event_days), 0)) AS avg_total_unique_event_volume,
        APPROX_QUANTILES(IFNULL(SAFE_DIVIDE(total_unique_event_volume, distinct_event_days), 0), 100)[OFFSET(50)] AS median_total_unique_event_volume,
        AVG(distinct_event_days) AS avg_distinct_event_day
    FROM calculate_durations
    WHERE
        is_alive
        AND is_active
    GROUP BY ALL
),

compute_kpis_active_once AS (
    -- compute kpis for profiles with at least one event (active at least once)
    SELECT
        reference_month,
        COUNT(email_profile_master_id) AS profile_volume,
        AVG(lifetime) AS avg_lifetime,
        AVG(activity_duration) AS avg_activity_duration,
        -- open
        SUM(open_volume) AS open_volume,
        AVG(IFNULL(SAFE_DIVIDE(open_volume, distinct_open_days), 0)) AS avg_open_volume,
        APPROX_QUANTILES(IFNULL(SAFE_DIVIDE(open_volume, distinct_open_days), 0), 100)[OFFSET(50)] AS median_open_volume,
        SUM(unique_open_volume) AS unique_open_volume,
        AVG(IFNULL(SAFE_DIVIDE(unique_open_volume, distinct_open_days), 0)) AS avg_unique_open_volume,
        APPROX_QUANTILES(IFNULL(SAFE_DIVIDE(unique_open_volume, distinct_open_days), 0), 100)[OFFSET(50)] AS median_unique_open_volume,
        AVG(distinct_open_days) AS avg_distinct_open_day,
        -- click
        SUM(click_volume) AS click_volume,
        AVG(IFNULL(SAFE_DIVIDE(click_volume, distinct_click_days), 0)) AS avg_click_volume,
        APPROX_QUANTILES(IFNULL(SAFE_DIVIDE(click_volume, distinct_click_days), 0), 100)[OFFSET(50)] AS median_click_volume,
        SUM(unique_click_volume) AS unique_click_volume,
        AVG(IFNULL(SAFE_DIVIDE(unique_click_volume, distinct_click_days), 0)) AS avg_unique_click_volume,
        APPROX_QUANTILES(IFNULL(SAFE_DIVIDE(unique_click_volume, distinct_click_days), 0), 100)[OFFSET(50)] AS median_unique_click_volume,
        AVG(distinct_click_days) AS avg_distinct_click_day,
        -- all event
        SUM(total_event_volume) AS total_event_volume,
        AVG(IFNULL(SAFE_DIVIDE(total_event_volume, distinct_event_days), 0)) AS avg_total_event_volume,
        APPROX_QUANTILES(IFNULL(SAFE_DIVIDE(total_event_volume, distinct_event_days), 0), 100)[OFFSET(50)] AS median_total_event_volume,
        SUM(total_unique_event_volume) AS total_unique_event_volume,
        AVG(IFNULL(SAFE_DIVIDE(total_unique_event_volume, distinct_event_days), 0)) AS avg_total_unique_event_volume,
        APPROX_QUANTILES(IFNULL(SAFE_DIVIDE(total_unique_event_volume, distinct_event_days), 0), 100)[OFFSET(50)] AS median_total_unique_event_volume,
        AVG(distinct_event_days) AS avg_distinct_event_day
    FROM calculate_durations
    WHERE total_event_volume > 0
    GROUP BY ALL
)

SELECT
    alp.reference_month,
    STRUCT(
        alp.profile_volume,
        alp.alive_volume,
        alp.avg_lifetime,
        alp.avg_activity_duration,
        STRUCT(
            alp.open_volume,
            alp.avg_open_volume,
            alp.median_open_volume,
            alp.unique_open_volume,
            alp.avg_unique_open_volume,
            alp.median_unique_open_volume,
            alp.avg_distinct_open_day
        ) AS opens,
        STRUCT(
            alp.click_volume,
            alp.avg_click_volume,
            alp.median_click_volume,
            alp.unique_click_volume,
            alp.avg_unique_click_volume,
            alp.median_unique_click_volume,
            alp.avg_distinct_click_day
        ) AS clicks,
        STRUCT(
            alp.total_event_volume,
            alp.avg_total_event_volume,
            alp.median_total_event_volume,
            alp.total_unique_event_volume,
            alp.avg_total_unique_event_volume,
            alp.median_total_unique_event_volume,
            alp.avg_distinct_event_day
        ) AS all_events
    ) AS all_profiles,
    STRUCT(
        SAFE_DIVIDE(cac.profile_volume, alp.profile_volume) AS activation_rate,
        cac.profile_volume,
        cac.avg_lifetime,
        cac.avg_activity_duration,
        STRUCT(
            cac.open_volume,
            cac.avg_open_volume,
            cac.median_open_volume,
            cac.unique_open_volume,
            cac.avg_unique_open_volume,
            cac.median_unique_open_volume,
            cac.avg_distinct_open_day
        ) AS opens,
        STRUCT(
            cac.click_volume,
            cac.avg_click_volume,
            cac.median_click_volume,
            cac.unique_click_volume,
            cac.avg_unique_click_volume,
            cac.median_unique_click_volume,
            cac.avg_distinct_click_day
        ) AS clicks,
        STRUCT(
            cac.total_event_volume,
            cac.avg_total_event_volume,
            cac.median_total_event_volume,
            cac.total_unique_event_volume,
            cac.avg_total_unique_event_volume,
            cac.median_total_unique_event_volume,
            cac.avg_distinct_event_day
        ) AS all_events
    ) AS currently_active,
    STRUCT(
        SAFE_DIVIDE(oac.profile_volume, alp.profile_volume) AS activation_rate,
        oac.profile_volume,
        oac.avg_lifetime,
        oac.avg_activity_duration,
        STRUCT(
            oac.open_volume,
            oac.avg_open_volume,
            oac.median_open_volume,
            oac.unique_open_volume,
            oac.avg_unique_open_volume,
            oac.median_unique_open_volume,
            oac.avg_distinct_open_day
        ) AS opens,
        STRUCT(
            oac.click_volume,
            oac.avg_click_volume,
            oac.median_click_volume,
            oac.unique_click_volume,
            oac.avg_unique_click_volume,
            oac.median_unique_click_volume,
            oac.avg_distinct_click_day
        ) AS clicks,
        STRUCT(
            oac.total_event_volume,
            oac.avg_total_event_volume,
            oac.median_total_event_volume,
            oac.total_unique_event_volume,
            oac.avg_total_unique_event_volume,
            oac.median_total_unique_event_volume,
            oac.avg_distinct_event_day
        ) AS all_events
    ) AS active_once
FROM compute_kpis_all_profiles AS alp
LEFT JOIN compute_kpis_currently_active AS cac USING(reference_month)
LEFT JOIN compute_kpis_active_once AS oac USING(reference_month);
