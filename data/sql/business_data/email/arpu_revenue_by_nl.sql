-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE start_date DEFAULT DATE("{{ params.start_date }}");
DECLARE end_date DEFAULT DATE("{{ params.end_date }}");

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.business_data.arpu_consolidated_by_nl`
(
    type                         STRING      OPTIONS(description="Type of revenue: direct, indirect, optin part"),
    observation_date             DATE        OPTIONS(description="Date of the observation"),
    email_profile_master_id      INT64       OPTIONS(description="Email profile master id"),
    email_consent_public_ref     STRING      OPTIONS(description="Consent"),
    utm_source                   STRING      OPTIONS(description="UTM source"),
    ad_partner                   STRING      OPTIONS(description="Ad partner"),
    revenue                      FLOAT64     OPTIONS(description="Revenue generated")
)
PARTITION BY observation_date CLUSTER BY email_consent_public_ref, utm_source, ad_partner
OPTIONS(
    description="This table contains revenue aggregated by consents.\n" ||
                "It's used in the ARPU dashboard:\n" ||
                "Atlas Prod: https://atlas.prismadata.fr/data-product/85-arpu-email-native-ads-sans-gala\n" ||
                "\n" ||
                "Sync.: Daily.\n" ||
                "DAG: {{ dag.dag_id }}"
);


-- if it's incremental mode, we work only on last 2 days

{% if params.is_full != true %}

SET start_date = DATE_SUB(DATE("{{ next_ds }}"), INTERVAL {{ params.time_interval }});
SET end_date = CURRENT_DATE();
-- Delete rows before insert updated data
DELETE FROM `{{ params.bq_project }}.business_data.arpu_consolidated_by_nl`
WHERE observation_date BETWEEN start_date AND end_date;

{% else %}

TRUNCATE TABLE `{{ params.bq_project }}.business_data.arpu_consolidated_by_nl`;

{% endif %}

INSERT INTO `{{ params.bq_project }}.business_data.arpu_consolidated_by_nl`

-- Direct
WITH direct_revenue_final AS (
SELECT
    per.observation_date,
    per.email_profile_master_id,
    per.email_consent_public_ref,
    'N/A' AS utm_source,
    per.ad_partner,
    SUM(revenue_per_open) AS revenue
FROM `{{ params.bq_project }}.generated_data.profile_email_revenue` AS per
WHERE per.ad_partner IS NOT NULL
GROUP BY ALL
),
-- Indirect
indirect_revenue_final AS (
SELECT DISTINCT
    observation_date,
    email_profile_master_id,
    newsletter,
    utm_source,
    ad_partner,
    SUM(revenue) AS revenue
FROM `{{ params.bq_project }}.generated_data.profile_indirect_revenue`
GROUP BY ALL
),
-- Optin part
optin_part_revenue AS (
    SELECT
    date AS observation_date,
    email_profile_master_id,
    newsletter,
    'N/A' AS utm_source,
    ad_partner,
    daily_revenue AS revenue
FROM `{{ params.bq_project }}.generated_data.optin_part_revenue`
)

SELECT 'direct' AS type, * from direct_revenue_final
WHERE observation_date BETWEEN start_date AND end_date
UNION ALL
SELECT 'indirect' AS type, * from indirect_revenue_final
WHERE observation_date BETWEEN start_date AND end_date
UNION ALL
SELECT 'optin part' AS type, * from optin_part_revenue
WHERE observation_date BETWEEN start_date AND end_date
;