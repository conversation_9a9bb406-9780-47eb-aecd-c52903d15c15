-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE start_date DATE DEFAULT DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL {{ params.time_interval }});
DECLARE end_date DATE DEFAULT DATE("{{ data_interval_end }}");

{% if params.is_full %}
SET start_date = DATE("{{ params.start_date }}");
SET end_date = DATE("{{ params.end_date }}");
{% endif %}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.business_data.pandora_acquisition_utm_by_status` (
    acquisition_date      DATE    NOT NULL      OPTIONS(description="Date of the acquisition event. ref: `{{ params.bq_project }}.refined_data.pandora_events#event_date`"),
    partner_name          STRING  NOT NULL      OPTIONS(description="Partner name. ref: `{{ params.bq_project }}.refined_data.pandora_events#setup.partner.name`"),
    brand_trigram         STRING  NOT NULL      OPTIONS(description="Brand trigram. ref: `{{ params.bq_project }}.refined_data.pandora_events#action.email.base.brand_trigram`"),
    status                STRING  NOT NULL      OPTIONS(description="Status, enum=['REA', 'NEW', 'DLP']. ref: `{{ params.bq_project }}.refined_data.pandora_events#response.email.status`"),
    utm_source            STRING                OPTIONS(description="UTM source sent by the partner, 'N/A' if null. ref: `{{ params.bq_project }}.refined_data.pandora_events#setup.partner.utm_info.utm_source`"),
    utm_medium            STRING                OPTIONS(description="UTM medium sent by the partner, 'N/A' if null. ref: `{{ params.bq_project }}.refined_data.pandora_events#setup.partner.utm_info.utm_medium`"),
    utm_campaign          STRING                OPTIONS(description="UTM campaign sent by the partner, 'N/A' if null. ref: `{{ params.bq_project }}.refined_data.pandora_events#setup.partner.utm_info.utm_campaign`"),
    utm_content           STRING                OPTIONS(description="UTM content sent by the partner, 'N/A' if null. ref: `{{ params.bq_project }}.refined_data.pandora_events#setup.partner.utm_info.utm_content`"),
    acquisition_volume    INT64   NOT NULL      OPTIONS(description="Volume of acquisitions, based on the event id. ref: `{{ params.bq_project }}.refined_data.pandora_events#event_id`"),
    PRIMARY KEY(acquisition_date, partner_name, brand_trigram, status, utm_source, utm_medium, utm_campaign, utm_content) NOT ENFORCED
)
PARTITION BY acquisition_date
CLUSTER BY partner_name, brand_trigram, status
OPTIONS(description="This table Pandora acquisition UTM data for different partners by status.\n\n"
                  ||"DAG: {{ dag.dag_id }}\n\n"
                  ||"Sync: Daily"
);

{% if params.is_full %}
TRUNCATE TABLE `{{ params.bq_project }}.business_data.pandora_acquisition_utm_by_status`;
{% else %}
DELETE FROM `{{ params.bq_project }}.business_data.pandora_acquisition_utm_by_status`
WHERE acquisition_date BETWEEN start_date AND end_date;
{% endif %}

INSERT INTO `{{ params.bq_project }}.business_data.pandora_acquisition_utm_by_status`
SELECT
    DATE(event_date) AS acquisition_date,
    LOWER(setup.partner.name) AS partner_name,
    brand_trigram,
    response.email.status,
    IFNULL(setup.partner.utm_info.utm_source, "N/A") AS utm_source,
    IFNULL(setup.partner.utm_info.utm_medium, "N/A") AS utm_medium,
    IFNULL(setup.partner.utm_info.utm_campaign, "N/A") AS utm_campaign,
    IFNULL(setup.partner.utm_info.utm_content, "N/A") AS utm_content,
    COUNT(DISTINCT event_id) AS acquisition_volume
FROM `{{ params.bq_project }}.refined_data.pandora_events`, UNNEST(action.email.base)
WHERE DATE(event_date) BETWEEN start_date AND end_date
GROUP BY ALL;
