-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE start_date DEFAULT DATE_TRUNC(DATE_SUB(DATE('{{ next_ds }}'), INTERVAL 3 DAY), MONTH);
DECLARE end_date DEFAULT DATE_TRUNC(DATE('{{ next_ds }}'), MONTH);

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.business_data.ga4_valuation_kpis_global` (
    visit_month         DATE     OPTIONS(description="Month of the visit"),
    is_logged           BOOLEAN  OPTIONS(description="Is the user logged in?"),
    segment             STRING   OPTIONS(description="User segment"),
    device              STRING   OPTIONS(description="Device category"),
    nb_users            INTEGER  OPTIONS(description="Number of unique users"),
    nb_session          INTEGER  OPTIONS(description="Number of sessions"),
    nb_pv               INTEGER  OPTIONS(description="Number of page views"),
    revenue             FLOAT64  OPTIONS(description="Revenue generated")
)
PARTITION BY visit_month
CLUSTER BY is_logged, segment, device
OPTIONS(
    description = "User segments global metrics based on navigation behavior and subscription status."
                ||"\n\n"
                ||"DAG: {{ dag.dag_id }}."
                ||"\n\n"
                ||"Sync: daily."
);

{% if params.is_full %}
    SET start_date = DATE("{{ params.start_date }}");
    SET end_date = DATE("{{ params.end_date }}");
    TRUNCATE TABLE `{{ params.bq_project }}.business_data.ga4_valuation_kpis_global`;
{% else %}
    DELETE FROM `{{ params.bq_project }}.business_data.ga4_valuation_kpis_global` WHERE visit_month >= start_date;
{% endif %}

INSERT INTO `{{ params.bq_project }}.business_data.ga4_valuation_kpis_global`
WITH get_anonymous_users AS (
  SELECT
    DATE_TRUNC(visit_date, MONTH) AS visit_month,
    user_pseudo_id,
    property_data.brand_trigram,
    CASE
      WHEN SUM(nb_session) = 1 THEN 1
      WHEN SUM(nb_session) BETWEEN 2 AND 3 THEN 2
      WHEN SUM(nb_session) BETWEEN 4 AND 7 THEN 3
      WHEN SUM(nb_session) >= 8 THEN 4
    END AS segment,
    device
  FROM `{{ params.bq_project }}.generated_data.ga4_profile_reactivity_by_property_daily`
  WHERE
     ARRAY_LENGTH(pmc_web_id) = 0
     AND visit_date BETWEEN start_date AND end_date
  GROUP BY ALL
  HAVING SUM(nb_session) IS NOT NULL
)
,
get_logged_users AS (
  SELECT
    DATE_TRUNC(visit_date, MONTH) AS visit_month,
    kpi.user_pseudo_id,
    kpi.property_data.brand_trigram,
    MAX(CASE
      WHEN nb_sub_nl > 0 THEN 8
      WHEN nb_sub_alerting > 0 THEN 7
      WHEN nb_sub_service > 0 THEN 6
      ELSE 5
    END) AS segment,
    device
  FROM `{{ params.bq_project }}.generated_data.ga4_profile_reactivity_by_property_daily` kpi,
  UNNEST(pmc_web_id) pmc
  LEFT JOIN `{{ params.bq_project }}.generated_data.pmc_profile_subs` pmcs ON pmc = pmcs.pmc_web_id
  WHERE kpi.visit_date BETWEEN start_date AND end_date
  GROUP BY ALL
)
,
get_all_users AS (
  SELECT visit_month, user_pseudo_id, brand_trigram, COALESCE(l.segment, a.segment) AS segment, device
  FROM get_logged_users l
  FULL JOIN get_anonymous_users a USING (visit_month, user_pseudo_id, brand_trigram, device)
)

SELECT
  DATE_TRUNC(visit_date, MONTH) AS visit_month,
  is_logged,
  CASE u.segment
      WHEN 8 THEN "Visiteur loggé avec NL"
      WHEN 7 THEN "Visiteur loggé avec Alerting"
      WHEN 6 THEN "Visiteur loggé avec service autre que NL et Alerting"
      WHEN 5 THEN "Visiteur loggé sans service"
      WHEN 4 THEN "Visiteur anonyme très engagé"
      WHEN 3 THEN "Visiteur anonyme appétent"
      WHEN 2 THEN "Visiteur anonyme"
      WHEN 1 THEN "Visiteur anonyme première fois"
  END AS segment,
  kpi.device,
  COUNT(DISTINCT kpi.user_pseudo_id) AS nb_users,
  SUM(nb_session) AS nb_session,
  SUM(nb_pv) AS nb_pv,
  SUM(kpi.revenue) AS revenue
FROM `{{ params.bq_project }}.generated_data.ga4_profile_reactivity_by_property_daily` kpi
LEFT JOIN get_all_users u
ON kpi.user_pseudo_id = u.user_pseudo_id
   AND kpi.property_data.brand_trigram = u.brand_trigram
   AND DATE_TRUNC(kpi.visit_date, MONTH) = u.visit_month
   AND kpi.device = u.device
WHERE
  kpi.visit_date BETWEEN start_date AND end_date
GROUP BY ALL
;
