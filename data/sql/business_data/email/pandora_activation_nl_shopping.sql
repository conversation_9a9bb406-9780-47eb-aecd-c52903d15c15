-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE start_date DEFAULT DATE('{{ params.start_date }}');
DECLARE end_date DEFAULT DATE('{{ params.end_date }}');

CREATE TABLE IF NOT EXISTS `{{ params.bq_project_matrix }}.business_data.pandora_activation_nl_shopping`
(
    acquisition_date              DATE      NOT NULL   OPTIONS(description="Acquisition date. ref: refined_data.pandora_events.event_date"),
    brand_trigram                 STRING               OPTIONS(description="Brand trigram corresponding to the context. ref: refined_data.email_base.brand_trigram"),
    partner                       STRING    NOT NULL   OPTIONS(description="Partner for acquisition"),
    context                       STRING    NOT NULL   OPTIONS(description="Acquisition context"),
    collect_lever                 STRING    NOT NULL   OPTIONS(description="Collect lever as enum=['popin_consentement', 'cosponsoring', 'coregistration', 'jeu_cobrandé', '\\N', 'facebook', 'gamification']"),
    status                        STRING    NOT NULL   OPTIONS(description="Email response as enum=['NEW','REA']"),
    gender                        STRING    NOT NULL   OPTIONS(description="Gender as enum=['H','F']. ref: business_data.profile_digital_360.info.birthdate"),
    age_bin                       STRING    NOT NULL   OPTIONS(description="Age bin. ref: business_data.profile_digital_360.info.birthdate"),
    esp                           STRING    NOT NULL   OPTIONS(description="Email service provider. ref: business_data.profile_digital_360.info.email"),
    activation_date               DATE                 OPTIONS(description="Activiation date if profiles are activated"),
    activation_click_date         DATE                 OPTIONS(description="First click date on the context"),
    cat_activation_delay          STRING               OPTIONS(description="Category of delay of activation based on First open date on the context"),
    cat_click_activation_delay    STRING               OPTIONS(description="Category of delay of activation based on First click date on the context"),
    is_activated                  INTEGER              OPTIONS(description="Boolean indicating if profiles have opened an nl"),
    is_click_activated            INTEGER              OPTIONS(description="Boolean indicating if profiles have clicked an nl"),
    volume                        INT64                OPTIONS(description="Volume")
)
PARTITION BY acquisition_date
CLUSTER BY brand_trigram, partner, context, collect_lever
OPTIONS(
    description="This table contains activation volume for profiles with pandora events on part context (NL Shopping).\n" ||
                "\n" ||
                "This table is used in the dashboard: Pandora - Suivi de l'activation\n" ||
                "[ATLAS PROD] https://atlas.prismadata.fr/data-product/99-pandora-suivi-de-l-activation-sans-gala \n" ||
                "[ATLAS PREPROD] https://atlas.preprod.prismadata.fr/data-product/104-pandora-suivi-de-l-activation \n" ||
                "\n" ||
                "Sync: Daily.\n" ||
                "DAG: {{ dag.dag_id }}"
);

{% if params.is_full != true %}
SET start_date  = DATE_SUB(DATE("{{ next_ds }}"), INTERVAL {{ params.time_interval }});
SET end_date  = CURRENT_DATE();
{% endif %}

DELETE FROM `{{ params.bq_project_matrix }}.business_data.pandora_activation_nl_shopping`
WHERE DATE(acquisition_date) BETWEEN start_date AND end_date;

INSERT INTO `{{ params.bq_project_matrix }}.business_data.pandora_activation_nl_shopping`
WITH pandora_acquisition_nl_shopping AS (
    SELECT
        DATE(event_date) AS acquisition_date,
        email_profile_master_id,
        setup.partner.name AS partner,
        setup.context.name AS context,
        collect_lever,
        IF(response.email.status = 'REO', 'REA', response.email.status) AS status,
        event_has_rea,
        1 AS is_collected
    FROM `{{ params.bq_project_matrix }}.refined_data.pandora_events`
    WHERE
        DATE(event_date) BETWEEN start_date AND end_date
        AND
        setup.context.name LIKE '%_part' -- is NL Shopping context
        AND
        response.email.status IN ('NEW','REA','REO')
        AND
        NOT action.email.is_cancelled
), pandora_activation_nl_shopping AS (
    SELECT
        *,
        1 AS is_activated,
        MIN(nl_shopping_reactivity.open.date) OVER (PARTITION BY email_profile_master_id, acquisition_date, partner, context, collect_lever) AS first_open_date,
        MIN(nl_shopping_reactivity.click.date) OVER (PARTITION BY email_profile_master_id, acquisition_date, partner, context, collect_lever) AS first_click_date
    FROM `{{ params.bq_project_matrix }}.generated_data.pandora_profile_nl_shopping_activation`
), pandora_acquisition_activation_nl_shopping AS (
    SELECT
        acq.email_profile_master_id,
        acq.acquisition_date,
        eb.brand_trigram AS brand_trigram,
        acq.partner,
        acq.context,
        acq.collect_lever,
        acq.status,
        CASE
            WHEN pd.info.gender = 'F' THEN 'Femme'
            WHEN pd.info.gender = 'M' THEN 'Homme'
            ELSE '(not set)'
        END AS gender,
        CASE
            WHEN IF(LENGTH(SPLIT(info.birthdate, '-')[ordinal(1)]) < 4, null, CAST(FLOOR(DATE_DIFF(CURRENT_DATE()+1,CAST(info.birthdate AS DATE),DAY)/365.25) AS INT)) BETWEEN  0 AND 17  THEN  '0-17'
            WHEN IF(LENGTH(SPLIT(info.birthdate, '-')[ordinal(1)]) < 4, null, CAST(FLOOR(DATE_DIFF(CURRENT_DATE()+1,CAST(info.birthdate AS DATE),DAY)/365.25) AS INT)) BETWEEN 18 AND 24  THEN '18-24'
            WHEN IF(LENGTH(SPLIT(info.birthdate, '-')[ordinal(1)]) < 4, null, CAST(FLOOR(DATE_DIFF(CURRENT_DATE()+1,CAST(info.birthdate AS DATE),DAY)/365.25) AS INT)) BETWEEN 25 AND 34  THEN '25-34'
            WHEN IF(LENGTH(SPLIT(info.birthdate, '-')[ordinal(1)]) < 4, null, CAST(FLOOR(DATE_DIFF(CURRENT_DATE()+1,CAST(info.birthdate AS DATE),DAY)/365.25) AS INT)) BETWEEN 35 AND 44  THEN '35-44'
            WHEN IF(LENGTH(SPLIT(info.birthdate, '-')[ordinal(1)]) < 4, null, CAST(FLOOR(DATE_DIFF(CURRENT_DATE()+1,CAST(info.birthdate AS DATE),DAY)/365.25) AS INT)) BETWEEN 45 AND 54  THEN '45-54'
            WHEN IF(LENGTH(SPLIT(info.birthdate, '-')[ordinal(1)]) < 4, null, CAST(FLOOR(DATE_DIFF(CURRENT_DATE()+1,CAST(info.birthdate AS DATE),DAY)/365.25) AS INT)) BETWEEN 55 AND 64  THEN '55-64'
            WHEN IF(LENGTH(SPLIT(info.birthdate, '-')[ordinal(1)]) < 4, null, CAST(FLOOR(DATE_DIFF(CURRENT_DATE()+1,CAST(info.birthdate AS DATE),DAY)/365.25) AS INT)) >= 65              THEN '65+'
            ELSE '(not set)'
        END AS age_bin,
        IF(pd.id.email_profile_master_id IS NOT NULL, SPLIT(pd.info.email, '@')[ordinal(2)], '(not set)') AS esp,
        IFNULL(is_collected, 0) AS is_collected,
        IFNULL(is_activated, 0) AS is_activated,
        IF(first_click_date IS NULL, 0, 1)  AS is_click_activated,
        first_click_date AS activation_click_date,
        CASE
            WHEN first_open_date IS     NULL AND first_click_date IS     NULL THEN NULL
            WHEN first_open_date IS NOT NULL AND first_click_date IS     NULL THEN first_open_date
            WHEN first_open_date IS     NULL AND first_click_date IS NOT NULL THEN first_click_date
            WHEN first_open_date <= first_click_date                          THEN first_open_date
            WHEN first_open_date <  first_click_date                          THEN first_click_date
            ELSE NULL
        END activation_date
    FROM      pandora_acquisition_nl_shopping                    AS acq
    LEFT JOIN pandora_activation_nl_shopping                     AS act USING(acquisition_date, email_profile_master_id, partner, context)
    LEFT JOIN `{{ params.bq_project_matrix }}.business_data.profile_digital_360` AS pd  ON pd.id.email_profile_master_id = acq.email_profile_master_id
    LEFT JOIN `{{ params.bq_project_matrix }}.refined_data.email_base`           AS eb  ON eb.consent_public_ref = IF(acq.context= "t2s_part", "tele_2_semaines_part", acq.context)
    WHERE acq.email_profile_master_id IS NOT NULL
)

SELECT
    acquisition_date,
    brand_trigram,
    partner,
    context,
    collect_lever,
    status,
    gender,
    age_bin,
    esp,
    activation_date,
    activation_click_date,
    CASE
	WHEN DATE_DIFF(activation_date,acquisition_date, DAY) <=   7  THEN '0-7 jours'
	WHEN DATE_DIFF(activation_date,acquisition_date, DAY) <=  15  THEN '7-15 jours'
	WHEN DATE_DIFF(activation_date,acquisition_date, DAY) <=  30  THEN '15-30 jours'
	WHEN DATE_DIFF(activation_date,acquisition_date, DAY) <=  60  THEN '30-60 jours'
	WHEN DATE_DIFF(activation_date,acquisition_date, DAY) <= 180  THEN '60-180 jours'
        ELSE "(not set)"
    END AS cat_activation_delay,
    CASE
	WHEN DATE_DIFF(activation_click_date,acquisition_date, DAY) <=    7  THEN '0-7 jours'
	WHEN DATE_DIFF(activation_click_date,acquisition_date, DAY) <=  15  THEN '7-15 jours'
	WHEN DATE_DIFF(activation_click_date,acquisition_date, DAY) <=  30  THEN '15-30 jours'
	WHEN DATE_DIFF(activation_click_date,acquisition_date, DAY) <=  60  THEN '30-60 jours'
	WHEN DATE_DIFF(activation_click_date,acquisition_date, DAY) <=  90  THEN '60-90 jours'
        ELSE "(not set)"
    END AS cat_click_activation_delay,
    is_activated,
    is_click_activated,
    COUNT(DISTINCT CONCAT(email_profile_master_id)) AS volume
FROM pandora_acquisition_activation_nl_shopping
WHERE partner IS NOT NULL
GROUP BY ALL;
