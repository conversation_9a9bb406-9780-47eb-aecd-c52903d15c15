-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE start_date DATE DEFAULT DATE_SUB(DATE("{{ data_interval_end }}"), INTERVAL {{ params.interval }});
DECLARE end_date DATE DEFAULT DATE("{{ data_interval_end }}");

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.business_data.crm_tag_history_by_universe` (
    observation_date        DATE        NOT NULL    OPTIONS(description="Observation date"),
    universe_name           STRING      NOT NULL    OPTIONS(description="Universe name"),
    state                   STRING      NOT NULL    OPTIONS(description="CRM state"),
    profile_volume          INTEGER     NOT NULL    OPTIONS(description="Volume of distinct email_profile_master_id"),
    PRIMARY KEY (observation_date, universe_name, state) NOT ENFORCED
)
PARTITION BY observation_date
CLUSTER BY universe_name, state
OPTIONS(
    description="This table contains historic aggregated data for CRM Tag at universe-level.\n\n"
              ||"DAG: {{ dag.dag_id }}.\n\n"
              ||"Sync: Daily."
);

{% if params.is_full %}
    TRUNCATE TABLE `{{ params.bq_project }}.business_data.crm_tag_history_by_universe`;
{% else %}
    DELETE FROM `{{ params.bq_project }}.business_data.crm_tag_history_by_universe`
    WHERE observation_date BETWEEN start_date AND end_date;
{% endif %}

INSERT INTO `{{ params.bq_project }}.business_data.crm_tag_history_by_universe`
SELECT
    observation_date,
    SPLIT(state, "-")[SAFE_OFFSET(1)] AS universe_name,
    SPLIT(state, "-")[SAFE_OFFSET(2)] AS state,
    COUNT(DISTINCT email_profile_master_id) AS profile_volume
FROM `{{ params.bq_project }}.generated_data.workflow_state_history`, UNNEST(workflow_states) AS state
WHERE
    SPLIT(state, "-")[SAFE_OFFSET(1)] NOT LIKE "%gala%"
    AND SPLIT(state, "-")[SAFE_OFFSET(0)] = "universe"
    {% if not params.is_full %}
    AND observation_date BETWEEN start_date AND end_date
    {% endif %}
GROUP BY ALL;
