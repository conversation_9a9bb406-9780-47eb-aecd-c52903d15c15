-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.business_data.classic_nl_metrics_by_email_subject_daily`
(
    shoot_date      DATE    NOT NULL OPTIONS(description="Shoot date at 'Europe/Paris' timezone."),
    owner_name      STRING  NOT NULL OPTIONS(description="Owner name as 'prisma' / 'cerise'. ref: {{ params.bq_project }}.store_karinto.owner#name."),
    pole_name       STRING  NOT NULL OPTIONS(description="Pole name as 'premium', 'tv',... For Cerise is 'all'. ref: {{ params.bq_project }}.store_karinto.pole#name."),
    brand_trigram   STRING  NOT NULL OPTIONS(description="Brand trigram. ref: {{ params.bq_project }}.store_karinto.brand#trigram."),
    universe_name   STRING  NOT NULL OPTIONS(description="Splio universe name. ref: {{ params.bq_project }}.store_karinto.universe#name."),
    nl_name         STRING  NOT NULL OPTIONS(description="NL object name composed of consent(optional) and theme (optional). This object represent NL name."),
    email_subject    STRING  NOT NULL OPTIONS(description="Email subject."),
    metrics STRUCT<
        nb_target           INT64 NOT NULL OPTIONS(description="Email adress to be target count"),
        nb_sent             INT64 NOT NULL OPTIONS(description="Valid email adress to be shooted count"),
        nb_delivered        INT64 NOT NULL OPTIONS(description="Reached email adress count"),
        nb_open             INT64 NOT NULL OPTIONS(description="Open count"),
        nb_opener           INT64 NOT NULL OPTIONS(description="Unique Opener count"),
        nb_click            INT64 NOT NULL OPTIONS(description="Click count"),
        nb_clicker          INT64 NOT NULL OPTIONS(description="Unique Clicker count"),
        nb_unsub            INT64 NOT NULL OPTIONS(description="Unsubscription count"),
        nb_soft_bounce      INT64 NOT NULL OPTIONS(description="Soft bounce count"),
        nb_hard_bounce      INT64 NOT NULL OPTIONS(description="hard bounce count"),
        nb_spam_complaint   INT64 NOT NULL OPTIONS(description="Spam count")
    > OPTIONS(description="Splio metrics at NL Object level.\n"||
             "These mertics are computed by Splio and extracted from their API."),
    PRIMARY KEY (shoot_date, owner_name, pole_name, brand_trigram,  universe_name, nl_name, email_subject) NOT ENFORCED
)
PARTITION BY shoot_date
CLUSTER BY owner_name, pole_name, brand_trigram, universe_name
OPTIONS(description="This table contains metrics that describes Classic NL activity by email subject. \n"||
                    "Sync: Daily \n"||
                    "DAG: businesss_data__email"
);

MERGE `{{ params.bq_project }}.business_data.classic_nl_metrics_by_email_subject_daily` AS dst
USING(
    WITH deduplicate_metrics AS (
        -- Keep one metric by shoot ref : (DATE(start_date), id.message_ref, id.campaign_ref, id.sendout_ref) for each universe and NL name
        SELECT
          DATE(DATETIME(start_date, "Europe/Paris"))    AS shoot_date,
          ebr.universe_name                             AS universe_name,
          ebr.owner_name AS owner_name,
          ebr.pole_name AS pole_name,
          ebr.brand_trigram AS brand_trigram,
          ebr.email_base_object_name AS nl_name,
          -- Campaign IDs
          id.message_ref, id.campaign_ref, id.sendout_ref,
          -- Campaign subject
          TRIM(campaign.message_subject) AS email_subject,
          stats.targets          AS nb_target,
          stats.sent             AS nb_sent,
          stats.delivered        AS nb_delivered,
          stats.opens            AS nb_open,
          stats.openers          AS nb_opener,
          stats.clicks           AS nb_click,
          stats.clickers         AS nb_clicker,
          stats.unsubscribes     AS nb_unsub,
          stats.soft_bounces     AS nb_soft_bounce,
          stats.hard_bounces     AS nb_hard_bounce,
          stats.spam_complaints  AS nb_spam_complaint
        FROM `{{ params.bq_project }}.refined_data.splio_report`             AS sr
        JOIN `{{ params.bq_project }}.refined_data.email_base_repository`    AS ebr  ON IFNULL(ebr.email_consent_public_ref, "(not set)") = IFNULL(sr.rogue_one.email_base.email_consent_public_ref, "(not set)")
                                                                            AND IFNULL(ebr.theme, "(not set)") = IFNULL(sr.rogue_one.theme, "(not set)")
                                                                            AND ebr.universe_name = sr.campaign.universe_name
        WHERE
          -- Keep only NL and NL-SHOPPING universe's
          -- NL-SHOPPING --> NL TYPE
          ebr.universe_type = "nl"
          -- Keep only campaign with ROGUE-ONE ID !
          AND CAST(IFNULL(sr.rogue_one.id, 0) AS INT64) != 0
          -- Keep only campaign NON-BAT
          AND NOT (sr.campaign.message_name LIKE "%BAT - %" OR sr.campaign.message_name LIKE "%BAT-%")
          -- Keep only non-null subject
          AND campaign.message_subject IS NOT NULL
        GROUP BY ALL
    )
    SELECT
      shoot_date,
      owner_name,
      pole_name,
      brand_trigram,
      universe_name,
      nl_name,
      email_subject,
      STRUCT(
        SUM(nb_target)          AS nb_target,
        SUM(nb_sent)            AS nb_sent,
        SUM(nb_delivered)       AS nb_delivered,
        SUM(nb_open)            AS nb_open,
        SUM(nb_opener)          AS nb_opener,
        SUM(nb_click)           AS nb_click,
        SUM(nb_clicker)         AS nb_clicker,
        SUM(nb_unsub)           AS nb_unsub,
        SUM(nb_soft_bounce)     AS nb_soft_bounce,
        SUM(nb_hard_bounce)     AS nb_hard_bounce,
        SUM(nb_spam_complaint)  AS nb_spam_complaint
      ) AS metrics
    FROM deduplicate_metrics
    GROUP BY ALL
) AS ref
ON
  dst.shoot_date = ref.shoot_date
  AND dst.owner_name = ref.owner_name
  AND dst.pole_name = ref.pole_name
  AND dst.brand_trigram = ref.brand_trigram
  AND dst.universe_name = ref.universe_name
  AND dst.nl_name = ref.nl_name
  AND TRIM(dst.email_subject) = TRIM(ref.email_subject)
WHEN MATCHED THEN
  UPDATE SET
  dst.metrics = ref.metrics
WHEN NOT MATCHED BY TARGET THEN
  INSERT(shoot_date, owner_name, pole_name, brand_trigram, universe_name, nl_name, email_subject, metrics)
  VALUES(ref.shoot_date, ref.owner_name, ref.pole_name, ref.brand_trigram, ref.universe_name, ref.nl_name, ref.email_subject, ref.metrics);

