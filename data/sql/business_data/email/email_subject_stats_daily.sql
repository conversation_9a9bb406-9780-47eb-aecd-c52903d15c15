-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE task_start_date DEFAULT DATE_SUB(DATE("{{ next_ds }}"),INTERVAL {{ params.time_interval }});
DECLARE task_end_date DEFAULT DATE("{{ next_ds }}");

-- create statement for subjects/daily
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.business_data.email_subject_stats_daily_by_subject`
(
    shoot_date        DATE   NOT NULL OPTIONS (description ="Date of the shoot"),
    brand_trigram     STRING OPTIONS (description ="Brand represented as a trigram"),
    newsletter        STRING NOT NULL OPTIONS (description ="Newsletter name"),
    subject           STRING NOT NULL OPTIONS (description ="Email subject"),
    nb_session        FLOAT64 OPTIONS (description ="Total number of sessions for the specified day and subject"),
    nb_pv             FLOAT64 OPTIONS (description ="Total number of page views for the specified day and subject"),
    nb_pv_per_session FLOAT64 OPTIONS (description ="Number of page views per session for the specified day and subject"),
    PRIMARY KEY(shoot_date, brand_trigram, newsletter, subject) NOT ENFORCED
)
    PARTITION BY shoot_date
    CLUSTER BY brand_trigram OPTIONS ( description ="Aggregated data by day and brand from the profile_email_subject_stats table.\n" ||
                                                    "Used for analyzing sessions and page views.\n" ||
                                                    "DAG: {{ dag.dag_id }}" );
-- create statement for nls/daily
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.business_data.email_subject_stats_daily_by_nl`
(
    shoot_date          DATE   NOT NULL OPTIONS (description ="Date of the shoot"),
    brand_trigram       STRING OPTIONS (description ="Brand represented as a trigram"),
    newsletter          STRING NOT NULL OPTIONS (description ="Newsletter name"),
    nb_session          FLOAT64 OPTIONS (description ="Total number of sessions for the specified day and nl"),
    nb_pv               FLOAT64 OPTIONS (description ="Total number of page views for the specified day and nl"),
    nb_pv_per_session   FLOAT64 OPTIONS (description ="Number of page views per session for the specified day and nl"),
    prv_nb_session      FLOAT64 OPTIONS (description ="Previous month number of sessions for the specified day and nl"),
    prv_nb_pv           FLOAT64 OPTIONS (description ="Previous month number of page views for the specified day and nl"),
    prv_pv_per_session  FLOAT64 OPTIONS (description ="Previous month number of page views per session for the specified day and nl"),
    evol_nb_session     FLOAT64 OPTIONS (description ="Evolution of number of sessions compared to the previous month for the specified day and nl (percentage)"),
    evol_nb_pv          FLOAT64 OPTIONS (description ="Evolution of number of page views compared to the previous month for the specified day and nl (percentage)"),
    evol_pv_per_session FLOAT64 OPTIONS (description ="Evolution of number of page views per session compared to the previous month for the specified day and nl (percentage)"),
    PRIMARY KEY (shoot_date,brand_trigram,newsletter) NOT ENFORCED
)
    PARTITION BY shoot_date
    CLUSTER BY brand_trigram, newsletter OPTIONS ( description ="Aggregated data by day, brand, and newsletter from the profile_email_subject_stats table.\n" ||
                                                                "Used for analyzing sessions and page views.\n" ||
                                                                "DAG: {{ dag.dag_id }}" );

-- create statement for brand/daily
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.business_data.email_subject_stats_daily_by_brand`
(
    shoot_date          DATE NOT NULL OPTIONS (description ="Date of the shoot"),
    brand_trigram       STRING OPTIONS (description ="Brand represented as a trigram"),
    nb_session          FLOAT64 OPTIONS (description ="Total number of sessions for the specified day and brand"),
    nb_pv               FLOAT64 OPTIONS (description ="Total number of page views for the specified day and brand"),
    nb_pv_per_session   FLOAT64 OPTIONS (description ="Number of page views per session for the specified day and brand"),
    prv_nb_session      FLOAT64 OPTIONS (description ="Previous month number of sessions for the specified day and brand"),
    prv_nb_pv           FLOAT64 OPTIONS (description ="Previous month number of page views for the specified day and brand"),
    prv_pv_per_session  FLOAT64 OPTIONS (description ="Previous month number of page views per session for the specified day and brand"),
    evol_nb_session     FLOAT64 OPTIONS (description ="Evolution of number of sessions compared to the previous month for the specified day and brand (percentage)"),
    evol_nb_pv          FLOAT64 OPTIONS (description ="Evolution of number of page views compared to the previous month for the specified day and brand (percentage)"),
    evol_pv_per_session FLOAT64 OPTIONS (description ="Evolution of number of page views per session compared to the previous month for the specified day and brand (percentage)"),
    PRIMARY KEY (shoot_date, brand_trigram) NOT ENFORCED
)
    PARTITION BY shoot_date
    CLUSTER BY brand_trigram OPTIONS ( description ="Aggregated data by day and brand from the profile_email_subject_stats table.\n" ||
                                                    "Used for analyzing sessions and page views.\n" ||
                                                    "DAG: {{ dag.dag_id }}" );
{% if params.partial == true %}
    SET task_start_date = DATE("{{ params.start_date }}");
    SET task_end_date = DATE("{{ params.end_date }}");
    -- Delete rows before insert updated data

    DELETE FROM `{{ params.bq_project }}.business_data.email_subject_stats_daily_by_brand`
    WHERE shoot_date BETWEEN task_start_date AND task_end_date;

    DELETE FROM `{{ params.bq_project }}.business_data.email_subject_stats_daily_by_nl`
    WHERE shoot_date BETWEEN task_start_date AND task_end_date;

    DELETE FROM `{{ params.bq_project }}.business_data.email_subject_stats_daily_by_subject`
    WHERE shoot_date BETWEEN task_start_date AND task_end_date;
{% endif %}
-- merge statement for subjects/daily
MERGE INTO
    `{{ params.bq_project }}.business_data.email_subject_stats_daily_by_subject` AS dest
    USING
        (WITH get_daily_subject_perf AS (
            -- perf per day and subject (used in merge 1)
            SELECT
                shoot_date,
                brand_trigram,
                newsletter,
                subject,
                SUM(nb_session) AS nb_session,
                SUM(nb_pv)      AS nb_pv
            FROM `{{ params.bq_project }}.generated_data.profile_email_subject_stats`
            WHERE shoot_date BETWEEN task_start_date
                      AND task_end_date
            GROUP BY 1, 2, 3, 4)
        SELECT
            shoot_date,
            brand_trigram,
            newsletter,
            subject,
            nb_session,
            nb_pv,
            SAFE_DIVIDE(nb_pv,nb_session) AS nb_pv_per_session
        FROM get_daily_subject_perf) AS src
    ON dest.shoot_date = src.shoot_date
        AND dest.brand_trigram = src.brand_trigram
        AND dest.newsletter = src.newsletter
        AND dest.subject = src.subject
    WHEN MATCHED THEN UPDATE SET dest.nb_session = src.nb_session, dest.nb_pv = src.nb_pv, dest.nb_pv_per_session = src.nb_pv_per_session
    WHEN NOT MATCHED THEN INSERT (shoot_date,
                                  brand_trigram,
                                  newsletter,
                                  subject,
                                  nb_session,
                                  nb_pv,
                                  nb_pv_per_session)
        VALUES
            (src.shoot_date,
             src.brand_trigram,
             src.newsletter,
             src.subject,
             src.nb_session,
             src.nb_pv,
             src.nb_pv_per_session);
-- merge statement for nls/daily
MERGE INTO
    `{{ params.bq_project }}.business_data.email_subject_stats_daily_by_nl` AS dest
    USING
        (WITH get_daily_nl_perf    AS (
            -- perf per day and nl (used in merge 2)
            SELECT
                shoot_date,
                brand_trigram,
                newsletter,
                SUM(nb_session) AS nb_session,
                SUM(nb_pv)      AS nb_pv
            FROM `{{ params.bq_project }}.generated_data.profile_email_subject_stats`
            WHERE shoot_date BETWEEN task_start_date
                      AND task_end_date
            GROUP BY 1, 2, 3),
              get_daily_nl_compare AS (
                  -- perf per day and nl completed with previous month data (used in merge 2)
                  SELECT
                      COALESCE(src.shoot_date,DATE_ADD(prev.shoot_date,INTERVAL 30 DAY))              AS shoot_date,
                      COALESCE(src.brand_trigram,prev.brand_trigram)                                  AS brand_trigram,
                      COALESCE(src.newsletter,prev.newsletter)                                        AS newsletter,
                      COALESCE(src.nb_session,0)                                                      AS nb_session,
                      COALESCE(src.nb_pv,0)                                                           AS nb_pv,
                      COALESCE(prev.nb_session,0)                                                     AS prv_nb_session,
                      COALESCE(prev.nb_pv,0)                                                          AS prv_nb_pv,
                      COALESCE(SAFE_DIVIDE(src.nb_pv,src.nb_session),0)                               AS nb_pv_per_session,
                      COALESCE(SAFE_DIVIDE(prev.nb_pv,prev.nb_session),0)                             AS prv_pv_per_session,
                      COALESCE(SAFE_DIVIDE(src.nb_session - prev.nb_session,prev.nb_session) * 100,0) AS evol_nb_session,
                      COALESCE(SAFE_DIVIDE(src.nb_pv - prev.nb_pv,prev.nb_pv) * 100,0)                AS evol_nb_pv
                  FROM get_daily_nl_perf AS src
                  FULL OUTER JOIN
                  get_daily_nl_perf AS prev
                  ON src.brand_trigram = prev.brand_trigram
                      AND src.newsletter = prev.newsletter
                      AND (src.shoot_date = DATE_ADD(prev.shoot_date,INTERVAL 30 DAY) OR prev.shoot_date = DATE_SUB(src.shoot_date,INTERVAL 30 DAY))
                  )
        SELECT
            shoot_date,
            brand_trigram,
            newsletter,
            nb_session                                                                  AS nb_session,
            nb_pv                                                                       AS nb_pv,
            prv_nb_session,
            prv_nb_pv,
            nb_pv_per_session,
            prv_pv_per_session,
            evol_nb_session,
            evol_nb_pv,
            COALESCE(SAFE_DIVIDE(nb_pv_per_session - prv_pv_per_session,prv_pv_per_session),0)       AS evol_pv_per_session,
        FROM get_daily_nl_compare
        WHERE shoot_date <= task_end_date ) AS ref
    ON dest.shoot_date = ref.shoot_date
        AND dest.brand_trigram = ref.brand_trigram
        AND dest.newsletter = ref.newsletter
    WHEN MATCHED THEN UPDATE SET
        dest.nb_session = ref.nb_session, dest.nb_pv = ref.nb_pv, dest.nb_pv_per_session = ref.nb_pv_per_session,
        dest.prv_nb_session = ref.prv_nb_session, dest.prv_nb_pv = ref.prv_nb_pv, dest.prv_pv_per_session = ref.prv_pv_per_session,
        dest.evol_nb_session = ref.evol_nb_session, dest.evol_nb_pv = ref.evol_nb_pv, dest.evol_pv_per_session = ref.evol_pv_per_session
    WHEN NOT MATCHED
        THEN
        INSERT
            (shoot_date,
             brand_trigram,
             newsletter,
             nb_session,
             nb_pv,
             nb_pv_per_session,
             prv_nb_session,
             prv_nb_pv,
             prv_pv_per_session,
             evol_nb_session,
             evol_nb_pv,
             evol_pv_per_session)
            VALUES
                (ref.shoot_date,
                 ref.brand_trigram,
                 ref.newsletter,
                 ref.nb_session,
                 ref.nb_pv,
                 ref.nb_pv_per_session,
                 ref.prv_nb_session,
                 ref.prv_nb_pv,
                 ref.prv_pv_per_session,
                 ref.evol_nb_session,
                 ref.evol_nb_pv,
                 ref.evol_pv_per_session);
-- merge statement for brand_trigram/daily
MERGE INTO
    `{{ params.bq_project }}.business_data.email_subject_stats_daily_by_brand` AS dest
    USING
        (WITH get_daily_brand_perf    AS (
            -- daily perf per day and brand
            SELECT
                shoot_date,
                brand_trigram,
                SUM(nb_session) AS nb_session,
                SUM(nb_pv)      AS nb_pv
            FROM`{{ params.bq_project }}.generated_data.profile_email_subject_stats`
            WHERE shoot_date BETWEEN task_start_date
                      AND task_end_date
            GROUP BY 1, 2),
              get_daily_brand_compare AS (
                  -- daily perf per day and brand completed with previous month data
                  SELECT
                      COALESCE(src.shoot_date,DATE_ADD(prev.shoot_date,INTERVAL 30 DAY))              AS shoot_date,
                      COALESCE(src.brand_trigram,prev.brand_trigram)                                  AS brand_trigram,
                      COALESCE(src.nb_session,0)                                                      AS nb_session,
                      COALESCE(src.nb_pv,0)                                                           AS nb_pv,
                      COALESCE(prev.nb_session,0)                                                     AS prv_nb_session,
                      COALESCE(prev.nb_pv,0)                                                          AS prv_nb_pv,
                      COALESCE(SAFE_DIVIDE(src.nb_pv,src.nb_session),0)                               AS nb_pv_per_session,
                      COALESCE(SAFE_DIVIDE(prev.nb_pv,prev.nb_session),0)                             AS prv_pv_per_session,
                      COALESCE(SAFE_DIVIDE(src.nb_session - prev.nb_session,prev.nb_session),0)       AS evol_nb_session,
                      COALESCE(SAFE_DIVIDE(src.nb_pv - prev.nb_pv,prev.nb_pv),0)                      AS evol_nb_pv
                  FROM get_daily_brand_perf AS src
                  FULL OUTER JOIN
                  get_daily_brand_perf AS prev
                  ON src.brand_trigram = prev.brand_trigram
                      AND (src.shoot_date = DATE_ADD(prev.shoot_date,INTERVAL 30 DAY) OR prev.shoot_date = DATE_SUB(src.shoot_date,INTERVAL 30 DAY)))
        SELECT
            shoot_date,
            brand_trigram,
            nb_session                                                                  AS nb_session,
            nb_pv                                                                       AS nb_pv,
            prv_nb_session,
            prv_nb_pv,
            nb_pv_per_session,
            prv_pv_per_session,
            evol_nb_session,
            evol_nb_pv,
            COALESCE(SAFE_DIVIDE(nb_pv_per_session - prv_pv_per_session,prv_pv_per_session),0)       AS evol_pv_per_session,
        FROM get_daily_brand_compare
        WHERE shoot_date <= task_end_date ) AS ref
    ON dest.shoot_date = ref.shoot_date
        AND dest.brand_trigram = ref.brand_trigram
    WHEN MATCHED THEN UPDATE SET dest.nb_session = ref.nb_session, dest.nb_pv = ref.nb_pv,
        dest.nb_pv_per_session = ref.nb_pv_per_session, dest.prv_nb_session = ref.prv_nb_session, dest.prv_nb_pv = ref.prv_nb_pv,
        dest.prv_pv_per_session = ref.prv_pv_per_session, dest.evol_nb_session = ref.evol_nb_session, dest.evol_nb_pv = ref.evol_nb_pv,
        dest.evol_pv_per_session = ref.evol_pv_per_session
    WHEN NOT MATCHED THEN INSERT
        (shoot_date,
         brand_trigram,
         nb_session,
         nb_pv,
         nb_pv_per_session,
         prv_nb_session,
         prv_nb_pv,
         prv_pv_per_session,
         evol_nb_session,
         evol_nb_pv,
         evol_pv_per_session)
        VALUES
            (ref.shoot_date,
             ref.brand_trigram,
             ref.nb_session,
             ref.nb_pv,
             ref.nb_pv_per_session,
             ref.prv_nb_session,
             ref.prv_nb_pv,
             ref.prv_pv_per_session,
             ref.evol_nb_session,
             ref.evol_nb_pv,
             ref.evol_pv_per_session);