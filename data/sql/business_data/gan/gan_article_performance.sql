-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE start_date DEFAULT DATE("{{ params.start_date }}");
DECLARE end_date DEFAULT DATE("{{ params.end_date }}");
DECLARE ga4_date DEFAULT DATE("2024-06-03");


{% if params.is_full != true %}
    SET start_date = DATE_SUB(DATE("{{ next_ds }}"),INTERVAL {{ params.time_interval }});
    SET end_date = CURRENT_DATE();

    DELETE FROM `{{ params.bq_project_matrix }}.business_data.gan_article_performance`
    WHERE visit_date BETWEEN start_date AND end_date;
{% endif %}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project_matrix }}.business_data.gan_article_performance`
(
    visit_date     DATE NOT NULL OPTIONS(description ="Visit date as %Y-%m-%d"),
    owner_name     STRING OPTIONS(description ="Owner name as enum=[''prisma'',''cerise'']"),
    brand_name     STRING OPTIONS(description ="Brand name"),
    brand_trigram  STRING OPTIONS(description ="Brand trigram"),
    source         STRING OPTIONS(description ="UTM Source"),
    medium         STRING OPTIONS(description ="UTM Medium"),
    channel        STRING OPTIONS(description ="Channel computed on source and medium as categorization"),
    site           STRING OPTIONS(description ="The site corresponding to the brand, example : gentside gaming for gentside"),
    article_title  STRING OPTIONS(description ="Article title"),
    canonical_url  STRING OPTIONS(description ="The canonical url of the page ."),
    hostname       STRING OPTIONS(description ="The hostname of the page"),
    article_format STRING OPTIONS(description ="Article format : slideshow, video, recipe..etc."),
    session        INT64  OPTIONS(description ="Number of sessions"),
    page_view      INT64  OPTIONS(description ="Number of page views")
)
    PARTITION BY visit_date
    CLUSTER BY owner_name, brand_trigram, channel
    OPTIONS
        (
        description ="This table contains business data about GAN navigation performance for article as session entrance.\n" ||
                     "It''s used in the dashboard Top article.\n" ||
                     "- Atlas prod: https://atlas.prismadata.fr/data-product/58-top-article\n" ||
                     "- Atlas preprod: https://atlas.preprod.prismadata.fr/data-product/127-top-article\n" ||
                     "\n" ||
                     "DAG: {{ dag.dag_id }}\n" ||
                     "Sync: Daily.\n"
        );

MERGE `{{ params.bq_project_matrix }}.business_data.gan_article_performance` AS dst
    USING (
        -- Extract aggregated stats from refined_data
        WITH get_owner_brand AS (
            -- Get relationship between owner and brand .
            SELECT
                owner_name,
                brand_trigram,
                brand_name
            FROM `{{ params.bq_project_matrix }}.refined_data.email_base`
            GROUP BY ALL),
        gan_article AS (
            -- Get session with hits on 'PAGE'
            SELECT
                visit_date                                                                   AS visit_date,
                session_id                                                                   AS session_id,
                property.owner_name                                                          AS owner_name,
                property.brand_trigram                                                       AS brand_trigram,
                visit.tracking.source                                                        AS source,
                visit.tracking.medium                                                        AS medium,
                visit.tracking.channel                                                       AS channel,
                property.website                                                             AS site,
                hits.number                                                                  AS hit_number,
                hits.page.canonical_url                                                      AS canonical_url,
                hits.page.hostname                                                           AS hostname,
                -- get rank hit per hit with type 'PAGE' in session
                ROW_NUMBER() OVER (PARTITION BY visit_date, session_id ORDER BY hits.number) AS hit_rank,
                hits.page.article_id
            FROM `{{ params.bq_project_matrix }}.refined_data.gan_navigation_all_visits_*` AS g,
                 UNNEST(hits) AS hits
            WHERE _TABLE_SUFFIX BETWEEN REGEXP_REPLACE(CAST(DATE_TRUNC(start_date,MONTH) AS STRING),'-','')
                AND
                REGEXP_REPLACE(CAST(DATE_TRUNC(end_date, MONTH) AS STRING), '-', '')
                AND DATE(visit_date) BETWEEN start_date AND end_date
                AND DATE(visit_date) < ga4_date
                AND hits.type = 'PAGE'),
        article_starts_session AS (SELECT
                visit_date,
                session_id,
                owner_name,
                brand_trigram,
                source,
                medium,
                channel,
                site,
                article_id,
                canonical_url,
                hostname
            FROM gan_article
            WHERE hit_rank = 1 AND article_id IS NOT NULL),
        article_performance AS (SELECT
                s.visit_date,
                s.session_id,
                s.owner_name,
                s.brand_trigram,
                s.source,
                s.medium,
                s.channel,
                s.site,
                s.article_id AS first_article,
                a.hit_rank,
                a.article_id,
                s.canonical_url,
                s.hostname
            FROM article_starts_session AS s
            LEFT JOIN gan_article AS a USING (visit_date, session_id)),
        get_gan_data AS (
            SELECT DISTINCT
                a.visit_date,
                eb.owner_name AS owner_name,
                eb.brand_name AS brand_name,
                a.brand_trigram,
                a.source,
                a.medium,
                a.channel,
                a.site,
                art.content.title                           AS article_title,
                a.canonical_url,
                a.hostname,
                INITCAP(art.format)                         as article_format,
                COUNT(DISTINCT session_id)                  AS session,
                COUNT(DISTINCT CONCAT(session_id,hit_rank)) AS page_view
            FROM article_performance AS a
                -- Get brand names
            LEFT JOIN get_owner_brand AS eb ON a.brand_trigram = eb.brand_trigram
                -- Get article names
            JOIN `{{ params.bq_project_mirror }}.refined_data.article` AS art ON a.first_article = art.article_id
            GROUP BY ALL ),
        get_ga4_web_data AS (
             SELECT
                e.visit_date AS visit_date,
                eb.owner_name AS owner_name,
                eb.brand_name AS brand_name,
                e.property_data.brand_trigram AS brand_trigram,
                sessions.tracking_info.`source` AS source,
                sessions.tracking_info.medium AS medium,
                sessions.tracking_info.custom_channel_group AS channel,
                CASE WHEN property_data.section != 'ALL'
                    THEN CONCAT(INITCAP(eb.brand_name),' ',INITCAP(property_data.section))
                    ELSE INITCAP(eb.brand_name)
                    END
                 AS site,
                COALESCE(abi.content.title, abu.content.title) AS article_title,
                pages.global_info.page_canonical_path AS canonical_url,
                pages.global_info.page_hostname AS hostname,
                INITCAP(COALESCE(abi.format, abu.format)) AS article_format,
                COUNT(DISTINCT sessions.session_id) AS session,
                COUNTIF(events.event_name = "page_view") AS page_view
            FROM `{{ params.bq_project_ga4 }}.refined_data.ga_events_WEB_*` AS e
            LEFT JOIN UNNEST(e.session_data)        AS sessions
            LEFT JOIN UNNEST(sessions.page_data)    AS pages
            LEFT JOIN UNNEST(pages.event_info)      AS events
            LEFT JOIN get_owner_brand AS eb ON e.property_data.brand_trigram = eb.brand_trigram
            LEFT JOIN `{{ params.bq_project_mirror }}.refined_data.article` AS abi ON pages.content_info.content_uuid = abi.article_id
            LEFT JOIN `{{ params.bq_project_mirror }}.refined_data.article` AS abu ON REGEXP_REPLACE(pages.global_info.page_canonical_path, r'^https://', '') = REGEXP_REPLACE(abu.content.url.main_public, r'^https://', '')
            WHERE DATE(visit_date) BETWEEN start_date AND end_date
            AND DATE(visit_date) >= ga4_date
            AND events.event_name = "page_view"
            AND _TABLE_SUFFIX NOT LIKE "%TEMPLATE%"
            AND _TABLE_SUFFIX NOT LIKE "%_v0%"
            AND COALESCE(abi.content.title, abu.content.title) IS NOT NULL
            GROUP BY ALL),
        get_ga4_app_data AS (
            SELECT
                e.visit_date AS visit_date,
                eb.owner_name AS owner_name,
                eb.brand_name AS brand_name,
                e.property_data.brand_trigram AS brand_trigram,
                sessions.tracking_info.`source` AS source,
                sessions.tracking_info.medium AS medium,
                sessions.tracking_info.custom_channel_group AS channel,
                CASE WHEN property_data.section != 'ALL'
                    THEN CONCAT(INITCAP(eb.brand_name),' ',INITCAP(property_data.section))
                    ELSE INITCAP(eb.brand_name)
                    END
                 AS site,
                -- @todo : use content_uuid to join with article refined table once available
                screens.content_info.content_title AS article_title,
                '(not set)' AS canonical_url,
                '(not set)' AS hostname,
                '(not set)' AS article_format,
                COUNT(DISTINCT sessions.session_id) AS session,
                COUNTIF(events.event_name = "screen_view") AS page_view
            FROM `{{ params.bq_project_ga4 }}.refined_data.ga_events_APP_*` AS e
            LEFT JOIN UNNEST(e.session_data) AS sessions
            LEFT JOIN UNNEST(sessions.screen_data) AS screens
            LEFT JOIN UNNEST(screens.event_info) AS events
            LEFT JOIN `{{ params.bq_project_matrix }}.refined_data.email_base` AS eb ON e.property_data.brand_trigram = eb.brand_trigram
            WHERE DATE(visit_date) BETWEEN start_date AND end_date
            AND DATE(visit_date) >= ga4_date
            AND events.event_name = "screen_view"
            AND _TABLE_SUFFIX NOT LIKE "%TEMPLATE%"
            AND _TABLE_SUFFIX NOT LIKE "%_v0%"
            GROUP BY ALL )

        SELECT * FROM get_gan_data
        UNION ALL
        SELECT * FROM get_ga4_web_data
        UNION ALL
        SELECT * FROM get_ga4_app_data
    ) AS ref

    ON ref.visit_date = dst.visit_date
        AND ref.owner_name = dst.owner_name
        AND ref.brand_name = dst.brand_name
        AND ref.brand_trigram = dst.brand_trigram
        AND ref.source = dst.source
        AND ref.medium = dst.medium
        AND ref.channel = dst.channel
        AND ref.site = dst.site
        AND ref.article_title = dst.article_title
        AND ref.canonical_url = dst.canonical_url
        AND ref.hostname = dst.hostname
        AND ref.article_format = dst.article_format
    WHEN MATCHED THEN
        UPDATE SET dst.session = ref.session,
            dst.page_view = ref.page_view
    WHEN NOT MATCHED THEN
        INSERT (
                visit_date,
                owner_name,
                brand_name,
                brand_trigram,
                source,
                medium,
                channel,
                site,
                article_title,
                canonical_url,
                hostname,
                article_format,
                session,
                page_view
            )
            VALUES
                (
                    ref.visit_date,
                    ref.owner_name,
                    ref.brand_name,
                    ref.brand_trigram,
                    ref.source,
                    ref.medium,
                    ref.channel,
                    ref.site,
                    ref.article_title,
                    ref.canonical_url,
                    ref.hostname,
                    ref.article_format,
                    ref.session,
                    ref.page_view);
