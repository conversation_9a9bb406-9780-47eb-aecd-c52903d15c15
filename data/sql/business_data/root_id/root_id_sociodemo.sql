-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.business_data.root_id_sociodemo`
(
  source            STRING   NOT NULL   OPTIONS(description="Domains in which the profile has a profile master ID, separated by '|'"),
  zipcode           INT64               OPTIONS(description="Profile zipcode"),
  gender            STRING              OPTIONS(description="Profile gender (F, M)"),
  age               INT64               OPTIONS(description="Profile age, calculating using the birthdate"),
  profile_number    INT64               OPTIONS(description="Number of distinct Root IDs profiles"),
  PRIMARY KEY (source, zipcode, gender, age) NOT ENFORCED
)
CLUSTER BY source
OPTIONS(description="This table contains Root ID profiles socio-demographic info\n"
                  ||"It is calculated using `refined_data.profile_email` and `refined_data.profile_pmc` tables\n\n"
                  ||"DAG: {{ dag.dag_id }}\n\n"
                  ||"Sync: Daily");

{% set counter = namespace() -%}
{%- set counter.age = 0 -%}
{%- set counter.gender = 0 -%}
{%- set counter.zipcode = 0 -%}
{%- for config in params.config -%}
    {%- for domain_name, domain_config in config.items() %}
        {%- if domain_config["is_active"] and domain_config["socio_demo"]["is_available"] -%}
            {%- if domain_config["socio_demo"]["age"]["is_available"] -%}
                {% set counter.age = counter.age + 1 %}
            {%- endif -%}
            {%- if domain_config["socio_demo"]["gender"]["is_available"] -%}
                {% set counter.gender = counter.gender + 1 %}
            {%- endif -%}           
            {%- if domain_config["socio_demo"]["zipcode"]["is_available"] -%}
                {% set counter.zipcode = counter.zipcode + 1 %}
            {%- endif -%}                
        {%- endif -%}
    {%- endfor -%}
{%- endfor -%}

TRUNCATE TABLE `{{ params.bq_project }}.business_data.root_id_sociodemo`;

INSERT INTO `{{ params.bq_project }}.business_data.root_id_sociodemo`
SELECT
    domains AS source,
    CAST(COALESCE(
        {%- set counter.processed_zipcode = 0 %}
        {%- for config in params.config -%}
            {%- for domain_name, domain_config in config.items() -%}
                {%- if domain_config["is_active"] and domain_config["socio_demo"]["zipcode"]["is_available"] -%}
                    {%- set counter.processed_zipcode = counter.processed_zipcode + 1 -%}
                    {%- set zipcode_column = domain_config["socio_demo"]["zipcode"]["column_name"] -%}
                    {{ domain_name }}.{{ zipcode_column }}{%- if counter.processed_zipcode < counter.zipcode -%}, {% endif -%}
                {% endif %}
            {%- endfor %}
        {%- endfor -%}
    ) AS INT64) AS zipcode,
    COALESCE(
        {%- set counter.processed_gender = 0 -%}
        {% for config in params.config %}
            {%- for domain_name, domain_config in config.items() %}
                {%- if domain_config["is_active"] and domain_config["socio_demo"]["gender"]["is_available"] -%}
                    {%- set counter.processed_gender = counter.processed_gender + 1 -%}
                    {%- set gender_column = domain_config["socio_demo"]["gender"]["column_name"] -%}
                    {{ domain_name }}.{{ gender_column }}{% if counter.processed_gender < counter.gender -%}, {% endif -%}
                {% endif %}
            {%- endfor %}
        {%- endfor -%}
    ) AS gender,
    `{{ params.bq_project }}.refined_data.CalcAge`(COALESCE(
        {%- set counter.processed_age = 0 -%}
        {%- for config in params.config -%}
            {%- for domain_name, domain_config in config.items() -%}
                {%- if domain_config["is_active"] and domain_config["socio_demo"]["age"]["is_available"] -%}
                    {%- set counter.processed_age = counter.processed_age + 1 -%}
                    {%- set age_column = domain_config["socio_demo"]["age"]["column_name"] -%}
                    DATE({{ domain_name }}.{{ age_column }}){% if counter.processed_age < counter.age -%}, {% endif -%}
                {% endif %}
            {%- endfor %}
        {%- endfor -%}
    )) AS age,
  COUNT(root_id) AS profile_number
FROM `{{ params.bq_project }}.refined_data.pivot_profile_root_id` AS pri
{% for config in params.config %}
    {%- for domain_name, domain_config in config.items() %}
        {%- if domain_config["is_active"] and domain_config["socio_demo"]["is_available"] %}
            {%- set profile_master_id = domain_config["profile_master_id"]["name"] -%}
            {%- set profile_table_schema = domain_config["profile_table_schema"] -%}
            LEFT JOIN `{{ profile_table_schema }}` AS {{ domain_name }} ON pri.{{ domain_name}}_profile_master_id = {{ domain_name }}.{{ profile_master_id }}
        {% endif -%}
    {%- endfor -%}
{%- endfor -%}
GROUP BY ALL;
