-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DROP TABLE IF EXISTS `{{ params.bq_project }}.business_data.follow_star_kpi_brand_monthly`;
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.business_data.follow_star_kpi_brand_monthly`(
    start_month                 DATE       NOT NULL     OPTIONS(description="start day of month. example: '2021-01-01'"),
    brand_trigram               STRING                  OPTIONS(description="brand trigram"),
    kpi                         STRUCT<
        subscriber_number           INTEGER                 OPTIONS(description="number of subscribers on start month per brand"),
        new_sub_number              INTEGER                 OPTIONS(description="number of new subscription on the month per brand"),
        new_unsub_number            INTEGER                 OPTIONS(description="number of new unsubscription on the month per brand"),
        alert_number                INTEGER                 OPTIONS(description="number of alert on the month per brand"),
        delivered                   INTEGER                 OPTIONS(description="number of delivered email on the month per brand"),
        opens                       INTEGER                 OPTIONS(description="number of opened email on the month per brand"),
        openers                     INTEGER                 OPTIONS(description="number of unique openers on the month per brand"),
        clicks                      INTEGER                 OPTIONS(description="number of clicked email on the month per brand"),
        clickers                    INTEGER                 OPTIONS(description="number of unique clickers on the month per brand"),
            evolution               STRUCT<
                subscriber_number        FLOAT64                OPTIONS(description="subscriber number evolution between previous month and current month per brand"),
                new_sub_number          FLOAT64                 OPTIONS(description="new subscription number evolution between previous month and current month per brand"),
                new_unsub_number        FLOAT64                 OPTIONS(description="new unsubscription evolution between previous month and current month per brand"),
                alert_number            FLOAT64                 OPTIONS(description="alert number evolution between previous month and current month per brand"),
                delivered              FLOAT64                  OPTIONS(description="number of delivered email evolution between previous month and current month per brand"),
                opens                   FLOAT64                 OPTIONS(description="number of opened email evolution between previous month and current month per brand"),
                openers                 FLOAT64                 OPTIONS(description="number of unique openers evolution between previous month and current month per brand"),
                clicks                  FLOAT64                 OPTIONS(description="number of clicked email evolution between previous month and current month per brand"),
                clickers                FLOAT64                 OPTIONS(description="number of unique clickers evolution between previous month and current month per brand")
            >                                                   OPTIONS(description="list kpis evolution between current vs previous month")
        >                                                    OPTIONS(description="list all kpis at brand scale"),
)
PARTITION BY start_month
OPTIONS(description="List follow stars kpis by brand."||
                    "Main kpis are:" ||
                    "* number of subscribers on start month."||
                    "* number of new subscription on current month."||
                    "* number of new unsubscription on current month."||
                    "* number of generated alerts on current month."||
                    "* evolution of stats by brand."||
                    "DAG: {{ dag.dag_id }}."||
                    "Sync: Monthly at 00:00AM UTC+2");

INSERT INTO `{{ params.bq_project }}.business_data.follow_star_kpi_brand_monthly`
-- WITH sub-queries :
-- * brand_kpis: aggregate kpis from follow_star_kpi_monthly by brand
-- * brand_kpi_evolution: calculate brand kpi evolution over month (current month vs previous month)

WITH month_couple AS(
    -- generate first day of each month
    -- min date = '2017-06-01'
    SELECT
        start_month,
        LAST_DAY(start_month, MONTH) AS end_month
    FROM UNNEST(GENERATE_DATE_ARRAY(CAST('2017-06-01' AS DATE), CURRENT_DATE(), INTERVAL 1 MONTH)) AS start_month
), count_profiles AS (
    SELECT
        start_month,
        brand_trigram,
        --@to be revised --> cf. Gala's Follow Star process
        COUNTIF(DATE(bcp.first_create_date) < mc.start_month AND (bcp.active_bookmarks > 0 OR (bcp.active_bookmarks = 0 AND DATE(bcp.last_update_date) >= mc.start_month))) AS subscriber_number,
        COUNTIF(DATE(bcp.first_create_date) BETWEEN DATE_SUB(mc.start_month, INTERVAL 1 MONTH) AND LAST_DAY(DATE_SUB(mc.start_month, INTERVAL 1 MONTH))) AS new_sub_number,
        COUNTIF(bcp.active_bookmarks = 0 AND DATE(bcp.last_update_date) BETWEEN DATE_SUB(mc.start_month, INTERVAL 1 MONTH) AND LAST_DAY(DATE_SUB(mc.start_month, INTERVAL 1 MONTH))) AS new_unsub_number,
    FROM `{{ params.bq_project }}.generated_data.active_bookmark_content_by_profile` AS bcp, month_couple AS mc
    WHERE bookmark_type = 'follow:people'
    GROUP BY ALL
), star_kpis AS (
    SELECT
        start_month,
        brand_trigram,
        SUM(IFNULL(person.kpi.alert_number, 0)) AS alert_number,
        SUM(IFNULL(person.kpi.delivered, 0))    AS delivered,
        SUM(IFNULL(person.kpi.opens, 0))        AS opens,
        SUM(IFNULL(person.kpi.openers, 0))      AS openers,
        SUM(IFNULL(person.kpi.clicks, 0))       AS clicks,
        SUM(IFNULL(person.kpi.clickers, 0))     AS clickers
    FROM `{{ params.bq_project }}.business_data.follow_star_kpi_monthly`
    GROUP BY ALL
), brand_kpis AS (
    SELECT
        start_month,
        brand_trigram,
        subscriber_number,
        new_sub_number,
        new_unsub_number,
        alert_number,
        delivered,
        opens,
        openers,
        clicks,
        clickers
    FROM star_kpis
    JOIN count_profiles USING(start_month, brand_trigram)
), brand_kpi_evolution AS (
    SELECT
        curr.start_month,
        curr.brand_trigram,
        curr.subscriber_number,
        curr.new_sub_number,
        curr.new_unsub_number,
        curr.alert_number,
        curr.delivered,
        curr.opens,
        curr.openers,
        curr.clicks,
        curr.clickers,
        IFNULL(ROUND(SAFE_DIVIDE((curr.subscriber_number - prev.subscriber_number), prev.subscriber_number), 4), 0)  AS subscriber_evolution,
        IFNULL(ROUND(SAFE_DIVIDE((curr.new_sub_number - prev.new_sub_number), prev.new_sub_number), 4), 0)           AS new_sub_evolution,
        IFNULL(ROUND(SAFE_DIVIDE((curr.new_unsub_number - prev.new_unsub_number), prev.new_unsub_number), 4), 0)     AS new_unsub_evolution,
        IFNULL(ROUND(SAFE_DIVIDE((curr.alert_number - prev.alert_number), prev.alert_number), 4), 0)                 AS alert_evolution,
        IFNULL(ROUND(SAFE_DIVIDE((curr.delivered - prev.delivered), prev.delivered), 4), 0)                          AS delivered_evolution,
        IFNULL(ROUND(SAFE_DIVIDE((curr.opens - prev.opens), prev.opens), 4), 0)                                      AS opens_evolution,
        IFNULL(ROUND(SAFE_DIVIDE((curr.openers - prev.openers), prev.openers), 4), 0)                                AS openers_evolution,
        IFNULL(ROUND(SAFE_DIVIDE((curr.clicks - prev.clicks), prev.clicks), 4), 0)                                   AS clicks_evolution,
        IFNULL(ROUND(SAFE_DIVIDE((curr.clickers - prev.clickers), prev.clickers), 4), 0)                             AS clickers_evolution
    FROM brand_kpis      AS curr
    LEFT JOIN brand_kpis AS prev
        ON curr.brand_trigram = prev.brand_trigram
        AND curr.start_month  = DATE_ADD(prev.start_month, INTERVAL 1 MONTH)
)

SELECT
    start_month,
    brand_trigram,
    STRUCT(
        subscriber_number            AS subscriber_number,
        new_sub_number               AS new_sub_number,
        new_unsub_number             AS new_unsub_number,
        alert_number                 AS alert_number,
        delivered                    AS delivered,
        opens                        AS opens,
        openers                      AS openers,
        clicks                       AS clicks,
        clickers                     AS clickers,
        STRUCT(
            subscriber_evolution        AS subscriber_number,
            new_sub_evolution           AS new_sub_number,
            new_unsub_evolution         AS new_unsub_number,
            alert_evolution             AS alert_number,
            delivered_evolution         AS delivered,
            opens_evolution             AS opens,
            openers_evolution           AS openers,
            clicks_evolution            AS clicks,
            clickers_evolution          AS clickers
        ) AS evolution
    ) AS kpi
FROM brand_kpi_evolution;
