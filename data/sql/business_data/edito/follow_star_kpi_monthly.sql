-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DROP TABLE IF EXISTS `{{ params.bq_project.mirror }}.business_data.follow_star_kpi_monthly`;
CREATE TABLE IF NOT EXISTS `{{ params.bq_project.mirror }}.business_data.follow_star_kpi_monthly`(
    start_month                 DATE       NOT NULL     OPTIONS(description="start day of month. example: '2021-01-01'"),
    brand_trigram               STRING                  OPTIONS(description="brand trigram"),
    person                      STRUCT<
        content_id                  STRING                  OPTIONS(description="star uuid. ref: {{ params.bq_project.mirror }}.store_one_person.person.id"),
        full_name                   STRING                  OPTIONS(description="star full name. ref:{{ params.bq_project.mirror }}.store_one_person.person.fullName"),
        image_link                  STRING                  OPTIONS(description="star image link. ref:{{ params.bq_project.mirror }}.store_one_person.person.bioPerBrand#brand_trigram#media#iframely#meta#canonical"),
        kpi                         STRUCT<
            subscriber_number           INTEGER          OPTIONS(description="number of subscribers on start month per brand per star"),
            new_sub_number              INTEGER          OPTIONS(description="number of new subscription on the month per brand per star"),
            new_unsub_number            INTEGER          OPTIONS(description="number of new unsubscription on the month per brand per star"),
            alert_number                INTEGER          OPTIONS(description="number of sent alerts on the month per brand per star"),
            delivered                   INTEGER          OPTIONS(description="number of delivered emails on the month per brand per star"),
            opens                       INTEGER          OPTIONS(description="opened emails on the month per brand per star"),
            openers                     INTEGER          OPTIONS(description="distinct openers on the month per brand per star"),
            clicks                      INTEGER          OPTIONS(description="clicked email on the month per brand per star"),
            clickers                    INTEGER          OPTIONS(description="distinct clickers on the month per brand per star"),
            rank                        STRUCT<
                subscriber_number           INTEGER        OPTIONS(description="star ranking per brand per month based on subscriber_number"),
                new_sub_number              INTEGER        OPTIONS(description="star ranking per brand per month based on new_sub_number"),
                new_unsub_number            INTEGER        OPTIONS(description="star ranking per brand per month based on new_unsub_number"),
                alert_number                INTEGER        OPTIONS(description="star ranking per brand per month based on alert_number"),
                delivered                   INTEGER        OPTIONS(description="star ranking per brand per month based on number of delivered emails"),
                opens                       INTEGER        OPTIONS(description="star ranking per brand per month based on number of opened emails"),
                openers                     INTEGER        OPTIONS(description="star ranking per brand per month based on number of distinct openers"),
                clicks                      INTEGER        OPTIONS(description="star ranking per brand per month based on number of clicked emails"),
                clickers                    INTEGER        OPTIONS(description="star ranking per brand per month based on number of distinct openers")
            >                                              OPTIONS(description="list person/star rank on the between current month based on listed kpi"),
            evolution                   STRUCT<
                subscriber_number           FLOAT64        OPTIONS(description="subscriber number evolution between previous month and current month"),
                new_sub_number              FLOAT64        OPTIONS(description="new subscription number evolution between previous month and current month"),
                new_unsub_number            FLOAT64        OPTIONS(description="new unsubscription number evolution between previous month and current month"),
                alert_number                FLOAT64        OPTIONS(description="alert number between previous month and current month"),
                delivered                   FLOAT64        OPTIONS(description="delivered kpi evolution between previous month and current month"),
                opens                       FLOAT64        OPTIONS(description="opens kpi evolution between previous month and current month"),
                openers                     FLOAT64        OPTIONS(description="openers kpi evolution between previous month and current month"),
                clicks                      FLOAT64        OPTIONS(description="clicks kpi evolution between previous month and current month"),
                clickers                    FLOAT64        OPTIONS(description="clickers kpi evolution between previous month and current month"),
                rank_subscriber_number      INTEGER        OPTIONS(description="star ranking evolution between previous and current month based on subscriber number"),
                rank_new_sub_number         INTEGER        OPTIONS(description="star ranking evolution between previous and current month based on new subscription number"),
                rank_new_unsub_number       INTEGER        OPTIONS(description="star ranking evolution between previous and current month based on new unsubscription number"),
                rank_alert_number           INTEGER        OPTIONS(description="star ranking evolution between previous and current month based on alert number"),
                rank_delivered              INTEGER        OPTIONS(description="star ranking evolution between previous and current month based on delivered kpi"),
                rank_opens                  INTEGER        OPTIONS(description="star ranking evolution between previous and current month based on opens kpi"),
                rank_openers                INTEGER        OPTIONS(description="star ranking evolution between previous and current month based on openers kpi"),
                rank_clicks                 INTEGER        OPTIONS(description="star ranking evolution between previous and current month based on clicks kpi"),
                rank_clickers               INTEGER        OPTIONS(description="star ranking evolution between previous and current month based on clickers kpi")
                >                                                   OPTIONS(description="list kpis evolution between current vs previous month")
            >                                                       OPTIONS(description="list of kpis")
       >                                                            OPTIONS(description="list of star description and kpis")
)
PARTITION BY start_month
OPTIONS(description="List follow stars kpis by brand on the start of each month."||
                    "To generate this table, we took only bookmarks with type='follow:people'."||
                    "Main kpis are:" ||
                    "* number of subscribers on start month."||
                    "* number of new subscription on current month."||
                    "* number of new unsubscription on current month."||
                    "* number of generated alerts on current month."||
                    "* star ranking based on these stats per month per brand."||
                    "* evolution of star following based on these stats per month per brand."||
                    "DAG: {{ dag.dag_id }}."||
                    "Sync: Monthly at 00:00AM UTC+2");

INSERT INTO `{{ params.bq_project.mirror }}.business_data.follow_star_kpi_monthly`
-- WITH sub-queries:
-- * month_couple: generate array of months (first and last days)
-- * follow_people: add is_subscribed category depending on start month
-- * count_subscribers: count subscriber (old and new) and unsubscriber number based of start month
-- * unnest_alert: unnest alert contents
-- * count_alert: count sent alert number
-- * alert_splio: get alert/bookmark related to splio campaigns
-- * count_email: count send emails per person and brand on the current month
-- * unnest_person: select person information with non null full name
-- * star_kpi: join all counts together and add star full name and image
-- * star_kpi_evolution: get evolution of all stars KPI between previous month and current month

WITH month_couple AS(
    -- generate first day of each month
    -- min date = '2017-06-01'
    SELECT
        start_month,
        LAST_DAY(start_month, MONTH) AS end_month
    FROM UNNEST(GENERATE_DATE_ARRAY(CAST('2017-06-01' AS DATE), CURRENT_DATE(), INTERVAL 1 MONTH)) AS start_month
), follow_people AS (
    -- extract bookmark = 'follow:people'
    SELECT
        content_id,
        brand_trigram,
        bmk.create_date,
        bmk.update_date,
        bmk.is_deleted,
        shoot_id,
        bmk.user.pmc_uuid
    FROM `{{ params.bq_project.mirror }}.refined_data.bookmark`
    LEFT JOIN UNNEST(bookmark) AS bmk
    WHERE bmk.type = 'follow:people'
), count_subscribers AS(
    -- count subscriber (old and new) and unsubscriber number based of start month
    SELECT
        mc.start_month,
        fp.content_id,
        fp.brand_trigram,
        --@to be revised --> cf. Gala's Follow Star process
        COUNT(DISTINCT IF(DATE(create_date) < mc.start_month AND (fp.is_deleted = 0 OR (fp.is_deleted = 1 AND DATE(fp.update_date) >= mc.start_month)), pmc_uuid, NULL)) AS subscriber_number,
        COUNT(DISTINCT IF(DATE(create_date) BETWEEN DATE_SUB(mc.start_month, INTERVAL 1 MONTH) AND LAST_DAY(DATE_SUB(mc.start_month, INTERVAL 1 MONTH)), pmc_uuid, NULL)) AS new_sub_number,
        COUNT(DISTINCT IF(fp.is_deleted = 1 AND DATE(update_date) BETWEEN DATE_SUB(mc.start_month, INTERVAL 1 MONTH) AND LAST_DAY(DATE_SUB(mc.start_month, INTERVAL 1 MONTH)), pmc_uuid, NULL)) AS new_unsub_number
    FROM follow_people  AS fp, month_couple AS mc
    GROUP BY ALL
), unnest_alert AS (
    -- unnest alert contents
    SELECT
        resource_id,
        create_date,
        brand_trigram,
        content_id
    FROM `{{ params.bq_project.mirror }}.refined_data.alert` AS a, UNNEST(a.content_id) AS content_id WITH OFFSET OFF
), count_alert AS (
    -- count sent alert number
    SELECT
        mc.start_month,
        ua.content_id,
        ua.brand_trigram,
        COUNT(DISTINCT IF(DATE(ua.create_date) BETWEEN DATE_SUB(mc.start_month, INTERVAL 1 MONTH) AND LAST_DAY(DATE_SUB(mc.start_month, INTERVAL 1 MONTH)), ua.resource_id, NULL)) AS alert_number
    FROM unnest_alert AS ua, month_couple AS mc
    JOIN follow_people AS fp
        ON fp.brand_trigram = ua.brand_trigram
        AND fp.content_id = ua.content_id
    GROUP BY ALL
), alert_splio AS(
    -- get alert/bookmark related to splio campaigns
    SELECT
        DISTINCT mc.start_month,
        ta.brand_trigram,
        fp.content_id,
        ta.campaign,
        s.stats.delivered,
        s.stats.opens,
        s.stats.openers,
        s.stats.clicks,
        s.stats.clickers
    FROM `{{ params.bq_project.matrix }}.refined_data.tmail_alert` AS ta, UNNEST(ta.content_id) AS content_id WITH OFFSET OFF
    JOIN month_couple AS mc
        ON DATE(ta.update_date) >= DATE_SUB(mc.start_month, INTERVAL 1 MONTH)
        AND DATE(ta.update_date) <= LAST_DAY(DATE_SUB(mc.start_month, INTERVAL 1 MONTH))
    JOIN `{{ params.bq_project.matrix }}.refined_data.splio_report` AS s
        ON s.id.campaign_ref = ta.campaign
        AND DATE(s.start_date) >= DATE_SUB(mc.start_month, INTERVAL 1 MONTH)
        AND DATE(s.start_date) <= LAST_DAY(DATE_SUB(mc.start_month, INTERVAL 1 MONTH))
    JOIN follow_people AS fp
        ON fp.shoot_id = SAFE_CAST(content_id AS INT64)
        AND fp.brand_trigram = ta.brand_trigram
        AND DATE(fp.update_date) >= DATE_SUB(mc.start_month, INTERVAL 1 MONTH)
        AND DATE(fp.update_date) <= LAST_DAY(DATE_SUB(mc.start_month, INTERVAL 1 MONTH))
    WHERE ta.brand_trigram IS NOT NULL
), count_email AS(
    -- count send emails per person and brand on the current month
    SELECT
        start_month,
        brand_trigram,
        content_id,
        SUM(IFNULL(delivered, 0)) AS delivered,
        SUM(IFNULL(opens, 0))     AS opens,
        SUM(IFNULL(openers, 0))   AS openers,
        SUM(IFNULL(clicks, 0))    AS clicks,
        SUM(IFNULL(clickers, 0))  AS clickers
    FROM alert_splio
    GROUP BY ALL
), unnest_person AS (
    -- select person information with non null full name
    SELECT
        person_uuid,
        personal_information.full_name,
        MAX(JSON_EXTRACT_SCALAR(media, '$.iframely.meta.canonical')) AS image_link
    FROM `{{ params.bq_project.mirror }}.refined_data.person` AS p
    LEFT JOIN `{{ params.bq_project.mirror }}.store_one_person.personBiography` AS b
      ON p.person_int_id = b.personIntId
    WHERE personal_information.full_name IS NOT NULL
    GROUP BY ALL
), star_kpi AS (
    -- join all counts together and add star full name and image
    SELECT DISTINCT
        cs.start_month,
        cs.content_id,
        cs.brand_trigram,
        up.full_name,
        up.image_link,
        IFNULL(subscriber_number, 0) AS subscriber_number,
        IFNULL(new_sub_number, 0 )   AS new_sub_number,
        IFNULL(new_unsub_number, 0)  AS new_unsub_number,
        IFNULL(alert_number, 0)      AS alert_number,
        delivered,
        opens,
        openers,
        clicks,
        clickers,
        ROW_NUMBER() OVER(PARTITION BY start_month, cs.brand_trigram ORDER BY IFNULL(subscriber_number, 0) DESC)   AS rank_by_subscriber_number,
        ROW_NUMBER() OVER(PARTITION BY start_month, cs.brand_trigram ORDER BY IFNULL(new_sub_number, 0) DESC)      AS rank_by_new_sub_number,
        ROW_NUMBER() OVER(PARTITION BY start_month, cs.brand_trigram ORDER BY IFNULL(new_unsub_number, 0) DESC)    AS rank_by_new_unsub_number,
        ROW_NUMBER() OVER(PARTITION BY start_month, cs.brand_trigram ORDER BY IFNULL(alert_number, 0) DESC)        AS rank_by_alert_number,
        ROW_NUMBER() OVER(PARTITION BY start_month, cs.brand_trigram ORDER BY IFNULL(delivered, 0) DESC)           AS rank_by_delivered,
        ROW_NUMBER() OVER(PARTITION BY start_month, cs.brand_trigram ORDER BY IFNULL(opens, 0) DESC)               AS rank_by_opens,
        ROW_NUMBER() OVER(PARTITION BY start_month, cs.brand_trigram ORDER BY IFNULL(openers, 0) DESC)             AS rank_by_openers,
        ROW_NUMBER() OVER(PARTITION BY start_month, cs.brand_trigram ORDER BY IFNULL(clicks, 0) DESC)              AS rank_by_clicks,
        ROW_NUMBER() OVER(PARTITION BY start_month, cs.brand_trigram ORDER BY IFNULL(clickers, 0) DESC)            AS rank_by_clickers
    FROM count_subscribers AS cs
    LEFT JOIN count_alert  USING(start_month, content_id, brand_trigram)
    LEFT JOIN count_email  USING(start_month, content_id, brand_trigram)
    JOIN unnest_person     AS up ON up.person_uuid = cs.content_id
), star_kpi_evolution AS (
-- get evolution of all stars KPI between previous month and current month
SELECT
    curr.start_month,
    curr.brand_trigram,
    curr.content_id,
    curr.full_name,
    curr.image_link,
    IFNULL(curr.subscriber_number, 0)   AS subscriber_number,
    IFNULL(curr.new_sub_number, 0)      AS new_sub_number,
    IFNULL(curr.new_unsub_number, 0)    AS new_unsub_number,
    IFNULL(curr.alert_number, 0)        AS alert_number,
    IFNULL(curr.delivered, 0)           AS delivered,
    IFNULL(curr.opens, 0)               AS opens,
    IFNULL(curr.openers, 0)             AS openers,
    IFNULL(curr.clicks, 0)              AS clicks,
    IFNULL(curr.clickers, 0)            AS clickers,
    curr.rank_by_subscriber_number,
    curr.rank_by_new_sub_number,
    curr.rank_by_new_unsub_number,
    curr.rank_by_alert_number,
    curr.rank_by_delivered,
    curr.rank_by_opens,
    curr.rank_by_openers,
    curr.rank_by_clicks,
    curr.rank_by_clickers,
    IFNULL(ROUND(SAFE_DIVIDE((curr.subscriber_number - prev.subscriber_number) , prev.subscriber_number), 4), 0)    AS subscriber_evolution,
    IFNULL(ROUND(SAFE_DIVIDE((curr.new_sub_number - prev.new_sub_number) , prev.new_sub_number), 4), 0)             AS new_sub_evolution,
    IFNULL(ROUND(SAFE_DIVIDE((curr.new_unsub_number - prev.new_unsub_number) , prev.new_unsub_number), 4), 0)       AS new_unsub_evolution,
    IFNULL(ROUND(SAFE_DIVIDE((curr.alert_number - prev.alert_number) , prev.alert_number), 4), 0)                   AS alert_evolution,
    IFNULL(ROUND(SAFE_DIVIDE((curr.delivered - prev.delivered) , prev.delivered), 4), 0)                            AS delivered_evolution,
    IFNULL(ROUND(SAFE_DIVIDE((curr.opens - prev.opens) , prev.opens), 4), 0)                                        AS opens_evolution,
    IFNULL(ROUND(SAFE_DIVIDE((curr.openers - prev.openers) , prev.openers), 4), 0)                                  AS openers_evolution,
    IFNULL(ROUND(SAFE_DIVIDE((curr.clicks - prev.clicks) , prev.clicks), 4), 0)                                     AS clicks_evolution,
    IFNULL(ROUND(SAFE_DIVIDE((curr.clickers - prev.clickers) , prev.clickers), 4), 0)                               AS clickers_evolution,
    IFNULL((prev.rank_by_subscriber_number - curr.rank_by_subscriber_number), 0)                                    AS rank_subscriber_evolution,
    IFNULL((prev.rank_by_new_sub_number - curr.rank_by_new_sub_number), 0)                                          AS rank_new_sub_evolution,
    IFNULL((prev.rank_by_new_unsub_number - curr.rank_by_new_unsub_number), 0)                                      AS rank_new_unsub_evolution,
    IFNULL((prev.rank_by_alert_number - curr.rank_by_alert_number), 0)                                              AS rank_alert_evolution,
    IFNULL((prev.rank_by_delivered - curr.rank_by_delivered), 0)                                                    AS rank_delivered_evolution,
    IFNULL((prev.rank_by_opens - curr.rank_by_opens), 0)                                                            AS rank_opens_evolution,
    IFNULL((prev.rank_by_openers - curr.rank_by_openers), 0)                                                        AS rank_openers_evolution,
    IFNULL((prev.rank_by_clicks - curr.rank_by_clicks), 0)                                                          AS rank_clicks_evolution,
    IFNULL((prev.rank_by_clickers - curr.rank_by_clickers), 0)                                                      AS rank_clickers_evolution
FROM star_kpi AS curr
LEFT JOIN star_kpi AS prev
    ON curr.brand_trigram = prev.brand_trigram
    AND curr.content_id   = prev.content_id
    AND curr.start_month  = DATE_ADD(prev.start_month, INTERVAL 1 MONTH)
)

SELECT
    start_month,
    brand_trigram,
    STRUCT(
        content_id                  AS content_id,
        full_name                   AS full_name,
        image_link                  AS image_link,
        STRUCT(
            subscriber_number            AS subscriber_number,
            new_sub_number               AS new_sub_number,
            new_unsub_number             AS new_unsub_number,
            alert_number                 AS alert_number,
            delivered                    AS delivered,
            opens                        AS opens,
            openers                      AS openers,
            clicks                       AS clicks,
            clickers                     AS clickers,
            STRUCT(
                rank_by_subscriber_number    AS subscriber_number,
                rank_by_new_sub_number       AS new_sub_number,
                rank_by_new_unsub_number     AS new_unsub_number,
                rank_by_alert_number         AS alert_number,
                rank_by_delivered            AS delivered,
                rank_by_opens                AS opens,
                rank_by_openers              AS openers,
                rank_by_clicks               AS clicks,
                rank_by_clickers             AS clickers
                ) AS rank,
            STRUCT(
                subscriber_evolution        AS subscriber_number,
                new_sub_evolution           AS new_sub_number,
                new_unsub_evolution         AS new_unsub_number,
                alert_evolution             AS alert_number,
                delivered_evolution         AS delivered,
                opens_evolution             AS opens,
                openers_evolution           AS openers,
                clicks_evolution            AS clicks,
                clickers_evolution          AS clickers,
                rank_subscriber_evolution   AS rank_subscriber_number,
                rank_new_sub_evolution      AS rank_new_sub_number,
                rank_new_unsub_evolution    AS rank_new_unsub_number,
                rank_alert_evolution        AS rank_alert_number,
                rank_delivered_evolution    AS rank_delivered,
                rank_opens_evolution        AS rank_opens,
                rank_openers_evolution      AS rank_openers,
                rank_clicks_evolution       AS rank_clicks,
                rank_clickers_evolution     AS rank_clickers
                ) AS evolution
            ) AS kpi
    ) AS person
FROM star_kpi_evolution;
