-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- Update volume for tracking indicators

DECLARE ga4_month DATE DEFAULT DATE("2024-06-01");
DECLARE task_start_date DATE DEFAULT DATE_TRUNC(DATE_SUB(CURRENT_DATE(), INTERVAL {{ params.time_interval }}), MONTH);
DECLARE task_end_date DATE DEFAULT DATE_TRUNC(DATE_SUB(CURRENT_DATE(), INTERVAL {{ params.time_interval }}), MONTH);

CREATE TABLE IF NOT EXISTS `{{ params.bq_project_matrix }}.business_data.users_mam_maum`
    (
        month DATE NOT NULL OPTIONS(description="Date of the month for which the data is aggregated."),
        global STRUCT<
            global_visitors           INT64 OPTIONS(description="Total number of unique visitors in the month."),
            global_connected_visitors INT64 OPTIONS(description="Total number of connected visitors in the month.")
        > OPTIONS(description="Global users statistics."),
        brand_detail ARRAY<STRUCT<
            brand_trigram            STRING OPTIONS(description="Trigram representing the brand."),
            brand_visitors           INT64  OPTIONS(description="Total number of unique visitors for the brand in the month."),
            brand_connected_visitors INT64  OPTIONS(description="Total number of connected visitors for the brand in the month."),
            channel_detail ARRAY<STRUCT<
                channel            STRING OPTIONS(description="Name of the channel."),
                visitors           INT64  OPTIONS(description="Total number of unique visitors for the channel in the month."),
                connected_visitors INT64  OPTIONS(description="Total number of connected visitors for the brand/channel in the month.")
            >> OPTIONS(description="Detailed users statistics per channel."),
            platform_detail ARRAY<STRUCT<
                platform           STRING OPTIONS(description="Name of the platform."),
                visitors           INT64  OPTIONS(description="Total number of unique visitors for the platform in the month."),
                connected_visitors INT64  OPTIONS(description="Total number of connected visitors for the brand/platform in the month.")
            >> OPTIONS(description="Detailed users statistics per platform.")
        >> OPTIONS(description="Detailed users statistics per brand.")
    )
PARTITION BY month
OPTIONS(
    description="Table containing monthly user statistics, including global metrics, and detailed metrics per brand, channel, and platform."||
                "It's used in the dashboard Dash MAUM/MAM V4.\n" ||
                "- Atlas prod: {{ params.url_atlas_prod }}\n" ||
                "- Atlas preprod: {{ params.url_atlas_preprod }}\n" ||
                "DAG: {{ dag.dag_id }}\n" ||
                "Sync: Daily.\n"
) ;

{% if params.is_full %}
    SET task_start_date = DATE_TRUNC(DATE("{{ params.default_start_date }}"), MONTH);
    SET task_end_date = DATE_TRUNC(DATE("{{ params.default_end_date }}"), MONTH);

    DELETE FROM `{{ params.bq_project_matrix }}.business_data.users_mam_maum`
    WHERE month BETWEEN task_start_date AND task_end_date;
{% endif %}

MERGE `{{ params.bq_project_matrix }}.business_data.users_mam_maum` AS dst

USING(
    WITH user_stats_per_brand AS (
        -- Extract stats from GAN refined_data before ga4 migration
        SELECT
            DATE_TRUNC(visit_date, MONTH)                                                AS month,
            property.brand_trigram,
            COUNT(DISTINCT user.full_visitor_id ) AS visitors,
            COUNT(DISTINCT pd360.id.pmc_uuid ) AS connected_visitors
        FROM `{{ params.bq_project_matrix }}.refined_data.gan_navigation_all_visits_*`
        LEFT join `{{ params.bq_project_matrix }}.business_data.profile_digital_360` AS pd360
          ON user.web_id = pd360.id.pmc_web_id
        WHERE _TABLE_SUFFIX BETWEEN REGEXP_REPLACE(CAST(task_start_date AS STRING), '-','') AND REGEXP_REPLACE(CAST(task_end_date AS STRING), '-','')
            AND _TABLE_SUFFIX < REGEXP_REPLACE(CAST(ga4_month AS STRING), '-','')
        GROUP BY ALL

        UNION ALL

        -- Extract stats from GA4 refined_data web tables
        SELECT
            DATE_TRUNC(e.visit_date, MONTH)              AS month,
            e.property_data.brand_trigram                AS brand_trigram,
            COUNT(DISTINCT user_pseudo_id ) AS visitors,
            COUNT(DISTINCT pd360.id.pmc_uuid) AS connected_visitors
          FROM  `{{ params.bq_project_ga4 }}.refined_data.ga_events_WEB_*` AS e
          LEFT JOIN UNNEST(e.user_data.pmc_web_id) AS pmc
          LEFT JOIN `{{ params.bq_project_matrix }}.business_data.profile_digital_360` AS pd360
          ON pmc = pd360.id.pmc_web_id
          WHERE DATE_TRUNC(visit_date, MONTH) BETWEEN task_start_date AND task_end_date AND visit_date >= ga4_month
                          AND _TABLE_SUFFIX NOT LIKE "%TEMPLATE%"
                          AND _TABLE_SUFFIX NOT LIKE "%_v0%"
        GROUP BY ALL

        UNION ALL

          -- Extract stats from GA4 refined_data app tables
          -- NB : for app tables all users are connected
        SELECT
            DATE_TRUNC(e.visit_date, MONTH)              AS month,
            e.property_data.brand_trigram                AS brand_trigram,
            COUNT(DISTINCT user_pseudo_id ) AS visitors,
            COUNT(DISTINCT pd360.id.pmc_uuid) AS connected_visitors
          FROM  `{{ params.bq_project_ga4 }}.refined_data.ga_events_APP_*` AS e
          LEFT JOIN UNNEST(e.user_data.pmc_uuid) AS pmc
          LEFT JOIN `{{ params.bq_project_matrix }}.business_data.profile_digital_360` AS pd360
          ON pmc = pd360.id.pmc_uuid
          WHERE DATE_TRUNC(visit_date, MONTH) BETWEEN task_start_date AND task_end_date AND visit_date >= ga4_month
                          AND _TABLE_SUFFIX NOT LIKE "%TEMPLATE%"
                          AND _TABLE_SUFFIX NOT LIKE "%_v0%"

        GROUP BY ALL
    ),

    user_stats_per_brand_channel AS (
        -- Extract stats from GAN refined_data before ga4 migration
        SELECT
            DATE_TRUNC(visit_date, MONTH)                                                AS month,
            property.brand_trigram,
            CASE
                WHEN INITCAP(visit.tracking.channel) = 'Organic' THEN 'Organic Search'
                ELSE INITCAP(REPLACE(visit.tracking.channel, '_', ' '))
            END AS channel,
            COUNT(DISTINCT user.full_visitor_id ) AS visitors,
            COUNT(DISTINCT pd360.id.pmc_uuid ) AS connected_visitors
        FROM `{{ params.bq_project_matrix }}.refined_data.gan_navigation_all_visits_*`
        LEFT join `{{ params.bq_project_matrix }}.business_data.profile_digital_360` AS pd360
          ON user.web_id = pd360.id.pmc_web_id
        WHERE _TABLE_SUFFIX BETWEEN REGEXP_REPLACE(CAST(task_start_date AS STRING), '-','') AND REGEXP_REPLACE(CAST(task_end_date AS STRING), '-','')
            AND _TABLE_SUFFIX < REGEXP_REPLACE(CAST(ga4_month AS STRING), '-','')
        GROUP BY ALL

        UNION ALL

        -- Extract stats from GA4 refined_data web tables
        SELECT
            DATE_TRUNC(e.visit_date, MONTH)              AS month,
            e.property_data.brand_trigram                AS brand_trigram,
            INITCAP(sessions.tracking_info.custom_channel_group)  AS channel,
            COUNT(DISTINCT user_pseudo_id ) AS visitors,
            COUNT(DISTINCT pd360.id.pmc_uuid) AS connected_visitors
          FROM  `{{ params.bq_project_ga4 }}.refined_data.ga_events_WEB_*` AS e,
          UNNEST(e.session_data) AS sessions
          LEFT JOIN UNNEST(e.user_data.pmc_web_id) AS pmc
          LEFT JOIN `{{ params.bq_project_matrix }}.business_data.profile_digital_360` AS pd360
          ON pmc = pd360.id.pmc_web_id
          WHERE DATE_TRUNC(visit_date, MONTH) BETWEEN task_start_date AND task_end_date AND visit_date >= ga4_month
                          AND _TABLE_SUFFIX NOT LIKE "%TEMPLATE%"
                          AND _TABLE_SUFFIX NOT LIKE "%_v0%"
        GROUP BY ALL

        UNION ALL

          -- Extract stats from GA4 refined_data app tables
          -- NB : for app tables all users are connected
        SELECT
            DATE_TRUNC(e.visit_date, MONTH)              AS month,
            e.property_data.brand_trigram                AS brand_trigram,
            INITCAP(sessions.tracking_info.custom_channel_group)  AS channel,
            COUNT(DISTINCT user_pseudo_id ) AS visitors,
            COUNT(DISTINCT pd360.id.pmc_uuid) AS connected_visitors
          FROM  `{{ params.bq_project_ga4 }}.refined_data.ga_events_APP_*` AS e,
          UNNEST(e.session_data) AS sessions
          LEFT JOIN UNNEST(e.user_data.pmc_uuid) AS pmc
          LEFT JOIN `{{ params.bq_project_matrix }}.business_data.profile_digital_360` AS pd360
          ON pmc = pd360.id.pmc_uuid
          WHERE DATE_TRUNC(visit_date, MONTH) BETWEEN task_start_date AND task_end_date AND visit_date >= ga4_month
                          AND _TABLE_SUFFIX NOT LIKE "%TEMPLATE%"
                          AND _TABLE_SUFFIX NOT LIKE "%_v0%"

        GROUP BY ALL
    ),

    user_stats_per_brand_platform AS (
        -- Extract stats from GAN refined_data before ga4 migration
        SELECT
            DATE_TRUNC(visit_date, MONTH)                                                AS month,
            property.brand_trigram,
            CASE
                WHEN property.platform = 'mob' THEN 'app'
                ELSE property.platform
            END AS platform,
            COUNT(DISTINCT user.full_visitor_id ) AS visitors,
            COUNT(DISTINCT pd360.id.pmc_uuid ) AS connected_visitors
        FROM `{{ params.bq_project_matrix }}.refined_data.gan_navigation_all_visits_*`
        LEFT join `{{ params.bq_project_matrix }}.business_data.profile_digital_360` AS pd360
          ON user.web_id = pd360.id.pmc_web_id
        WHERE _TABLE_SUFFIX BETWEEN REGEXP_REPLACE(CAST(task_start_date AS STRING), '-','') AND REGEXP_REPLACE(CAST(task_end_date AS STRING), '-','')
            AND _TABLE_SUFFIX < REGEXP_REPLACE(CAST(ga4_month AS STRING), '-','')
        GROUP BY ALL

        UNION ALL

        -- Extract stats from GA4 refined_data web tables
        SELECT
            DATE_TRUNC(e.visit_date, MONTH)              AS month,
            e.property_data.brand_trigram                AS brand_trigram,
            LOWER(property_data.platform)                AS platform,
            COUNT(DISTINCT user_pseudo_id ) AS visitors,
            COUNT(DISTINCT pd360.id.pmc_uuid) AS connected_visitors
          FROM  `{{ params.bq_project_ga4 }}.refined_data.ga_events_WEB_*` AS e
          LEFT JOIN UNNEST(e.user_data.pmc_web_id) AS pmc
          LEFT JOIN `{{ params.bq_project_matrix }}.business_data.profile_digital_360` AS pd360
          ON pmc = pd360.id.pmc_web_id
          WHERE DATE_TRUNC(visit_date, MONTH) BETWEEN task_start_date AND task_end_date AND visit_date >= ga4_month
                          AND _TABLE_SUFFIX NOT LIKE "%TEMPLATE%"
                          AND _TABLE_SUFFIX NOT LIKE "%_v0%"
        GROUP BY ALL

        UNION ALL

          -- Extract stats from GA4 refined_data app tables
          -- NB : for app tables all users are connected
        SELECT
            DATE_TRUNC(e.visit_date, MONTH)              AS month,
            e.property_data.brand_trigram                AS brand_trigram,
            LOWER(property_data.platform)                AS platform,
            COUNT(DISTINCT user_pseudo_id ) AS visitors,
            COUNT(DISTINCT pd360.id.pmc_uuid) AS connected_visitors
          FROM  `{{ params.bq_project_ga4 }}.refined_data.ga_events_APP_*` AS e
          LEFT JOIN UNNEST(e.user_data.pmc_uuid) AS pmc
          LEFT JOIN `{{ params.bq_project_matrix }}.business_data.profile_digital_360` AS pd360
          ON pmc = pd360.id.pmc_uuid
          WHERE DATE_TRUNC(visit_date, MONTH) BETWEEN task_start_date AND task_end_date AND visit_date >= ga4_month
                          AND _TABLE_SUFFIX NOT LIKE "%TEMPLATE%"
                          AND _TABLE_SUFFIX NOT LIKE "%_v0%"

        GROUP BY ALL
    ),

    monthly_global_stats AS (
        SELECT
            month,
            SUM(visitors) AS global_visitors,
            SUM(connected_visitors) AS global_connected_visitors
        FROM
        (
        -- compute global stats
        -- Extract stats from GAN refined_data before ga4 migration
        SELECT
            DATE_TRUNC(visit_date, MONTH)                                                AS month,
            COUNT(DISTINCT user.full_visitor_id ) AS visitors,
            COUNT(DISTINCT pd360.id.pmc_uuid ) AS connected_visitors
        FROM `{{ params.bq_project_matrix }}.refined_data.gan_navigation_all_visits_*`
        LEFT join `{{ params.bq_project_matrix }}.business_data.profile_digital_360` AS pd360
          ON user.web_id = pd360.id.pmc_web_id
        WHERE _TABLE_SUFFIX BETWEEN REGEXP_REPLACE(CAST(task_start_date AS STRING), '-','') AND REGEXP_REPLACE(CAST(task_end_date AS STRING), '-','')
            AND _TABLE_SUFFIX < REGEXP_REPLACE(CAST(ga4_month AS STRING), '-','')
        GROUP BY ALL

        UNION ALL

        -- Extract stats from GA4 refined_data web tables
        SELECT
            DATE_TRUNC(e.visit_date, MONTH)              AS month,
            COUNT(DISTINCT user_pseudo_id ) AS visitors,
            COUNT(DISTINCT pd360.id.pmc_uuid) AS connected_visitors
          FROM  `{{ params.bq_project_ga4 }}.refined_data.ga_events_WEB_*` AS e
          LEFT JOIN UNNEST(e.user_data.pmc_web_id) AS pmc
          LEFT JOIN `{{ params.bq_project_matrix }}.business_data.profile_digital_360` AS pd360
          ON pmc = pd360.id.pmc_web_id
          WHERE DATE_TRUNC(visit_date, MONTH) BETWEEN task_start_date AND task_end_date AND visit_date >= ga4_month
                          AND _TABLE_SUFFIX NOT LIKE "%TEMPLATE%"
                          AND _TABLE_SUFFIX NOT LIKE "%_v0%"
        GROUP BY ALL

        UNION ALL

          -- Extract stats from GA4 refined_data app tables
          -- NB : for app tables all users are connected
         SELECT
            DATE_TRUNC(e.visit_date, MONTH)              AS month,
            COUNT(DISTINCT user_pseudo_id ) AS visitors,
            COUNT(DISTINCT pd360.id.pmc_uuid) AS connected_visitors
          FROM  `{{ params.bq_project_ga4 }}.refined_data.ga_events_APP_*` AS e
          LEFT JOIN UNNEST(e.user_data.pmc_uuid) AS pmc
          LEFT JOIN `{{ params.bq_project_matrix }}.business_data.profile_digital_360` AS pd360
          ON pmc = pd360.id.pmc_uuid
          WHERE DATE_TRUNC(visit_date, MONTH) BETWEEN task_start_date AND task_end_date AND visit_date >= ga4_month
                          AND _TABLE_SUFFIX NOT LIKE "%TEMPLATE%"
                          AND _TABLE_SUFFIX NOT LIKE "%_v0%"

        GROUP BY ALL
        )
        GROUP BY month
    ),

    monthly_detail_brand AS (
        -- structured detail stats
        SELECT
            month,
            brand_trigram,
            SUM(visitors) AS brand_visitors,
            SUM(connected_visitors) AS brand_connected_visitors
        FROM user_stats_per_brand
        GROUP BY ALL
    ),
        monthly_detail_channel_array AS (
        -- structured detail stats
        SELECT
            month,
            brand_trigram,
            ARRAY_AGG(STRUCT(
                channel,
                visitors,
                connected_visitors
            )) AS channel_detail,
        FROM user_stats_per_brand_channel
        GROUP BY ALL
    ),
        monthly_detail_platfrom_array AS (
        -- structured detail stats
        SELECT
            month,
            brand_trigram,
            ARRAY_AGG(STRUCT(
                platform,
                visitors,
                connected_visitors
            )) AS platform_detail,
        FROM user_stats_per_brand_platform
        GROUP BY ALL
    ),

    merge_underdetails AS (
        -- Combine channel and platform details into a single CTE
        SELECT
            p.month AS month,
            ARRAY_AGG(STRUCT(
                b.brand_trigram,
                b.brand_visitors,
                b.brand_connected_visitors,
                c.channel_detail,
                p.platform_detail
            )) AS brand_detail
        FROM monthly_detail_channel_array AS c
        JOIN monthly_detail_platfrom_array AS p USING (month, brand_trigram)
        JOIN monthly_detail_brand AS b USING(month, brand_trigram)
        GROUP BY ALL
    ),

    merged_stats AS (
        -- Merged global and detail stats
        SELECT
            g.month,
            STRUCT(
                global_visitors,
                global_connected_visitors
            ) AS global,
            d.brand_detail
        FROM monthly_global_stats AS g
        LEFT JOIN merge_underdetails AS d USING(month)
    )

    SELECT *
    FROM merged_stats
) AS ref

ON ref.month = dst.month

-- Update rows
WHEN MATCHED THEN
    UPDATE SET dst.brand_detail= ref.brand_detail,
               dst.global = ref.global

-- Add new rows
WHEN NOT MATCHED THEN
    INSERT(
        month,
        global,
        brand_detail
    )
    VALUES(
        ref.month,
        ref.global,
        ref.brand_detail
    )
