-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
-- MAUM vue completes Poles

DECLARE debut DATE;
DECLARE fin DATE;

SET debut = DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH);
SET fin = DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY);

INSERT INTO `{{ params.bq_project }}.business_data.MAUM_vue_complete_poles`

WITH data AS (
    SELECT FORMAT_DATE('%Y-%m', DATE_TRUNC(date, MONTH)) AS date,
            user.web_id,
            CASE 
                WHEN website IN ('Beaute Addict', 'Femme Actuelle', 'Cuisine Actuelle', 'Gala') THEN 'Femmes'
                WHEN website IN ('Business Insider', 'Capital', 'Geo', 'Néon', "Ça m'intéresse") THEN 'Premium'
                WHEN website IN ('Ce Soir TV', 'Tele 2 Semaines', 'Tele Loisirs', 'Voici') THEN 'TV-Entertainement'
                END                                                                                                     AS pole,
            device.device_category                                   AS device,
            device.browser                                           AS navigateur
    FROM `{{ params.bq_project }}.refined_data.gan_navigation`
    WHERE date BETWEEN debut AND fin 
), count_mum AS (
    SELECT date,
            pole,
            COUNT(DISTINCT web_id) AS MUM,
    FROM data
    GROUP BY 1, 2
    UNION ALL 
    SELECT date,
            'Global'                    AS pole,
            COUNT(DISTINCT web_id)      AS MUM
    FROM data
    GROUP BY 1, 2

), count_desktop AS (
    SELECT date,
            Pole,
            COUNT(DISTINCT web_id) as Desktop
    FROM(
        SELECT date,
                web_id, 
                Pole
        FROM data
        WHERE device = 'desktop'
        EXCEPT DISTINCT
        SELECT date, 
                web_id, 
                Pole
        FROM data
        WHERE device IN ('mobile', 'tablet')
    )
    GROUP BY date, Pole
    UNION ALL
    SELECT date, 
            Pole, 
            COUNT(DISTINCT web_id) AS Desktop
    FROM(
      SELECT date,
            web_id, 
            'Global' AS Pole
      FROM data
      WHERE device = 'desktop'
      EXCEPT DISTINCT
      SELECT date,
            web_id, 
            'Global' as Pole
      FROM data
      WHERE device IN ('mobile', 'tablet')
    )
    GROUP BY date, Pole

), count_mobile AS (
    SELECT date,
            Pole,
            COUNT(DISTINCT web_id) AS Mobile
    FROM (
        SELECT date,
                web_id, 
                Pole
        FROM data
        WHERE device = 'mobile'
        EXCEPT DISTINCT
        SELECT date,
                web_id, 
                Pole
        FROM data
        WHERE device IN ('desktop', 'tablet')
    )
    GROUP BY date, Pole
    UNION ALL
    SELECT date, 
            Pole,
            COUNT(DISTINCT web_id) AS Mobile
    FROM (
        SELECT date,
                web_id, 
                'Global' as Pole
        FROM data
        WHERE device = 'mobile'
        EXCEPT DISTINCT
        SELECT date,
                web_id, 
                'Global' AS Pole
        FROM data
        WHERE device IN ('desktop', 'tablet')
    )
    GROUP BY date, Pole
), count_tablet AS (
    SELECT date,
            Pole,
            COUNT(DISTINCT web_id) as Tablet
    FROM (
        SELECT date,
                web_id, 
                Pole
        FROM data
        WHERE device = 'tablet'
        EXCEPT DISTINCT
        SELECT date,
                web_id, 
                Pole
        FROM data
        WHERE device IN ('mobile', 'desktop')
    )  
    GROUP BY date, Pole
    UNION ALL
    SELECT date, 
            Pole,
            COUNT(DISTINCT web_id) AS Tablet
    FROM (
        SELECT date,
                web_id, 
                'Global' AS Pole
        FROM data
        WHERE device = 'tablet'
        EXCEPT DISTINCT
        SELECT date,
                web_id, 
                'Global' as Pole
        FROM data
        WHERE device IN ('mobile', 'desktop')
    )   
    GROUP BY date, Pole
), count_desktop_and_mobile AS (
    SELECT date,
            Pole,
            COUNT(DISTINCT web_id) AS Desktop_Mobile
    FROM (
        (SELECT date,
                Pole, 
                web_id
        FROM data
        WHERE device = 'desktop'
        INTERSECT DISTINCT
        SELECT date,
                Pole, 
                web_id
        FROM data
        WHERE device = 'mobile')
        EXCEPT DISTINCT
        SELECT date,
                Pole, 
                web_id
        FROM data
        where device = 'tablet'
    )
    GROUP BY date, Pole
    UNION ALL
    SELECT date,
            Pole,
            COUNT(DISTINCT web_id) AS Desktop_Mobile
    FROM (
        (
        SELECT date,
             'Global' AS Pole, 
             web_id
        FROM data
        WHERE device = 'desktop'
        INTERSECT DISTINCT
        SELECT date,
                'Global' AS Pole, 
                web_id
        FROM data
        WHERE device = 'mobile'
        )
        EXCEPT DISTINCT
        SELECT date,
                'Global' AS Pole, 
                web_id
        FROM data
        where device = 'tablet'
    )
    GROUP BY date, Pole
), count_desktop_and_tablet AS (
    SELECT date,
            Pole,
            COUNT(DISTINCT web_id) AS Desktop_Tablet
    FROM (
        (
        SELECT date,
                Pole, 
                web_id
        FROM data
        WHERE device = 'desktop'
        INTERSECT DISTINCT
        SELECT date,
                Pole, 
                web_id
        FROM data
        WHERE device = 'tablet'
        )
        EXCEPT DISTINCT
        SELECT date,
                Pole, 
                web_id
        FROM data
        where device = 'mobile'
    )
    GROUP BY date, pole  
    UNION ALL  
    SELECT date, 
            pole,
            COUNT(DISTINCT web_id) AS Desktop_Tablet
    FROM (
        (
        SELECT date,
                'Global' AS Pole, 
                web_id
        FROM data
        WHERE device = 'desktop'
        INTERSECT DISTINCT
        SELECT date,
                'Global' AS Pole, 
                web_id
        FROM data
        WHERE device = 'tablet'
        )
        EXCEPT DISTINCT
        SELECT date,
                'Global' AS Pole, 
                web_id
        FROM data
        where device = 'mobile'
    )
    GROUP BY date, pole


), count_mobile_and_tablet AS (
    SELECT date,
            Pole,
            COUNT(DISTINCT web_id) AS Mobile_Tablet
    FROM (
        (
        SELECT date,
                Pole, 
                web_id
        FROM data
        WHERE device = 'mobile'
        INTERSECT DISTINCT
        SELECT date,
                Pole, 
                web_id
        FROM data
        WHERE device = 'tablet'
        )
        EXCEPT DISTINCT
        SELECT date, 
                Pole, 
                web_id
        FROM data
        where device = 'desktop'
    )
    GROUP BY date, Pole   
    UNION ALL  
    SELECT date,
            Pole,
            COUNT(DISTINCT web_id) AS Mobile_Tablet
    FROM (
        (
        SELECT date,
                'Global' AS Pole, 
                web_id
        FROM data
        WHERE device = 'mobile'
        INTERSECT DISTINCT
        SELECT date,
                'Global' AS Pole, 
                web_id
        FROM data
        WHERE device = 'tablet'
        )
        EXCEPT DISTINCT
        SELECT date,
                'Global' AS Pole, 
                web_id
        FROM data
        where device = 'desktop'
    )
    GROUP BY date, Pole
), count_desktop_and_mobile_and_tablet as (
    SELECT date,
            Pole,
            COUNT(DISTINCT web_id) AS Desktop_Mobile_Tablet
    FROM (
        SELECT date,
                Pole, 
                web_id
        FROM data
        WHERE device = 'desktop'
        INTERSECT DISTINCT
        SELECT date,
                Pole, 
                web_id
        FROM data
        WHERE device = 'mobile'
        INTERSECT DISTINCT
        SELECT date,
                Pole, 
                web_id
        FROM data
        WHERE device = 'tablet'
    )
    GROUP BY date, Pole  
    UNION ALL
    SELECT date,
            Pole,
            COUNT(DISTINCT web_id) AS Desktop_Mobile_Tablet
    FROM (
        SELECT date,
                'Global' AS Pole, 
                web_id
        FROM data
        WHERE device = 'desktop'
        INTERSECT DISTINCT
        SELECT date,
                'Global' AS Pole, 
                web_id
        FROM data
        WHERE device = 'mobile'
        INTERSECT DISTINCT
        SELECT date,
                'Global' AS Pole, 
                web_id
        FROM data
        WHERE device = 'tablet'
    )
    GROUP BY date, Pole
),count_support as (
    SELECT date, 
            pole,
            web_id,
            COUNT(DISTINCT device) * COUNT(DISTINCT navigateur) AS nb_support
    FROM data
    GROUP BY date, web_id, Pole
    UNION ALL
    SELECT date, 
            'Global' as Pole,
            web_id, 
            COUNT(DISTINCT device) * COUNT(DISTINCT navigateur) AS nb_support
    FROM data
    GROUP BY date, pole, web_id
), count_1_support as (
    SELECT date,
            Pole, 
            COUNT(DISTINCT web_id) AS un_support
    FROM count_support
    WHERE nb_support = 1
    GROUP BY date, Pole
), count_2_support as (
    SELECT date,
            Pole, 
            COUNT(DISTINCT web_id) AS deux_support
    FROM count_support
    WHERE nb_support = 2
    GROUP BY date, Pole
), count_3_support as (
    SELECT date,
            Pole, 
            COUNT(DISTINCT web_id) AS trois_support
    FROM count_support
    WHERE nb_support = 3
    GROUP BY date, Pole
), count_4plus_support as (
    SELECT date,
            Pole, 
            COUNT(DISTINCT web_id) AS quatre_support
    FROM count_support
    WHERE nb_support > 3
    GROUP BY date, Pole

), join_data as (
    SELECT m.date,
            m.pole, 
            ifnull(MUM,0) as MUM, 
            ifnull(d.Desktop,0) as Desktop, 
            ifnull(Mobile,0) as Mobile, 
            ifnull(Tablet,0) as Tablet, 
            ifnull(Desktop_Mobile,0) as Desktop_Mobile, 
            ifnull(dt.Desktop_Tablet,0) as Desktop_Tablet, 
            ifnull(mt.Mobile_Tablet,0) as Mobile_Tablet, 
            ifnull(dmt.Desktop_Mobile_Tablet,0) as Desktop_Mobile_Tablet,
            ifnull(uns.un_support, 0) as Un_support,
            ifnull(des.deux_support, 0) as Deux_supports,
            ifnull(trs.trois_support, 0) as Trois_supports,
            ifnull(qus.quatre_support, 0) as Quatre_supports
    FROM count_mum m
    LEFT JOIN count_desktop d                         ON m.pole = d.pole AND m.date = d.date
    LEFT JOIN count_mobile mo                         ON m.pole = mo.pole AND m.date = mo.date
    LEFT JOIN count_tablet t                          ON m.pole = t.pole AND m.date = t.date
    LEFT JOIN count_desktop_and_mobile dm             ON m.pole = dm.pole AND m.date = dm.date
    LEFT JOIN count_desktop_and_tablet dt             ON m.pole = dt.pole AND m.date = dt.date
    LEFT JOIN count_mobile_and_tablet mt              ON m.pole = mt.pole AND m.date = mt.date
    LEFT JOIN count_desktop_and_mobile_and_tablet dmt ON m.pole = dmt.pole AND m.date = dmt.date
    LEFT JOIN count_1_support uns                     ON uns.pole = m.pole AND m.date = uns.date
    LEFT JOIN count_2_support des                     ON m.pole = des.pole AND m.date = des.date
    LEFT JOIN count_3_support trs                     ON m.pole = trs.pole AND m.date = trs.date
    LEFT JOIN count_4plus_support qus                 ON m.pole = qus.pole AND m.date = qus.date
)

SELECT curr.*,
        prev.date AS date_prev,
        prev.mum AS volumes_mum_prec,
        round( (curr.MUM - prev.MUM) / prev.MUM, 4) as Taux_evolution_mums
FROM join_data AS curr 
LEFT JOIN `{{ params.bq_project }}.business_data.MAUM_vue_complete_poles` AS prev ON curr.date = FORMAT_DATE('%Y-%m', DATE_ADD(PARSE_DATE('%Y-%m', prev.date), INTERVAL 1 MONTH))
                            AND curr.pole = prev.pole
ORDER BY pole, date