-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DECLARE ga4_month DATE DEFAULT DATE("2024-06-01");
DECLARE task_start_date DATE DEFAULT DATE_TRUNC(DATE_SUB(CURRENT_DATE(), INTERVAL {{ params.time_interval }}), MONTH);
DECLARE task_end_date DATE DEFAULT DATE_TRUNC(DATE_SUB(CURRENT_DATE(), INTERVAL {{ params.time_interval }}), MONTH);

CREATE TABLE IF NOT EXISTS `{{ params.bq_project_matrix }}.business_data.pmc_mam_maum_tracking` (
    month                   DATE      NOT NULL    OPTIONS(description="Analysis month in YYYY-MM-01 format"),
    brand_trigram           STRING    NOT NULL    OPTIONS(description="Brand identifier. Examples: MMA, GMI, GEN, etc. Used to identify which brand the metrics belong to"),
    support                 STRING    NOT NULL    OPTIONS(description="Platform type - 'web' for website traffic or 'app' for mobile application traffic"),
    channel                 STRING    NOT NULL    OPTIONS(description="Traffic source channel (e.g., 'Direct', 'Organic Search', 'Social', 'Email', etc.). Indicates how users reached the site/app"),
    sessions_volume         INT64                 OPTIONS(description="Total number of sessions (visits) for this brand/support/channel combination. A session is a group of user interactions within a given time frame"),
    sessions_logged_volume  INT64                 OPTIONS(description="Number of sessions from logged-in users. Helps understand authenticated user engagement"),
    page_view_volume        INT64                 OPTIONS(description="Total number of page views for this brand/support/channel. For web, counts page loads; for app, counts screen views"),
    page_view_logged_volume INT64                 OPTIONS(description="Number of page views from logged-in users. Helps analyze content engagement by authenticated users"),
    PRIMARY KEY (month, brand_trigram, support, channel) NOT ENFORCED
)
PARTITION BY DATE_TRUNC(month, MONTH)
OPTIONS(
    description="This table tracks user engagement metrics (sessions and page_views) per brand, support, channel.\n" ||
                "It combines data from both GAN (pre-June 2024) and GA4 (post-June 2024) analytics systems.\n" ||
                "Key metrics include sessions and page views, broken down by:\n" ||
                "- Authentication status (logged vs. not logged)\n" ||
                "DAG: {{ dag.dag_id }}\n" ||
                "Sync: Daily"
);

{% if params.is_full %}
    SET task_start_date = DATE_TRUNC(DATE("{{ params.default_start_date }}"), MONTH);
    SET task_end_date = DATE_TRUNC(DATE("{{ params.default_end_date }}"), MONTH);
{% endif %}

MERGE `{{ params.bq_project_matrix }}.business_data.pmc_mam_maum_tracking` AS dst
USING (
    WITH tracking_stats AS (
        -- Extract stats from GAN refined_data before ga4 migration
        SELECT
            DATE_TRUNC(visit_date, MONTH) AS month,
            COALESCE(property.brand_trigram, '(not set)') AS brand_trigram,
            CASE
                WHEN property.platform = 'mob' THEN 'app'
                WHEN property.platform IS NULL THEN '(not set)'
                ELSE property.platform
            END AS support,
            CASE
                WHEN INITCAP(visit.tracking.channel) = 'Organic' THEN 'Organic Search'
                WHEN visit.tracking.channel IS NULL THEN '(not set)'
                ELSE INITCAP(REPLACE(visit.tracking.channel, '_', ' '))
            END AS channel,
            COUNT(DISTINCT session_id) AS sessions_volume,
            COUNT(DISTINCT CASE WHEN visit.is_logged THEN session_id END) AS sessions_logged_volume,
            COUNT(DISTINCT CASE WHEN hits.type = "PAGE" THEN CONCAT(session_id, CAST(hits.number AS string)) END) AS page_view_volume,
            COUNT(DISTINCT CASE WHEN visit.is_logged AND hits.type = "PAGE" THEN CONCAT(session_id, CAST(hits.number AS string)) END) AS page_view_logged_volume
        FROM `{{ params.bq_project_matrix }}.refined_data.gan_navigation_all_visits_*`,
            UNNEST(hits) AS hits
        WHERE _TABLE_SUFFIX BETWEEN REGEXP_REPLACE(CAST(task_start_date AS STRING), '-','')
            AND REGEXP_REPLACE(CAST(task_end_date AS STRING), '-','')
            AND _TABLE_SUFFIX < REGEXP_REPLACE(CAST(ga4_month AS STRING), '-','')
        GROUP BY ALL

        UNION ALL

        -- Extract stats from GA4 refined_data web tables
        SELECT
            DATE_TRUNC(e.visit_date, MONTH) AS month,
            COALESCE(e.property_data.brand_trigram, '(not set)') AS brand_trigram,
            COALESCE(LOWER(property_data.platform), '(not set)') AS support,
            COALESCE(INITCAP(sessions.tracking_info.custom_channel_group), '(not set)') AS channel,
            COUNT(DISTINCT sessions.session_id) AS sessions_volume,
            COUNT(DISTINCT CASE WHEN pmc IS NOT NULL THEN sessions.session_id END) AS sessions_logged_volume,
            COUNTIF(events.event_name = "page_view") AS page_view_volume,
            COUNTIF(events.event_name = "page_view" AND pmc IS NOT NULL) AS page_view_logged_volume
        FROM `{{ params.bq_project_ga4 }}.refined_data{{ dag.default_args.dataset_suffix }}.ga_events_WEB_*` AS e,
            UNNEST(e.session_data) AS sessions,
            UNNEST(sessions.page_data) AS pages,
            UNNEST(event_info) AS events
            LEFT JOIN UNNEST(e.user_data.pmc_web_id) AS pmc
        WHERE DATE_TRUNC(visit_date, MONTH) BETWEEN task_start_date AND task_end_date
            AND visit_date >= ga4_month
            AND _TABLE_SUFFIX NOT LIKE "%TEMPLATE%"
            AND _TABLE_SUFFIX NOT LIKE "%_v0%"
        GROUP BY ALL

        UNION ALL

          -- Extract stats from GA4 refined_data app tables
        SELECT
            DATE_TRUNC(e.visit_date, MONTH) AS month,
            COALESCE(e.property_data.brand_trigram, '(not set)') AS brand_trigram,
            COALESCE(LOWER(property_data.platform), '(not set)') AS support,
            COALESCE(INITCAP(sessions.tracking_info.custom_channel_group), '(not set)') AS channel,
            COUNT(DISTINCT sessions.session_id) AS sessions_volume,
            COUNT(DISTINCT CASE WHEN pmc IS NOT NULL THEN sessions.session_id END) AS sessions_logged_volume,
            COUNTIF(events.event_name = "screen_view") AS page_view_volume,
            COUNTIF(events.event_name = "screen_view" AND pmc IS NOT NULL) AS page_view_logged_volume
        FROM `{{ params.bq_project_ga4 }}.refined_data{{ dag.default_args.dataset_suffix }}.ga_events_APP_*` AS e,
            UNNEST(e.session_data) AS sessions,
            UNNEST(screen_data) AS screens,
            UNNEST(event_info) AS events
            LEFT JOIN UNNEST(e.user_data.pmc_uuid) AS pmc
        WHERE DATE_TRUNC(visit_date, MONTH) BETWEEN task_start_date AND task_end_date
            AND visit_date >= ga4_month
            AND _TABLE_SUFFIX NOT LIKE "%TEMPLATE%"
            AND _TABLE_SUFFIX NOT LIKE "%_v0%"
        GROUP BY ALL
    )

    SELECT * FROM tracking_stats
) AS ref
ON
    dst.month = ref.month
    AND dst.brand_trigram = ref.brand_trigram
    AND dst.support = ref.support
    AND dst.channel = ref.channel
WHEN MATCHED THEN
    UPDATE SET
        dst.sessions_volume = ref.sessions_volume,
        dst.sessions_logged_volume = ref.sessions_logged_volume,
        dst.page_view_volume = ref.page_view_volume,
        dst.page_view_logged_volume = ref.page_view_logged_volume
WHEN NOT MATCHED THEN
    INSERT (
        month,
        brand_trigram,
        support,
        channel,
        sessions_volume,
        sessions_logged_volume,
        page_view_volume,
        page_view_logged_volume
    )
    VALUES (
        ref.month,
        ref.brand_trigram,
        ref.support,
        ref.channel,
        ref.sessions_volume,
        ref.sessions_logged_volume,
        ref.page_view_volume,
        ref.page_view_logged_volume
    );
