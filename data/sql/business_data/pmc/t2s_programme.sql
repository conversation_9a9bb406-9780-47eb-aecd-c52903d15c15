-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_email_segment.segment-sha256_t2s_programme` (
  email_sha256          STRING    NOT NULL          OPTIONS(description="email_sha256 from business_data.profile_digital_360"),
  update_date           DATE      NOT NULL          OPTIONS(description="datetime for updating data")
)
OPTIONS(description="Users who have visited TV programme grid of the T2S's website" ||
                    "\n\n" ||
                    "DAG: {{ dag.dag_id }}" ||
                    "Sync: Daily.");

TRUNCATE TABLE `{{ params.bq_project }}.store_email_segment.segment-sha256_t2s_programme`;

INSERT INTO `{{ params.bq_project }}.store_email_segment.segment-sha256_t2s_programme`
WITH t2s_programme AS (
    SELECT DISTINCT
        user.web_id
    FROM `{{ params.bq_project }}.refined_data.gan_navigation`,
        UNNEST(content.page_path) AS page_path,
        UNNEST(content.hostname) AS hostname
    WHERE
        DATE(date) >= DATE_SUB(CURRENT_DATE(), INTERVAL 90 DAY)
        AND
        hostname = "www.programme.tv" AND page_path = "/"
), profile_digital_360_sha256 AS (
    SELECT DISTINCT
        id.email_sha256,
        id.pmc_web_id AS web_id
    FROM `{{ params.bq_project }}.business_data.profile_digital_360`
)

SELECT DISTINCT
    email_sha256,
    CURRENT_DATE() AS update_date
FROM t2s_programme
JOIN profile_digital_360_sha256 USING(web_id)
