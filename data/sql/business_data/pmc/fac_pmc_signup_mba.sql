-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_email_segment.segment-sha256_fac_pmc_signup_mba` (
    email_sha256    STRING     NOT NULL     OPTIONS(description="Email"),
    update_date     DATE       NOT NULL     OPTIONS(description="Generation Date")
) OPTIONS (description="This table contains all users having signed up or logged in through FAC GoldCircle MBA PMC services since 2023-06-01\n"
              || "\n"
              || "Daily updates through the Airflow DAG '{{ dag.dag_id }}'.");

TRUNCATE TABLE `{{ params.bq_project }}.store_email_segment.segment-sha256_fac_pmc_signup_mba`;

INSERT INTO `{{ params.bq_project }}.store_email_segment.segment-sha256_fac_pmc_signup_mba`
WITH pmc_users_service_mba AS (
    SELECT DISTINCT
        pmc_uuid,
        pmc_web_id
    FROM `pm-prod-userhub.generated_data.pmc_profile_journey`,
        UNNEST(login) AS login
    WHERE (DATE(create_date) >= '2023-06-01' OR DATE(login.date) >= '2023-06-01')
        AND (
            signup_service.global IN ('FAC_GOLDCIRCLE_LANDING-PAGE_SITE-FAC_MES-BONNES-AFFAIRES', 'FAC_GOLDCIRCLE_LANDING-PAGE_NL-FAC-FIDELISATION_MES-BONNES-AFFAIRES')
            OR
            login.service IN ('FAC_GOLDCIRCLE_LANDING-PAGE_SITE-FAC_MES-BONNES-AFFAIRES', 'FAC_GOLDCIRCLE_LANDING-PAGE_NL-FAC-FIDELISATION_MES-BONNES-AFFAIRES')
        )
    )

SELECT DISTINCT
    pd36.id.email_sha256,
    CURRENT_DATE() AS update_date
FROM `pm-prod-matrix.business_data.profile_digital_360` AS pd36
JOIN pmc_users_service_mba ON pd36.id.pmc_uuid = pmc_users_service_mba.pmc_uuid
