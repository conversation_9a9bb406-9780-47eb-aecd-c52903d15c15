-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
DECLARE start_date DATE;

CREATE TABLE IF NOT EXISTS `{{ params.bq_project_matrix }}.business_data.pmc_mam_maum_signin_service` (
    month                DATE      NOT NULL    OPTIONS(description="Reference month for signin metrics. Format: YYYY-MM-01"),
    signin_service STRUCT<
        full_name                 STRING                OPTIONS(description="Complete identifier of the signin service"),
        brand_trigram             STRING                OPTIONS(description="Three-letter identifier of the brand where signin occurred "),
        content_id                STRING                OPTIONS(description="Identifier of the content/page where signin was initiated"),
        interface                 STRING                OPTIONS(description="Type of interface used for signin (web, app, desktop, mobile)"),
        core_name                 STRING                OPTIONS(description="Core service name without platform specifics"),
        source                    STRING                OPTIONS(description="Origin/source that led to the signin (direct, google, facebook, etc.)")
    > OPTIONS(description="Detailed information about the signin service with its details"),
    volume              INT64                 OPTIONS(description="Count of unique PMC users who logged in through this specific service during the month")
)
PARTITION BY DATE_TRUNC(month, MONTH)
OPTIONS(
    description="Tracks PMC users signin volume by month and by service with detailed attribution.\n" ||
                "Used for analyzing signin patterns in MAM/MAUM dashboard.\n" ||
                "DAG: {{ dag.dag_id }}\n" ||
                "Sync: Daily"
);

{% if params.is_full %}
    SET start_date = DATE_TRUNC(DATE('{{ params.default_start_date }}'), MONTH);
{% else %}
    SET start_date = DATE_TRUNC(DATE_SUB(CURRENT_DATE(), INTERVAL {{ params.time_interval }}), MONTH);
{% endif %}

-- Merge pmc accounts per signin service and per month
MERGE `{{ params.bq_project_matrix }}.business_data.pmc_mam_maum_signin_service` AS dst
USING (
    WITH
    -- Generating list of months
    generated_months AS (
        SELECT month
        FROM UNNEST(GENERATE_DATE_ARRAY(start_date, CURRENT_DATE(), INTERVAL 1 MONTH)) AS month
        ORDER BY month
    ),
    -- Extract unnested data for activities
    pmc_signin_services AS (
        SELECT
            DATE_TRUNC(login.date, MONTH) AS month,
            login.service                 AS full_name,
            login.service_brand_trigram   AS brand_trigram,
            login.service_content_id      AS content_id,
            login.service_interface       AS interface,
            login.service_name            AS core_name,
            login.source                  AS source,
            COUNT(DISTINCT pmc_uuid)      AS volume
        FROM `{{ params.bq_project_userhub }}.generated_data{{ dag.default_args.dataset_suffix }}.pmc_profile_journey`,
            UNNEST(login) AS login
        WHERE login.date >= start_date
        GROUP BY ALL
    )
    SELECT
        gm.month,
        STRUCT(
            ps.full_name,
            ps.brand_trigram,
            ps.content_id,
            ps.interface,
            ps.core_name,
            ps.source
        ) as signin_service,
        ps.volume
    FROM generated_months gm
    LEFT JOIN pmc_signin_services ps
    USING(month)
) AS ref
ON dst.month = ref.month
   AND dst.signin_service = ref.signin_service
WHEN MATCHED THEN
    UPDATE SET dst.volume = ref.volume
WHEN NOT MATCHED THEN
    INSERT(month, signin_service, volume)
    VALUES(ref.month, ref.signin_service, ref.volume);
