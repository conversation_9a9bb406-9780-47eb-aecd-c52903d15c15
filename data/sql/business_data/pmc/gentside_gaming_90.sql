-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- Create segment table
CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_email_segment.segment-sha256_gentside_gaming_90` (
    email_sha256     STRING     NOT NULL    OPTIONS(description="Email SHA256"),
    update_date      DATE       NOT NULL    OPTIONS(description="Update date")
)
OPTIONS(description="Segment 'Users with PMC account who visited the Gaming section in the last 90 days\n" ||
                    "Sync.: Daily.");

-- Delete all profiles
TRUNCATE TABLE `{{ params.bq_project }}.store_email_segment.segment-sha256_gentside_gaming_90`;

-- Insert profiles into segment table
INSERT INTO `{{ params.bq_project }}.store_email_segment.segment-sha256_gentside_gaming_90`
    SELECT DISTINCT
        pmc.email_sha256 AS email_sha256 ,
        CURRENT_DATE()   AS update_date
    FROM `{{ params.bq_project }}.refined_data.gan_navigation_cerise` AS gan -- Visits on Cerise site
    JOIN `{{ params.bq_project }}.refined_data.profile_pmc` AS pmc           -- Profile with PMC account
        ON pmc.web_id = gan.user.web_id
    WHERE DATE(date) >= DATE_SUB(CURRENT_DATE(), INTERVAL 90 DAY)            -- In the last 90 days
        AND
        category = 'Gentside Gaming'                                         -- On gaming.gentside.com
;