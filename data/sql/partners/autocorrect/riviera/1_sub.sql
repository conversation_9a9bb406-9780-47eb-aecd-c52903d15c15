-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- remove some fields cf ITDATA-2646

SELECT  
    NULL as email_md5,
    email_sha256,
    email,
    CASE WHEN gender = 'F' THEN 'MME'
    WHEN gender = 'M' THEN 'M'
    ELSE REPLACE(gender,'\\N','')
    END as gender,
    REPLACE(lastname,'\\N','') as lastname,
    REPLACE(firstname,'\\N','') as firstname,
    birthdate,
    '' as ip_address,
    REPLACE(adress1,'\\N','') as adress1,
    '' as adress2,
    REPLACE(zipcode,'\\N','') as zipcode,
    REPLACE(town,'\\N','') as town,
    '' as country,
    null as department,
    -- string
    '2000-01-01 00:00:00' as last_update,
    subscription_date    
FROM `{{ params.bq_project }}.store_partner.order_to_riviera`
WHERE order_to_sent = 'sub'
    AND email_consent_id = {{params.consent_id}}
;