-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.store_partner.autocorrect_orders_by_partner` (
    partner                     STRING NOT NULL    OPTIONS(description="Partner name (riviera, webrivage, splio)"),
    order_to_send               STRING NOT NULL    OPTIONS(description="Order type (sub, unsub)"),
    profile_master_id           INT64              OPTIONS(description="Profile master identifier"),
    email                       STRING             OPTIONS(description="Email of the profile"),
    email_sha256                STRING             OPTIONS(description="Email hash of the profile"),
    email_consent_id            INT64              OPTIONS(description="Profile master identifier"),
    email_consent_public_ref    STRING             OPTIONS(description="Public reference for the consent"),
    PRIMARY KEY(partner, order_to_send, profile_master_id, email_consent_id) NOT ENFORCED
)
CLUSTER BY partner, order_to_send, email_consent_public_ref
OPTIONS(
    description="Corrections needed for each partner based on subscription discrepancies.\n"
                || "Created/updated by the 'partners_autocorrect' DAG.",
    labels=[
        ('scope', 'autocorrect'),
        ('owner', 'nossair_errahmani'),
        ('backup_owner', 'anes_ben_ramdhan'),
        ('context', 'production')
    ]
);

-- Clear existing data for this partner
DELETE FROM `{{ params.bq_project }}.store_partner.autocorrect_orders_by_partner`
WHERE partner = '{{ params.partner_name }}';

-- Insert the sub and unsub orders
INSERT INTO `{{ params.bq_project }}.store_partner.autocorrect_orders_by_partner`

-- {{ params.partner_name|title }} discrepancies J-1
WITH discrepancies_prev AS (
    -- Profiles that should be subscribed
    (SELECT
        profile_master_id,
        email,
        email_sha256,
        {% if params.partner_name == 'splio' %}
        CASE
            WHEN ec.type = 'part' AND br.trigram NOT IN ('PMS', 'PRM', 'PMC', 'OPA', 'GAL') THEN 'nls'
            ELSE consent_public_ref
        END AS consent_public_ref,
        CASE
            WHEN ec.type = 'part' AND br.trigram NOT IN ('PMS', 'PRM', 'PMC', 'OPA', 'GAL') THEN 0
            ELSE consent_id
        END AS consent_id,
        {% else %}
        consent_public_ref,
        consent_id,
        {% endif %}
        'sub' as order_to_send
    FROM `{{ params.bq_project }}.store_partner.autocorrect_unified_user_list` au
    {% if params.partner_name == 'splio' %}
    LEFT JOIN `{{ params.bq_project }}.store_karinto.email_consent` as ec on ec.public_ref = au.consent_public_ref
    LEFT JOIN `{{ params.bq_project }}.store_karinto.brand` AS br ON br.id = ec.brand_id
    {% endif %}
    WHERE source = 'prisma'
        AND snapshot_date = 'D-1'
        AND consent_status = 'sub'
        {% for exclusion in params.exclusions.sub_filter %}
        AND {{ exclusion.field }} {{ 'LIKE' if exclusion.condition_type == 'match_pattern' else 'NOT LIKE' if exclusion.condition_type == 'exclude_pattern' else '=' if exclusion.condition_type == 'equals' else '!=' if exclusion.condition_type == 'not_equals' else '>' if exclusion.condition_type == 'greater_than' else '<' if exclusion.condition_type == 'less_than' else exclusion.condition_type }} {% if exclusion.value_type == 'string' %}r"""{{ exclusion.value }}"""{% else %}{{ exclusion.value }}{% endif %}
        {% endfor %}
    EXCEPT DISTINCT
    SELECT
        profile_master_id,
        email,
        email_sha256,
        consent_public_ref,
        consent_id,
        'sub'
    FROM `{{ params.bq_project }}.store_partner.autocorrect_unified_user_list`
    WHERE source = '{{ params.partner_name }}'
        AND snapshot_date = 'D-1'
        AND (consent_status = 'sub' OR consent_status = 'lop tech'))

    UNION ALL

    -- Profiles that should be unsubscribed
    (SELECT
        profile_master_id,
        email,
        email_sha256,
        consent_public_ref,
        consent_id,
        'unsub' as order_to_send
    FROM `{{ params.bq_project }}.store_partner.autocorrect_unified_user_list`
    WHERE source = '{{ params.partner_name }}'
        AND snapshot_date = 'D-1'
        AND consent_status = 'sub'
        {% for exclusion in params.exclusions.unsub_filter %}
        AND {{ exclusion.field }} {{ 'LIKE' if exclusion.condition_type == 'match_pattern' else 'NOT LIKE' if exclusion.condition_type == 'exclude_pattern' else '=' if exclusion.condition_type == 'equals' else '!=' if exclusion.condition_type == 'not_equals' else '>' if exclusion.condition_type == 'greater_than' else '<' if exclusion.condition_type == 'less_than' else exclusion.condition_type }} {% if exclusion.value_type == 'string' %}r"""{{ exclusion.value }}"""{% else %}{{ exclusion.value }}{% endif %}
        {% endfor %}
    EXCEPT DISTINCT
    SELECT
        profile_master_id,
        email,
        email_sha256,
        {% if params.partner_name == 'splio' %}
        CASE
            WHEN ec.type = 'part' AND br.trigram NOT IN ('PMS', 'PRM', 'PMC', 'OPA', 'GAL') THEN 'nls'
            ELSE consent_public_ref
        END AS consent_public_ref,
        CASE
            WHEN ec.type = 'part' AND br.trigram NOT IN ('PMS', 'PRM', 'PMC', 'OPA', 'GAL') THEN 0
            ELSE consent_id
        END AS consent_id,
        {% else %}
        consent_public_ref,
        consent_id,
        {% endif %}
        'unsub'
    FROM `{{ params.bq_project }}.store_partner.autocorrect_unified_user_list` au
    {% if params.partner_name == 'splio' %}
    LEFT JOIN `{{ params.bq_project }}.store_karinto.email_consent` as ec on ec.public_ref = au.consent_public_ref
    LEFT JOIN `{{ params.bq_project }}.store_karinto.brand` AS br ON br.id = ec.brand_id
    {% endif %}
    WHERE source = 'prisma'
        AND snapshot_date = 'D-1'
        AND consent_status = 'sub')
),

-- {{ params.partner_name|title }} discrepancies J
discrepancies_current AS (
    -- Profiles that should be subscribed
    (SELECT
        profile_master_id,
        email,
        email_sha256,
        {% if params.partner_name == 'splio' %}
        CASE
            WHEN ec.type = 'part' AND br.trigram NOT IN ('PMS', 'PRM', 'PMC', 'OPA', 'GAL') THEN 'nls'
            ELSE consent_public_ref
        END AS consent_public_ref,
        CASE
            WHEN ec.type = 'part' AND br.trigram NOT IN ('PMS', 'PRM', 'PMC', 'OPA', 'GAL') THEN 0
            ELSE consent_id
        END AS consent_id,
        {% else %}
        consent_public_ref,
        consent_id,
        {% endif %}
        'sub' as order_to_send
    FROM `{{ params.bq_project }}.store_partner.autocorrect_unified_user_list` au
    {% if params.partner_name == 'splio' %}
    LEFT JOIN `{{ params.bq_project }}.store_karinto.email_consent` as ec on ec.public_ref = au.consent_public_ref
    LEFT JOIN `{{ params.bq_project }}.store_karinto.brand` AS br ON br.id = ec.brand_id
    {% endif %}
    WHERE source = 'prisma'
        AND snapshot_date = 'D'
        AND consent_status = 'sub'
        {% for exclusion in params.exclusions.sub_filter %}
        AND {{ exclusion.field }} {{ 'LIKE' if exclusion.condition_type == 'match_pattern' else 'NOT LIKE' if exclusion.condition_type == 'exclude_pattern' else '=' if exclusion.condition_type == 'equals' else '!=' if exclusion.condition_type == 'not_equals' else '>' if exclusion.condition_type == 'greater_than' else '<' if exclusion.condition_type == 'less_than' else exclusion.condition_type }} {% if exclusion.value_type == 'string' %}r"""{{ exclusion.value }}"""{% else %}{{ exclusion.value }}{% endif %}
        {% endfor %}
    EXCEPT DISTINCT
    SELECT
        profile_master_id,
        email,
        email_sha256,
        consent_public_ref,
        consent_id,
        'sub'
    FROM `{{ params.bq_project }}.store_partner.autocorrect_unified_user_list`
    WHERE source = '{{ params.partner_name }}'
        AND snapshot_date = 'D'
        AND (consent_status = 'sub' OR consent_status = 'lop tech'))

    UNION ALL

    -- Profiles that should be unsubscribed
    (SELECT
        profile_master_id,
        email,
        email_sha256,
        consent_public_ref,
        consent_id,
        'unsub' as order_to_send
    FROM `{{ params.bq_project }}.store_partner.autocorrect_unified_user_list`
    WHERE source = '{{ params.partner_name }}'
        AND snapshot_date = 'D'
        AND consent_status = 'sub'
        {% for exclusion in params.exclusions.unsub_filter %}
        AND {{ exclusion.field }} {{ 'LIKE' if exclusion.condition_type == 'match_pattern' else 'NOT LIKE' if exclusion.condition_type == 'exclude_pattern' else '=' if exclusion.condition_type == 'equals' else '!=' if exclusion.condition_type == 'not_equals' else '>' if exclusion.condition_type == 'greater_than' else '<' if exclusion.condition_type == 'less_than' else exclusion.condition_type }} {% if exclusion.value_type == 'string' %}r"""{{ exclusion.value }}"""{% else %}{{ exclusion.value }}{% endif %}
        {% endfor %}
    EXCEPT DISTINCT
    SELECT
        profile_master_id,
        email,
        email_sha256,
        {% if params.partner_name == 'splio' %}
        CASE
            WHEN ec.type = 'part' AND br.trigram NOT IN ('PMS', 'PRM', 'PMC', 'OPA', 'GAL') THEN 'nls'
            ELSE consent_public_ref
        END AS consent_public_ref,
        CASE
            WHEN ec.type = 'part' AND br.trigram NOT IN ('PMS', 'PRM', 'PMC', 'OPA', 'GAL') THEN 0
            ELSE consent_id
        END AS consent_id,
        {% else %}
        consent_public_ref,
        consent_id,
        {% endif %}
        'unsub'
    FROM `{{ params.bq_project }}.store_partner.autocorrect_unified_user_list` au
    {% if params.partner_name == 'splio' %}
    LEFT JOIN `{{ params.bq_project }}.store_karinto.email_consent` as ec on ec.public_ref = au.consent_public_ref
    LEFT JOIN `{{ params.bq_project }}.store_karinto.brand` AS br ON br.id = ec.brand_id
    {% endif %}
    WHERE source = 'prisma'
        AND snapshot_date = 'D'
        AND consent_status = 'sub')
)

-- {{ params.partner_name|title }} orders - intersection of D-1 and D discrepancies
(
  SELECT
      '{{ params.partner_name }}' as partner,
      prev.order_to_send,
      prev.profile_master_id,
      prev.email,
      prev.email_sha256,
      prev.consent_id,
      prev.consent_public_ref
  FROM discrepancies_prev AS prev
  WHERE prev.profile_master_id IS NOT NULL
  INTERSECT DISTINCT
  SELECT
      '{{ params.partner_name }}' as partner,
      curr.order_to_send,
      curr.profile_master_id,
      curr.email,
      curr.email_sha256,
      curr.consent_id,
      curr.consent_public_ref
  FROM discrepancies_current AS curr
  WHERE curr.profile_master_id IS NOT NULL
)

UNION ALL

-- Add anonymized profiles
(
  SELECT DISTINCT
      '{{ params.partner_name }}' as partner,
      'unsub' AS order_to_send,
      NULL AS profile_master_id,
      CAST(NULL AS STRING) AS email,
      email AS email_sha256,
      consent_id,
      consent_public_ref
  FROM `{{ params.bq_project }}.store_partner.autocorrect_unified_user_list`
  WHERE profile_master_id IS NULL
  AND email IS NOT NULL
  AND consent_status = "sub"
  AND snapshot_date = "D"
  AND source = '{{ params.partner_name }}'
)
;
