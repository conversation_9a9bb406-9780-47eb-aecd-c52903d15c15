-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
SELECT
    email_sha256,
    email,
    CASE WHEN gender = 'F' THEN 'MME'
    WHEN gender = 'M' THEN 'M'
    ELSE REPLACE(gender,'\\N','')
    E<PERSON> as gender,
    <PERSON><PERSON><PERSON><PERSON>(lastname,'\\N','') as lastname,
    REP<PERSON><PERSON>(firstname,'\\N','') as firstname,
    birthdate,
    REPLACE(adress1,'\\N','') as adress1,
    REPLACE(zipcode,'\\N','') as zipcode,
    REPLACE(town,'\\N','') as town,
    last_update,
    subscription_date    
FROM `{{ params.bq_project }}.store_partner.order_to_webrivage`
WHERE order_to_sent = 'sub'
    AND email_consent_id = {{params.consent_id}}
;