-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

--Part 1 : Every profile that was in Webrivage and not in Prisma 2 days in a row
SELECT  
    email_sha256,
    last_update AS unsubscribe_date
FROM `store_partner.order_to_webrivage`
WHERE order_to_sent = 'unsub'
    AND email_consent_id = {{params.consent_id}}

UNION ALL

--Part 2 : Every profile that was subbed in Webrivage and not found in Prisma (no PMI)
SELECT
    wsl.email AS email_sha256, --wsl.email receives the sha256 value for cases  without a PMI match, email otherwise
    FORMAT_TIMESTAMP('%Y-%m-%d %H:%M:%S', CURRENT_DATETIME()) AS unsubscribe_date
FROM `{{ params.bq_project }}.store_partner.webrivage_snapshot_latest` AS wsl
JOIN `{{ params.bq_project }}.store_karinto.email_consent` AS kec
ON wsl.consent_public_ref = kec.public_ref
WHERE
  kec.id = {{params.consent_id}}
  AND wsl.profile_master_id IS NULL
  AND consent_status = "sub"
;
