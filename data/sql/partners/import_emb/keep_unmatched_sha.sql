-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}


-- We don't need to drop table because the table import_emb_sha is changed every mounth with new email. 

CREATE TABLE IF NOT EXISTS `store_partner.profile_emb` (
    email_sha256  STRING          OPTIONS(description="email_sha256."),
    is_prisma     BOOL            OPTIONS(description="True or flase if the profil is present inside prisma pd360"),
    gender        STRING          OPTIONS(description="gender of the profil given by the enrichement emb once this profil has a cookie from Liveramp"),
    birthdate     STRING          OPTIONS(description="birthdate of the profil given by the enrichement emb once this profil has a cookie from <PERSON>ram<PERSON>"),
    cookies       ARRAY<INTEGER>   OPTIONS(description="list of all cookiers from Liveramp"),
    create_date   DATE            OPTIONS(description="Creation date where the profil came the first time"),
    update_date   DATE            OPTIONS(description="last update date for this profile")
) OPTIONS(
  expiration_timestamp=NULL,
  description="Table where all EMB sha email are store with a field if they are in our base or not.\n"
              || "\n\n"
              || "Daily updates thru the Airflow DAG {{ dag.dag_id }}. Content dated " --|| current_run_timestamp || " CET."
);


-- We insert if the email is in `store_partner.import_emb_sha` but not in `store_partner.profile_emb`
------ We set the create_date and the update_date to current_date 
------ We check if the profile exists in our base

-- If the profile is in `store_partner.import_emb_sha` as B and `store_partner.profile_emb` as A
------ We check if the profile is in the pd360 
        --> if B.yes and A.is_prisma is True: no modif
        --> if B.yes and A.is_prisma is False: set is_prisma to True and update_date to current_date
        --> if B.no and A.is_prisma is False: no modif
        --> if B.no and A.is_prisma is True: set is_prisma to False and update_date to current_date


-- If the profile is NOT in `store_partner.import_emb_sha` BUT he is IN `store_partner.profile_emb`
------ We need to delete the profile from `store_partner.profile_emb`


-- Used to update the status if it's prisma or not and the update date
MERGE `store_partner.profile_emb` as A
USING (
        SELECT 
          lower(emb.email_sha256) as email_sha256,
          IF(pd.id.email_sha256 IS NOT NULL, True, False) AS is_prisma 
        FROM `store_partner.import_emb_sha` as emb
        LEFT JOIN `business_data.profile_digital_360` as pd ON lower(emb.email_sha256) = pd.id.email_sha256
        ) as B
ON lower(A.email_sha256) = B.email_sha256
-- If We have a profile in both table
WHEN MATCHED AND (B.is_prisma != A.is_prisma) THEN
UPDATE SET 
          A.is_prisma = B.is_prisma, -- we put the last status
          A.update_date = current_date() -- we update the date 
-- If the profils is in the B table but not in the A table
WHEN NOT MATCHED THEN 
INSERT VALUES(B.email_sha256,B.is_prisma, null, null, null, current_date(), current_date())
;


-- We delete from the table `store_partner.profile_emb` all profile who are not anymore in the table `store_partner.import_emb_sha`
DELETE FROM `store_partner.profile_emb`
WHERE email_sha256 NOT IN (SELECT lower(email_sha256) FROM `store_partner.import_emb_sha`)
;

-- We used this sql query to add cookies list into the emb profile
MERGE `store_partner.profile_emb` as A
USING(
  SELECT 
      email_emb as email_sha256,
      ARRAY_AGG(vector_id IGNORE NULLS) as cookies
  FROM `store_partner.import_emb_cookies` -- table where we store email from lr
  GROUP BY 1
) as B
ON A.email_sha256 = B.email_sha256
WHEN MATCHED THEN
  UPDATE SET 
      A.cookies = B.cookies,
      A.update_date = current_date() -- we update the date 
;

-- We used this sql query to add birthdate and gender into the emb profile
MERGE `store_partner.profile_emb` as A
USING(
  SELECT 
      email_sha256,
      civilite,
      birthdate
  FROM `store_partner.emb_full_enrichment` -- table where gender and birthdate are store after enrichment
) as B
ON A.email_sha256 = B.email_sha256
WHEN MATCHED THEN
  UPDATE SET 
      A.gender = B.civilite,
      A.birthdate = B.birthdate,
      A.update_date = current_date() -- we update the date 
;


