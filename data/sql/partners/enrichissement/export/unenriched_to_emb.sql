-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

-- FOR GENDER 
DROP TABLE IF EXISTS `{{ params.bq_project }}.{{ params.bq_dataset_store_partner }}.unenriched_to_emb_gender`;
CREATE TABLE `{{ params.bq_project }}.{{ params.bq_dataset_store_partner }}.unenriched_to_emb_gender` (
    email_sha256    STRING  NOT NULL    OPTIONS(description="email_sha256 for a profil with missing or incomplete information about civility or dob.")
) OPTIONS(
  expiration_timestamp=NULL,
  description="table used to send all email_sha256 to EMB in order to have enriched data about gender.\n"
              || "\n\n"
              || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'. "
);

INSERT INTO `{{ params.bq_project }}.{{ params.bq_dataset_store_partner }}.unenriched_to_emb_gender`
SELECT distinct email_sha256 
FROM 
  (-- select need email sha from pd 360 needed to be enriched
  SELECT id.email_sha256,
  FROM `business_data.profile_digital_360`
  WHERE info.gender is null
        OR info.relevance.gender NOT IN  ("declaratif","EMB enrichment")
  UNION ALL
  -- select all emb sha email from emb with cookies from liveramp 
  -- We need to send only emb sha email with cookies and with no known gender
  SELECT 
    a.email_sha256
  FROM `store_partner.profile_emb` as a
  -- we need the file from the previous month
  JOIN `store_partner.profile_only_emb_previous` as b ON a.email_sha256 = b.email_sha256
  WHERE array_length(cookies) != 0
        AND gender is null
  )
-- we take all profil with missing info for gender
;

-- FOR BIRTHDATE 
DROP TABLE IF EXISTS `{{ params.bq_project }}.{{ params.bq_dataset_store_partner }}.unenriched_to_emb_birthdate`;
CREATE TABLE `{{ params.bq_project }}.{{ params.bq_dataset_store_partner }}.unenriched_to_emb_birthdate` (
    email_sha256    STRING  NOT NULL    OPTIONS(description="email_sha256 for a profil with missing or incomplete information about civility or dob.")
) OPTIONS(
  expiration_timestamp=NULL,
  description="table used to send all email_sha256 to EMB in order to have enriched data about birthdate.\n"
              || "\n\n"
              || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'. "
);

INSERT INTO `{{ params.bq_project }}.{{ params.bq_dataset_store_partner }}.unenriched_to_emb_birthdate`
SELECT distinct email_sha256 
FROM 
  (-- select need email sha from pd 360 needed to be enriched
  SELECT id.email_sha256,
  FROM `business_data.profile_digital_360`
  WHERE info.birthdate is null
        OR info.relevance.birthdate NOT IN  ("declaratif","EMB enrichment")
  UNION ALL
  -- select all emb sha email from emb with cookies from liveramp 
  -- We need to send only emb sha email with cookies and with no known birthdate
  SELECT 
    a.email_sha256
  FROM `store_partner.profile_emb` as a
  -- we need the file from the previous month
  JOIN `store_partner.profile_only_emb_previous` as b ON a.email_sha256 = b.email_sha256
  WHERE array_length(cookies) != 0
        AND birthdate is null
  )
-- we take all profil with missing info for birthdate
;


----- DEAL WITH MONITORING ----- 
-- La table est déja crée
INSERT INTO `store_partner.full_event_monitoring_EMB`
-- GENDER
-- + total_volume_sent_for_gender_to_unenriched
  SELECT 
  CURRENT_DATE() AS date_of_event,
  "total_volume_sent_for_gender_to_unenriched" AS type_of_event,
  count(*) AS volume,
  LOWER(FORMAT_DATETIME("%B", DATETIME(CURRENT_DATE()))) as month,
  EXTRACT(YEAR FROM CURRENT_DATE()) AS year
  FROM store_partner.unenriched_to_emb_gender
UNION ALL 
  SELECT 
    CURRENT_DATE(),
    "volume_sent_for_gender_from_prisma_base",
    count(*),
    LOWER(FORMAT_DATETIME("%B", DATETIME(CURRENT_DATE()))) as month,
    EXTRACT(YEAR FROM CURRENT_DATE()) AS year
  FROM `business_data.profile_digital_360`
  WHERE info.gender is null
        OR info.relevance.gender NOT IN  ("declaratif","EMB enrichment")
UNION ALL
-- + volume_sent_for_gender_to_EMB_only_with_cookies
  SELECT CURRENT_DATE(), "volume_sent_for_gender_to_EMB_only_with_cookies", count(*),
          LOWER(FORMAT_DATETIME("%B", DATETIME(CURRENT_DATE()))) as month,
          EXTRACT(YEAR FROM CURRENT_DATE()) AS year
  FROM `store_partner.profile_emb` as a
  JOIN `store_partner.profile_only_emb_previous` as b ON a.email_sha256 = b.email_sha256
  WHERE array_length(cookies) != 0
        AND gender is null
UNION ALL
-- Birthdate
-- + total_volume_sent_for_birthdate_to_unenriched
  SELECT
  CURRENT_DATE() AS date_of_event,
  "total_volume_sent_for_birthdate_to_unenriched" AS type_of_event,
  count(*) AS volume,
  LOWER(FORMAT_DATETIME("%B", DATETIME(CURRENT_DATE()))) as month,
  EXTRACT(YEAR FROM CURRENT_DATE()) AS year
FROM store_partner.unenriched_to_emb_birthdate
UNION ALL 
  SELECT CURRENT_DATE(), "volume_sent_for_birthdate_from_prisma_base", count(*),
  LOWER(FORMAT_DATETIME("%B", DATETIME(CURRENT_DATE()))) as month,
  EXTRACT(YEAR FROM CURRENT_DATE()) AS year
  FROM `business_data.profile_digital_360`
  WHERE info.birthdate is null
        OR info.relevance.birthdate NOT IN  ("declaratif","EMB enrichment")
UNION ALL
-- + volume_sent_for_birthdate_to_EMB_only_with_cookies
  SELECT CURRENT_DATE(), "volume_sent_for_birthdate_to_EMB_only_with_cookies", count(*),
  LOWER(FORMAT_DATETIME("%B", DATETIME(CURRENT_DATE()))) as month,
  EXTRACT(YEAR FROM CURRENT_DATE()) AS year
  FROM `store_partner.profile_emb` as a
  JOIN `store_partner.profile_only_emb_previous` as b ON a.email_sha256 = b.email_sha256
  WHERE array_length(cookies) != 0
        AND birthdate is null

;

