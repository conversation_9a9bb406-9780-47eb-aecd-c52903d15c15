-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
-- remove for squadata : [ civilité, nom, prénom, date d'anniversaire, adresse IP (Vide), adresse postal, 
-- complément d'adresse, code postal, ville, pays, département, date de mise à jour, date d'abonnement]

SELECT 
    NULL,
    email_sha256,
    email
FROM `{{ params.generic_export_inc }}`
WHERE action = 'to_sub' AND email_consent_id = {{params.consent_id}} 
    AND last_update IS NOT NULL;
