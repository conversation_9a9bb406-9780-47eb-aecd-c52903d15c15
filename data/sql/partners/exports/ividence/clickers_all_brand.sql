-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
DROP TABLE IF EXISTS `{{ params.bq_project }}.store_partner.export_ividence`;

CREATE TABLE IF NOT EXISTS
  `{{ params.bq_project }}.store_partner.export_ividence`(
        date DATE OPTIONS(description="date de creation de la table"),
        email_sha256 STRING OPTIONS(description="email_sha256 des clickers 30 last days"),
        brand STRING OPTIONS(description="brand on which the profils clicked") ) OPTIONS(description="Create daily table where we store email_sha256 profil  for active profil last 30 days for ividence"|| "DAG: {{ dag.dag_id }}. "|| "Sync: daily at 01:15AM UTC+2");

INSERT INTO
  `{{ params.bq_project }}.store_partner.export_ividence`
SELECT
  max(last_open_date), pd360.id.email_sha256, lacb.brand_trigram
FROM `{{ params.bq_project }}.generated_data.last_activity_by_base` AS lacb
JOIN `{{ params.bq_project }}.business_data.profile_digital_360` AS pd360 ON pd360.id.email_profile_master_id = lacb.email_profile_master_id
JOIN `{{ params.bq_project }}.refined_data.email_base` AS eb ON eb.consent_public_ref = lacb.email_consent_public_ref
WHERE
  -- openers
  last_open_date IS NOT NULL
  AND
  -- last click since 37 days
  DATE(last_click_date) >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
  -- -- in which brand ? --> to uncomment in Mozart SQL if we need to parametrize the brand
  -- AND
  -- brand_trigram = " paramètre de trigramme "
  AND
  eb.consent_type = "nl"
GROUP BY 2,3
  ORDER BY
    1 asc,
    2 asc;