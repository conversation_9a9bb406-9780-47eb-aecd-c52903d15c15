-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `export_partner.gdpr_purge_riviera` (
    email         STRING                     OPTIONS(description="email")
    ) OPTIONS(
                 expiration_timestamp=NULL,
                 description="Weekly table with the profiles sent to riviera for purge.\n"
                 || "\n\n"
                 || "Daily updates through the Airflow DAG '{{ dag.dag_id }}'."
);


TRUNCATE TABLE `export_partner.gdpr_purge_riviera`;

INSERT INTO `export_partner.gdpr_purge_riviera`
-- 1) The profiles to purge (0 consents, unsubbed more than 7 days ago...) are sent every time
SELECT
  distinct TO_HEX(SHA256(email))
FROM `export_partner.gdpr_profile_to_delete`, unnest(email_consents) as inactive_consent
-- Check if those profil where sub to a part consent
-- riviera only have part consent
WHERE inactive_consent LIKE "%_part"
{% if params.is_autocorrect_run %}

-- 2) Every first run of the month, we'll send the autocorrect (profiles that are in Riviera's base and not in ours, and thus have to be purged)
UNION DISTINCT

SELECT
  DISTINCT TO_HEX(SHA256(r.email))
FROM
  `pm-prod-matrix.store_partner.riviera_snapshot_latest` r
left JOIN `pm-prod-matrix.business_data.profile_digital_360` p
on r.email = p.info.email
where p.info.email is null --
{% endif %}

;
