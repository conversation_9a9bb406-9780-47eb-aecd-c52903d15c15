-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}


CREATE TABLE IF NOT EXISTS `export_partner.gdpr_purge_webrivage` (
    email_sha256         STRING                     OPTIONS(description="email")
    ) OPTIONS(
                 expiration_timestamp=NULL,
                 description="Weekly table with profil sent to webrivage for purge.\n"
                 || "\n\n"
                 || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
);


TRUNCATE TABLE `export_partner.gdpr_purge_webrivage`;

INSERT INTO `export_partner.gdpr_purge_webrivage`
SELECT
  distinct email_hash
FROM `export_partner.gdpr_profile_to_delete`, unnest(email_consents) as inactive_consent
-- Check if those profil where sub to a part consent
-- Webrivage only have part consent
WHERE inactive_consent LIKE "%_part"
;