-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DROP TABLE IF EXISTS`{{ params.bq_project }}.export_partner.{{ params.bq_table_brand }}`;

CREATE TABLE IF NOT EXISTS `{{ params.bq_project }}.export_partner.{{ params.bq_table_brand }}`
(
    email_sha256    STRING NOT NULL    OPTIONS(description="email md5 non-clickers over 37 days for a brand" )
)
OPTIONS(description="This table contains email_sha256's that opened an email but didn't click, for brand: {{ params.brand }}."
                  ||"\n"
                  ||"It's a daily full export."
                  ||"\n\n"
                  ||"DAG: {{ dag.dag_id }}."
                  ||"Sync: daily");

INSERT INTO `{{ params.bq_project }}.export_partner.{{ params.bq_table_brand }}`

SELECT
  DISTINCT pd360.id.email_sha256
FROM
  `{{ params.bq_project }}.generated_data.last_activity_by_base` AS lacb
JOIN
  `{{ params.bq_project }}.business_data.profile_digital_360` AS pd360
ON
  pd360.id.email_profile_master_id = lacb.email_profile_master_id
JOIN
  `{{ params.bq_project }}.refined_data.email_base` AS eb
ON
  eb.consent_public_ref = lacb.email_consent_public_ref
WHERE
  -- openers
  last_open_date IS NOT NULL
  -- last click since 37 days
  AND DATE(last_click_date) <= DATE_SUB(CURRENT_DATE(), INTERVAL 37 DAY)
  -- in which brand ?
  AND lacb.brand_trigram = "{{ params.brand_trigram }}"
  -- only nl consents
  AND eb.consent_type = "nl";
