-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
SELECT
    pmi.id AS profile_master_id,
    SAFE_CAST(event_date  as TIMESTAMP) AS event_date,
    IF(event_type='opening', 'open',event_type) as event_type,
    ec.public_id as public_id,
    SAFE_CAST(webrivage_id  as INT64) AS webrivage_id,
    ec.id AS consent_id
FROM `{{params.table_import}}_{{execution_date.strftime("%Y_%m_%d")}}` AS import
JOIN `store_karinto.email_consent` AS ec ON ec.id = SAFE_CAST(import.consent_id AS INT64)
JOIN `{{params.pmi}}` AS pmi ON pmi.email_sha256 = import.email_sha256 AND char_length(import.email_sha256) = 64
GROUP BY
    pmi.id,
    event_date,
    event_type,
    public_id,
    webrivage_id,
    consent_id

