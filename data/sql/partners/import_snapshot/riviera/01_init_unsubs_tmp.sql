-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DROP TABLE IF EXISTS matrix__email_tmp.riviera_unsubs_source_{{ execution_date.strftime("%Y%m%d") }};
CREATE TABLE matrix__email_tmp.riviera_unsubs_source_{{ execution_date.strftime("%Y%m%d") }}(
    email_sha256 varchar(150),
	"unsubDate" varchar(150),
    "unsubType" varchar(150),
    "idCampaign" varchar(150),
    "campaignName" varchar(150),
    consent varchar(150)
);
ALTER TABLE matrix__email_tmp.riviera_unsubs_source_{{ execution_date.strftime("%Y%m%d") }} OWNER TO matrix_email;