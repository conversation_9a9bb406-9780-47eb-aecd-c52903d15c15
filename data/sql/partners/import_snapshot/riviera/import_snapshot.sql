-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}
DROP TABLE IF EXISTS `{{params.bq_project}}.prepare.partner_snapshot_riviera_{{ next_ds_nodash }}`;
CREATE TABLE `{{params.bq_project}}.prepare.partner_snapshot_riviera_{{ next_ds_nodash }}`
(
    profile_master_id INT64,
    email STRING,
    consent_public_ref STRING,
    consent_status STRING,
    consent_last_sub_date TIMESTAMP,
    consent_unsub_date TIMESTAMP
);

INSERT INTO `{{params.bq_project}}.prepare.partner_snapshot_riviera_{{ next_ds_nodash }}`
WITH data AS (
    SELECT
        email_sha256,
        consent_status,
        consent_last_sub_date,
        consent_unsub_date,
        SPLIT(_TABLE_SUFFIX, '-')[OFFSET(0)] AS consent_public_ref
    FROM `{{params.bq_project}}.prepare.partner_riviera_*`
    -- work with today tables only
    WHER<PERSON> SPLIT(_TABLE_SUFFIX, '-')[OFFSET(1)] like '{{ next_ds_nodash }}%'
)
SELECT
    pmg.id AS profile_master_id,
    COALESCE(pmg.email, data.email_sha256) AS email, --<-- for cases where sha256 not found in pmg table
    data.consent_public_ref,
    data.consent_status,
    data.consent_last_sub_date,
    data.consent_unsub_date
FROM data
LEFT JOIN `{{params.bq_project}}.store_matrix_email.profile_master_id` AS pmg ON data.email_sha256 = pmg.email_sha256
;

DROP TABLE IF EXISTS `{{params.bq_project}}.store_partner.riviera_snapshot`;
CREATE TABLE `{{params.bq_project}}.store_partner.riviera_snapshot`
(
    profile_master_id INT64,
    email STRING,
    consent_public_ref STRING,
    consent_status STRING,
    consent_last_sub_date TIMESTAMP,
    consent_unsub_date TIMESTAMP
);

INSERT INTO `{{params.bq_project}}.store_partner.riviera_snapshot`
SELECT *
FROM `{{params.bq_project}}.store_partner.riviera_snapshot_latest`
;

ALTER TABLE IF EXISTS `{{params.bq_project}}.store_partner.riviera_snapshot` SET OPTIONS (description='' ||
    '  Riviera database snapshot on {{ ds_nodash }}. ' ||

    ' \n\n columns : \n\n' ||
    ' \n -profile_master_id: the id of the profile on prisma ' ||
    ' \n -email: email of the profile (kept for auto-correction) ' ||
    ' \n -consent_public_ref: consent public_ref (see karinto.email_consent.public_ref) ' ||
    ' \n -consent_status:  sub status on riviera' ||
    ' \n -last_sub_date: date of sub provided by riviera ' ||
    ' \n -unsub_date: date on unsub provided by riviera' ||

    ' \n\n generated by this DAG https://j5fc9f889c8d8fba3p-tp.appspot.com/admin/airflow/graph?dag_id=partner_snapshot ' ||
    '')
;

DROP TABLE IF EXISTS `{{params.bq_project}}.store_partner.riviera_snapshot_latest`;
CREATE TABLE `{{params.bq_project}}.store_partner.riviera_snapshot_latest`
(
    profile_master_id INT64,
    email STRING,
    consent_public_ref STRING,
    consent_status STRING,
    consent_last_sub_date TIMESTAMP,
    consent_unsub_date TIMESTAMP
);

INSERT INTO `{{params.bq_project}}.store_partner.riviera_snapshot_latest`
    (profile_master_id, email, consent_public_ref, consent_status, consent_last_sub_date, consent_unsub_date)
SELECT profile_master_id, email, consent_public_ref, consent_status, consent_last_sub_date, consent_unsub_date
FROM `{{params.bq_project}}.prepare.partner_snapshot_riviera_{{ next_ds_nodash }}`
;

ALTER TABLE IF EXISTS `{{params.bq_project}}.store_partner.riviera_snapshot_latest` SET OPTIONS (description='' ||
    '  Riviera database latest snapshot on {{ next_ds_nodash }}. ' ||

    ' \n\n columns : \n\n' ||
    ' \n -profile_master_id: the id of the profile on prisma ' ||
    ' \n -email: email of the profile (kept for auto-correction) ' ||
    ' \n -consent_public_ref: consent public_ref (see karinto.email_consent.public_ref) ' ||
    ' \n -consent_status:  sub status on riviera' ||
    ' \n -last_sub_date: date of sub provided by riviera ' ||
    ' \n -unsub_date: date on unsub provided by riviera' ||

    ' \n\n generated by this DAG https://j5fc9f889c8d8fba3p-tp.appspot.com/admin/airflow/graph?dag_id=partner_snapshot ' ||
    '')
;
