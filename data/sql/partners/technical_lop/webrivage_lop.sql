-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `export_matrix_email.lop_webrivage` (
    event_date         TIMESTAMP   NOT NULL    OPTIONS(description="Creation date of the event"),
    event_type         STRING      NOT NULL    OPTIONS(description="Type of the event. enum = sub, unsub"),
    email              STRING      NOT NULL    OPTIONS(description="Email"),
    email_hash         STRING                  OPTIONS(description="Email_sha256"),
    payload            STRING                  OPTIONS(description="Additionnal information, depending on event type")
    ) OPTIONS(
                 expiration_timestamp=NULL,
                 description="Service card loyality events.\n"
                 || "\n\n"
                 || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
             );

TRUNCATE TABLE `export_matrix_email.lop_webrivage`;

INSERT INTO `export_matrix_email.lop_webrivage`
WITH webrivage_prisma AS (
SELECT
    pmi.id                                AS profile_master_id,
    pmi.email_sha256                      AS email_hash, 
    ws.email                              AS email,
    ws.consent_public_ref                 AS consent_public_ref,
    kec.id                                AS email_consent_id,
    ws.consent_status                     AS status_type,
    pec.consent_status                    AS prisma_status,
    CAST(ws.consent_unsub_date AS DATE)   AS date_status
FROM `store_partner.webrivage_snapshot` AS ws
JOIN `store_karinto.email_consent` AS kec ON kec.public_ref = ws.consent_public_ref
JOIN `store_matrix_email.profile_master_id` AS pmi ON pmi.email = ws.email
JOIN `store_matrix_email.profiles_email_consents` AS pec ON pec.profile_master_id = pmi.id AND kec.id = pec.email_consent_id
WHERE kec.type = 'part' -- just for security
) , purged_spam_only_if_sub AS (
  -- spam_reported and unsub --> Désabonner le profil du consentement part de l’univers
  -- We only send information inside the queue if the profil is sub into prisma base
  SELECT * FROM webrivage_prisma WHERE prisma_status = 'sub' AND status_type  IN ("spam_reported", "unsub")
), purged_spam AS (
    -- we create the table for unsub and spam_reported
    SELECT
    CURRENT_TIMESTAMP() AS event_date,
    'unsub' AS event_type,
    email,
    email_hash,
    status_type, --adding this in case a profil is unsub and spam_reported
    '{'
        || '"ev": 100,'
        || '"app": "mozart",'
        || '"process": "{{ dag.dag_id }}",'
        || '"source": "webrivage",'
        || '"medium": "Webrivage: lop technique - ' || MAX(status_type) || '",'
        || '"consent_ids":' || TO_JSON_STRING(ARRAY_AGG(DISTINCT email_consent_id ORDER BY email_consent_id ASC))
        || '}' AS payload
    FROM purged_spam_only_if_sub
    GROUP BY 1,2,3,4,5

), only_bounced as(
    -- NOW we will focus on bounced only
    -- pour les bounced : 
    -- Désabonner le profil des consentements part de tous les univers part
    -- We select all PMI who has a bounced in at least one consent
    SELECT distinct profile_master_id
    FROM webrivage_prisma 
    WHERE status_type = 'bounced'

), bounced_and_all_sub AS (
    -- For each pmi we selected, we match ALL consent for which this profil is sub into the prisma base
    SELECT 
        ob.profile_master_id,
        kec.public_ref,
        pec.consent_status as prisma_status,
        pec.email_consent_id
    FROM only_bounced AS ob
    LEFT JOIN `store_matrix_email.profiles_email_consents` AS pec ON pec.profile_master_id = ob.profile_master_id
    LEFT JOIN `store_karinto.email_consent` AS kec ON kec.id = pec.email_consent_id
    -- keep only part and exclude shopping part , keep only sub too
    WHERE kec.public_ref LIKE '%part' AND pec.consent_status = 'sub' AND kec.public_ref NOT LIKE '%shopping_part'

), bounced AS (
    -- we create the table for bounced profil
    SELECT
    CURRENT_TIMESTAMP() AS event_date,
    'unsub' AS event_type,
    wp.email,
    wp.email_hash,
    'bounced' AS status_type, --adding this in case a profil is purged and spam_reported
    '{'
        || '"ev": 100,'
        || '"app": "mozart",'
        || '"process": "{{ dag.dag_id }}",'
        || '"source": "webrivage",'
        || '"medium": "Webrivage: lop technique - bounced",'
        || '"consent_ids":' || TO_JSON_STRING(ARRAY_AGG(DISTINCT bas.email_consent_id ORDER BY bas.email_consent_id ASC))
        || '}' AS payload

    FROM bounced_and_all_sub AS bas
    LEFT JOIN webrivage_prisma AS wp on bas.profile_master_id = wp.profile_master_id 
    WHERE status_type IN ("bounced")
    GROUP BY 1,2,3,4,5

), only_opposition as(
    -- NOW we will focus on opposition from the log and 
    -- We will apply same actions than bounce status
    SELECT distinct profile_master_id
    FROM `store_partner.snapshot_log_webrivage` 
    WHERE reason = "opposition" -- and  timedate = '{{ yesterday_ds }}' not useful

), opposition_and_all_sub AS (
    -- For each pmi we selected, we match ALL consent for which this profil is sub into the prisma base
    SELECT 
        oo.profile_master_id,
        kec.public_ref,
        pec.consent_status as prisma_status,
        pec.email_consent_id
    FROM only_opposition AS oo
    LEFT JOIN `store_matrix_email.profiles_email_consents` AS pec ON pec.profile_master_id = oo.profile_master_id
    LEFT JOIN `store_karinto.email_consent` AS kec ON kec.id = pec.email_consent_id
    -- keep only part and exclude shopping part , keep only sub too
    WHERE kec.public_ref LIKE '%part' AND pec.consent_status = 'sub' AND kec.public_ref NOT LIKE '%shopping_part'

), opposition AS (
    -- we create the table for bounced profil
    SELECT
    CURRENT_TIMESTAMP() AS event_date,
    'unsub' AS event_type,
    pmi.email,
    pmi.email_sha256 as email_hash,
    'opposition' AS status_type, --adding this in case a profil is purged and spam_reported
    '{'
        || '"ev": 100,'
        || '"app": "mozart",'
        || '"process": "{{ dag.dag_id }}",'
        || '"source": "webrivage",'
        || '"medium": "Webrivage: lop technique - opposition",'
        || '"consent_ids":' || TO_JSON_STRING(ARRAY_AGG(DISTINCT oaas.email_consent_id ORDER BY oaas.email_consent_id ASC))
        || '}' AS payload

    FROM opposition_and_all_sub AS oaas
    LEFT JOIN `store_matrix_email.profile_master_id` AS pmi ON pmi.id = oaas.profile_master_id
    GROUP BY 1,2,3,4,5
)


--We only take 50 000 rows to start the process
-- 25000 for purged and spam_reported
-- 25000 for bounced

(SELECT * except(status_type) FROM purged_spam LIMIT 25000)
UNION ALL
(SELECT * except(status_type) FROM bounced LIMIT 15000)
UNION ALL
(SELECT * except(status_type) FROM opposition LIMIT 10000)

-- For verification of the count of unsub
--SELECT count(pur),'purged' as type FROM purged_spam,UNNEST(JSON_QUERY_ARRAY(payload , '$.consent_ids')) as pur WHERE status_type = 'purged'
--UNION ALL 
--SELECT count(spa),'spam_reported' as type FROM purged_spam,UNNEST(JSON_QUERY_ARRAY(payload , '$.consent_ids')) as spa WHERE status_type = 'spam_reported'
--UNION ALL
--SELECT count(bou),'bounced' as type FROM bounced,UNNEST(JSON_QUERY_ARRAY(payload , '$.consent_ids')) as bou
