-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

CREATE TABLE IF NOT EXISTS `export_matrix_email.lop_partners_event` (
    event_date         TIMESTAMP   NOT NULL    OPTIONS(description="Creation date of the event"),
    event_type         STRING      NOT NULL    OPTIONS(description="Type of the event. enum = sub, unsub"),
    email              STRING      NOT NULL    OPTIONS(description="Email"),
    email_hash         STRING                  OPTIONS(description="Email_sha256"),
    payload            STRING                  OPTIONS(description="Additionnal information, depending on event type")
    ) OPTIONS(
                 expiration_timestamp=NULL,
                 description="Service card loyalty events.\n"
                 || "\n\n"
                 || "Daily updates thru the Airflow DAG '{{ dag.dag_id }}'."
             );

TRUNCATE TABLE `export_matrix_email.lop_partners_event`;

INSERT INTO `export_matrix_email.lop_partners_event`
SELECT * FROM `export_matrix_email.lop_webrivage`;


MERGE `export_matrix_email.lop_partners_event` AS LW
USING `export_matrix_email.lop_riviera` AS LR
ON CONCAT(LW.email,ARRAY_TO_STRING(JSON_QUERY_ARRAY(LW.payload , '$.consent_ids'),','))
    = CONCAT(LR.email,ARRAY_TO_STRING(JSON_QUERY_ARRAY(LR.payload , '$.consent_ids'),','))
WHEN NOT MATCHED THEN
    INSERT (event_date, event_type,email,email_hash,payload) VALUES(LR.event_date,LR. event_type,LR.email,LR.email_hash,LR.payload)
