-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}

DROP TABLE IF EXISTS  `{{ params.bq_project }}.prepare.qualifio_campaign_{{ next_ds_nodash }}`;
CREATE TABLE IF NOT EXISTS  `{{ params.bq_project }}.prepare.qualifio_campaign_{{ next_ds_nodash }}`
(
    campaign_id     INTEGER   NOT NULL      OPTIONS(description="campaign id extracted from the payload. "),
    campaign_name   STRING                  OPTIONS(description="camapign name extracted from the payload. "),
    start_date      TIMESTAMP NOT NULL      OPTIONS(description="campaign start datetime extracted from the payload."),
    end_date        TIMESTAMP               OPTIONS(description="campaign end datetime extracted from the payload. "), 
    payload         STRING                  OPTIONS(description="payload content")  

)
OPTIONS(description="this table contains prepared data to be exported from import to prepare. "||
                    "DAG: {{dag.dag_id}}. "||
                    "Sync: daily");

INSERT INTO  `{{ params.bq_project }}.prepare.qualifio_campaign_{{ next_ds_nodash }}`
SELECT DISTINCT
    SAFE_CAST(JSON_EXTRACT_SCALAR(payload, '$.campaign.campaignId') AS INT64)       AS campaign_id,
    JSON_EXTRACT_SCALAR(payload, '$.campaign.campaignTitle')                        AS campaign_name,
    SAFE_CAST(JSON_EXTRACT_SCALAR(payload, '$.schedule.startDate') AS TIMESTAMP)    AS start_date,
    SAFE_CAST(JSON_EXTRACT_SCALAR(payload, '$.schedule.endDate') AS TIMESTAMP)      AS end_date,
    REPLACE(REPLACE(REPLACE(payload, "\\'", '"'), '="', "=\""), 'False', '"False"') AS payload
FROM  `{{ params.bq_project }}.import.qualifio_campaign_{{ next_ds_nodash }}`;
