from __future__ import print_function

import csv
import os.path
import pickle
import sys

from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build

# Preprod
# gsutil cp data/scripts/postmaster_api_call.py gs://europe-west1-prod-mozart-co-fd73c443-bucket/data/scripts/
# developer doc: https://developers.google.com/gmail/postmaster/quickstart/python?hl=fr&authuser=1

csv_file = sys.argv[1]
token_file = sys.argv[2]
credential_file = sys.argv[3]

# If modifying these scopes, delete the file token.pickle.
SCOPES = ['https://www.googleapis.com/auth/postmaster.readonly']


# To launch it python deliv_3.py | jq

def list_traffic_stats(service, domain_name, page_size):
    # Recuperation d'un full from today to yyyy:mmm:dd
    list_traffic_stats_response = service.domains().trafficStats().list(
        parent=domain_name, pageSize=page_size).execute()
    # A = list_traffic_stats_response['trafficStats']
    # test = [int(A[i]['name'][-8:]) for i in range(len(A))]
    return list_traffic_stats_response


def main(csv_file, token_file):
    """
    Shows basic usage of the PostmasterTools v1beta1 API.
    Prints the visible domains on user's domain dashboard in https://postmaster.google.com/managedomains.
    """
    creds = None
    # The file token.pickle stores the user's access and refresh tokens, and is
    # created automatically when the authorization flow completes for the first
    # time.
    print(os.getcwd())
    if os.path.exists(token_file):
        print("Token was found")
        with open(token_file, 'rb') as token:
            creds = pickle.load(token)
            # Careful, we need the original token (it can be copy)
    # If there are no (valid) credentials available, let the user log in.
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
            print("Token was refreshed")
        else:
            print("We need to regenerate a new token file")
            # We don't need the credentials files.
            flow = InstalledAppFlow.from_client_secrets_file(
                credential_file, SCOPES)
            creds = flow.run_local_server(port=0)

        # Save the credentials for the next run
        with open(token_file, 'wb') as token:
            pickle.dump(creds, token)

    service = build('gmailpostmastertools', 'v1beta1', credentials=creds)

    domains = service.domains().list(pageSize=500).execute()
    print('domains : ' + str(domains))
    if not domains:
        print('No domains found.')
    else:
        i = 0
        for domain in domains['domains']:
            print('domain : ' + str(domain))
            domain_full = domain['name'].split('/')[1]
            type_domain = domain_full.split('.')[0]
            domain_name = domain_full.split('.')[1].split('-')[0]

            try:

                s = list_traffic_stats(service, domain_name=domain['name'], page_size=200)

                csv_columns = ['name', 'userReportedSpamRatio',
                               'ipReputations', 'domainReputation',
                               'spammyFeedbackLoops', 'spfSuccessRatio',
                               'dkimSuccessRatio', 'dmarcSuccessRatio',
                               'inboundEncryptionRatio', 'deliveryErrors',
                               'date', 'domain_name', 'type_domain']

                if 'trafficStats' in s:
                    dict_data = list(s['trafficStats'])
                    with open(csv_file, 'a') as csvfile:  # mettre un a pour le append
                        writer = csv.DictWriter(csvfile, fieldnames=csv_columns)
                        for data in dict_data:
                            data['date'] = data['name'].split('/')[3]
                            data['domain_name'] = domain_name
                            data['type_domain'] = type_domain
                            writer.writerow(data)
                        # i = i + 1

                else:
                    print('No Traffic Stats for:', domain_full)

            except:
                print('Requested entity was not found:', domain_full)

            # if we only want 3 different domains
            # if i == 3 : break


main(csv_file, token_file)
