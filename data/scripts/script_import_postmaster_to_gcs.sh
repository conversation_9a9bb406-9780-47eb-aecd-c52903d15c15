#!/bin/bash

# gsutil cp data/scripts/script_import_postmaster_to_gcs.sh gs://europe-west1-prod-mozart-co-fd73c443-bucket/data/scripts/
# gsutil cp data/resources/postmaster_token/token.pickle gs://europe-west1-prod-mozart-co-fd73c443-bucket/data/resources/postmaster_token/

python_file="{{ params.python_file }}"
gcs_output_file="gs://{{ params.bucket }}/{{ params.postmaster_dir }}/{{ ds_nodash }}/{{ params.import_data }}/{{ ds_nodash }}_{{ params.import_filename }}"
local_file="/home/<USER>/gcs/data/tmp/postmaster_import.csv"
token_file="/home/<USER>/gcs/data/resources/postmaster_token/token.pickle"
credential_file="/home/<USER>/gcs/data/resources/postmaster_token/client_secret_465148673646-c1739ei<PERSON>l30toun5u8aasslrivk15ks.apps.googleusercontent.com.json"

echo "${python_file}"
echo "${gcs_output_file}"

# rm if exist
[ -f "${local_file}" ] && rm -f "${local_file}"

mkdir -p "/home/<USER>/gcs/data/tmp/"
touch "${local_file}"

# Create the local file
python /home/<USER>/gcs/data/scripts/"${python_file}" "${local_file}" "${token_file}" "${credential_file}"

# local to gcs
gsutil cp "${local_file}" "${gcs_output_file}"

# rm if exist
[ -f "${local_file}" ] && rm -f "${local_file}"
