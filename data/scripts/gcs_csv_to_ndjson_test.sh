#!/bin/bash

gcs_input_file="gs://{{ params.gcs_bucket }}/{{ params.mics_dir }}/{{ ds_nodash }}/{{ params.source }}/{{ params.gcs_input_file }}"
gcs_output_file="gs://{{ params.gcs_bucket }}/{{ params.mics_dir }}/{{ ds_nodash }}{{ params.target }}"


for gcs_file in $( gsutil ls "${gcs_input_file}" ); do
    # clean local files
    filename=$( basename "${gcs_file}" )
    output_local_file="/tmp/$( echo "${filename}" )"
    [ -f "${output_local_file}" ] && rm -f "${output_local_file}"

    # get gcs files content, make sed and store the result
    gsutil cat "${gcs_file}" \
        | sed -e 's/"{/{/g; s/}"/}/g; s/""/"/g;' \
        > "${output_local_file}"

    file_name="$(echo "${filename}" | cut -d"." -f1).json"
    # copy local files to gcs storage
    gsutil cp "${output_local_file}" "${gcs_output_file}/${file_name}"

done
