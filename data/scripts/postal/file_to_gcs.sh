#!/bin/bash

day="{{ next_execution_date.strftime('%Y%m%d') }}"
bucket="{{ params.bucket }}"
path="{{ params.path }}"
file="{{ params.file }}"
datetype="{{ params.datetype }}"

if [ -z "$bucket" ]
then
	echo "\$bucket param not found"
	exit 1
fi

if [ -z "$path" ]
then
	echo "\$path param not found"
	exit 1
fi

if [ -z "$file" ]
then
	echo "\$file param not found"
	exit 1
fi

case "${datetype}" in
"day_nodash" )
  file_date="{{ next_execution_date.strftime('%Y%m%d') }}"
  ;;
"month_dash" )
  file_date="{{ next_execution_date.strftime('%Y-%m') }}"
  ;;
* )
  file_date="{{ next_execution_date.strftime('%Y-%m-%d') }}"
  ;;
esac

airflow_dirname="/home/<USER>/gcs/data/tmp"
archive=$(echo ${file} | sed "s/%-date-%/${file_date}/g")

gsutil mv "${airflow_dirname}/${archive}" "gs://${bucket}/${path}/${day}/"
