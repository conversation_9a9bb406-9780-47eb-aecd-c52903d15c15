#!/bin/bash

day="{{ next_execution_date.strftime('%Y%m%d') }}"
bucket="{{ params.bucket }}"
path="{{ params.path }}"
file="{{ params.file }}"
header="{{ params.header }}"

if [ -z "$day" ]
then
	echo "\$day param not found"
	exit 1
fi

if [ -z "$bucket" ]
then
	echo "\$bucket param not found"
	exit 1
fi

if [ -z "$path" ]
then
	echo "\$path param not found"
	exit 1
fi

if [ -z "$file" ]
then
	echo "\$file param not found"
	exit 1
fi

if [ -z "$header" ]
then
	echo "\$header param not found"
	exit 1
fi

airflow_dirname="/home/<USER>/gcs/data/tmp"
filename="${file}${day}.csv"

rm -f "${airflow_dirname}/${filename}" "${airflow_dirname}/transaction_*.csv"

echo "${header}" > "${airflow_dirname}/${filename}"
gsutil cat "gs://${bucket}/${path}/${day}/transaction_*.csv" >> "${airflow_dirname}/${filename}"
gsutil cp "${airflow_dirname}/${filename}" "gs://${bucket}/${path}/${day}/"
