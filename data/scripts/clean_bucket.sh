#!/bin/bash

OLD_IFS=$IFS
IFS='
'

echo "Clean files created a month ago"

LINUX=true

if [[ "$LINUX" = "true" ]]; then
  today=$(date  +%Y%m%d )
  last_month=$(date --date "1 month ago" +%Y%m%d )
else
  # for macOs
  today=$(date +%Y%m%d)
  last_month=$(date -v-1m +%Y%m%d)
fi

echo "Reference date : $last_month"


BUCKET_URL="gs://$GCS_BUCKET"
echo $BUCKET_URL

tmp_data=$(gsutil ls -l $BUCKET_URL/data/tmp/** | sort -k2 | grep -E ".(gz|csv|txt|log)")

echo 'Process tmp files:'

deleted_count=0


log_file="/home/<USER>/gcs/data/tmp/clean_bucket_log_$today.log"
if [ -e "$log_file" ] ; then
  # truncate file
  echo -n "" > "$log_file"
fi

for tmp in $tmp_data
  do
    # echo "$tmp"
    date=$(echo "$tmp" | awk '{print $2}' | grep -Eo "[0-9]{4}\-[0-9]{2}\-[0-9]{2}" | tr -d "-") # 2022-03-15T10:19:01Z
    uri=$(echo "$tmp" | awk '{print $3}')
    # echo "$uri"
    # echo "$date"
    if [[ "$last_month" > "$date" ]] && [[ ${uri} != *"unmatched_element"* ]]; then
      if [ ! -e "$log_file" ] ; then
        touch "$log_file"
      fi
      deleted_count=$((deleted_count+1))
      echo " --> will delete file : $uri "
      $(gsutil rm -rf "$uri")
      echo " --> delete file : $uri " >> "$log_file"
    fi

  done

if [ "$deleted_count" -gt "0" ]; then
  echo "Number of deleted files: $deleted_count"
  echo "See deleted files list: gs://$GCS_BUCKET/data/tmp/clean_bucket_log_$date.log"
fi

