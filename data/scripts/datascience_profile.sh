#!/bin/bash

# Script to be uploaded to data/script folder on mozart gcs 

# gsutil cp production/data/scripts/datascience_profile.sh gs://europe-west1-mozart-cluster-4128dbb8-bucket/data/scripts/


if [  $# -ne 2  ]
then
	me=`basename "$0"`
	echo "Bash [$me]: No arguments supplied"
	exit 1
fi

gcs="gs://it-data-prod-matrix-pipeline"
airflow_dirname="/home/<USER>/gcs/data/tmp/"
directory="datascience/profile"
day=$1
header_data=$2
filename="export_profile_${day}_.csv"

echo "Create header file : $airflow_dirname/$filename with $header_data contents"
echo $header_data > $airflow_dirname/$filename
cd $airflow_dirname
gzip $filename

echo "GCS copy $airflow_dirname/$filename.gz to $gcs/$directory/"
gsutil cp "$airflow_dirname/$filename.gz" $gcs/$directory/ 
echo "GCS compose $gcs/$directory/export_profile_${day}_*.csv.gz  $gcs/$directory/export_profile_$day.csv.gz"
gsutil compose $gcs/$directory/export_profile_${day}_*.csv.gz  $gcs/$directory/export_profile_$day.csv.gz
echo "GCS rm $gcs/$directory/export_profile_${day}_*.csv.gz"
gsutil rm $gcs/$directory/export_profile_${day}_*.csv.gz
echo "GCS rm $gcs/$directory/export_profile_.csv.gz"
gsutil rm $gcs/$directory/export_profile_.csv.gz
echo "Local file : rm $airflow_dirname/$filename.gz"
rm "$airflow_dirname/$filename.gz"