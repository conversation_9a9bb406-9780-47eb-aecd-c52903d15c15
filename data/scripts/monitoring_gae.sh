#!/bin/bash

# Script to be uploaded to data/script folder on mozart gcs 

# gsutil cp production/data/scripts/monitoring_gae.sh gs://europe-west1-mozart-cluster-4128dbb8-bucket/data/scripts/

filename=/home/<USER>/gcs/data/tmp/monitoring_gcp.log
if [ ! -f $filename ]
then
    touch $filename
fi

echo -n "" > $filename

for project in $( gcloud projects list | awk '{print $1}' | grep '^pm-prod' ); do
# gcloud app versions list
# result format :
# SERVICE  VERSION          TRAFFIC_SPLIT  LAST_DEPLOYED              SERVING_STATUS
# default  20190507t111727  0.00           2019-05-07T11:22:04+02:00  SERVING
# default  20190507t141414  1.00           2019-05-07T14:18:37+02:00  SERVING
gcloud app versions list --project="$project"  2>/dev/null \
| while read line ; do

service=$( echo "$line" | tr -s ' ' | cut -d' ' -f 1 )
if [[ $service = "SERVICE" ]]; then
continue;
fi
# get each column
version=$( echo "$line" | tr -s ' ' | cut -d' ' -f 2 )
traffic_split=$( echo "$line" | tr -s ' ' | cut -d' ' -f 3 )
serving_status=$( echo "$line" | tr -s ' ' | cut -d' ' -f 5 )

# if SERVING with 0% traffic !
if [[ "$serving_status & $traffic_split" = "SERVING & 0.00" ]]; then
## get length of $distro array
echo $project' service:' $service' version:'$version' <b><a href="https://console.cloud.google.com/appengine/versions?project='$project'&serviceId='$service'&versionssize=50" target="_blank"> go to project </a></b> ;' >> $filename

fi
done
done
