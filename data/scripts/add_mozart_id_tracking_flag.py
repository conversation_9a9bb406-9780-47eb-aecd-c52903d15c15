"""
This Python script allows us to:

1.   Parse each SQL file content's
2.   Detect with a regular expression each DML/DLL SQL statement
3.   Add '-- mozart-id : {{ dag.dag_id }}.{{ task.task_id }}' flag after each SQL statement

"""

import os
import re
import glob
import logging


def init__logger():
	"""
		Setup logging configuration.
	"""
	# Setup logging
	logging.basicConfig(
		level=logging.INFO,
		format='%(asctime)s - %(levelname)s - %(message)s'
	)
	logger = logging.getLogger(__name__)
	return logger


def insert_mozart_id(sql_script, dag_id, task_id):
	"""
		Insert "mozart-id" comment after each SQL statement.

		Parameters:
		- sql_script (str): The SQL script to be modified.
		- dag_id (str): The DAG ID.
		- task_id (str): The task ID.

		Returns:
		- str: The modified SQL script

	"""

	# Create the mozart-id string
	mozart_id_comment = f'-- mozart-id : {dag_id}.{task_id}'

	# Split the SQL script into lines
	lines = sql_script.splitlines()

	# This list will hold the modified lines
	modified_script = []

	# Init list
	modified_script.append(lines[0])

	# Loop over each line in the script
	for (i, line) in enumerate(lines):

		# Skip first line
		if i == 0: continue

		# Fetch current line
		current_line = lines[i]

		# Add current line
		if not current_line.strip().startswith("-- mozart-id : "):
			modified_script.append(current_line)

		# Fetch next line
		next_line = lines[i + 1] if i + 1 > len(lines) else "error !"

		# Add mozart-id if current line is SQL statement, and it's not succeeded by the mozart-id comment
		if (re.match(r"^(CREATE OR REPLACE TABLE|CREATE OR REPLACE VIEW|INSERT INTO|DELETE FROM|MERGE|MERGE INTO)",
								current_line.strip(), re.IGNORECASE) and not next_line.strip().startswith("-- mozart-id : ")):
			# Insert the mozart-id comment after the SQL statement
			modified_script.append(mozart_id_comment)

	# Join the modified lines into a single script
	return '\n'.join(modified_script)


def read_file(file_path):
	"""
		Read file contents.

		Parameters:
		- file_path (str): The path to the file.

		Returns:
		- file_contents (str): The file contents.

	"""
	# init logger
	logger = init__logger()
	try:
		with open(file_path, 'r') as file:
			file_content = file.read()
			return file_content
	except Exception as e:
		logger.error(f"Failed to read file at this path {file_path}: {str(e)}")


def write_file(file_path, content):
	"""
		Write content to a file.

		Parameters:
		- file_path (str): The path to the file.
		- content (str): content to be written.

	"""
	# init logger
	logger = init__logger()
	try:
		with open(file_path, 'w') as file:
			file.write(content)
	except Exception as e:
		logger.error(f"Failed to write in file at this path {file_path}: {str(e)}")


def process_sql_folder(folder_path, dag_id, task_id):
	"""
		Process all SQL files in a folder and insert "mozart-id" comments.

		Parameters:
		- folder_path (str): The path to the folder containing SQL files.
		- dag_id (str): The DAG ID.
		- task_id (str): The task ID.

	"""

	# Check if folder_path is already an absolute path or relative
	full_folder_path = os.path.join(os.getcwd(), folder_path) if os.path.isabs(folder_path) else folder_path
	print(f"Full Folder Path: {full_folder_path}")

	# List all SQL files inside the folder if full_folder_path is a directory
	sql_files = glob.glob(os.path.join(full_folder_path, '**', '*.sql'), recursive=True) if os.path.isdir(
		full_folder_path) else [full_folder_path]

	if not sql_files:
		print("No sql files found !!")
		return False

	# Process SQL files
	for file_path in sql_files:
		print(f"File to be processed: {file_path}")
		# Read the original SQL file
		sql_script = read_file(file_path)
		if not sql_script: exit()
		# Modify the SQL script by adding the mozart-id comment
		modified_sql_script = insert_mozart_id(sql_script, dag_id, task_id)
		# Write the modified SQL back to the file or print it
		write_file(file_path, modified_sql_script)
		print(f"Processed file: {file_path}")


if __name__ == "__main__":
	# Set a Folder Path
	folder_path = input("Please enter the folder containing SQL files: ")
	print("You entered:", folder_path)

	# Set a Dag-id
	dag_id = input("Please enter dag-id flag as {{ dag.dag_id }}: ")
	print("You entered:", dag_id)

	# Set a Task-id
	task_id = input("Please enter task-id flag as {{ task.task_id }}: ")
	print("You entered:", task_id)

	# Process all SQL files inside the folder
	process_sql_folder(folder_path, dag_id, task_id)
