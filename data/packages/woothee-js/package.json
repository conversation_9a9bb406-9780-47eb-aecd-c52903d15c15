{"name": "woothee", "version": "1.11.0", "description": "User-Agent string parser (js implementation)", "main": "./release/woothee", "devDependencies": {"chai": ">= 1.3.0", "js-yaml": ">= 1.0.3", "mocha": "^6.0.0", "serialize-to-js": "^3.0.0", "should": "~1.2.2"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "https://github.com/woothee/woothee-js"}, "author": "<PERSON><PERSON><PERSON>", "license": "Apache-2.0"}