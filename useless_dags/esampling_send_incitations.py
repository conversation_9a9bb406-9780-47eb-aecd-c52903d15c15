###########
# gsutil cp dags/esampling_send_incitations.py gs://europe-west1-mozart-cluster-d3558445-bucket/dags/
############

import os
from airflow import DAG
from airflow.models import Variable
from esampling_plugin.operators.esampling_incitation_operator import EsamplingIncitationOperator
from datetime import timedelta, datetime

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']

bq_project = 'pm-prod-matrix'

email_on_failure = True
# ------------- FOR PREPROD ENV -----------------
env = os.environ.get("ENV")
env_str = ''
tmail_api_uri = 'https://tmail.prismadata.fr'
if env != 'prod':
    env_str = '_' + env
    tmail_api_uri = 'https://tmail.preprod.prismadata.fr'
    email_on_failure = False
    bq_project = 'pm-preprod-matrix'
# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2021, 5, 3, 12, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=3),
    'sla': None,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=1),
    'depends_on_past': False,

    # esampling config
    'database': 'matrix',
    'schema': 'esampling',
}

esampling_export_params = Variable.get("esampling_export_params", deserialize_json=True)

with DAG(
        'esampling_send_welcome_email' + env_str,
        schedule_interval="*/20 * * * *",
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=3),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/esampling/incitations/']
) as dag_welcome:
    # ---------------------------------------------------------
    # -- send welcome-email NEWS-2283
    # -- Trigger :  arrivée en base dans le statut Inscrit
    # ---------------------------------------------------------
    welcome_email = EsamplingIncitationOperator(
        task_id='welcome_email',
        esampling_conn_id='psql_esampling_app',
        template_name='welcome_email',
        sql_get_active_campaigns='0_get_active_campaigns.sql',
        sql_get_qualifio_contacts='1_get_qualifio_profiles_for_welcome_email.sql',
        sql_insert_contact_incitation='2_insert_contact_incitation.sql',
        sql_update_contact_status=None,
        tmail_api_uri=tmail_api_uri,
        # campaign_refs_ignored : liste des public ref ignorés
    )

with DAG(
        'esampling_send_sample' + env_str,
        schedule_interval="11 */4 * * *",
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=3),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/esampling/incitations/']
) as dag_sample:
    # ---------------------------------------------------------
    # -- send réception échantillon NEWS-2282
    # -- Trigger : lorsque l'utilisateur arrive dans l'état "en attente échantillon"
    # ---------------------------------------------------------
    echantillon_email = EsamplingIncitationOperator(
        task_id='echantillon_email',
        esampling_conn_id='psql_esampling_app',
        template_name='echantillon_email',
        sql_get_active_campaigns='0_get_active_campaigns.sql',
        sql_get_qualifio_contacts='1_get_qualifio_profiles_for_send_sample.sql',
        sql_insert_contact_incitation='2_insert_contact_incitation.sql',
        sql_update_contact_status='3_update_contact_status_after_send_sample.sql',
        tmail_api_uri=tmail_api_uri,
        params={
            'interval': esampling_export_params['incitation_interval']
        },
        # campaign_refs_ignored : liste des public ref ignorés
    )

with DAG(
        'esampling_send_incitation' + env_str,
        schedule_interval="11 */4 * * *",
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(hours=3),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/esampling/incitations/']
) as dag_incitation:
    # ---------------------------------------------------------
    # -- send incitation-noter ITDATA-436
    # -- Tous les soirs envoyer les incitations à noter aux contacts qui doivent le recevoir
    # -- règle à définir (X jour après traitement côté Holy Sampling) :
    # -- on met 1 jour le temps de recetter, ce paramètre pourra changer
    # -- (mais c'est un paramètre global, on ne souhaite pas le gérer à la campagne près)
    # ---------------------------------------------------------
    incitation_email = EsamplingIncitationOperator(
        task_id='incitation_email',
        esampling_conn_id='psql_esampling_app',
        template_name='incitation_email',
        sql_get_active_campaigns='0_get_active_campaigns.sql',
        sql_get_qualifio_contacts='1_get_qualifio_profiles_for_send_incitation.sql',
        sql_insert_contact_incitation='2_insert_contact_incitation.sql',
        sql_update_contact_status=None,
        tmail_api_uri=tmail_api_uri,
        params={
            'interval': esampling_export_params['incitation_interval']
        },
        # campaign_refs_ignored : liste des public ref ignorés
    )
