r"""
**PURPOSE:**<br />

This DAG generates unsub events from gala consents for profiles not transmitted to Le Figaro (2023-10-21)<br />

__METHODOLOGY:__<br />

generate unsub events from gala consents <br />
export unsub events to postgres email_event<br />

"""
import os
from datetime import timedelta, datetime

from airflow.models import Variable
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from collapsible_doc_dag import CDocDAG
from matrix_plugin.operators.bigquery_to_email_event import BigQueryToEmailEventOperator

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']
env = os.environ.get("ENV")

bq_project = 'pm-{}-matrix'.format(env)

# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2024, 7, 1, 00, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': False if env != 'prod' else False,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=10),
    'sla': None,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=1),
    'depends_on_past': False,

    # GCP
    'gcp_conn_id': 'gcs_matrix',
    'bucket': matrix_bucket,

    # BigQuery
    'bigquery_conn_id': 'bq_matrix',
    'bq_project': bq_project,
    'write_disposition': 'WRITE_TRUNCATE',
    'use_legacy_sql': False,
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',

    # PSQL Connection
    'postgres_conn_id': 'psql_matrix_email_app',
    'database': 'matrix',
}

with CDocDAG(f'matrix__email_gala_unsub_profiles{"_" + env if env != "prod" else ""}',
             schedule_interval='15 */4 * * *',
             default_args=default_args,
             max_active_runs=1,
             tags=["gala", "temporary"],
             dagrun_timeout=timedelta(hours=1),
             catchup=False,
             description='Generate unsub events from gala consents for profiles not transmitted to Le Figaro',
             doc_md=__doc__,
             template_searchpath=['/home/<USER>/gcs/data/sql/matrix_email']) as dag:
    generate_unsub_events = BigQueryExecuteQueryOperator(
        task_id='generate_unsub_events',
        use_legacy_sql=False,
        sql='00_generate_unsub_gala_not_transmitted_profiles.sql',
        params={
            'limit': Variable.get("gala_unsub_events_limit", default_var=25000, deserialize_json=True)
        }
    )
    generate_unsub_events.doc_md = 'Generate unsub events from gala consents for profiles not transmitted to Le Figaro'

    export_unsub_events = BigQueryToEmailEventOperator(
        task_id='export_unsub_events',
        destination_cloud_storage_object_path=
        'tmp_exports/{{ next_execution_date.strftime("%Y%m%d") }}/gala_unsub_events_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}.csv',
        import_table='matrix__email_tmp.gala_unsub_events',
        source_project_dataset_table='temp.gala_to_unsub_profiles_events',
        bucket=buckets['matrix'],
        copy_expert_query="COPY {} (create_date, type, email, email_hash, payload) FROM STDIN WITH CSV QUOTE e'\"' DELIMITER AS e'\t'".format(
            'matrix__email_tmp.gala_unsub_events'),
    )
    export_unsub_events.doc_md = 'Export unsub events to email_event queue <br />'

    generate_unsub_events >> export_unsub_events
