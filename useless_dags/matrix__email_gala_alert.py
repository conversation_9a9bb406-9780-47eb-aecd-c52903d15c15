r"""
**PURPOSE:**<br />

This DAG generates sub/unsub events to gala_alert consent, from received tags in userhub & send them to psql email_event.<br />
It is a trashable/temporary dag till the end of transfer period to "Le Figaro".<br />

__METHODOLOGY:__<br />

generate sub/unsub events to gala_alert for received tags per email<br />
export sub/unsub events to postgres email_event<br />

"""

# ----------------------------
# gsutil cp dags/matrix__email_gala_alert.py  gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/
#
# Update SQL:
# gsutil -m cp -R data/sql/matrix_email/00_generate_sub_gala_alert  gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/matrix_email


import os
from datetime import timedelta, datetime

from airflow.models import Variable
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from collapsible_doc_dag import CDocDAG
from matrix_plugin.operators.bigquery_to_email_event import BigQueryToEmailEventOperator

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']
mozart_bucket = buckets['mozart']

dag_name = 'matrix__email_gala_alert'
email_on_failure = True
str_env = ''
gcs_path = 'gs://it-data-prod-matrix-pipeline/'
bq_project = 'pm-prod-matrix'
bq_userhub_project = 'pm-prod-userhub'
# ------------- FOR PREPROD ENV -----------------
env = os.environ.get("ENV")
schedule_interval = "1 6 * * *"
if env != 'prod':
    str_env = '_' + env
    dag_name = dag_name + '_' + env
    email_on_failure = False
    gcs_path = 'gs://it-data-preprod-matrix-preprod-pipeline/'
    bq_project = 'pm-preprod-matrix'
    bq_userhub_project = 'pm-preprod-userhub'

# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2024, 2, 1, 00, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=10),
    'sla': None,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=1),
    'depends_on_past': False,

    # GCP
    'gcp_conn_id': 'gcs_matrix',
    'bucket': matrix_bucket,

    # BigQuery
    'bigquery_conn_id': 'bq_matrix',
    'bq_project': 'pm-{}-matrix'.format(env),
    'write_disposition': 'WRITE_TRUNCATE',
    'use_legacy_sql': False,
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',

    # PSQL Connection
    'postgres_conn_id': 'psql_matrix_email_app',
    'database': 'matrix',
}

with CDocDAG(dag_name,
             schedule_interval=schedule_interval,
             default_args=default_args,
             max_active_runs=1,
             dagrun_timeout=timedelta(hours=1),
             tags=["gala"],
             catchup=False,
             description='Generate sub for gala_alert from events received in userhub',
             doc_md=__doc__,
             template_searchpath=['/home/<USER>/gcs/data/sql/matrix_email']) as dag:

    task_doc_md = """
    Generate sub/unsub events to gala_alert using events received in userhub (tags by email)
    When no tags detected, generate unsub, otherwise a sub
    join with refined_data.profile_email to verify consent status to not generate dead-events
    """
    generate_sub_unsub_events = BigQueryExecuteQueryOperator(
        task_id='generate_sub_events',
        use_legacy_sql=False,
        sql='00_generate_sub_gala_alert.sql',
        params={
            'bq_project': bq_project,
            'bq_userhub_project': bq_userhub_project,
        }
    )
    generate_sub_unsub_events.doc_md = task_doc_md

    task_doc_md = """
    Export sub events to email_event queue <br />
    """
    export_events = BigQueryToEmailEventOperator(
        task_id='export_events',
        destination_cloud_storage_object_path=
        'tmp_exports/{{ next_execution_date.strftime("%Y%m%d") }}/gala_alert_events_{{ next_execution_date.strftime("%Y%m%d_%H%M") }}.csv',
        import_table='matrix__email_tmp.gala_alert_events',
        source_project_dataset_table='export_matrix_email.gala_alert_events',
        bucket=buckets['matrix'],
        trigger_rule='none_failed',
        copy_expert_query="COPY {} (create_date, type, email, email_hash, payload) FROM STDIN WITH CSV QUOTE e'\"' DELIMITER AS e'\t'".format(
            'matrix__email_tmp.gala_alert_events'),
    )
    export_events.doc_md = task_doc_md

    generate_sub_unsub_events >> export_events


