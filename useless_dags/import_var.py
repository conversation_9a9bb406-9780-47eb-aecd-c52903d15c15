import json

from airflow import DAG
from gcs_plugin.operators.gcs_download_operator import GoogleCloudStorageDownloadOperator
from airflow.models import Variable
from airflow.operators.python import PythonOperator
from datetime import timedelta

from airflow.utils.dates import days_ago

# Configure DAG parameters
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email': ['<EMAIL>'],
    'email_on_failure': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']

with DAG(
    dag_id='gcs_json_to_variable',
    default_args=default_args,
    description='Downloads JSON from GCS, parses it, and creates/updates variable',
    start_date=days_ago(1),
    schedule_interval=None,  # Run manually
) as dag:

    # Download JSON file from GCS
    download_json_task = GoogleCloudStorageDownloadOperator(
        task_id='download_json',
        bucket=matrix_bucket,
        object='backup_airflow/mozart_preprod_variables_decrypted_20240328_0725.json',
        filename='/tmp/var.json',
    )

    # Parse JSON file and update variables
    def parse_json_and_update_variables(**kwargs):
        with open('/tmp/var.json') as f:
            data = json.load(f)
        for key, value in data.items():
            Variable.set(key, value=value)

    parse_json_task = PythonOperator(
        task_id='parse_json',
        python_callable=parse_json_and_update_variables,
        provide_context=True,
    )

    # Define task dependencies
    download_json_task >> parse_json_task
