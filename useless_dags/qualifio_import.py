r"""
**PURPOSE:**

This dag imports Qualifio campaigns after API call.

**Process:**
1.**Download campaigns in FULL and INCREMENTAL ways:**

- FULL: if is_full = True --> Download all campaigns
- INCREMENTAL: if is_full = False --> Download all campaigns that proceed the last one

2. Import downloaded campaigns into CSV files in GCS
3. Import CSV to BQ import table 
4. Import BQ table to prepare table:*** Extract some fields as campaign_id, start_date, etc
5. Store campaign Cast fields to the right data type
For more details,
**you can get a look on dataflow schema:**
**https:**//drive.google.com/file/d/1ml65Df3_8R5BAYv-J6wqvcIAFgZXELdO/view?usp=sharing
"""

import os
from datetime import timedelta, datetime
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from airflow.providers.google.cloud.transfers.gcs_to_bigquery import GCSToBigQueryOperator
from airflow.operators.python import PythonOperator
from airflow.providers.google.cloud.hooks.gcs import GCSHook
import requests as req
from airflow.models import Variable
from collapsible_doc_dag import CDocDAG
import logging
import json
import ast
import tempfile
import pandas as pd
import itdata_plugin

dag_name = 'qualifio_import'
email_on_failure = True
bq_project = 'pm-prod-matrix'
searchpath = '/home/<USER>/gcs/data/sql/qualifio_import/'
schema = "qualifio_campaign.json"

# ------------- FOR PREPROD ENV -----------------
env = os.environ.get("ENV")
if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = True
    bq_project = 'pm-preprod-matrix'
# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2022, 3, 22, 00, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=3),
    'sla': None,
    'email_on_retry': False,
    'retries': 0,
    'retry_delay': timedelta(minutes=1),
    'depends_on_past': False,
    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'gcp_conn_id': 'bq_matrix',
    'write_disposition': 'WRITE_APPEND',
    'allow_large_results': True,
    'flatten_results': False,
    'priority': 'INTERACTIVE',
    'use_legacy_sql': False
}

# upload Airflow variables
qualifio_import_var = Variable.get(
    "qualifio_import", deserialize_json=True)
buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']
matrix_bucket_url = 'gs://it-data-prod-matrix-pipeline/'


def set_airflow_variable_callable(**kwargs):
    """
        Result(Airflow variable): set airflow variable
    """
    # get table schema
    load_schema = json.load(
        open('{}schemas/{}'.format(kwargs['searchpath'],
                                   kwargs['schema']), 'r'))
    return itdata_plugin.set_variable_in_secret_manager(kwargs['schema'].split(".")[0], load_schema)


def download_campaign(**kwargs):
    """
        result(dict):
            * return dictionary contains campaign data after API call
        Args:
            * canonical_url(string): canonical API route as "https://api.qualif.io/v1/campaignfeed/channels/"
            * guid(string): Global Unique Id
            * client_id(int): client Id
            * is_full: True/False. if True than donwload all campaigns else the most recent
        For further information, take a look on : https://support.qualifio.com/hc/en-us/articles/************-About-campaign-feeds on "Building a campaign feed, step by step" section
    """
    # init variables
    campaign_dict = {"payload": []}
    last_campaign_id = [0]
    if not kwargs['is_full']:
        # overwride last campaign ids
        last_campaign_id = Variable.get("qualifio_last_campaign_id", deserialize_json=True)
    start_campaign_id = last_campaign_id[-1]
    json_response = {0: 0}
    while json_response:
        logging.info("Downlaod start ! ")
        logging.info("Start campaign id is:  {}! ".format(start_campaign_id))
        url = kwargs["canonical_url"] + "{guid}/json?clientId={client_id}&cursor={campaign_id}".format(
            guid=kwargs["guid"],
            client_id=kwargs["client_id"],
            campaign_id=start_campaign_id + 1)
        logging.info('url : {}'.format(str(url)))
        # API call
        response = req.request("GET", url)
        logging.info('response : {}'.format(str(response)))
        # get response content into json
        json_response = json.loads(response.content)
        # get the last campaign id
        if json_response["numberRows"] != 0:
            # get last campaign
            last_campaign = json_response["channels"][-1]["campaign"]["campaignId"]
            last_campaign_id.append(last_campaign)
        else:
            logging.info("Downlaod finish ! ")
            # quit import process if we arrive to the last campaign
            break
        # set cursor to last campaign id
        end_campaign_id = start_campaign_id
        start_campaign_id = last_campaign
        # fill dictionary
        for campaign in json_response["channels"]:
            campaign_dict["payload"].append(campaign)
    # set last campaign ids into Airflow variable
    itdata_plugin.set_variable_in_secret_manager("qualifio_last_campaign_id", last_campaign_id)
    return campaign_dict


def import_campaign_to_gcs_callable(**kwargs):
    """
        result(): Upload campaigns into CSV files on GCS
        Args:
            * date(string): execution date
    """
    logging.info("Import to GCS start !")
    # download campaigns
    campaign_dict = download_campaign(**kwargs)
    # save content in local & upload to GCS & clean local
    # create tmp file
    tmp_fd, tmp_file = tempfile.mkstemp(text=True)
    # create temp dataframe based on tmp_file
    tmp_df = pd.DataFrame(campaign_dict)
    # upload dataframe to csvs
    tmp_df.to_csv(tmp_file, index=False)
    gcs_hook = GCSHook(
        gcp_conn_id='gcs_matrix')
    gcs_hook.upload(
        bucket_name=matrix_bucket,
        object_name='temp/campaigns/Qualifio-campaign_{}.csv'.format(
            kwargs['next_ds_nodash']),
        filename=tmp_file,
        gzip=False
    )
    os.remove(tmp_file)
    os.close(tmp_fd)
    logging.info("GCS upload finish")


with CDocDAG(
        dag_name,
        description="Qualifio Import",
        tags=["partner", "qualifio"],
        doc_md=__doc__,
        schedule_interval="1 7 * * *",
        default_args=default_args,
        max_active_runs=1,
        dagrun_timeout=timedelta(minutes=40),
        catchup=False,
        template_searchpath=['/home/<USER>/gcs/data/sql/qualifio_import/']
) as dag:
    # set airflow variable
    task_doc_md = """
    Set campaign table schema <br />
    """
    set_airflow_variable = PythonOperator(
        task_id='set_airflow_variable',
        python_callable=set_airflow_variable_callable,
        op_kwargs={
            'schema': schema,
            'searchpath': searchpath
        }
    )
    set_airflow_variable.doc_md = task_doc_md

    # import qualifio campaigns into CSV files
    task_doc_md = """
    Download Qualifio campaigns by calling API route: <br />
        {canonical_url}{guid}/json?clientId={client_id}. <br />

    Then, we import these campaigns to GCS into CSV files to be imported later into BQ tables. <br/>
    """.format(canonical_url=qualifio_import_var["canonical_url"],
               guid=qualifio_import_var["guid"],
               client_id=qualifio_import_var["client_id"])
    import_campaign_to_gcs = PythonOperator(
        task_id='import_campaign_to_gcs',
        python_callable=import_campaign_to_gcs_callable,
        op_kwargs={
            'canonical_url': qualifio_import_var["canonical_url"],
            'guid': qualifio_import_var["guid"],
            'client_id': qualifio_import_var["client_id"],
            'is_full': qualifio_import_var["is_full"],
            'next_ds_nodash': '{{ next_ds_nodash }}'
        }
    )
    import_campaign_to_gcs.doc_md = task_doc_md

    # import qualifio to bq into tables
    task_doc_md = """
    Import Qualifio campaigns from GCS to BQ  tables. <br />
    """
    import_campaign_to_bq = GCSToBigQueryOperator(
        task_id='import_campaign_to_bq',
        source_objects=[
            'temp/campaigns/Qualifio-campaign_{date}.csv'.format(
                date='{{ next_ds_nodash }}')],
        destination_project_dataset_table=bq_project +
                                          ':import.qualifio_campaign_{date}'.format(date='{{ next_ds_nodash }}'),
        source_format='CSV',
        field_delimiter=';',
        schema_fields=ast.literal_eval(Variable.get(schema.split(".")[0])),
        skip_leading_rows=1,
        autodetect=False,
        max_bad_records=0,
        write_disposition='WRITE_TRUNCATE',
        allow_jagged_rows=True,
        bucket=matrix_bucket
    )
    import_campaign_to_bq.doc_md = task_doc_md

    # prepare qualifio campaigns to be stored into BQ table
    task_doc_md = """
    Prepare campaigns to be stored into final BQ table. <br />
    """
    prepare_campaign = BigQueryExecuteQueryOperator(
        task_id='prepare_campaign',
        sql='prepare_campaign.sql',
        params={
            'bq_project': bq_project
        }
    )
    prepare_campaign.doc_md = task_doc_md

    # store qualifio campaigns
    task_doc_md = """
    Store Qualifio campaigns. <br />
    """
    store_campaign = BigQueryExecuteQueryOperator(
        task_id='store_campaign',
        sql='store_campaign.sql',
        params={
            'bq_project': bq_project,
            'is_full': qualifio_import_var["is_full"]
        }
    )
    store_campaign.doc_md = task_doc_md

    set_airflow_variable >> import_campaign_to_gcs >> import_campaign_to_bq >> prepare_campaign >> store_campaign
