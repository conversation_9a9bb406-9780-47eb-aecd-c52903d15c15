from airflow import DAG
from airflow.models import Variable
from import_plugin.operators.gcs_pmc_bmk_csv_to_bq_bulk import GcsBmkToBqBulkOperator
from bq_plugin.operators.skippable_bigquery_operator import SkippableBigQueryOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator

import os, base64
from datetime import timedelta, datetime
import pendulum


dag_name = 'bq__bmk_events_all'
email_on_failure = True
email_on_retry = False
#------------- FOR NON-PROD ENV -----------------
env = os.environ.get("ENV")
if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = True
    email_on_retry = True

    # suppress end-user credential warning in logs
    # especially usefull when in DEV (local) env
    import warnings
    warnings.filterwarnings(
        "ignore", "Your application has authenticated using end user credentials")
    os.environ["GOOGLE_CLOUD_PROJECT"] = "pm-preprod-mozart" # doesn't really matter
#-----------------------------------------------


###############################################################################################


def png_to_img_tag(
    file_rel_path: str,
    alt: str
) -> str:
    """
    returns the html 'img' tag
    with the image being sourced as a base64 encoding string.
    If the input image file is can't be reached,
    the returned string is empty.

    Parameters :
        -- file_rel_path (str) :
            The relative path to the source png image
            (relative to the '/home/<USER>/gcs' directory
             on the Airflow webserver).
        -- alt (str) :
            The img html tag 'alternative information' attribute value
            (mouseover or displayed text when image unreachable.

    Results:
        -- str :
            the html 'img' tag
    """

    COMPOSER_BUCKET = os.environ.get("GCS_BUCKET")

    if COMPOSER_BUCKET is not None:
        # We most likely are inside a GCP Composer instance
        result_str = '<img src="https://storage.cloud.google.com/{}{}" ' + \
                    'alt="{}" ' + \
                    'style="border: 5px solid #0091a1; ' + \
                        ' padding: 10px 10px 10px 10px;" />'
        result_str = result_str.format(COMPOSER_BUCKET, file_rel_path, alt)
        return result_str
    else:
        # We most likely are inside a local DEV Airflow instance
        filename = os.path.join('/home/<USER>/gcs', file_rel_path.lstrip('/'))
        if os.path.isfile(filename):
            with open(filename, "rb") as image_file:
                return '<img src="data:image/jpeg;base64,' + \
                       base64.b64encode(image_file.read()).decode('utf-8') + '" ' + \
                        'alt="DAG process flow" ' + \
                        'style="border: 5px solid #0091a1; ' + \
                            ' padding: 10px 10px 10px 10px;" />'
        else:
            return '<img src="where-is-'+filename.replace('/', '_')+'" ' + \
                        'alt="DAG process flow" ' + \
                        'style="border: 5px solid #0091a1; ' + \
                            ' padding: 10px 10px 10px 10px;" />'


###############################################################################################


process_img_filename = \
    '/data/resources/bmk_events_all_DAG.png'
dag_doc_md = """
# PMC 'bookmark' CSV input files turned into inline User-Journey timeline data.

Working with the so-called 'PrismaAlert' as per the following slides :<br />
<a target="_blank"
 href="https://docs.google.com/presentation/d/1KJ827wDcZtyDmDx4_dX6mJxAQiDb2ttxitgdHWV4lBI/edit#slide=id.g3416f36bcd_9_0">
https://docs.google.com/presentation/d/1KJ827wDcZtyDmDx4_dX6mJxAQiDb2ttxitgdHWV4lBI/edit#slide=id.g3416f36bcd_9_0
</a>

<hr />
<br />

""" + png_to_img_tag(process_img_filename, alt='DAG process flow') + """

DAG for the daily updates to the 'pmc_bmk_events_all' treatment.

Reconstructing user journey when it comes to PMC TAG subscription/unsubscritption timelines.

Bringing the content of the raw PMC-issued CSV input files of the form
'uuid/brand/updated_at/content_id' (where 'content_id' is
a concatenated string of all active tag ids)<br />
into an inline (sanitized) form as follows : 'uuid/brand/tag/subscribed_at/unsubscribed_at'.


The mother of all tables, the 'bulk' table, hosts the totality of
the 'bookmark' CSV records historically received from PMC.<br />
The other tables all are re-build from scratch at each iteration
of the daily DAG 1AM run, based solely on the content of the (updated) 'bulk' table.

"""


local_tz = pendulum.timezone("Europe/Paris")
default_args = {
    'owner': 'airflow',
    'start_date': datetime(2019, 6, 25, tzinfo=local_tz),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'email_on_retry': email_on_retry,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes = 50),
    'sla': None,
    'retries': 1,
    'retry_delay': timedelta(minutes=1),
    'depends_on_past': False,

    'gcs_conn_id': 'gcs_sftp',
    'bucket_name': 'sftp',
    'dir_rel_path': 'home/uploads/prismalert/imports/',

    'bigquery_conn_id': 'bq_matrix',
    'gcp_conn_id': 'bq_matrix',
    'bq_tmp_dataset_name': "prepare",
    'bq_dataset_name': "store_matrix_pmc",
    'bq_bulk_table_name': 'bmk_events_all_bulk',
    'bq_ranked_table_name': 'bmk_events_all_ranked',
    'bq_split_table_name': 'bmk_events_all_split',
    'bq_sub_unsub_tmp_table_name': 'bmk_events_all_sub_unsub_tmp',
    'bq_final_table_name': 'bmk_events_all'
}

# REMARK : Time zone aware DAGs (thru 'start_date') that use cron schedules
# respect daylight savings time.
# For example, a DAG with a start date in the US/Eastern time zone
# with a schedule of 0 0 * * * will run daily
# on times for the PREVIOUS days at 22:00 UTC when DST (Daylight Saving Time)
# is observed (summer) and at 23:00 UTC otherwise.
with DAG(dag_name,
         schedule_interval='0 1 * * *', # once a day, at 1AM CET
         default_args=default_args,
         max_active_runs=1,
         dagrun_timeout=timedelta(hours=1),
         catchup=False,
         template_searchpath=['/home/<USER>/gcs/data/sql/bq_bmk/']
) as dag:

    dag.doc_md = dag_doc_md


    task_doc_md = """
    concatenate and import PMC-issued "bookmark" CSV files
    in an 'as-is' manner (no processing of the data whatsoever)
    for the day prior to the task 'execution_date' (CET).
    """
    csv_to_bq_bulk_task = GcsBmkToBqBulkOperator(
        task_id='gcs_pmc_bmk_csv_to_bq_bulk',
        dag=dag
    )
    csv_to_bq_bulk_task.doc_md = task_doc_md


    task_doc_md = """
    process the extended recordset (with new data from previous day)
    We use the "skippable" version of the BigQueryExecuteQueryOperator
    which we instruct to 'skip' the task if the upstream task
    results in no (0/zero) new record from the PMC-issued "bookmark" CSV input files
    for the entire previous day (via xCom)
    """
    process_bulk_to_ranked_task = SkippableBigQueryOperator(
        task_id='bq_process_bulk_to_ranked',
        sql='01_bmk_events_all_rank.sql',
        params={'bq_dataset_name': default_args['bq_dataset_name'],
                  'bq_bulk_table_name': default_args['bq_bulk_table_name'],
                  'bq_ranked_table_name': default_args['bq_ranked_table_name']},
        use_legacy_sql=False, create_disposition=False, write_disposition=False,
        schema_update_options=False, # location='EU',

        xcom_dag_ids_list=[csv_to_bq_bulk_task.task_id],
        to_not_be_skipped_xcom_key='new_rows_count',
        if_skipped_message='The upstream task didn\'t generate ' +
                             'conditions requiring further processing.',
        dag=dag
    )
    process_bulk_to_ranked_task.doc_md = task_doc_md


    task_doc_md = """
    Here, we :
       - get rid of superseded rows, i.e.rows for which PMC CSV files declare several times
         (often even in conflicting manner) the states for a given 'uuid/brand/updated_at' tuple.
       - 'rank' records for latter 'where rank = rank-1' outer joins
         in order to compare 2 successive states (and be able to identify
         dates of sub/unsub individual events.
    """
    process_ranked_to_split_task = BigQueryExecuteQueryOperator(
        task_id='bq_process_ranked_to_split',
        sql='02_bmk_events_all_split.sql',
        params={'bq_dataset_name': default_args['bq_dataset_name'],
                  'bq_tmp_dataset_name': default_args['bq_tmp_dataset_name'],
                  'bq_ranked_table_name': default_args['bq_ranked_table_name'],
                  'bq_split_table_name': default_args['bq_split_table_name']},
        use_legacy_sql=False, create_disposition=False, write_disposition=False,
        schema_update_options=False, # location='EU',

        dag=dag
    )
    process_ranked_to_split_task.doc_md = task_doc_md


    task_doc_md = """
    Here, we move
       - from a "uuid/brand/tag/is (still) active subscription" chain
         of 'updated_at' "uuid/brand"-state dates
       - To a single row inline structure of the form
         "uuid/brand/tag/subscribed_at/unsubscribed_at"
    REMARK : we unsure that any several sub/unsub occurrences
             for a given "uuid/brand/tag" tuple are indeed
             represented by several rows in such a new structure.
    """
    process_split_to_sub_unsub_tmp_task = BigQueryExecuteQueryOperator(
        task_id='bq_process_split_to_sub_unsub_tmp',
        sql='03_bmk_events_all_sub_unsub_tmp.sql',
        params={'bq_tmp_dataset_name': default_args['bq_tmp_dataset_name'],
                  'bq_split_table_name': default_args['bq_split_table_name'],
                  'bq_sub_unsub_tmp_table_name': default_args['bq_sub_unsub_tmp_table_name']},
        use_legacy_sql=False, create_disposition=False, write_disposition=False,
        schema_update_options=False, # location='EU',

        dag=dag
    )
    process_split_to_sub_unsub_tmp_task.doc_md = task_doc_md


    task_doc_md = """
    Computation of the table final to the data transformation process.
    """
    process_sub_unsub_tmp_to_final_task = BigQueryExecuteQueryOperator(
        task_id='bq_process_sub_unsub_tmp_to_final',
        sql='04_bmk_events_all_final.sql',
        params={'bq_dataset_name': default_args['bq_dataset_name'],
                  'bq_tmp_dataset_name': default_args['bq_tmp_dataset_name'],
                  'bq_sub_unsub_tmp_table_name': default_args['bq_sub_unsub_tmp_table_name'],
                  'bq_final_table_name': default_args['bq_final_table_name']},
        use_legacy_sql=False, create_disposition=False, write_disposition=False,
        schema_update_options=False, # location='EU',

        dag=dag
    )
    process_sub_unsub_tmp_to_final_task.doc_md = task_doc_md


csv_to_bq_bulk_task >> process_bulk_to_ranked_task \
    >> process_ranked_to_split_task \
    >> process_split_to_sub_unsub_tmp_task \
    >> process_sub_unsub_tmp_to_final_task


###############################################################################################