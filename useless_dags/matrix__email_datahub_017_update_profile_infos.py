r"""
**PURPOSE:**
**This dag is used to:**- Synchronize personal information from the PMC profile to Splio Universes .

**Diagram:**

**METHODOLOGY:**

-**Preparation Step:**-- 1/ get profile to synch to Splio  
-- 2/ export to Splio 
-- 3/ keep email sha256 already processed  

**COMMAND TO TEST:** 

- gsutil cp dags/matrix__email_datahub_017_update_profile_infos.py gs://europe-west1-preprod-mozart-2fa49086-bucket/dags/ 
- gsutil -m cp -R data/sql/matrix_email/*_017.sql**gs://europe-west1-preprod-mozart-2fa49086-bucket/data/sql/matrix_email/
"""

import os
from datetime import timedelta, datetime

from airflow.models import Variable
from export_plugin.operators.export_splio_bigquery_operator import ExportSplioBigQueryOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryExecuteQueryOperator
from collapsible_doc_dag import CDocDAG

dag_name = 'matrix__email_datahub_017_update_profile_infos'

email_on_failure = True
gcs_matrix_uri = 'gs://it-data-prod-matrix-pipeline'
# ------------- FOR PREPROD ENV -----------------
env = os.environ.get("ENV")
bq_project = 'pm-{}-matrix'.format(env)
bq_userhub_project = 'pm-{}-userhub'.format(env)
if env != 'prod':
    dag_name = dag_name + '_' + env
    email_on_failure = False
    gcs_matrix_uri = 'gs://it-data-preprod-matrix-preprod-pipeline'
# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2023, 10, 10, 00, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(hours=6),
    'sla': None,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'depends_on_past': False,

    'gcp_conn_id': 'gcs_matrix',
    # BQ CONNECTION
    'bigquery_conn_id': 'bq_matrix',
    'use_legacy_sql': False,
    'write_disposition': 'WRITE_TRUNCATE',
    'allow_large_results': True,
    'flatten_results': False,
}

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']

datahub_contact_017 = Variable.get("datahub_contact_017", deserialize_json=True)
splio_universes_sftp = Variable.get("splio_universes_sftp", deserialize_json=True)
splio_universes_all = Variable.get("splio_universes_all", deserialize_json=True)
splio_universes = {u: splio_universes_all[u] for u in datahub_contact_017['universes'] if u in splio_universes_all}

with CDocDAG(dag_name,
             description='Synchronize personal information from the PMC profile to Splio Universes',
             doc_md=__doc__,
             schedule_interval="11 10 * * *",
             default_args=default_args,
             max_active_runs=1,
             dagrun_timeout=timedelta(hours=4),
             catchup=False,
             tags=["datahub"],
             template_searchpath=['/home/<USER>/gcs/data/sql/matrix_email/']) as dag:

    prepare_datahub_017 = BigQueryExecuteQueryOperator(
        task_id="prepare_datahub_017",
        sql="10_prepare_datahub_017.sql",
        params={
            'bq_project': bq_project,
            'bq_userhub_project': bq_userhub_project,
            'limit': datahub_contact_017["limit"],
            'limit_date': datahub_contact_017["limit_date"],
            'full_export': datahub_contact_017["full_export"]
        }
    )
    prepare_datahub_017.doc_md = 'prepare profile and universe to synchronize to Splio'

    export_splio_datahub_017 = ExportSplioBigQueryOperator(
        task_id='export_splio_datahub_017',
        sql='11_export_splio_datahub_017.sql',
        params={
            'universe_id': '{universe_id}',
            'bq_project': bq_project,
        },
        destination_dataset_table_temporary='temp.{universe}_contacts_017_{splio_sequence}',
        universes=splio_universes,
        universes_sftp=splio_universes_sftp,
        datahub_file_template='{universe}_contacts_017_{splio_sequence}*.csv',
        splio_sequence='{{ next_execution_date.strftime("%Y%m%d_%H%M") }}',
        bucket=matrix_bucket,
        bucket_path='export_splio/datahub_017/{{ next_execution_date.strftime("%Y%m%d_%H%M") }}/',
        destination_cloud_storage_path_uris=gcs_matrix_uri,
        splio_remote_dir='imports/',
        flux_ref=17,
        bq_project=bq_project,
    )
    export_splio_datahub_017.doc_md = 'Export selected profile to Splio universes'

    refresh_store_datahub_017 = BigQueryExecuteQueryOperator(
        task_id="refresh_store_datahub_017",
        sql="12_update_store_datahub_017.sql",
    )
    refresh_store_datahub_017.doc_md = 'add exported profiles to store table'
    
    prepare_datahub_017 >> export_splio_datahub_017 >> refresh_store_datahub_017
