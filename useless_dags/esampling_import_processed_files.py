# gsutil cp dags/esampling_import_processed_files.py gs://europe-west1-mozart-cluster-2e910a39-bucket/dags/

# gsutil -m cp -R data/sql/esampling/*  gs://europe-west1-mozart-cluster-2e910a39-bucket/data/sql/esampling/

import os
from datetime import timedelta, datetime

from airflow import DAG
from airflow.models import Variable
from esampling_plugin.operators.esampling_import_processed_files_operator import EsamplingImportProcessedFilesOperator

buckets = Variable.get("gcs_bucket_names", deserialize_json=True)
matrix_bucket = buckets['matrix']

dag_name = 'esampling_import_processed_files'
bq_project = 'pm-prod-matrix'

email_on_failure = True
str_env = ''
# ------------- FOR PREPROD ENV -----------------
env = os.environ.get("ENV")
if env != 'prod':
    str_env = '_' + env
    dag_name = dag_name + '_' + env
    email_on_failure = False
    bq_project = 'pm-preprod-matrix'
# -----------------------------------------------

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2019, 12, 20, 18, 00, 00),
    'email': [eval(Variable.get("airflow_email_alertes"))],
    'email_on_failure': email_on_failure,
    'log_response': True,
    'xcom_push': True,
    'provide_context': True,
    'execution_timeout': timedelta(minutes=90),
    'sla': None,
    'email_on_retry': False,
    'retries': 0,
    'retry_delay': timedelta(minutes=1),
    'depends_on_past': False,

    # PSQL Connection
    'postgres_conn_id': 'psql_esampling_app',

    # GCP CONNECTION
    'gcp_conn_id': 'gcs_matrix',

    'database': 'matrix'
}

'''
esampling_export_params = {"interval": " 2 days", "min_rows" : 5000 }
'''
esampling_export_params = Variable.get("esampling_export_params", deserialize_json=True)
esampling_export_interval = esampling_export_params['interval']
esampling_export_min_rows = int(esampling_export_params['min_rows'])
send_email_notification = esampling_export_params['send_email_notification']
send_email_notification_cc = esampling_export_params['send_email_notification_cc']

with DAG(dag_name,
         schedule_interval="10 1 * * *",
         default_args=default_args,
         max_active_runs=1,
         dagrun_timeout=timedelta(hours=2),
         catchup=False,
         template_searchpath=['/home/<USER>/gcs/data/sql/esampling/imports/']) as dag:
    # IMPORT FROM HOLY SAMPLING (gcs -> psql)
    import_processed_files = EsamplingImportProcessedFilesOperator(
        task_id='import_processed_files',
        params={
            'minimum': esampling_export_min_rows,
            'interval': esampling_export_interval
        },
        sql_insert_history_file='2_sql_insert_history_file.sql',
        sql_insert_holy_sampling_contact='10_insert_holy_sampling_contact.sql',
        sql_update_contact_status='11_update_contacts_status.sql',
        sql_get_campaign_from_qualifio_campaign_ref='0_get_campaign_from_qualifio_campaign_ref.sql',
        sql_check_exist_qualifio_ref_list='12_check_exist_qualifio_ref_list.sql',
        bucket=buckets['sftp'],
        gcp_conn_id='gcs_sftp',
        alert_error_emails=send_email_notification,
        alert_error_emails_cc=send_email_notification_cc,
    )
